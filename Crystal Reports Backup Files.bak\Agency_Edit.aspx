﻿<%@ Page Title="Agency | Edit" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Agency_Edit.aspx.cs" Inherits="KSDCSCST_Portal.Agency_Edit" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">
    <link rel="stylesheet" href="sweetalert2.min.css">

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Agency Edit</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                       
                          <li class="breadcrumb-item"><a href="Agency_List.aspx">Agency List</a></li>
                        <li class="breadcrumb-item active">Agency Edit</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="DIV_Agency">

                                    <div class="form-group row">
                                        <label for="inputName" class="col-sm-3 col-form-label">Agency Name *</label>
                                        <div class="col-sm-9"> 
                                            <input type="text"required name="txtAgency_Name" class="form-control" id="txtAgency_Name" placeholder="Agency Name">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName" class="col-sm-3 col-form-label">Description</label>
                                        <div class="col-sm-9">
                                            <textarea class="form-control" id="txtDescription" rows="3" placeholder="Enter ..."></textarea>
                                        </div>
                                    </div>
                                     <div class="form-group row">
                                                <label for="inputEmail" class="col-sm-3 col-form-label">Caste *</label>
                                                <div class="col-sm-9">
                                                    <select id="dropCast" class="form-control">
                                                        <option value="SC">SC</option>
                                                        <option value="ST">ST</option>
                                                        <option value="SC/ST">SC/ST</option>
                                                        <option value="No Caste (KSDC Employees)">No Caste (KSDC Employees)</option>

                                                    </select>
                                                </div>
                                            </div>
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="checkActive" checked="checked">
                                            <label class="form-check-label">Active</label>
                                        </div>

                                    </div>

                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                            <a href="Agency_List.aspx" class="btn btn-dark">Back</a>
                                            <a onclick="Update();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->


    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script>
        $(function () {

            LoadAgencyById(getParameterByName("Id"));





        });
        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }

        function LoadAgencyById(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Id: Id }), // If you have parameters
                url: "WebService.asmx/Select_All_Agency_By_ID",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Name = value.Name;
                        var Description = value.Description;
                        var Cast = value.Cast;
                        var IsActive = value.IsActive;

                        $("#txtAgency_Name").val(Name);
                        $("#txtDescription").val(Description);
                        $("#dropCast").val(Cast);

                        if (IsActive == 0) {
                            $('#checkActive').prop('checked', false);
                        }
                        else {
                            $('#checkActive').prop('checked', true);
                        }
 
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
              


            });
        }

        function Update() {
            var IsActive = $("#checkActive").is(":checked") ? $("#checkActive").val() : "off";
            if (IsActive == 'on') {
                IsActive = '1';
            }
            else {
                IsActive = '0';
            }
         
            
            $.ajax({
                type: "POST", // or "GET" depending on your web method
                url: "WebService.asmx/Update_To_tbl_Agency",
                data: JSON.stringify({ Id: getParameterByName("Id"), Name: $("#txtAgency_Name").val(), Description: $("#txtDescription").val(), Cast: $("#dropCast").val(), IsActive: IsActive  }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (response) {

                    // Handle the successful response from the web method
                    console.log(response.d); // "d" is the default property name for the response
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Agency Successfully Updated !',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked
                             
                            location.href = 'Agency_List.aspx';
                            // Your code here
                        }
                    });
                   
                },
                error: function (xhr, status, error) {
                    // Handle the error response

                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                        confirmButtonText: 'OK',

                    })
                },
                done: function (response) {
                    // Handle the error response
                    alert(response);

                }
            });




        }
    </script>
</asp:Content>
