﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Employee_Confirmation_Letter_Print.aspx.cs" Inherits="KSDCSCST_Portal.Employee_Confirmation_Letter_Print" %>


<asp:Content ID="Content2" ContentPlaceHolderID="MainContent" runat="server">
      <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css" />
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css" />
    <style>
        @media print {
            html, body {
             /*   width: 210mm;
                height: 297mm;*/
             width:100%;
                margin: 0;
            }

            /* Set landscape orientation */
            @page {
                size: A4 portrait;
            }
        }
    </style>
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Print Receipt</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">Print Receipt</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-7 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane">

                                    <div id="Print_Section">

                                        <div style="padding: 0px; float: left; width: 100%;">

                                            <div style="margin: 20px;">
                                                <div style="width: 100%">
                                                    <div style="width: 30%; float: left;">
                                                        <img style="width: 220px;" src="assets/img/Logo_Black_And_White.jpg" />
                                                        <p style="font-size: 12px; font-weight: bold; margin: 0; margin-top: 9px;">A GOVT.OF KERALA UNDER TAKING</p>
                                                    </div>
                                                    <div style="width: 70%; float: left;" >
                                                        <p style="font-size: 16px; font-weight: bold; line-height: 1.5; margin-bottom: 0px !important; margin-top: 0px;">The Kerala State Development Corporation For Scheduled Castes & Scheduled Tribes Limited</p>
                                                        <p style="display:none;margin-bottom: 0; font-size: 14px; margin-top: 5px;">CIN: U91990KL1972SGC002466</p>
                                                        <p style="text-align: center; font-weight: bold;margin-bottom: 0; font-size: 14px; margin-top: 5px;text-decoration: underline;">
                                                            OFFICE : <span id="span_Branch_Name"></span>
                                                        </p>
                                                         <p style="text-align: center;margin-bottom: 0; font-size: 14px; margin-top: 5px;"><span id="span_Address"></span></p>
                                                        
                                                    </div>
                                                </div>

                                                


                                                <hr style="float: left; width: 100%; margin-top: 0px;" />
                                                <div style="width: 100%; float: left; margin-top: 5px;">
                                                    <div style="width: 50%; float: left;">
                                                        <div style="text-align: left; font-weight: bold;">
                                                            <span style="font-size: 14px; float: left; font-weight: normal !important;">Registered with A/D.<br />
                                                                Ref. No:<span id="span_Reg_No"></span> </span>

                                                        </div>
                                                    </div>
                                                    <div style="width: 50%; float: right;">
                                                        <div style="text-align: right; font-weight: bold;">
                                                            <span style="font-size: 14px; float: right; font-weight: normal !important;">Date:<span id="span_Current_Date"></span></span>

                                                        </div>
                                                    </div>
                                                    <div style="width: 100%; float: left;    margin-top: 15px;margin-bottom: 15px;">
                                                        <span style="font-size: 14px; font-weight: bold;">To,<br />
                                                            <span id="span_senior_empname"></span><br />
                                                        <span id="span_senior_empdes"></span><br />
                                                            <span id="span_senior_empoffname"></span><br />
                                                            <span id="span_senior_emplane1"></span><br />
                                                           <span id="span_senior_emppost"></span> - <span id="span_senior_emppin"></span> </span>
                                                    </div>
                                                    <div style="width: 100%; float: left;">
                                                        <table border="1" style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                                                            <tr>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;">Name of Principal Debtor</td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;">Amount of Loan</td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;">Name of Employee</td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;">Date of Birth</td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;">Debtor / Surety</td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;">Designation</td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;">PEN</td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;">Pension Scheme (NPS or Statutory)</td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;">If Belongs to NPS, Specify PRAN Number</td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;">Date of Retirement</td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;">Gross Salary</td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;">Net Salary</td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;">PF Number</td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;">Sacle of Pay</td>

                                                            </tr>
                                                            <tr>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;"><span id="span_Name_Applicant"></span></td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;"><span id="span_Loan_Amount"></span></td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;"><span id="span_Employee_Name"></span></td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;"><span id="span_DOB"></span></td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;">SURETY</td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;"><span id="span_Emp_Desig"></span></td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;"><span id="span_PEN"></span></td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;"><span id="span_Pension_Scheme"></span></td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;"><span id="span_PRAN_No"></span></td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;"><span id="span_DOR"></span></td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;"><span id="span_Gross_Salary"></span></td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;"><span id="span_Net_Salary"></span></td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;"><span id="span_PF_No"></span></td>
                                                                <td style="padding: 5px; text-align: center; font-weight: bold; font-size: 12px;"><span id="span_Scale_Of_Pay"></span></td>

                                                            </tr>

                                                        </table>
                                                    </div>
                                                    <div style="width: 100%; float: left;">
                                                        <span style="width: 100%; font-size: 14px; text-align: left; line-height: 1.5; margin-bottom: 0px !important; margin-top: 10px; float: right;">Name & Signature of Authority :- <span id="span_DO_Name_Desig"></span><br />
                                                            (Financial Institution Concerned)</span>

                                                    </div>
                                                    <div style="width: 100%; float: left;">
                                                        <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                                                            <tr>
                                                                <td style="width: 10%;  text-align: left; font-weight: bold; font-size: 12px;">Address:</td>
                                                                <td style=" text-align: left; font-weight: bold; font-size: 12px;">
                                                                    <label style="margin: 0;  font-size: 14px; font-weight: normal !important;"><span id="span_DM_Address"></span></label></td>

                                                            </tr>
                                                               <tr>
                                                                <td style="width: 15%;   text-align: left; font-weight: bold; font-size: 12px;">Phone Number:</td>
                                                                <td style="  text-align: left; font-weight: bold; font-size: 12px;">
                                                                    <label style="margin: 0; font-size: 14px; font-weight: normal !important;"><span id="span_DM_Phone_No"></span> </label></td>

                                                            </tr>
                                                                <tr>
                                                                <td style="width: 10%;   text-align: left; font-weight: bold; font-size: 12px;">Date:</td>
                                                                <td style="  text-align: left; font-weight: bold; font-size: 12px;">
                                                                    <label style="margin: 0; font-size: 14px; font-weight: normal !important;"><span id="span_DM_Current_Address"></span></label></td>

                                                            </tr>
                                                                <tr>
                                                                <td style="width: 10%;   text-align: left; font-weight: bold; font-size: 12px;">Office Seal:</td>
                                                                <td style="  text-align: left; font-weight: bold; font-size: 12px;">
                                                                    <label style="margin: 0; font-size: 14px; font-weight: normal !important;"></label></td>

                                                            </tr>


                                                        </table>
                                                    </div>

   
                                                    <div style="width: 100%; float: left; text-align: center;">
                                                        <span style="font-weight:bold; font-size: 14px; text-align: center; line-height: 1.5; margin-bottom: 0px !important; margin-top: 10px;">The details specified in the above table is confirmed.</span>

                                                    </div>
                                                      <div style="width: 50%; float: right; text-align: center;">
                                                           <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                                                            <tr>
                                                                <td style="width: 10%;  text-align: left;   font-size: 14px;">Signature:</td>
                                                                <td style=" text-align: left; font-weight: bold; font-size: 12px;">
                                                                    <label style="margin: 0;  font-size: 14px; font-weight: normal !important;"> </label></td>

                                                            </tr>
                                                               <tr>
                                                                <td style="width: 25%;   text-align: left;   font-size: 14px;">Name of DDO:</td>
                                                                <td style="  text-align: left; font-weight: bold; font-size: 12px;">
                                                                    <label style="margin: 0; font-size: 14px; font-weight: normal !important;"></label></td>

                                                            </tr>
                                                                <tr>
                                                                <td style="width: 10%;   text-align: left;   font-size: 14px;">PEN:</td>
                                                                <td style="  text-align: left; font-weight: bold; font-size: 12px;">
                                                                    <label style="margin: 0; font-size: 14px; font-weight: normal !important;"></label></td>

                                                            </tr>
                                                                <tr>
                                                                <td style="width: 10%;   text-align: left;   font-size: 14px;">Office Address:</td>
                                                                <td style="  text-align: left; font-weight: bold; font-size: 12px;">
                                                                    <label style="margin: 0; font-size: 14px; font-weight: normal !important;"></label></td>

                                                            </tr>


                                                        </table>
                                                    </div>
                                                         <div style="width: 100%; float: left; text-align: center;">
                                                           <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                                                            <tr>
                                                                <td style="width: 10%;  text-align: left;   font-size: 14px;">Place:</td>
                                                                <td style=" text-align: left; font-weight: bold; font-size: 12px;">
                                                                    <label style="margin: 0;  font-size: 14px; font-weight: normal !important;"> </label></td>

                                                            </tr>
                                                               <tr>
                                                                <td style="width: 20%;   text-align: left;   font-size: 14px;">Date:</td>
                                                                <td style="  text-align: left; font-weight: bold; font-size: 12px;">
                                                                    <label style="margin: 0; font-size: 14px; font-weight: normal !important;"></label></td>

                                                            </tr>
                                                            


                                                        </table>
                                                    </div>
                                                                                                        <div style="width: 100%; float: left; text-align: center;">
                                                        <span style="font-weight:bold; font-size: 14px; text-align: center; line-height: 1.5; margin-bottom: 0px !important; margin-top: 10px;">(Office Seal)</span>

                                                    </div>
                                                </div>
                                            </div>

                                        </div>

                                    </div>

                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                              <a href="Receipt_Employee_Confirmation_Letter_List.aspx" class="btn btn-dark">Back</a>
                                     
                                            <a onclick="Print();" class="btn btn-success">Print</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <script src="assets/plugins/toastr/toastr.min.js"></script>



    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/inputmask/5.0.4/jquery.inputmask.min.js"></script>
    <script>
        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;
        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }
        $(document).ready(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            // Get the current date
            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtApplication_Date').val(formattedDate);

            $("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
            $("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");

            //   Load_All_Districts();

            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }


            $(".form-control").on("focusout", function () {
                $(".form-control").removeAttr("style");

                var This_Id = $(this).attr("id");
                var This_Control = $(this);
                if (This_Id == "txtApplicant_Name") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Name of Applicant is required !'
                        })
                    }
                    else if (!Is_Valid_Text(This_Control.val().trim())) {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Special characters or Numbers not allowed in Name of Applicant !'
                        })
                    }
                }
                else if (This_Id == "dropScheme") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Loan Scheme is required !'
                        })
                    }
                }
                else if (This_Id == "dropCast") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Caste is required !'
                        })
                    }
                }
                else if (This_Id == "dropSubCast") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Sub Caste is required !'
                        })
                    }
                }
                else if (This_Id == "txtPhoneNo") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Phone No is required !'
                        })
                    }
                }
                //else if (This_Id == "txtAadharNumber") {
                //    if (This_Control.val().trim() == "") {
                //        Focus_Error(This_Control);
                //        Toast.fire({
                //            icon: 'error',
                //            title: 'Aadhar No is required !'
                //        })
                //    }
                //}



                // Your common focusout function code goes here
                //   alert("Input value changed: " + value);
            });



            Load_Employee_Confirmation_Letter_Print();


        });

        function Print() {
            var printWindow = window.open('', '_blank');
            printWindow.document.open();
            printWindow.document.write('<html><head><title>Print</title></head><body style="width: 100%;margin: 0; ">');
            printWindow.document.write($("#Print_Section").html());
            printWindow.document.write('</body></html>');
            printWindow.document.close();
            printWindow.print();
        }


        function Load_Employee_Confirmation_Letter_Print() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Employee_Confirmation_Letter_Print",
                data: JSON.stringify({ int_empid: getParameterByName("Id") }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $.each(data.d, function (key, value) {
                        var Formated_Current_Date = value.Formated_Current_Date;
                        var vchr_applname = value.vchr_applname;
                        var vchr_empname = value.vchr_empname;

                        var vchr_empdesig = value.vchr_empdesig;
                        var Formated_dte_empdateofretire = value.Formated_dte_empdateofretire;
                        var int_netsal = value.int_netsal;
                        var int_grosssal = value.int_grosssal;
                        var vchr_senior_empname = value.vchr_senior_empname;
                        var vchr_senior_empdes = value.vchr_senior_empdes;
                        var vchr_senior_empoffname = value.vchr_senior_empoffname;
                        var vchr_senior_emplane1 = value.vchr_senior_emplane1;
                        var vchr_senior_emppost = value.vchr_senior_emppost;
                        var vchr_senior_emppin = value.vchr_senior_emppin;
                        var vchr_appreceivregno = value.vchr_appreceivregno;
                        var Branch_Address = value.Branch_Address;
                        var Name = value.Name;
                        var vchr_pen = value.vchr_pen;
                        var Formated_dte_dob = value.Formated_dte_dob;
                        var int_amtsanction = value.int_amtsanction;
                        var Phone_1 = value.Phone_1;
                        var Phone_2 = value.Phone_2;
                        var District_Manager_Name = value.District_Manager_Name;
                        var Designation = value.Designation;

                        var vchr_PensionScheme = value.vchr_PensionScheme;
                        var vchr_PFNo = value.vchr_PFNo;
                        var vchr_scaleofpay = value.vchr_scaleofpay;
                        var vchr_PRAN = value.vchr_PRAN;


                        
                        $("#span_Address").text(Branch_Address);
                        $("#span_Branch_Name").text(Name);
                        $("#span_Reg_No").text(vchr_appreceivregno);
                        $("#span_Current_Date").text(Formated_Current_Date);
                        $("#span_DO_Name_Desig").text(District_Manager_Name + "," + Designation);
                        $("#span_DM_Address").text(Branch_Address);
                        $("#span_DM_Phone_No").text(Phone_1);
                        
                        $("#span_DM_Current_Address").text(Formated_Current_Date);
                        $("#span_DOR").text(Formated_dte_empdateofretire);
                        $("#span_Gross_Salary").text("Rs." + int_grosssal);
                        $("#span_Net_Salary").text("Rs." + int_netsal);

                        $("#span_senior_empname").text(vchr_senior_empname);
                        $("#span_senior_empdes").text(vchr_senior_empdes);
                        $("#span_senior_empoffname").text(vchr_senior_empoffname);
                        $("#span_senior_emplane1").text(vchr_senior_emplane1);
                        $("#span_senior_emppost").text(vchr_senior_emppost);
                        $("#span_senior_emppin").text(vchr_senior_emppin);
                        $("#span_Name_Applicant").text(vchr_applname);
                        $("#span_Loan_Amount").text("Rs." + int_amtsanction);
                        $("#span_Employee_Name").text(vchr_empname);
                        $("#span_DOB").text(Formated_dte_dob);

                        $("#span_Emp_Desig").text(vchr_empdesig);
                        $("#span_PEN").text(vchr_pen);


                        var vchr_PensionScheme = value.vchr_PensionScheme;
                        var vchr_PFNo = value.vchr_PFNo;
                        var vchr_scaleofpay = value.vchr_scaleofpay;
                        var vchr_PRAN = value.vchr_PRAN;


                        $("#span_Pension_Scheme").text(vchr_PensionScheme);
                        $("#span_Scale_Of_Pay").text("Rs."+vchr_scaleofpay);
                        $("#span_PF_No").text(vchr_PFNo);
                        if (vchr_PensionScheme="NPS") {
                            $("#span_PRAN_No").text(vchr_PRAN);
                        }
                      
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $('#dropDistrict').change(function () {
                    Load_All_Sub_Districts($(this).val());
                });

            });

        }
        function Load_All_Sub_Districts(District_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Districts",
                data: JSON.stringify({ District_Id: District_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubDistrict').empty();
                    $('#dropSubDistrict').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Sub_District_Name = value.Sub_District_Name;
                        var html = '<option value="' + Id + '">' + Sub_District_Name + '</option>';

                        $('#dropSubDistrict').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });

        }

        function Load_All_Schemes() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Schemes",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropScheme').empty();
                    $('#dropScheme').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var IsActive = value.IsActive;
                        var Scheme = value.Scheme;
                        var html = '<option value="' + Id + '">' + Scheme + '</option>';
                        if (IsActive == 1) {
                            $('#dropScheme').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_All_Cast();

            });

        }

        function Load_All_Cast() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Cast",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropCast').empty();
                    $('#dropCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var Cast = value.Cast;
                        var html = '<option value="' + Id + '">' + Cast + '</option>';

                        $('#dropCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $('#dropCast').change(function () {
                    Load_All_SubCast($(this).val());
                });

            });

        }


        function Load_All_SubCast(Cast_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Cast",
                data: JSON.stringify({ Cast_Id: Cast_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubCast').empty();
                    $('#dropSubCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var SubCast = value.SubCast;
                        var html = '<option value="' + Id + '">' + SubCast + '</option>';

                        $('#dropSubCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {


            });

        }
        function Save() {
            //alert(Counter);



            var district_Id = $("#dropDistrict");

            var subDistrict_id = $("#dropSubDistrict");
            var name_Applicant = $("#txtApplicant_Name");
            var scheme_Id = $("#dropScheme");

            var cast_Id = $("#dropCast");
            var sub_Cast_Id = $("#dropSubCast");

            var remarks = $("#txtRemarks");
            var PhoneNo = $("#txtPhoneNo");
            var AadharNumber = $("#txtAadharNumber");





            $(".form-control").removeAttr("style");



            if (name_Applicant.val().trim() == "") {
                Focus_Error(name_Applicant);
                Toast.fire({
                    icon: 'error',
                    title: 'Name of Applicant is required !'
                })
            }
            else if (!Is_Valid_Text(name_Applicant.val().trim())) {
                Focus_Error(name_Applicant);
                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed in Name of Applicant !'
                })
            }
            else if (scheme_Id.val() == "0") {
                Focus_Error(scheme_Id);
                Toast.fire({
                    icon: 'error',
                    title: 'Loan Scheme is required !'
                })
            }
            else if (cast_Id.val() == "0") {
                Focus_Error(cast_Id);
                Toast.fire({
                    icon: 'error',
                    title: 'Caste is required !'
                })
            }
            else if (sub_Cast_Id.val() == "0") {
                Focus_Error(sub_Cast_Id);
                Toast.fire({
                    icon: 'error',
                    title: 'Sub Caste is required !'
                })
            }
            else if (PhoneNo.val().trim() == "") {
                Focus_Error(PhoneNo);
                Toast.fire({
                    icon: 'error',
                    title: 'Phone No is required !'
                })
            }
            //else if (AadharNumber.val().trim() == "") {
            //    Focus_Error(PhoneNo);
            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Aadhar No is required !'
            //    })
            //}


            else {
                if (PhoneNo.val().trim() != "") {

                    var values = PhoneNo.val().trim().split(",");
                    for (var i = 0; i < values.length; i++) {
                        if (values[values.length - 1] != "") {
                            if (!Is_Valid_Mobile_Number(values[i])) {
                                Focus_Error(PhoneNo);
                                Toast.fire({
                                    icon: 'error',
                                    title: 'Enter valid phone number !'
                                })
                                return;
                            }
                        }
                    }
                }

                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/Insert_To_tbl_Loan_App_Issue",
                    data: JSON.stringify({ district_Id: district_Id.val(), subDistrict_id: subDistrict_id.val(), name_Applicant: name_Applicant.val(), scheme_Id: scheme_Id.val(), cast_Id: cast_Id.val(), sub_Cast_Id: sub_Cast_Id.val(), remarks: remarks.val(), PhoneNo: PhoneNo.val(), AadharNumber: AadharNumber.val() }), // If you have parameters
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {

                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        // Handle AJAX error
                        //   alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);

                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                        })

                    }

                }).done(function () {
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Application Issue Successfully Saved !',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked

                            location.href = 'ApplicationIssue_List.aspx';
                            // Your code here
                        }
                    });

                });


            }




        }

        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }

        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }


        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }



    </script>
 </asp:Content>


 
