// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "\u4eca\u5929";
var L_January   = "\u4e00\u6708";
var L_February  = "\u4e8c\u6708";
var L_March     = "\u4e09\u6708";
var L_April     = "\u56db\u6708";
var L_May       = "\u4e94\u6708";
var L_June      = "\u516d\u6708";
var L_July      = "\u4e03\u6708";
var L_August    = "\u516b\u6708";
var L_September = "\u4e5d\u6708";
var L_October   = "\u5341\u6708";
var L_November  = "\u5341\u4e00\u6708";
var L_December  = "\u5341\u4e8c\u6708";
var L_Su        = "\u65e5";
var L_Mo        = "\u4e00";
var L_Tu        = "\u4e8c";
var L_We        = "\u4e09";
var L_Th        = "\u56db";
var L_Fr        = "\u4e94";
var L_Sa        = "\u516d";

// strings for dt_param.js
var L_TIME_SEPARATOR = ":";
var L_AM_DESIGNATOR = "\u4e0a\u5348";
var L_PM_DESIGNATOR = "\u4e0b\u5348";

// strings for range parameter
var L_FROM = "\u4ece\uff08\u5305\u542b\uff09 {0}";
var L_TO = "\u5230\uff08\u5305\u542b\uff09{0}";
var L_AFTER = "\u4ece\uff08\u4e0d\u5305\u542b\uff09{0} ";
var L_BEFORE = "\u5230\uff08\u4e0d\u5305\u542b\uff09{0} ";
var L_FROM_TO = "\u4ece\uff08\u5305\u542b\uff09{0} \u5230\uff08\u5305\u542b\uff09{1}";
var L_FROM_BEFORE = "\u4ece\uff08\u5305\u542b\uff09{0} \u5230\uff08\u4e0d\u5305\u542b\uff09{1}";
var L_AFTER_TO = " \u4ece\uff08\u4e0d\u5305\u542b\uff09{0} \u5230\uff08\u5305\u542b\uff09{1}";
var L_AFTER_BEFORE = "\u4ece\uff08\u4e0d\u5305\u542b\uff09{0} \u5230\uff08\u4e0d\u5305\u542b\uff09{1} ";

// Strings for prompts.js and prompts_param.js
var L_BadNumber		= "\u6b64\u53c2\u6570\u7684\u7c7b\u578b\u4e3a \"\u6570\u5b57\"\uff0c\u53ea\u80fd\u5305\u542b\u8d1f\u53f7\u3001\u6570\u5b57 (\"0-9\")\u3001\u6570\u5b57\u5206\u7ec4\u7b26\u53f7\u6216\u5c0f\u6570\u70b9\u3002\u8bf7\u7ea0\u6b63\u8f93\u5165\u7684\u53c2\u6570\u503c\u3002";
var L_BadCurrency	= "\u6b64\u53c2\u6570\u7684\u7c7b\u578b\u4e3a \"\u8d27\u5e01\"\uff0c\u53ea\u80fd\u5305\u542b\u8d1f\u53f7\u3001\u6570\u5b57 (\"0-9\")\u3001\u6570\u5b57\u5206\u7ec4\u7b26\u53f7\u6216\u5c0f\u6570\u70b9\u3002\u8bf7\u7ea0\u6b63\u8f93\u5165\u7684\u53c2\u6570\u503c\u3002";
var L_BadDate		= "\u6b64\u53c2\u6570\u7684\u7c7b\u578b\u4e3a \"\u65e5\u671f\"\uff0c\u5176\u683c\u5f0f\u5e94\u4e3a \"\u65e5\u671f(yyyy,mm,dd)\"\uff0c\u5176\u4e2d \"yyyy\" \u662f\u56db\u4f4d\u6570\u5e74\u4efd\uff0c\"mm\" \u662f\u6708\u4efd\uff08\u4f8b\u5982\uff0c\u4e00\u6708 = 1\uff09\uff0c\"dd\" \u662f\u6307\u5b9a\u6708\u4efd\u7684\u5929\u6570\u3002";
var L_BadDateTime   = "\u6b64\u53c2\u6570\u7684\u7c7b\u578b\u4e3a \"\u65e5\u671f\u65f6\u95f4\"\uff0c\u6b63\u786e\u7684\u683c\u5f0f\u4e3a \"\u65e5\u671f\u65f6\u95f4(yyyy,mm,dd,hh,mm,ss)\"\u3002\"yyyy\" \u662f\u56db\u4f4d\u6570\u5e74\u4efd\uff0c\"mm\" \u662f\u6708\u4efd\uff08\u4f8b\u5982\uff0c\u4e00\u6708 = 1\uff09\uff0c\"dd\" \u662f\u6708\u4e2d\u7684\u67d0\u5929\uff0c\"hh\" \u662f 24 \u5c0f\u65f6\u5236\u7684\u5c0f\u65f6\u6570\uff0c\"mm\" \u662f\u5206\u949f\u6570\uff0c\"ss\" \u662f\u79d2\u6570\u3002";
var L_BadTime       = "\u6b64\u53c2\u6570\u7684\u7c7b\u578b\u4e3a \"\u65f6\u95f4\"\uff0c\u5176\u683c\u5f0f\u5e94\u4e3a \"\u65f6\u95f4(hh,mm,ss)\"\uff0c\u5176\u4e2d \"hh\" \u662f 24 \u5c0f\u65f6\u5236\u7684\u5c0f\u65f6\u6570\uff0c\"mm\" \u662f\u5206\u949f\u6570\uff0c\"ss\" \u662f\u79d2\u6570\u3002";
var L_NoValue       = "\u65e0\u503c";
var L_BadValue      = "\u82e5\u8981\u8bbe\u7f6e \"\u65e0\u503c\"\uff0c\u60a8\u5fc5\u987b\u5c06\u201c\u4ece\u201c \u548c \u201c\u5230\u201c \u7684\u503c\u6539\u4e3a \"\u65e0\u503c\".";
var L_BadBound      = "\u60a8\u4e0d\u80fd\u4e0e \"\u65e0\u4e0a\u9650\" \u4e00\u8d77\u8bbe\u7f6e \"\u65e0\u4e0b\u9650\".";
var L_NoValueAlready = "\u6b64\u53c2\u6570\u5df2\u8bbe\u7f6e\u4e3a \"\u65e0\u503c\"\u3002\u5728\u6dfb\u52a0\u5176\u4ed6\u503c\u4e4b\u524d\u5220\u9664 \"\u65e0\u503c\"";
var L_RangeError    = "\u8303\u56f4\u7684\u8d77\u59cb\u503c\u4e0d\u80fd\u5927\u4e8e\u7ed3\u675f\u503c\u3002";
var L_NoDateEntered = "\u60a8\u5fc5\u987b\u8f93\u5165\u65e5\u671f\u3002";

// Strings for ../html/crystalexportdialog.htm
var L_ExportOptions     = "\u5bfc\u51fa\u9009\u9879";
var L_PrintOptions      = "\u6253\u5370\u9009\u9879";
var L_PrintPageTitle    = "\u6253\u5370\u62a5\u8868";
var L_ExportPageTitle   = "\u5bfc\u51fa\u62a5\u8868";
var L_OK                = "\u786e\u5b9a";
var L_PrintPageRange    = "\u8f93\u5165\u8981\u6253\u5370\u7684\u9875\u7801\u8303\u56f4\u3002";
var L_ExportPageRange   = "\u8f93\u5165\u8981\u5bfc\u51fa\u7684\u9875\u7801\u8303\u56f4\u3002";
var L_InvalidPageRange  = "\u9875\u7801\u8303\u56f4\u503c\u4e0d\u6b63\u786e\u3002\u8bf7\u8f93\u5165\u6709\u6548\u7684\u9875\u7801\u8303\u56f4\u3002";
var L_ExportFormat      = "\u8bf7\u4ece\u5217\u8868\u4e2d\u9009\u62e9\u5bfc\u51fa\u683c\u5f0f\u3002";
var L_Formats           = "\u683c\u5f0f\uff1a";
var L_All               = "\u6240\u6709";
var L_Pages             = "\u9875";
var L_From              = "\u4ece\uff1a";
var L_To                = "\u5230\uff1a";
var L_PrintStep0        = "\u6253\u5370\uff1a";
var L_PrintStep1        = "1.  \u5728\u51fa\u73b0\u7684\u4e0b\u4e00\u4e2a\u5bf9\u8bdd\u6846\u4e2d\uff0c\u9009\u62e9 \"\u6253\u5f00\u6b64\u6587\u4ef6\" \u9009\u9879\u5e76\u5355\u51fb\u201c\u786e\u5b9a\u201d\u6309\u94ae\u3002";
var L_PrintStep2        = "2.  \u5355\u51fb Acrobat Reader \u83dc\u5355\u4e0a\u7684\u6253\u5370\u673a\u56fe\u6807\uff0c\u800c\u4e0d\u662f Internet \u6d4f\u89c8\u5668\u4e0a\u7684\u6253\u5370\u673a\u56fe\u6807\u3002";
var L_RTFFormat         = "RTF \u683c\u5f0f";
var L_AcrobatFormat     = "Acrobat \u683c\u5f0f (PDF)";
var L_CrystalRptFormat  = "Crystal Reports (RPT)";
var L_WordFormat        = "MS Word";
var L_ExcelFormat       = "MS Excel 97-2000";
var L_ExcelRecordFormat = "MS Excel 97-2000\uff08\u4ec5\u9650\u6570\u636e\uff09";
if(typeof(Sys)!=='undefined')Sys.Application.notifyScriptLoaded();
