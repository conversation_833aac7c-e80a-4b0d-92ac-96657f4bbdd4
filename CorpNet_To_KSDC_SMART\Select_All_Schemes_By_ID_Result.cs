//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CorpNet_To_KSDC_SMART
{
    using System;
    
    public partial class Select_All_Schemes_By_ID_Result
    {
        public int Id { get; set; }
        public int Agency_Id { get; set; }
        public string Agency { get; set; }
        public string Scheme { get; set; }
        public Nullable<decimal> Loan_Amount { get; set; }
        public Nullable<double> Loan_Interest { get; set; }
        public Nullable<int> Loan_Period { get; set; }
        public Nullable<double> Penal_Interest { get; set; }
        public Nullable<int> Max_Age { get; set; }
        public Nullable<int> Min_Age { get; set; }
        public Nullable<decimal> Anninc_Rural_Max { get; set; }
        public Nullable<decimal> Anninc_Urban_Max { get; set; }
        public Nullable<decimal> BeneficiaryContribution { get; set; }
        public Nullable<int> IsActive { get; set; }
        public Nullable<int> IsDeleted { get; set; }
        public Nullable<int> Created_By { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<int> Updated_By { get; set; }
        public Nullable<System.DateTime> Updated_Date { get; set; }
    }
}
