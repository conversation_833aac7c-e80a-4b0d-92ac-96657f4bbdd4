﻿<%@ Page Title="Project | Add" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Project_Add.aspx.cs" Inherits="KSDCSCST_Portal.Project_Add" %>






<asp:Content ID="Content2" ContentPlaceHolderID="MainContent" runat="server">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">
    <link rel="stylesheet" href="sweetalert2.min.css">
    <!-- Select2 -->
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Project Add</h1>
                </div>
                <div class="col-sm-6">
                  <ol class="breadcrumb float-sm-right">
                      
                        <li class="breadcrumb-item"> Settings </li>
                        <li class="breadcrumb-item active"><a href="Project_List.aspx">Projects</a></li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="DIV_Agency">
                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Agency</label>
                                        <div class="col-sm-9">
                                            <select id="dropAgency" class="form-control">
                                                <option>Please wait...</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Scheme</label>
                                        <div class="col-sm-9">
                                            <select id="dropScheme" class="form-control">
                                                <option>Select</option>
                                            </select>
                                        </div>
                                    </div>

                                     <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Sector</label>
                                        <div class="col-sm-9">
                                            <select id="dropSector" class="form-control">
                                                <option>Select</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label for="inputName" class="col-sm-3 col-form-label">Project *</label>
                                        <div class="col-sm-9">
                                            <input type="text" required   class="form-control" id="txtProject" placeholder="Project">
                                        </div>
                                    </div>

                                  


                                     
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="checkActive" checked="checked">
                                            <label class="form-check-label">Active</label>
                                        </div>

                                    </div>

                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                            <a href="Project_List.aspx" class="btn btn-dark">Back</a>
                                            <a onclick="Save();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->


    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <script>
        var Counter = 1;
        $(function () {
            $('.select2').select2();
            




            LoadAgencyList();
            $('#dropAgency').change(function () {
                LoadSchemeList(this.value);
                
            });

          




        });
        function LoadAgencyList() {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Agency_ACTIVE",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropAgency').empty();
                    $('#dropAgency').append('<option value="0">Select Agency</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Name = value.Name;
                        var html = '<option value="' + Id + '">' + Name + '</option>';
                        $('#dropAgency').append(html);
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

           
                LoadSectorList();
            });
        }

        function LoadSchemeList(Agency_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Schemes_Active",
                data: JSON.stringify({ Agency_Id: Agency_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropScheme').empty();
                    $('#dropScheme').append('<option value="0">Select Scheme</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Scheme = value.Scheme;
                        var html = '<option value="' + Id + '">' + Scheme + '</option>';
                        $('#dropScheme').append(html);
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });
        }

        function LoadSectorList() {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sectors_ACTIVE",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSector').empty();
                    $('#dropSector').append('<option value="0">Select Sector</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Sector = value.Sector;
                        var html = '<option value="' + Id + '">' + Sector + '</option>';
                        $('#dropSector').append(html);
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });
        }

      
  
        function Save() {
            //alert(Counter);




            var agency_Id = $("#dropAgency").val();

            var Scheme_Id = $("#dropScheme").val();
            var Sector_Id = $("#dropSector").val();
            var Project = $("#txtProject").val();
            
 
            var IsActive = $("#checkActive").is(":checked") ? $("#checkActive").val() : "off";
            if (IsActive == 'on') {
                IsActive = '1';
            }
            else {
                IsActive = '0';
            }
            var isActive = IsActive;


            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Insert_To_tbl_Projects",
                data: JSON.stringify({ agency_Id: agency_Id, scheme_Id: Scheme_Id, sector_Id: Sector_Id, project: Project, isActive: isActive  }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                   
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Swal.fire({
                    icon: 'success',
                    title: 'Message',
                    text: 'Project Successfully Saved !',

                }).then((result) => {
                    if (result.isConfirmed) {
                        // OK button clicked

                        location.href = 'Project_List.aspx';
                        // Your code here
                    }
                });

            });




        }

    </script>
</asp:Content>