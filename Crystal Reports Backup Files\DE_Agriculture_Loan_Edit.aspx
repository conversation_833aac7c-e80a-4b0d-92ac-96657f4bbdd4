﻿<%@ Page Title="Application Data Entry - Agriculture Loan | Edit" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="DE_Agriculture_Loan_Edit.aspx.cs" Inherits="KSDCSCST_Portal.DE_Agriculture_Loan_Edit" %>
<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
      <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">
    
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
               <div class="col-sm-6">
                    <h1>To be filled for Agriculture Land Purchase Loan</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="Welcome.aspx">Home</a></li>
						  <li class="breadcrumb-item  "><a href="ApplicationDataEntry_List.aspx">Application DataEntry</a><Application Issue</li>
                        <li class="breadcrumb-item active">To be filled for Agriculture Land Purchase Loan</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Sector*</label>
                                         <div class="col-sm-9">
                                            <select id="dropSector"   class="form-control">  <option>[Select]</option>
                                            </select>
                                        </div>
                                    </div>
									  <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Project*</label>
                                         <div class="col-sm-9">
                                            <select id="dropProject"   class="form-control">  <option>[Select]</option>
                                            </select>
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Estimated Amount*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtEstimated_Amount" placeholder="Estimated Amount">
                                        </div>
                                    </div>
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Amount In Hand*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtAmount_In_Hand" placeholder="Amount In Hand">
                                        </div>
                                    </div>
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Purpose of the loan*</label>
                                        <div class="col-sm-9">
                                             <textarea class="form-control" maxlength="200" id="txtPurpose_Of_Loan" placeholder="For what purpose the loan amount is used"></textarea>
                                            <span class="validation_message">Maximum text length is 200</span>
                                        </div>
                                    </div>
									
									
							
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Extent of Property*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtExtent_Of_Property" placeholder="Extent of Property">
                                        </div>
                                    </div>
									 
									
									
									
									
							

                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                            
                                            <a onclick="Save();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <script src="assets/plugins/toastr/toastr.min.js"></script>
    
    
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.js"></script>

    <script>

        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }


        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;
        $(document).ready(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            // Get the current date
            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtApplication_Date').val(formattedDate);

            $("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
            $("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");
            Load_All_Sectors();
            //   Load_All_Districts();

            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }


        });

        function Load_All_Sectors() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_Sectors",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSector').empty();
                    $('#dropSector').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Sector = value.Sector + " (" + value.Code + ")";
                        var html = '<option value="' + Id + '">' + Sector + '</option>';
                        if (Id != 15) {
                            $('#dropSector').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                $("#dropSector").select2();

                Load_All_Agriculture_Loan(getParameterByName("Id"));

            });

        }

        function Load_All_Projects(Sector_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_Projects_By_Sector_Id",
                data: JSON.stringify({ Sector_Id: Sector_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropProject').empty();
                    $('#dropProject').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Project = value.Project;
                        var html = '<option value="' + Id + '">' + Project + '</option>';
                        if (Id != 15) {
                            $('#dropProject').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $("#dropProject").select2();

            });

        }

        function Load_All_Projects_Update(Sector_Id, Project_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_Projects_By_Sector_Id",
                data: JSON.stringify({ Sector_Id: Sector_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropProject').empty();
                    $('#dropProject').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Project = value.Project;
                        var html = '<option value="' + Id + '">' + Project + '</option>';
                        if (Id != 15) {
                            $('#dropProject').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $("#dropProject").select2();
                $("#dropProject").val(Project_Id).trigger('change');

            });

        }

        var vchr_sector;
        var vchr_project;
        function Load_All_Agriculture_Loan(int_loanappid) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_tbl_agriloan_By_int_loanappid",
                data: JSON.stringify({ int_loanappid: int_loanappid }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {



                    $.each(data.d, function (key, value) {
                        var int_agriid = value.int_agriid;
                        var int_loanappid = value.int_loanappid;
                        var int_schemeid = value.int_schemeid;
                        vchr_sector = value.vchr_sector;
                        vchr_project = value.vchr_project;
                        var int_amt_est = value.int_amt_est;
                        var int_amt_hand = value.int_amt_hand;
                        var vchr_projdetails = value.vchr_projdetails;
                        var vchr_ext_prop = value.vchr_ext_prop;

                        $('#dropSector').val(vchr_sector).trigger('change');
                        $('#dropProject').val(vchr_project);
                        $('#txtEstimated_Amount').val(int_amt_est);
                        $('#txtAmount_In_Hand').val(int_amt_hand);
                        $('#txtPurpose_Of_Loan').val(vchr_projdetails);
                        $('#txtExtent_Of_Property').val(vchr_ext_prop);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_All_Projects_Update(vchr_sector, vchr_project);

            });

        }
        function Save() {
            //alert(Counter);









            var Sector = $("#dropSector");
            var Project = $("#dropProject");
            var Estimated_Amount = $("#txtEstimated_Amount");
            var Amount_In_Hand = $("#txtAmount_In_Hand");
            var Purpose_Of_Loan = $("#txtPurpose_Of_Loan");
            var Extent_Of_Property = $("#txtExtent_Of_Property");


            $(".form-control").removeAttr("style");

            if (Sector.val() == "0") {
                Focus_Error(Sector);
                Toast.fire({
                    icon: 'error',
                    title: 'Sector is required !'
                })
            }

            else if (Project.val() == "0") {
                Focus_Error(Project);
                Toast.fire({
                    icon: 'error',
                    title: 'Project is required !'
                })
            }
            else if (Estimated_Amount.val() == "") {
                Focus_Error(Estimated_Amount);
                Toast.fire({
                    icon: 'error',
                    title: 'Estimated Amount is required !'
                })
            }
            else if (Amount_In_Hand.val() == "") {
                Focus_Error(Amount_In_Hand);
                Toast.fire({
                    icon: 'error',
                    title: 'Amount in Hand is required !'
                })
            }

            else if (Purpose_Of_Loan.val() == "") {
                Focus_Error(Purpose_Of_Loan);
                Toast.fire({
                    icon: 'error',
                    title: 'Loan Purpose is required !'
                })
            }

            else if (Extent_Of_Property.val() == "") {
                Focus_Error(Extent_Of_Property);
                Toast.fire({
                    icon: 'error',
                    title: 'Extent of Property is required !'
                })
            }

            else {





                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/Update_To_tbl_agriloan",
                    data: JSON.stringify({ int_loanappid: getParameterByName("Id"), int_schemeid: getParameterByName("Scheme_Id"), vchr_sector: Sector.val(), vchr_project: Project.val(), int_amt_est: Estimated_Amount.val(), int_amt_hand: Amount_In_Hand.val(), vchr_projdetails: Purpose_Of_Loan.val(), vchr_ext_prop: Extent_Of_Property.val() }),

                    contentType: "application/json; charset=utf-8",
                    success: function (data) {

                    },
                    error: function (error) {


                        // alert("error" + error.responseText);
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                        })
                    }
                }).done(function () {
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Application Data Entry Successfully Submitted!',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked
                            location.href = 'ApplicationDataEntry_List.aspx';
                            // location.href = 'ApplicationDataEntry_View.aspx';
                            // Your code here
                        }
                    });


                });


            }




        }



        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }


        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }



    </script>
</asp:Content>
