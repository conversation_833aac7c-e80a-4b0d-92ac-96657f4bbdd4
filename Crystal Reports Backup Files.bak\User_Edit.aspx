﻿<%@ Page Title="User | EditS" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="User_Edit.aspx.cs" Inherits="KSDCSCST_Portal.User_Edit" %>





<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">
    <link rel="stylesheet" href="sweetalert2.min.css">

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>User Edit</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item"><a href="User_List.aspx">User List</a></li>
                        <li class="breadcrumb-item active">User Edit</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="DIV_Agency">

                                    <div class="form-group row">
                                        <label for="inputName" class="col-sm-3 col-form-label">Name *</label>
                                        <div class="col-sm-9">
                                            <input type="text" required name="txt_Name" class="form-control" id="txt_Name" placeholder="Name">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName" class="col-sm-3 col-form-label">Username *</label>
                                        <div class="col-sm-9">
                                            <input type="text" required name="txt_Username" class="form-control" id="txt_Username" placeholder="Username">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName" class="col-sm-3 col-form-label">Email Id *</label>
                                        <div class="col-sm-9">
                                            <input type="text" required name="txt_EmailId" class="form-control" id="txt_EmailId" placeholder="Email Id">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName" class="col-sm-3 col-form-label">Mobile *</label>
                                        <div class="col-sm-9">
                                            <input type="text" required name="txt_Mobile" class="form-control" id="txt_Mobile" placeholder="Mobile">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName" class="col-sm-3 col-form-label">Address</label>
                                        <div class="col-sm-9">
                                            <textarea class="form-control" id="txtAddress" rows="3" placeholder="Enter ..."></textarea>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Role *</label>
                                        <div class="col-sm-9">
                                            <select id="dropRole" class="form-control">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Office *</label>
                                        <div class="col-sm-9">
                                            <select id="dropOffice" class="form-control">
                                            </select>
                                        </div>
                                    </div>
                                    <hr />
                                    <div class="form-group row">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="checkPassReset">
                                            <label style="font-size: 17px; color: #838383;font-weight: bold;margin-top: -3px;" class="form-check-label">Reset Password</label>
                                        </div>

                                    </div>
                                    
                                    <div id="DIV_Reset_Password" style="display: none;">
                                        <div class="form-group row">
                                            <label for="inputName" class="col-sm-3 col-form-label">New Password *</label>
                                            <div class="col-sm-9">
                                                <input type="password" required name="txt_New_Password" class="form-control" id="txt_New_Password" placeholder="New Password">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="inputName" class="col-sm-3 col-form-label">Confirm Password *</label>
                                            <div class="col-sm-9">
                                                <input type="password" required name="txt_Confirm_Password" class="form-control" id="txt_Confirm_Password" placeholder="Confirm Password">
                                            </div>
                                        </div>

                                    </div>
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="checkActive" checked="checked">
                                            <label class="form-check-label">Active</label>
                                        </div>

                                    </div>

                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                            <a href="User_List.aspx" class="btn btn-dark">Back</a>
                                            <a onclick="Update();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->


    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script>
        var Old_Passoword = "";
        $(function () {
            Load_All_Roles();

            


            
        });


        function Load_All_Roles() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Roles_ACTIVE",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropRole').empty();
                    $('#dropRole').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Name = value.Name;

                        var html = '<option value="' + Id + '">' + Name + '</option>';
                        $('#dropRole').append(html);
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Offices();
            });

        }



        function Load_All_Offices() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Offices",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropOffice').empty();
                    $('#dropOffice').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Office_Name = value.Office_Name;
                        var Code = value.Code;
                        var ShortCode = value.ShortCode;
                        var html = '<option value="' + Id + '">' + Office_Name + '</option>';
                        $('#dropOffice').append(html);
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_User_ById(getParameterByName("Id"));


            });

        }


        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }

        function Load_User_ById(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Id: Id }), // If you have parameters
                url: "WebService.asmx/Select_All_Users_By_ID",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Name = value.Name;
                        var Username = value.Username;
                        var Password = value.Password;

                        var Role_id = value.Role_id;
                        var Office_Id = value.Office_Id;
                        var EmailId = value.EmailId;
                        var Mobile = value.Mobile;
                        var Address = value.Address;
                        var IsActive = value.IsActive;

                        Old_Passoword = Password;

                        $("#txt_Name").val(Name);
                        $("#txt_Username").val(Username);
                        $("#dropRole").val(Role_id);
                        $("#dropOffice").val(Office_Id);
                        $("#txt_EmailId").val(EmailId);
                        $("#txt_Mobile").val(Mobile);
                        $("#txtAddress").val(Address);



                        if (IsActive == 0) {
                            $('#checkActive').prop('checked', false);
                        }
                        else {
                            $('#checkActive').prop('checked', true);
                        }




                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });
        }

        function Update() {
            var IsActive = $("#checkActive").is(":checked") ? $("#checkActive").val() : "off";
            if (IsActive == 'on') {
                IsActive = '1';
            }
            else {
                IsActive = '0';
            }


            var checkPassReset = $("#checkPassReset").is(":checked") ? $("#checkPassReset").val() : "off";
            if (checkPassReset == 'on') {
                checkPassReset = '1';
            }
            else {
                checkPassReset = '0';
            }

            var New_pass = "";
            if (checkPassReset == "1") {
                if ($("#txt_New_Password").val().trim() != $("#txt_Confirm_Password").val().trim()) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'It appears that the new password and the confirm password you provided do not match.',

                    })
                    return;
                }
                New_pass = $("#txt_Confirm_Password").val().trim();
            }
            else {
                New_pass = Old_Passoword;
            }

           
            

            $.ajax({
                type: "POST", // or "GET" depending on your web method
                url: "WebService.asmx/Update_To_tbl_Users",
                data: JSON.stringify({ id: getParameterByName("Id"), name: $("#txt_Name").val(), username: $("#txt_Username").val(), password: New_pass, Role_Id: $("#dropRole").val(), office_Id: $("#dropOffice").val(), emailId: $("#txt_EmailId").val(), mobile: $("#txt_Mobile").val(), address: $("#txtAddress").val(), isActive: IsActive }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (response) {

                    // Handle the successful response from the web method
                    console.log(response.d); // "d" is the default property name for the response
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'User Successfully Updated !',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked

                            location.href = 'User_List.aspx';
                            // Your code here
                        }
                    });

                },
                error: function (xhr, status, error) {
                    // Handle the error response

                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                        confirmButtonText: 'OK',

                    })
                },
                done: function (response) {
                    // Handle the error response
                    alert(response);

                }
            });




        }
    </script>
</asp:Content>
