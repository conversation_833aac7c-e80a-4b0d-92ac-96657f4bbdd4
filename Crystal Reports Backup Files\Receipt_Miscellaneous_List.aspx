﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Receipt_Miscellaneous_List.aspx.cs" Inherits="KSDCSCST_Portal.Receipt_Miscellaneous_List" %>
<asp:Content ID="Content2" ContentPlaceHolderID="MainContent" runat="server">

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- DataTables -->
    <link rel="stylesheet" href="assets/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">


    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Miscellaneous Fee List</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="Dashboard.aspx">Home</a></li>
                        <li class="breadcrumb-item active">Miscellaneous Fee List</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">


                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">List All Miscellaneous Fee</h3>
                            <div style="display: none; float: right;"><a href="ApplicationIssue_Add.aspx" style="background: #0f6caa; border-color: #0f6caa;" class="btn btn-block btn-success">Create New Application Issue</a></div>
                        </div>
                        <!-- /.card-header -->
                        <div class="card-body">
                            <table id="Application_Data_Entry_List" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Id</th>
                                        <th>Application Reg No</th>
                                        <th>Applicant's Name</th>

                                        <th>Scheme</th>
                                        <th>Caste</th>
                                        <th>Sub Caste</th>
                                        <th>Submitted Date</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="tblBody">
                                </tbody>
                            </table>
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->


    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables  & Plugins -->
    <script src="assets/plugins/datatables/jquery.dataTables.min.js"></script>
    <script src="assets/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="assets/plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
    <script src="assets/plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
    <script src="assets/plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
    <script src="assets/plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
    <script src="assets/plugins/jszip/jszip.min.js"></script>
    <script src="assets/plugins/pdfmake/pdfmake.min.js"></script>
    <script src="assets/plugins/pdfmake/vfs_fonts.js"></script>
    <script src="assets/plugins/datatables-buttons/js/buttons.html5.min.js"></script>
    <script src="assets/plugins/datatables-buttons/js/buttons.print.min.js"></script>
    <script src="assets/plugins/datatables-buttons/js/buttons.colVis.min.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <!-- Page specific script -->
    <script>
          $(function () {

              Load_Application_Data_Entry();





          });
          function getParameterByName(name, url = window.location.href) {
              name = name.replace(/[\[\]]/g, '\\$&');
              var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                  results = regex.exec(url);
              if (!results) return null;
              if (!results[2]) return '';
              return decodeURIComponent(results[2].replace(/\+/g, ' '));
          }
          function Load_Application_Data_Entry() {

              var table = $('#Application_Data_Entry_List').DataTable({
                  "responsive": true, "lengthChange": false, "autoWidth": false,
                  "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"],
                  "processing": true,
                  "serverSide": true,
                  "ajax": {
                      "url": "WebService.asmx/Select_All_tbl_LoanApp_By_Status", // Replace with your server-side API URL
                      "type": "POST",
                      "data": function (data) {
                          //  var jsonObject = JSON.parse(jsonData);

                          // Add the orderColumn parameter to the data being sent to the server
                          // Add the required parameters to the DataTables AJAX request

                          data.Status = "Application Data Entry";
                          data.draw = data.draw || 1; // Example: Default value for draw parameter
                          data.start = data.start || 0;
                          data.length = data.length || 10;
                          data.orderColumn = 'int_loanappid';
                          data.orderDirection = 'desc';
                          data.int_loanappid = getParameterByName("int_loanappid");
                          data.vchr_appreceivregno = getParameterByName("vchr_appreceivregno");
                          data.vchr_applname = getParameterByName("vchr_applname");
                         
                          data.Scheme = getParameterByName("Scheme");
                          data.Cast = getParameterByName("Cast");
                          data.SubCast_Name = getParameterByName("SubCast_Name");
                          data.Submitted_Date_Formated = getParameterByName("Submitted_Date_Formated");

                          data.search = $('#Application_Data_Entry_List_filter label input').val();//{ value: $('#AgencyList_filter label input').val() }; // Send the search value to the server
                          //     alert($('#AgencyList_filter label input').val());

                          // Log the modified data object to the console
                          console.log(data);
                          // var jsonData = JSON.stringify(data);

                          // Return the modified data object
                          return data;
                      }
                  },
                  "columns": [
                      { "data": "int_loanappid" },
                      { "data": "vchr_appreceivregno" },
                      { "data": "vchr_applname" },
                      
                      { "data": "Scheme" },
                      { "data": "Cast" },
                      { "data": "SubCast_Name" },
                      { "data": "Submitted_Date_Formated" },
                      {
                          data: null,
                          title: 'Actions',
                          render: function (data, type, row) {
                              return '        <div class="btn-group">' +
                                  '            <button type="button" class="btn btn-success">Action</button>' +
                                  '            <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-expanded="false">' +
                                  '                <span class="sr-only">Toggle Dropdown</span>' +
                                  '            </button>' +
                                  '            <div class="dropdown-menu" role="menu" style="">' +
                                 
                                  '                <a class="dropdown-item" href="Surety_Land_View.aspx?Id=' + data.int_loanappid + '">View</a>' +
                                
                                  '            </div>' +
                                  '        </div>';


                              //   '<button data-name="' + data.vchr_applname + '" data-loan-no="' + data.int_loanno + '" onclick="View(this);" type="button" class="btn btn-block btn-success">View</button>';
                              // Replace "editData" with your function to handle the button click event
                              // "data.ID" is the ID of the row, adjust this based on your data structure
                          }
                      }
                  ]
              });




              //$.ajax({
              //    type: "POST",
              //    dataType: "json",
              //    url: "WebService.asmx/Select_All_tbl_LoanApp_By_Status",
              //    data: JSON.stringify({Status: 'Application Receipt' }), // If you have parameters

              //    contentType: "application/json; charset=utf-8",
              //    success: function (data) {
              //        $('#tblBody').empty();
              //        $.each(data.d, function (key, value) {
              //            var int_loanappid = value.int_loanappid;
              //            var vchr_appreceivregno = value.vchr_appreceivregno;
              //            var Reciept_No = value.Reciept_No;
              //            var vchr_applname = value.vchr_applname;
              //            var Dustrict_Name = value.Dustrict_Name;
              //            var Scheme = value.Scheme;

              //            var Cast = value.Cast;

              //            var SubCast = value.SubCast_Name;
              //            var Submitted_Date = value.Submitted_Date_Formated;



              //            var html = '<tr>' +
              //                '    <td>' + int_loanappid + '</td>' +
              //                '    <td>' + vchr_appreceivregno + '</td>' +
                             
              //                '    <td>' + vchr_applname + '</td>' +
                               
              //                '    <td>' + Scheme + '</td>' +

              //                '    <td>' + Cast + '</td>' +
              //                '    <td>' + SubCast + '</td>' +
              //                '    <td>' + Submitted_Date + '</td>' +
              //                '    <td>' +
              //                '        <div class="btn-group">' +
              //                '            <button type="button" class="btn btn-success">Action</button>' +
              //                '            <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-expanded="false">' +
              //                '                <span class="sr-only">Toggle Dropdown</span>' +
              //                '            </button>' +
              //                '            <div class="dropdown-menu" role="menu" style="">' +
              //                '                <a class="dropdown-item" href="ApplicationDataEntry_View.aspx?Id=' + int_loanappid + '">View</a>' +
              //              //  '                <a class="dropdown-item" data-id="' + Id + '"  onclick="Delete(this);" >Delete</a>' +
              //                '            </div>' +
              //                '        </div>' +
              //                '    </td>' +
              //                '</tr>';
              //            $('#tblBody').append(html);
              //        });


              //    },
              //    error: function (error) {


              //        // alert("error" + error.responseText);
              //        Swal.fire({
              //            icon: 'error',
              //            title: 'Oops...',
              //            text: 'Something went wrong!',

              //        })
              //    }
              //}).done(function () {
              //    $("#Application_Issue_List").DataTable({
              //        "bSort": false,
              //        "responsive": true, "lengthChange": false, "autoWidth": false,
              //        "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"]
              //    }).buttons().container().appendTo('#example1_wrapper .col-md-6:eq(0)');


              //});
          }

          function Delete(This_Val) {
              $.ajax({
                  type: "POST", // or "GET" depending on your web method
                  url: "WebService.asmx/Delete_From_tbl_Loan_App_Issue",
                  data: JSON.stringify({ Id: $(This_Val).attr("data-Id") }), // If you have parameters
                  contentType: "application/json; charset=utf-8",
                  dataType: "json",
                  success: function (response) {

                      // Handle the successful response from the web method
                      console.log(response.d); // "d" is the default property name for the response
                      Swal.fire({
                          icon: 'success',
                          title: 'Message',
                          text: 'Application Issue Successfully Deleted !',

                      }).then((result) => {
                          if (result.isConfirmed) {
                              // OK button clicked

                              location.href = 'ApplicationIssue_List.aspx';
                              // Your code here
                          }
                      });

                  },
                  error: function (xhr, status, error) {
                      // Handle the error response

                      Swal.fire({
                          icon: 'error',
                          title: 'Oops...',
                          text: 'Something went wrong!',

                      })
                  },
                  done: function (response) {
                      // Handle the error response
                      alert(response);

                  }
              });
          }
        
    </script>
</asp:Content>