﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Disbursement_Agreement_DataEntry_Modification_View.aspx.cs" Inherits="KSDCSCST_Portal.Disbursement_Agreement_DataEntry_Modification_View" %>



<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">





    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- daterange picker -->
    <link rel="stylesheet" href="assets/plugins/daterangepicker/daterangepicker.css">
    <!-- iCheck for checkboxes and radio inputs -->
    <link rel="stylesheet" href="assets/plugins/icheck-bootstrap/icheck-bootstrap.min.css">
    <!-- Bootstrap Color Picker -->
    <link rel="stylesheet" href="assets/plugins/bootstrap-colorpicker/css/bootstrap-colorpicker.min.css">
    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet" href="assets/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css">
    <!-- Select2 -->
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">
    <!-- Bootstrap4 Duallistbox -->
    <link rel="stylesheet" href="assets/plugins/bootstrap4-duallistbox/bootstrap-duallistbox.min.css">
    <!-- BS Stepper -->
    <link rel="stylesheet" href="assets/plugins/bs-stepper/css/bs-stepper.min.css">
    <!-- dropzonejs -->
    <link rel="stylesheet" href="assets/plugins/dropzone/min/dropzone.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">
    <style>
        input:disabled {
            border: 0;
        }
    </style>
    <style>
        /* Styles for the search input */
        txtPostOffices {
            width: 300px;
            padding: 10px;
            font-size: 16px;
        }

        /* Styles for the search results container */
        #search-results {
            list-style: none;
            padding: 0;
            margin-top: 5px;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
        }

            /* Styles for individual search result items */
            #search-results li {
                padding: 5px;
                cursor: pointer;
            }

                /* Highlight the selected result */
                #search-results li:hover {
                    background-color: #f2f2f2;
                }
    </style>

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">





                <div class="col-sm-6">
                    <h1>Loan Agreement</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">Loan Agreement</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">

                                    <div class="Sub_Header">Master View</div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Agreement Date*</label>
                                        <div class="col-sm-9">
                                            <input disabled="disabled" type="text" class="form-control" id="txtAgreement_Date" placeholder="dd/mm/yyyy">
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Applicant Name*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtApplicant_Name" placeholder="Applicant Name">
                                            <input type="hidden" id="RegNo" name="RegNo" value="">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Loan Scheme*</label>
                                        <div class="col-sm-9">
                                            <input type="hidden" id="Caste" name="Caste" value="">
                                            <input type="hidden" id="Scheme_Id" name="Scheme_Id" value="">
                                            <input type="hidden" id="Caste_Id" name="Caste_Id" value="">
                                            <input type="text" disabled="disabled" class="form-control" id="txt_LoanScheme" placeholder="Loan Scheme">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Fund*</label>
                                        <div class="col-sm-9">
                                            <select id="dropFund" class="form-control">
                                                <option>[Select]</option>
                                            </select>
                                        </div>
                                    </div>
                                
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Interest*</label>
                                        <div class="col-sm-9">
                                            <input type="number" disabled="disabled" class="form-control" id="txtInterest" placeholder="Interest">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Penal Interest*</label>
                                        <div class="col-sm-9">
                                            <input type="number" disabled="disabled" class="form-control" id="txtPenal_Interest" placeholder="Penal Interest">
                                        </div>
                                    </div>

                                     <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Max-Loan Amount*</label>
                                        <div class="col-sm-9">
                                            <input type="number" disabled="disabled" class="form-control" id="txtLoan_Amount" placeholder="Loan Amount">
                                        </div>
                                    </div>

                                     <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Requested Loan Amount*</label>
                                        <div class="col-sm-9">
                                            <input type="number"  disabled="disabled"  class="form-control" id="txtRequested_Loan_Amount" placeholder="Requested Loan Amount ">
                                        </div>
                                    </div>

                                     <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Sanctioned Loan Amount*</label>
                                        <div class="col-sm-9">
                                            <input type="number"   disabled="disabled"   class="form-control" id="txtSanctioned_Loan_Amount" placeholder="Sanctioned Loan Amount">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Loan Disbursement Installments*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtInstallment_Number" placeholder="Installment Number">
                                            <span id="SPAN_Error_Message_Installment_No" class="validation_message" style="display: none;">Maximum Installment Number is 5</span>
                                        </div>
                                    </div>

                                    <div id="DIV_Installment_Amount">
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Repayment Period*</label>
                                        <div class="col-sm-9">
                                            <input type="number" disabled="disabled" class="form-control" id="txtRepayment_Period" placeholder="Repayment Period">
                                        </div>
                                    </div>

                                    <div class="form-group row" style="display:none;">
                                        <label class="col-sm-3 col-form-label">EMI*</label>
                                        <div class="col-sm-9">
                                            <input disabled="disabled" type="number" class="form-control" id="txtEMI"   placeholder="EMI">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">

                                            <a href="Disbursement_Agreement_DataEntry_Modification_List.aspx" class="btn btn-dark">Back</a>
                                            <a onclick="Delete_All_Disbursement();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>
                                    <input type="hidden" id="hdn_Retirement_Age" />
                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->

            <!-- /.container-fluid -->
    </section>
    <!-- /.content -->


    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 -->
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <!-- Bootstrap4 Duallistbox -->
    <script src="assets/plugins/bootstrap4-duallistbox/jquery.bootstrap-duallistbox.min.js"></script>
    <!-- InputMask -->
    <script src="assets/plugins/moment/moment.min.js"></script>
    <script src="assets/plugins/inputmask/jquery.inputmask.min.js"></script>
    <!-- date-range-picker -->
    <script src="assets/plugins/daterangepicker/daterangepicker.js"></script>
    <!-- bootstrap color picker -->
    <script src="assets/plugins/bootstrap-colorpicker/js/bootstrap-colorpicker.min.js"></script>
    <!-- Tempusdominus Bootstrap 4 -->
    <script src="assets/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js"></script>
    <!-- BS-Stepper -->
    <script src="assets/plugins/bs-stepper/js/bs-stepper.min.js"></script>
    <!-- dropzonejs -->
    <script src="assets/plugins/dropzone/min/dropzone.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script src="assets/js/jquery.dateandtime.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>


    <script>

        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;

        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }
        $(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            // Load_All_Districts();

            // Load_All_Schemes();
            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            Load_All_Application_Issue_By_Id(getParameterByName("Id"));

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }


        })

        $(document).ready(function () {

            // Get the current date
            // Get the current date
            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var month = currentDate.getMonth() + 1; // Adding 1 to get the correct month
            var formattedMonth = (month < 10 ? '0' : '') + month;
         //   alert(formattedMonth);
            var formattedDate = currentDate.getDate() + '/' + formattedMonth + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtAgreement_Date').val(formattedDate);

            $('#FirstInst').hide();
            $('#SecondInst').hide();


            $("#txtInstallment_Number").on('input', function () {
                var Installment_No = $(this).val();
                if (Installment_No > 5) {
                    $("#SPAN_Error_Message_Installment_No").css("display", "block");
                }
                else {
                    $("#SPAN_Error_Message_Installment_No").css("display", "none");
                    $("#DIV_Installment_Amount").empty();
                    for (var i = 1; i <= Installment_No; i++) {
                        $("#DIV_Installment_Amount").append('    <div class="form-group row">'
                            + '        <label class="col-sm-4 col-form-label">Installment Amount ' + i + '*</label>'
                            + '        <div class="col-sm-8">'
                            + '             <input type="number" class="form-control" id="txtInstallment_Amount_' + i + '" placeholder="Installment Amount ' + i + '">'
                            + '        </div>'
                            + '    </div>');
                    }

                    $("#DIV_Installment_Amount .form-control").on('blur', function () {
                        Calculate_EMI();
                    });
                }
            });






        });

        function Calculate_EMI() {
            var Total_Installment_Amount = 0;
            $("#DIV_Installment_Amount .form-control").each(function (index) {
                var inputValue = $(this).val();
                Total_Installment_Amount = Total_Installment_Amount + parseInt(inputValue);

            });


            var interestRate = parseFloat($("#txtInterest").val()) / 1200; // Monthly interest rate
            var numberOfPayments = parseInt($("#txtRepayment_Period").val());  // Number of payments (months)


            var monthlyPayment = calculatePMT(interestRate, numberOfPayments, Total_Installment_Amount);
            monthlyPayment = Math.round(monthlyPayment / 50) * 50;
            $("#txtEMI").val(monthlyPayment);

        }
        function calculatePMT(rate, nper, pv) {
            var pmt = (pv * rate) / (1 - Math.pow(1 + rate, -nper));
            return pmt;
        }

        var Scheme_Id;
        var Caste_Id;
        var Fund;
        var App_Reg_No;

        function Load_All_Application_Issue_By_Id(Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Id: Id }), // If you have parameters
                url: "WebService.asmx/Select_tbl_LoanApp_By_LoanAppId",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        $(".LABEL_APP_Status").text(value.chr_status);
                        var int_loanappid = value.int_loanappid;
                        var Name_Applicant = value.vchr_applname;
                        var vchr_appreceivregno = value.vchr_appreceivregno;
                        var Loan_Amount = value.int_amtsanction;
                        Scheme_Id = value.int_schemeid;
                        Fund = value.vchr_fund;
                        App_Reg_No = vchr_appreceivregno;



                        $("#txtApplicant_Name").val(Name_Applicant);
                        $("#txtApplicationRegNo").val(vchr_appreceivregno);
                        $("#txtLoan_Amount").val(Loan_Amount);
                        Caste_Id = value.vchr_caste;
                        $("#Scheme_Id").val(Scheme_Id);
                        $("#Caste_Id").val(Caste_Id);
                        $("#RegNo").val(vchr_appreceivregno);
                        $("#txtRequested_Loan_Amount").val(value.int_loanamt_req);

                        $("#txtSanctioned_Loan_Amount").val(value.int_amtsanction);

                        // console.log(value.int_amtsanction);

                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Schemes_By_Id(Scheme_Id, Fund);

            });
        }

        function Load_All_Schemes_By_Id(Scheme_Id, Fund) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Schemes_By_ID",
                data: JSON.stringify({ Id: Scheme_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txt_LoanScheme').val('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Scheme = value.Scheme;
                        $('#txt_LoanScheme').val(Scheme);

                        $('#txtInterest').val(value.Loan_Interest);
                        $('#txtPenal_Interest').val(value.Penal_Interest);
                        $('#txtLoan_Amount').val(value.Loan_Amount);
                        $('#txtRepayment_Period').val(value.Loan_Period);

                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_All_Agency_By_LonaApp_Id(getParameterByName("Id"), Fund);
            });
        }

        function Load_All_Agency_By_LonaApp_Id(LoanappId, Fund) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Agency_By_int_loanappid",
                data: JSON.stringify({ int_loanappid: LoanappId }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropFund').empty();
                    $('#dropFund').append('<option value="0">[Select]</option>');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Name = value.Name;
                        var html = '<option value="' + Id + '">' + Name + '</option>';
                        $('#dropFund').append(html);
                        if (Name == Fund) {
                            $("#dropFund").val(Id);
                        }
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Loanreg_By_RegNo_Or_LoanNo()
            });
        }

        function Load_All_Loanreg_By_RegNo_Or_LoanNo() {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_tbl_loanreg_By_RegNo_Or_LoanNo",
                data: JSON.stringify({ appreceivr_RegNo: App_Reg_No, loanNo: "" }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $.each(data.d, function (key, value) {
                        var int_disb_inst = value.int_disb_inst;
                        $("#txtInstallment_Number").val(int_disb_inst);
                        $("#txtRepayment_Period").val(value.int_repay_inst);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Disbursement_By_RegNo(App_Reg_No);
            });
        }


        function Load_All_Disbursement_By_RegNo(App_Reg_No) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_tbl_disbursement_By_RegNo",
                data: JSON.stringify({ appreceivr_RegNo: App_Reg_No, loanNo: "" }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    var i = 0;
                    $.each(data.d, function (key, value) {

                        i = i + 1;
                        $("#DIV_Installment_Amount").append('<div class="form-group row">'
                            + '        <label class="col-sm-4 col-form-label">Installment Amount ' + i + '*</label>'
                            + '        <div class="col-sm-8">'
                            + '             <input type="number" value="' + value.mny_InstallAmt + '" class="form-control" id="txtInstallment_Amount_' + i + '" placeholder="Installment Amount ' + i + '">'
                            + '        </div>'
                            + '    </div>');




                        Calculate_EMI();

                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }
        $('#dropFund').change(function () {
            var optionSelected = $(this).find("option:selected");
            var valueSelected = optionSelected.val();
            if (valueSelected != 0) {
                //   Load_All_Schemes_By_ID(int_schemeid);
            }
            else {
                $('#txtInterest').val('');
                $('#txtPenal_Interest').val('');
                $('#txtRepayment_Period').val('');
            }
        });



        function DisbursementInstallmentsAmountCheck() {
            var Loan_Amount = $('#txtLoan_Amount').val();
            var Total_Installment_Amount = 0;
            $("#DIV_Installment_Amount .form-control").each(function (index) {
                var inputValue = $(this).val();
                Total_Installment_Amount = Total_Installment_Amount + parseInt(inputValue);

            });
            // alert(Loan_Amount);
            // alert(Total_Installment_Amount);
            if (Loan_Amount < Total_Installment_Amount) {
                Toast.fire({
                    icon: 'error',
                    title: 'Sum of All Instalments Should be Equal to Amount Sanctioned !'
                })
                return false;
            }
            //var Disb_Installmnt = $('#txtLn_Disb_Installmnt').val();
            //var Loan_Amount = $('#txtLoan_Amount').val();
            //if (Disb_Installmnt == 1) {
            //    var FistInst = $('#txtFirst_Installmnt').val();
            //    if (Loan_Amount != FistInst) {
            //        Focus_Error($('#txtFirst_Installmnt'));
            //        Toast.fire({
            //            icon: 'error',
            //            title: 'Amount !'
            //        })
            //        return false;
            //    }
            //}
            //else if (Disb_Installmnt == 2) {
            //    var FistInst = $('#txtFirst_Installmnt').val();
            //    var SecondInst = $('#txtSecond_Installmnt').val();
            //    var TotAmount = parseInt(FistInst) + parseInt(SecondInst);
            //    if (Loan_Amount != TotAmount) {
            //        Toast.fire({
            //            icon: 'error',
            //            title: 'Amount !'
            //        })
            //        return false;
            //    }
            //}
        }

        function Load_All_Schemes_By_ID(Scheme_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Schemes_By_ID",
                data: JSON.stringify({ Id: Scheme_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtInterest').val('');
                    $('#txtPenal_Interest').val('');
                    $('#txtRepayment_Period').val('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        $('#txtInterest').val(value.Loan_Interest);
                        $('#txtPenal_Interest').val(value.Penal_Interest);
                        $('#txtRepayment_Period').val(value.Loan_Period);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }



        function Delete_All_Disbursement(Reg_No) {
            var RegNo = $("#RegNo").val();
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Delete_tbl_disbursement_By_RegNo",
                data: JSON.stringify({ RegNo: RegNo }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Save();
            });
        }

        function getFormattedDate(date) {
            date = date.split('/')[2] + "-" + date.split('/')[1] + "-" + date.split('/')[0];
            return date;
        }
        function Convert_Date_Format(Date_Value) {
            var New_Date = '';
            var Split_Vlaues = Date_Value.split('/');
            New_Date = Split_Vlaues[1] + "/" + Split_Vlaues[0] + "/" + Split_Vlaues[2];
            return New_Date;


        }
        function Save() {

            if (DisbursementInstallmentsAmountCheck() == false) {

            }
            else {
                var RegNo = $("#RegNo").val();
                var Fund = $("#dropFund option:selected");
                var AgrDate = $("#txtAgreement_Date");
                var status = "Agreement DataEntry";
                var Loan_Amount = $('#txtLoan_Amount');
                var InstallmntNo = $('#txtInstallment_Number');
                var IntRate = $('#txtInterest');
                var PenalIntRate = $('#txtPenal_Interest');
                var EMI = $('#txtEMI');
                var RepayPeriod = $('#txtRepayment_Period');
                var Sanctioned_Loan_Amount = $('#txtSanctioned_Loan_Amount');

                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/Update_To_tbl_loanapp_Agreement_Status",
                    data: JSON.stringify({ int_loanappid: getParameterByName("Id"), status: status, fund: Fund.text(), agrdate: Convert_Date_Format(AgrDate.val()), int_amtsanction: Sanctioned_Loan_Amount.val() }),

                    contentType: "application/json; charset=utf-8",
                    success: function (data) {

                    },
                    error: function (error) {
                        // alert("error" + error.responseText);
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                        })
                    }
                }).done(function () {
                    $.ajax({
                        type: "POST",
                        dataType: "json",
                        url: "WebService.asmx/Insert_To_tbl_loanreg_NEW",
                        data: JSON.stringify({ vchr_appreceivregno: RegNo, int_Loanno: 0, old_Scheme_Id: 0, old_Scheme_Abrivation: "", old_GLHDNAME: "", old_ACCHDCODE: "0", old_Scheme_Name: "", dte_agrement_date: Convert_Date_Format(AgrDate.val()), dte_dateoforder: Convert_Date_Format("01/01/1900"), mny_Loanamt: Loan_Amount.val(), mny_disbamt: 0, dt_disbdate: Convert_Date_Format("01/01/1900"), int_disb_inst: InstallmntNo.val(), int_inst_disbursed: 0, int_rate_int: IntRate.val(), int_rate_penal: PenalIntRate.val(), mny_repayamt: "0", int_repay_inst: RepayPeriod.val(), mny_loanbal: 0, mny_prin_due: 0, mny_int_due: 0, mny_penal_due: 0, dt_last_repay_date: Convert_Date_Format("01/01/1900"), dt_first_due_date: Convert_Date_Format("01/01/1900"), dt_next_due_date: Convert_Date_Format("01/01/1900"), mny_intramt: 0, mny_intr_emi: 0, mny_intrbal: 0, vchr_stage: "", vchr_offid: "", vchr_oldloanno: 0, int_ots: 0, int_rvcd: 0, dt_rvcd_date: Convert_Date_Format("01/01/1900"), vchr_RRCno: "", dt_RR_Date: Convert_Date_Format("01/01/1900"), int_RR: 0, int_Prefix: 0, int_ForClosing: 0, int_Green: 0, vchr_verify_remark: "", int_rrdemand: 0, vchr_TRRCno: 0, vchr_offidC: "", int_DefaultOts: 0, vchr_RRRemarks: "", int_Ashwas: 0, int_MTR: 0, int_Ashwas_M: 0, int_repay_inst_Ext: 0, int_SR: 0, dt_SRDate: Convert_Date_Format( "01/01/1900") }),
                              contentType: "application/json; charset=utf-8",
                        success: function (data) {
                        },
                        error: function (error) {
                            // alert("error" + error.responseText);
                            Swal.fire({
                                icon: 'error',
                                title: 'Oops...',
                                text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                            })
                        }
                    }).done(function () {
                        if ($("#DIV_Installment_Amount .form-control").length > 0) {
                            $("#DIV_Installment_Amount .form-control").each(function (index) {
                                var inputValue = $(this).val();
                                index = index + 1;
                                $.ajax({
                                    type: "POST",
                                    dataType: "json",
                                    url: "WebService.asmx/Insert_To_tbl_disbursement_New",
                                    data: JSON.stringify({ vchr_regno: RegNo, int_instalNo: index, mny_InstallAmt: inputValue, mny_disbamt: 0, mny_disbamtbal: 0, dt_disbdate: "01/01/1900", vchr_vouchrno: "", vchr_chkno: "", vchr_accno: "", vchr_loanno: "", vchr_verify: "", dt_verify: "01/01/1900", int_BankAccNo: "", vchr_IFSC: "", dt_UCDate: "01/01/1900", int_Disb: 0 }),
                                    contentType: "application/json; charset=utf-8",
                                    success: function (data) {
                                    },
                                    error: function (error) {
                                        // alert("error" + error.responseText);
                                        Swal.fire({
                                            icon: 'error',
                                            title: 'Oops...',
                                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                                        })
                                    }
                                }).done(function () {
                                    if (parseInt(InstallmntNo.val()) == index) {
                                        Swal.fire({
                                            icon: 'success',
                                            title: 'Message',
                                            text: 'Disbursement Agreement Data Entry Successfully Submitted !',

                                        }).then((result) => {
                                            if (result.isConfirmed) {
                                                // OK button clicked
                                                //  Next();
                                                location.href = 'Disbursement_Agreement_DataEntry_Modification_List.aspx';
                                                //  Your code here
                                            }
                                        });
                                    }

                                });

                            });
                        }
                        else {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Message',
                                    text: 'Disbursement Agreement Data Entry Successfully Submitted !',

                                }).then((result) => {
                                    if (result.isConfirmed) {
                                        // OK button clicked
                                        //  Next();
                                        location.href = 'Disbursement_Agreement_DataEntry_Modification_List.aspx';
                                        //  Your code here
                                    }
                                });
                             
                        }

                       



                    });
                });

            }
        }

        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }
        function Is_Number(inputText) {
            if (isNaN(inputText)) {
                return false;
            } else {
                return true;
            }
        }

        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }

        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }




    </script>



</asp:Content>
































