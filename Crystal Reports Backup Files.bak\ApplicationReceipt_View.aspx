﻿<%@ Page Title="Application Receipt | View" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="ApplicationReceipt_View.aspx.cs" Inherits="KSDCSCST_Portal.ApplicationReceipt_View" %>


<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- daterange picker -->
    <link rel="stylesheet" href="assets/plugins/daterangepicker/daterangepicker.css">
    <!-- iCheck for checkboxes and radio inputs -->
    <link rel="stylesheet" href="assets/plugins/icheck-bootstrap/icheck-bootstrap.min.css">
    <!-- Bootstrap Color Picker -->
    <link rel="stylesheet" href="assets/plugins/bootstrap-colorpicker/css/bootstrap-colorpicker.min.css">
    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet" href="assets/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css">
    <!-- Select2 -->
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">
    <!-- Bootstrap4 Duallistbox -->
    <link rel="stylesheet" href="assets/plugins/bootstrap4-duallistbox/bootstrap-duallistbox.min.css">
    <!-- BS Stepper -->
    <link rel="stylesheet" href="assets/plugins/bs-stepper/css/bs-stepper.min.css">
    <!-- dropzonejs -->
    <link rel="stylesheet" href="assets/plugins/dropzone/min/dropzone.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">
    <style>
        input:disabled {
            border: 0;
        }
    </style>
    <style>
        /* Styles for the search input */
        txtPostOffices {
            width: 300px;
            padding: 10px;
            font-size: 16px;
        }

        /* Styles for the search results container */
        #search-results {
            list-style: none;
            padding: 0;
            margin-top: 5px;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
        }

            /* Styles for individual search result items */
            #search-results li {
                padding: 5px;
                cursor: pointer;
            }

                /* Highlight the selected result */
                #search-results li:hover {
                    background-color: #f2f2f2;
                }
    </style>

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Application Receipt</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">Application Receipt</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">
                                    <div class="Sub_Header_Status row">

                                        <div class="col-sm-12">
                                            <i class="fa fa-battery-half" aria-hidden="true"></i><span class="SPAN_APP_Status_Text">Application Status</span>
                                            <label class="LABEL_APP_Status"></label>
                                        </div>

                                    </div>
                                    <div class="Sub_Header">Applicant Details</div>
                                    <div class="form-group row">
                                        <label for="inputName" class="col-sm-3 col-form-label">Today's Date *</label>
                                        <div class="col-sm-9">
                                            <input type="text" disabled="disabled" class="form-control" id="txtApplication_Date" placeholder="Today's Date(DD/MM/YYYY)">
                                        </div>
                                    </div>
                                    <div class="form-group row" style="display: none;" id="DIV_Application_Register_No">
                                        <label for="inputName" class="col-sm-3 col-form-label">Application Register No</label>
                                        <div class="col-sm-9">
                                            <input disabled="disabled" type="text" class="form-control" id="txtApplicationRegNo" value="" placeholder="">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Name of Applicant *</label>
                                        <div class="col-sm-9">
                                            <input type="text" disabled="disabled" class="form-control" id="txtApplicant_Name" placeholder="Name of Applicant">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Aadhar Number*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtAadharNumber" placeholder="Aadhar Number">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Loan Scheme *</label>
                                        <div class="col-sm-9">
                                            <select id="dropScheme" class="form-control">
                                                <option>Select</option>

                                            </select>
                                        </div>
                                    </div>
                                    <div class="Sub_Header">Address Details</div>

                                    <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">House Name/No *</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtHouseName" placeholder="House Name/No">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Lane1 *</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtPlace1" placeholder="Lane1">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Lane2 *</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtPlace2" placeholder="Lane2">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">District *</label>
                                        <div class="col-sm-9">
                                            <select id="dropDistrict" disabled="disabled" class="form-control">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Branch *</label>
                                        <div class="col-sm-9">
                                            <select id="dropSubDistrict" disabled="disabled" class="form-control">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Pincode *</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtPincode" placeholder="Search...">
                                            <ul id="search-results"></ul>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Post Office *</label>
                                        <div class="col-sm-9">
                                            <input type="text" disabled="disabled" class="form-control" id="txtPostOffices" placeholder="Post Office">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Phone No *</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtPhoneNo" placeholder="Phone No">
                                            <span style="font-style: italic; color: red; font-size: 14px; height: 35px; display: block;">seperate numbers by comma ","</span>
                                        </div>
                                    </div>


                                    <div class="Sub_Header">Personal Details</div>
                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Caste *</label>
                                        <div class="col-sm-9">
                                            <select id="dropCast" class="form-control">
                                                <option value="0">Select</option>

                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Sub Caste *</label>
                                        <div class="col-sm-9">
                                            <select id="dropSubCast" class="form-control">
                                                <option value="0">Select</option>


                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Sex *</label>
                                        <div class="col-sm-9">
                                            <select id="dropSex" class="form-control">
                                                <option value="0">Select Sex</option>
                                                <option value="Male">Male</option>
                                                <option value="Female">Female</option>
                                                <option value="Other">Other</option>

                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Date of Birth *</label>
                                        <div class="col-sm-9">
                                            <input class="dateandtime form-control" id="txtDOB" type="text" name="machin-b" value="">
                                            <%-- <div class="input-group date" id="txtDOB" data-target-input="nearest">
                                                <input type="text" id="txtDOB_Input" class="form-control datetimepicker-input" data-target="#txtDOB" />
                                                <div class="input-group-append" data-target="#txtDOB" data-toggle="datetimepicker">
                                                    <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                                                </div>
                                            </div>--%>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Age *</label>
                                        <div class="col-sm-9">
                                            <input type="text" disabled="disabled" class="form-control" id="txtAge" value="0" placeholder="Age">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Annual Income *</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtAnnualIncome" placeholder="Annual Income">
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">

                                            <a href="ApplicationReceipt_List.aspx" class="btn btn-dark">Back</a>
                                            <a onclick="Save();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->


    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 -->
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <!-- Bootstrap4 Duallistbox -->
    <script src="assets/plugins/bootstrap4-duallistbox/jquery.bootstrap-duallistbox.min.js"></script>
    <!-- InputMask -->
    <script src="assets/plugins/moment/moment.min.js"></script>
    <script src="assets/plugins/inputmask/jquery.inputmask.min.js"></script>
    <!-- date-range-picker -->
    <script src="assets/plugins/daterangepicker/daterangepicker.js"></script>
    <!-- bootstrap color picker -->
    <script src="assets/plugins/bootstrap-colorpicker/js/bootstrap-colorpicker.min.js"></script>
    <!-- Tempusdominus Bootstrap 4 -->
    <script src="assets/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js"></script>
    <!-- BS-Stepper -->
    <script src="assets/plugins/bs-stepper/js/bs-stepper.min.js"></script>
    <!-- dropzonejs -->
    <script src="assets/plugins/dropzone/min/dropzone.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>


    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="assets/js/jquery.dateandtime.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>




    <script>

        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;
        const $searchInput = $('#txtPincode');
        // Results container element
        const $searchResults = $('#search-results');
        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }
        $(function () {
            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });


            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtApplication_Date').val(formattedDate);


            //Date picker
            //$('#txtDOB').datetimepicker({
            //    format: 'DD/MM/YYYY',
            //    allowInputToggle: false
            //});

            $('.dateandtime').dateAndTime();

            Load_All_Schemes();


            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }


            $(document).on("focusout", ".form-control", function () {
                $(".form-control").removeAttr("style");

                var This_Id = $(this).attr("id");
                var This_Control = $(this);
                if (This_Id == "txtApplicant_Name") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Name of Applicant is required !'
                        })
                    }
                    else if (!Is_Valid_Text(This_Control.val().trim())) {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Special characters or Numbers not allowed in Name of Applicant !'
                        })
                    }
                }
                else if (This_Id == "dropScheme") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Loan Scheme is required !'
                        })
                    }
                }
                else if (This_Id == "dropCast") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Caste is required !'
                        })
                    }
                }
                else if (This_Id == "dropSubCast") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Sub Caste is required !'
                        })
                    }
                }
                else if (This_Id == "txtPhoneNo") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Phone No is required !'
                        })
                    }
                }
                else if (This_Id == "txtHouseName") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'House Name/No is required !'
                        })
                    }

                }
                else if (This_Id == "txtPlace1") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Lane1 is required !'
                        })
                    }

                }
                else if (This_Id == "txtPlace2") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Lane2 is required !'
                        })
                    }

                }
                else if (This_Id == "txtPincode") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Pincode is required !'
                        })
                    }

                }
                else if (This_Id == "txtPhoneNo") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Phone No is required !'
                        })
                    }

                }
                else if (This_Id == "dropSex") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Sex is required !'
                        })
                    }
                }
                else if (This_Id == "txtDOB") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Date of Birth is required !'
                        })
                    }
                }
                else if (This_Id == "txtAnnualIncome") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Annual Income is required !'
                        })
                    }
                    else if (!Is_Number(This_Control.val().trim())) {

                        Toast.fire({
                            icon: 'error',
                            title: 'Annual Income only accept numbers !'
                        })
                    }
                }

                // Your common focusout function code goes here
                //   alert("Input value changed: " + value);
            });


            //$(document).on("input", ".form-control", function () {
            //    // Get the current value of the input
            //    var currentValue = $(this).val();

            //    // Convert the value to propcase
            //    var propcaseValue = currentValue.replace(/\w\S*/g, function (txt) {
            //        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
            //    });

            //    // Update the input with the propcase value
            //    $(this).val(propcaseValue);
            //});

            //   Load_All_Districts();
            $searchInput.on('input', function () {
                const query = $searchInput.val();
                fetchAutocompleteResults(query);
            });

            // Event handler for selecting a result
            $searchResults.on('click', 'li', function () {
                // alert($(this).html());
                $("#txtPincode").val($(this).attr("data-pincode"));
                $("#txtPostOffices").val($(this).html());
                //  const selectedResult = $(this).text();
                //$searchInput.val(selectedResult);
                $searchResults.hide();
            });

        })

        $(document).ready(function () {


            var previousValue = $("#txtDOB").val(); // Initialize with the initial value

            $("#txtDOB").on("input", function () {
                var currentValue = $(this).val();

                // Check if the input value has changed
                if (currentValue !== previousValue) {
                    $("#txtAge").val(calculateAge(currentValue));
                    //alert("Date selected or changed:"+ currentValue);
                    // Add your custom code here to handle the selected date
                    previousValue = currentValue; // Update the previous value
                }
            });



            $("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
            $("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");



        });

        function calculateAge(dateOfBirth) {
            // Parse the date of birth string into a Date object
            const dob = new Date(dateOfBirth);

            // Get the current date
            const currentDate = new Date();

            // Calculate the difference in milliseconds
            const ageInMilliseconds = currentDate - dob;

            // Convert the milliseconds to years
            const ageInYears = ageInMilliseconds / (365 * 24 * 60 * 60 * 1000);

            // Round down to the nearest whole number to get the age
            const age = Math.floor(ageInYears);
            if (isNaN(age))
                age = 0;

            return age;
        }

        function Load_All_Districts() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropDistrict').empty();
                    $('#dropDistrict').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var District_Name = value.District_Name;
                        var html = '<option value="' + Id + '">' + District_Name + '</option>';
                        if (Id != 15) {
                            $('#dropDistrict').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $('#dropDistrict').change(function () {
                    Load_All_Sub_Districts($(this).val());
                });

            });

        }
        function Load_All_Sub_Districts(District_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Districts",
                data: JSON.stringify({ District_Id: District_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubDistrict').empty();
                    $('#dropSubDistrict').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Sub_District_Name = value.Sub_District_Name;
                        var html = '<option value="' + Id + '">' + Sub_District_Name + '</option>';

                        $('#dropSubDistrict').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });

        }
        function fetchAutocompleteResults(query) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ pincode: query }), // If you have parameters
                url: "WebService.asmx/Select_All_PostOffices",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $searchResults.empty();
                    if (data.d.length > 0) {

                        $searchResults.show();
                    } else {
                        $searchResults.hide();
                        $("#txtPostOffices").val('');
                    }

                    $.each(data.d, function (key, value) {
                        $searchResults.append('<li data-pincode="' + value.Pincode + '">' + value.Post_office + '</li>');



                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });


        }
        function Load_All_Schemes() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Schemes",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropScheme').empty();
                    $('#dropScheme').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var IsActive = value.IsActive;
                        var Scheme = value.Scheme;
                        var html = '<option data-Min_Age="' + value.Min_Age + '" data-Max_Age="' + value.Max_Age + '" data-Anninc_Rural_Max="' + value.Anninc_Rural_Max + '" data-Anninc_Urban_Max="' + value.Anninc_Urban_Max + '"  value="' + Id + '">' + Scheme + '</option>';
                        if (IsActive == 1) {
                            $('#dropScheme').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_All_Cast();

            });

        }

        function Load_All_Cast() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Cast",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropCast').empty();
                    $('#dropCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var Cast = value.Cast;
                        var html = '<option value="' + Id + '">' + Cast + '</option>';

                        $('#dropCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $('#dropCast').change(function () {
                    Load_All_SubCast($(this).val());
                });

                Load_LoanApp_By_IssueId(getParameterByName("Id"));
            });

        }

        var District_Id;
        var SubDistrict_id;
        var Cast_Id;
        var Sub_Cast_Id;

        function Load_LoanApp_By_IssueId(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Loan_App_Issue_Id: Id }), // If you have parameters
                url: "WebService.asmx/Select_LoanApp_By_IssueId",
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    if (data.d.length == 0) {
                        Load_All_Application_Issue_By_Id(Id);
                    }
                    else {
                        $.each(data.d, function (key, value) {
                            alert(value.chr_status);
                            var int_loanappid = value.int_loanappid;
                            District_Id = value.vchr_district;
                            SubDistrict_id = value.vchr_subdistrict;
                            var Name_Applicant = value.vchr_applname;
                            var Application_Submitted_Date = value.Submitted_Date;
                            var Scheme_Id = value.int_schemeid;

                            Cast_Id = value.vchr_caste;
                            Sub_Cast_Id = value.SubCast;

                            var vchr_appreceivregno = value.vchr_appreceivregno;
                            var vchr_hsename = value.vchr_hsename;
                            var vchr_place1 = value.vchr_place1;
                            var vchr_place2 = value.vchr_place2;
                            var vchr_post = value.vchr_post;
                            var int_pincode = value.int_pincode;
                            var vchr_phno = value.vchr_phno;
                            var vchr_sex = value.vchr_sex;
                            var dte_dob = value.dte_dob;
                            var Age = value.Age;
                            var int_anninc = value.int_anninc;



                            $("#txtApplication_Date").val(Application_Submitted_Date);
                            $("#dropDistrict").val(District_Id);

                            $("#txtApplicant_Name").val(Name_Applicant);
                            $("#dropScheme").val(Scheme_Id);
                            $("#dropCast").val(Cast_Id);





                            $("#DIV_Application_Register_No").css("display", "flex");
                            $("#txtApplicationRegNo").val(vchr_appreceivregno);
                            $("#txtHouseName").val(vchr_hsename);
                            $("#txtPlace1").val(vchr_place1);
                            $("#txtPlace2").val(vchr_place2);
                            $("#txtPostOffices").val(vchr_post);
                            $("#txtPincode").val(int_pincode);
                            $("#txtPhoneNo").val(vchr_phno);
                            $("#dropSex").val(vchr_sex);



                            const inputDate = dte_dob;
                            const parts = inputDate.split('/');
                            if (parts.length === 3) {
                                const yyyy = parts[2];
                                const mm = parts[1];
                                const dd = parts[0];

                                const formattedDate = `${yyyy}-${mm}-${dd}`;

                                // Set the value of the date input
                                $('#txtDOB').val(formattedDate);
                            }

                            //  $("#txtDOB").val(dte_dob);
                            $("#txtAge").val(Age);
                            $("#txtAnnualIncome").val(int_anninc);


                            Load_Sub_District_And_Set(District_Id, SubDistrict_id, Cast_Id, Sub_Cast_Id);



                        });

                    }


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                //Load_Sub_District_And_Set(District_Id, SubDistrict_id, Cast_Id, Sub_Cast_Id);

            });
        }



        function Load_All_Application_Issue_By_Id(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Id: Id }), // If you have parameters
                url: "WebService.asmx/Select_All_tbl_Loan_App_Issue_By_ID",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        $(".LABEL_APP_Status").text("Application Issued");

                        var Id = value.Id;
                        District_Id = value.District_Id;
                        SubDistrict_id = value.SubDistrict_id;
                        var Name_Applicant = value.Name_Applicant;

                        var District_Name = value.District_Name;
                        var Sub_District_Name = value.Sub_District_Name;

                        var PhoneNo = value.PhoneNo;
                        var Application_Submitted_Date = value.Application_Submitted_Date;
                        var Scheme_Id = value.Scheme_Id;

                        Cast_Id = value.Cast_Id;
                        Sub_Cast_Id = value.Sub_Cast_Id;
                        var Reciept_No = value.Reciept_No;
                        var Amount = value.Amount;
                        var Remarks = value.Remarks;
                        var AadharNumber = value.AadharNumber;

                        $("#txtApplication_Date").val(Application_Submitted_Date);

                        //  $("#txtDistrict").val(District_Name);
                        // $("#txtSubDistrict").val(Sub_District_Name);

                        //    $("#dropDistrict").empty();
                     //   $("#dropSubDistrict").empty();

                        //     $("#dropDistrict").append("<option value='" + District_Id+"'>" + District_Name+"</option>");
                        //     $("#dropSubDistrict").append("<option value='" + SubDistrict_id + "'>" + Sub_District_Name + "</option>");

                        $("#txtApplicant_Name").val(Name_Applicant);
                        $("#dropScheme").val(Scheme_Id);
                        $("#dropCast").val(Cast_Id);
                        $("#txtPhoneNo").val(PhoneNo);
                        $("#txtReceiptNo").val(Reciept_No);
                        $("#txtAmount").val(Amount);
                        $("#txtRemarks").val(Remarks);
                        $("#txtApplicationNo").val(Id);
                        $("#txtAadharNumber").val(AadharNumber);







                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_Sub_District_And_Set(District_Id, SubDistrict_id, Cast_Id, Sub_Cast_Id);

            });
        }
        var Sub_District_Count;
        function Load_Sub_District_And_Set(District_Id, SubDistrict_id, Cast_Id, Sub_Cast_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Districts",
                data: JSON.stringify({ District_Id: District_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    //$('#dropSubDistrict').empty();
                    //$('#dropSubDistrict').append('<option value="0">Select</option>');
                    //Sub_District_Count = data.d.length;
                    //$.each(data.d, function (key, value) {
                    //    var Id = value.Id;
                    //    var Sub_District_Name = value.Sub_District_Name;
                    //    var html = '<option value="' + Id + '">' + Sub_District_Name + '</option>';

                    //    $('#dropSubDistrict').append(html);

                    //});


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                if (Sub_District_Count == 0) {
                    $("#dropSubDistrict").val('0');
                }
                else {
                    $("#dropSubDistrict").val(SubDistrict_id);
                }


                Load_All_SubCast_And_Set(Cast_Id, Sub_Cast_Id);
            });

        }

        function Load_All_SubCast_And_Set(Cast_Id, Sub_Cast_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Cast",
                data: JSON.stringify({ Cast_Id: Cast_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubCast').empty();
                    $('#dropSubCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var SubCast = value.SubCast;
                        var html = '<option value="' + Id + '">' + SubCast + '</option>';

                        $('#dropSubCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                $("#dropSubCast").val(Sub_Cast_Id);

            });

        }


        function Load_All_SubCast(Cast_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Cast",
                data: JSON.stringify({ Cast_Id: Cast_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubCast').empty();
                    $('#dropSubCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var SubCast = value.SubCast;
                        var html = '<option value="' + Id + '">' + SubCast + '</option>';

                        $('#dropSubCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {


            });

        }
        function Save() {

            var Application_Date = $("#txtApplication_Date").val();
            var ApplicationRegNo = $("#txtApplicationRegNo").val();
            var Applicant_Name = $("#txtApplicant_Name").val();
            var Scheme = $("#dropScheme").val();
            var HouseName = $("#txtHouseName").val();
            var Place1 = $("#txtPlace1").val();
            var Place2 = $("#txtPlace2").val();
            var District = $("#dropDistrict").val();
            var SubDistrict = $("#dropSubDistrict").val();
            var PostOffices = $("#txtPostOffices").val();
            var Pincode = $("#txtPincode").val();
            var PhoneNo = $("#txtPhoneNo").val();
            var Cast = $("#dropCast").val();
            var SubCast = $("#dropSubCast").val();
            var Age = $("#txtAge").val();
            var AnnualIncome = $("#txtAnnualIncome").val();
            var Sex = $("#dropSex").val();
            var DOB_Input = $("#txtDOB").val();
            var AadharNumber = $("#txtAadharNumber").val();


            if (Applicant_Name.trim() == "") {
                //  alert("Name is required");
                Toast.fire({
                    icon: 'error',
                    title: 'Name of Applicant is required !'
                })
            }
            else if (!Is_Valid_Text(Applicant_Name.trim())) {
                //  alert("Name is required");
                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed !'
                })
            }
            if (AadharNumber.trim() == "") {
                //  alert("Name is required");
                Toast.fire({
                    icon: 'error',
                    title: 'Aadhar Number is required !'
                })
            }

            else if (Scheme == "0") {

                Toast.fire({
                    icon: 'error',
                    title: 'Loan Scheme is required !'
                })
            }
            else if (HouseName == "") {

                Toast.fire({
                    icon: 'error',
                    title: 'House Name/No is required !'
                })
            }

            else if (Place1 == "") {

                Toast.fire({
                    icon: 'error',
                    title: 'Lane1 is required !'
                })
            }

            else if (Place2 == "") {

                Toast.fire({
                    icon: 'error',
                    title: 'Lane2 is required !'
                })
            }
            else if (Pincode == "") {

                Toast.fire({
                    icon: 'error',
                    title: 'Pincode is required !'
                })
            }


            else if (Cast == "0") {

                Toast.fire({
                    icon: 'error',
                    title: 'Caste is required !'
                })
            }
            else if (SubCast == "0") {

                Toast.fire({
                    icon: 'error',
                    title: 'Sub Caste is required !'
                })
            }
            else if (PhoneNo.trim() == "") {

                Toast.fire({
                    icon: 'error',
                    title: 'Phone No is required !'
                })
            }

            else if (Sex.trim() == "") {

                Toast.fire({
                    icon: 'error',
                    title: 'Sex is required !'
                })
            }
            else if (DOB_Input.trim() == "") {

                Toast.fire({
                    icon: 'error',
                    title: 'Date of Birth is required !'
                })
            }
            else if (AnnualIncome.trim() == "") {

                Toast.fire({
                    icon: 'error',
                    title: 'Annual Income is required !'
                })
            }
            else if (!Is_Number(AnnualIncome.trim())) {

                Toast.fire({
                    icon: 'error',
                    title: 'Annual Income only accept numbers !'
                })
            }

            else if (!Check_Age_Limit()) {

                Toast.fire({
                    icon: 'error',
                    title: 'Age is below the limit of the scheme you selected !'
                })
            }

            //else if (!Check_Annual_Income_Limit()) {

            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Annual Income is below the limit of the scheme you selected !'
            //    })
            //}


            else {

                if (PhoneNo.trim() != "") {

                    var values = PhoneNo.trim().split(",");
                    for (var i = 0; i < values.length; i++) {
                        if (!Is_Valid_Mobile_Number(values[i])) {
                            Toast.fire({
                                icon: 'error',
                                title: 'Enter valid phone number !'
                            })
                            return;
                        }
                    }
                }
                var Application_Reg_No = "";


                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/New_Insert_To_tbl_loanapp",
                    data: JSON.stringify({ Loan_App_Issue_Id: getParameterByName("Id"), vchr_appreceivregno: ApplicationRegNo, int_no: 0, vchr_applname: Applicant_Name, vchr_hsename: HouseName, vchr_place1: Place1, vchr_place2: Place2, vchr_phno: PhoneNo, vchr_caste: Cast, vchr_sex: Sex, vchr_district: District, vchr_subdistrict: SubDistrict, int_anninc: AnnualIncome, vchr_offid: 0, int_schemeid: Scheme, subCast: SubCast, dte_dob: DOB_Input, age: Age, vchr_post: PostOffices, int_pincode: Pincode, status: "Application Receipt", AadharNumber: AadharNumber }), // If you have parameters
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        if (data.d.Status == "Success") {
                            Application_Reg_No = data.d.Application_Reg_No;
                        }
                    },
                    error: function (error) {


                        // alert("error" + error.responseText);
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                        })
                    }
                }).done(function () {
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Application Receipt Successfully Updated and Generated Application Reg No : ' + Application_Reg_No,

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked

                            location.href = 'ApplicationReceipt_List.aspx';
                            // Your code here
                        }
                    });

                });

            }




        }

        function Is_Number(inputText) {


            if (isNaN(inputText)) {
                return false;
            } else {
                return true;
            }


        }

        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }

        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }

        function Check_Age_Limit() {
            var Age = $("#txtAge").val();
            var Min_Age = $("#dropScheme option:selected").attr("data-min_age");
            var Max_Age = $("#dropScheme option:selected").attr("data-max_age");

            Age = parseInt(Age);
            Min_Age = parseInt(Min_Age);
            Max_Age = parseInt(Max_Age);

            if (Max_Age != 0) {
                if (Min_Age <= Age && Max_Age >= Age) {
                    return true;
                }
                else {
                    return false;
                }
            }
            else {
                return true;
            }

        }

        function Check_Annual_Income_Limit() {
            var Annual_Income = $("#txtAnnualIncome").val();
            var Anninc_Rural_Max = $("#dropScheme option:selected").attr("data-anninc_rural_max");
            var Anninc_Urban_Max = $("#dropScheme option:selected").attr("data-anninc_urban_max");

            Annual_Income = parseInt(Annual_Income);
            Anninc_Rural_Max = parseInt(Anninc_Rural_Max);
            Anninc_Urban_Max = parseInt(Anninc_Urban_Max);

            if (Anninc_Rural_Max <= Annual_Income && Anninc_Urban_Max <= Annual_Income) {
                return true;
            }
            else {
                return false;
            }

        }



    </script>
</asp:Content>
