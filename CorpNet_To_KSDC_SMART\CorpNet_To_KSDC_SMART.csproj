﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{4517D1EB-8434-499A-8E3D-2D89E3DF4ED7}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>CorpNet_To_KSDC_SMART</RootNamespace>
    <AssemblyName>CorpNet_To_KSDC_SMART</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ClosedXML, Version=0.102.1.0, Culture=neutral, PublicKeyToken=fd1eb21b62ae805b, processorArchitecture=MSIL">
      <HintPath>..\packages\ClosedXML.0.102.1\lib\netstandard2.0\ClosedXML.dll</HintPath>
    </Reference>
    <Reference Include="DocumentFormat.OpenXml, Version=2.16.0.0, Culture=neutral, PublicKeyToken=8fb06cb64d019a17, processorArchitecture=MSIL">
      <HintPath>..\packages\DocumentFormat.OpenXml.2.16.0\lib\net46\DocumentFormat.OpenXml.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="ExcelNumberFormat, Version=1.1.0.0, Culture=neutral, PublicKeyToken=23c6f5d73be07eca, processorArchitecture=MSIL">
      <HintPath>..\packages\ExcelNumberFormat.1.1.0\lib\net20\ExcelNumberFormat.dll</HintPath>
    </Reference>
    <Reference Include="Irony, Version=1.0.11.0, Culture=neutral, PublicKeyToken=ca48ace7223ead47, processorArchitecture=MSIL">
      <HintPath>..\packages\Irony.NetCore.1.0.11\lib\net461\Irony.dll</HintPath>
    </Reference>
    <Reference Include="KSDC, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\KSDC\bin\Debug\KSDC.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IO.RecyclableMemoryStream, Version=1.4.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IO.RecyclableMemoryStream.1.4.1\lib\net46\Microsoft.IO.RecyclableMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="SixLabors.Fonts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=d998eea7b14cab13, processorArchitecture=MSIL">
      <HintPath>..\packages\SixLabors.Fonts.1.0.0\lib\netstandard2.0\SixLabors.Fonts.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Annotations.5.0.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.IO, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Packaging, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Packaging.6.0.0\lib\net461\System.IO.Packaging.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.4\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.0\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.4.7.0\lib\netstandard2.0\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.RegularExpressions.4.3.1\lib\net463\System.Text.RegularExpressions.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks" />
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="XLParser, Version=1.5.2.0, Culture=neutral, PublicKeyToken=63397e1e46bb91b4, processorArchitecture=MSIL">
      <HintPath>..\packages\XLParser.1.5.2\lib\net461\XLParser.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Compile Include="Check_Year_End_Record_Exist_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Employee_Confirmation_Letter_Print_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="GetMenuItems_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="KSDC_Entites.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Login_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Migration_Select_All_Transaction_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="New_Insert_To_tbl_loanapp_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="New_Migration.xaml.cs">
      <DependentUpon>New_Migration.xaml</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Agency_By_Cast_And_Scheme_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Agency_By_ID_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Agency_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Agency_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Bank_Details_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Block_By_District_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Cast_By_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Cast_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Constituency_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Corporation_By_District_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Disbursement_Details_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Districts_By_Village_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Districts_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_EmpSurety_By_Lnaccid_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Lnaaccdtls_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_LoanDisburment_By_Lnaccid_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Loanee_Details_By_Lnaccid_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_LoanRepayment_Lnaccid_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_loanTrans_LoanNo_Migration_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_loanTrans_LoanNo_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Lok_Sabha_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Municipality_By_District_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Offices_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_OtherRcpt_By_Lnaccid_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Panchayath_By_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Panjayath_By_Block_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Personal_Ledger_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_PostOffices_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Projects_ACTIVE_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Projects_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Project_By_ID_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Roles_By_ID_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Roles_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Schemes_By_ID_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Schemes_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Sectors_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Sector_By_ID_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Sub_Cast_By_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Sub_Cast_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Sub_Districts_By_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Sub_Districts_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Surety_Confirmation_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Surety_Type_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Department_int_deptid_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Department_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Designations_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_EmpSurety_By_LoanAppId_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_FDLICSurety_By_LoanAppId_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_KSDC_SMART_Issues_All_Branches_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_KSDC_SMART_Issues_Attachments_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_KSDC_SMART_Issues_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_LandSurety_By_LoanAppId_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_LoanApp_By_Status_Disbursement_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_LoanApp_By_Status_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_LoanApp_By_Status_Surety_Confirm_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_LoanApp_By_Status_Surety_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_loanreg_By_LoanNo_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Loan_App_Issue_By_ID_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Loan_App_Issue_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Logs_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Projects_By_Sector_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Sectors_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Taluk_By_District_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Taluk_By_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Users_By_ID_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Users_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Villages_By_Taluk_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Villages_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Village_By_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Village_By_Office_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Last_Loan_Trans_Details_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Last_Reg_Details_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_LoanAppId_By_LoanNo_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_LoanApp_By_IssueId_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Lonee_Details_And_Account_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Lonee_Details_By_Loan_No_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Pricipale_Due_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Scheme_By_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Sub_Caste_Details_By_Sub_Caste_Name_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_agriloan_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_BankDetails_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_disbursement_By_RegNo_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_eduloanapp_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_empsurety_By_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_FDLICsurety_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_foreignloan_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_House_Loan_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_hsemaintanenceloan_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_KSDC_SMART_Issues_By_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_KSDC_SMART_Issues_Messages_By_Issue_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_landsurety_By_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_LoanApp_By_LoanAppId_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_loanapp_By_Reg_No_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_loanreg_By_RegNo_Or_LoanNo_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_Logs_By_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_marriageloan_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_personal_loan_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_selfloan_By_loanappid_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_Sub_Reg_Office_By_District_Id_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_workcapital_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_To_tbl_veh_hme_loan_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Year_End_Record_Result.cs">
      <DependentUpon>KSDC_Entites.tt</DependentUpon>
    </Compile>
    <Page Include="MainWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Compile Include="App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="KSDC_Entites.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>KSDC_Entites.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="KSDC_Entites.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>KSDC_Entites.edmx</DependentUpon>
    </Compile>
    <Compile Include="MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Page Include="New_Migration.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EntityDeploy Include="KSDC_Entites.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>KSDC_Entites.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <None Include="KSDC_Entites.edmx.diagram">
      <DependentUpon>KSDC_Entites.edmx</DependentUpon>
    </None>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="KSDC_Entites.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>KSDC_Entites.Context.cs</LastGenOutput>
      <DependentUpon>KSDC_Entites.edmx</DependentUpon>
    </Content>
    <Content Include="KSDC_Entites.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>KSDC_Entites.edmx</DependentUpon>
      <LastGenOutput>KSDC_Entites.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>