/* Copyright (c) Business Objects 2006. All rights reserved. */

// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "\u0421\u0435\u0433\u043E\u0434\u043D\u044F";
var L_January   = "\u042F\u043D\u0432\u0430\u0440\u044C";
var L_February  = "\u0424\u0435\u0432\u0440\u0430\u043B\u044C";
var L_March     = "\u041C\u0430\u0440\u0442";
var L_April     = "\u0410\u043F\u0440\u0435\u043B\u044C";
var L_May       = "\u041C\u0430\u0439";
var L_June      = "\u0418\u044E\u043D\u044C";
var L_July      = "\u0418\u044E\u043B\u044C";
var L_August    = "\u0410\u0432\u0433\u0443\u0441\u0442";
var L_September = "\u0421\u0435\u043D\u0442\u044F\u0431\u0440\u044C";
var L_October   = "\u041E\u043A\u0442\u044F\u0431\u0440\u044C";
var L_November  = "\u041D\u043E\u044F\u0431\u0440\u044C";
var L_December  = "\u0414\u0435\u043A\u0430\u0431\u0440\u044C";
var L_Su        = "\u0412\u0441";
var L_Mo        = "\u041F\u043D";
var L_Tu        = "\u0412\u0442";
var L_We        = "\u0421\u0440";
var L_Th        = "\u0427\u0442";
var L_Fr        = "\u041F\u0442";
var L_Sa        = "\u0421\u0431";

// Strings for prompts.js and prompts_param.js
var L_YYYY          = "\u0433\u0433\u0433\u0433";
var L_MM            = "\u043C\u043C";
var L_DD            = "\u0434\u0434";
var L_BadNumber     = "\u042D\u0442\u043E\u0442 \u043F\u0430\u0440\u0430\u043C\u0435\u0442\u0440 \u043F\u0440\u0438\u043D\u0438\u043C\u0430\u0435\u0442 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F \u0442\u0438\u043F\u0430 \"\u0427\u0438\u0441\u043B\u043E\", \u043A\u043E\u0442\u043E\u0440\u044B\u0435 \u043C\u043E\u0433\u0443\u0442 \u0441\u043E\u0434\u0435\u0440\u0436\u0430\u0442\u044C \u0442\u043E\u043B\u044C\u043A\u043E \u0437\u043D\u0430\u043A \"\u043C\u0438\u043D\u0443\u0441\", \u0446\u0438\u0444\u0440\u044B (0-9), \u0441\u0438\u043C\u0432\u043E\u043B\u044B \u0433\u0440\u0443\u043F\u043F\u0438\u0440\u043E\u0432\u043A\u0438 \u0446\u0438\u0444\u0440 \u0438 \u0434\u0435\u0441\u044F\u0442\u0438\u0447\u043D\u044B\u0439 \u0437\u043D\u0430\u043A. \u0418\u0441\u043F\u0440\u0430\u0432\u044C\u0442\u0435 \u0432\u0432\u0435\u0434\u0435\u043D\u043D\u043E\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435 \u043F\u0430\u0440\u0430\u043C\u0435\u0442\u0440\u0430.";
var L_BadCurrency   = "\u042D\u0442\u043E\u0442 \u043F\u0430\u0440\u0430\u043C\u0435\u0442\u0440 \u043F\u0440\u0438\u043D\u0438\u043C\u0430\u0435\u0442 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F \u0442\u0438\u043F\u0430 \"\u0412\u0430\u043B\u044E\u0442\u0430\", \u043A\u043E\u0442\u043E\u0440\u044B\u0435 \u043C\u043E\u0433\u0443\u0442 \u0441\u043E\u0434\u0435\u0440\u0436\u0430\u0442\u044C \u0442\u043E\u043B\u044C\u043A\u043E \u0437\u043D\u0430\u043A \"\u043C\u0438\u043D\u0443\u0441\", \u0446\u0438\u0444\u0440\u044B (0-9), \u0441\u0438\u043C\u0432\u043E\u043B\u044B \u0433\u0440\u0443\u043F\u043F\u0438\u0440\u043E\u0432\u043A\u0438 \u0446\u0438\u0444\u0440 \u0438 \u0434\u0435\u0441\u044F\u0442\u0438\u0447\u043D\u044B\u0439 \u0437\u043D\u0430\u043A. \u0418\u0441\u043F\u0440\u0430\u0432\u044C\u0442\u0435 \u0432\u0432\u0435\u0434\u0435\u043D\u043D\u043E\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435 \u043F\u0430\u0440\u0430\u043C\u0435\u0442\u0440\u0430.";
var L_BadDate       = "\u042D\u0442\u043E\u0442 \u043F\u0430\u0440\u0430\u043C\u0435\u0442\u0440 \u043F\u0440\u0438\u043D\u0438\u043C\u0430\u0435\u0442 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F \u0442\u0438\u043F\u0430 \"\u0414\u0430\u0442\u0430\" \u0432 \u0444\u043E\u0440\u043C\u0430\u0442\u0435 \"%1\", \u0433\u0434\u0435 \"\u0433\u0433\u0433\u0433\" \u2013 \u0433\u043E\u0434, \u0437\u0430\u043F\u0438\u0441\u0430\u043D\u043D\u044B\u0439 4 \u0446\u0438\u0444\u0440\u0430\u043C\u0438, \"\u043C\u043C\" \u2013 \u043C\u0435\u0441\u044F\u0446 (\u043D\u0430\u043F\u0440\u0438\u043C\u0435\u0440, \u044F\u043D\u0432\u0430\u0440\u044C = 1), \u0430 \"\u0434\u0434\" \u2013 \u043D\u043E\u043C\u0435\u0440 \u0434\u043D\u044F \u0432 \u0434\u0430\u043D\u043D\u043E\u043C \u043C\u0435\u0441\u044F\u0446\u0435.";
var L_BadDateTime   = "\u042D\u0442\u043E\u0442 \u043F\u0430\u0440\u0430\u043C\u0435\u0442\u0440 \u043F\u0440\u0438\u043D\u0438\u043C\u0430\u0435\u0442 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F \u0442\u0438\u043F\u0430 \"\u0414\u0430\u0442\u0430-\u0432\u0440\u0435\u043C\u044F\" \u0432 \u0444\u043E\u0440\u043C\u0430\u0442\u0435 \"%1 hh:mm:ss\". \"\u0433\u0433\u0433\u0433\" \u043E\u0431\u043E\u0437\u043D\u0430\u0447\u0430\u0435\u0442 \u0433\u043E\u0434, \u0437\u0430\u043F\u0438\u0441\u0430\u043D\u043D\u044B\u0439 4 \u0446\u0438\u0444\u0440\u0430\u043C\u0438, \"\u043C\u043C\" \u2013 \u043C\u0435\u0441\u044F\u0446 (\u043D\u0430\u043F\u0440\u0438\u043C\u0435\u0440, \u044F\u043D\u0432\u0430\u0440\u044C = 1), \"\u0434\u0434\" \u2013 \u043D\u043E\u043C\u0435\u0440 \u0434\u043D\u044F \u0432 \u0434\u0430\u043D\u043D\u043E\u043C \u043C\u0435\u0441\u044F\u0446\u0435, \"\u0447\u0447\" \u2013 \u0447\u0430\u0441\u044B \u0432 24-\u0447\u0430\u0441\u043E\u0432\u043E\u043C \u0444\u043E\u0440\u043C\u0430\u0442\u0435, \"\u043C\u043C\" \u2013 \u043C\u0438\u043D\u0443\u0442\u044B, \u0430 \"\u0441\u0441\" \u2013 \u0441\u0435\u043A\u0443\u043D\u0434\u044B.";
var L_BadTime       = "\u042D\u0442\u043E\u0442 \u043F\u0430\u0440\u0430\u043C\u0435\u0442\u0440 \u043F\u0440\u0438\u043D\u0438\u043C\u0430\u0435\u0442 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F \u0442\u0438\u043F\u0430 \"\u0412\u0440\u0435\u043C\u044F\" \u0432 \u0444\u043E\u0440\u043C\u0430\u0442\u0435 \"\u0447\u0447,\u043C\u043C,\u0441\u0441 \", \u0433\u0434\u0435 \"\u0447\u0447\" \u2013 \u0447\u0430\u0441\u044B \u0432 24-\u0447\u0430\u0441\u043E\u0432\u043E\u043C \u0444\u043E\u0440\u043C\u0430\u0442\u0435, \"\u043C\u043C\" \u2013 \u043C\u0438\u043D\u0443\u0442\u044B, \u0430 \"\u0441\u0441\" \u2013 \u0441\u0435\u043A\u0443\u043D\u0434\u044B.";
var L_NoValue       = "\u041D\u0435\u0442 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F";
var L_BadValue      = "\u0414\u043B\u044F \u0443\u0441\u0442\u0430\u043D\u043E\u0432\u043A\u0438 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F \"\u041D\u0435\u0442 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F\" \u043D\u0435\u043E\u0431\u0445\u043E\u0434\u0438\u043C\u043E \u0437\u0430\u0434\u0430\u0442\u044C \u0435\u0433\u043E \u0432 \u043F\u043E\u043B\u044F\u0445 \"\u041E\u0442\" \u0438 \"\u0414\u043E\".";
var L_BadBound      = "\u041F\u0430\u0440\u0430\u043C\u0435\u0442\u0440\u044B \"\u041D\u0435\u0442 \u043D\u0438\u0436\u043D\u0435\u0439 \u0433\u0440\u0430\u043D\u0438\u0446\u044B\" \u0438 \"\u041D\u0435\u0442 \u0432\u0435\u0440\u0445\u043D\u0435\u0439 \u0433\u0440\u0430\u043D\u0438\u0446\u044B\" \u043D\u0435 \u043C\u043E\u0433\u0443\u0442 \u0431\u044B\u0442\u044C \u0443\u0441\u0442\u0430\u043D\u043E\u0432\u043B\u0435\u043D\u044B \u043E\u0434\u043D\u043E\u0432\u0440\u0435\u043C\u0435\u043D\u043D\u043E.";
var L_NoValueAlready = "\u042D\u0442\u043E\u043C\u0443 \u043F\u0430\u0440\u0430\u043C\u0435\u0442\u0440\u0443 \u0443\u0436\u0435 \u043F\u0440\u0438\u0441\u0432\u043E\u0435\u043D\u043E \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435 \"\u041D\u0435\u0442 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F\". \u0423\u0434\u0430\u043B\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435 \"\u041D\u0435\u0442 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F\" \u043F\u0435\u0440\u0435\u0434 \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D\u0438\u0435\u043C \u0434\u0440\u0443\u0433\u0438\u0445 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0439.";
var L_RangeError    = "\u041D\u0430\u0447\u0430\u043B\u043E \u0434\u0438\u0430\u043F\u0430\u0437\u043E\u043D\u0430 \u043D\u0435 \u043C\u043E\u0436\u0435\u0442 \u0431\u044B\u0442\u044C \u0431\u043E\u043B\u044C\u0448\u0435 \u043A\u043E\u043D\u0446\u0430 \u0434\u0438\u0430\u043F\u0430\u0437\u043E\u043D\u0430.";
var L_NoDateEntered = "\u041D\u0435\u043E\u0431\u0445\u043E\u0434\u0438\u043C\u043E \u0432\u0432\u0435\u0441\u0442\u0438 \u0434\u0430\u0442\u0443.";
var L_Empty         = "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435.";

// Strings for filter dialog
var L_closeDialog="\u0417\u0430\u043A\u0440\u044B\u0442\u044C \u043E\u043A\u043D\u043E";

var L_SetFilter = "\u0423\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u044C \u0444\u0438\u043B\u044C\u0442\u0440";
var L_OK        = "\u041E\u041A";
var L_Cancel    = "\u041E\u0442\u043C\u0435\u043D\u0430";

 /* Crystal Decisions Confidential Proprietary Information */
