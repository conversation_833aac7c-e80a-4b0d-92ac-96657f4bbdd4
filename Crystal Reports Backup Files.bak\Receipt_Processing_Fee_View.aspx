﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Receipt_Processing_Fee_View.aspx.cs" Inherits="KSDCSCST_Portal.Receipt_Processing_Fee_View" %>


<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/css/datepicker.css" rel="stylesheet" type="text/css" />

    <style>
        @media print {
            html, body {
                width: 210mm;
                height: 297mm;
                margin: 0;
            }

            /* Set landscape orientation */
            @page {
                size: A4 portrait;
            }
        }
    </style>

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Processing Fee</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">Miscellaneous Fee</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-8 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">


                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="row" style="margin: 1%;">
                                                <div class="col-sm-12">
                                                    <div class="Sub_Header_Abstract">LOANEE DETAILS</div>


                                                    <table class="table_Abstract" style="width: 100%;">
                                                        <tbody>
                                                            <tr>
                                                                <td>Loanee Name</td>

                                                                <td style="">
                                                                    <label id="txtLoanee_Name"></label>
                                                                </td>



                                                                <td>Address</td>

                                                                <td colspan="3">
                                                                    <label id="txtAddress"></label>
                                                                </td>



                                                            </tr>
                                                            <tr>
                                                                <td>Old Agr.No</td>

                                                                <td>
                                                                    <label id="txtOld_Agr_No"></label>
                                                                </td>
                                                                <td>Scheme Name</td>

                                                                <td>
                                                                    <label id="txtScheme_Name"></label>
                                                                </td>

                                                                <td>Contact Number</td>

                                                                <td>
                                                                    <label id="txtContact_Number"></label>
                                                                </td>



                                                            </tr>


                                                        </tbody>
                                                    </table>


                                                </div>
                                            </div>

                                        </div>
                                    </div>



                                    <div style="display: block; padding-top: 20px; border-top: 1px solid #cfcdcd; margin-top: 15px; padding-top: 15px;">

                                        <div style="float: left;">
                                            <div style="float: left;">
                                                <label for="txtDate_Of_Transaction" class="col-sm-12 col-form-label">Date Of Transaction</label>
                                            </div>
                                            <div style="float: left;">
                                                <div id="datepicker" class="input-group date" data-date-format="dd-mm-yyyy">
                                                    <input readonly class="dateandtime form-control" id="txtTransaction_Date" type="text" name="machin-b" value="">
                                                    <span class="input-group-addon"><i class="glyphicon glyphicon-calendar"></i></span>
                                                </div>
                                            </div>

                                        </div>

                                        <div style="float: right;">
                                            <div style="float: left;">
                                                <label for="dropMode_Payment" class="col-sm-12 col-form-label">Mode of Payment</label>
                                            </div>
                                            <div style="float: left;">
                                                <select id="dropMode_Payment" class="form-control">
                                                    <option value="Cash" selected="selected">Cash</option>
                                                    <option value="Bank">Bank</option>

                                                </select>
                                            </div>

                                        </div>



                                    </div>

                                    <div style="float: left; width: 100%; display: block; border-top: 1px solid #cfcdcd; margin-top: 15px; padding-top: 15px;">
                                        <div class="form-group row">
                                            <label for="inputName2" class="col-sm-3 col-form-label">Processing Fee</label>
                                            <div class="col-sm-6">
                                                <input type="number" disabled="disabled" max="12" class="form-control" id="txtProcessing_Fee" placeholder=" ">
                                                <span style="color: red; font-size: 12px; font-style: italic;">Included 18% GST Amount </span>

                                            </div>


                                            <div class="col-sm-3">
                                                <span id="SPAN_ProcessingFee" style="color: red; font-weight: bold;"></span>
                                                <a onclick="Save_Processing_Fee();" style="padding: 2px; padding-left: 5px; padding-right: 5px; float: right;" id="SaveProcessingFee" class="btn btn-success">Submit</a>
                                            </div>
                                        </div>
                                        <br />
                                        <div class="form-group row">
                                            <label for="inputName2" class="col-sm-3 col-form-label">Legal Scrutiny Fee</label>
                                            <div class="col-sm-6">
                                                <input type="number" max="12" class="form-control" id="txtLegal_Scrutiny_Fee" placeholder=" ">
                                                <span style="color: red; font-size: 12px; font-style: italic;">Included 18% GST Amount </span>
                                            </div>
                                            <div class="col-sm-3">
                                                <span id="SPAN_LegalFee" style="color: red; font-weight: bold;"></span>
                                                <a onclick="Save_Legal_Fee();" style="padding: 2px; padding-left: 5px; padding-right: 5px; float: right;" id="SaveLegalFee" class="btn btn-success">Preview</a>
                                            </div>
                                        </div>
                                        <br />
                                        <div class="form-group row">
                                            <label for="inputName2" class="col-sm-3 col-form-label">Land Verification Fee</label>
                                            <div class="col-sm-6">
                                                <input type="number" max="12" class="form-control" id="txtLand_Verification_Fee" placeholder=" ">
                                                <span style="color: red; font-size: 12px; font-style: italic;">Included 18% GST Amount </span>
                                            </div>
                                            <div class="col-sm-3">
                                                <span id="SPAN_LandFee" style="color: red; font-weight: bold;"></span>
                                                <a onclick="Save_Land_Fee();" style="padding: 2px; padding-left: 5px; padding-right: 5px; float: right;" id="SaveLandFee" class="btn btn-success">Preview</a>
                                            </div>
                                        </div>
                                        <br />
                                        <div class="Sub_Header" style="text-align: left;">Beneficiary Contribution</div>
                                        <br />
                                        <div class="form-group row">
                                            <label for="inputName2" class="col-sm-3 col-form-label">Amount</label>
                                            <div class="col-sm-9">
                                                <input type="number" max="12" class="form-control" id="txt_BC_Amount" disabled="disabled" placeholder=" ">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="inputName2" class="col-sm-3 col-form-label">Remarks</label>
                                            <div class="col-sm-9">
                                                <textarea class="form-control" maxlength="200" id="txt_BC_Remarks" placeholder="Remarks"></textarea>
                                                <span class="validation_message">Maximum text length is 200</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <div class="col-sm-12 text-right">

                                                <span id="SPAN_BC" style="color: red; font-weight: bold;"></span>
                                                <a onclick="Save_BC();" style="padding: 2px; padding-left: 5px; padding-right: 5px; float: right;" class="btn btn-success" id="Save_BC">Submit</a>
                                            </div>
                                        </div>
                                        <br />
                                        <div style="display: none;">
                                            <div class="Sub_Header" style="text-align: left;">Postage</div>

                                            <div class="form-group row">
                                                <label for="inputName2" class="col-sm-3 col-form-label">Amount</label>
                                                <div class="col-sm-9">
                                                    <input type="number" max="12" class="form-control" id="txt_Postage_Amount" placeholder=" ">
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label for="inputName2" class="col-sm-3 col-form-label">Remarks</label>
                                                <div class="col-sm-9">
                                                    <textarea class="form-control" maxlength="200" id="txt_Postage_Remark" placeholder="Remarks"></textarea>
                                                    <span class="validation_message">Maximum text length is 200</span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-12 text-right">


                                                    <a onclick="Save_Postage();" style="padding: 2px; padding-left: 5px; padding-right: 5px; float: right;" class="btn btn-success" class="btn btn-success">Submit</a>
                                                </div>
                                            </div>
                                            <br />
                                            <div class="Sub_Header" style="text-align: left;">others</div>


                                            <div class="form-group row">
                                                <label for="inputName2" class="col-sm-3 col-form-label">Amount</label>
                                                <div class="col-sm-9">
                                                    <input type="number" max="12" class="form-control" id="txt_Other_Amount" placeholder=" ">
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label for="inputName2" class="col-sm-3 col-form-label">Remarks</label>
                                                <div class="col-sm-9">
                                                    <textarea class="form-control" maxlength="200" id="txt_Other_Remark" placeholder="Remarks"></textarea>
                                                    <span class="validation_message">Maximum text length is 200</span>
                                                </div>
                                            </div>


                                            <div class="form-group row">
                                                <div class="col-sm-12 text-right">

                                                    <a href="ApplicationIssue_List.aspx" style="padding: 2px; padding-left: 5px; padding-right: 5px;" class="btn btn-dark">Back</a>
                                                    <%--   <a onclick="Print();" class="btn btn-info">Print</a>--%>
                                                    <a onclick="Save_Other();" style="padding: 2px; padding-left: 5px; padding-right: 5px;" class="btn btn-success" class="btn btn-success">Submit</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>


    <!-- /.content -->

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <script src="assets/plugins/toastr/toastr.min.js"></script>



    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/inputmask/5.0.4/jquery.inputmask.min.js"></script>

    
    <script src="assets/js/jquery.dateandtime.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.2.0/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/js/bootstrap-datepicker.js"></script>
    <script>

        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));

        }
        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;
        $(document).ready(function () {

            Load_Processing_Fee();
            Select_Lonees_By_Reg_No();
            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            var startDate = new Date();
            startDate.setDate(startDate.getDate());

            var endDate = new Date();
            endDate.setDate(endDate.getDate());

            var today = new Date();

            // Format today's date as 'dd/mm/yyyy'
            var formattedDate = (today.getDate() < 10 ? '0' : '') + today.getDate() + '/' +
                ((today.getMonth() + 1) < 10 ? '0' : '') + (today.getMonth() + 1) + '/' +
                today.getFullYear();

            // Initialize the datepicker with start and end dates
            $("#datepicker").datepicker({
              
                autoclose: true,
                todayHighlight: true,
                format: 'dd-mm-yyyy' // Set the date format
            });

            $("#datepicker").datepicker("setDate", formattedDate);

            // Get the current date
            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtApplication_Date').val(formattedDate);

            $("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
            $("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");
            Load_All_Schemes();
            //   Load_All_Districts();

            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }


            $(".form-control").on("focusout", function () {
                $(".form-control").removeAttr("style");

                var This_Id = $(this).attr("id");
                var This_Control = $(this);
                if (This_Id == "txtApplicant_Name") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Name of Applicant is required !'
                        })
                    }
                    else if (!Is_Valid_Text(This_Control.val().trim())) {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Special characters or Numbers not allowed in Name of Applicant !'
                        })
                    }
                }
                else if (This_Id == "dropScheme") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Loan Scheme is required !'
                        })
                    }
                }
                else if (This_Id == "dropCast") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Caste is required !'
                        })
                    }
                }
                else if (This_Id == "dropSubCast") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Sub Caste is required !'
                        })
                    }
                }
                else if (This_Id == "txtPhoneNo") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Phone No is required !'
                        })
                    }
                }
                //else if (This_Id == "txtAadharNumber") {
                //    if (This_Control.val().trim() == "") {
                //        Focus_Error(This_Control);
                //        Toast.fire({
                //            icon: 'error',
                //            title: 'Aadhar No is required !'
                //        })
                //    }
                //}



                // Your common focusout function code goes here
                //   alert("Input value changed: " + value);
            });



            //$(document).on("input", ".form-control", function () {
            //    // Get the current value of the input
            //    var currentValue = $(this).val();

            //    // Convert the value to propcase
            //    var propcaseValue = currentValue.replace(/\w\S*/g, function (txt) {
            //        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
            //    });

            //    // Update the input with the propcase value
            //    $(this).val(propcaseValue);
            //});



        });

        function Select_Lonees_By_Reg_No() {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ reg_No: getParameterByName("Reg_No") }), // If you have parameters
                url: "WebService.asmx/Select_Lonees_By_Reg_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $.each(data.d, function (key, value) {


                        $("#txtAddress").text(value.vchr_hsename);
                        $("#txtLoanee_Name").text(value.vchr_applname);
                        $("#txtOld_Agr_No").text(value.vchr_remark);
                        $("#txtContact_Number").text(value.vchr_phno + value.vchr_phno2);
                        $("#txtScheme_Name").text(value.Remarks_Manager);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });
        }

        function Load_All_Remittance() {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ reg_No: getParameterByName("Reg_No") }), // If you have parameters
                url: "WebService.asmx/Get_All_Remittance_By_Reg_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $.each(data.d, function (key, value) {

                        if (parseInt($("#txtProcessing_Fee").val()) == parseInt(value.ProcessingFee)) {
                            $("#SaveProcessingFee").hide();
                            $("#SPAN_ProcessingFee").text('PAID');
                        }

                        if (parseInt(value.LegalFee) > 0) {

                            $("#txtLegal_Scrutiny_Fee").val(value.LegalFee);
                            $("#txtLegal_Scrutiny_Fee").attr("disabled", "disabled")
                            $("#SaveLegalFee").hide();
                            $("#SPAN_LegalFee").text('PAID');

                        }

                        if (parseInt(value.LandFee) > 0) {

                            $("#txtLand_Verification_Fee").val(value.LandFee);
                            $("#txtLand_Verification_Fee").attr("disabled", "disabled")
                            $("#SaveLandFee").hide();
                            $("#SPAN_LandFee").text('PAID');
                            $("#SPAN_LandFeeGST").text('Included 18% GST Amount ');

                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });
        }


        function Load_Processing_Fee() {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ reg_No: getParameterByName("Reg_No") }), // If you have parameters
                url: "WebService.asmx/Get_Processing_Fee_By_Reg_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $.each(data.d, function (key, value) {

                        $("#txtProcessing_Fee").val(Math.round(value.Total_Processing_Fee));




                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_BC_Fee();

            });
        }

        function Load_BC_Fee() {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ reg_No: getParameterByName("Reg_No") }), // If you have parameters
                url: "WebService.asmx/Receipt_BC_Amount_By_Reg_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    if (data.d.length > 0) {
                        $.each(data.d, function (key, value) {

                            $("#txt_BC_Amount").val(value.BC_Amount);




                        });
                    }
                    else {
                        $("#txt_BC_Amount").val('0');
                        $("#Save_BC").hide();
                        $("#SPAN_BC").text('PAID / No BC');

                    }


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_All_Remittance();

            });
        }

        function Save_Processing_Fee() {
            var ProcessingFee = $('#txtProcessing_Fee').val();

            if ($('#txtProcessing_Fee').val() != "0") {
                if (confirm('Are you sure you want to perform this action with the amount: ' + ProcessingFee))
                    $.ajax({
                        type: "POST",
                        dataType: "json",
                        data: JSON.stringify({ regNo: getParameterByName("Reg_No"), amount: $('#txtProcessing_Fee').val(), Mode: $('#dropMode_Payment').val(), Transaction_Date: $('#txtTransaction_Date').val() }), // If you have parameters
                        url: "WebService.asmx/Update_Processing_Fee",
                        contentType: "application/json; charset=utf-8",
                        success: function (data) {




                        },
                        error: function (error) {


                            // alert("error" + error.responseText);
                            Swal.fire({
                                icon: 'error',
                                title: 'Oops...',
                                text: 'Something went wrong!',

                            })
                        }
                    }).done(function () {

                        window.location.href = "Print_Processing_Fee.aspx?Reg_No=" + getParameterByName("Reg_No") + "&Amount=" + $("#txtProcessing_Fee").val() + "&Name=" + getParameterByName("Name");


                    });
            }
            else {
                Swal.fire({
                    icon: 'warning',
                    title: 'Message',
                    text: 'Processing Fee Amount is Zero !',

                }).then((result) => {
                    if (result.isConfirmed) {
                        // OK button clicked

                        //  location.href = 'Agency_List.aspx';
                        // Your code here
                    }
                });
            }
        }
        

        function Save_Legal_Fee() {
            var LegalFee = $('#txtLegal_Scrutiny_Fee').val();

            if ($('#txtLegal_Scrutiny_Fee').val().trim() != "0" && $('#txtLegal_Scrutiny_Fee').val().trim() != "") {



                window.location.href = "Print_Legal_Fee.aspx?Reg_No=" + getParameterByName("Reg_No") + "&Amount=" + $("#txtLegal_Scrutiny_Fee").val() + "&Name=" + getParameterByName("Name") + "&Mode=" + $("#dropMode_Payment").val() + "&Transaction_Date=" + $("#txtTransaction_Date").val() ;
            }
        }








        function Save_Land_Fee() {

            var LandFee = $('#txtLand_Verification_Fee').val();

            if ($('#txtLand_Verification_Fee').val().trim() != "0" && $('#txtLand_Verification_Fee').val().trim() != "") {


                window.location.href = "Print_Land_Fee.aspx?Reg_No=" + getParameterByName("Reg_No") + "&Amount=" + $("#txtLand_Verification_Fee").val() + "&Name=" + getParameterByName("Name") + "&Mode=" + $("#dropMode_Payment").val() + "&Transaction_Date=" + $("#txtTransaction_Date").val();



            }
        }

        function Save_BC() {

            if ($('#txt_BC_Amount').val().trim() != "") {
                $.ajax({
                    type: "POST",
                    dataType: "json",
                    data: JSON.stringify({ regNo: getParameterByName("Reg_No"), amount: $('#txt_BC_Amount').val(), Mode: $('#dropMode_Payment').val(), Transaction_Date: $('#txtTransaction_Date').val() }), // If you have parameters
                    url: "WebService.asmx/Update_BC_Fee",
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        alert(data.d.length);
                        //if (parseInt(value.LegalFee) > 0) {

                        //    $("#txtLegal_Scrutiny_Fee").val(value.LegalFee);
                        //    $("#txtLegal_Scrutiny_Fee").attr("disabled", "disabled")
                        //    $("#SaveLegalFee").hide();
                        //    $("#SPAN_LegalFee").text('PAID');

                        //}


                    },
                    error: function (error) {


                        // alert("error" + error.responseText);
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Something went wrong!',

                        })
                    }
                }).done(function () {

                    window.location.href = "Print_BC.aspx?Reg_No=" + getParameterByName("Reg_No") + "&Amount=" + $("#txt_BC_Amount").val() + "&Name=" + getParameterByName("Name");


                });
            }
            else {
                Toast.fire({
                    icon: 'error',
                    title: 'Beneficiary Contribution Amount is not Valid!'
                })
            }
        }

        function Save_Postage() {

        }

        function Save_Other() {

        }








        function Print() {
            var printWindow = window.open('', '_blank');
            printWindow.document.open();
            printWindow.document.write('<html><head><title>Print</title></head><body style="width: 210mm;height: 297mm;margin: 0;  ">');
            printWindow.document.write($("#Print_Section").html());
            printWindow.document.write('</body></html>');
            printWindow.document.close();
            printWindow.print();
            if (confirm('Print dialog initiated. Please close the window after printing.')) {
                printWindow.close();
            }
        }

        function Load_All_Districts() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropDistrict').empty();
                    $('#dropDistrict').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var District_Name = value.District_Name;
                        var html = '<option value="' + Id + '">' + District_Name + '</option>';
                        if (Id != 15) {
                            $('#dropDistrict').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $('#dropDistrict').change(function () {
                    Load_All_Sub_Districts($(this).val());
                });

            });

        }
        function Load_All_Sub_Districts(District_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Districts",
                data: JSON.stringify({ District_Id: District_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubDistrict').empty();
                    $('#dropSubDistrict').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Sub_District_Name = value.Sub_District_Name;
                        var html = '<option value="' + Id + '">' + Sub_District_Name + '</option>';

                        $('#dropSubDistrict').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });

        }

        function Load_All_Schemes() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Schemes",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropScheme').empty();
                    $('#dropScheme').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var IsActive = value.IsActive;
                        var Scheme = value.Scheme;
                        var html = '<option value="' + Id + '">' + Scheme + '</option>';
                        if (IsActive == 1) {
                            $('#dropScheme').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_All_Cast();

            });

        }

        function Load_All_Cast() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Cast",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropCast').empty();
                    $('#dropCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var Cast = value.Cast;
                        var html = '<option value="' + Id + '">' + Cast + '</option>';

                        $('#dropCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $('#dropCast').change(function () {
                    Load_All_SubCast($(this).val());
                });

            });

        }


        function Load_All_SubCast(Cast_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Cast",
                data: JSON.stringify({ Cast_Id: Cast_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubCast').empty();
                    $('#dropSubCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var SubCast = value.SubCast;
                        var html = '<option value="' + Id + '">' + SubCast + '</option>';

                        $('#dropSubCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {


            });

        }
        function Save() {
            //alert(Counter);



            var district_Id = $("#dropDistrict");

            var subDistrict_id = $("#dropSubDistrict");
            var name_Applicant = $("#txtApplicant_Name");
            var scheme_Id = $("#dropScheme");

            var cast_Id = $("#dropCast");
            var sub_Cast_Id = $("#dropSubCast");

            var remarks = $("#txtRemarks");
            var PhoneNo = $("#txtPhoneNo");
            var AadharNumber = $("#txtAadharNumber");





            $(".form-control").removeAttr("style");



            if (name_Applicant.val().trim() == "") {
                Focus_Error(name_Applicant);
                Toast.fire({
                    icon: 'error',
                    title: 'Name of Applicant is required !'
                })
            }
            else if (!Is_Valid_Text(name_Applicant.val().trim())) {
                Focus_Error(name_Applicant);
                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed in Name of Applicant !'
                })
            }
            else if (scheme_Id.val() == "0") {
                Focus_Error(scheme_Id);
                Toast.fire({
                    icon: 'error',
                    title: 'Loan Scheme is required !'
                })
            }
            else if (cast_Id.val() == "0") {
                Focus_Error(cast_Id);
                Toast.fire({
                    icon: 'error',
                    title: 'Caste is required !'
                })
            }
            else if (sub_Cast_Id.val() == "0") {
                Focus_Error(sub_Cast_Id);
                Toast.fire({
                    icon: 'error',
                    title: 'Sub Caste is required !'
                })
            }
            else if (PhoneNo.val().trim() == "") {
                Focus_Error(PhoneNo);
                Toast.fire({
                    icon: 'error',
                    title: 'Phone No is required !'
                })
            }
            //else if (AadharNumber.val().trim() == "") {
            //    Focus_Error(PhoneNo);
            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Aadhar No is required !'
            //    })
            //}


            else {
                if (PhoneNo.val().trim() != "") {

                    var values = PhoneNo.val().trim().split(",");
                    for (var i = 0; i < values.length; i++) {
                        if (values[values.length - 1] != "") {
                            if (!Is_Valid_Mobile_Number(values[i])) {
                                Focus_Error(PhoneNo);
                                Toast.fire({
                                    icon: 'error',
                                    title: 'Enter valid phone number !'
                                })
                                return;
                            }
                        }
                    }
                }

                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/Insert_To_tbl_Loan_App_Issue",
                    data: JSON.stringify({ district_Id: district_Id.val(), subDistrict_id: subDistrict_id.val(), name_Applicant: name_Applicant.val(), scheme_Id: scheme_Id.val(), cast_Id: cast_Id.val(), sub_Cast_Id: sub_Cast_Id.val(), remarks: remarks.val(), PhoneNo: PhoneNo.val(), AadharNumber: AadharNumber.val() }), // If you have parameters
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {

                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        // Handle AJAX error
                        //   alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);

                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                        })

                    }

                }).done(function () {
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Application Issue Successfully Saved !',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked

                            location.href = 'ApplicationIssue_List.aspx';
                            // Your code here
                        }
                    });

                });


            }




        }

        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }

        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }


        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }



    </script>
</asp:Content>




