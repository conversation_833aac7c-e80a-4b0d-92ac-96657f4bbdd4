﻿<%@ Page Title="Surety Confirmation" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Surety_Confirmation.aspx.cs" Inherits="KSDCSCST_Portal.Surety_Confirmation" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    
  <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- daterange picker -->
    <link rel="stylesheet" href="assets/plugins/daterangepicker/daterangepicker.css">
    <!-- iCheck for checkboxes and radio inputs -->
    <link rel="stylesheet" href="assets/plugins/icheck-bootstrap/icheck-bootstrap.min.css">
    <!-- Bootstrap Color Picker -->
    <link rel="stylesheet" href="assets/plugins/bootstrap-colorpicker/css/bootstrap-colorpicker.min.css">
    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet" href="assets/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css">
    <!-- Select2 -->
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">
    <!-- Bootstrap4 Duallistbox -->
    <link rel="stylesheet" href="assets/plugins/bootstrap4-duallistbox/bootstrap-duallistbox.min.css">
    <!-- BS Stepper -->
    <link rel="stylesheet" href="assets/plugins/bs-stepper/css/bs-stepper.min.css">
    <!-- dropzonejs -->
    <link rel="stylesheet" href="assets/plugins/dropzone/min/dropzone.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">
    <style>
        input:disabled {
            border: 0;
        }
    </style>
    <style>
        /* Styles for the search input */
        txtPostOffices {
            width: 300px;
            padding: 10px;
            font-size: 16px;
        }

        /* Styles for the search results container */
        #search-results {
            list-style: none;
            padding: 0;
            margin-top: 5px;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
        }

            /* Styles for individual search result items */
            #search-results li {
                padding: 5px;
                cursor: pointer;
            }

                /* Highlight the selected result */
                #search-results li:hover {
                    background-color: #f2f2f2;
                }
    </style>

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">





                <div class="col-sm-6">
                    <h1>Guarantor Confirmation</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">Guarantor Confirmation</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-9 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">
                                <div class="active tab-pane" id="settings">
                                    <div>
                                        <div id="Header_Employee" class="Sub_Header" style="display: none;">Employee / Personal Guarantor Confirmation dates</div>
                                        <div id="Main_Div_Append_Employee">
                                        </div>
                                        <div id="Header_Land" class="Sub_Header" style="display: none; margin-top: 20px;">Land Guarantor Confirmation dates</div>
                                        <div id="Main_Div_Append_Land">
                                        </div>


                                        <div id="Header_FD_LIC" class="Sub_Header" style="display: none; margin-top: 20px;">FD / LIC Guarantor Confirmation dates</div>
                                        <div id="Main_Div_Append_FD_LIC">
                                        </div>
                                        <div id="Header_Personal" class="Sub_Header" style="display: none; margin-top: 20px;">Personal Guarantor Confirmation dates</div>
                                        <div id="Main_Div_Append_Personal">
                                        </div>




                                    </div>



                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">

                                            <a onclick="Confirm();" class="btn btn-success">Confirm</a>


                                        </div>
                                    </div> 
                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 -->
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <!-- Bootstrap4 Duallistbox -->
    <script src="assets/plugins/bootstrap4-duallistbox/jquery.bootstrap-duallistbox.min.js"></script>
    <!-- InputMask -->
    <script src="assets/plugins/moment/moment.min.js"></script>
    <script src="assets/plugins/inputmask/jquery.inputmask.min.js"></script>
    <!-- date-range-picker -->
    <script src="assets/plugins/daterangepicker/daterangepicker.js"></script>
    <!-- bootstrap color picker -->
    <script src="assets/plugins/bootstrap-colorpicker/js/bootstrap-colorpicker.min.js"></script>
    <!-- Tempusdominus Bootstrap 4 -->
    <script src="assets/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js"></script>
    <!-- BS-Stepper -->
    <script src="assets/plugins/bs-stepper/js/bs-stepper.min.js"></script>
    <!-- dropzonejs -->
    <script src="assets/plugins/dropzone/min/dropzone.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script src="assets/js/jquery.dateandtime.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>


    <script>
        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;
        const $searchInput = $('#txtAddress_Pincode');
        const $searchResults = $('#search-results');
        const $searchInputPrePin = $('#txtPresent_Pincode');
        const $searchResultsPrePin = $('#search-results-pre-pin');
        const $searchInputPerPin = $('#txtPermanent_Pincode');
        const $searchResultsPerPin = $('#search-results-per-pin');
        const $searchInputDOfficerPin = $('#txtDrawing_Or_Higher_Office_Pincode');
        const $searchResultsDOfficerPin = $('#search-results-dofficer-pin');

        $('#search-results').hide();
        $('#search-results-pre-pin').hide();
        $('#search-results-per-pin').hide();
        $('#search-results-dofficer-pin').hide();


        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }

        $(function () {

            $searchInput.on('input', function () {
                const query = $searchInput.val();
                fetchAutocompleteResults(query);
            });

            // Event handler for selecting a result
            $searchResults.on('click', 'li', function () {
                $("#txtAddress_Pincode").val($(this).attr("data-pincode"));
                $("#txtAddress_Post_Office").val($(this).html());
                //  const selectedResult = $(this).text();
                //  $searchInput.val(selectedResult);
                $searchResults.hide();
            });

            $searchInputPrePin.on('input', function () {
                const query = $searchInputPrePin.val();
                fetchAutocompleteResultsPrePin(query);
            });

            // Event handler for selecting a result
            $searchResultsPrePin.on('click', 'li', function () {
                $("#txtPresent_Pincode").val($(this).attr("data-pincode"));
                $("#txtPresent_Post_Office").val($(this).html());
                //  const selectedResult = $(this).text();
                //  $searchInput.val(selectedResult);
                $searchResultsPrePin.hide();
            });

            $searchInputPerPin.on('input', function () {
                const query = $searchInputPerPin.val();
                fetchAutocompleteResultsPerPin(query);
            });

            // Event handler for selecting a result
            $searchResultsPerPin.on('click', 'li', function () {
                $("#txtPermanent_Pincode").val($(this).attr("data-pincode"));
                $("#txtPermanent_Post_Office").val($(this).html());
                //  const selectedResult = $(this).text();
                //  $searchInput.val(selectedResult);
                $searchResultsPerPin.hide();
            });
            $searchInputDOfficerPin.on('input', function () {
                const query = $searchInputDOfficerPin.val();
                fetchAutocompleteResultsDOfficerPin(query);
            });

            // Event handler for selecting a result
            $searchResultsDOfficerPin.on('click', 'li', function () {
                $("#txtDrawing_Or_Higher_Office_Pincode").val($(this).attr("data-pincode"));
                $("#txtDrawing_Or_Higher_Office_Post_Office").val($(this).html());
                //  const selectedResult = $(this).text();
                //  $searchInput.val(selectedResult);
                $searchResultsDOfficerPin.hide();
            });
        })

        $(document).ready(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            // Get the current date
            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtApplication_Date').val(formattedDate);

            $("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
            $("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");

            //   Load_All_Districts();

            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            function getParameterByName(name, url = window.location.href) {
                name = name.replace(/[\[\]]/g, '\\$&');
                var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                    results = regex.exec(url);
                if (!results) return null;
                if (!results[2]) return '';
                return decodeURIComponent(results[2].replace(/\+/g, ' '));
            }

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }

            $('#GazettedOfficer').hide();
            $('#NPS_PRAN').hide();

            //   Load_All_Application_Issue_By_Id(getParameterByName("Id"));

            Load_Surety_Details_By_LoaanappId();

        });

        function fetchAutocompleteResults(query) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ pincode: query }), // If you have parameters
                url: "WebService.asmx/Select_All_PostOffices",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $searchResults.empty();
                    if (data.d.length > 0) {

                        $searchResults.show();
                    } else {
                        $searchResults.hide();
                        $("#txtAddress_Post_Office").val('');
                    }
                    $.each(data.d, function (key, value) {
                        $searchResults.append('<li data-pincode="' + value.Pincode + '">' + value.Post_office + '</li>');
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });


        }

        function fetchAutocompleteResultsPrePin(query) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ pincode: query }), // If you have parameters
                url: "WebService.asmx/Select_All_PostOffices",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $searchResultsPrePin.empty();
                    if (data.d.length > 0) {

                        $searchResultsPrePin.show();
                    } else {
                        $searchResultsPrePin.hide();
                        $("#txtPresent_Post_Office").val('');
                    }
                    $.each(data.d, function (key, value) {
                        $searchResultsPrePin.append('<li data-pincode="' + value.Pincode + '">' + value.Post_office + '</li>');
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });


        }
        function fetchAutocompleteResultsPerPin(query) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ pincode: query }), // If you have parameters
                url: "WebService.asmx/Select_All_PostOffices",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $searchResultsPerPin.empty();
                    if (data.d.length > 0) {

                        $searchResultsPerPin.show();
                    } else {
                        $searchResultsPerPin.hide();
                        $("#txtPermanent_Post_Office").val('');
                    }
                    $.each(data.d, function (key, value) {
                        $searchResultsPerPin.append('<li data-pincode="' + value.Pincode + '">' + value.Post_office + '</li>');
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });


        }
        function fetchAutocompleteResultsDOfficerPin(query) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ pincode: query }), // If you have parameters
                url: "WebService.asmx/Select_All_PostOffices",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $searchResultsDOfficerPin.empty();
                    if (data.d.length > 0) {

                        $searchResultsDOfficerPin.show();
                    } else {
                        $searchResultsDOfficerPin.hide();
                        $("#txtDrawing_Or_Higher_Office_Post_Office").val('');
                    }
                    $.each(data.d, function (key, value) {
                        $searchResultsDOfficerPin.append('<li data-pincode="' + value.Pincode + '">' + value.Post_office + '</li>');
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });


        }


        function Load_Surety_Details_By_LoaanappId() {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Surety_Confirmation_By_int_loanappid",
                data: JSON.stringify({ int_loanappid: getParameterByName("Id") }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $("#Header_Employee").css("display", "none");
                    $("#Header_Land").css("display", "none");
                    $("#Header_FD_LIC").css("display", "none");
                    $("#Header_Personal").css("display", "none");


                    $("#Main_Div_Append_Employee").empty();
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var int_loanappid = value.int_loanappid;
                        var Guarantor_Name = value.Guarantor_Name;
                        var Send_Date = value.Send_Date;
                        Send_Date = Send_Date.split('/')[2] + "-" + Send_Date.split('/')[1] + "-" + Send_Date.split('/')[0];
                        var Received_Date = value.Received_Date;
                        Received_Date = Received_Date.split('/')[2] + "-" + Received_Date.split('/')[1] + "-" + Received_Date.split('/')[0];
                        var Type = value.Type;

                        if (Type == "Employee") {
                            $("#Header_Employee").css("display", "block");

                            $("#Main_Div_Append_Employee").append('<div class="row">'
                                + '    <div class="col-sm-4">'
                                + '        <div class="form-group row">'
                                + '            <label class="col-sm-6 col-form-label">Guarantor Name*</label>'
                                + '            <div class="col-sm-6">'
                                + '                <input type="text" value="' + Guarantor_Name + '" class="form-control"  disabled="disabled" placeholder="Guarantor Name">'
                                + '            </div>'
                                + '        </div>'
                                + '    </div>'
                                + '    <div class="col-sm-3">'
                                + '        <div class="form-group row">'
                                + '            <label class="col-sm-5 col-form-label">Sending Date*</label>'
                                + '            <div class="col-sm-7">'
                                + '                <input type="date"  class="form-control" id="txtSending_Date_' + Type + "_" +  Id + '" placeholder="dd/mm/yyyy">'
                                + '            </div>'
                                + '        </div>'
                                + '    </div>'
                                + '    <div class="col-sm-3">'
                                + '        <div class="form-group row">'
                                + '            <label class="col-sm-5 col-form-label">Receiving Date*</label>'
                                + '            <div class="col-sm-7">'
                                + '                <input type="date"  class="form-control"  id="txtReceiving_Date_' + Type + "_" + Id + '" placeholder="dd/mm/yyyy">'
                                + '            </div>'
                                + '        </div>'
                                + '    </div>'
                                + '    <div class="col-sm-2" >'
                                + '        <div class="form-group row">'
                                + '            <div class="col-sm-12 text-right">'
                                + '                <a data-Id="' + Id + '" data-Type="' + Type + '" data-Send_Date="' + Send_Date + '" data-Received_Date="' + Received_Date + '"onclick="Save(this);" class="btn btn-info">Submit</a>'
                                + '            </div>'
                                + '        </div>'
                                + '    </div>'
                                + '</div>'
                                + '<hr />');

                            if (Send_Date != "0001-01-01") {
                                $("#txtSending_Date_" + Type + "_" + Id).val(Send_Date);
                            }

                            if (Received_Date != "0001-01-01") {
                                $("#txtReceiving_Date_" + Type + "_" + Id).val(Received_Date);
                            }




                        }
                        if (Type == "FD_LIC") {

                            $("#Header_FD_LIC").css("display", "block");


                            $("#Main_Div_Append_FD_LIC").append('<div class="row">'
                                + '    <div class="col-sm-4">'
                                + '        <div class="form-group row">'
                                + '            <label class="col-sm-6 col-form-label">Guarantor Name*</label>'
                                + '            <div class="col-sm-6">'
                                + '                <input type="text" value="' + Guarantor_Name + '" class="form-control"  disabled="disabled" placeholder="Guarantor Name">'
                                + '            </div>'
                                + '        </div>'
                                + '    </div>'
                                + '    <div class="col-sm-3">'
                                + '        <div class="form-group row">'
                                + '            <label class="col-sm-5 col-form-label">Sending Date*</label>'
                                + '            <div class="col-sm-7">'
                                + '                <input type="date"  class="form-control" id="txtSending_Date_' + Type + "_" +  Id + '" placeholder="dd/mm/yyyy">'
                                + '            </div>'
                                + '        </div>'
                                + '    </div>'
                                + '    <div class="col-sm-3">'
                                + '        <div class="form-group row">'
                                + '            <label class="col-sm-5 col-form-label">Receiving Date*</label>'
                                + '            <div class="col-sm-7">'
                                + '                <input type="date"  class="form-control"  id="txtReceiving_Date_' + Type + "_" +  Id + '" placeholder="dd/mm/yyyy">'
                                + '            </div>'
                                + '        </div>'
                                + '    </div>'
                                + '    <div class="col-sm-2" >'
                                + '        <div class="form-group row">'
                                + '            <div class="col-sm-12 text-right">'
                                + '                <a data-Id="' + Id + '" data-Type="' + Type + '" data-Send_Date="' + Send_Date + '" data-Received_Date="' + Received_Date + '"onclick="Save(this);" class="btn btn-info">Submit</a>'
                                + '            </div>'
                                + '        </div>'
                                + '    </div>'
                                + '</div>'
                                + '<hr />');

                            if (Send_Date != "0001-01-01") {
                                $("#txtSending_Date_" + Type + "_" + Id).val(Send_Date);
                            }

                            if (Received_Date != "0001-01-01") {
                                $("#txtReceiving_Date_" + Type + "_" + Id).val(Received_Date);
                            }

                        }
                        if (Type == "Land") {
                            $("#Header_Land").css("display", "block");


                            $("#Main_Div_Append_Land").append('<div class="row">'
                                + '    <div class="col-sm-4">'
                                + '        <div class="form-group row">'
                                + '            <label class="col-sm-6 col-form-label">Guarantor Name*</label>'
                                + '            <div class="col-sm-6">'
                                + '                <input type="text" value="' + Guarantor_Name + '" class="form-control"  disabled="disabled" placeholder="Guarantor Name">'
                                + '            </div>'
                                + '        </div>'
                                + '    </div>'
                                + '    <div class="col-sm-3">'
                                + '        <div class="form-group row">'
                                + '            <label class="col-sm-5 col-form-label">Sending Date*</label>'
                                + '            <div class="col-sm-7">'
                                + '                <input type="date"  class="form-control" id="txtSending_Date_' + Type + "_" +  Id + '" placeholder="dd/mm/yyyy">'
                                + '            </div>'
                                + '        </div>'
                                + '    </div>'
                                + '    <div class="col-sm-3">'
                                + '        <div class="form-group row">'
                                + '            <label class="col-sm-5 col-form-label">Receiving Date*</label>'
                                + '            <div class="col-sm-7">'
                                + '                <input type="date"  class="form-control"  id="txtReceiving_Date_' + Type + "_" +  Id + '" placeholder="dd/mm/yyyy">'
                                + '            </div>'
                                + '        </div>'
                                + '    </div>'
                                + '    <div class="col-sm-2" >'
                                + '        <div class="form-group row">'
                                + '            <div class="col-sm-12 text-right">'
                                + '                <a data-Id="' + Id + '" data-Type="' + Type + '" data-Send_Date="' + Send_Date + '" data-Received_Date="' + Received_Date + '"onclick="Save(this);" class="btn btn-info">Submit</a>'
                                + '            </div>'
                                + '        </div>'
                                + '    </div>'
                                + '</div>'
                                + '<hr />');

                            if (Send_Date != "0001-01-01") {
                                $("#txtSending_Date_" + Type + "_" + Id).val(Send_Date);
                            }

                            if (Received_Date != "0001-01-01") {
                                $("#txtReceiving_Date_" + Type + "_" + Id).val(Received_Date);
                            }
                        }
                        if (Type == "Personal") {
                            $("#Header_Personal").css("display", "block");


                            $("#Main_Div_Append_Personal").append('<div class="row">'
                                + '    <div class="col-sm-4">'
                                + '        <div class="form-group row">'
                                + '            <label class="col-sm-6 col-form-label">Guarantor Name*</label>'
                                + '            <div class="col-sm-6">'
                                + '                <input type="text" value="' + Guarantor_Name + '" class="form-control"  disabled="disabled" placeholder="Guarantor Name">'
                                + '            </div>'
                                + '        </div>'
                                + '    </div>'
                                + '    <div class="col-sm-3">'
                                + '        <div class="form-group row">'
                                + '            <label class="col-sm-5 col-form-label">Sending Date*</label>'
                                + '            <div class="col-sm-7">'
                                + '                <input type="date"  class="form-control" id="txtSending_Date_' + Type + "_" + Id + '" placeholder="dd/mm/yyyy">'
                                + '            </div>'
                                + '        </div>'
                                + '    </div>'
                                + '    <div class="col-sm-3">'
                                + '        <div class="form-group row">'
                                + '            <label class="col-sm-5 col-form-label">Receiving Date*</label>'
                                + '            <div class="col-sm-7">'
                                + '                <input type="date"  class="form-control"  id="txtReceiving_Date_' + Type + "_" +  Id + '" placeholder="dd/mm/yyyy">'
                                + '            </div>'
                                + '        </div>'
                                + '    </div>'
                                + '    <div class="col-sm-2" >'
                                + '        <div class="form-group row">'
                                + '            <div class="col-sm-12 text-right">'
                                + '                <a data-Id="' + Id + '" data-Type="' + Type + '" data-Send_Date="' + Send_Date + '" data-Received_Date="' + Received_Date + '"onclick="Save(this);" class="btn btn-success">Submit</a>'
                                + '            </div>'
                                + '        </div>'
                                + '    </div>'
                                + '</div>'
                                + '<hr />');

                            if (Send_Date != "0001-01-01") {
                                $("#txtSending_Date_" + Type + "_" +  Id).val(Send_Date);
                            }

                            if (Received_Date != "0001-01-01") {
                                $("#txtReceiving_Date_" + Type + "_" +  Id).val(Received_Date);
                            }

                        }
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                //alert(Present_District);
            });
        }
        
        function Save(This_Val) {
            //alert(Counter);

            var Id = $(This_Val).attr("data-Id");
            var Type = $(This_Val).attr("data-Type");
            var Send_Date = $(This_Val).attr("data-Send_Date");
            var Received_Date = $(This_Val).attr("data-Received_Date");

            if ($("#txtSending_Date_" + Type + "_" + Id).val() != "") {
                Send_Date = $("#txtSending_Date_" + Type + "_" + Id).val();
            }

            if ($("#txtReceiving_Date_" + Type + "_" + Id).val() != "") {
                Received_Date = $("#txtReceiving_Date_" + Type + "_" +  Id).val();
            }

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Update_Surety_Confirmation_By_Id",
                data: JSON.stringify({ id: Id, type: Type, dte_conf_send_date: Send_Date, dte_conf_rec_date: Received_Date }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                },
                error: function (jqXHR, textStatus, errorThrown) {
                    // Handle AJAX error
                    alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                    })

                }

            }).done(function () {

                //  Load_Surety_Details_By_LoaanappId();


                Toast.fire({
                    icon: 'success',
                    title: 'Guarantor Confirmation dates Updated!'
                })

            });
        }


        function Confirm() {
          

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Update_To_tbl_loanapp_Status",
                data: JSON.stringify({ int_loanappid: getParameterByName("Id"), status: "Guarantor Confirmed" }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                },
                error: function (jqXHR, textStatus, errorThrown) {
                    // Handle AJAX error
                    alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                    })

                }

            }).done(function () {

                //  Load_Surety_Details_By_LoaanappId();


                Swal.fire({
                    icon: 'success',
                    title: 'Message',
                    text: 'Guarantor Confirmation Successfully Submitted !',
                    allowOutsideClick: false,

                }).then((result) => {
                    if (result.isConfirmed) {
                        // OK button clicked

                        location.href = 'Surety_Confirmation_List.aspx';
                        // Your code here
                    }
                });

            });
        }







        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }


        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }



    </script>
</asp:Content>
