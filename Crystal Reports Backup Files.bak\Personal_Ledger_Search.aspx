﻿<%@ Page Title="Personal ledger | Search" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Personal_Ledger_Search.aspx.cs" Inherits="KSDCSCST_Portal.Personal_Ledger_Search" %>



<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">
    <link rel="stylesheet" href="sweetalert2.min.css">

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Personal Ledger</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">Personal Ledger</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="DIV_Agency">
                                    <div style="display:none;">
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="ChkBranch">
                                            <label class="form-check-label">Select For Other Branch</label>
                                        </div>

                                    </div>
                                    <div id="DIV_District" style="display: none;">
                                        <div class="form-group row" >
                                            <label for="inputEmail" class="col-sm-3 col-form-label">District</label>
                                            <div class="col-sm-9">
                                                <select id="dropDistrict" class="form-control">
                                                    <option value="Please wait..">Please wait..</option>


                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="ChkLive">
                                            <label class="form-check-label">Search For Live Loans Only</label>
                                        </div>

                                    </div>
                                    <hr />
                                        </div>
                                    <div class="form-group row">
                                        <label for="inputName" class="col-sm-3 col-form-label">Old Loan No</label>
                                        <div class="col-sm-9">
                                            <input type="text" required name="txtOld_Loan_No" class="form-control" id="txtOld_Loan_No" placeholder="Old Loan No">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName" class="col-sm-3 col-form-label">Loan No</label>
                                        <div class="col-sm-9">
                                            <input type="text" required name="txtLoan_No" class="form-control" id="txtLoan_No" placeholder="Loan No">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName" class="col-sm-3 col-form-label">Loanee Name</label>
                                        <div class="col-sm-9">
                                            <input type="text" required name="txtLoanee_Name" class="form-control" id="txtLoanee_Name" placeholder="Loanee Name">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName" class="col-sm-3 col-form-label">House Name</label>
                                        <div class="col-sm-9">
                                            <input type="text" required name="txtHouse_Name" class="form-control" id="txtHouse_Name" placeholder="House Name">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName" class="col-sm-3 col-form-label">Mobile No</label>
                                        <div class="col-sm-9">
                                            <input type="text" required name="txtMobile_No" class="form-control" id="txtMobile_No" placeholder="Mobile No">
                                        </div>
                                    </div>





                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">

                                            <a onclick="Search();" class="btn btn-success">Search</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->


    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script>
        $(document).ready(function () {

            Load_All_Districts();
            $('#ChkBranch').change(function () {
                if ($(this).is(':checked')) {
                    // Checkbox is checked
                    $("#DIV_District").css("display", "block");
                } else {
                    // Checkbox is unchecked
                    $("#DIV_District").css("display", "none");
                }
            });
        });


        function Load_All_Districts() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropDistrict').empty();
                    $('#dropDistrict').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var District_Name = value.District_Name;
                        var html = '<option value="' + Id + '">' + District_Name + '</option>';
                        $('#dropDistrict').append(html);
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });

        }

        function Search() {
          
            location.href = 'Personal_Ledger_List.aspx?Old_Loan_No=' + $("#txtOld_Loan_No").val() + '&Loan_No=' + $("#txtLoan_No").val() + '&Loanee_Name=' + $("#txtLoanee_Name").val() + '&House_Name=' + $("#txtHouse_Name").val() + '&Mobile_No=' + $("#txtMobile_No").val();
        }

        function Save() {
            var IsActive = $("#checkActive").is(":checked") ? $("#checkActive").val() : "off";
            if (IsActive == 'on') {
                IsActive = '1';
            }
            else {
                IsActive = '0';
            }


            $.ajax({
                type: "POST", // or "GET" depending on your web method
                url: "WebService.asmx/Agency_Add",
                data: JSON.stringify({ Name: $("#txtAgency_Name").val(), Description: $("#txtDescription").val(), Cast: $("#dropCast").val(), IsActive: IsActive, CreatedBy: "1" }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (response) {

                    // Handle the successful response from the web method
                    console.log(response.d); // "d" is the default property name for the response
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Agency Successfully Saved !',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked

                            location.href = 'Agency_List.aspx';
                            // Your code here
                        }
                    });

                },
                error: function (xhr, status, error) {
                    // Handle the error response

                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                },
                done: function (response) {
                    // Handle the error response
                    alert(response);

                }
            });


        }
    </script>
</asp:Content>
