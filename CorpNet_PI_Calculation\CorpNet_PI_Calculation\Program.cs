﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CorpNet_PI_Calculation
{
    class Program
    {
        static void Main(string[] args)
        {


            double principal = 190000;
            int loanDurationMonths = 60;
            double annualInterestRate = 0.02; // 2% interest rate

            double monthlyInterestRate = annualInterestRate / 12;
            double emi = 3800;// CalculateEMI(principal, annualInterestRate, loanDurationMonths);

            DateTime firstDueDate = new DateTime(2020, 7, 5);
            DateTime customerPayeeDate = new DateTime(2020, 6, 1);

            double piValue = CalculatePIValue(principal, emi, monthlyInterestRate, firstDueDate, customerPayeeDate);

            Console.WriteLine("PI Value: " + piValue.ToString("N2"));




            double Dmdamt = 0;
            double instamt = 3800;
            double PI = 0;
            double Penal_Interest_Rate = 2;
            int int_repay_inst = 60;

            DateTime First_Due_Date = new DateTime(2020, 05, 07);

            DateTime Due_Date = new DateTime(2020, 05, 07);
           // DateTime From_Date = new DateTime(2020, 05, 07);
            DateTime curDate = new DateTime(2020, 08, 14);


            DateTime lduedate = First_Due_Date.AddMonths(int_repay_inst - 1);


            if (curDate.Month == 2 && First_Due_Date.Day > 28)
            {
                Due_Date = new DateTime(curDate.Year, curDate.Month, 28);
            }
            else if ((curDate.Month == 4 || curDate.Month == 6 || curDate.Month == 9 || curDate.Month == 11) && First_Due_Date.Day > 30)
            {
                Due_Date = new DateTime(curDate.Year, curDate.Month, 30);
            }
            else
            {
                Due_Date = new DateTime(curDate.Year, curDate.Month, First_Due_Date.Day);
            }

          
            if (Due_Date > curDate)
            {
                Due_Date = Due_Date.AddMonths(-1);
            }
            if (Due_Date > lduedate)
            {
                Due_Date = lduedate;
            }


            TimeSpan difference = curDate - Due_Date;
            double daysDifference = difference.Days;
            daysDifference = daysDifference + 1;

            TimeSpan Dmdamt_difference = Due_Date - First_Due_Date;
            double Dmdamt_Day__difference = Dmdamt_difference.Days;
            Dmdamt_Day__difference = Dmdamt_Day__difference + 1;

            Dmdamt = Dmdamt_Day__difference * instamt;
            PI = (Dmdamt * daysDifference) * Penal_Interest_Rate / 36500;
            PI = Math.Round(PI, 2);

            Console.WriteLine($"Day Difference: {daysDifference} days");

           
        }

        static double CalculateEMI(double principal, double annualInterestRate, int loanDurationMonths)
        {
            double monthlyInterestRate = annualInterestRate / 12;
            double emi = principal * (monthlyInterestRate * Math.Pow(1 + monthlyInterestRate, loanDurationMonths)) /
                         (Math.Pow(1 + monthlyInterestRate, loanDurationMonths) - 1);
            return emi;
        }

        static double CalculatePIValue(double principal, double emi, double monthlyInterestRate, DateTime firstDueDate, DateTime payeeDate)
        {
            int monthsElapsed = (payeeDate.Year - firstDueDate.Year) * 12 + payeeDate.Month - firstDueDate.Month;
            double outstandingPrincipal = principal * Math.Pow(1 + monthlyInterestRate, monthsElapsed) - (emi * (Math.Pow(1 + monthlyInterestRate, monthsElapsed) - 1) / monthlyInterestRate);

            double interestComponent = outstandingPrincipal * monthlyInterestRate;
            double principalComponent = emi - interestComponent;

            return principalComponent + interestComponent;
        }
    }
}
