// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "Oggi";
var L_January   = "Gennaio";
var L_February  = "Febbraio";
var L_March     = "Marzo";
var L_April     = "Aprile";
var L_May       = "Maggio";
var L_June      = "Giugno";
var L_July      = "Luglio";
var L_August    = "Agosto";
var L_September = "Settembre";
var L_October   = "Ottobre";
var L_November  = "Novembre";
var L_December  = "Dicembre";
var L_Su        = "dom";
var L_Mo        = "lun";
var L_Tu        = "mar";
var L_We        = "mer";
var L_Th        = "gio";
var L_Fr        = "ven";
var L_Sa        = "sab";

// strings for dt_param.js
var L_TIME_SEPARATOR = ":";
var L_AM_DESIGNATOR = "AM";
var L_PM_DESIGNATOR = "PM";

// strings for range parameter
var L_FROM = "Da {0}";
var L_TO = "A {0}";
var L_AFTER = "Dopo {0}";
var L_BEFORE = "Prima di {0}";
var L_FROM_TO = "Da {0} a {1}";
var L_FROM_BEFORE = "Da {0} a prima di {1}";
var L_AFTER_TO = "Dopo {0} a {1}";
var L_AFTER_BEFORE = "Dopo {0} a prima di {1}";

// Strings for prompts.js and prompts_param.js
var L_BadNumber		= "Questo parametro \u00e8 di tipo \"Numero\" e pu\u00f2 contenere solo un segno negativo, cifre (\"0-9\"), simboli di raggruppamento di cifre o un simbolo decimale. Correggere il valore di parametro immesso.";
var L_BadCurrency	= "Questo parametro \u00e8 di tipo \"Valuta\" e pu\u00f2 contenere solo un segno negativo, cifre (\"0-9\"), simboli di raggruppamento di cifre o un simbolo decimale. Correggere il valore di parametro immesso.";
var L_BadDate		= "Questo parametro \u00e8 di tipo \"Data\" e deve presentarsi nel formato \"Data(aaaa,mm,gg)\" in cui \"aaaa\" corrisponde all'anno di quattro cifre, \"mm\" corrisponde al mese (ad esempio, gennaio = 1) e \"gg\" corrisponde al numero di giorni del mese specificato.";
var L_BadDateTime   = "Questo parametro \u00e8 di tipo \"DataOra\" e il formato corretto \u00e8 \"DateTime(aaaa,mm,gg,hh,mm,ss)\". \"aaaa\" corrisponde all'anno di quattro cifre, \"mm\" corrisponde al mese (ad esempio, gennaio = 1), \"gg\" corrisponde al giorno del mese, \"hh\" corrisponde alle ore in un formato di 24 ore, \"mm\" corrisponde ai minuti e \"ss\" ai secondi.";
var L_BadTime       = "Questo parametro \u00e8 di tipo \"Ora\" e deve essere espresso come \"Time(hh,mm,ss)\", in cui \"hh\" corrisponde alle ore in un formato di 24 ore, \"mm\" corrisponde ai minuti di un'ora e \"ss\" corrisponde ai secondi di un minuto.";
var L_NoValue       = "Nessun valore";
var L_BadValue      = "Per impostare \"Nessun valore\", \u00e8 necessario impostare entrambi i valori Da e A su \"Nessun valore\".";
var L_BadBound      = "Impossibile impostare \"Nessun limite inferiore\" insieme a \"Nessun limite superiore\".";
var L_NoValueAlready = "Questo parametro \u00e8 gi\u00e0 impostato su \"Nessun valore\". Rimuovere \"Nessun valore\" prima di aggiungere altri valori";
var L_RangeError    = "L'inizio dell'intervallo non pu\u00f2 essere maggiore della sua fine.";
var L_NoDateEntered = "Immettere una data.";

// Strings for ../html/crystalexportdialog.htm
var L_ExportOptions     = "Opzioni di esportazione";
var L_PrintOptions      = "Opzioni di stampa";
var L_PrintPageTitle    = "Stampa il report";
var L_ExportPageTitle   = "Esporta il report";
var L_OK                = "OK";
var L_PrintPageRange    = "Immettere l'intervallo di pagine da stampare.";
var L_ExportPageRange   = "Immettere l'intervallo di pagine da esportare.";
var L_InvalidPageRange  = "I valori d'intervallo della pagina non sono corretti. Immettere un intervallo di pagine valido.";
var L_ExportFormat      = "Selezionare un formato di esportazione dall'elenco.";
var L_Formats           = "Formati:";
var L_All               = "Tutto";
var L_Pages             = "Pagine";
var L_From              = "Da:";
var L_To                = "A:";
var L_PrintStep0        = "Da stampare:";
var L_PrintStep1        = "1.  Nella finestra di dialogo che verr\u00e0 visualizzata, selezionare l'opzione \"Apri questo file\" e fare clic sul pulsante OK.";
var L_PrintStep2        = "2.  Fare clic sull'icona della stampante del menu di Acrobat Reader invece che sul pulsante Stampa del browser Internet.";
var L_RTFFormat         = "Rich Text Format";
var L_AcrobatFormat     = "Acrobat Format (PDF)";
var L_CrystalRptFormat  = "Crystal Reports (RPT)";
var L_WordFormat        = "MS Word";
var L_ExcelFormat       = "MS Excel 97-2000";
var L_ExcelRecordFormat = "MS Excel 97-2000 (solo dati)";
if(typeof(Sys)!=='undefined')Sys.Application.notifyScriptLoaded();
