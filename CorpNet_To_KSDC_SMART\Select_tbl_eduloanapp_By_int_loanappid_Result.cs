//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CorpNet_To_KSDC_SMART
{
    using System;
    
    public partial class Select_tbl_eduloanapp_By_int_loanappid_Result
    {
        public decimal int_eduloanappid { get; set; }
        public Nullable<decimal> int_loanappid { get; set; }
        public string vchr_stuname { get; set; }
        public string vchr_stufathername { get; set; }
        public string vchr_stumothername { get; set; }
        public Nullable<System.DateTime> dte_studob { get; set; }
        public Nullable<decimal> int_stuage { get; set; }
        public string vchr_caste { get; set; }
        public string Sub_Cast { get; set; }
        public string vchr_presenthsename { get; set; }
        public string vchr_presentlane1 { get; set; }
        public string vchr_presentlane2 { get; set; }
        public string vchr_presentpost { get; set; }
        public Nullable<decimal> int_presentpin { get; set; }
        public string vchr_presentphno { get; set; }
        public string vchr_permhsename { get; set; }
        public string vchr_permlane1 { get; set; }
        public string vchr_permlane2 { get; set; }
        public string vchr_permpost { get; set; }
        public Nullable<decimal> int_permpin { get; set; }
        public string vchr_permphno { get; set; }
        public string vchr_coursename { get; set; }
        public string vchr_instname { get; set; }
        public string vchr_affluni { get; set; }
        public string vchr_affldetails { get; set; }
        public string vchr_duration { get; set; }
        public string vchr_month_commencement { get; set; }
        public Nullable<decimal> int_yr_commencement { get; set; }
        public string vchr_month_completion { get; set; }
        public Nullable<decimal> int_yr_completion { get; set; }
        public string vchr_parent_fullname { get; set; }
        public string vchr_parent_presenthsename { get; set; }
        public string vchr_parent_presentlane1 { get; set; }
        public string vchr_parent_presentlane2 { get; set; }
        public string vchr_parent_presentpost { get; set; }
        public Nullable<decimal> int_parent_presentpin { get; set; }
        public string vchr_parent_presentphno { get; set; }
        public string vchr_parent_permhsename { get; set; }
        public string vchr_parent_permlane1 { get; set; }
        public string vchr_parent_permlane2 { get; set; }
        public string vchr_parent_permpost { get; set; }
        public Nullable<decimal> int_parent_permpin { get; set; }
        public string vchr_parent_permphno { get; set; }
        public string vchr_parent_relationship { get; set; }
        public string vchr_employed { get; set; }
        public string vchr_parentfathername { get; set; }
        public Nullable<decimal> int_parentage { get; set; }
        public string vchr_sex { get; set; }
        public string vchr_country { get; set; }
        public string vchr_state { get; set; }
    }
}
