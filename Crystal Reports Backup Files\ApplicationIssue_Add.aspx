﻿<%@ Page Title="Application Issue | Add" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="ApplicationIssue_Add.aspx.cs" Inherits="KSDCSCST_Portal.ApplicationIssue_Add" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">
    <style>
        @media print {
            html, body {
                width: 210mm;
                height: 297mm;
                margin: 0;
            }

            /* Set landscape orientation */
            @page {
                size: A4 portrait;
            }
        }
    </style>

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Application Issue Add</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">Application Issue Add</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">

                                    <div class="form-group row">
                                        <label for="inputName" class="col-sm-3 col-form-label">Date(DD/MM/YYYY)</label>
                                        <div class="col-sm-9">
                                            <input disabled="disabled" type="text" class="form-control" id="txtApplication_Date" placeholder="Date(DD/MM/YYYY)">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">District *</label>
                                        <div class="col-sm-9">
                                            <select id="dropDistrict" disabled="disabled" class="form-control">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Branch *</label>
                                        <div class="col-sm-9">
                                            <select id="dropSubDistrict" disabled="disabled" class="form-control">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row" style="display: none;">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Application Number</label>
                                        <div class="col-sm-9">
                                            <input disabled="disabled" type="text" class="form-control" id="inputName2" value="255" placeholder="Mobile Number">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="txtApplicant_Name" class="col-sm-3 col-form-label">Name of Applicant *</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtApplicant_Name" placeholder="Name of Applicant">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">
                                            Address
                                        </label>
                                        <div class="col-sm-9">
                                            <textarea class="form-control" maxlength="200" id="txtAddress" placeholder="Address"></textarea>
                                            <span class="validation_message">Maximum text length is 200</span>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Aadhar Number*</label>
                                        <div class="col-sm-9">
                                            <input type="number" max="12" class="form-control" id="txtAadharNumber" placeholder="Aadhar Number">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Loan Scheme *</label>
                                        <div class="col-sm-9">
                                            <select id="dropScheme" class="form-control">
                                                <option>Select</option>


                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Caste *</label>
                                        <div class="col-sm-9">
                                            <select id="dropCast" class="form-control">
                                                <option>Select</option>

                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Sub Caste *</label>
                                        <div class="col-sm-9">
                                            <select id="dropSubCast" class="form-control">
                                                <option>Select</option>


                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row" style="display: none;">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Receipt No *</label>
                                        <div class="col-sm-9">
                                            <input disabled="disabled" type="text" class="form-control" id="inputName2" value="65758" placeholder="Name of Applicant">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Phone No *</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtPhoneNo" placeholder="Phone No">
                                            <span style="font-style: italic; color: red; font-size: 14px; height: 35px; display: block;">seperate numbers by comma ","</span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Amount *</label>
                                        <div class="col-sm-9">
                                            <input disabled="disabled" type="text" class="form-control" id="txtAmount" value="10" placeholder="Name of Applicant">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Remarks</label>
                                        <div class="col-sm-9">
                                            <textarea class="form-control" maxlength="200" id="txtRemarks" placeholder="Remarks"></textarea>
                                            <span class="validation_message">Maximum text length is 200</span>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">

                                            <a href="ApplicationIssue_List.aspx" class="btn btn-dark">Back</a>
                                            <%--   <a onclick="Print();" class="btn btn-info">Print</a>--%>
                                            <a onclick="Save();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>

    <div id="Print_Section" style="display: none;">

        <div style="padding: 0px; float: left; width: 100%;">

            <div style="margin: 20px;">
                <div style="width: 100%">
                    <div style="width: 30%; float: left;">
                        <img style="width: 220px;" src="assets/img/Logo_Black_And_White.jpg" />
                        <p style="font-size: 12px; font-weight: bold; margin: 0; margin-top: 9px;">A GOVT.OF KERALA UNDER TAKING</p>
                    </div>
                    <div style="width: 70%; float: left;">
                        <p style="font-size: 16px; font-weight: bold; line-height: 1.5; margin-bottom: 0px !important; margin-top: 0px;">കേരള സംസ്ഥാന പട്ടികജാതി  പട്ടിക വർഗ്ഗ വികസന  കോർപ്പറേഷൻ  ലിമിറ്റഡ്</p>
                        <p style="margin-bottom: 0; font-size: 14px; margin-top: 5px;">CIN: U91990KL1972SGC002466</p>
                        <p style="margin-bottom: 0; font-size: 12px; margin-top: 5px;">
                            <b>CORPORATE OFFICE:</b> P.B. No. 523, Town Hall Road, Thrissur - 680020 |
                                                       
                                                        <b>Off.Ph:</b> 0487 2331064 |
                                                            <br />
                            <b>Email : </b><EMAIL> | Website : ksdcscst.kerala.gov.in
                        </p>
                    </div>
                </div>

                <div style="width: 100%; float: left; margin-top: 5px;">
                    <div style="width: 25%; float: left;">
                        <div style="text-align: left; font-weight: bold;">
                            <span id="span_Receipt_No" style="font-size: 14px;">Receipt No: </span>
                            <label style="font-size: 14px; font-weight: normal !important;"></label>
                        </div>
                    </div>
                    <div style="width: 50%; float: left;">
                        <div style="text-align: center; font-weight: bold;">
                            <p style="font-size: 14px; text-decoration: underline;">DISTRICT OFFICE :  <span id="span_Office_Name"></span></p>
                        </div>
                    </div>
                    <div style="width: 25%; float: left;">
                        <div style="text-align: right; font-weight: bold;">
                            <span style="font-size: 14px;">Date : </span>
                            <label id="lblDate" style="font-size: 14px; font-weight: normal !important;"></label>
                        </div>
                    </div>
                </div>


                <hr style="float: left; width: 100%; margin-top: 0px;" />
                <div style="width: 100%; float: left; margin-top: 5px;">
                    <div style="width: 100%; float: left;">
                        <div style="text-align: left; font-weight: bold;">
                            <span style="font-size: 14px; float: left; font-weight: normal !important;">Received From </span>
                            <label id="lblName" style="font-size: 14px; float: left; margin-left: 5px; margin-right: 5px;"></label>
                            <span style="font-size: 14px; float: left; font-weight: normal !important;">the sum of Rupees </span>
                            <label style="font-size: 14px; float: left; margin-left: 5px; margin-right: 5px;">Ten Rupees Only</label>

                        </div>
                    </div>
                    <div style="width: 100%; float: left;">
                        <span style="font-size: 14px; font-weight: bold;">in cash towards the following </span>
                    </div>
                    <div style="width: 100%; float: left;">
                        <table border="1" style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                            <tr>
                                <td rowspan="2" style="text-align: center; font-weight: bold; font-size: 12px;">തിയ്യതി</td>
                                <td rowspan="2" style="text-align: center; font-weight: bold; font-size: 12px;">ഇനം</td>
                                <td colspan="2" style="text-align: center; font-weight: bold; font-size: 12px;">തുക</td>

                            </tr>
                            <tr>

                                <td style="text-align: center; font-weight: bold;">Rs.</td>
                                <td style="text-align: center; font-weight: bold;">Ps.</td>

                            </tr>
                            <tr>
                                <td><span id="span_Date"></span></td>
                                <td style="font-size: 12px;">അപേക്ഷ ഫോറം</td>

                                <td style="text-align: right;">10</td>
                                <td style="text-align: right;">0</td>
                            </tr>
                            <tr>
                                <td colspan="2" style="padding-right: 10px; font-weight: bold; text-align: right; font-size: 12px;">ആകെ</td>

                                <td style="font-weight: bold; text-align: right;" colspan="2">10.00</td>

                            </tr>
                            <tr>
                                <td style="font-weight: bold;">Prepared</td>
                                <td style="font-weight: bold;">Checked</td>
                                <td style="text-align: right;" colspan="2"></td>

                            </tr>
                        </table>
                    </div>
                    <div style="width: 100%; float: right;">
                        <span style="width: 45%; font-size: 14px; text-align: right; line-height: 1.5; margin-bottom: 0px !important; margin-top: 10px; float: right;">For The Kerala State Development Corporation For Scheduled Castes & Scheduled Tribes Limited </span>

                    </div>
                    <div style="width: 100%; float: right;">
                        <span style="width: 45%; font-size: 14px; text-align: right; line-height: 1.5; margin-bottom: 0px !important; margin-top: 65px; float: right; font-weight: bold;">ജില്ലാ മാനേജർ</span>

                    </div>
                    <div style="width: 100%; float: left; text-align: center;">
                        <span style="font-size: 14px; text-align: center; line-height: 1.5; margin-bottom: 0px !important; margin-top: 10px;">This Receipt is valid only on realisation of Cheque/Draft</span>

                    </div>
                </div>
            </div>

        </div>

    </div>
    <!-- /.content -->

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <script src="assets/plugins/toastr/toastr.min.js"></script>



    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/inputmask/5.0.4/jquery.inputmask.min.js"></script>
    <script>
        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;
        $(document).ready(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            // Get the current date
            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtApplication_Date').val(formattedDate);

            $("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
            $("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");
            Load_All_Schemes();
            //   Load_All_Districts();

            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }


            $(".form-control").on("focusout", function () {
                $(".form-control").removeAttr("style");

                var This_Id = $(this).attr("id");
                var This_Control = $(this);
                if (This_Id == "txtApplicant_Name") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Name of Applicant is required !'
                        })
                    }
                    else if (!Is_Valid_Text(This_Control.val().trim())) {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Special characters or Numbers not allowed in Name of Applicant !'
                        })
                    }
                }
                else if (This_Id == "dropScheme") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Loan Scheme is required !'
                        })
                    }
                }
                else if (This_Id == "dropCast") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Caste is required !'
                        })
                    }
                }
                else if (This_Id == "dropSubCast") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Sub Caste is required !'
                        })
                    }
                }
                else if (This_Id == "txtPhoneNo") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Phone No is required !'
                        })
                    }
                }
                //else if (This_Id == "txtAadharNumber") {
                //    if (This_Control.val().trim() == "") {
                //        Focus_Error(This_Control);
                //        Toast.fire({
                //            icon: 'error',
                //            title: 'Aadhar No is required !'
                //        })
                //    }
                //}



                // Your common focusout function code goes here
                //   alert("Input value changed: " + value);
            });



            //$(document).on("input", ".form-control", function () {
            //    // Get the current value of the input
            //    var currentValue = $(this).val();

            //    // Convert the value to propcase
            //    var propcaseValue = currentValue.replace(/\w\S*/g, function (txt) {
            //        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
            //    });

            //    // Update the input with the propcase value
            //    $(this).val(propcaseValue);
            //});



        });


        function Print() {
            var printWindow = window.open('', '_blank');
            printWindow.document.open();
            printWindow.document.write('<html><head><title>Print</title></head><body style="width: 210mm;height: 297mm;margin: 0;  ">');
            printWindow.document.write($("#Print_Section").html());
            printWindow.document.write('</body></html>');
            printWindow.document.close();
            printWindow.print();
            if (confirm('Print dialog initiated. Please close the window after printing.')) {
                printWindow.close();
            }
        }

        function Load_All_Districts() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropDistrict').empty();
                    $('#dropDistrict').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var District_Name = value.District_Name;
                        var html = '<option value="' + Id + '">' + District_Name + '</option>';
                        if (Id != 15) {
                            $('#dropDistrict').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $('#dropDistrict').change(function () {
                    Load_All_Sub_Districts($(this).val());
                });

            });

        }
        function Load_All_Sub_Districts(District_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Districts",
                data: JSON.stringify({ District_Id: District_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubDistrict').empty();
                    $('#dropSubDistrict').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Sub_District_Name = value.Sub_District_Name;
                        var html = '<option value="' + Id + '">' + Sub_District_Name + '</option>';

                        $('#dropSubDistrict').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });

        }

        function Load_All_Schemes() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Schemes",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropScheme').empty();
                    $('#dropScheme').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var IsActive = value.IsActive;
                        var Scheme = value.Scheme;
                        var html = '<option value="' + Id + '">' + Scheme + '</option>';
                        if (IsActive == 1) {
                            $('#dropScheme').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                $('#dropScheme').change(function () {
                    Load_All_Cast($(this).val());
                });
                

            });

        }

        function Load_All_Cast() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Cast_By_Scheme_Id",
                data: JSON.stringify({ Scheme_Id: $("#dropScheme").val() }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropCast').empty();
                    $('#dropCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var Cast = value.Cast;
                        var html = '<option value="' + Id + '">' + Cast + '</option>';

                        $('#dropCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $('#dropCast').change(function () {
                    Load_All_SubCast($(this).val());
                });

            });

        }


        function Load_All_SubCast(Cast_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Cast",
                data: JSON.stringify({ Cast_Id: Cast_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubCast').empty();
                    $('#dropSubCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var SubCast = value.SubCast;
                        var html = '<option value="' + Id + '">' + SubCast + '</option>';

                        $('#dropSubCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {


            });

        }
        function Save() {
            //alert(Counter);
            if (confirm('Are you sure you want to perform this action?')) {



                var district_Id = $("#dropDistrict");

                var subDistrict_id = $("#dropSubDistrict");
                var name_Applicant = $("#txtApplicant_Name");
                var Address = $("#txtAddress");
                var scheme_Id = $("#dropScheme");

                var cast_Id = $("#dropCast");
                var sub_Cast_Id = $("#dropSubCast");

                var remarks = $("#txtRemarks");
                var PhoneNo = $("#txtPhoneNo");
                var AadharNumber = $("#txtAadharNumber");





                $(".form-control").removeAttr("style");



                if (name_Applicant.val().trim() == "") {
                    Focus_Error(name_Applicant);
                    Toast.fire({
                        icon: 'error',
                        title: 'Name of Applicant is required !'
                    })
                }
                else if (Address.val().trim() == "") {
                    Focus_Error(Address);
                    Toast.fire({
                        icon: 'error',
                        title: 'Applicant Address is required !'
                    })
                }
                else if (!Is_Valid_Text(name_Applicant.val().trim())) {
                    Focus_Error(name_Applicant);
                    Toast.fire({
                        icon: 'error',
                        title: 'Special characters or Numbers not allowed in Name of Applicant !'
                    })
                }
                else if (scheme_Id.val() == "0") {
                    Focus_Error(scheme_Id);
                    Toast.fire({
                        icon: 'error',
                        title: 'Loan Scheme is required !'
                    })
                }
                else if (cast_Id.val() == "0") {
                    Focus_Error(cast_Id);
                    Toast.fire({
                        icon: 'error',
                        title: 'Caste is required !'
                    })
                }
                else if (sub_Cast_Id.val() == "0") {
                    Focus_Error(sub_Cast_Id);
                    Toast.fire({
                        icon: 'error',
                        title: 'Sub Caste is required !'
                    })
                }
                else if (PhoneNo.val().trim() == "") {
                    Focus_Error(PhoneNo);
                    Toast.fire({
                        icon: 'error',
                        title: 'Phone No is required !'
                    })
                }
                //else if (AadharNumber.val().trim() == "") {
                //    Focus_Error(PhoneNo);
                //    Toast.fire({
                //        icon: 'error',
                //        title: 'Aadhar No is required !'
                //    })
                //}


                else {
                    if (PhoneNo.val().trim() != "") {

                        var values = PhoneNo.val().trim().split(",");
                        for (var i = 0; i < values.length; i++) {
                            if (values[values.length - 1] != "") {
                                if (!Is_Valid_Mobile_Number(values[i])) {
                                    Focus_Error(PhoneNo);
                                    Toast.fire({
                                        icon: 'error',
                                        title: 'Enter valid phone number !'
                                    })
                                    return;
                                }
                            }
                        }
                    }

                    $.ajax({
                        type: "POST",
                        dataType: "json",
                        url: "WebService.asmx/Insert_To_tbl_Loan_App_Issue",
                        data: JSON.stringify({ district_Id: district_Id.val(), subDistrict_id: subDistrict_id.val(), name_Applicant: name_Applicant.val(), address_Applicant: Address.val(), scheme_Id: scheme_Id.val(), cast_Id: cast_Id.val(), sub_Cast_Id: sub_Cast_Id.val(), remarks: remarks.val(), PhoneNo: PhoneNo.val(), AadharNumber: AadharNumber.val() }), // If you have parameters
                        contentType: "application/json; charset=utf-8",
                        success: function (data) {

                        },
                        error: function (jqXHR, textStatus, errorThrown) {
                            // Handle AJAX error
                            //   alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);

                            Swal.fire({
                                icon: 'error',
                                title: 'Oops...',
                                text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                            })

                        }

                    }).done(function () {
                        Swal.fire({
                            icon: 'success',
                            title: 'Message',
                            text: 'Application Issue Successfully Saved !',

                        }).then((result) => {
                            if (result.isConfirmed) {
                                // OK button clicked

                                location.href = 'ApplicationIssue_List.aspx';
                                // Your code here
                            }
                        });

                    });


                }
            }
            else {
                // User clicked "Cancel", do nothing
                alert('Action was cancelled.');
            }



        }

        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }

        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }


        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }



    </script>
</asp:Content>

