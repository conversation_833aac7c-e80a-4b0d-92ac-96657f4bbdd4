/* Copyright (c) Business Objects 2006. All rights reserved. */

// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "Dnes";
var L_January   = "Janu\u00E1r";
var L_February  = "Febru\u00E1r";
var L_March     = "Marec";
var L_April     = "Apr\u00EDl";
var L_May       = "M\u00E1j";
var L_June      = "J\u00FAn";
var L_July      = "J\u00FAl";
var L_August    = "August";
var L_September = "September";
var L_October   = "Okt\u00F3ber";
var L_November  = "November";
var L_December  = "December";
var L_Su        = "Ne";
var L_Mo        = "Po";
var L_Tu        = "Ut";
var L_We        = "St";
var L_Th        = "\u0160t";
var L_Fr        = "Pi";
var L_Sa        = "So";

// Strings for prompts.js and prompts_param.js
var L_YYYY          = "rrrr";
var L_MM            = "mm";
var L_DD            = "dd";
var L_BadNumber     = "Tento parameter je typu \"\u010C\u00EDslo\" a m\u00F4\u017Ee obsahova\u0165 len z\u00E1porn\u00E9 znamienko, \u010D\u00EDslice (\"0-9\"), odde\u013Eova\u010De skup\u00EDn \u010D\u00EDslic alebo odde\u013Eova\u010D desatinn\u00FDch miest. Opravte zadan\u00FA hodnotu parametra.";
var L_BadCurrency   = "Tento parameter je typu \"Mena\" a m\u00F4\u017Ee obsahova\u0165 len z\u00E1porn\u00E9 znamienko, \u010D\u00EDslice (\"0-9\"), odde\u013Eova\u010De skup\u00EDn \u010D\u00EDslic alebo odde\u013Eova\u010D desatinn\u00FDch miest. Opravte zadan\u00FA hodnotu parametra.";
var L_BadDate       = "Tento parameter je typu \"D\u00E1tum\" a mal by by\u0165 vo form\u00E1te \"%1\", kde \"rrrr\" je \u0161tvorcifern\u00FD rok, \"mm\" je mesiac (napr. janu\u00E1r = 1) a \"dd\" je de\u0148 v mesiaci.";
var L_BadDateTime   = "Typ tohto parametra je \"D\u00E1tum a \u010Das\" a spr\u00E1vny form\u00E1t je \"%1 hh:mm:ss\". \"rrrr\" je \u0161tvorcifern\u00FD rok, \"mm\" je mesiac (napr\u00EDklad janu\u00E1r = 1), \"dd\" je de\u0148 v mesiaci, \"hh\" s\u00FA hodiny v 24-hodinovom re\u017Eime, \"mm\" s\u00FA min\u00FAty a \"ss\" sekundy.";
var L_BadTime       = "Tento parameter je typu \"\u010Cas\" a mal by by\u0165 vo form\u00E1te \"hh:mm:ss\", kde \"hh\" s\u00FA hodiny v 24-hodinovom re\u017Eime, \"mm\" s\u00FA min\u00FAty a \"ss\" s\u00FA sekundy.";
var L_NoValue       = "\u017Diadna hodnota";
var L_BadValue      = "Ak chcete nastavi\u0165 \"\u017Diadna hodnota\", mus\u00EDte nastavi\u0165 hodnoty Od aj Do na \"\u017Diadna hodnota\".";
var L_BadBound      = "\"Bez dolnej hranice\" nem\u00F4\u017Eete nastavi\u0165 spolu s \"Bez hornej hranice\".";
var L_NoValueAlready = "Tento parameter je u\u017E nastaven\u00FD na \"\u017Diadna hodnota\". Pred pridan\u00EDm \u010Fal\u0161\u00EDch hodn\u00F4t odstr\u00E1\u0148te \"\u017Diadna hodnota\".";
var L_RangeError    = "Za\u010Diatok rozsahu nem\u00F4\u017Ee by\u0165 v\u00E4\u010D\u0161\u00ED ako koniec rozsahu.";
var L_NoDateEntered = "Mus\u00EDte zada\u0165 d\u00E1tum.";
var L_Empty         = "Zadajte hodnotu.";

// Strings for filter dialog
var L_closeDialog="Zavrie\u0165 okno";

var L_SetFilter = "Nastavi\u0165 filter";
var L_OK        = "OK";
var L_Cancel    = "Zru\u0161i\u0165";

 /* Crystal Decisions Confidential Proprietary Information */
