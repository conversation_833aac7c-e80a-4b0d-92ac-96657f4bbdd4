//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CorpNet_To_KSDC_SMART
{
    using System;
    
    public partial class Select_All_tbl_LoanApp_By_Status_Disbursement_Result
    {
        public decimal int_loanappid { get; set; }
        public Nullable<int> Loan_App_Issue_Id { get; set; }
        public string vchr_appreceivregno { get; set; }
        public Nullable<decimal> int_no { get; set; }
        public string vchr_fund { get; set; }
        public Nullable<int> int_schemeid { get; set; }
        public string vchr_applname { get; set; }
        public string vchr_hsename { get; set; }
        public string vchr_place1 { get; set; }
        public string vchr_place2 { get; set; }
        public string vchr_post { get; set; }
        public Nullable<int> int_pincode { get; set; }
        public string vchr_phno { get; set; }
        public string vchr_fathername { get; set; }
        public string vchr_spousename { get; set; }
        public string vchr_caste { get; set; }
        public string SubCast { get; set; }
        public string vchr_religion { get; set; }
        public string vchr_sex { get; set; }
        public Nullable<System.DateTime> dte_dob { get; set; }
        public Nullable<int> Age { get; set; }
        public string vchr_village { get; set; }
        public string vchr_taluk { get; set; }
        public string vchr_district { get; set; }
        public string vchr_subdistrict { get; set; }
        public string vchr_locality { get; set; }
        public string vchr_panchayat { get; set; }
        public Nullable<decimal> int_anninc { get; set; }
        public string vchr_ration { get; set; }
        public string vchr_idtype { get; set; }
        public string vchr_other { get; set; }
        public string vchr_idno { get; set; }
        public Nullable<System.DateTime> dte_doappl { get; set; }
        public Nullable<decimal> int_loanamt_req { get; set; }
        public string int_loanno { get; set; }
        public string vchr_orderno { get; set; }
        public Nullable<int> int_app_rej { get; set; }
        public Nullable<int> int_surety_app_rej { get; set; }
        public string vchr_remark { get; set; }
        public Nullable<decimal> int_amtsanction { get; set; }
        public Nullable<System.DateTime> dte_agrement_date { get; set; }
        public string chr_status { get; set; }
        public string vchr_suretytype { get; set; }
        public string vchr_forwardremark { get; set; }
        public string vchr_offid { get; set; }
        public string vchr_oldno { get; set; }
        public Nullable<System.DateTime> dte_dosanc { get; set; }
        public Nullable<decimal> int_AgrChk { get; set; }
        public string vchr_AgrChkRem { get; set; }
        public string vchr_offidC { get; set; }
        public decimal int_bcdc { get; set; }
        public string vchr_phno2 { get; set; }
        public string vchr_Aadhaar { get; set; }
        public string vchr_return { get; set; }
        public string vchr_Ashwas { get; set; }
        public string Vchr_Qualification { get; set; }
        public string vchr_Subject { get; set; }
        public Nullable<int> Block_Id { get; set; }
        public Nullable<System.DateTime> Submitted_Date { get; set; }
        public string Dustrict_Name { get; set; }
        public string Submitted_Date_Formated { get; set; }
        public string Scheme { get; set; }
        public string Cast { get; set; }
        public string SubCast_Name { get; set; }
        public Nullable<int> int_instalNo { get; set; }
        public Nullable<int> int_inst_disbursed { get; set; }
    }
}
