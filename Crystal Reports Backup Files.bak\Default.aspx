﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Default.aspx.cs" Inherits="KSDCSCST_Portal._Default" %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>KSDC</title>

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->

    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- icheck bootstrap -->
    <link rel="stylesheet" href="assets/plugins/icheck-bootstrap/icheck-bootstrap.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">
    <link rel="stylesheet" href="assets/css/main.css">
    
    <link rel="icon" href="assets/img/fav_32.png" sizes="32x32" />
    <link rel="icon" href="assets/img/fav_192.png" sizes="192x192" />
</head>
<body class="hold-transition login-page">
    <div class="login-box">
        <div class="login-logo">
            <a href="#">
                <img src="assets/img/logo.png" />
            </a>
        </div>
        <!-- /.login-logo -->
        <div class="card">
            <div class="card-body login-card-body">
                <p class="login-box-msg">Sign in to start your session</p>


                <div class="input-group mb-3">
                    <input type="text" id="txtUserName" class="form-control" placeholder="Username">
                    <div class="input-group-append">
                        <div class="input-group-text">
                            <span class="fas fa-user"></span>
                        </div>
                    </div>
                </div>
                <div class="input-group mb-3">
                    <input type="password" id="txtPassword" class="form-control" placeholder="Password">
                    <div class="input-group-append">
                        <div class="input-group-text">
                            <span class="fas fa-lock"></span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div class="icheck-primary">
                            <input type="checkbox" id="remember">
                            <label for="remember">
                                Remember Me
             
                            </label>
                        </div>
                    </div>
                    <!-- /.col -->
                    <div class="col-6">
                        <a onclick="SignIn(this);" class="btn btn-primary btn-block btntheme">Sign In</a>

                    </div>
                    <!-- /.col -->
                </div>



            </div>
            <!-- /.login-card-body -->
        </div>
    </div>
    <!-- /.login-box -->

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
      <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
      
        function SignIn(This_Val) {
           
         //   if ($("#txtUserName").val().trim() == "admin" && $("#txtPassword").val().trim() == "admin") {
         //       location.href = "Personal_Ledger_Search.aspx"
         //   }
         //   else {
         //       Swal.fire({
         //           icon: 'error',
         //           title: 'Oops...',
         //           text: 'Invalid username or password !',
         //
         //       })
         //
         //   }
            $(This_Val).html("Please Wait !");
            $(This_Val).attr("disable", "disabled");
            

            $.ajax({
                type: "POST", // or "GET" depending on your web method
                url: "WebService.asmx/Login",
                data: JSON.stringify({ Username: $("#txtUserName").val().trim(), Password: $("#txtPassword").val().trim() }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (response) {

                    // Handle the successful response from the web method
                   // console.log(response.d); // "d" is the default property name for the response
                    if (response.d.length != 0) {
                        location.href = "Welcome.aspx"
                    }
                    else {
                        //

                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Due to Migration , our website may be fully or partially inaccessible from 29-06-2024 to 31-07-2024. We appreciate your patience during this time!',

                        })

                        //Swal.fire({
                        //    icon: 'error',
                        //    title: 'Oops...',
                        //    text: 'Invalid username or password !',

                        //})
                    }
                     

                },
                error: function (xhr, status, error) {
                    // Handle the error response

                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                },
                done: function (response) {
                    // Handle the error response
                    alert(response);

                }
            });

             
        }
        $(function () {
          //  Calculate_EMI()
        });

        function Calculate_EMI() {
            


            var interestRate =6 / 1200; // Monthly interest rate
            var numberOfPayments = parseInt(60);  // Number of payments (months)


            var monthlyPayment = calculatePMT(interestRate, numberOfPayments, 37000);
            alert(monthlyPayment);
            monthlyPayment = Math.ceil(monthlyPayment / 50) * 50;
           alert(monthlyPayment);

        }
        function calculatePMT(rate, nper, pv) {
            var pmt = (pv * rate) / (1 - Math.pow(1 + rate, -nper));
            return pmt;
        }
    </script>


</body>
</html>
