<!--
=============================================================
WebIntelligence(r) Report Panel
Copyright(c) 2001-2003 Business Objects S.A.
All rights reserved

Use and support of this software is governed by the terms
and conditions of the software license agreement and support
policy of Business Objects S.A. and/or its subsidiaries. 
The Business Objects products and technology are protected
by the US patent number 5,555,403 and 6,247,008

File: basicDialog.html
=============================================================
-->

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>Basic events window</title>
			
	

		<script>
		
		
			function CB1(e)
			{
				alert("CB1")
								
				document.removeEventListener("keydown", CB2, false)
			}
		
			function CB2()
			{
				alert("CB2")
			}
		
			function loadCB()
			{
				document.addEventListener("keydown", CB1, false)
				
				document.addEventListener("keydown", CB2, false)
				
			}
		
		</script>

	</head>

	<body class="dialogzone" onload="setTimeout('loadCB()',1)">	
	
	
		this is a sample text
	
	
		<!--<iframe src="http://www.ulead.com/ma/samples/slider-1/slider-1.htm" width="500" height"=300"></iframe>-->

	
	</body>
	
</html>