// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "\uc624\ub298";
var L_January   = "1\uc6d4";
var L_February  = "2\uc6d4";
var L_March     = "3\uc6d4";
var L_April     = "4\uc6d4";
var L_May       = "5\uc6d4";
var L_June      = "6\uc6d4";
var L_July      = "7\uc6d4";
var L_August    = "8\uc6d4";
var L_September = "9\uc6d4";
var L_October   = "10\uc6d4";
var L_November  = "11\uc6d4";
var L_December  = "12\uc6d4";
var L_Su        = "\uc77c";
var L_Mo        = "\uc6d4";
var L_Tu        = "\ud654";
var L_We        = "\uc218";
var L_Th        = "\ubaa9";
var L_Fr        = "\uae08";
var L_Sa        = "\ud1a0";

// strings for dt_param.js
var L_TIME_SEPARATOR = ":";
var L_AM_DESIGNATOR = "\uc624\uc804";
var L_PM_DESIGNATOR = "\uc624\ud6c4";

// strings for range parameter
var L_FROM = "{0}\ubd80\ud130";
var L_TO = "{0}\uae4c\uc9c0";
var L_AFTER = "{0} \uc774\ud6c4";
var L_BEFORE = "{0} \uc774\uc804";
var L_FROM_TO = "{0}\ubd80\ud130 {1}\uae4c\uc9c0";
var L_FROM_BEFORE = "{0}\ubd80\ud130 {1} \uc774\uc804\uae4c\uc9c0";
var L_AFTER_TO = "{0} \uc774\ud6c4\ubd80\ud130 {1}\uae4c\uc9c0";
var L_AFTER_BEFORE = "{0} \uc774\ud6c4\ubd80\ud130 {1} \uc774\uc804\uae4c\uc9c0";

// Strings for prompts.js and prompts_param.js
var L_BadNumber		= "\uc774 \ub9e4\uac1c \ubcc0\uc218\uc758 \ud615\uc2dd\uc740 \"\uc22b\uc790\"\uc774\uace0 \uc74c\uc218 \ubd80\ud638, \uc22b\uc790(\"0-9\"), \uc22b\uc790 \uadf8\ub8f9 \uae30\ud638 \ub610\ub294 \uc18c\uc218\uc810 \uae30\ud638\ub9cc \uc0ac\uc6a9\ud560 \uc218 \uc788\uc2b5\ub2c8\ub2e4. \uc785\ub825\ud55c \ub9e4\uac1c \ubcc0\uc218 \uac12\uc744 \uc218\uc815\ud558\uc2ed\uc2dc\uc624.";
var L_BadCurrency	= "\uc774 \ub9e4\uac1c \ubcc0\uc218\uc758 \ud615\uc2dd\uc740 \"\ud1b5\ud654\"\uc774\uace0 \uc74c\uc218 \ubd80\ud638, \uc22b\uc790(\"0-9\"), \uc22b\uc790 \uadf8\ub8f9 \uae30\ud638 \ub610\ub294 \uc18c\uc218\uc810 \uae30\ud638\ub9cc \uc0ac\uc6a9\ud560 \uc218 \uc788\uc2b5\ub2c8\ub2e4. \uc785\ub825\ud55c \ub9e4\uac1c \ubcc0\uc218 \uac12\uc744 \uc218\uc815\ud558\uc2ed\uc2dc\uc624.";
var L_BadDate		= "\uc774 \ub9e4\uac1c \ubcc0\uc218\uc758 \ud615\uc2dd\uc740 \"\ub0a0\uc9dc\"\uc774\uace0 \"\ub0a0\uc9dc(yyyy,mm,dd)\" \ud615\uc2dd\uc73c\ub85c \uc9c0\uc815\ud574\uc57c \ud569\ub2c8\ub2e4. \uc5ec\uae30\uc11c \"yyyy\"\ub294 \uc5f0\ub3c4(4\uc790\ub9ac), \"mm\"\uc740 \uc6d4(\uc608: 1\uc6d4 = 1), \"dd\"\ub294 \uc9c0\uc815\ub41c \uc6d4\uc758 \ub0a0\uc9dc\ub97c \ub098\ud0c0\ub0c5\ub2c8\ub2e4.";
var L_BadDateTime   = "\uc774 \ub9e4\uac1c \ubcc0\uc218\uc758 \ud615\uc2dd\uc740 \"\ub0a0\uc9dc \uc2dc\uac04\"\uc774\uace0 \"\ub0a0\uc9dc \uc2dc\uac04(yyyy,mm,dd,hh,mm,ss)\" \ud615\uc2dd\uc73c\ub85c \uc9c0\uc815\ud574\uc57c \ud569\ub2c8\ub2e4. \uc5ec\uae30\uc11c \"yyyy\"\ub294 \uc5f0\ub3c4(4\uc790\ub9ac), \"mm\"\uc740 \uc6d4(\uc608: 1\uc6d4 = 1), \"dd\"\ub294 \ub0a0\uc9dc, \"hh\"\ub294 \uc2dc\uac04(24\uc2dc\uac04\uc81c), \"mm\"\uc740 \ubd84, \"ss\"\ub294 \ucd08\ub97c \ub098\ud0c0\ub0c5\ub2c8\ub2e4.";
var L_BadTime       = "\uc774 \ub9e4\uac1c \ubcc0\uc218\uc758 \ud615\uc2dd\uc740 \"\uc2dc\uac04\"\uc774\uace0 \"\uc2dc\uac04(hh,mm,ss)\" \ud615\uc2dd\uc73c\ub85c \uc9c0\uc815\ud574\uc57c \ud569\ub2c8\ub2e4. \uc5ec\uae30\uc11c \"hh\"\ub294 \uc2dc\uac04(24\uc2dc\uac04\uc81c), \"mm\"\uc740 \ubd84, \"ss\"\ub294 \ucd08\ub97c \ub098\ud0c0\ub0c5\ub2c8\ub2e4.";
var L_NoValue       = "\uac12 \uc5c6\uc74c";
var L_BadValue      = "\"\uac12 \uc5c6\uc74c\"\uc73c\ub85c \uc124\uc815\ud558\ub824\uba74 [\uc2dc\uc791] \ubc0f [\ub05d] \uac12\uc744 \ubaa8\ub450 \"\uac12 \uc5c6\uc74c\"\uc73c\ub85c \uc124\uc815\ud574\uc57c \ud569\ub2c8\ub2e4.";
var L_BadBound      = "\"\ud558\ud55c \uc5c6\uc74c\"\uacfc \"\uc0c1\ud55c \uc5c6\uc74c\"\uc744 \ud568\uaed8 \uc124\uc815\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.";
var L_NoValueAlready = "\uc774 \ub9e4\uac1c \ubcc0\uc218\ub294 \uc774\ubbf8 \"\uac12 \uc5c6\uc74c\"\uc73c\ub85c \uc124\uc815\ub418\uc5b4 \uc788\uc2b5\ub2c8\ub2e4. \ub2e4\ub978 \uac12\uc744 \ucd94\uac00\ud558\ub824\uba74 \uba3c\uc800 \"\uac12 \uc5c6\uc74c\"\uc744 \uc81c\uac70\ud558\uc2ed\uc2dc\uc624.";
var L_RangeError    = "\ubc94\uc704\uc758 \uc2dc\uc791 \uac12\uc740 \ubc94\uc704\uc758 \ub05d \uac12\ubcf4\ub2e4 \ud074 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.";
var L_NoDateEntered = "\ub0a0\uc9dc\ub97c \uc785\ub825\ud574\uc57c \ud569\ub2c8\ub2e4.";

// Strings for ../html/crystalexportdialog.htm
var L_ExportOptions     = "\ub0b4\ubcf4\ub0b4\uae30 \uc635\uc158";
var L_PrintOptions      = "\uc778\uc1c4 \uc635\uc158";
var L_PrintPageTitle    = "\ubcf4\uace0\uc11c \uc778\uc1c4";
var L_ExportPageTitle   = "\ubcf4\uace0\uc11c \ub0b4\ubcf4\ub0b4\uae30";
var L_OK                = "\ud655\uc778";
var L_PrintPageRange    = "\uc778\uc1c4\ud560 \ud398\uc774\uc9c0 \ubc94\uc704\ub97c \uc785\ub825\ud558\uc2ed\uc2dc\uc624.";
var L_ExportPageRange   = "\ub0b4\ubcf4\ub0bc \ud398\uc774\uc9c0 \ubc94\uc704\ub97c \uc785\ub825\ud558\uc2ed\uc2dc\uc624.";
var L_InvalidPageRange  = "\ud398\uc774\uc9c0 \ubc94\uc704 \uac12\uc774 \uc62c\ubc14\ub974\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4. \uc62c\ubc14\ub978 \ud398\uc774\uc9c0 \ubc94\uc704\ub97c \uc785\ub825\ud558\uc2ed\uc2dc\uc624.";
var L_ExportFormat      = "\ubaa9\ub85d\uc5d0\uc11c \ub0b4\ubcf4\ub0b4\uae30 \ud615\uc2dd\uc744 \uc120\ud0dd\ud558\uc2ed\uc2dc\uc624.";
var L_Formats           = "\ud615\uc2dd:";
var L_All               = "\ubaa8\ub450";
var L_Pages             = "\ud398\uc774\uc9c0";
var L_From              = "\uc2dc\uc791:";
var L_To                = "\ub05d:";
var L_PrintStep0        = "\uc778\uc1c4\ud558\ub824\uba74";
var L_PrintStep1        = "1.  \ub2e4\uc74c \ub300\ud654 \uc0c1\uc790\uc5d0\uc11c \"\uc774 \ud30c\uc77c \uc5f4\uae30\" \uc635\uc158\uc744 \uc120\ud0dd\ud558\uace0 [\ud655\uc778] \ub2e8\ucd94\ub97c \ud074\ub9ad\ud569\ub2c8\ub2e4.";
var L_PrintStep2        = "2.  \uc778\ud130\ub137 \ube0c\ub77c\uc6b0\uc800\uc758 \uc778\uc1c4 \ub2e8\ucd94 \ub300\uc2e0 Acrobat Reader \uba54\ub274\uc5d0\uc11c \ud504\ub9b0\ud130 \uc544\uc774\ucf58\uc744 \ud074\ub9ad\ud569\ub2c8\ub2e4.";
var L_RTFFormat         = "\uc11c\uc2dd \uc788\ub294 \ud14d\uc2a4\ud2b8";
var L_AcrobatFormat     = "Acrobat \ud615\uc2dd(PDF)";
var L_CrystalRptFormat  = "Crystal Reports(RPT)";
var L_WordFormat        = "MS Word";
var L_ExcelFormat       = "MS Excel 97-2000";
var L_ExcelRecordFormat = "MS Excel 97-2000(\ub370\uc774\ud130\ub9cc)";
if(typeof(Sys)!=='undefined')Sys.Application.notifyScriptLoaded();
