﻿
using ClosedXML.Excel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;



namespace CorpNet_To_KSDC_SMART
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        string Log_folderPath = string.Empty;
        string Current_Date_Folder_Path = string.Empty;
        string Log_File_Name = string.Empty;
        string Folder_FilePath = string.Empty;

        string Dist_Code = string.Empty;
        string Dist_Code_No = string.Empty;
        string District_Id = string.Empty;
        string Office_Code = string.Empty;

        string folderPath = string.Empty;

        private ObservableCollection<string> comboBoxItems;
        public MainWindow()
        {
            InitializeComponent();


            comboBoxItems = new ObservableCollection<string>();
            dropDistrict.ItemsSource = PopulateComboBox(comboBoxItems);

            // Call a method to populate the ComboBox initially

        }


        public DataTable ReadExcelFileToDataTable(string filePath, string Table_Name_Split)
        {
            DataTable dt_1 = new DataTable();
            try
            {
                using (XLWorkbook workBook = new XLWorkbook(filePath))
                {
                    //Read the first Sheet from Excel file.
                    IXLWorksheet workSheet = workBook.Worksheet(1);

                    //Create a new DataTable.


                    //Loop through the Worksheet rows.
                    bool firstRow = true;
                    foreach (IXLRow row in workSheet.Rows())
                    {
                        //Use the first row to add columns to DataTable.
                        if (firstRow)
                        {
                            foreach (IXLCell cell in row.Cells())
                            {
                                dt_1.Columns.Add(cell.Value.ToString());
                            }
                            firstRow = false;
                        }
                        else
                        {
                            //Add rows to DataTable.
                            dt_1.Rows.Add();
                            int i = 0;
                            int Max_Length = dt_1.Columns.Count + 1;
                            for (int j = 1; j < Max_Length; j++)
                            {
                                try
                                {
                                    if (Table_Name_Split == "ACCHD")
                                    {
                                        if (j == 1 || j == 11 || j == 12 || j == 13)
                                        {

                                            if (row.Cell(j).Value.ToString().Trim() == "")
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = 0;

                                            }
                                            else
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                            }
                                        }
                                        else
                                        {
                                            dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                        }
                                    }
                                    else if (Table_Name_Split == "INTERESTRATES")
                                    {
                                        if (j == 1 || j == 2 || j == 3 || j == 8)
                                        {

                                            if (row.Cell(j).Value.ToString().Trim() == "")
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = 0;

                                            }
                                            else
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                            }
                                        }
                                        else if (j == 4 || j == 5 || j == 6 || j == 7)
                                        {

                                            if (row.Cell(j).Value.ToString().Trim() == "")
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = "01-01-1900";

                                            }
                                            else
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                            }
                                        }
                                        else
                                        {
                                            dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                        }
                                    }
                                    else if (Table_Name_Split == "Lnaccdtls")
                                    {
                                        if (j == 1 || j == 2 || j == 6 || j == 8 || j == 9 || j == 11 || j == 12 || j == 13 || j == 14 || j == 18 || j == 19)
                                        {

                                            if (row.Cell(j).Value.ToString().Trim() == "")
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = 0;

                                            }
                                            else
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                            }
                                        }
                                        else if (j == 4 || j == 16 || j == 22 || j == 20)
                                        {

                                            if (row.Cell(j).Value.ToString().Trim() == "")
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = "01-01-1900";

                                            }
                                            else
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                            }
                                        }
                                        else
                                        {
                                            dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                        }
                                    }
                                    else if (Table_Name_Split == "LoanDisburment")
                                    {
                                        if (j == 1 || j == 2 || j == 3 || j == 6 || j == 8)
                                        {

                                            if (row.Cell(j).Value.ToString().Trim() == "")
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = 0;

                                            }
                                            else
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                            }
                                        }
                                        else if (j == 4)
                                        {

                                            if (row.Cell(j).Value.ToString().Trim() == "")
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = "01-01-1900";

                                            }
                                            else
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                            }
                                        }
                                        else
                                        {
                                            dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                        }
                                    }
                                    else if (Table_Name_Split == "Loanee")
                                    {
                                        if (j == 1 || j == 4 || j == 9 || j == 16 || j == 17 || j == 19 || j == 20 || j == 21 || j == 22 || j == 23 || j == 24)
                                        {

                                            if (row.Cell(j).Value.ToString().Trim() == "")
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = 0;

                                            }
                                            else
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                            }
                                        }
                                        else if (j == 13 || j == 14 || j == 15)
                                        {

                                            if (row.Cell(j).Value.ToString().Trim() == "")
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = "01-01-1900";

                                            }
                                            else
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                            }
                                        }

                                        else
                                        {
                                            dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                        }
                                    }
                                    else if (Table_Name_Split == "LoanRepayment")
                                    {
                                        if (j == 2 || j == 4 || j == 9)
                                        {

                                            if (row.Cell(j).Value.ToString().Trim() == "")
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = 0;

                                            }
                                            else
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                            }
                                        }
                                        else if (j == 1 || j == 8)
                                        {

                                            if (row.Cell(j).Value.ToString().Trim() == "")
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = "01-01-1900";

                                            }
                                            else
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                            }
                                        }
                                        else
                                        {
                                            dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                        }
                                    }
                                    else if (Table_Name_Split == "Loantransacfnlsd")
                                    {
                                        if (j == 2 || j == 3 || j == 4 || j == 5 || j == 7 || j == 9 || j == 14 || j == 15 || j == 16 || j == 17 || j == 18 || j == 19 || j == 20 || j == 21 || j == 22 || j == 23 || j == 24 || j == 25 || j == 26 || j == 27 || j == 28 || j == 29 || j == 30 || j == 31 || j == 32 || j == 33 || j == 34 || j == 35 || j == 36 || j == 37 || j == 38 || j == 39 || j == 40 || j == 41 || j == 42 || j == 43 || j == 44 || j == 45 || j == 46
)
                                        {

                                            if (row.Cell(j).Value.ToString().Trim() == "")
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = 0;

                                            }
                                            else
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                            }
                                        }
                                        else if (j == 1 || j == 6 || j == 10 || j == 13)
                                        {

                                            if (row.Cell(j).Value.ToString().Trim() == "")
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = "01-01-1900";

                                            }
                                            else
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                            }
                                        }
                                        else
                                        {
                                            dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                        }
                                    }
                                    else if (Table_Name_Split == "OtherRcpt")
                                    {
                                        if (j == 1 || j == 2 || j == 3 || j == 6 || j == 7 || j == 8 || j == 9)
                                        {

                                            if (row.Cell(j).Value.ToString().Trim() == "")
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = 0;

                                            }
                                            else
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                            }
                                        }
                                        else if (j == 4)
                                        {

                                            if (row.Cell(j).Value.ToString().Trim() == "")
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = "01-01-1900";

                                            }
                                            else
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                            }
                                        }
                                        else
                                        {
                                            dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                        }
                                    }

                                    else if (Table_Name_Split == "EmpSurety")
                                    {
                                        //dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                        if (j == 8)
                                        {
                                            if (row.Cell(j).Value.ToString().Trim() == "")
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = "01-01-1900";

                                            }
                                            else
                                            {
                                                string DOR = row.Cell(j).Value.ToString();
                                                DOR = DOR.Replace('.', '-');
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = DOR;
                                            }

                                        }
                                        else
                                        {
                                            if (row.Cell(j).Value.ToString().Trim() == "")
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = 0;
                                            }
                                            else
                                            {
                                                dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                            }

                                        }


                                    }
                                    else
                                    {
                                        dt_1.Rows[dt_1.Rows.Count - 1][i] = row.Cell(j).Value;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    throw;
                                    break;
                                }

                                i++;
                            }

                        }


                    }
                }
                return dt_1;
            }
            catch (Exception ex)
            {
                throw;
                Report_Log($"Error->Method Name (ReadExcelFileToDataTable) ->File Path ( '{filePath}' ) Table Split Name ( '{Table_Name_Split}' )->Exception ( '{ex.Message}' ) ");

                return dt_1;

            }


        }

        public void Truncate_Table(string File_Name, string Con)
        {
            try
            {
                using (SqlConnection dbConnection = new SqlConnection(Con))
                {
                    dbConnection.Open();
                    string Query = @"truncate table " + File_Name + ";";
                    using (var command = dbConnection.CreateCommand())
                    {
                        command.CommandText = Query;
                        command.ExecuteReader();
                        dbConnection.Close();
                    }

                }
            }
            catch (Exception ex)
            {
                throw;
                Report_Log($"Error->Method Name (Truncate_Table) Table Name ( '{File_Name}' )->Exception ( '{ex.Message}' ) ");
            }
        }

        public void Insert_Data_Into_SQL_Table(DataTable csvData, string con, string Table_Name)
        {
            try
            {
                using (SqlConnection dbConnection = new SqlConnection(con))
                {
                    dbConnection.Open();
                    using (SqlBulkCopy s = new SqlBulkCopy(dbConnection))
                    {
                        s.DestinationTableName = Table_Name;
                        for (int i = 0; i < csvData.Columns.Count; i++)
                        {
                            s.ColumnMappings.Add(csvData.Columns[i].ColumnName, csvData.Columns[i].ColumnName);
                        }

                        s.WriteToServer(csvData);

                    }
                }
            }
            catch (Exception ex)
            {
                throw;
                Report_Log($"Error->Method Name (Insert_Data_Into_SQL_Table) Table Name ( '{Table_Name}' )->Exception ( '{ex.Message}' ) ");
            }
        }
        public static void Insert_Data_Into_SQL_Table_Loanee_Details(DataTable csvData, string con, string Table_Name)
        {
            using (SqlConnection dbConnection = new SqlConnection(con))
            {
                dbConnection.Open();
                using (SqlBulkCopy s = new SqlBulkCopy(dbConnection))
                {
                    s.DestinationTableName = Table_Name;

                    s.ColumnMappings.Add(csvData.Columns["Loaneeid"].ColumnName, "Loaneeid");
                    s.ColumnMappings.Add(csvData.Columns["Loaneename"].ColumnName, "Loaneename");
                    s.ColumnMappings.Add(csvData.Columns["Loaneeadress"].ColumnName, "Loaneeadress");
                    s.ColumnMappings.Add(csvData.Columns["LnaccNo"].ColumnName, "LnaccNo");
                    s.ColumnMappings.Add(csvData.Columns["Female"].ColumnName, "Female");
                    s.ColumnMappings.Add(csvData.Columns["Caste"].ColumnName, "Caste");
                    s.ColumnMappings.Add(csvData.Columns["SubCaste"].ColumnName, "SubCaste");
                    s.ColumnMappings.Add(csvData.Columns["Block"].ColumnName, "Block");
                    s.ColumnMappings.Add(csvData.Columns["income"].ColumnName, "income");
                    s.ColumnMappings.Add(csvData.Columns["irdp"].ColumnName, "irdp");
                    s.ColumnMappings.Add(csvData.Columns["tobetkn"].ColumnName, "tobetkn");
                    s.ColumnMappings.Add(csvData.Columns["phone"].ColumnName, "phone");
                    s.ColumnMappings.Add(csvData.Columns["CScertiDate"].ColumnName, "CScertiDate");
                    s.ColumnMappings.Add(csvData.Columns["INcertiDate"].ColumnName, "INcertiDate");
                    s.ColumnMappings.Add(csvData.Columns["DND"].ColumnName, "DND");
                    s.ColumnMappings.Add(csvData.Columns["Taluk"].ColumnName, "Taluk");
                    s.ColumnMappings.Add(csvData.Columns["Village"].ColumnName, "Village");
                    s.ColumnMappings.Add(csvData.Columns["LandMark"].ColumnName, "LandMark");
                    s.ColumnMappings.Add(csvData.Columns["Talukid"].ColumnName, "Talukid");
                    s.ColumnMappings.Add(csvData.Columns["AffidavitDate"].ColumnName, "AffidavitDate");
                    s.ColumnMappings.Add(csvData.Columns["VillageID"].ColumnName, "VillageID");
                    s.ColumnMappings.Add(csvData.Columns["DistrictName"].ColumnName, "DistrictName");
                    s.ColumnMappings.Add(csvData.Columns["Transferdate"].ColumnName, "Transferdate");
                    s.ColumnMappings.Add(csvData.Columns["Received"].ColumnName, "Received");


                    s.WriteToServer(csvData);

                }
            }
        }

        public void Report_Log(string Message)
        {

            if (!Directory.Exists(Log_folderPath + "\\" + Current_Date_Folder_Path))
            {
                // If it doesn't exist, create the folder
                try
                {
                    Directory.CreateDirectory(Log_folderPath + "\\" + Current_Date_Folder_Path);

                    WriteToFile(Folder_FilePath, Message);

                }
                catch (Exception ex)
                {
                    throw;
                    // Console.WriteLine($"Error creating folder: {ex.Message}");
                }
            }
            else
            {
                WriteToFile(Folder_FilePath, Message);
            }
        }
        public void WriteToFile(string filePath, string Message)
        {
            try
            {
                // Create or overwrite the text file
                using (StreamWriter writer = new StreamWriter(filePath, true))
                {
                    // Write content to the file
                    writer.WriteLine(DateTime.Now.ToString() + ":->" + Message);

                }

                //Console.WriteLine($"Content written to '{filePath}' successfully.");
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        private async void Button_Click(object sender, RoutedEventArgs e)
        {
            StatusLabel.Content = "Processing...";
            await Task.Run(() =>
            {
                // Your time-consuming code here


                string Con_String = "Data Source =VIVEK; Initial Catalog =KSDC_LIVE_V2; uid =sa; password = *******; Integrated Security = SSPI; Trusted_Connection = False";
                //  string Con_String = "Data Source =SQL5109.site4now.net; Initial Catalog =db_a7a28a_ksdc; uid =db_a7a28a_ksdc_admin; password = ************; Integrated Security = SSPI; Trusted_Connection = False";


                folderPath = @"C:\KSDC_WorkSpace\Corpnet_30_12_2023_All_Tables";

                string filePath = string.Empty;
                string File_Name = string.Empty;


                Log_folderPath = @"C:\KSDC_WorkSpace\Corpnet_30_12_2023_All_Tables\Logs";
                Current_Date_Folder_Path = DateTime.Now.ToString("dd-MM-yyyy");
                Log_File_Name = DateTime.Now.Hour + "_" + DateTime.Now.Minute + "_" + DateTime.Now.Second + ".txt";
                Folder_FilePath = System.IO.Path.Combine(Log_folderPath + "\\" + Current_Date_Folder_Path + "\\", Log_File_Name);


                string Table_Name_Split = string.Empty;
                if (Directory.Exists(folderPath))
                {
                    // Get all files in the folder
                    string[] files = Directory.GetFiles(folderPath);

                    foreach (string file in files)
                    {
                        //  Console.WriteLine(file);
                        File_Name = file.Split('\\')[file.Split('\\').Length - 1].Split('.')[0];
                        Truncate_Table(File_Name, Con_String);
                    }


                    // Display the names of all files
                    foreach (string file in files)
                    {
                        //  Console.WriteLine(file);
                        File_Name = file.Split('\\')[file.Split('\\').Length - 1].Split('.')[0];
                        Table_Name_Split = File_Name.Split('_')[0];


                        if (Table_Name_Split == "ACCHD")
                        {
                            DataTable dataTable = ReadExcelFileToDataTable(file, Table_Name_Split);
                            Insert_Data_Into_SQL_Table(dataTable, Con_String, File_Name);
                        }
                        else if (Table_Name_Split == "INTERESTRATES")
                        {
                            DataTable dataTable = ReadExcelFileToDataTable(file, Table_Name_Split);
                            Insert_Data_Into_SQL_Table(dataTable, Con_String, File_Name);
                        }
                        else if (Table_Name_Split == "Lnaccdtls")
                        {
                            DataTable dataTable = ReadExcelFileToDataTable(file, Table_Name_Split);
                            Insert_Data_Into_SQL_Table(dataTable, Con_String, File_Name);
                        }
                        else if (Table_Name_Split == "LoanDisburment")
                        {
                            DataTable dataTable = ReadExcelFileToDataTable(file, Table_Name_Split);
                            Insert_Data_Into_SQL_Table(dataTable, Con_String, File_Name);
                        }
                        else if (Table_Name_Split == "Loanee")
                        {
                            DataTable dataTable = ReadExcelFileToDataTable(file, Table_Name_Split);
                            Insert_Data_Into_SQL_Table(dataTable, Con_String, File_Name);
                        }
                        else if (Table_Name_Split == "LoanRepayment")
                        {
                            DataTable dataTable = ReadExcelFileToDataTable(file, Table_Name_Split);
                            Insert_Data_Into_SQL_Table(dataTable, Con_String, File_Name);
                        }
                        else if (Table_Name_Split == "Loantransacfnlsd")
                        {
                            DataTable dataTable = ReadExcelFileToDataTable(file, Table_Name_Split);
                            // Insert_Data_Into_SQL_Table(dataTable, Con_String, File_Name);
                        }
                        else if (Table_Name_Split == "OtherRcpt")
                        {
                            DataTable dataTable = ReadExcelFileToDataTable(file, Table_Name_Split);
                            Insert_Data_Into_SQL_Table(dataTable, Con_String, File_Name);
                        }
                        else if (Table_Name_Split == "EmpSurety")
                        {
                            DataTable dataTable = ReadExcelFileToDataTable(file, Table_Name_Split);
                            Insert_Data_Into_SQL_Table(dataTable, Con_String, File_Name);
                        }


                    }
                }
                else
                {
                    Console.WriteLine("The specified folder does not exist.");
                }
            });

            Dispatcher.Invoke(() =>
            {
                // Update UI elements here
                StatusLabel.Content = "DONE";
            });

        }
        private async void btn_Migration_Start_Click(object sender, RoutedEventArgs e)
        {
            StatusLabel_Migration.Content = "Processing...";

            await Task.Run(() =>
            {


                folderPath = @"C:\KSDC_WorkSpace\Corpnet_30_12_2023_All_Tables";

                string filePath = string.Empty;
                string File_Name = string.Empty;


                Log_folderPath = @"C:\KSDC_WorkSpace\Corpnet_30_12_2023_All_Tables\Logs";
                Current_Date_Folder_Path = DateTime.Now.ToString("dd-MM-yyyy");
                Log_File_Name = DateTime.Now.Hour + "_" + DateTime.Now.Minute + "_" + DateTime.Now.Second + ".txt";
                Folder_FilePath = System.IO.Path.Combine(Log_folderPath + "\\" + Current_Date_Folder_Path + "\\", Log_File_Name);



                Data_Migration(Dist_Code, Dist_Code_No, District_Id, Office_Code);
            });

            Dispatcher.Invoke(() =>
            {
                // Update UI elements here
                StatusLabel_Migration.Content = "DONE";
            });
        }

        public string Data_Migration(string Dist_Code, string Dist_Code_No, string District_Id, string Office_Code)
        {
            try
            {
                double PI = 0;
                string remit_date = string.Empty;
                DateTime date_remit_date;

                Transaction_Details Obj_Transaction_Details = new Transaction_Details();

                KSDC.Class_CalcPenal obj_CalcPenal = new KSDC.Class_CalcPenal();


                int Start_Number = 1;
                string Sex = string.Empty;
                string Cast = string.Empty;
                int Sub_Cast = 0;
                string Phone_1 = string.Empty;
                string Phone_2 = string.Empty;
                string Talukid = string.Empty;
                string LoanNumber = string.Empty;
                string OfficeId = string.Empty;

                string Village = string.Empty;
                string Remarks = string.Empty;
                string Reg_No = string.Empty;
                string Status = string.Empty;
                string Chqno = string.Empty;
                string TrType = string.Empty;
                string ChqNo = string.Empty;
                int Reg_No_Cout = 0;


                OfficeId = Office_Code;

                List<Select_All_Lnaaccdtls_Result> objList = new List<Select_All_Lnaaccdtls_Result>();
                using (var entities = new KSDC_Entities())
                {
                    entities.Database.CommandTimeout = 3600;
                    objList = entities.Select_All_Lnaaccdtls(Dist_Code).ToList<Select_All_Lnaaccdtls_Result>();
                    foreach (var obj in objList)
                    {
                        //if (obj.Lnaccid == 7943)
                        //{


                        //  Need to get the last id from Sub District table
                        //  
                        Reg_No_Cout = Reg_No_Cout + 1;
                        Reg_No = Dist_Code + "/" + Reg_No_Cout;
                        // Reg_No = Dist_Code + "/" + obj.Lnaccid;
                        List<Select_All_Loanee_Details_By_Lnaccid_Result> objList_2 = new List<Select_All_Loanee_Details_By_Lnaccid_Result>();
                        List<Select_All_LoanDisburment_By_Lnaccid_Result> objList_3 = new List<Select_All_LoanDisburment_By_Lnaccid_Result>();
                        List<Select_Scheme_By_Id_Result> objList_4 = new List<Select_Scheme_By_Id_Result>();
                        List<Select_All_OtherRcpt_By_Lnaccid_Result> objList_5 = new List<Select_All_OtherRcpt_By_Lnaccid_Result>();
                        List<Select_All_EmpSurety_By_Lnaccid_Result> objList_6 = new List<Select_All_EmpSurety_By_Lnaccid_Result>();

                        List<Migration_Select_All_Transaction_Result> objList_Trans = new List<Migration_Select_All_Transaction_Result>();



                       

                        try
                        {

                            objList_2 = entities.Select_All_Loanee_Details_By_Lnaccid(Convert.ToInt32(obj.Lnaccid), Dist_Code).ToList<Select_All_Loanee_Details_By_Lnaccid_Result>();
                        }
                        catch (Exception ex)
                        {
                            throw;
                            // Handle the error and return an error message
                            var lineNumber = GetLineNumber(ex);
                            Report_Log($"Error->Method Name (Data_Migration)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");
                        }

                        try
                        {

                            objList_3 = entities.Select_All_LoanDisburment_By_Lnaccid(Convert.ToInt32(obj.Lnaccid), Dist_Code).ToList<Select_All_LoanDisburment_By_Lnaccid_Result>();
                        }
                        catch (Exception ex)
                        {
                            throw;
                            // Handle the error and return an error message
                            var lineNumber = GetLineNumber(ex);
                            Report_Log($"Error->Method Name (Data_Migration)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");
                        }

                        // Getting ERROR -VIVEK NEED TO CORRECT
                        try
                        {

                            objList_4 = entities.Select_Scheme_By_Id(Dist_Code, Convert.ToInt32(obj.Acccode)).ToList<Select_Scheme_By_Id_Result>();
                        }
                        catch (Exception ex)
                        {
                            throw;
                            // Handle the error and return an error message
                            var lineNumber = GetLineNumber(ex);
                            Report_Log($"Error->Method Name (Data_Migration)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");
                        }


                        try
                        {

                            objList_5 = entities.Select_All_OtherRcpt_By_Lnaccid(Convert.ToInt32(obj.Lnaccid), Dist_Code).ToList<Select_All_OtherRcpt_By_Lnaccid_Result>();

                        }
                        catch (Exception ex)
                        {
                            throw;
                            // Handle the error and return an error message
                            var lineNumber = GetLineNumber(ex);
                            Report_Log($"Error->Method Name (Data_Migration)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");
                        }

                        try
                        {

                            objList_6 = entities.Select_All_EmpSurety_By_Lnaccid(Convert.ToInt32(obj.Lnaccid), Dist_Code).ToList<Select_All_EmpSurety_By_Lnaccid_Result>();

                        }
                        catch (Exception ex)
                        {
                            throw;
                            // Handle the error and return an error message
                            var lineNumber = GetLineNumber(ex);
                            Report_Log($"Error->Method Name (Data_Migration)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");
                        }



                        List<Select_Year_End_Record_Result> Obj_Year_End_List = new List<Select_Year_End_Record_Result>();



                        if (objList_2.Count == 1)
                        {



                            if (objList_2[0].Female == true)
                            {
                                Sex = "F";
                            }
                            else
                            {
                                Sex = "M";
                            }
                            if (objList_2[0].Caste == "SC")
                            {
                                Cast = "1";

                            }
                            else if (objList_2[0].Caste == "ST")
                            {
                                Cast = "2";
                            }
                            else
                            {
                                Cast = "0";
                            }

                            List<Select_Sub_Caste_Details_By_Sub_Caste_Name_Result> objList_Sub_Cast = new List<Select_Sub_Caste_Details_By_Sub_Caste_Name_Result>();
                            try
                            {
                                objList_Sub_Cast = Select_Sub_Caste_Details_By_Sub_Caste_Name(objList_2[0].SubCaste).ToList<Select_Sub_Caste_Details_By_Sub_Caste_Name_Result>();
                            }
                            catch (Exception ex)
                            {
                                throw;
                                // Handle the error and return an error message
                                var lineNumber = GetLineNumber(ex);
                                Report_Log($"Error->Method Name (Data_Migration)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");
                            }
                            if (objList_Sub_Cast.Count == 0)
                            {
                                Sub_Cast = 0;
                            }
                            else
                            {
                                Sub_Cast = objList_Sub_Cast[0].Id;
                            }



                            if (objList_2[0].phone != null)
                            {
                                Phone_1 = objList_2[0].phone.Substring(0, Math.Min(objList_2[0].phone.Length, 10));
                                if (objList_2[0].phone.Length > 10)
                                {
                                    Phone_2 = objList_2[0].phone.Substring(10);
                                }
                                else
                                {
                                    Phone_2 = "";
                                }
                            }
                            else
                            {
                                Phone_1 = "";
                                Phone_2 = "";
                            }

                            if (objList_2[0].Talukid == null)
                            {
                                Talukid = "";
                            }
                            else
                            {
                                Talukid = Convert.ToString(objList_2[0].Talukid);
                            }


                            if (objList_2[0].Village == null)
                            {
                                Village = "";
                            }
                            else
                            {
                                Village = objList_2[0].Village;
                            }

                            if (obj.oldLnagrno == null)
                            {
                                Remarks = "";
                            }
                            else
                            {
                                Remarks = obj.oldLnagrno;
                            }
                            if (obj.closed == true)
                            {
                                Status = "Loan Closed";
                            }
                            else
                            {
                                Status = "Loan Disbursed";
                            }



                            double? int_prin_dues = 0;

                            string formattedNumber = Start_Number.ToString("D5");
                            LoanNumber = OfficeId + formattedNumber;
                            if (LoanNumber == "101100006")
                            {
                                string A = "";
                            }
                            Start_Number = Start_Number + 1;
                            //if (LoanNumber == "140101150")
                            //{
                            //------Insert To tbl_loanapp
                            //if (objList_2[0].Loaneename == "Jayakumar.T")
                            //{

                            try
                            {
                                entities.Insert_To_tbl_loanapp(Reg_No,
                            Convert.ToDecimal(Reg_No_Cout),
                            obj.Finagncy,
                            objList_2[0].Loaneename,
                            objList_2[0].Loaneeadress,
                            Phone_1,
                            Cast,
                            "",
                            Sex,
                            Village,
                            Talukid,
                            District_Id,
                            Convert.ToDecimal(objList_2[0].income),
                            Remarks,
                            obj.Lnaccno,
                            Phone_2,
                            LoanNumber,
                            OfficeId,
                            obj.Agrdate,
                            Convert.ToInt32(obj.Acccode),
                            Convert.ToInt32(obj.Block),
                            Status,
                            Sub_Cast,
                           Convert.ToInt32(objList_2[0].LnaccNo));
                            }
                            catch (Exception ex)
                            {
                                // Handle the error and return an error message

                                throw;
                                var lineNumber = GetLineNumber(ex);
                                Report_Log($"Error->Method Name (Insert_To_tbl_loanapp)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");

                            }


                            // ---- Insetr to Land Surety
                            List<Select_LoanAppId_By_LoanNo_Result> Obj_Select_LoanAppId_By_LoanNo = new List<Select_LoanAppId_By_LoanNo_Result>();
                            try
                            {

                                Obj_Select_LoanAppId_By_LoanNo = entities.Select_LoanAppId_By_LoanNo(LoanNumber).ToList<Select_LoanAppId_By_LoanNo_Result>();
                            }
                            catch (Exception ex)
                            {
                                throw;
                                var lineNumber = GetLineNumber(ex);
                                Report_Log($"Error->Method Name (Select_LoanAppId_By_LoanNo)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");

                            }
                            foreach (var obj_6 in objList_6)
                            {
                                // Insert Land Surety
                                if (obj_6.Design == "0")
                                {
                                    try
                                    {
                                        DateTime Certidate;

                                        if (obj_6.Certidate == "0")
                                        {
                                            Certidate = new DateTime(1900, 1, 1);
                                        }
                                        else
                                        {
                                            try
                                            {
                                                Certidate = Convert.ToDateTime(obj_6.Certidate);
                                            }
                                            catch
                                            {

                                            }
                                            finally
                                            {
                                                Certidate = new DateTime(1900, 1, 1);
                                            }
                                        }
                                        entities.Insert_To_tbl_landsurety(
                                            Obj_Select_LoanAppId_By_LoanNo[0].int_loanappid,
                                            Reg_No,
                                            obj_6.Name,
                                            obj_6.HomeAddrpermanent,
                                            "",
                                            "",
                                            "",
                                            0,
                                            obj_6.MobileNo,
                                            "",
                                            "",
                                            obj_6.subregi,
                                            obj_6.synum,
                                            obj_6.area,
                                            "",
                                            obj_6.Village,
                                            obj_6.Taluk,
                                            obj_6.District,
                                            obj_6.Village,
                                            obj_6.Taluk,
                                            obj_6.District,
                                            Convert.ToDateTime("01-01-1900"),
                                            Certidate,
                                            LoanNumber,
                                            obj_6.Guardianname,
                                            obj_6.Docunum,
                                            0,
                                            "",
                                            0,
                                            false,
                                            0
                                        );
                                    }
                                    catch (Exception ex)
                                    {
                                        // Handle the error and return an error message

                                        throw;
                                        var lineNumber = GetLineNumber(ex);
                                        Report_Log($"Error->Method Name (Insert_To_tbl_landsurety)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");

                                    }
                                }
                                else
                                {
                                    // Insetr Employee Surety -- NEED TO DO VIVEK
                                    try
                                    {
                                        DateTime DOR;


                                        try
                                        {
                                            DOR = Convert.ToDateTime(obj_6.DOR);
                                        }
                                        catch
                                        {

                                        }
                                        finally
                                        {
                                            DOR = new DateTime(1900, 1, 1);
                                        }

                                        entities.Insert_To_tbl_empsurety(
                                            Obj_Select_LoanAppId_By_LoanNo[0].int_loanappid,
                                            Reg_No,
                                            obj_6.Name,
                                            "",
                                            obj_6.Spousename,
                                            obj_6.Design,
                                            obj_6.officeaddr,
                                            "",
                                            "",
                                            "",
                                            0,
                                            "",
                                            obj_6.HomeAddrpresent,
                                            "",
                                            "",
                                            "",
                                            "",
                                            obj_6.MobileNo,
                                            obj_6.HomeAddrpermanent,
                                            "",
                                            "",
                                            "",
                                            "",
                                            "",
                                            Convert.ToDateTime("01-01-1900"),
                                            Convert.ToDateTime("01-01-1900"),
                                            DOR,
                                            Convert.ToDecimal(obj_6.Bpay),
                                            Convert.ToDecimal(obj_6.Netsalary),
                                            Convert.ToDecimal(obj_6.grosssalary),
                                            obj_6.Offhead,
                                            "",
                                            "",
                                            "",
                                            "",
                                            "",
                                            "",
                                            "",
                                            "",
                                            "",
                                            Convert.ToBoolean(false),
                                            "",
                                            Convert.ToDateTime("01-01-1900"),
                                            Convert.ToDateTime("01-01-1900"),
                                            LoanNumber,
                                            "",
                                            obj_6.Village,
                                            obj_6.Taluk,
                                            obj_6.District,
                                            "",
                                            "",
                                            "",
                                            "",
                                            "",
                                            "",
                                            "",
                                            0,
                                            "",
                                            Convert.ToBoolean(false),
                                            Convert.ToBoolean(false),
                                            "",
                                            "",
                                            "",
                                            0,
                                            "",
                                            0,
                                            0,
                                            obj_6.payScale,
                                            "",
                                            "",
                                            "",
                                            "",
                                            "",
                                            ""
                                         );
                                    }
                                    catch (Exception ex)
                                    {
                                        // Handle the error and return an error message

                                        throw;
                                        var lineNumber = GetLineNumber(ex);
                                        Report_Log($"Error->Method Name (Insert_To_tbl_landsurety)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");

                                    }
                                }



                            }

                            List<Select_tbl_loanapp_By_Reg_No_Result> obj_New_Loan_No_List = new List<Select_tbl_loanapp_By_Reg_No_Result>();
                            try
                            {
                                obj_New_Loan_No_List = Select_tbl_loanapp_By_Reg_No(Reg_No);
                            }
                            catch (Exception ex)
                            {
                                throw;
                                // Handle the error and return an error message
                                var lineNumber = GetLineNumber(ex);
                                Report_Log($"Error->Method Name (Data_Migration)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");
                            }

                            foreach (var obj_New_Loan_No in obj_New_Loan_No_List)
                            {
                                LoanNumber = obj_New_Loan_No.int_loanno.ToString();
                            }


                            //---------------END-------------------
                            if (objList_3.Count > 0 && objList_4.Count > 0)
                            {
                                decimal Dsbrsmnt_Amount = 0;
                                //------Insert To tbl_loanreg
                                foreach (var obj_3 in objList_3)
                                {
                                    //Dsbrsmnt_Amount = Dsbrsmnt_Amount + Convert.ToDecimal(obj_3.Dsbrsmnt_Amount);
                                }
                                DateTime dt_IR_From = new DateTime();
                                decimal Interest_Perc = 0;
                                foreach (var obj_IR in objList_4)
                                {


                                    if (obj_IR.IRATEFROM != "")
                                    {
                                        dt_IR_From = Convert.ToDateTime(obj_IR.IRATEFROM);
                                    }
                                    if (dt_IR_From <= obj.Agrdate)
                                    {
                                        Interest_Perc = Convert.ToDecimal(obj_IR.IRATE);
                                    }
                                }

                                int RR = 0;
                                int SR = 0;
                                DateTime SR_Date = new DateTime();
                                DateTime RR_Date = new DateTime();
                                string RRCno = string.Empty;

                                if (obj.Status == 1)
                                {
                                    SR = 1;
                                    SR_Date = Convert.ToDateTime(obj.RefDate);
                                }
                                else
                                {
                                    SR = 0;
                                    SR_Date = Convert.ToDateTime("01-01-1900");

                                }
                                if (obj.Status == 2)
                                {
                                    RR = 1;
                                    RR_Date = Convert.ToDateTime(obj.RefDate);
                                }
                                else
                                {
                                    SR = 0;
                                    RR_Date = Convert.ToDateTime("01-01-1900");
                                    RRCno = obj.RRCNO;

                                }
                                try
                                {
                                    entities.Insert_To_tbl_loanreg(Reg_No,
                                    LoanNumber,
                                  Convert.ToInt32(objList_4[0].ACCHDID),
                                      objList_4[0].Abrivation,
                                        objList_4[0].GLHDNAME,
                                          objList_4[0].ACCHDCODE,

                                    objList_4[0].ACCHDNAME,
                                    Convert.ToDecimal(Dsbrsmnt_Amount),
                                    Convert.ToDecimal(Dsbrsmnt_Amount),
                                    obj.Agrdate,
                                    Interest_Perc,
                                    //Convert.ToDecimal(objList_4[0].IRATE),
                                    Convert.ToDecimal(2), // Fixed PI Rate
                                    Convert.ToDecimal(obj.Lnemi),
                                    Convert.ToDecimal(obj.Lnduration),
                                    Convert.ToDecimal(Dsbrsmnt_Amount),
                                    OfficeId,
                                    OfficeId,
                                    objList_3[objList_3.Count - 1].DisbsmntDate,
                                    obj.DueDate,
                                    objList_3.Count,
                                    objList_3.Count, RR, RR_Date, RRCno, SR, SR_Date);
                                }
                                catch (Exception ex)
                                {
                                    throw;
                                    // Handle the error and return an error message
                                    var lineNumber = GetLineNumber(ex);
                                    Report_Log($"Error->Method Name (Insert_To_tbl_loanreg)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");


                                }

                                //---------------END-------------------
                                //------Insert To tbl_loanTrans

                                try
                                {

                                    objList_Trans = entities.Migration_Select_All_Transaction(Dist_Code, objList_2[0].LnaccNo.ToString()).ToList<Migration_Select_All_Transaction_Result>();

                                   


                                }
                                catch (Exception ex)
                                {
                                    throw;
                                    // Handle the error and return an error message
                                    var lineNumber = GetLineNumber(ex);
                                    Report_Log($"Error->Method Name (Data_Migration)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");
                                }


                                bool BC_Flag = false;

                                foreach (var obj_3 in objList_3)
                                {
                                    //------Insert To tbl_disbursement




                                    if (BC_Flag == false)
                                    {

                                        BC_Flag = true;
                                        if (objList_5.Count > 0)
                                        {
                                            foreach (var obj_BC in objList_5)
                                            {
                                                if (obj_BC.OCPrincipal != 0)
                                                {
                                                    if (obj_BC.Chqno == null)
                                                    {
                                                        Chqno = "";
                                                    }
                                                    else
                                                    {
                                                        Chqno = obj_BC.Chqno.ToString();
                                                    }





                                                }
                                            }



                                        }
                                        //    int_prin_dues = Convert.ToDouble(Principal_Out);
                                    }






                                    //---------------END-------------------

                                }

                                decimal BC_Amount = 0;
                                decimal Principal_Out = 0;
                                foreach (var obj_Trans in objList_Trans)
                                {

                                    if (Convert.ToInt32(objList_2[0].LnaccNo) == Convert.ToInt32(obj_Trans.Lnaccid))
                                    {

                                        if (obj_Trans.Status == "BENEFICIARY CONTRIBUTION")
                                        {
                                            try
                                            {
                                                Obj_Transaction_Details = Calculate_Interest_Main(Convert.ToDateTime(obj.DueDate), Convert.ToDecimal(int_prin_dues), LoanNumber, Convert.ToDateTime(obj_Trans.Date), 0, "BENEFICIARY CONTRIBUTION");
                                            }
                                            catch (Exception ex)
                                            {
                                                throw;
                                                // Handle the error and return an error message
                                                var lineNumber = GetLineNumber(ex);
                                                Report_Log($"Error->Method Name (Data_Migration)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");
                                            }



                                            Principal_Out = Convert.ToDecimal(int_prin_dues) - Convert.ToDecimal(obj_Trans.Amount);

                                            try
                                            {
                                                entities.Insert_To_tbl_loanTrans(LoanNumber,
                                                   Chqno,
                                                    Convert.ToDecimal(obj_Trans.Amount * -1),
                                            Principal_Out,
                                                    OfficeId,
                                                    obj_Trans.Date,
                                                    "Cheque",
                                                    "Payment",
                                                     "BENEFICIARY CONTRIBUTION", "",
                                                     Obj_Transaction_Details.PI,
                                                         Obj_Transaction_Details.Principal_Remit,

                                                          Obj_Transaction_Details.Interest_Remit,
                                                          Obj_Transaction_Details.Penal_Remit,
                                                          Obj_Transaction_Details.Interest_Due,
                                                          Obj_Transaction_Details.Penal_Due,
                                                            Obj_Transaction_Details.Interest);

                                                BC_Amount = Convert.ToDecimal(obj_Trans.Amount * -1);
                                            }
                                            catch (Exception ex)
                                            {
                                                // Handle the error and return an error message
                                                throw;
                                                var lineNumber = GetLineNumber(ex);
                                                Report_Log($"Error->Method Name (Insert_To_tbl_loanTrans)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");


                                            }
                                        }
                                        else if (obj_Trans.Status == "SUBSIDY")
                                        {
                                            try
                                            {
                                                Obj_Transaction_Details = Calculate_Interest_Main(Convert.ToDateTime(obj.DueDate), Convert.ToDecimal(int_prin_dues), LoanNumber, Convert.ToDateTime(obj_Trans.Date), 0, "SUBSIDY");
                                            }
                                            catch (Exception ex)
                                            {
                                                throw;
                                                // Handle the error and return an error message
                                                var lineNumber = GetLineNumber(ex);
                                                Report_Log($"Error->Method Name (Data_Migration)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");
                                            }



                                            Principal_Out = Convert.ToDecimal(int_prin_dues) - Convert.ToDecimal(obj_Trans.Amount);

                                            try
                                            {
                                                entities.Insert_To_tbl_loanTrans(LoanNumber,
                                                   Chqno,
                                                    Convert.ToDecimal(obj_Trans.Amount * -1),
                                            Principal_Out,
                                                    OfficeId,
                                                    obj_Trans.Date,
                                                    "Cheque",
                                                    "Payment",
                                                     "SUBSIDY", "",
                                                     Obj_Transaction_Details.PI,
                                                         Obj_Transaction_Details.Principal_Remit,

                                                          Obj_Transaction_Details.Interest_Remit,
                                                          Obj_Transaction_Details.Penal_Remit,
                                                          Obj_Transaction_Details.Interest_Due,
                                                          Obj_Transaction_Details.Penal_Due,
                                                            Obj_Transaction_Details.Interest);

                                                BC_Amount = Convert.ToDecimal(obj_Trans.Amount * -1);
                                            }
                                            catch (Exception ex)
                                            {
                                                // Handle the error and return an error message
                                                throw;
                                                var lineNumber = GetLineNumber(ex);
                                                Report_Log($"Error->Method Name (Insert_To_tbl_loanTrans)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");


                                            }

                                        }
                                        if (obj_Trans.Status == "REPAYMENT")
                                        {


                                            try
                                            {
                                                Obj_Transaction_Details = Calculate_Interest_Main(Convert.ToDateTime(obj.DueDate), 0, LoanNumber, Convert.ToDateTime(obj_Trans.Date), Convert.ToDecimal(obj_Trans.Amount), "REPAYMENT");
                                            }
                                            catch (Exception ex)
                                            {
                                                throw;
                                                // Handle the error and return an error message
                                                var lineNumber = GetLineNumber(ex);
                                                Report_Log($"Error->Method Name (Data_Migration)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");
                                            }
                                            try
                                            {
                                                entities.Insert_To_tbl_loanTrans(LoanNumber,
                                                     ChqNo,
                                                      Convert.ToDecimal(obj_Trans.Amount),
                                                     Obj_Transaction_Details.Principal_Due,
                                                      OfficeId,
                                                      obj_Trans.Date,
                                                       "Cheque",
                                                        "Receipt",
                                                      "REPAYMENT",
                                                   //   Convert.ToString(obj_6.TrNumber),
                                                   "",
                                                       Obj_Transaction_Details.PI,
                                                     Obj_Transaction_Details.Principal_Remit,
                                                     Obj_Transaction_Details.Interest_Remit,
                                                     Obj_Transaction_Details.Penal_Remit,
                                                     Obj_Transaction_Details.Interest_Due,
                                                     Obj_Transaction_Details.Penal_Due,
                                                     Obj_Transaction_Details.Interest);
                                            }
                                            catch (Exception ex)
                                            {
                                                throw;
                                                // Handle the error and return an error message
                                                var lineNumber = GetLineNumber(ex);
                                                Report_Log($"Error->Method Name (Insert_To_tbl_loanTrans)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");


                                            }



                                        }
                                        if (obj_Trans.Status == "LOAN DISBURSED")
                                        {



                                            if (Convert.ToDateTime(obj_Trans.Date).Month > 3 && objList_3[0].DisbsmntDate <= Convert.ToDateTime("31/03/" + Convert.ToDateTime(obj_Trans.Date).Year))
                                            {
                                                Obj_Year_End_List = new List<Select_Year_End_Record_Result>();

                                                try
                                                {
                                                    Obj_Year_End_List = entities.Select_Year_End_Record(LoanNumber, Convert.ToDateTime("31/03/" + Convert.ToDateTime(obj_Trans.Date).Year)).ToList<Select_Year_End_Record_Result>();
                                                }
                                                catch (Exception ex)
                                                {
                                                    throw;
                                                    // Handle the error and return an error message
                                                    var lineNumber = GetLineNumber(ex);
                                                    Report_Log($"Error->Method Name (Data_Migration)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");
                                                }

                                                if (Obj_Year_End_List.Count == 0)
                                                {

                                                    //  double Total_Interest = Calculate_Interest_Main("FINANCIAL YEAR END", 0, Convert.ToDateTime("31/03/" + Convert.ToDateTime(obj_6.Date).Year), obj_LoanReg_List[0].int_rate_int);


                                                    // -------Calcuate Interest


                                                    try
                                                    {
                                                        Obj_Transaction_Details = Calculate_Interest_Main(Convert.ToDateTime(obj.DueDate), 0, LoanNumber, Convert.ToDateTime("31/03/" + Convert.ToDateTime(obj_Trans.Date).Year), 0, "FINANCIAL YEAR END");
                                                    }
                                                    catch (Exception ex)
                                                    {
                                                        throw;
                                                        // Handle the error and return an error message
                                                        var lineNumber = GetLineNumber(ex);
                                                        Report_Log($"Error->Method Name (Data_Migration)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");
                                                    }

                                                    if (objList_3[0].DisbsmntDate < Convert.ToDateTime("31/03/" + Convert.ToDateTime(obj_Trans.Date).Year))
                                                    {
                                                        try
                                                        {
                                                            entities.Insert_To_tbl_loanTrans(LoanNumber,
                                                             "",
                                                               Convert.ToDecimal(0),
                                                               Obj_Transaction_Details.Principal_Due,
                                                              OfficeId,
                                                            Convert.ToDateTime("31/03/" + Convert.ToDateTime(obj_Trans.Date).Year),
                                                               "",
                                                                TrType,
                                                              "FINANCIAL YEAR END", "", Obj_Transaction_Details.PI,
                                                             Obj_Transaction_Details.Principal_Remit,

                                                              Obj_Transaction_Details.Interest_Remit,
                                                              Obj_Transaction_Details.Penal_Remit,
                                                              Obj_Transaction_Details.Interest_Due,
                                                              Obj_Transaction_Details.Penal_Due,
                                                                Obj_Transaction_Details.Interest);
                                                        }
                                                        catch (Exception ex)
                                                        {
                                                            // Handle the error and return an error message
                                                            throw;
                                                            var lineNumber = GetLineNumber(ex);
                                                            Report_Log($"Error->Method Name (Insert_To_tbl_loanTrans)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");


                                                        }

                                                        try
                                                        {

                                                            entities.Insert_To_tbl_disbursement(Reg_No,
                                                                Convert.ToDecimal(obj_Trans.Amount),
                                                                Convert.ToDecimal(obj_Trans.Amount),
                                                                obj_Trans.Date,
                                                              //  Convert.ToString(obj_3.ChqNo),
                                                              "",
                                                                LoanNumber);
                                                        }
                                                        catch (Exception ex)
                                                        {
                                                            // Handle the error and return an error message
                                                            throw;
                                                            var lineNumber = GetLineNumber(ex);
                                                            Report_Log($"Error->Method Name (Insert_To_tbl_disbursement)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");


                                                        }

                                                    }

                                                }
                                                else
                                                {
                                                    try
                                                    {

                                                        entities.Insert_To_tbl_disbursement(Reg_No,
                                                            Convert.ToDecimal(obj_Trans.Amount),
                                                            Convert.ToDecimal(obj_Trans.Amount),
                                                            obj_Trans.Date,
                                                          //  Convert.ToString(obj_3.ChqNo),
                                                          "",
                                                            LoanNumber);
                                                    }
                                                    catch (Exception ex)
                                                    {
                                                        // Handle the error and return an error message
                                                        throw;
                                                        var lineNumber = GetLineNumber(ex);
                                                        Report_Log($"Error->Method Name (Insert_To_tbl_disbursement)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");


                                                    }
                                                }




                                            }
                                            else
                                            {
                                                try
                                                {

                                                    entities.Insert_To_tbl_disbursement(Reg_No,
                                                        Convert.ToDecimal(obj_Trans.Amount),
                                                        Convert.ToDecimal(obj_Trans.Amount),
                                                        obj_Trans.Date,
                                                      //  Convert.ToString(obj_3.ChqNo),
                                                      "",
                                                        LoanNumber);
                                                }
                                                catch (Exception ex)
                                                {
                                                    // Handle the error and return an error message
                                                    throw;
                                                    var lineNumber = GetLineNumber(ex);
                                                    Report_Log($"Error->Method Name (Insert_To_tbl_disbursement)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");


                                                }

                                            }




                                            try
                                            {

                                                Obj_Transaction_Details = Calculate_Interest_Main(Convert.ToDateTime(obj.DueDate), Convert.ToDecimal(int_prin_dues), LoanNumber, Convert.ToDateTime(obj_Trans.Date), 0, "LOAN DISBURSED");
                                            }
                                            catch (Exception ex)
                                            {
                                                throw;
                                                // Handle the error and return an error message
                                                var lineNumber = GetLineNumber(ex);
                                                Report_Log($"Error->Method Name (Data_Migration)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");
                                            }

                                            int_prin_dues = int_prin_dues + obj_Trans.Amount + Convert.ToDouble(BC_Amount);
                                            BC_Amount = 0;

                                            try
                                            {
                                                entities.Insert_To_tbl_loanTrans(LoanNumber,
                                                      // Convert.ToString(obj_3.ChqNo),
                                                      "",
                                                        Convert.ToDecimal(obj_Trans.Amount),
                                                        //  Convert.ToDecimal(int_prin_dues),
                                                        Convert.ToDecimal(int_prin_dues),
                                                        OfficeId,
                                                        obj_Trans.Date,
                                                         "Cheque",
                                                        "Payment",
                                                        "LOAN DISBURSED", "",
                                                        Obj_Transaction_Details.PI,
                                                                     Obj_Transaction_Details.Principal_Remit,

                                                                      Obj_Transaction_Details.Interest_Remit,
                                                                      Obj_Transaction_Details.Penal_Remit,
                                                                      Obj_Transaction_Details.Interest_Due,
                                                                      Obj_Transaction_Details.Penal_Due,
                                                                        Obj_Transaction_Details.Interest);
                                            }
                                            catch (Exception ex)
                                            {
                                                throw;
                                                // Handle the error and return an error message

                                                var lineNumber = GetLineNumber(ex);
                                                Report_Log($"Error->Method Name (Insert_To_tbl_loanTrans)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");


                                            }


                                        }

                                    }
                                }



                                List<Select_All_LoanRepayment_Lnaccid_Result> objList_66 = new List<Select_All_LoanRepayment_Lnaccid_Result>();

                                try
                                {
                                    objList_66 = entities.Select_All_LoanRepayment_Lnaccid(Convert.ToInt32(obj.Lnaccid), Dist_Code).ToList<Select_All_LoanRepayment_Lnaccid_Result>(); ;
                                }
                                catch (Exception ex)
                                {
                                    throw;
                                    // Handle the error and return an error message
                                    var lineNumber = GetLineNumber(ex);
                                    Report_Log($"Error->Method Name (Data_Migration)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");
                                }
                                List<Select_All_tbl_loanreg_By_LoanNo_Result> obj_LoanReg_List = new List<Select_All_tbl_loanreg_By_LoanNo_Result>();
                                try
                                {
                                    obj_LoanReg_List = entities.Select_All_tbl_loanreg_By_LoanNo(LoanNumber).ToList<Select_All_tbl_loanreg_By_LoanNo_Result>();
                                }
                                catch (Exception ex)
                                {
                                    throw;
                                    // Handle the error and return an error message
                                    var lineNumber = GetLineNumber(ex);
                                    Report_Log($"Error->Method Name (Data_Migration)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");
                                }
                                foreach (var obj_6 in objList_66)
                                {



                                    if (obj_6.TrType == null)
                                    {
                                        TrType = "";
                                    }
                                    else
                                    {
                                        TrType = obj_6.TrType.ToString();
                                    }

                                    if (obj_6.ChqNo == null)
                                    {
                                        ChqNo = "";
                                    }
                                    else
                                    {
                                        ChqNo = obj_6.ChqNo.ToString();
                                    }
                                    DateTime loanStartDate = new DateTime(2023, 03, 31);
                                    int Disb_Year = Convert.ToDateTime(objList_3[0].DisbsmntDate).Year;
                                    int Current_Year = Convert.ToDateTime(obj_6.Date).Year;
                                    for (int i = Disb_Year; i <= Current_Year; i++)
                                    {
                                        if (Current_Year != i)
                                        {
                                            Obj_Year_End_List = new List<Select_Year_End_Record_Result>();

                                            Obj_Year_End_List = entities.Select_Year_End_Record(LoanNumber, Convert.ToDateTime("31/03/" + i.ToString())).ToList<Select_Year_End_Record_Result>(); ;
                                            if (Obj_Year_End_List.Count == 0)
                                            {

                                                //  double Total_Interest = Calculate_Interest_Main("FINANCIAL YEAR END", 0, Convert.ToDateTime("31/03/" + Convert.ToDateTime(obj_6.Date).Year), obj_LoanReg_List[0].int_rate_int);


                                                // -------Calcuate Interest


                                                try
                                                {
                                                    Obj_Transaction_Details = Calculate_Interest_Main(Convert.ToDateTime(obj.DueDate), 0, LoanNumber, Convert.ToDateTime("31/03/" + i.ToString()), 0, "FINANCIAL YEAR END");
                                                }
                                                catch (Exception ex)
                                                {
                                                    throw;
                                                    // Handle the error and return an error message
                                                    var lineNumber = GetLineNumber(ex);
                                                    Report_Log($"Error->Method Name (Data_Migration)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");
                                                }

                                                if (objList_3[0].DisbsmntDate < Convert.ToDateTime("31/03/" + i.ToString()))
                                                {

                                                    try
                                                    {
                                                        entities.Insert_To_tbl_loanTrans(LoanNumber,
                                                         "",
                                                           Convert.ToDecimal(0),
                                                           Obj_Transaction_Details.Principal_Due,
                                                          OfficeId,
                                                        Convert.ToDateTime("31/03/" + i.ToString()),
                                                           "",
                                                            TrType,
                                                          "FINANCIAL YEAR END", "", Obj_Transaction_Details.PI,
                                                         Obj_Transaction_Details.Principal_Remit,

                                                          Obj_Transaction_Details.Interest_Remit,
                                                          Obj_Transaction_Details.Penal_Remit,
                                                          Obj_Transaction_Details.Interest_Due,
                                                          Obj_Transaction_Details.Penal_Due,
                                                            Obj_Transaction_Details.Interest);
                                                    }
                                                    catch (Exception ex)
                                                    {
                                                        // Handle the error and return an error message
                                                        throw;
                                                        var lineNumber = GetLineNumber(ex);
                                                        Report_Log($"Error->Method Name (Insert_To_tbl_loanTrans)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");


                                                    }
                                                }



                                            }

                                        }
                                    }



                                }


                            }

                        }


                        // Calculate All Year End to Current Date
                        if (objList_3.Count > 0)
                        {
                            int Disb_Year_Last = Convert.ToDateTime(objList_3[0].DisbsmntDate).Year;
                            int Current_Year_Last = Convert.ToDateTime(DateTime.Now).Year;
                            for (int i = Disb_Year_Last; i <= Current_Year_Last; i++)
                            {

                                Obj_Year_End_List = new List<Select_Year_End_Record_Result>();
                                try
                                {
                                    Obj_Year_End_List = entities.Select_Year_End_Record(LoanNumber, Convert.ToDateTime("31/03/" + i.ToString())).ToList<Select_Year_End_Record_Result>(); ;
                                }
                                catch (Exception ex)
                                {
                                    throw;
                                    Report_Log($"Error->Method Name (Select_Year_End_Record)->Exception ( '{ex.InnerException}' ) ");

                                }

                                if (Obj_Year_End_List.Count == 0)
                                {

                                    //  double Total_Interest = Calculate_Interest_Main("FINANCIAL YEAR END", 0, Convert.ToDateTime("31/03/" + Convert.ToDateTime(obj_6.Date).Year), obj_LoanReg_List[0].int_rate_int);


                                    // -------Calcuate Interest

                                    if (objList_3[0].DisbsmntDate < Convert.ToDateTime("31/03/" + i.ToString()))
                                    {
                                        try
                                        {
                                            Obj_Transaction_Details = Calculate_Interest_Main(Convert.ToDateTime(obj.DueDate), 0, LoanNumber, Convert.ToDateTime("31/03/" + i.ToString()), 0, "FINANCIAL YEAR END");
                                        }
                                        catch (Exception ex)
                                        {
                                            throw;
                                            // Handle the error and return an error message
                                            var lineNumber = GetLineNumber(ex);
                                            Report_Log($"Error->Method Name (Data_Migration)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");
                                        }

                                        try
                                        {
                                            entities.Insert_To_tbl_loanTrans(LoanNumber,
                                                 "",
                                                   Convert.ToDecimal(0),
                                                   Obj_Transaction_Details.Principal_Due,
                                                  OfficeId,
                                                Convert.ToDateTime("31/03/" + i.ToString()),
                                                   "",
                                                    TrType,
                                                  "FINANCIAL YEAR END", "", Obj_Transaction_Details.PI,
                                                 Obj_Transaction_Details.Principal_Remit,

                                                  Obj_Transaction_Details.Interest_Remit,
                                                  Obj_Transaction_Details.Penal_Remit,
                                                  Obj_Transaction_Details.Interest_Due,
                                                  Obj_Transaction_Details.Penal_Due,
                                                    Obj_Transaction_Details.Interest);
                                        }
                                        catch (Exception ex)
                                        {
                                            throw;
                                            // Handle the error and return an error message
                                            var lineNumber = GetLineNumber(ex);
                                            Report_Log($"Error->Method Name (Insert_To_tbl_loanTrans)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");



                                        }

                                    }



                                }


                            }
                        }


                        // Calculate Current Date

                        if (objList_3.Count > 0)
                        {
                            Obj_Year_End_List = new List<Select_Year_End_Record_Result>();
                            try
                            {


                                Obj_Year_End_List = entities.Select_Year_End_Record(LoanNumber, DateTime.Now).ToList<Select_Year_End_Record_Result>(); ;


                            }
                            catch (Exception ex)
                            {
                                throw;
                                Report_Log($"Error->Method Name (Select_Year_End_Record)->Exception ( '{ex.InnerException}' ) ");

                            }
                            if (Obj_Year_End_List.Count == 0)
                            {

                                //  double Total_Interest = Calculate_Interest_Main("FINANCIAL YEAR END", 0, Convert.ToDateTime("31/03/" + Convert.ToDateTime(obj_6.Date).Year), obj_LoanReg_List[0].int_rate_int);


                                // -------Calcuate Interest

                                if (objList_3[0].DisbsmntDate < DateTime.Now)
                                {
                                    try
                                    {
                                        Obj_Transaction_Details = Calculate_Interest_Main(Convert.ToDateTime(obj.DueDate), 0, LoanNumber, DateTime.Now, 0, "CURRENT DATE");
                                    }
                                    catch (Exception ex)
                                    {
                                        throw;
                                        // Handle the error and return an error message
                                        var lineNumber = GetLineNumber(ex);
                                        Report_Log($"Error->Method Name (Data_Migration)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");
                                    }

                                    try
                                    {
                                        entities.Insert_To_tbl_loanTrans(LoanNumber,
                                             "",
                                               Convert.ToDecimal(0),
                                               Obj_Transaction_Details.Principal_Due,
                                              OfficeId,
                                            DateTime.Now,
                                               "",
                                                TrType,
                                              "CURRENT DATE", "", Obj_Transaction_Details.PI,
                                             Obj_Transaction_Details.Principal_Remit,

                                              Obj_Transaction_Details.Interest_Remit,
                                              Obj_Transaction_Details.Penal_Remit,
                                              Obj_Transaction_Details.Interest_Due,
                                              Obj_Transaction_Details.Penal_Due,
                                                Obj_Transaction_Details.Interest);
                                    }
                                    catch (Exception ex)
                                    {
                                        throw;
                                        // Handle the error and return an error message
                                        var lineNumber = GetLineNumber(ex);
                                        Report_Log($"Error->Method Name (Insert_To_tbl_loanTrans)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");


                                    }

                                }



                            }

                        }

                        //                    }
                    }


                    entities.Database.Connection.Close();
                    return "Success";
                }
            }
            catch (Exception ex)
            {
                throw;
                // Handle the error and return an error message
                var lineNumber = GetLineNumber(ex);
                Report_Log($"Error->Method Name (Data_Migration)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");




                return "";
            }

            //---------END---------------------
        }


        public class Transaction_Details
        {
            public decimal? Interest { get; set; }
            public decimal? PI { get; set; }
            public decimal? Principal_Remit { get; set; }
            public decimal? Interest_Remit { get; set; }
            public decimal? Penal_Remit { get; set; }
            public decimal? Principal_Due { get; set; }
            public decimal? Interest_Due { get; set; }
            public decimal? Penal_Due { get; set; }
        }

        public Transaction_Details Calculate_Interest_Main(DateTime DueDate, decimal Out_Princ, string LoanNumber, DateTime ToDate, decimal? int_amt, string Type)
        {
            try
            {
                //---------Calcualte Loan END Date
                decimal Interest_Rate = 0;
                DateTime Loan_End_Date = DueDate.AddMonths(60);

                //


                Transaction_Details obj_Return = new Transaction_Details();
                List<Select_Pricipale_Due_Result> objList_Pricipale_Due = new List<Select_Pricipale_Due_Result>();
                KSDC.Class_CalcPenal obj_CalcPenal = new KSDC.Class_CalcPenal();


                double PI = 0;
                decimal Interest = 0;
                objList_Pricipale_Due = Select_Pricipale_Due(LoanNumber, Convert.ToDateTime(ToDate));


                List<Select_All_tbl_loanreg_By_LoanNo_Result> objList_loanreg_By = new List<Select_All_tbl_loanreg_By_LoanNo_Result>();

                using (var entities = new KSDC_Entities())
                {
                    objList_loanreg_By = entities.Select_All_tbl_loanreg_By_LoanNo(LoanNumber).ToList<Select_All_tbl_loanreg_By_LoanNo_Result>();
                }

                if (objList_loanreg_By.Count > 0)
                {
                    if (Loan_End_Date >= Convert.ToDateTime(ToDate).AddDays(1))
                    {

                        try
                        {
                            PI = Convert.ToDouble(obj_CalcPenal.CalcPenal(Convert.ToDateTime(ToDate).AddDays(1), LoanNumber));
                        }
                        catch (Exception ex)
                        {
                            throw;
                            // Handle the error and return an error message
                            var lineNumber = GetLineNumber(ex);
                            Report_Log($"Error->Method Name (Data_Migration)->Line Number :" + lineNumber + "->Exception ( '{ex.Message}' ) ");


                        }
                        Interest_Rate = Convert.ToDecimal(objList_loanreg_By[0].int_rate_int);
                    }
                    else
                    {
                        PI = 0;
                        Interest_Rate = Convert.ToDecimal(objList_loanreg_By[0].int_rate_int) + Convert.ToDecimal(objList_loanreg_By[0].int_rate_penal);
                    }
                    // if (Type == "REPAYMENT")
                    // {
                    //     PI = Convert.ToDouble(obj_CalcPenal.CalcPenal(Convert.ToDateTime(ToDate).AddDays(1), LoanNumber));
                    //
                    //
                    // }
                    // else
                    // {
                    //     PI = Convert.ToDouble(obj_CalcPenal.CalcPenal(Convert.ToDateTime(ToDate), LoanNumber));
                    // }
                    PI = Math.Round(PI, 2);




                    if (objList_Pricipale_Due.Count != 0 && objList_loanreg_By.Count != 0)
                    {
                        //--Remarks like BENEFICIARY CONTRIBUTION || LOAN DISBURSED || REPAYMENT || FINANCIAL YEAR END
                        if (objList_Pricipale_Due.Count == 1)
                        {
                            if (Type == "LOAN DISBURSED" || Type == "BENEFICIARY CONTRIBUTION" || Type == "SUBSIDY")
                            {
                                Interest = Calculate_Interest(Convert.ToDateTime(objList_Pricipale_Due[0].dt_transaction), ToDate, Out_Princ, Interest_Rate);

                            }
                            else
                            {
                                Interest = Calculate_Interest(Convert.ToDateTime(objList_Pricipale_Due[0].dt_transaction), ToDate, Convert.ToDecimal(objList_loanreg_By[0].mny_loanbal), Interest_Rate);

                            }
                            Interest = Math.Round(Interest, 2);
                            obj_Return.Interest = Interest;
                            obj_Return.PI = Convert.ToDecimal(PI);

                            //------- Calculate All Remits
                            decimal? PI_And_Interest = Convert.ToDecimal(PI) + objList_Pricipale_Due[0].int_penal_dues + Interest + objList_Pricipale_Due[0].int_int_dues;

                            if (int_amt >= PI_And_Interest)
                            {
                                obj_Return.Interest = Interest;
                                obj_Return.Penal_Remit = Convert.ToDecimal(PI) + objList_Pricipale_Due[0].int_penal_dues;
                                obj_Return.Interest_Remit = Interest + objList_Pricipale_Due[0].int_int_dues;
                                PI_And_Interest = obj_Return.Penal_Remit + obj_Return.Interest_Remit;
                                obj_Return.Principal_Remit = int_amt - PI_And_Interest;
                                obj_Return.Principal_Due = Convert.ToDecimal(objList_loanreg_By[0].mny_loanbal) - obj_Return.Principal_Remit;
                                obj_Return.Interest_Due = 0;
                                obj_Return.Penal_Due = 0;



                            }
                            else
                            {
                                if (int_amt >= Convert.ToDecimal(PI) && int_amt != 0)
                                {
                                    obj_Return.Penal_Remit = Convert.ToDecimal(PI) + objList_Pricipale_Due[0].int_penal_dues;
                                    int_amt = int_amt - Convert.ToDecimal(PI);
                                    if (int_amt >= Interest)
                                    {
                                        obj_Return.Interest_Remit = Interest + objList_Pricipale_Due[0].int_int_dues;
                                        int_amt = int_amt - Interest;
                                    }
                                    //  obj_Return.Penal_Remit = int_amt;
                                    obj_Return.Principal_Remit = int_amt - PI_And_Interest;
                                    obj_Return.Principal_Due = Convert.ToDecimal(objList_loanreg_By[0].mny_loanbal) - obj_Return.Principal_Remit;
                                    obj_Return.Interest_Due = Interest + objList_Pricipale_Due[0].int_int_dues;
                                    obj_Return.Penal_Due = Convert.ToDecimal(PI) + objList_Pricipale_Due[0].int_penal_dues;

                                }
                                else
                                {
                                    obj_Return.Interest_Due = objList_Pricipale_Due[0].int_int_dues + Interest;
                                    obj_Return.Penal_Due = objList_Pricipale_Due[0].int_penal_dues + Convert.ToDecimal(PI);
                                    obj_Return.Principal_Due = Convert.ToDecimal(objList_loanreg_By[0].mny_loanbal);

                                    obj_Return.Penal_Remit = 0;
                                    obj_Return.Interest_Remit = 0;
                                    obj_Return.Principal_Remit = 0;


                                }



                            }


                        }
                        else
                        {
                            if (Type == "LOAN DISBURSED" || Type == "BENEFICIARY CONTRIBUTION" || Type == "SUBSIDY")
                            {
                                Interest = Calculate_Interest(Convert.ToDateTime(objList_Pricipale_Due[1].dt_transaction), ToDate, Out_Princ, Interest_Rate);
                            }
                            else
                            {
                                Interest = Calculate_Interest(Convert.ToDateTime(objList_Pricipale_Due[1].dt_transaction), ToDate, Convert.ToDecimal(objList_loanreg_By[0].mny_loanbal), Interest_Rate);
                            }

                            Interest = Math.Round(Interest, 2);
                            obj_Return.Interest = Interest;
                            obj_Return.PI = Convert.ToDecimal(PI);

                            //obj_Return.Interest_Due = Interest + objList_Pricipale_Due[1].int_int_dues;
                            //obj_Return.Principal_Due = objList_Pricipale_Due[1].int_prin_dues + Interest + Convert.ToDecimal(PI);


                            //------- Calculate All Remits
                            decimal? PI_And_Interest = Convert.ToDecimal(PI) + objList_Pricipale_Due[1].int_penal_dues + Interest + objList_Pricipale_Due[1].int_int_dues;

                            if (int_amt >= PI_And_Interest)
                            {
                                obj_Return.Interest = Interest;
                                obj_Return.Penal_Remit = Convert.ToDecimal(PI) + objList_Pricipale_Due[1].int_penal_dues;
                                obj_Return.Interest_Remit = Interest + objList_Pricipale_Due[1].int_int_dues;
                                PI_And_Interest = obj_Return.Penal_Remit + obj_Return.Interest_Remit;
                                obj_Return.Principal_Remit = int_amt - PI_And_Interest;
                                obj_Return.Principal_Due = Convert.ToDecimal(objList_loanreg_By[0].mny_loanbal) - obj_Return.Principal_Remit;
                                obj_Return.Interest_Due = 0;
                                obj_Return.Penal_Due = 0;



                            }
                            else
                            {
                                if (int_amt >= Convert.ToDecimal(PI) && int_amt != 0)
                                {
                                    obj_Return.Penal_Remit = Convert.ToDecimal(PI) + objList_Pricipale_Due[1].int_penal_dues;
                                    int_amt = int_amt - Convert.ToDecimal(PI);
                                    if (int_amt >= (Interest + objList_Pricipale_Due[1].int_int_dues))
                                    {

                                        obj_Return.Interest_Remit = Interest + objList_Pricipale_Due[1].int_int_dues;
                                        int_amt = int_amt - obj_Return.Interest_Remit;
                                    }
                                    else
                                    {
                                        obj_Return.Interest_Remit = int_amt;
                                        int_amt = 0;
                                    }
                                    //  obj_Return.Penal_Remit = int_amt;
                                    if (int_amt >= (obj_Return.Interest_Remit + obj_Return.Penal_Remit))
                                    {
                                        obj_Return.Principal_Remit = int_amt - (obj_Return.Interest_Remit + obj_Return.Penal_Remit);
                                    }
                                    else
                                    {
                                        obj_Return.Principal_Remit = 0;
                                    }




                                    // VIVEK NEED TO CHECK THIS ASHRAF





                                    obj_Return.Principal_Due = Convert.ToDecimal(objList_loanreg_By[0].mny_loanbal) - obj_Return.Principal_Remit;
                                    obj_Return.Interest_Due = Interest + objList_Pricipale_Due[0].int_int_dues;
                                    obj_Return.Penal_Due = Convert.ToDecimal(PI) + objList_Pricipale_Due[0].int_penal_dues;

                                }
                                else
                                {
                                    if(Type == "SUBSIDY")
                                    {
                                        obj_Return.Interest_Due = objList_Pricipale_Due[1].int_int_dues + Interest;
                                        obj_Return.Penal_Due = objList_Pricipale_Due[1].int_penal_dues + Convert.ToDecimal(PI);
                                        obj_Return.Principal_Due = Convert.ToDecimal(objList_loanreg_By[0].mny_loanbal);

                                        obj_Return.Penal_Remit = 0;
                                        obj_Return.Interest_Remit = 0;
                                        obj_Return.Principal_Remit = 0;
                                    }

                                    obj_Return.Interest_Due = objList_Pricipale_Due[1].int_int_dues + Interest;
                                    obj_Return.Penal_Due = objList_Pricipale_Due[1].int_penal_dues + Convert.ToDecimal(PI);
                                    obj_Return.Principal_Due = Convert.ToDecimal(objList_loanreg_By[0].mny_loanbal);

                                    obj_Return.Penal_Remit = 0;
                                    obj_Return.Interest_Remit = 0;
                                    obj_Return.Principal_Remit = 0;


                                }



                            }
                        }

                    }
                    else
                    {
                        obj_Return.Interest = 0;
                        obj_Return.PI = 0;
                        obj_Return.Principal_Remit = 0;
                        obj_Return.Interest_Remit = 0;
                        obj_Return.Penal_Remit = 0;
                        obj_Return.Principal_Due = 0;
                        obj_Return.Interest_Due = 0;
                        obj_Return.Penal_Due = 0;
                    }
                }
                else
                {
                    obj_Return.Interest = 0;
                    obj_Return.PI = 0;
                    obj_Return.Principal_Remit = 0;
                    obj_Return.Interest_Remit = 0;
                    obj_Return.Penal_Remit = 0;
                    obj_Return.Principal_Due = 0;
                    obj_Return.Interest_Due = 0;
                    obj_Return.Penal_Due = 0;
                }
                return obj_Return;
            }
            catch (Exception ex)
            {
                throw;
                Transaction_Details obj_Return = new Transaction_Details();
                Report_Log($"Error->Method Name (Calculate_Interest_Main)->Exception ( '{ex.InnerException}' ) ");
                return obj_Return;
            }
        }
        public decimal Calculate_Interest(DateTime date_From, DateTime date_To, decimal Out_Princ, decimal? intere_Per)
        {
            try
            {
                TimeSpan timeSpan = date_To - date_From;
                decimal daysDifference = (decimal)timeSpan.TotalDays;

                //  Console.WriteLine("Days Difference: " + daysDifference);
                decimal Interest = Out_Princ * (Convert.ToDecimal(intere_Per) / 100) * (daysDifference / Convert.ToDecimal(365));
                return Interest;
            }
            catch (Exception ex)
            {
                throw;
                Report_Log($"Error->Method Name (Calculate_Interest)->Exception ( '{ex.InnerException}' ) ");
                return 0;
            }
        }
        public List<Select_Sub_Caste_Details_By_Sub_Caste_Name_Result> Select_Sub_Caste_Details_By_Sub_Caste_Name(string Sub_Caste)
        {
            try
            {
                List<Select_Sub_Caste_Details_By_Sub_Caste_Name_Result> objList = new List<Select_Sub_Caste_Details_By_Sub_Caste_Name_Result>();
                using (var entities = new KSDC_Entities())
                {
                    objList = entities.Select_Sub_Caste_Details_By_Sub_Caste_Name(Sub_Caste).ToList<Select_Sub_Caste_Details_By_Sub_Caste_Name_Result>();

                }
                return objList;
            }

            catch (Exception ex)
            {
                throw;
                List<Select_Sub_Caste_Details_By_Sub_Caste_Name_Result> objList = new List<Select_Sub_Caste_Details_By_Sub_Caste_Name_Result>();


                return objList;
            }
        }

        public List<Select_Pricipale_Due_Result> Select_Pricipale_Due(string int_loanno, DateTime dt_transaction)
        {
            try
            {

                List<Select_Pricipale_Due_Result> objList = new List<Select_Pricipale_Due_Result>();
                using (var entities = new KSDC_Entities())
                {
                    objList = entities.Select_Pricipale_Due(int_loanno, dt_transaction).ToList<Select_Pricipale_Due_Result>();
                }
                return objList;

            }


            catch (Exception ex)
            {
                throw;
                List<Select_Pricipale_Due_Result> objList = new List<Select_Pricipale_Due_Result>();

                return objList;
            }
        }
        public List<Select_tbl_loanapp_By_Reg_No_Result> Select_tbl_loanapp_By_Reg_No(string Reg_No)
        {
            try
            {

                List<Select_tbl_loanapp_By_Reg_No_Result> objList = new List<Select_tbl_loanapp_By_Reg_No_Result>();
                using (var entities = new KSDC_Entities())
                {
                    objList = entities.Select_tbl_loanapp_By_Reg_No(Reg_No).ToList<Select_tbl_loanapp_By_Reg_No_Result>();
                }
                return objList;

            }


            catch (Exception ex)
            {
                throw;
                List<Select_tbl_loanapp_By_Reg_No_Result> objList = new List<Select_tbl_loanapp_By_Reg_No_Result>();


                return objList;
            }
        }

        public List<Select_All_Offices_Result> Select_All_Offices()
        {
            try
            {

                List<Select_All_Offices_Result> objList = new List<Select_All_Offices_Result>();
                using (var entities = new KSDC_Entities())
                {
                    objList = entities.Select_All_Offices().ToList<Select_All_Offices_Result>();
                }
                return objList;
            }


            catch (Exception ex)
            {
                List<Select_All_Offices_Result> objList = new List<Select_All_Offices_Result>();


                return objList;
            }
        }


        private ObservableCollection<string> PopulateComboBox(ObservableCollection<string> comboBoxItems)
        {
            List<Select_All_Offices_Result> objList = new List<Select_All_Offices_Result>();
            objList = Select_All_Offices();
            foreach (var obj in objList)
            {
                //dropDistrict.Items.Add(obj.Office_Name+"|"+ obj.District_Id+","+ obj.Office_Code+","+ obj.Code+"-"+ obj.ShortCode);
                comboBoxItems.Add(obj.Office_Name + "|" + obj.District_Id + "," + obj.Office_Code + "," + obj.Code + "-" + obj.ShortCode);
            }
            return comboBoxItems;
        }
        private void OnComboBoxSelectionChanged(object sender, RoutedEventArgs e)
        {
            if (dropDistrict.SelectedItem != null)
            {
                string item = dropDistrict.SelectedItem.ToString();
                Dist_Code = item.Split('|')[1].Split(',')[2].Split('-')[1];
                Dist_Code_No = item.Split('|')[1].Split(',')[2].Split('-')[0];
                District_Id = item.Split('|')[1].Split(',')[0];
                Office_Code = item.Split('|')[1].Split(',')[1];
            }

        }

        private int GetLineNumber(Exception ex)
        {
            // Extract line number information from the stack trace
            var stackTrace = new System.Diagnostics.StackTrace(ex, true);
            var frame = stackTrace.GetFrame(0);
            return frame.GetFileLineNumber();
        }

        public bool CustomBooleanConversion(string input)
        {
            // Check if the input is specifically "0"
            if (string.Equals(input, "0", StringComparison.OrdinalIgnoreCase))
            {
                return false;
            }

            // Use Convert.ToBoolean() for other cases
            return Convert.ToBoolean(input);
        }
    }
}
