// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "\u4eca\u5929";
var L_January   = "1 \u6708";
var L_February  = "2 \u6708";
var L_March     = "3 \u6708";
var L_April     = "4 \u6708";
var L_May       = "5 \u6708";
var L_June      = "6 \u6708";
var L_July      = "7 \u6708";
var L_August    = "8 \u6708";
var L_September = "9 \u6708";
var L_October   = "10 \u6708";
var L_November  = "11 \u6708";
var L_December  = "12 \u6708";
var L_Su        = "\u9031\u65e5";
var L_Mo        = "\u9031\u4e00";
var L_Tu        = "\u9031\u4e8c";
var L_We        = "\u9031\u4e09";
var L_Th        = "\u9031\u56db";
var L_Fr        = "\u9031\u4e94";
var L_Sa        = "\u9031\u516d";

// strings for dt_param.js
var L_TIME_SEPARATOR = ":";
var L_AM_DESIGNATOR = "\u4e0a\u5348";
var L_PM_DESIGNATOR = "\u4e0b\u5348";

// strings for range parameter
var L_FROM = "\u5f9e {0}";
var L_TO = "\u5230 {0}";
var L_AFTER = "{0} \u4e4b\u5f8c";
var L_BEFORE = "{0} \u4e4b\u524d";
var L_FROM_TO = "\u5f9e {0} \u5230 {1}";
var L_FROM_BEFORE = "\u5f9e {0} \u5230 {1} \u4e4b\u524d";
var L_AFTER_TO = "{0} \u4e4b\u5f8c\u5230 {1}";
var L_AFTER_BEFORE = "{0} \u4e4b\u5f8c\u5230 {1} \u4e4b\u524d";

// Strings for prompts.js and prompts_param.js
var L_BadNumber		= "\u6b64\u53c3\u6578\u7684\u985e\u578b\u70ba \"\u6578\u5b57\" \u800c\u4e14\u53ea\u5305\u542b\u8ca0\u6578\u7b26\u865f\u3001\u6578\u5b57 (\"0-9\")\u3001\u6578\u5b57\u7fa4\u7d44\u7b26\u865f\u6216\u5c0f\u6578\u9ede\u7b26\u865f\u3002\u8acb\u4fee\u6b63\u4e26\u8f38\u5165\u53c3\u6578\u503c\u3002";
var L_BadCurrency	= "\u6b64\u53c3\u6578\u7684\u985e\u578b\u70ba \"\u8ca8\u5e63\" \u800c\u4e14\u53ea\u5305\u542b\u8ca0\u6578\u7b26\u865f\u3001\u6578\u5b57 (\"0-9\")\u3001\u6578\u5b57\u7fa4\u7d44\u7b26\u865f\u6216\u5c0f\u6578\u9ede\u7b26\u865f\u3002\u8acb\u4fee\u6b63\u4e26\u8f38\u5165\u53c3\u6578\u503c\u3002";
var L_BadDate		= "\u6b64\u53c3\u6578\u7684\u985e\u578b\u70ba \"\u65e5\u671f\"\u800c\u4e14\u683c\u5f0f\u61c9\u70ba \"\u65e5\u671f(yyyy,mm,dd)\" \u5176\u4e2d \"yyyy\" \u70ba 4 \u4f4d\u6578\u7684\u5e74\uff0c\"mm\" \u70ba\u6708 (\u4f8b\u5982: 1 \u6708 = 1)\uff0c\u800c \"dd\" \u70ba\u63d0\u4f9b\u6708\u4efd\u4e2d\u7684\u65e5\u671f\u6578\u5b57\u3002";
var L_BadDateTime   = "\u6b64\u53c3\u6578\u7684\u985e\u578b\u70ba \"\u65e5\u671f\u6642\u9593\" \u800c\u4e14\u6b63\u78ba\u7684\u683c\u5f0f\u70ba \"\u65e5\u671f\u6642\u9593(yyyy,mm,dd,hh,mm,ss)\"\u3002 \"yyyy\" \u70ba 4 \u4f4d\u6578\u7684\u5e74\uff0c\"mm\" \u70ba\u6708 (\u4f8b\u5982: 1 \u6708 = 1)\uff0c\"dd\" \u70ba\u6708\u4e2d\u7684\u65e5\u671f\uff0c\"hh\" \u70ba 24 \u5236\u7684\u6642\u6578\uff0c\"mm\" \u70ba\u5206\u9418\u6578\uff0c\u800c \"ss\" \u5247\u70ba\u79d2\u6578\u3002";
var L_BadTime       = "\u6b64\u53c3\u6578\u7684\u985e\u578b\u70ba \"\u6642\u9593\"\u800c\u4e14\u683c\u5f0f\u61c9\u70ba \"\u6642\u9593(hh,mm,ss)\" \u5176\u4e2d \"hh\" \u70ba 24 \u6642\u5236\u7684\u6642\u6578\uff0c \"mm\" \u70ba\u5206\u9418\u6578\uff0c\u800c \"ss\" \u5247\u70ba\u79d2\u6578\u3002";
var L_NoValue       = "\u6c92\u6709\u503c";
var L_BadValue      = "\u82e5\u8981\u8a2d\u5b9a\u6210 \"\u6c92\u6709\u503c\"\uff0c\u5fc5\u9808\u540c\u6642\u5c07 \"\u5f9e\" \u548c \"\u5230\" \u7684\u503c\u8a2d\u6210 \"\u6c92\u6709\u503c\"\u3002";
var L_BadBound      = "\"\u7121\u4e0b\u9650\" \u4e0d\u80fd\u8207 \"\u7121\u4e0a\u9650\" \u4e00\u8d77\u8a2d\u5b9a\u3002";
var L_NoValueAlready = "\u53c3\u6578\u5df2\u7d93\u8a2d\u5b9a\u6210 \"\u6c92\u6709\u503c\"\u3002\u5728\u52a0\u5165\u5176\u4ed6\u503c\u4e4b\u524d\uff0c\u8acb\u5148\u79fb\u9664 \"\u6c92\u6709\u503c\"";
var L_RangeError    = "\u7bc4\u570d\u8d77\u9ede\u4e0d\u5f97\u5927\u65bc\u7bc4\u570d\u7d42\u9ede\u3002";
var L_NoDateEntered = "\u5fc5\u9808\u8f38\u5165\u65e5\u671f\u3002";

// Strings for ../html/crystalexportdialog.htm
var L_ExportOptions     = "\u532f\u51fa\u9078\u9805";
var L_PrintOptions      = "\u5217\u5370\u9078\u9805";
var L_PrintPageTitle    = "\u5217\u5370\u5831\u8868";
var L_ExportPageTitle   = "\u532f\u51fa\u5831\u8868";
var L_OK                = "\u78ba\u5b9a";
var L_PrintPageRange    = "\u8f38\u5165\u8981\u5217\u5370\u7684\u9801\u9762\u7bc4\u570d\u3002";
var L_ExportPageRange   = "\u8f38\u5165\u8981\u532f\u51fa\u7684\u9801\u9762\u7bc4\u570d\u3002";
var L_InvalidPageRange  = "\u9801\u9762\u7bc4\u570d\u503c\u4e0d\u6b63\u78ba\u3002\u8acb\u8f38\u5165\u6709\u6548\u7684\u9801\u9762\u7bc4\u570d\u3002";
var L_ExportFormat      = "\u8acb\u5f9e\u6e05\u55ae\u4e2d\u9078\u53d6\u532f\u51fa\u683c\u5f0f\u3002";
var L_Formats           = "\u683c\u5f0f:";
var L_All               = "\u5168\u90e8";
var L_Pages             = "\u9801\u6578";
var L_From              = "\u5f9e:";
var L_To                = "\u5230:";
var L_PrintStep0        = "\u82e5\u8981\u5217\u5370:";
var L_PrintStep1        = "1.  \u5728\u4e0b\u4e00\u500b\u51fa\u73fe\u7684\u5c0d\u8a71\u65b9\u584a\u4e2d\uff0c\u8acb\u9078\u53d6 \"\u958b\u555f\u6b64\u6a94\u6848\" \u9078\u9805\uff0c\u7136\u5f8c\u518d\u6309\u4e00\u4e0b [\u78ba\u5b9a] \u6309\u9215\u3002";
var L_PrintStep2        = "2.  \u6309\u4e00\u4e0b Acrobat Reader \u529f\u80fd\u8868\u4e0a\u7684\u5370\u8868\u6a5f\u5716\u793a\uff0c\u800c\u4e0d\u662f\u7db2\u969b\u7db2\u8def\u700f\u89bd\u5668\u4e0a\u7684\u5217\u5370\u6309\u9215\u3002";
var L_RTFFormat         = "Rich Text Format";
var L_AcrobatFormat     = "Acrobat \u683c\u5f0f (PDF)";
var L_CrystalRptFormat  = "Crystal Reports (RPT)";
var L_WordFormat        = "MS Word";
var L_ExcelFormat       = "MS Excel 97-2000";
var L_ExcelRecordFormat = "MS Excel 97-2000 (\u53ea\u6709\u8cc7\u6599)";
if(typeof(Sys)!=='undefined')Sys.Application.notifyScriptLoaded();
