//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CorpNet_To_KSDC_SMART
{
    using System;
    
    public partial class Select_tbl_House_Loan_By_int_loanappid_Result
    {
        public decimal int_houloanappid { get; set; }
        public decimal int_loanappid { get; set; }
        public string vchr_coapp_name { get; set; }
        public string vchr_<PERSON> { get; set; }
        public string vchr_<PERSON> { get; set; }
        public string vchr_Spouse { get; set; }
        public string vchr_Relation { get; set; }
        public Nullable<System.DateTime> dte_dob { get; set; }
        public Nullable<decimal> int_age { get; set; }
        public string vchr_Aadhaar { get; set; }
        public string vchr_electionid { get; set; }
        public string vchr_ration { get; set; }
        public Nullable<decimal> int_income_salary { get; set; }
        public Nullable<decimal> int_income_agr { get; set; }
        public Nullable<decimal> int_income_other { get; set; }
        public Nullable<decimal> int_income_total { get; set; }
        public string vchr_ownername { get; set; }
        public string vchr_extent { get; set; }
        public string vchr_surveyno { get; set; }
        public string vchr_Blockno { get; set; }
        public string vchr_TPno { get; set; }
        public string vchr_land_village { get; set; }
        public string vchr_land_taluk { get; set; }
        public string vchr_land_district { get; set; }
        public string vchr_deedtype { get; set; }
        public string vchr_deedno { get; set; }
        public string vchr_subregoffiname { get; set; }
        public string vchr_base_area { get; set; }
        public Nullable<decimal> int_no_floor { get; set; }
        public string vchr_carpet_area { get; set; }
        public string vchr_construction_type { get; set; }
        public Nullable<decimal> int_cost { get; set; }
        public string vchr_permit_no { get; set; }
        public Nullable<System.DateTime> dte_permit_date { get; set; }
        public string vchr_validity { get; set; }
        public Nullable<decimal> int_concession { get; set; }
        public string vchr_details { get; set; }
        public Nullable<decimal> int_concessionamt { get; set; }
        public Nullable<decimal> int_total_cost { get; set; }
        public Nullable<decimal> int_cash_hand { get; set; }
        public Nullable<decimal> int_subsidy { get; set; }
        public Nullable<decimal> int_loan_req { get; set; }
        public Nullable<decimal> int_repay_year { get; set; }
    }
}
