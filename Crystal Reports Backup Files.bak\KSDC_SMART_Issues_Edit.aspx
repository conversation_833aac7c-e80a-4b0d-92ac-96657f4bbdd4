﻿<%@ Page Title="KSDC SMART Issues | Edit" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="KSDC_SMART_Issues_Edit.aspx.cs" Inherits="KSDCSCST_Portal.KSDC_SMART_Issues_Edit" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">
    <link rel="stylesheet" href="assets/plugins/dropzone/min/dropzone.min.css">
    <link rel="stylesheet" href="assets/plugins/summernote/summernote-bs4.min.css">
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>KSDC- SMART Issue Tracking System - ITS</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item"><a href="KSDC_SMART_Issues_List.aspx">KSDC- SMART Issue Tracking System - ITS</a></li>
                        <li class="breadcrumb-item active">Edit Issue</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">
                                    <div class="form-group row">
                                        <label for="txtApplicant_Name" class="col-sm-3 col-form-label">Issue Title *</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtIssue_Title" placeholder="Issue Title">
                                        </div>
                                    </div>
                                    <%-- <div class="form-group row">
                                        <label for="txtApplicant_Name" class="col-sm-3 col-form-label">Priority *</label>
                                        <div class="col-sm-9">
                                            <select id="dropPriority" class="form-control">
                                                <option>High</option>
                                                <option>Low</option>
                                                <option>Critical</option>
                                            </select>
                                        </div>
                                    </div>--%>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Issue Description *</label>
                                        <div class="col-sm-9">
                                            <textarea class="form-control" id="txtIssue_Description" placeholder="Issue Description"></textarea>


                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="txtApplicant_Name" class="col-sm-3 col-form-label">Status *</label>
                                        <div class="col-sm-9">
                                            <select id="dropStatus" class="form-control">
                                                <option value="Pending">Pending</option>
                                                <option value="Resolved">Resolved</option>
                                                <option value="Closed">Closed</option>
                                                <option value="Re-Opened">Re-Opened</option>
                                            </select>
                                        </div>
                                    </div>
                                    <hr />
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="Sub_Header">Messages</div>
                                        </div>
                                    </div>
                                    <div id="DIV_AllMessages">
                                        
                                    </div>
                                    <hr />
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="form-group row">
                                                <label for="inputName2" class="col-sm-3 col-form-label">Message</label>
                                                <div class="col-sm-9">
                                                    <textarea class="form-control" maxlength="200" id="txtMessage" placeholder="Message"></textarea>
                                                    <span class="validation_message">Maximum text length is 200</span>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">

                                            <a onclick="Send();" style="background-color: #03a84e !important; border-color: #03a84e !important;" class="btn btn-success">Send</a>
                                        </div>
                                    </div>
                                    <hr />
                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                            <a onclick="Get_Session_District_Id();" accesskey="B" class="btn btn-dark">Back</a>
                                            <a onclick="Save();" class="btn btn-success" accesskey="N">Next</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <script src="assets/plugins/toastr/toastr.min.js"></script>

    <script src="assets/plugins/summernote/summernote-bs4.min.js"></script>

    <%-- <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/inputmask/5.0.4/jquery.inputmask.min.js"></script>--%>

    <script src="assets/plugins/dropzone/min/dropzone.min.js"></script>
    <script>
        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;

        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }


        $(document).ready(function () {

            //  $('#txtIssue_Description').summernote()


            // DropzoneJS Demo Code Start
            Dropzone.autoDiscover = false



            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });


            //function Input_Phone_Input_On_Event() {

            //    var inputValue = $(this).val();
            //    var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
            //    $(this).val(sanitizedValue); // Set the sanitized value back to the input field


            //    if (inputValue.trim() == ",") {
            //        sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
            //        $(this).val("");
            //    }
            //    $(this).val($(this).val().replace(/,+/g, ','));

            //    var values = sanitizedValue.split(",");
            //    if (values[1] == "" && values[0].length < 10) {
            //        Toast.fire({
            //            icon: 'error',
            //            title: 'Enter valid phone number !'
            //        })
            //    }

            //}





            //$(document).on("input", ".form-control", function () {
            //    // Get the current value of the input
            //    var currentValue = $(this).val();

            //    // Convert the value to propcase
            //    var propcaseValue = currentValue.replace(/\w\S*/g, function (txt) {
            //        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
            //    });

            //    // Update the input with the propcase value
            //    $(this).val(propcaseValue);
            //});

            Load_All_Issue_Details();

        });




        function Load_All_Issue_Details() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_tbl_KSDC_SMART_Issues_By_Id",
                data: JSON.stringify({ Id: getParameterByName("Id") }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {


                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Issue_Title = value.Issue_Title;
                        var Issue_Desc = value.Issue_Desc;
                        var Status = value.Status


                        $('#txtIssue_Title').val(Issue_Title);
                        $("#txtIssue_Description").val(Issue_Desc);

                        $('#dropStatus').val(Status);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Messages();
             

            });

        }

        function Load_All_Messages() {



            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_tbl_KSDC_SMART_Issues_Messages_By_Issue_Id",
                data: JSON.stringify({ issue_Id: getParameterByName("Id") }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $("#DIV_AllMessages").empty();
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Office = value.Office;
                        var Message = value.Message;
                        var Created_DateTime = value.Created_DateTime;
 
                        $("#DIV_AllMessages").append('<div class="row">' +
                            '    <div class="col-sm-12">' +
                            '        <b class="Message_Office_Name">' + Office+'</b>' +
                            '        <span class="Meesage_Date_Time">' + Created_DateTime+'</span>' +
                                        '    </div>' + 
                                        '</div>' +
                            '<div class="row">' +
                            '    <div class="col-sm-12">' +
                            '        <p class="Messages">' + Message+'</p>' +
                            '    </div>' +
                            '</div>');
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

               

            });





        }

        function Send() {



            var Message = $("#txtMessage");

            if (Message.val().trim() == "") {
                Focus_Error(Message);
                Toast.fire({
                    icon: 'error',
                    title: 'Message is required !'
                })
            }
            else {
                var currentDate = new Date();


                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/Insert_To_tbl_KSDC_SMART_Issues_Messages",
                    data: JSON.stringify({ issue_Id: getParameterByName("Id"), message: Message.val(), Created_DateTime: currentDate }), // If you have parameters
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                         

                    },
                    error: function (error) {


                        // alert("error" + error.responseText);
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Something went wrong!',

                        })
                    }
                }).done(function () {
                    Message.val("");
                    Load_All_Messages();

                });

            }



        }
        function Save() {
            //alert(Counter);
            // var get_code = $('#txtIssue_Description').summernote('code');
            // $('#txtIssue_Description').summernote('code', '<b> hello world </b>');
            // alert(get_code);

            txtMessage
            var Issue_Title = $("#txtIssue_Title");

            var Issue_Description = $("#txtIssue_Description");
            var Status = $("#dropStatus");




            $(".form-control").removeAttr("style");



            if (Issue_Title.val().trim() == "") {
                Focus_Error(Issue_Title);
                Toast.fire({
                    icon: 'error',
                    title: 'Issue Title is required !'
                })
            }
            else if (Issue_Description.val() == "") {
                Focus_Error(Issue_Title);
                Toast.fire({
                    icon: 'error',
                    title: 'Issue Description is required !'
                })
            }



            else {
                var Issue_Id = '';

                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/Update_To_tbl_KSDC_SMART_Issues",
                    data: JSON.stringify({ Id: getParameterByName("Id"), issue_Title: Issue_Title.val(), issue_Desc: Issue_Description.val(), status: Status.val() }), // If you have parameters
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        // Issue_Id=data.d;
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        // Handle AJAX error
                        //   alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);

                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                        })

                    }

                }).done(function () {
                    location.href = 'KSDC_SMART_Issues_Add_Attachment.aspx?Id=' + getParameterByName("Id");
                    //Swal.fire({
                    //    icon: 'success',
                    //    title: 'Message',
                    //    text: 'Issue Successfully Updated !',

                    //}).then((result) => {
                    //    if (result.isConfirmed) {
                    //        // OK button clicked

                    //        location.href = 'KSDC_SMART_Issues_Add_Attachment.aspx?Id=' + getParameterByName("Id");
                    //        // Your code here
                    //    }
                    //});

                });


            }




        }
        function Get_Session_District_Id() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Get_Session_District_Id",


                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    if (data.d == "15") {
                        location.href = 'KSDC_SMART_Issues_All_List.aspx';
                    }
                    else {
                        location.href = 'KSDC_SMART_Issues_List.aspx';
                    }


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });

        }

        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }

        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }


        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }



    </script>
</asp:Content>
