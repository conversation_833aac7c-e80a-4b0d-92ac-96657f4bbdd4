﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Surety_Land_Edit.aspx.cs" Inherits="KSDCSCST_Portal.Surety_Land_Edit" %>


<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- daterange picker -->
    <link rel="stylesheet" href="assets/plugins/daterangepicker/daterangepicker.css">
    <!-- iCheck for checkboxes and radio inputs -->
    <link rel="stylesheet" href="assets/plugins/icheck-bootstrap/icheck-bootstrap.min.css">
    <!-- Bootstrap Color Picker -->
    <link rel="stylesheet" href="assets/plugins/bootstrap-colorpicker/css/bootstrap-colorpicker.min.css">
    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet" href="assets/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css">
    <!-- Select2 -->
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">
    <!-- Bootstrap4 Duallistbox -->
    <link rel="stylesheet" href="assets/plugins/bootstrap4-duallistbox/bootstrap-duallistbox.min.css">
    <!-- BS Stepper -->
    <link rel="stylesheet" href="assets/plugins/bs-stepper/css/bs-stepper.min.css">
    <!-- dropzonejs -->
    <link rel="stylesheet" href="assets/plugins/dropzone/min/dropzone.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">

    <style>
        input:disabled {
            border: 0;
        }
    </style>
    <style>
        /* Styles for the search input */
        txtPost_Office {
            width: 300px;
            padding: 10px;
            font-size: 16px;
        }

        /* Styles for the search results container */
        #search-results {
            list-style: none;
            padding: 0;
            margin-top: 5px;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
        }

            /* Styles for individual search result items */
            #search-results li {
                padding: 5px;
                cursor: pointer;
            }


                #search-results li:hover {
                    background-color: #f2f2f2;
                }

        #search-results-pre-pin {
            list-style: none;
            padding: 0;
            margin-top: 5px;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
        }
    </style>

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Guarantor Land Modification</h1>

                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="Welcome.aspx">Home</a></li>
                        <li class="breadcrumb-item active">Guarantor Land Modification</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">
                                <div class="active tab-pane" id="settings">
                                    <div class="Sub_Header">Land Guarantor Details</div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Application Register No*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtApplicationRegNo" placeholder="Application Register No">
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Loanee Name*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtLoanee_Name" placeholder="Loanee Name">
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name of Title Holder*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtName_Of_Title_Holder" placeholder="Name of Title Holder">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name of Father*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtName_Of_Father" placeholder="Name of Father">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">House Name/Number*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtHouse_Name_Number" placeholder="House Name/Number">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Lane1*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtLane1" placeholder="Lane1">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Lane2*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtLane2" placeholder="Lane2">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Pincode*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtPincode" placeholder="Search...">
                                            <ul id="search-results"></ul>
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Post Office*</label>
                                        <div class="col-sm-9">
                                            <input type="text" disabled="disabled" class="form-control" id="txtPost_Office" placeholder="Post Office">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Phone Number*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtPhone_Number" placeholder="Phone Number">
                                        </div>


                                    </div>




                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">District*</label>
                                        <div class="col-sm-9">
                                            <select id="txtLand_District" class="form-control">
                                                <option value="0">Select</option>

                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Taluk*</label>
                                        <div class="col-sm-9">
                                            <select id="dropTaluk" class="form-control">
                                                <option value="0">Select</option>
                                            </select>

                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Village*</label>
                                        <div class="col-sm-9">
                                            <select id="dropVillage" class="form-control">
                                                <option value="0">Select</option>
                                            </select>
                                        </div>
                                    </div>



                                    <div class="Sub_Header">Land Details</div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Type of Title Deed*</label>
                                        <div class="col-sm-9">
                                            <select id="dropdeedtype" class="form-control">
                                                <option value="[Select]">[Select]</option>
                                                <option value="Sale">Sale</option>
                                                <option value="Settlement">Settlement</option>
                                                <option value="Partition">Partition</option>
                                                <option value="Pattayam">Pattayam</option>
                                                <option value="Gift">Gift</option>
                                                <option value="RightRedemtion">RightRedemtion</option>
                                                <option value="Others">Others</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group row" id="DeedType_Others">
                                        <label class="col-sm-3 col-form-label">Specify Others*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtDeedType_Others" placeholder="Others">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Deed No.*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtDeed_No" placeholder="Deed No.">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Survey No.*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtSurvey_No" placeholder="Survey No">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Extent*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtExtent" placeholder="Extent">
                                        </div>
                                    </div>




                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Tandaperu*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtTandaperu" placeholder="Tandaperu">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Valuation Amount*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtValuation_Amount" placeholder="Valuation Amount">
                                        </div>
                                    </div>




                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">District*</label>
                                        <div class="col-sm-9">
                                            <select id="txtLand_District_2" class="form-control">
                                                <option value="0">Select</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Taluk*</label>
                                        <div class="col-sm-9">
                                            <select id="dropTaluk_2" class="form-control">
                                                <option value="0">Select</option>
                                            </select>

                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Village*</label>
                                        <div class="col-sm-9">
                                            <select id="dropLand_Village" class="form-control">
                                                <option value="0">Select</option>
                                            </select>
                                        </div>
                                    </div>



                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name of SubRegistrar Office*</label>
                                        <div class="col-sm-9">
                                            <select id="dropName_SubRegistrar_Office" class="form-control">
                                                <option value="[Select]">[Select]</option>
                                            </select>
                                            <input type="hidden" id="SubRegistrar_Office" name="SubRegistrar_Office" value="">
                                        </div>
                                    </div>


                                    <div class="form-group row" style="display: none;">
                                        <label class="col-sm-3 col-form-label"></label>
                                        <div class="col-sm-9">
                                            <label class="col-sm-8 col-form-label">Check If Fairvalue Certificate used for Land Valuation</label>

                                            <input class="col-sm-1 form-check-input" style="margin: 0; position: relative;" type="checkbox" id="checkIf_Fairvalue_Certificate">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                            <a onclick="Save();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->

    </section>
    <!-- /.content -->

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <script src="assets/plugins/toastr/toastr.min.js"></script>



    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.js"></script>

    <script>
        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;

        const $searchInput = $('#txtPincode');
        const $searchResults = $('#search-results');
        $('#search-results').hide();
        $('#DeedType_Others').hide();

        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }

        $(function () {

            $searchInput.on('input', function () {
                const query = $searchInput.val();
                fetchAutocompleteResults(query);
            });

            // Event handler for selecting a result
            $searchResults.on('click', 'li', function () {
                $("#txtPincode").val($(this).attr("data-pincode"));
                $("#txtPost_Office").val($(this).html());
                //  const selectedResult = $(this).text();
                //  $searchInput.val(selectedResult);
                $searchResults.hide();
            });
        });

        $(document).ready(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            // Get the current date
            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtApplication_Date').val(formattedDate);

            $("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
            $("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");
            // Load_All_Schemes();
            //   Load_All_Districts();

            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }

            Load_All_Application_Issue_By_Id(getParameterByName("LoanappId"));

        });

        function fetchAutocompleteResults(query) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ pincode: query }), // If you have parameters
                url: "WebService.asmx/Select_All_PostOffices",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $searchResults.empty();
                    if (data.d.length > 0) {

                        $searchResults.show();
                    } else {
                        $searchResults.hide();
                        $("#txtPost_Office").val('');
                    }
                    $.each(data.d, function (key, value) {
                        $searchResults.append('<li data-pincode="' + value.Pincode + '">' + value.Post_office + '</li>');
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });


        }

        function Load_All_Application_Issue_By_Id(LoanappId) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Id: LoanappId }), // If you have parameters
                url: "WebService.asmx/Select_tbl_LoanApp_By_LoanAppId",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        $(".LABEL_APP_Status").text(value.chr_status);
                        var int_loanappid = value.int_loanappid;
                        var Name_Applicant = value.vchr_applname;
                        var Scheme_Id = value.int_schemeid;
                        Cast_Id = value.vchr_caste;
                        Sub_Cast_Id = value.SubCast;
                        var vchr_appreceivregno = value.vchr_appreceivregno;
                        $("#txtLoanee_Name").val(Name_Applicant);
                        $("#txtApplicationRegNo").val(vchr_appreceivregno);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
               
                Load_All_Surety_Land_By_Id(getParameterByName("Id"));
            });
        }

        function getFormattedDate(date) {
            date = date.split('/')[2] + "-" + date.split('/')[1] + "-" + date.split('/')[0];
            return date;
        }

        var Village_Id;
        var LandVillage_Id;


        function Load_All_Surety_Land_By_Id(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Id: Id }), // If you have parameters
                url: "WebService.asmx/Select_tbl_landsurety_By_Id",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        Village_Id = value.vchr_village;
                        LandVillage_Id = value.vchr_land_village;
                        $("#SubRegistrar_Office").val(value.vchr_subregoffiname);
                     $("#txtName_Of_Title_Holder").val(value.vchr_ownername);
                        $("#txtName_Of_Father").val(value.vchr_fathername);
                        $("#txtHouse_Name_Number").val(value.vchr_hsename);
                        $("#txtLane1").val(value.vchr_lane1);
                        $("#txtLane2").val(value.vchr_lane2);
                        $("#txtPincode").val(value.int_pin);
                        $("#txtPost_Office").val(value.vchr_post);
                        $("#txtPhone_Number").val(value.vchr_phno);
                        $("#txtTaluk").val(value.vchr_taluk);
                        $("#txtDistrict").val(value.vchr_district);
                        var DeedType = value.vchr_deedtype;
                        $("#dropdeedtype option:contains(" + DeedType + ")").attr('selected', 'selected');
                        if (DeedType == "Others") {
                            $("#dropdeedtype").trigger('change');
                            DeedType = $('#txtDeedType_Others').val();
                        }
                        $("#txtDeed_No").val(value.vchr_deedno);
                        $("#txtSurvey_No").val(value.vchr_surveyno);
                        $("#txtExtent").val(value.vchr_extent);
                        $("#txtTandaperu").val(value.vchr_tpno);
                        $("#txtValuation_Amount").val(value.int_ValAmt);
                        $("#txtLand_Taluk").val(value.vchr_land_taluk);
                        $("#txtLand_District").val(value.vchr_land_district);
                        var Fair = value.int_fair;
                        if (Fair == "1") {
                            $('#checkIf_Fairvalue_Certificate').prop('checked', true);
                        }
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Select_All_Districts();
                Load_All_Villages(Village_Id, LandVillage_Id);
            });
        }

        function Load_All_Villages(Village_Id, LandVillage_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Villages",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropVillage').empty();
                    $('#dropLand_Village').empty();
                    $('#dropVillage').append('<option value="0">[Select]</option>');
                    $('#dropLand_Village').append('<option value="0">[Select]</option>');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Village = value.Village;
                        var html = '<option value="' + Id + '">' + Village + '</option>';
                        $('#dropVillage').append(html);
                        $('#dropLand_Village').append(html);
                    });
                },
                error: function (error) {
                    //alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                $("#dropVillage option:contains(" + Village_Id + ")").attr('selected', 'selected');
                $("#dropLand_Village option:contains(" + LandVillage_Id + ")").attr('selected', 'selected');
                $("#dropLand_Village").trigger('change');
            });

        }

        $('#txtLand_District').change(function () {
            $("#dropTaluk").empty();
            $("#dropVillage").empty();
            $('#dropVillage').append('<option value="0">Select</option>');
            var optionSelected = $(this).find("option:selected");
            var valueSelected = optionSelected.val();
            if (valueSelected > 0) {
                Load_All_Land_Taluk_By_District_Id(valueSelected);

            }

        });
        $('#txtLand_District_2').change(function () {
            $("#dropTaluk_2").empty();
            $("#dropLand_Village").empty();
            $('#dropLand_Village').append('<option value="0">Select</option>');
            var optionSelected = $(this).find("option:selected");
            var valueSelected = optionSelected.val();
            if (valueSelected > 0) {
                Load_All_Land_Taluk_By_District_Id_2(valueSelected);
                Select_tbl_Sub_Reg_Office_By_District_Id(valueSelected);

            }

        });

        $('#dropTaluk').change(function () {
            var optionSelected = $(this).find("option:selected");
            var valueSelected = optionSelected.val();
            if (valueSelected > 0) {
                Load_All_Land_Villages_By_Taluk_Id(valueSelected);
            }
        });
        $('#dropTaluk_2').change(function () {
            var optionSelected = $(this).find("option:selected");
            var valueSelected = optionSelected.val();
            if (valueSelected > 0) {
                Load_All_Land_Villages_By_Taluk_Id_2(valueSelected);

            }

        });

        $('#dropVillage').change(function () {
            var optionSelected = $(this).find("option:selected");
            var valueSelected = optionSelected.val();
            if (valueSelected > 0) {
                Load_All_tbl_Taluk_By_Village_Id(valueSelected);
                Load_All_Districts_By_Village_Id(valueSelected);
            }
            else {
                $('#txtTaluk').val('');
                $('#txtDistrict').val('');
            }

        });
        $('#dropLand_Village').change(function () {
            var optionSelected = $(this).find("option:selected");
            var valueSelected = optionSelected.val();
            if (valueSelected > 0) {
                Load_All_Land_Taluk_By_Village_Id(valueSelected);
                Load_All_Land_Districts_By_Village_Id(valueSelected);
            }
            else {
                $('#txtLand_Taluk').val('');
                $('#txtLand_District').val('');
            }
        });
        $('#dropdeedtype').change(function () {
            var optionSelected = $(this).find("option:selected");
            var valueSelected = optionSelected.val();
            if (valueSelected == "Others") {
                $('#DeedType_Others').show();
            }
            else {
                $('#DeedType_Others').hide();
            }
        });

        function Load_All_tbl_Taluk_By_Village_Id(Village_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_Taluk_By_Id",
                data: JSON.stringify({ Village_Id: Village_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtTaluk').val('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Taluk = value.Taluk;
                        $('#txtTaluk').val(Taluk);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }
        function Load_All_Land_Taluk_By_Village_Id(Village_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_Taluk_By_Id",
                data: JSON.stringify({ Village_Id: Village_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtLand_Taluk').val('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Taluk = value.Taluk;
                        $('#txtLand_Taluk').val(Taluk);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }





        /*----------------------------------------------------*/

        /*Load First Section*/
        function Select_All_Districts() {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts",
                //  data: JSON.stringify({ Village_Id: Village_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtLand_District').empty();
                    $('#txtLand_District').append('<option value="0">Select</option>');


                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var District_Name = value.District_Name;
                        var html = '<option value="' + Id + '">' + District_Name + '</option>';

                        $('#txtLand_District').append(html);


                    });

                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                //alert(Present_District);
                Select_All_Districts_2();
            });
        }
        function Load_All_Land_Taluk_By_District_Id(District_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_Taluk_By_District_Id",
                data: JSON.stringify({ District_Id: District_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropTaluk').empty();
                    $('#dropTaluk').append('<option value="0">Select</option>');



                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var Taluk = value.Taluk;
                        var html = '<option value="' + Id + '">' + Taluk + '</option>';

                        $('#dropTaluk').append(html);

                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }

        function Load_All_Land_Villages_By_Taluk_Id(Taluk_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Villages_By_Taluk_Id",
                data: JSON.stringify({ Taluk_Id: Taluk_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropVillage').empty();
                    $('#dropVillage').append('<option value="0">Select</option>');



                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var Village = value.Village;
                        var html = '<option value="' + Id + '">' + Village + '</option>';

                        $('#dropVillage').append(html);

                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }


        /*Load Second Section*/

        function Select_All_Districts_2() {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts",
                //  data: JSON.stringify({ Village_Id: Village_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtLand_District_2').empty();
                    $('#txtLand_District_2').append('<option value="0">Select</option>');


                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var District_Name = value.District_Name;
                        var html = '<option value="' + Id + '">' + District_Name + '</option>';

                        $('#txtLand_District_2').append(html);


                    });

                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                //alert(Present_District);
            });
        }

        function Load_All_Land_Taluk_By_District_Id_2(District_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_Taluk_By_District_Id",
                data: JSON.stringify({ District_Id: District_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $('#dropTaluk_2').empty();
                    $('#dropTaluk_2').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var Taluk = value.Taluk;
                        var html = '<option value="' + Id + '">' + Taluk + '</option>';


                        $('#dropTaluk_2').append(html);

                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }

        function Load_All_Land_Villages_By_Taluk_Id_2(Taluk_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Villages_By_Taluk_Id",
                data: JSON.stringify({ Taluk_Id: Taluk_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $('#dropLand_Village').empty();
                    $('#dropLand_Village').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var Village = value.Village;
                        var html = '<option value="' + Id + '">' + Village + '</option>';


                        $('#dropLand_Village').append(html);

                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }


        /*-----------------------------------------*/



          


















        function Select_All_Districts() {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts",
                //  data: JSON.stringify({ Village_Id: Village_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtLand_District').empty();
                    $('#txtLand_District').append('<option value="0">Select</option>');


                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var District_Name = value.District_Name;
                        var html = '<option value="' + Id + '">' + District_Name + '</option>';

                        $('#txtLand_District').append(html);


                    });

                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                //alert(Present_District);
                Select_All_Districts_2();
            });
        }



        function Load_All_Districts_By_Village_Id(Village_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts_By_Village_Id",
                data: JSON.stringify({ Village_Id: Village_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtDistrict').val('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var District_Name = value.District_Name;
                        $('#txtDistrict').val(District_Name);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                //alert(Present_District);
            });
        }

        var LandDistrictId = "";

        function Load_All_Land_Districts_By_Village_Id(Village_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts_By_Village_Id",
                data: JSON.stringify({ Village_Id: Village_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtLand_District').val('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var District_Name = value.District_Name;
                        $('#txtLand_District').val(District_Name);
                        LandDistrictId = Id;
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Select_tbl_Sub_Reg_Office_By_District_Id(LandDistrictId);
            });
        }

        function Select_tbl_Sub_Reg_Office_By_District_Id(LandDistrictId) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_tbl_Sub_Reg_Office_By_District_Id",
                data: JSON.stringify({ district_Id: LandDistrictId }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropName_SubRegistrar_Office').empty();
                    $('#dropName_SubRegistrar_Office').append('<option value="0">[Select]</option>');
                    $.each(data.d, function (key, value) {
                        var Id = value.int_sroid;
                        var SRO = value.Sub_Reg_Office_Name;
                        var html = '<option value="' + Id + '">' + SRO + '</option>';
                        $('#dropName_SubRegistrar_Office').append(html);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                var SubRegistrarOffice = $("#SubRegistrar_Office").val();
                if (SubRegistrarOffice != "") {
                    $("#dropName_SubRegistrar_Office option:contains(" + SubRegistrarOffice + ")").attr('selected', 'selected');
                    $("#SubRegistrar_Office").val('');
                }
            });
        }


        function Save() {

            var int_loanappid = getParameterByName("LoanappId");
            var landid = getParameterByName("Id");
            var ApplicationRegNo = $("#txtApplicationRegNo");
            var TitleHolder = $("#txtName_Of_Title_Holder");
            var FatherName = $("#txtName_Of_Father");
            var HouseName = $("#txtHouse_Name_Number");
            var Lane1 = $("#txtLane1");
            var Lane2 = $("#txtLane2");
            var PinCode = $("#txtPincode");
            var PostOffice = $("#txtPost_Office");
            var PhoneNo = $("#txtPhone_Number");
            var Village = $("#dropVillage option:selected");
            var Taluk = $("#dropTaluk");  
            var District = $("#txtLand_District");
            var DeedType = $("#dropdeedtype").val();
            if (DeedType == "Others") {
                DeedType = $('#txtDeedType_Others').val();
            }
            var DeedNo = $("#txtDeed_No");
            var SureveyNo = $("#txtSurvey_No");
            var Extent = $("#txtExtent");
            var Tandaperu = $("#txtTandaperu");
            var ValuationAmount = $("#txtValuation_Amount");
            var LandVillage = $("#dropLand_Village option:selected");
            var LandTaluk = $("#dropTaluk_2");  
            var LandDistrict = $("#txtLand_District_2");
            var SRO = $("#dropName_SubRegistrar_Office option:selected");

            var ckboxFair = $('#checkIf_Fairvalue_Certificate');
            var Fair = "0";
            if (ckboxFair.is(':checked')) {
                Fair = "1";
            }

            if (TitleHolder.val().trim() == "") {
                Toast.fire({
                    icon: 'error',
                    title: 'Name of Loanne is required !'
                })
            }
            else if (!Is_Valid_Text(TitleHolder.val().trim())) {
                //  alert("Name is required");
                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed !'
                })
            }
            else {
                $("#btnSave").css("background-color", "#000");
                $("#btnSave").attr("disabled", "disabled");
                $("#btnSave").html("Please wait !");
                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/Update_To_tbl_landsurety",
                    data: JSON.stringify({ Id: landid, int_loanappid: int_loanappid, vchr_apprecregno: ApplicationRegNo.val(), vchr_ownername: TitleHolder.val(), vchr_hsename: HouseName.val(), vchr_lane1: Lane1.val(), vchr_lane2: Lane2.val(), vchr_post: PostOffice.val(), int_pin: PinCode.val(), vchr_phno: PhoneNo.val(), vchr_deedtype: DeedType, vchr_deedno: DeedNo.val(), vchr_subregoffiname: SRO.text(), vchr_surveyno: SureveyNo.val(), vchr_extent: Extent.val(), vchr_tpno: Tandaperu.val(), vchr_village: Village.text(), vchr_taluk: Taluk.val(), vchr_district: District.val(), vchr_land_village: LandVillage.text(), vchr_land_taluk: LandTaluk.val(), vchr_land_district: LandDistrict.val(), dte_conf_send_date: "01-01-001", dte_conf_rec_date: "01-01-001", int_loanno: 0, vchr_fathername: FatherName.val(), vchr_documentno: "", int_ValAmt: ValuationAmount.val(), vchr_Ashwas: "", int_LoaneeH: 0, int_fair: Fair, int_NotPledged: 0 }), // If you have parameters
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {

                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        // Handle AJAX error
                        //   alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);

                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                        })

                    }

                }).done(function () {
                    //  Update_Status();
                    $("#btnSave").css("background-color", "#c8328b");
                    $("#btnSave").removeAttr("disabled");
                    $("#btnSave").html("Submit");
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Guarantor Land Details Successfully Updated !',
                        allowOutsideClick: false,

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked

                            location.href = 'Surety_Modification_List.aspx';
                            // Your code here
                        }
                    });

                });


            }




        }


        function Update_Status() {
            //alert(Counter);
            var loanapp_Id = getParameterByName("Id");
            var Type = 'Land';
            var Status = 'Surety Entry Done';



            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/New_Update_To_tbl_loanapp_Surety_Status_And_Type",
                data: JSON.stringify({ int_loanappid: loanapp_Id, status: Status, type: Type }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                },
                error: function (jqXHR, textStatus, errorThrown) {
                    // Handle AJAX error
                    alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                    })

                }

            }).done(function () {
                Swal.fire({
                    icon: 'success',
                    title: 'Message',
                    text: 'Guarantor Land Details Successfully Saved !',
                    allowOutsideClick: false,

                }).then((result) => {
                    if (result.isConfirmed) {
                        // OK button clicked

                        location.href = 'Surety_Land_List.aspx';
                        // Your code here
                    }
                });

            });




        }

        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }


        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }



    </script>
</asp:Content>

