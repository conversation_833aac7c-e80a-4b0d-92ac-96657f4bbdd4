//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CorpNet_To_KSDC_SMART
{
    using System;
    
    public partial class Select_tbl_loanreg_By_RegNo_Or_LoanNo_Result
    {
        public string vchr_appreceivregno { get; set; }
        public string Int_Loanno { get; set; }
        public Nullable<int> Old_Scheme_Id { get; set; }
        public string Old_Scheme_Abrivation { get; set; }
        public string Old_GLHDNAME { get; set; }
        public Nullable<int> Old_ACCHDCODE { get; set; }
        public string Old_Scheme_Name { get; set; }
        public Nullable<System.DateTime> dte_agrement_date { get; set; }
        public Nullable<System.DateTime> dte_dateoforder { get; set; }
        public Nullable<decimal> mny_Loanamt { get; set; }
        public Nullable<decimal> mny_disbamt { get; set; }
        public Nullable<System.DateTime> dt_disbdate { get; set; }
        public Nullable<decimal> int_disb_inst { get; set; }
        public Nullable<decimal> int_inst_disbursed { get; set; }
        public Nullable<decimal> int_rate_int { get; set; }
        public Nullable<decimal> int_rate_penal { get; set; }
        public Nullable<decimal> mny_repayamt { get; set; }
        public Nullable<decimal> int_repay_inst { get; set; }
        public Nullable<decimal> mny_loanbal { get; set; }
        public Nullable<decimal> mny_prin_due { get; set; }
        public Nullable<decimal> mny_int_due { get; set; }
        public Nullable<decimal> mny_penal_due { get; set; }
        public Nullable<System.DateTime> dt_last_repay_date { get; set; }
        public Nullable<System.DateTime> dt_first_due_date { get; set; }
        public Nullable<System.DateTime> dt_next_due_date { get; set; }
        public Nullable<decimal> mny_intramt { get; set; }
        public Nullable<decimal> mny_intr_emi { get; set; }
        public Nullable<decimal> mny_intrbal { get; set; }
        public string vchr_stage { get; set; }
        public string vchr_offid { get; set; }
        public string vchr_oldloanno { get; set; }
        public Nullable<decimal> int_ots { get; set; }
        public Nullable<decimal> int_rvcd { get; set; }
        public Nullable<System.DateTime> dt_rvcd_date { get; set; }
        public string vchr_RRCno { get; set; }
        public Nullable<System.DateTime> dt_RR_Date { get; set; }
        public Nullable<decimal> int_RR { get; set; }
        public Nullable<decimal> int_Prefix { get; set; }
        public Nullable<decimal> int_ForClosing { get; set; }
        public Nullable<decimal> int_Green { get; set; }
        public string vchr_verify_remark { get; set; }
        public Nullable<decimal> int_rrdemand { get; set; }
        public string vchr_TRRCno { get; set; }
        public string vchr_offidC { get; set; }
        public Nullable<decimal> int_DefaultOts { get; set; }
        public string vchr_RRRemarks { get; set; }
        public Nullable<decimal> int_Ashwas { get; set; }
        public Nullable<decimal> int_MTR { get; set; }
        public Nullable<decimal> int_Ashwas_M { get; set; }
        public Nullable<decimal> int_repay_inst_Ext { get; set; }
        public Nullable<decimal> int_SR { get; set; }
        public Nullable<System.DateTime> dt_SRDate { get; set; }
    }
}
