﻿<%@ Page Title="Application Data Entry - Computer Loan For Student | View" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="DE_Computer_Loan_For_Student_View.aspx.cs" Inherits="KSDCSCST_Portal.DE_Computer_Loan_For_Student" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>To be filled for Computer Loan for Student</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="Welcome.aspx">Home</a></li>
						  <li class="breadcrumb-item  "><a href="ApplicationDataEntry_List.aspx">Application DataEntry</a><Application Issue</li>
                        <li class="breadcrumb-item active">To be filled for Computer Loan for Student</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name Of Student*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtName_Of_Student" placeholder="Name Of Student">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name Of Course*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtName_Of_Course" placeholder="Name Of Course">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name Of Institution*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtInstitution_Name" placeholder="Name Of Institution">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Address of the Institution*</label>
                                        <div class="col-sm-9">
                                            <textarea class="form-control" maxlength="200" id="txtInstitution_Address" placeholder="Address of the Institution"></textarea>
                                            <span class="validation_message">Maximum text length is 200</span>
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Year Of Completion*</label>
                                        <div class="col-sm-9">
                                           
										   <select id="dropYear_Of_Completion" class="form-control">
										   <option>[Select]</option>
                                            </select>
                                        </div>
                                    </div>
									
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Month Of Completion*</label>
                                        <div class="col-sm-9">
                                            <select id="dropMonth_Of_Completion" class="form-control">
											<option>[Select]</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Details of Computer/Hardware to be purchased*</label>
                                        <div class="col-sm-9">
                                            <textarea class="form-control" maxlength="200" id="txtDetails_Computer_Or_Hardware" placeholder="Details of Computer/Hardware to be purchased"></textarea>
                                            <span class="validation_message">Maximum text length is 200</span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Invoice Amount*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtInvoice_Amount" placeholder="Invoice Amount">
                                        </div>
                                    </div>








                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                            
                                            <a onclick="Save();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <script src="assets/plugins/toastr/toastr.min.js"></script>



    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.js"></script>

    <script>
        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;
        $(document).ready(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            // Get the current date
            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtApplication_Date').val(formattedDate);

            $("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
            $("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");
            Load_All_Schemes();
            //   Load_All_Districts();

            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }


        });




        function Load_All_Districts() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropDistrict').empty();
                    $('#dropDistrict').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var District_Name = value.District_Name;
                        var html = '<option value="' + Id + '">' + District_Name + '</option>';
                        if (Id != 15) {
                            $('#dropDistrict').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $('#dropDistrict').change(function () {
                    Load_All_Sub_Districts($(this).val());
                });

            });

        }
        function Load_All_Sub_Districts(District_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Districts",
                data: JSON.stringify({ District_Id: District_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubDistrict').empty();
                    $('#dropSubDistrict').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Sub_District_Name = value.Sub_District_Name;
                        var html = '<option value="' + Id + '">' + Sub_District_Name + '</option>';

                        $('#dropSubDistrict').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });

        }

        function Load_All_Schemes() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Schemes",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropScheme').empty();
                    $('#dropScheme').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var IsActive = value.IsActive;
                        var Scheme = value.Scheme;
                        var html = '<option value="' + Id + '">' + Scheme + '</option>';
                        if (IsActive == 1) {
                            $('#dropScheme').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_All_Cast();

            });

        }

        function Load_All_Cast() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Cast",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropCast').empty();
                    $('#dropCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var Cast = value.Cast;
                        var html = '<option value="' + Id + '">' + Cast + '</option>';

                        $('#dropCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $('#dropCast').change(function () {
                    Load_All_SubCast($(this).val());
                });

            });

        }


        function Load_All_SubCast(Cast_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Cast",
                data: JSON.stringify({ Cast_Id: Cast_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubCast').empty();
                    $('#dropSubCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var SubCast = value.SubCast;
                        var html = '<option value="' + Id + '">' + SubCast + '</option>';

                        $('#dropSubCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {


            });

        }
        function Save() {

            Swal.fire({
                icon: 'success',
                title: 'Message',
                text: 'Application Data Entry Successfully Submitted!',

            }).then((result) => {
                if (result.isConfirmed) {
                    // OK button clicked
                    location.href = 'ApplicationDataEntry_List.aspx';
                    // location.href = 'ApplicationDataEntry_View.aspx';
                    // Your code here
                }
            });
            //alert(Counter);



            //var district_Id = $("#dropDistrict").val();

            //var subDistrict_id = $("#dropSubDistrict").val();
            //var name_Applicant = $("#txtApplicant_Name").val();
            //var scheme_Id = $("#dropScheme").val();

            //var cast_Id = $("#dropCast").val();
            //var sub_Cast_Id = $("#dropSubCast").val();

            //var remarks = $("#txtRemarks").val();
            //var PhoneNo = $("#txtPhoneNo").val();

            //if (name_Applicant.trim() == "") {
            //    //  alert("Name is required");
            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Name of Applicant is required !'
            //    })
            //}
            //else if (!Is_Valid_Text(name_Applicant.trim())) {
            //    //  alert("Name is required");
            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Special characters or Numbers not allowed !'
            //    })
            //}
            //else if (scheme_Id == "0") {

            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Loan Scheme is required !'
            //    })
            //}
            //else if (cast_Id == "0") {

            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Cast is required !'
            //    })
            //}
            //else if (sub_Cast_Id == "0") {

            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Sub Cast is required !'
            //    })
            //}
            //else if (PhoneNo.trim() == "") {

            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Phone No is required !'
            //    })
            //}


            //else {
            //    if (PhoneNo.trim() != "") {

            //        var values = PhoneNo.trim().split(",");
            //        for (var i = 0; i < values.length; i++) {
            //            if (values[values.length - 1] != "") {
            //                if (!Is_Valid_Mobile_Number(values[i])) {
            //                    Toast.fire({
            //                        icon: 'error',
            //                        title: 'Enter valid phone number !'
            //                    })
            //                    return;
            //                }
            //            }
            //        }
            //    }
            //    $.ajax({
            //        type: "POST",
            //        dataType: "json",
            //        url: "WebService.asmx/Insert_To_tbl_Loan_App_Issue",
            //        data: JSON.stringify({ district_Id: district_Id, subDistrict_id: subDistrict_id, name_Applicant: name_Applicant, scheme_Id: scheme_Id, cast_Id: cast_Id, sub_Cast_Id: sub_Cast_Id, remarks: remarks, PhoneNo: PhoneNo }), // If you have parameters
            //        contentType: "application/json; charset=utf-8",
            //        success: function (data) {

            //        },
            //        error: function (jqXHR, textStatus, errorThrown) {
            //            // Handle AJAX error
            //            //   alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);

            //            Swal.fire({
            //                icon: 'error',
            //                title: 'Oops...',
            //                text: 'Contact your administrator for more information, Email Id: <EMAIL>',

            //            })

            //        }

            //    }).done(function () {
            //        Swal.fire({
            //            icon: 'success',
            //            title: 'Message',
            //            text: 'Application Issue Successfully Saved !',

            //        }).then((result) => {
            //            if (result.isConfirmed) {
            //                // OK button clicked

            //                location.href = 'ApplicationIssue_List.aspx';
            //                // Your code here
            //            }
            //        });

            //    });


            //}




        }


        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }


        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }



    </script>
</asp:Content>
