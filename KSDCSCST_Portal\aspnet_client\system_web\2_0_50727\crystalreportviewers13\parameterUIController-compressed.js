if(typeof bobj=="undefined"){bobj={}}bobj.Colors={BLACK:"#000000",GRAY:"#a5a5a5"};bobj.crv.params.newTextField=function(a){a=MochiKit.Base.update({id:bobj.uniqueId(),cleanValue:"",width:"100%",maxChar:null,tooltip:null,disabled:false,editable:true,password:false,focusCB:null,blurCB:null,changeCB:null,keyUpCB:null,enterCB:null,foreColor:"black",isTextItalic:false,canOpenAdvDialog:false},a);var b=newTextFieldWidget(a.id,a.changeCB,a.maxChar,a.keyUpCB,a.enterCB,true,a.tooltip,null,a.focusCB,a.blurCB);b.widgetType="TextField";bobj.fillIn(b,a);b.disabled=a.disabled;b.width=a.width;MochiKit.Base.update(b,bobj.crv.params.TextField);if(a.cleanValue){b.setValue(a.cleanValue)}return b};bobj.crv.params.TextField={setForeColor:function(a){this.foreColor=a;if(this.css){this.css.color=a}},setTextItalic:function(a){this.isTextItalic=a;if(this.css){this.css.fontStyle=a?"italic":""}},setTabDisabled:function(a){bobj.disableTabbingKey(this.layer,a)},eraseHelpTxt:MochiKit.Base.noop,getHTML:function(){var d={width:bobj.unitValue(this.width)};var c=MochiKit.Base.isIE();var b="iactTextField";var a={type:this.password?"password":"text",name:this.id,id:this.id,maxLength:this.maxChar,style:d,"class":b,oncontextmenu:"event.cancelBubble=true;return true",onfocus:"TextFieldWidget_focus(this)",onblur:"TextFieldWidget_blur(this)",onchange:"TextFieldWidget_changeCB(event, this)",onkeydown:"return TextFieldWidget_keyDownCB(event, this);",onkeyup:"return TextFieldWidget_keyUpCB(event, this);",onkeypress:"return TextFieldWidget_keyPressCB(event, this);",ondragstart:"event.cancelBubble=true; return true;",onselectstart:"event.cancelBubble=true; return true;"};if(this.disabled){a.disabled="disabled"}if(this.isTextItalic){d["font-style"]="italic"}d.color=this.foreColor;if(!this.editable){a.readonly="readonly";if(this.canOpenAdvDialog){d.cursor="pointer"}else{d.cursor="default"}}if(this.tooltip){a.title=this.tooltip.replace(/"/g,"&quot;")}return bobj.html.INPUT(a)},reset:function(a){this.value=a;this.cleanValue=a;this.setValue(a)},setValue:function(a){TextFieldWidget_setValue.call(this,a)},setCleanValue:function(a){this.cleanValue=a}};bobj.crv.params.newTextCombo=function(a){var d=MochiKit.Base.update;var b=bobj.crv.params;a=d({id:bobj.uniqueId(),width:"100%",maxChar:null,tooltip:null,disabled:false,editable:false,changeCB:null,enterCB:null,keyUpCB:null,isTextItalic:false},a);var c=newTextComboWidget(a.id,a.maxChar,a.tooltip,null,a.changeCB,null,null,null);c.widgetType="TextCombo";bobj.fillIn(c,a);c.width=a.width;c.init_TextCombo=c.init;d(c,b.TextCombo);c._createTextField();c._createArrow();c.arrow.dy+=2;c.arrow.disDy+=2;return c};bobj.crv.params.TextCombo={setTextItalic:function(a){if(this.text){this.text.setTextItalic(a)}},setForeColor:function(a){if(this.text){this.text.setForeColor(a)}},setTooltip:function(a){if(this.text){this.text.setTooltip(a)}},setTabDisabled:function(a){if(this.text){this.text.setTabDisabled(a)}if(this.arrow){bobj.disableTabbingKey(this.arrow.layer,a)}},setMenu:function(a){this.menu=a},init:function(){this.init_TextCombo();this.arrowContainer=getLayer(this.id+"_arrowCtn");if(this.arrow){this.arrow.layer.onfocus=IconWidget_realOverCB;this.arrow.layer.onblur=IconWidget_realOutCB}this.text.setValue(this.cleanValue)},toggleMenu:function(){var b=this.menu;b.parIcon=this;var a=!b.isShown();b.show(a);if(a){b.valueSelect(this.text.getValue()+"")}},_createArrow:function(){var a=_openMenu.replace("{0}",this.tooltip?this.tooltip:"");this.arrow=newIconWidget(this.id+"arrow_",bobj.skinUri("menus.gif"),bobj.bindFunctionToObject(this.toggleMenu,this),null,a,7,12,0,83,0,99);this.arrow.setClasses("iconnocheck","combobtnhover","combobtnhover","combobtnhover");this.arrow.par=this},_createTextField:function(){this.text=bobj.crv.params.newTextField({id:this.id+"_text",cleanValue:this.cleanValue,width:"100%",maxChar:null,tooltip:this.tooltip,disabled:false,editable:this.editable,password:false,focusCB:this.focusCB,blurCB:this.blurCB,keyUpCB:bobj.bindFunctionToObject(this._onKeyUp,this),enterCB:this.enterCB,foreColor:this.foreColor,isTextItalic:this.isTextItalic})},getHTML:function(){var d=bobj.html;var b="iactTextComboArrow";var a={};a.right="0px";if(MochiKit.Base.isIE()){a.height="18px"}else{a.height="16px"}var c=d.DIV({id:this.id,style:{width:"100%",position:"relative"}},d.DIV({style:{position:"relative"},"class":"iactTextComboTextField"},this.text.getHTML()),d.DIV({"class":b,id:this.id+"_arrowCtn",style:a},this.arrow.getHTML()));return c},reset:function(a){this.text.reset(a)},setValue:function(a){this.text.setValue(a)},setCleanValue:function(a){this.text.setCleanValue(a)},selectItem:function(a){if(a){this.val=a.value;this.text.setValue(a.value,true);this.menu.select(a.index)}},getValue:function(){return this.text.getValue()},_onKeyUp:function(a){var b=this.text.getValue();if(this.keyUpCB){this.keyUpCB(a)}}};bobj.crv.params.newScrollMenuWidget=function(a){a=MochiKit.Base.update({id:bobj.uniqueId(),originalValues:[],hasProperWidth:false,hasValueList:false,maxVisibleItems:10,openAdvDialogCB:null,maxNumParameterDefaultValues:null},a);var c=(a.originalValues.length>=a.maxVisibleItems)?a.maxVisibleItems:a.originalValues.length;if(c===1){c++}var b=newScrollMenuWidget("menu_"+a.id,bobj.crv.params.ScrollMenuWidget.onChange,false,null,c,null,null,null,false,"","",null,null);b.oldShow=b.show;MochiKit.Base.update(b,a,bobj.crv.params.ScrollMenuWidget);return b};bobj.crv.params.ScrollMenuWidget={onChange:function(){var b=this.parIcon;var a=this.getSelection();if(a){if(this.maxNumParameterDefaultValues&&a.index==this.maxNumParameterDefaultValues){if(this.openAdvDialogCB){this.openAdvDialogCB();this.clearSelection()}}else{b.val=a.value;b.text.setValue(a.value)}}else{b.val=null;b.text.setValue("")}if(b.changeCB){b.changeCB()}},getPosition:function(){if(this.parIcon===null){return}var c=this.parIcon.layer;var b=MochiKit.Style.getElementDimensions;var a=getPosScrolled(c);var e=a.x+2;var d=a.y+b(c).h+3;if(MochiKit.Base.isIE()){e-=1;if(bobj.isQuirksMode()){d-=2}}return{x:e,y:d}},setProperWidth:function(){if(this.hasProperWidth===false){this.css.display="block";this.orginalWidth=this.layer.offsetWidth;this.css.display="none";this.hasProperWidth=true}},setValueList:function(){if(this.hasValueList===false){this.hasValueList=true;var b=this.originalValues;for(var c=0,a=b.length;c<a;c++){this.add(b[c],b[c],false)}}},setFocus:function(a){if(a){var b=bobj.bindFunctionToObject(this.list.focus,this.list);setTimeout(b,300)}else{if(this.parIcon.selected===true){this.parIcon.arrow.focus()}}},show:function(a){if(this.layer===null){this.justInTimeInit()}if(this.hasValueList===false){this.setValueList()}if(this.parIcon===null){return}if(this.hasProperWidth===false){this.setProperWidth()}if(this.parIcon&&this.parIcon.layer){var b=this.parIcon.layer;if(b.clientWidth>this.orginalWidth){this.css.width=b.clientWidth+"px";this.list.css.width=b.clientWidth+"px"}else{this.css.width=this.orginalWidth+"px";this.list.css.width=this.orginalWidth+"px"}}var c=this.getPosition();this.oldShow(a,c.x,c.y);this.setFocus(a)}};bobj.crv.params.newRangeField=function(a){return new bobj.crv.params.RangeField(a)};bobj.crv.params.RangeField=function(a){a=MochiKit.Base.update({id:bobj.uniqueId(),cleanValue:{},foreColor:"black",isTextItalic:false,tooltip:""},a);this.widgetType="RangeField";this.value=a.cleanValue;bobj.fillIn(this,a)};bobj.crv.params.RangeField.prototype={setTabDisabled:function(b){if(this.layer){var d=this.layer.getElementsByTagName("TD");for(var c=0,a=d.length;c<a;c++){bobj.disableTabbingKey(d[c],b)}}},setTooltip:function(a){if(this.layer){this.layer.title=a}},setForeColor:function(a){if(this.layer){this.layer.style.color=a}},setTextItalic:function(a){if(this.layer){this.layer.style.fontStyle=a?"italic":""}},getValueHTML:function(c,a){c=c?c:"&nbsp;";var b={"text-align":a?(bobj.crv.config.isRTL?"left":"right"):(bobj.crv.config.isRTL?"right":"left")};var d="";if(MochiKit.Base.isIE()){d=bobj.html.SPAN({style:{"white-space":"nowrap"}},c)}else{d=bobj.html.SPAN(null,c)}return bobj.html.TD({style:b,role:"presentation"},d)},isValueValidRange:function(){return this.value!=null&&this.value.lowerBound!=null&&this.value.upperBound!=null},getHTML:function(){var b="";var a=bobj.html;if(this.isValueValidRange()){b=a.TR(null,this.getValueHTML(this.getValue().lowerBound.value,true),this.getMiddleImageHTML(),this.getValueHTML(this.getValue().upperBound.value,false))}else{b=a.TR(null,this.getValueHTML(this.getValue(),false))}return a.TABLE({id:this.id,"class":"iactRangeFieldTable",role:"group",title:this.tooltip+" "+this._getRangeValueToolTip(),style:{color:this.foreColor,"font-style":this.isTextItalic?"italic":""}},b)},_getRangeValueToolTip:function(){var b="";if(this.isValueValidRange()){var a=bobj.crv.params.RangeBoundTypes;switch(this.getValue().lowerBound.type){case a.EXCLUSIVE:b+="("+this.getValue().lowerBound.value;break;case a.INCLUSIVE:b+="["+this.getValue().lowerBound.value;break;case a.UNBOUNDED:b+="( ";break}b+="..";switch(this.getValue().upperBound.type){case a.EXCLUSIVE:b+=this.getValue().upperBound.value+")";break;case a.INCLUSIVE:b+=this.getValue().upperBound.value+"]";break;case a.UNBOUNDED:b+=" )";break}}else{b+=this.getValue()}return b},getMiddleImageHTML:function(){var e="";var c="";var a="images/line.gif";switch(this.getValue().lowerBound.type){case bobj.crv.params.RangeBoundTypes.EXCLUSIVE:e="images/hollowCircle.gif";break;case bobj.crv.params.RangeBoundTypes.INCLUSIVE:e="images/filledCircle.gif";break;case bobj.crv.params.RangeBoundTypes.UNBOUNDED:if(bobj.crv.config.isRTL){e="images/rightTriangle.gif"}else{e="images/leftTriangle.gif"}break}switch(this.getValue().upperBound.type){case bobj.crv.params.RangeBoundTypes.EXCLUSIVE:c="images/hollowCircle.gif";break;case bobj.crv.params.RangeBoundTypes.INCLUSIVE:c="images/filledCircle.gif";break;case bobj.crv.params.RangeBoundTypes.UNBOUNDED:if(bobj.crv.config.isRTL){c="images/leftTriangle.gif"}else{c="images/rightTriangle.gif"}break}var b={"vertical-align":"middle"};var d=bobj.html.IMG;return bobj.html.TD({style:b},d({src:bobj.crvUri(e)}),d({src:bobj.crvUri(a)}),d({src:bobj.crvUri(c)}))},init:function(){this.layer=getLayer(this.id)},getLowerBoundValueWidth:function(){if(!this.isValueValidRange()){return 0}var c=this.getValue().lowerBound.value;var a=MochiKit.Style.computedStyle(this.layer,"fontFamily");var b=MochiKit.Style.computedStyle(this.layer,"fontSize");if(!a){a="arial , sans-serif"}if(!b){b="12px"}return bobj.getStringWidth(c,a,b)},getLowerBoundTD:function(){if(this.layer){return this.layer.getElementsByTagName("TD")[0]}return null},setLowerBoundValueWidth:function(a){if(this.getLowerBoundTD()){this.getLowerBoundTD().style.width=a+"px"}},reset:function(a){this.value=a;this.cleanValue=a;this.updateUI()},updateUI:function(){var a=this.layer.parentNode;MochiKit.DOM.removeElement(this.layer);append2(a,this.getHTML());this.init()},setValue:function(a){this.value=a;this.updateUI()},setCleanValue:function(a){this.cleanValue=a},getValue:function(){return this.value}};bobj.crv.params.ParameterInfoRow=function(a){this.layer=null;this.parentId=a;this.id=bobj.uniqueId()};bobj.crv.params.ParameterInfoRow.prototype={setTabDisabled:function(a){if(this.layer){bobj.disableTabbingKey(this.layer,a)}},init:function(){var a=getLayer(this.parentId);if(a){append2(a,this.getHTML());this.layer=getLayer(this.id)}if(this.layer){MochiKit.Signal.connect(this.layer,"onclick",this,"_onClick");MochiKit.Signal.connect(this.layer,"onkeydown",this,"_onKeyDown")}},getHTML:function(){return bobj.html.DIV({"class":"parameterInfoRow",id:this.id,tabIndex:"0"})},setText:function(a){if(!this.layer){this.init()}this.layer.innerHTML=a},setVisible:function(a){if(a){if(!this.layer){this.init()}this.shiftToLastRow();this.layer.style.display="block"}else{if(this.layer){this.layer.style.display="none"}}},shiftToLastRow:function(){var a=getLayer(this.parentId);if(this.layer&&a){a.removeChild(this.layer);a.appendChild(this.layer)}},_onClick:function(a){MochiKit.Signal.signal(this,"switch");a.stop()},_onKeyDown:function(a){if(a&&a.key()&&a.key().code==13){MochiKit.Signal.signal(this,"switch");a.stop()}}};bobj.crv.params.newParameterValueRow=function(a){a=MochiKit.Base.update({id:bobj.uniqueId(),value:"",defaultValues:null,isReadOnlyParam:true,canChangeOnPanel:false,isRangeValue:false,allowCustom:false,isPassword:false,calendarProperties:{displayValueFormat:"",isTimeShown:false,hasButton:false,iconUrl:"",clickCB:null},changeCB:null,enterCB:null,defaultValuesMenu:null,tooltip:null,canOpenAdvDialog:false},a);var b=newWidget(a.id);b.widgetType="ParameterValueRow";b._prevValueString=a.value;b._warning=null;bobj.fillIn(b,a);MochiKit.Base.update(b,bobj.crv.params.ParameterValueRow);return b};bobj.crv.params.ParameterValueRow={setTabDisabled:function(a){if(this._valueWidget){this._valueWidget.setTabDisabled(a)}if(this._calendarButton){bobj.disableTabbingKey(this._calendarButton.layer,a)}},reset:function(a){this.value=a;this._prevValueString=a;this.setWarning(null);MochiKit.DOM.removeElementClass(this.layer,"hasError");if(this._valueWidget){this._valueWidget.reset(a)}},init:function(){Widget_init.call(this);this._valueWidget.init();if(this._calendarButton){this._calendarButton.init()}this._valueCtn=getLayer(this.id+"_vc");this._btnCtn=getLayer(this.id+"_bc");this._valBtnCtn=getLayer(this.id+"_vab");if(MochiKit.Base.isIE()){var a=parseInt(MochiKit.Style.computedStyle(this._valueCtn,"margin-right"),10);if(bobj.isNumber(a)){this._valueWidget.layer.style.marginRight=(-1*a)+"px"}}},calendarSetValue:function(a){this.setValue(a);this.changeCB()},getHTML:function(){if(!this._valueWidget){this._valueWidget=this._getValueWidget()}if(this.calendarProperties.hasButton&&!this._calendarButton){var c=bobj.bindFunctionToObject(this.getValue,this);var b=bobj.bindFunctionToObject(this.calendarSetValue,this);this._calendarButton=bobj.crv.params.newCalendarButton({calendarProperties:this.calendarProperties,getValueCB:c,setValueCB:b})}var d=bobj.html.DIV;var e=bobj.html.IMG;var a=" iactParamRow";if(this.canChangeOnPanel){a+=" editable"}else{a+=" readOnly"}if(MochiKit.Base.isIE()&&bobj.isQuirksMode()){a+=" iactParamRowIE"}return d({id:this.id,"class":a},this.calendarProperties.hasButton?this._getValueAndCalendarHTML():this._getValueHTML())},_getValueHTML:function(){var b=bobj.html.DIV;var a={};if(MochiKit.Base.isIE()&&bobj.isQuirksMode()){a.position="absolute";a.top="0px";a.left="0px"}return b({id:this.id+"_vc","class":"iactParamValue",style:a},this._valueWidget.getHTML())},_getValueAndCalendarHTML:function(){var c={};if(MochiKit.Base.isIE()&&bobj.isQuirksMode()){c.width="100%"}var d=bobj.html.DIV;var a=(this._valueWidget.widgetType=="TextCombo")?16:0;a+="px";var b=d({id:this.id+"_vab",style:c,"class":"iactParamValueAndButton"},this._getValueHTML(),d({id:this.id+"_bc","class":"iactValueIcon",style:{position:"absolute",right:a,top:"0",cursor:_hand}},this._calendarButton.getHTML()));return b},getNewValueWidgetConstructor:function(){var a=this.defaultValuesMenu!==null&&!this.isReadOnlyParam&&this.canChangeOnPanel;if(this.isRangeValue){return bobj.crv.params.newRangeField}else{if(a){return bobj.crv.params.newTextCombo}else{return bobj.crv.params.newTextField}}},getNewValueWidgetArgs:function(){var a=this.allowCustom&&this.canChangeOnPanel;return{password:this.isPassword,cleanValue:this.value,editable:a,enterCB:bobj.bindFunctionToObject(this._onEnterPress,this),keyUpCB:a?bobj.bindFunctionToObject(this._onKeyUp,this):null,tooltip:this.tooltip,foreColor:this.isReadOnlyParam?bobj.Colors.GRAY:bobj.Colors.BLACK,focusCB:bobj.bindFunctionToObject(this.onFocus,this),blurCB:bobj.bindFunctionToObject(this.onBlur,this),canOpenAdvDialog:this.canOpenAdvDialog}},_getValueWidget:function(){var a=this.getNewValueWidgetConstructor();var b=a(this.getNewValueWidgetArgs());var c=this.defaultValuesMenu!==null&&!this.isReadOnlyParam&&this.canChangeOnPanel;if(c){b.setMenu(this.defaultValuesMenu);b.changeCB=bobj.bindFunctionToObject(this._onChange,this)}return b},onFocus:function(){this.refreshWarningPopup();MochiKit.DOM.removeElementClass(this.layer,"hasError")},refreshWarningPopup:function(){if(this._warning){var g=getPosScrolled(this.layer);var b=MochiKit.Style.computedStyle(this.layer,"fontFamily");var e=MochiKit.Style.computedStyle(this.layer,"fontSize");var a=bobj.getStringWidth(this.getValue(),b,e);var d=this.layer.offsetWidth<a?this.layer.offsetWidth:a;var c=33;bobj.crv.WarningPopup.getInstance().show(this._warning.message,g.x+d,g.y+c)}else{bobj.crv.WarningPopup.getInstance().hide();MochiKit.DOM.removeElementClass(this.layer,"hasError")}var f=this._warning?this.tooltip+this._warning.message:this.tooltip;if(this._valueWidget){this._valueWidget.setTooltip(f)}},onBlur:function(){if(this._warning){bobj.crv.WarningPopup.getInstance().hide();MochiKit.DOM.addElementClass(this.layer,"hasError")}},getValue:function(){return this.value},setValue:function(a){this.value=a;if(this._valueWidget){this._valueWidget.setValue(a)}},setCleanValue:function(a){if(this._valueWidget){this._valueWidget.setCleanValue(a)}},focus:function(){if(this._valueWidget.widgetType=="TextCombo"){this._valueWidget.text.focus()}else{this._valueWidget.focus()}},setWarning:function(a){this._warning=a;this.refreshWarningPopup()},getWarning:function(){return this._warning},resize:function(a,b){bobj.setOuterSize(this.layer,a,b)},deleteValue:function(){this._valueWidget.setValue("",true)},_onKeyUp:function(d){var c=new MochiKit.Signal.Event(src,d);var b=c.key().string;var a=this._valueWidget.getValue();switch(b){case"KEY_ESCAPE":this._valueWidget.setValue(this._valueWidget.cleanValue);this._onChange();break;case"KEY_ARROW_LEFT":case"KEY_ARROW_RIGHT":case"KEY_HOME":case"KEY_END":case"KEY_TAB":break;default:if(a!==this._prevValueString){this._onChange();this._prevValueString=a}break}},deleteValue:function(){this._valueWidget.setValue("",true)},_onChange:function(){this.value=this._valueWidget.getValue();if(this.changeCB){this.changeCB()}},_onEnterPress:function(){if(this.enterCB){this.enterCB()}}};bobj.crv.params.newCalendarButton=function(a){a=MochiKit.Base.update({id:bobj.uniqueId(),calendarProperties:null,getValueCB:null,setValueCB:null,calendarSignals:{okSignal:null,hideSignal:null,cancelSignal:null}},a);if(a.getValueCB==null||a.setValueCB==null||a.calendarProperties==null){throw"InvalidArgumentException"}var b=newIconWidget(a.id,a.calendarProperties.iconUrl,null,null,L_bobj_crv_ParamsCalBtn,14,14,0,0,0,0);b.setClasses("","","","iconcheckhover");bobj.fillIn(b,a);b.margin=0;b.oldInit=b.init;MochiKit.Base.update(b,bobj.crv.params.CalendarButton);b.clickCB=bobj.bindFunctionToObject(b.onClick,b);return b};bobj.crv.params.CalendarButton={onClick:function(){var e=bobj.crv.Calendar.getInstance();var d=bobj.external.date.getDateFromFormat(this.getValueCB(),this.calendarProperties.displayValueFormat);var c=MochiKit.Signal.connect;if(d){e.setDate(d)}this.calendarSignals={okSignal:c(e,e.Signals.OK_CLICK,this,"onClickCalendarOKButton"),cancelSignal:c(e,e.Signals.CANCEL_CLICK,this,"onClickCalendarCancelButton"),hideSignal:c(e,e.Signals.ON_HIDE,this,"onHideCalendar")};var b=getPosScrolled(this.layer);var a=b.x+this.getWidth();var f=b.y+this.getHeight()+1;e.setShowTime(this.calendarProperties.isTimeShown);e.show(true,a,f)},onClickCalendarOKButton:function(a){var b=bobj.external.date.formatDate(a,this.calendarProperties.displayValueFormat);this.setValueCB(b)},onClickCalendarCancelButton:function(){this.clearCalendarSignals()},clearCalendarSignals:function(){for(var a in this.calendarSignals){bobj.crv.SignalDisposer.dispose(this.calendarSignals[a],true);this.calendarSignals[a]=null}},onHideCalendar:function(){this.clearCalendarSignals();if(this.layer.focus){this.layer.focus()}},init:function(){this.oldInit();this.layer.onfocus=IconWidget_realOverCB;this.layer.onblur=IconWidget_realOutCB},getHTML:function(){var c;var b=bobj.html;if(this.src){c=b.DIV({style:{overflow:"hidden",height:"14px",position:"relative",top:"2px",width:this.w+"px"}},simpleImgOffset(this.src,this.w,this.h,this.dx,this.dy,"IconImg_"+this.id,null,this.alt,"cursor:"+_hand),this.extraHTML)}else{c=b.DIV({"class":"iconText",style:{width:"1px",height:(this.h+this.border)+"px"}})}var a={margin:this.margin+"px",padding:"1px"};if(this.width){a.width=this.width+"px"}if(!this.disp){a.display="none"}return b.DIV({style:a,id:this.id,"class":this.nocheckClass},(this.clickCB&&_ie)?lnk(c,null,null,null,' tabIndex="-1"'):c)}};bobj.crv.params.newOptionalParameterValueRow=function(a){a=MochiKit.Base.update({noValueDisplayText:"",isEmptyStringNoValue:true,clearValuesCB:null},a);var b=bobj.crv.params.newParameterValueRow(a);bobj.extendClass(b,bobj.crv.params.OptionalParameterValueRow,bobj.crv.params.ParameterValueRow);if(b.canChangeOnPanel){b._clearValueButton=newIconWidget(b.id+"_clearBtn",bobj.crvUri("images/clear_x.gif"),b.clearValuesCB,null,L_bobj_crv_ParamsClearValues,10,10,2,2);b._clearValueButton.setClasses("","iconcheck","","iconcheckhover");b._clearValueButton.margin=2}return b};bobj.crv.params.OptionalParameterValueRow={getNewValueWidgetArgs:function(){var a=this.superClass.getNewValueWidgetArgs();if(this.value==undefined){a.cleanValue=this.noValueDisplayText;a.foreColor=bobj.Colors.GRAY}return a},init:function(){this.superClass.init();if(this._clearValueButton){this._clearValueButton.init()}this.updateUI()},getHTML:function(){var a=this.superClass.getHTML();if(this.canChangeOnPanel){var b=bobj.html;a=b.DIV({style:{position:"relative"}},a,b.DIV({"class":"clearValueBtnCtn"},this._clearValueButton.getHTML()))}return a},onFocus:function(){this.superClass.onFocus();if(this.canChangeOnPanel){if(this.value==undefined){this._valueWidget.setValue("")}}},onBlur:function(){this.superClass.onBlur();if(this.canChangeOnPanel){if(this.value==undefined){this._valueWidget.setValue(this.noValueDisplayText)}}},_onChange:function(){this.value=this._valueWidget.getValue();if(this.isEmptyStringNoValue&&this.value!=null&&this.value.length==0){this.value=undefined}this.updateUI();if(this.changeCB){this.changeCB()}},updateUI:function(){if(this._valueWidget){if(this.isReadOnlyParam){this._valueWidget.setForeColor(bobj.Colors.GRAY);this._valueWidget.setTextItalic(false)}else{if(this.value==undefined){this._valueWidget.setForeColor(bobj.Colors.GRAY);if(this._clearValueButton){this._clearValueButton.setDisplay(false)}}else{this._valueWidget.setForeColor(bobj.Colors.BLACK);if(this._clearValueButton){this._clearValueButton.setDisplay(true)}}}}},getValueWidgetDisplayValue:function(a){return(a==undefined)?this.noValueDisplayText:a},setValue:function(a){this.value=a;this._valueWidget.setValue(this.getValueWidgetDisplayValue(a));this.updateUI();this.setWarning(null)},reset:function(a){this.superClass.reset(a);if(this._valueWidget){this._valueWidget.reset(this.getValueWidgetDisplayValue(a))}this.updateUI()}};bobj.crv.params.newParameterUI=function(a){a=MochiKit.Base.update({id:bobj.uniqueId(),canChangeOnPanel:false,allowCustom:false,isPassword:false,isReadOnlyParam:true,allowRange:false,values:[],defaultValues:null,width:"200px",changeValueCB:null,enterPressCB:null,openAdvDialogCB:null,maxNumParameterDefaultValues:200,tooltip:null,calendarProperties:{displayValueFormat:"",isTimeShown:false,hasButton:false,iconUrl:""},maxNumValuesDisplayed:7,canOpenAdvDialog:false},a);var b=newWidget(a.id);bobj.fillIn(b,a);b.displayAllValues=false;MochiKit.Base.update(b,bobj.crv.params.ParameterUI);b._createMenu();b._rows=[];b._infoRow=new bobj.crv.params.ParameterInfoRow(b.id);return b};bobj.crv.params.ParameterUI={_createMenu:function(){var a=this.defaultValues.length;if(a>0){var b={originalValues:this.defaultValues};if(a==this.maxNumParameterDefaultValues){b.originalValues[this.maxNumParameterDefaultValues]=L_bobj_crv_ParamsMaxNumDefaultValues;MochiKit.Base.update(b,{openAdvDialogCB:this.openAdvDialogCB,maxNumParameterDefaultValues:this.maxNumParameterDefaultValues})}this._defaultValuesMenu=bobj.crv.params.newScrollMenuWidget(b)}else{this._defaultValuesMenu=null}},setFocusOnRow:function(b){var a=this._rows[b];if(a){a.focus()}},setTabDisabled:function(b){for(var c=0,a=this._rows.length;c<a;c++){this._rows[c].setTabDisabled(b)}this._infoRow.setTabDisabled(b)},init:function(){Widget_init.call(this);var c=this._rows;for(var b=0,a=c.length;b<a;++b){c[b].init()}MochiKit.Signal.connect(this._infoRow,"switch",this,"_onSwitchDisplayAllValues");this.refreshUI()},_onSwitchDisplayAllValues:function(){this.displayAllValues=!this.displayAllValues;var a=10;var g=0;if(this.displayAllValues){if(this.values.length>this._rows.length){for(var f=this._rows.length,c=this.values.length;f<c;f++){var b=function(i,h){return function(){return i._addRow(h)}};g++;setTimeout(b(this,this.values[f]),a*g)}}}else{if(this._rows.length>this.maxNumValuesDisplayed){for(var f=this._rows.length-1;f>=this.maxNumValuesDisplayed;f--){var e=function(i,h){return function(){return i.deleteValue(h)}};g++;setTimeout(e(this,f),a*g)}}}var d=function(h){return function(){MochiKit.Signal.signal(h,"ParameterUIResized")}};setTimeout(d(this),a*g)},getHTML:function(){var e="";var a=this.values;var d=this._rows;var c=Math.min(a.length,this.maxNumValuesDisplayed);for(var b=0;b<c;++b){d.push(this._getRow(a[b]));e+=d[b].getHTML()}return bobj.html.DIV({id:this.id,style:{width:bobj.unitValue(this.width),"padding-left":"20px"}},e)},_getNewValueRowArgs:function(a){return{value:a,defaultValues:this.defaultValues,width:this.width,isReadOnlyParam:this.isReadOnlyParam,canChangeOnPanel:this.canChangeOnPanel,allowCustom:this.allowCustom,isPassword:this.isPassword,calendarProperties:this.calendarProperties,defaultValuesMenu:this._defaultValuesMenu,tooltip:this.tooltip,isRangeValue:this.allowRange,canOpenAdvDialog:this.canOpenAdvDialog}},_getNewValueRowConstructor:function(){return bobj.crv.params.newParameterValueRow},_getRow:function(a){var b=this._getNewValueRowConstructor()(this._getNewValueRowArgs(a));var c=MochiKit.Base.bind;b.changeCB=c(this._onChangeValue,this,b);b.enterCB=c(this._onEnterValue,this,b);return b},_addRow:function(a){var b=this._getRow(a);this._rows.push(b);append(this.layer,b.getHTML());b.init();this.refreshUI();return b},_onChangeValue:function(a){if(this.changeValueCB){this.changeValueCB(this._getRowIndex(a),a.getValue())}},_onEnterValue:function(a){if(this.enterPressCB){this.enterPressCB(this._getRowIndex(a))}},_getRowIndex:function(d){if(d){var c=this._rows;for(var b=0,a=c.length;b<a;++b){if(c[b]===d){return b}}}return -1},getNumValues:function(){return this._rows.length},refreshUI:function(){if(this.allowRange){this.alignRangeRows()}var b=false;var a="";if(this.values.length>this.maxNumValuesDisplayed){b=true;if(this.displayAllValues){a=L_bobj_crv_Collapse}else{var c=this.values.length-this.maxNumValuesDisplayed;a=(c==1)?L_bobj_crv_ParamsMoreValue:L_bobj_crv_ParamsMoreValues;a=a.replace("%1",c)}}this._infoRow.setText(a);this._infoRow.setVisible(b)},getValueAt:function(a){var b=this._rows[a];if(b){return b.getValue()}return null},getValues:function(){var b=[];for(var c=0,a=this._rows.length;c<a;++c){b.push(this._rows[c].getValue())}return b},setValueAt:function(a,b){var c=this._rows[a];if(c){c.setValue(b)}this.refreshUI()},resetValues:function(b){if(!b){return}this.values=b;var a=b.length;var d=this._rows.length;for(var c=0;c<a&&c<d;++c){this._rows[c].reset(b[c])}if(d>a){for(var c=d-1;c>=a;--c){this.deleteValue(c)}}else{if(a>d){for(var c=d;c<a&&(this.displayAllValues||c<this.maxNumValuesDisplayed);++c){var e=this._addRow(b[c])}}}MochiKit.Signal.signal(this,"ParameterUIResized");this.refreshUI()},alignRangeRows:function(){if(!this.allowRange){return}var c=0;for(var b=0,a=this._rows.length;b<a;b++){var e=this._rows[b];var d=e._valueWidget;c=Math.max(c,d.getLowerBoundValueWidth())}for(var b=0,a=this._rows.length;b<a;b++){var e=this._rows[b];var d=e._valueWidget;d.setLowerBoundValueWidth(c)}},setValues:function(b){if(!b){return}this.values=b;var a=b.length;var d=this._rows.length;for(var c=0;c<a&&c<d;++c){this._rows[c].setValue(b[c])}if(d>a){for(var c=d-1;c>=a;--c){this.deleteValue(c)}}else{if(a>d){for(var c=d;c<a&&(this.displayAllValues||c<this.maxNumValuesDisplayed);++c){this._addRow(b[c])}}}MochiKit.Signal.signal(this,"ParameterUIResized");this.refreshUI()},setCleanValue:function(a,b){var c=this._rows[a];if(c){c.setCleanValue(b)}},deleteValue:function(a){if(a>=0&&a<this._rows.length){var c=this._rows[a];c.layer.parentNode.removeChild(c.layer);_widgets[c.widx]=null;this._rows.splice(a,1);var b=this._rows.length}this.refreshUI()},setWarning:function(a,b){var c=this._rows[a];if(c){c.setWarning(b)}},getWarning:function(a){var b=this._rows[a];if(b){return b.getWarning()}return null},resize:function(a){if(a!==null){this.width=a;if(this.layer){bobj.setOuterSize(this.layer,a)}}}};bobj.crv.params.newOptionalParameterUI=function(a){a=MochiKit.Base.update({noValueDisplayText:"",isEmptyStringNoValue:true,clearValuesCB:null},a);var b=bobj.crv.params.newParameterUI(a);bobj.extendClass(b,bobj.crv.params.OptionalParameterUI,bobj.crv.params.ParameterUI);return b};bobj.crv.params.OptionalParameterUI={_getNewValueRowConstructor:function(){return bobj.crv.params.newOptionalParameterValueRow},_getNewValueRowArgs:function(b){var a=this.superClass._getNewValueRowArgs(b);a.noValueDisplayText=this.noValueDisplayText;a.isEmptyStringNoValue=this.isEmptyStringNoValue;a.clearValuesCB=this.clearValuesCB;return a}};bobj.crv.params.ParameterController=function(b,e,a){this._panel=b;this._viewerCtrl=e;this._paramOpts=a;this._paramList=null;this._unusedParamList=null;var d=bobj.bindFunctionToObject(this._onClickTbApplyButton,this);var c=bobj.bindFunctionToObject(this._onClickTbResetButton,this);this._panel.setToolbarCallBacks(d,c)};bobj.crv.params.ParameterController.prototype={setParameters:function(d,m){var l=bobj.crv.params.DataTypes;var x=MochiKit.Base.map;var y=MochiKit.Base.bind;this._deleteWidgets();var a=50;this._paramList=d;for(var r=0;r<d.length;++r){var g=d[r];var w=y(this._getDisplayText,this,g);var p=y(this._getDefaultValue,this,g.valueDataType,g.defaultDisplayType);var t=this._canPanelChangeValues(g);var q=this._getMinMaxText(g.valueDataType,g.minValue,g.maxValue);var c=y(this._onClickTbAdvButton,this,g);var s=y(this.clearParameterValues,this,g);var o=!g.isEditable;var k=g.getValue();var v=g.isOptionalPrompt||g.allowNullValue;var f={hasButton:g.allowCustomValue&&t&&(g.valueDataType===l.DATE||g.valueDataType===l.DATE_TIME),isTimeShown:g.valueDataType===l.DATE_TIME,displayValueFormat:this._getDateTimeFormat(g.valueDataType),iconUrl:bobj.crvUri("images/calendar.gif")};if(v){k=this.convertOptionalParameterValue(k)}var u=v?bobj.crv.params.newOptionalParameterUI:bobj.crv.params.newParameterUI;var b=g.isEditable&&(g.allowRangeValue||g.allowMultiValue||g.isDCP()||g.editMask);var j={values:x(w,k),canChangeOnPanel:t,allowCustom:g.allowCustomValue,allowRange:g.allowRangeValue,canAddValues:g.allowMultiValue&&t,isReadOnlyParam:o,isPassword:g.isPassword(),defaultValues:x(p,g.defaultValues||[]),openAdvDialogCB:c,maxNumParameterDefaultValues:this._paramOpts.maxNumParameterDefaultValues,tooltip:g.getTitle(),calendarProperties:f,clearValuesCB:s,canOpenAdvDialog:b};if(v){j.noValueDisplayText=this._getNoValueDisplayText(g.valueDataType,t);j.isEmptyStringNoValue=g.valueDataType!=l.STRING}var n=u(j);this._observeParamWidget(n);var e=function(i,z,A,G,B,D,F,C,E){return function(){i.addParameter({paramUI:A,label:z.getTitle(),name:z.getName(),isDataFetching:z.isDataFetching,openAdvCB:G});if(C&&E){E()}}};setTimeout(e(this._panel,g,n,b?c:null,v,t,s,r==d.length-1,m),a*r)}if(d){var h=function(i){return function(){i.resize();if(MochiKit.Base.isIE()){var z=i.isDisplayed();i.setDisplay(!z);i.setDisplay(z)}}};setTimeout(h(this._panel),a*d.length)}this._panel.setDisabled(!this._isCurrentViewMainReport());this._panel.setApplyButtonEnabled(false);this._panel.setResetButtonEnabled(false)},setUnusedParameters:function(a){this._unusedParamList=a},convertOptionalParameterValue:function(a){if(a===undefined||a.length===undefined){return[]}var b=bobj.cloneArray(a);if(b.length==0||b[0]==null){b[0]=undefined}return b},getParameters:function(){return this._paramList},onChangeView:function(){this._panel.setDisabled(!this._isCurrentViewMainReport())},_isPanelEditable:function(){return this._isCurrentViewMainReport()},_isCurrentViewMainReport:function(){var a=this._viewerCtrl.getCurrentView();return a&&a.isMainReport()},findParamIndexByName:function(b){for(var a=0;a<this._paramList.length;++a){if(this._paramList[a].paramName===b){return a}}return -1},findUnusedParamIndexByName:function(b){for(var a=0;a<this._unusedParamList.length;++a){if(this._unusedParamList[a].paramName===b){return a}}return -1},findParamByName:function(b){var a=this.findParamIndexByName(b);if(a==-1){return null}else{return this._paramList[a]}},updateParameter:function(c,f){if(c){var b=this.findParamIndexByName(c);if(b!=-1){var e=this._paramList[b];e.setValue(f);var g=this._panel.getParameter(b);var d=MochiKit.Base.bind(this._getDisplayText,this,this._paramList[b]);if(e.isOptionalPrompt||e.allowNullValue){f=this.convertOptionalParameterValue(f)}g.setValues(MochiKit.Base.map(d,f));var a=this._panel.getParameterTab(b);if(a){a.setDirty(true)}}else{b=this.findUnusedParamIndexByName(c);if(b!=-1){var e=this._unusedParamList[b];e.setValue(f)}}}},getFocusAdvButtonCB:function(a){return MochiKit.Base.bind(this._focusAdvButton,this,a)},_focusAdvButton:function(c){if(c){var b=this.findParamIndexByName(c);if(b!=-1){var a=this._panel.getParameterTab(b);if(a){a.focusAdvButton()}}}},_canPanelChangeValues:function(a){return a&&a.isEditable&&!a.allowRangeValue&&!a.editMask&&!a.isDCP()&&!a.allowMultiValue},_deleteWidgets:function(){var a=this._panel.getParameter(0);while(a){delete _widgets[a.widx];this._panel.removeParameter(0);a=this._panel.getParameter(0)}},resetParamPanel:function(){for(var f=0,b=this._paramList.length;f<b;f++){var c=this._paramList[f];c.reset();var a=this._findWidget(c);var g=MochiKit.Base.bind(this._getDisplayText,this,c);var d=c.getValue();if(c.isOptionalPrompt||c.allowNullValue){d=this.convertOptionalParameterValue(d)}var j=MochiKit.Base.map(g,d);a.resetValues(j)}for(var f=0,e=this._panel.getParameterCount();f<e;f++){var h=this._panel.getParameterTab(f);if(h){h.setDirty(false)}}this._viewerCtrl.clearAdvancedPromptData();this._panel.setApplyButtonEnabled(false);this._panel.setResetButtonEnabled(false)},_compareCustomDateObject:function(b,a){if(b.y!=a.y){return false}if(b.m!=a.m){return false}if(b.d!=a.d){return false}if(b.h!=a.h){return false}if(b.min!=a.min){return false}if(b.s!=a.s){return false}if(b.ms!=a.ms){return false}return true},_hasMinBound:function(b,c){if(c==null){return false}var a=bobj.crv.params.DataTypes;switch(b){case a.STRING:if(c==0){return false}return true;case a.DATE:case a.DATE_TIME:absoluteMin={y:1753,m:0,d:1,h:0,min:0,s:0,ms:0};return !this._compareCustomDateObject(absoluteMin,c);case a.TIME:absoluteMin={y:1899,m:11,d:30,h:0,min:0,s:0,ms:0};return !this._compareCustomDateObject(absoluteMin,c);case a.NUMBER:case a.CURRENCY:if(c==-3.40282346638529e+38){return false}return true}return false},_hasMaxBound:function(b,c){if(c==null){return false}var a=bobj.crv.params.DataTypes;switch(b){case a.STRING:if(c==65534){return false}return true;case a.DATE:absoluteMax={y:9999,m:11,d:12,h:0,min:0,s:0,ms:0};return !this._compareCustomDateObject(absoluteMax,c);case a.TIME:absoluteMax={y:1899,m:11,d:30,h:23,min:59,s:59,ms:0};return !this._compareCustomDateObject(absoluteMax,c);case a.DATE_TIME:absoluteMax={y:9999,m:11,d:12,h:23,min:59,s:59,ms:0};return !this._compareCustomDateObject(absoluteMax,c);case a.NUMBER:case a.CURRENCY:if(c==3.40282346638529e+38){return false}return true}return false},_getNoValueDisplayText:function(d,a){if(!a){return L_bobj_crv_ParamsNoneSelected}else{var c=bobj.crv.params.DataTypes;var b="";switch(d){case c.DATE:b=L_bobj_crv_Date;break;case c.DATE_TIME:b=L_bobj_crv_DateTime;break;case c.TIME:b=L_bobj_crv_Time;break;case c.STRING:b=L_bobj_crv_Text;break;case c.NUMBER:b=L_bobj_crv_Number;break;case c.CURRENCY:b=L_bobj_crv_Number;break;case c.BOOLEAN:b=L_bobj_crv_Boolean;break}return L_bobj_crv_ParamsEnterOptional.replace("%1",b)}},_getMinMaxText:function(g,i,f){var a=bobj.crv.params.DataTypes;var j,c;if(g==a.STRING){c=this._getValueText(a.NUMBER,i);j=this._getValueText(a.NUMBER,f)}else{c=this._getValueText(g,i);j=this._getValueText(g,f)}if(g==a.BOOLEAN||(i==null&&f==null)){return null}var e,h;switch(g){case a.DATE:e=L_bobj_crv_Date;break;case a.TIME:e=L_bobj_crv_Time;break;case a.DATE_TIME:e=L_bobj_crv_DateTime;break;case a.NUMBER:e=L_bobj_crv_Number;break;case a.CURRENCY:e=L_bobj_crv_Number;break}var d=this._hasMinBound(g,i);var b=this._hasMaxBound(g,f);switch(g){case a.STRING:if(d&&b){h=L_bobj_crv_ParamsStringMinAndMaxTooltip.replace("%1",c);h=h.replace("%2",j)}else{if(d){h=L_bobj_crv_ParamsStringMinOrMaxTooltip.replace("%1",L_bobj_crv_Minimum);h=h.replace("%2",c)}else{if(b){h=L_bobj_crv_ParamsStringMinOrMaxTooltip.replace("%1",L_bobj_crv_Maximum);h=h.replace("%2",j)}}}break;default:if(d&&b){h=L_bobj_crv_ParamsMinAndMaxTooltip.replace("%1",e);h=h.replace("%2",c);h=h.replace("%3",j)}else{if(d){h=L_bobj_crv_ParamsMinTooltip.replace("%1",e);h=h.replace("%2",c)}else{if(b){h=L_bobj_crv_ParamsMaxTooltip.replace("%1",e);h=h.replace("%2",j)}}}}return h},_getDefaultValue:function(e,d,f){var a=bobj.crv.params.DefaultDisplayTypes;var c=this._getValueText(e,f.value);var b;switch(d){case a.Description:if(f.desc!=null&&f.desc.length>0){b=f.desc}else{b=c}break;case a.DescriptionAndValue:b=c;if(f.desc!=null&&f.desc.length>0){b+=" - "+f.desc}break}return b},_getValueTextFromDefValueDesc:function(d,c){if(d.defaultValues&&bobj.isArray(d.defaultValues)){for(var a=0;a<d.defaultValues.length;a++){var b=this._getDefaultValue(d.valueDataType,d.defaultDisplayType,d.defaultValues[a]);if(b==c){return this._getValueText(d.valueDataType,d.defaultValues[a].value)}}}return null},_getValueText:function(b,c){if(c===undefined){return undefined}c=bobj.crv.params.getValue(c);var a=bobj.crv.params.DataTypes;switch(b){case a.DATE:return this._getDateTimeText(c,this._paramOpts.dateFormat);case a.TIME:return this._getDateTimeText(c,this._paramOpts.timeFormat);case a.DATE_TIME:return this._getDateTimeText(c,this._paramOpts.dateTimeFormat);case a.NUMBER:case a.CURRENCY:return this._getNumberText(c,this._paramOpts.numberFormat);case a.BOOLEAN:return this._getBooleanText(c,this._paramOpts.booleanFormat);case a.STRING:default:return""+c}},_getBooleanText:function(b,a){return a[""+b]},_getNumberText:function(h,g){var f=g.decimalSeperator;var a=g.groupSeperator;var j=(""+h).split(".");var i,b,e;var k=null;i=j[0];if(i.length>0&&i.slice(0,1)=="-"||i.slice(0,1)=="+"){k=i.slice(0,1);i=i.slice(1,i.length)}b=(j.length==2)?j[1]:null;formattedLeftVal=null;if(i.length<=3){formattedLeftVal=i}else{var d=null;var c=null;while(i.length>0){c=(i.length>3)?i.length-3:0;d=i.slice(c,i.length);i=i.slice(0,c);formattedLeftVal=(formattedLeftVal==null)?d:d+a+formattedLeftVal}}e=(b!=null)?formattedLeftVal+f+b:formattedLeftVal;e=(k!=null)?k+e:e;return e},_getDateTimeText:function(b,c){var a=bobj.crv.params.jsonToDate(b);if(a){return bobj.external.date.formatDate(a,c)}return""},_getValueTextFromDefaultValue:function(e,b){var d=bobj.crv.params.getDescription(b);if(d!==null){return this._getDefaultValue(e.valueDataType,e.defaultDisplayType,b)}b=bobj.crv.params.getValue(b);if(bobj.isArray(e.defaultValues)){var c=bobj.getValueHashCode(e.valueDataType,b);for(var a=0;a<e.defaultValues.length;a++){if(c==bobj.getValueHashCode(e.valueDataType,e.defaultValues[a].value)){return this._getDefaultValue(e.valueDataType,e.defaultDisplayType,e.defaultValues[a])}}}return null},_getDisplayText:function(c,b){if(b===undefined){return undefined}if(b.lowerBoundType!==undefined||b.upperBoundType!==undefined){return this._getRangeDisplayText(c,b)}var a=this._getValueTextFromDefaultValue(c,b);if(a==null){a=this._getValueText(c.valueDataType,b)}return a},_getRangeDisplayText:function(c,b){var a=new Object;a.lowerBound={type:b.lowerBoundType,value:this._getDisplayText(c,b.beginValue)};a.upperBound={type:b.upperBoundType,value:this._getDisplayText(c,b.endValue)};return a},_getParamValue:function(b,c){if(b===undefined){return undefined}var a=bobj.crv.params.DataTypes;switch(b){case a.DATE:return this._getDateTimeParamValue(c,this._paramOpts.dateFormat);case a.TIME:return this._getDateTimeParamValue(c,this._paramOpts.timeFormat);case a.DATE_TIME:return this._getDateTimeParamValue(c,this._paramOpts.dateTimeFormat);case a.NUMBER:case a.CURRENCY:return this._getNumberParamValue(c,this._paramOpts.numberFormat);case a.BOOLEAN:return this._getBooleanParamValue(c,this._paramOpts.booleanFormat);case a.STRING:default:return c}},_getBooleanParamValue:function(b,a){if(b!=null&&b.length!=0){return a["true"]==b}else{return null}},clearParameterValues:function(a){if(a.allowNullValue){this.updateParameter(a.paramName,[null])}else{this.updateParameter(a.paramName,[])}this._updateToolbar()},_getNumberParamValue:function(d,c){if(d==null){return null}var a="";if(/[ \f\n\r\t\v\u00A0\u2028\u2029]/.test(c.groupSeperator)){a=d.replace(/[ \f\n\r\t\v\u00A0\u2028\u2029]/g,"")}else{var b=new RegExp("\\"+c.groupSeperator,"g");a=d.replace(b,"")}return a.replace(c.decimalSeperator,".")},_getDateTimeParamValue:function(c,b){var a=bobj.external.date.getDateFromFormat(c,b);if(a){return bobj.crv.params.dateToJson(a)}return c},_observeParamWidget:function(a){if(a){var b=MochiKit.Base.bind;a.changeValueCB=b(this._onChangeValue,this,a);a.enterPressCB=b(this._onEnterPress,this,a)}},_onChangeValue:function(b,a,d){if(!this._isPanelEditable()){return}var c=this._panel.getParameterTabByWidget(b);if(c){c.setDirty(true)}this._checkAndSetValue(b,a);this._updateToolbar()},_onEnterPress:function(b,a){if(this._panel.isApplyButtonEnabled()){this._applyValues()}},_onClickTbApplyButton:function(){this._applyValues()},_onClickTbResetButton:function(){this.resetParamPanel()},_onClickTbAdvButton:function(a){if(this._isPanelEditable()){this._viewerCtrl.showAdvancedParamDialog(a)}},_applyValues:function(){var d=this._paramList.length;var h=-1;var a=-1;var l=null;var b=null;for(var f=0;(f<d)&&!l;++f){b=this._panel.getParameter(f);var k=b.getNumValues();for(var e=0;(e<k)&&!l;++e){l=b.getWarning(e);if(l){h=f;a=e}}}if(l){b=this._panel.getParameter(h);b.setFocusOnRow(a)}else{for(var f=0,g=this._paramList.length;f<g;f++){this._paramList[f].commitValue()}for(var f=0,g=this._unusedParamList.length;f<g;f++){this._unusedParamList[f].commitValue()}this._viewerCtrl.applyParams(this._paramList.concat(this._unusedParamList));this._panel.setApplyButtonEnabled(false)}},_findParam:function(a){return this._paramList[this._panel.getIndex(a)]},_findWidget:function(b){for(var a=0;a<this._paramList.length;a++){if(this._paramList[a].paramName==b.paramName){return this._panel.getParameter(a)}}return null},_updateToolbar:function(){if(!this._isPanelEditable()){return}this._panel.setApplyButtonEnabled(true);this._panel.setResetButtonEnabled(true)},_getDateTimeFormat:function(b){var a=bobj.crv.params.DataTypes;switch(b){case a.DATE:return this._paramOpts.dateFormat;case a.TIME:return this._paramOpts.timeFormat;case a.DATE_TIME:return this._paramOpts.dateTimeFormat;default:return null}},_checkAndSetValue:function(c,i){if(!c.canChangeOnPanel){return}var h=this._findParam(c);var d=c.getValueAt(i);var g=this._getValueTextFromDefValueDesc(h,d);if(g!=null){d=g}var b=this._getParamValue(h.valueDataType,d);if(i==0&&b==undefined){if(h.allowNullValue&&h.getValue().length==0){h.setValue(0,null)}if(h.isOptionalPrompt){h.clearValue()}c.setWarning(i,null);return}var f=bobj.crv.params.Validator.ValueStatus;var a=f.OK;var j=null;a=bobj.crv.params.Validator.getInstance().validateValue(h,b);if(f.OK===a){var e=this._getValueTextFromDefaultValue(h,b);if(e!=null){c.setValueAt(i,e)}c.setWarning(i,null);h.setValue(i,b)}else{j=this._getWarningText(h,a);c.setWarning(i,{code:a,message:j})}},_getWarningText:function(c,b){var a=bobj.crv.params.DataTypes;switch(c.valueDataType){case a.DATE:case a.TIME:case a.DATE_TIME:return this._getDateTimeWarning(c,b);case a.STRING:return this._getStringWarning(c,b);case a.NUMBER:case a.CURRENCY:return this._getNumberWarning(c,b);default:return null}},_getDateTimeWarning:function(e,d){var c=bobj.crv.params.DataTypes;var b=bobj.crv.params.Validator.ValueStatus;var a=this._paramOpts.dateFormat;a=a.replace("yyyy","%1");a=a.replace(/M+/,"%2");a=a.replace(/d+/,"%3");a=a.replace("%1",L_bobj_crv_ParamsYearToken);a=a.replace("%2",L_bobj_crv_ParamsMonthToken);a=a.replace("%3",L_bobj_crv_ParamsDayToken);if(d==b.ERROR||d==b.VALUE_INVALID_TYPE){switch(e.valueDataType){case c.DATE:return L_bobj_crv_ParamsBadDate.replace("%1",a);case c.DATE_TIME:return L_bobj_crv_ParamsBadDateTime.replace("%1",a);break;case c.TIME:return L_bobj_crv_ParamsBadTime;break}}else{if(d==b.VALUE_TOO_BIG||d==b.VALUE_TOO_SMALL){return this._getMinMaxText(e.valueDataType,e.minValue,e.maxValue)}}return null},_getStringWarning:function(c,b){var a=bobj.crv.params.Validator.ValueStatus;if(a.VALUE_TOO_LONG===b){return L_bobj_crv_ParamsTooLong.replace("%1",c.maxValue)}else{if(a.VALUE_TOO_SHORT===b){return L_bobj_crv_ParamsTooShort.replace("%1",c.minValue)}}return null},_getNumberWarning:function(d,c){var b=bobj.crv.params.Validator.ValueStatus;var a=bobj.crv.params.DataTypes;switch(c){case b.ERROR:case b.VALUE_INVALID_TYPE:if(d.valueDataType==a.NUMBER){return L_bobj_crv_ParamsBadNumber}else{if(d.valueDataType==a.CURRENCY){return L_bobj_crv_ParamsBadCurrency}}case b.VALUE_TOO_BIG:case b.VALUE_TOO_SMALL:return this._getMinMaxText(d.valueDataType,d.minValue,d.maxValue);default:return null}}};if(typeof bobj==="undefined"){bobj={}}if(typeof bobj.crv==="undefined"){bobj.crv={}}if(typeof bobj.crv.WarningPopup==="undefined"){bobj.crv.WarningPopup={}}bobj.crv.WarningPopup.getInstance=function(){if(bobj.crv.WarningPopup.__instance===undefined){bobj.crv.WarningPopup.__instance=new bobj.crv.WarningPopup.Class()}return bobj.crv.WarningPopup.__instance};bobj.crv.WarningPopup.Class=function(){this.layer=null;this.id=bobj.uniqueId()};bobj.crv.WarningPopup.Class.prototype={show:function(c,b,a){if(!this.layer){this.init()}this.layer.style.top=a+"px";this.layer.style.left=b+"px";this.txtLayer.innerHTML=c;this.layer.style.display="block"},hide:function(){if(this.layer){this.layer.style.display="none"}},getHTML:function(){return bobj.html.DIV({"class":"WarningPopup",id:this.id},bobj.html.IMG({id:this.id+"img",style:{position:"absolute",left:"10px",top:"-19px"},src:bobj.crvUri("images/WarningPopupTriangle.gif")}),bobj.html.DIV({id:this.id+"txt",style:{padding:"5px"}}))},init:function(){append2(document.body,this.getHTML());this.layer=getLayer(this.id);this.txtLayer=getLayer(this.id+"txt")}};