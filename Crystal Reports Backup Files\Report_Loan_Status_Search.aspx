﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Report_Loan_Status_Search.aspx.cs" Inherits="KSDCSCST_Portal.Report_Loan_Status_Search" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- DataTables -->
    <link rel="stylesheet" href="assets/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">

    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">



    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Application Status Report</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="Dashboard.aspx">Home</a></li>
                        <li class="breadcrumb-item active">Enhanced Search</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        -
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6 mx-auto">


                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Filter</h3>

                            <div style="float: right; display: none;"><a onclick="GeneratePDF()" style="background: #0f6caa; border-color: #0f6caa;" class="btn btn-block btn-success">Generate PDF</a></div>
                        </div>
                        <!-- /.card-header -->


                        <div class="card-body">
                            <div class="tab-content">
                                <div class="active tab-pane" id="settings">


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Select Office*</label>
                                        <div class="col-sm-9">
                                            <select id="dropOffice" class="form-control">
                                            </select>
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Select Status*</label>
                                        <div class="col-sm-9">
                                            <select id="dropStatus" class="form-control" >
                                                 <option selected>Select</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">From Date*</label>
                                        <div class="col-sm-9">
                                            <input type="date" class="dateandtime form-control" id="txt_From_Date">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">To Date*</label>
                                        <div class="col-sm-9">
                                            <input type="date" class="dateandtime form-control" id="txt_To_Date">
                                        </div>
                                    </div>












                                    <%--



                                    <div class="row">
                                        <div class="col-sm-12">
                                            <table style="width: 33.33%; float: left;">
                                                <tr>
                                                    <td>
                                                        <select id="dropLoan_Scheme" class="form-control">
                                                            <option selected>Loan Scheme</option>
                                                        </select>
                                                    </td>



                                                </tr>

                                            </table>
                                            <table style="width: 33.30%; float: left;">
                                                <tr>

                                                    <td style="text-align: right;">
                                                        <select id="dropLoan_Scheme" class="form-control">
                                                            <option selected></option>
                                                        </select>
                                                    </td>
                                                </tr>

                                            </table>
                                            <table style="width: 33.30%; float: left;">
                                                <tr>


                                                    <td style="text-align: right;">
                                                        <select id="dropLoan_Scheme" class="form-control">
                                                            <option selected>And</option>
                                                            <option>Or</option>
                                                        </select>
                                                    </td>
                                                </tr>

                                            </table>
                                        </div>

                                    </div>

                                    <div class="row">
                                        <div class="col-sm-12">
                                            <table style="width: 33.33%; float: left;">
                                                <tr>
                                                    <td>
                                                        <select id="dropLoan_Scheme" class="form-control">
                                                            <option selected>Taluk</option>
                                                        </select>
                                                    </td>



                                                </tr>

                                            </table>
                                            <table style="width: 33.30%; float: left;">
                                                <tr>

                                                    <td style="text-align: right;">
                                                        <select id="dropLoan_Scheme" class="form-control">
                                                            <option selected></option>
                                                        </select>
                                                    </td>
                                                </tr>

                                            </table>
                                            <table style="width: 33.30%; float: left;">
                                                <tr>


                                                    <td style="text-align: right;">
                                                        <select id="dropLoan_Scheme" class="form-control">
                                                            <option selected>And</option>
                                                            <option>Or</option>
                                                        </select>
                                                    </td>
                                                </tr>

                                            </table>
                                        </div>

                                    </div>

                                    <div class="row">
                                        <div class="col-sm-12">
                                            <table style="width: 33.33%; float: left;">
                                                <tr>
                                                    <td>
                                                        <select id="dropLoan_Scheme" class="form-control">
                                                            <option selected>Village</option>
                                                        </select>
                                                    </td>



                                                </tr>

                                            </table>
                                            <table style="width: 33.30%; float: left;">
                                                <tr>

                                                    <td style="text-align: right;">
                                                        <select id="dropLoan_Scheme" class="form-control">
                                                            <option selected></option>
                                                        </select>
                                                    </td>
                                                </tr>

                                            </table>
                                            <table style="width: 33.30%; float: left;">
                                                <tr>


                                                    <td style="text-align: right;">
                                                        <select id="dropLoan_Scheme" class="form-control">
                                                            <option selected>And</option>
                                                            <option>Or</option>
                                                        </select>
                                                    </td>
                                                </tr>

                                            </table>
                                        </div>

                                    </div>


                                    <div class="row">
                                        <div class="col-sm-12">
                                            <table style="width: 33.33%; float: left;">
                                                <tr>
                                                    <td>
                                                        <select id="dropLoan_Scheme" class="form-control">
                                                            <option selected>Locality</option>
                                                        </select>
                                                    </td>



                                                </tr>

                                            </table>
                                            <table style="width: 33.30%; float: left;">
                                                <tr>

                                                    <td style="text-align: right;">
                                                        <select id="dropLoan_Scheme" class="form-control">
                                                            <option selected></option>
                                                        </select>
                                                    </td>
                                                </tr>

                                            </table>
                                            <table style="width: 33.30%; float: left;">
                                                <tr>


                                                    <td style="text-align: right;">
                                                        <select id="dropLoan_Scheme" class="form-control">
                                                            <option selected>And</option>
                                                            <option>Or</option>
                                                        </select>
                                                    </td>
                                                </tr>

                                            </table>
                                        </div>

                                    </div>


                                    <div class="row">
                                        <div class="col-sm-12">
                                            <table style="width: 33.33%; float: left;">
                                                <tr>
                                                    <td>
                                                        <select id="dropLoan_Scheme" class="form-control">
                                                            <option selected>Caste</option>
                                                        </select>
                                                    </td>



                                                </tr>

                                            </table>
                                            <table style="width: 33.30%; float: left;">
                                                <tr>

                                                    <td style="text-align: right;">
                                                        <select id="dropLoan_Scheme" class="form-control">
                                                            <option selected></option>
                                                        </select>
                                                    </td>
                                                </tr>

                                            </table>
                                            <table style="width: 33.30%; float: left;">
                                                <tr>


                                                    <td style="text-align: right;">
                                                        <select id="dropLoan_Scheme" class="form-control">
                                                            <option selected>And</option>
                                                            <option>Or</option>
                                                        </select>
                                                    </td>
                                                </tr>

                                            </table>
                                        </div>

                                    </div>



                                    <div class="row">
                                        <div class="col-sm-12">
                                            <table style="width: 33.33%; float: left;">
                                                <tr>
                                                    <td>
                                                        <select id="dropLoan_Scheme" class="form-control">
                                                            <option selected>Religion</option>
                                                        </select>
                                                    </td>



                                                </tr>

                                            </table>
                                            <table style="width: 33.30%; float: left;">
                                                <tr>

                                                    <td style="text-align: right;">
                                                        <select id="dropLoan_Scheme" class="form-control">
                                                            <option selected></option>
                                                        </select>
                                                    </td>
                                                </tr>

                                            </table>
                                            <table style="width: 33.30%; float: left;">
                                                <tr>


                                                    <td style="text-align: right;">
                                                        <select id="dropLoan_Scheme" class="form-control">
                                                            <option selected>And</option>
                                                            <option>Or</option>
                                                        </select>
                                                    </td>
                                                </tr>

                                            </table>
                                        </div>

                                    </div>



                                    <div class="row">
                                        <div class="col-sm-12">
                                            <table style="width: 33.33%; float: left;">
                                                <tr>
                                                    <td>
                                                        <select id="dropLoan_Scheme" class="form-control">
                                                            <option selected>Select</option>
                                                        </select>
                                                    </td>



                                                </tr>

                                            </table>
                                            <table style="width: 33.30%; float: left;">
                                                <tr>

                                                    <td style="text-align: right;">
                                                        <select id="dropLoan_Scheme" class="form-control">
                                                            <option selected></option>
                                                        </select>
                                                    </td>
                                                </tr>

                                            </table>

                                        </div>

                                    </div>




                                    <section class="content">
                                        <div class="container-fluid">

                                            <form action="enhanced-results.html">
                                                <div class="row">
                                                    <div class="col-md-10 offset-md-3">
                                                        <div class="row">

                                                            <div class="col-3">
                                                                <div class="form-group">
                                                                    <label>Sort Order:</label>
                                                                    <select class="select2" style="width: 100%;">
                                                                        <option selected>ASC</option>
                                                                        <option>DESC</option>
                                                                    </select>
                                                                </div>
                                                            </div>
                                                            <div class="col-3">
                                                                <div class="form-group">
                                                                    <label>Order By:</label>
                                                                    <select class="select2" style="width: 100%;">
                                                                        <option selected>Title</option>
                                                                        <option>Date</option>
                                                                    </select>
                                                                </div>
                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </section>


                                    --%>










                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">

                                            <a onclick="Save();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>













                        















                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <table id="ApplicationStatusList" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Sl No</th>
                                        <th>RegNo</th>
                                        <th>Name</th>
                                        <th>Scheme</th>
                                        <th>Application Date</th>
                                        <th>LegalFee Coll Date</th>
                                        <th>Sanction Date</th>
                                        <th>SanctionAmt</th>
                                        <th>SuertyType</th>
                                        <th>SuertySent</th>
                                        <th>Confirmation</th>
                                        <th>Agrement</th>
                                        <th>Fund</th>
                                        <th>Order</th>
                                        <th>Status</th>
                                        <th>Disburse Date</th>
                                    </tr>
                                </thead>
                                <tbody id="tblBody">
                                </tbody>
                            </table>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->


    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <script src="assets/plugins/toastr/toastr.min.js"></script>



    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/inputmask/5.0.4/jquery.inputmask.min.js"></script>
    <!-- Page specific script -->
    <script>
        var Toast;
        function GeneratePDF() {
            const pdf = new jsPDF({
                unit: 'px',
                format: 'a4'
            });

            // Define the element to be converted to PDF
            const element = $("#Application_Issue_List")[0];

            // Generate the PDF from the HTML element
            pdf.fromHTML(element, 10, 10);

            // Save or display the PDF
            pdf.save("report.pdf");
        }
        $(function () {
            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });
            $('.select2').select2();

            var currentDate = new Date();

            var day = currentDate.getDate();
            var month = currentDate.getMonth() + 1; // Months are zero-based, so January is 0
            var year = currentDate.getFullYear();

            // Pad day and month with leading zeros if necessary
            if (day < 10) {
                day = '0' + day;
            }

            if (month < 10) {
                month = '0' + month;
            }

            var formattedDate = day + '/' + month + '/' + year;

            // Format the date as desired (e.g., "MM/DD/YYYY")
            //  var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div

            //$('#txt_From_Date').val(formattedDate).trigger('change');

            function formatDate(date) {
                var day = date.getDate();
                var month = date.getMonth() + 1;
                var year = date.getFullYear();

                // Add leading zeros if necessary
                if (day < 10) {
                    day = '0' + day;
                }
                if (month < 10) {
                    month = '0' + month;
                }

                return year + '-' + month + '-' + day;
            }

            // Get today's date
            var today = new Date();

            // Format the date as dd/MM/yyyy
            var formattedDate = formatDate(today);

            // Set the value of the input field

            //  var dateParts = "28/05/2024".split("/");
            //  var formattedDate = dateParts[2] + "-" + dateParts[1] + "-" + dateParts[0];

            $('#txt_From_Date').val(formattedDate);
            $('#txt_To_Date').val(formattedDate);
            // $('#txt_To_Date').val(formattedDate);


        });

        $(document).ready(function () {

            $('#ApplicationStatusList').hide();
            Load_Offices_By_Office_Code();

        });

        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }

        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }

        function Save() {


            var ApplicationStatus = $("#dropStatus");
            var Office = $("#dropOffice");
            var dtFrom = $("#txt_From_Date");
            var dtTo = $("#txt_To_Date");

            if (Office.val().trim() == "0") {
                Focus_Error(Office);
                Toast.fire({
                    icon: 'error',
                    title: 'Office is required !'
                })
            }
            else if (ApplicationStatus.val().trim() == "0") {
                Focus_Error(ApplicationStatus);
                Toast.fire({
                    icon: 'error',
                    title: 'Application Status is required !'
                })
            }
            else if (dtFrom.val().trim() == "") {
                Focus_Error(dtFrom);
                Toast.fire({
                    icon: 'error',
                    title: 'From Date is required !'
                })
            }
            else if (dtTo.val().trim() == "") {
                Focus_Error(dtTo);
                Toast.fire({
                    icon: 'error',
                    title: 'To Date is required !'
                })
            }
            else {

                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/Report_ApplicationStatus",
                    data: JSON.stringify({ officeCode: Office.val(), applicationStatus: ApplicationStatus.val(), dtFrom: dtFrom.val(), dtTo: dtTo.val() }), // If you have parameters
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        $('#tblBody').empty();
                        var i = 0;
                        $.each(data.d, function (key, value) {

                            var SlNo = i += 1;
                            var RegNo = value.RegNo;
                            var Name = value.Name;
                            var Scheme = value.Scheme;
                            var AppDate = value.AppDate;
                            var LegalDate = value.LegalDate;
                            var SancDate = value.SancDate;
                            var SancAmt = value.SancAmt;
                            var SuretyType = value.SuretyType;
                            var ConfSendDate = value.ConfSendDate;
                            var ConfRecDate = value.ConfRecDate;
                            var AgrDate = value.AgrDate;
                            var Fund = value.Fund;
                            var OrderDate = value.OrderDate;
                            var AppStatus = value.AppStatus;
                            var DisbDate = value.DisbDate;

                            var html = '<tr>' +
                                '    <td>' + SlNo + '</td>' +
                                '    <td>' + RegNo + '</td>' +
                                '    <td>' + Name + '</td>' +
                                '    <td>' + Scheme + '</td>' +
                                '    <td>' + AppDate + '</td>' +
                                '    <td>' + LegalDate + '</td>' +
                                '    <td>' + SancDate + '</td>' +
                                '    <td>' + SancAmt + '</td>' +
                                '    <td>' + SuretyType + '</td>' +
                                '    <td>' + ConfSendDate + '</td>' +
                                '    <td>' + ConfRecDate + '</td>' +
                                '    <td>' + AgrDate + '</td>' +
                                '    <td>' + Fund + '</td>' +
                                '    <td>' + OrderDate + '</td>' +
                                '    <td>' + AppStatus + '</td>' +
                                '    <td>' + DisbDate + '</td>' +
                                '</tr>';
                            $('#tblBody').append(html);
                        });
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        // Handle AJAX error
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Something went wrong!',

                        })

                    }

                }).done(function () {
                    $('#ApplicationStatusList').show();
                });


            }

        }
        function Load_Offices_By_Office_Code() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_Offices_By_Office_Code",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropOffice').empty();
                    // $('#dropOffice').append('<option value="0">Select</option>');
                    $.each(data.d, function (key, value) {
                        var OfficeCode = value.Office_Code;
                        var Office = value.Office_Name;
                        var html = '<option value="' + OfficeCode + '">' + Office + '</option>';
                        $('#dropOffice').append(html);
                    });
                },
                error: function (error) {
                    //alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_ApplicationStatus();
            });

        }

        function Load_All_ApplicationStatus() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_ApplicationStatus",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropStatus').empty();
                    $('#dropStatus').append('<option value="0">Select</option>');
                    $('#dropStatus').append('<option value="All">All</option>');
                    $.each(data.d, function (key, value) {
                        var ApplicationStatus = value.ApplicationStatus;
                        var html = '<option value="' + ApplicationStatus + '">' + ApplicationStatus + '</option>';
                        $('#dropStatus').append(html);
                    });
                },
                error: function (error) {
                    //alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });

        }

    </script>
</asp:Content>
