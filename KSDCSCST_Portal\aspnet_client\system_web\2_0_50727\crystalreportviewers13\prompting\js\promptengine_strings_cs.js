/* Copyright (c) Business Objects 2006. All rights reserved. */

// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "Dnes";
var L_January   = "Leden";
var L_February  = "\u00DAnor";
var L_March     = "B\u0159ezen";
var L_April     = "Duben";
var L_May       = "Kv\u011Bten";
var L_June      = "\u010Cerven";
var L_July      = "\u010Cervenec";
var L_August    = "Srpen";
var L_September = "Z\u00E1\u0159\u00ED";
var L_October   = "\u0158\u00EDjen";
var L_November  = "Listopad";
var L_December  = "Prosinec";
var L_Su        = "Ne";
var L_Mo        = "Po";
var L_Tu        = "\u00DAt";
var L_We        = "St";
var L_Th        = "\u010Ct";
var L_Fr        = "P\u00E1";
var L_Sa        = "So";

// Strings for prompts.js and prompts_param.js
var L_YYYY          = "rrrr";
var L_MM            = "mm";
var L_DD            = "dd";
var L_BadNumber     = "Tento parametr je typu \"\u010C\u00EDslo\" a m\u016F\u017Ee obsahovat pouze symbol z\u00E1porn\u00E9ho znam\u00E9nka, \u010D\u00EDslice (\"0-9\"), symboly pro seskupov\u00E1n\u00ED \u010D\u00EDslic nebo desetinn\u00FD symbol. Opravte pros\u00EDm zadanou hodnotu parametru.";
var L_BadCurrency   = "Tento parametr je typu \"M\u011Bna\" a m\u016F\u017Ee obsahovat pouze symbol z\u00E1porn\u00E9ho znam\u00E9nka, \u010D\u00EDslice (\"0-9\"), symboly pro seskupov\u00E1n\u00ED \u010D\u00EDslic nebo desetinn\u00FD symbol. Opravte pros\u00EDm zadanou hodnotu parametru";
var L_BadDate       = "Tento parametr je typu \"Datum\" a m\u011Bl by b\u00FDt ve form\u00E1tu \"%1\", kde \"rrrr\" je \u010Dty\u0159m\u00EDstn\u00FD rok, \"mm\" je m\u011Bs\u00EDc (nap\u0159. leden = 1) a \"dd\" je den v m\u011Bs\u00EDci.";
var L_BadDateTime   = "Tento parametr je typu \"Datum a \u010Das\" a spr\u00E1vn\u00FD form\u00E1t je \"%1 hh:mm:ss\". \"rrrr\" je \u010Dty\u0159m\u00EDstn\u00FD rok, \"mm\" je m\u011Bs\u00EDc (nap\u0159. leden = 1), \"dd\" je den v m\u011Bs\u00EDci, \"hh\" jsou hodiny ve form\u00E1tu 24 hodin, \"mm\" jsou minuty a \"ss\" jsou sekundy.";
var L_BadTime       = "Tento parametr je typu \"\u010Cas\" a m\u011Bl by b\u00FDt ve form\u00E1tu \"hh:mm:ss\", kde \"hh\" jsou hodiny ve form\u00E1tu 24 hodin, \"mm\" jsou minuty a \"ss\" jsou sekundy.";
var L_NoValue       = "\u017D\u00E1dn\u00E1 hodnota";
var L_BadValue      = "Pro nastaven\u00ED \"\u017D\u00E1dn\u00E1 hodnota\" mus\u00EDte nastavit hodnoty Od i Do na \"\u017D\u00E1dn\u00E1 hodnota\".";
var L_BadBound      = "Nem\u016F\u017Eete nastavit \"\u017D\u00E1dn\u00E1 doln\u00ED hranice\" spolu s \"\u017D\u00E1dn\u00E1 horn\u00ED hranice\".";
var L_NoValueAlready = "Tento parametr je ji\u017E nastaven na \"\u017D\u00E1dn\u00E1 hodnota\". P\u0159ed p\u0159id\u00E1n\u00EDm jin\u00E9 hodnoty odstra\u0148te \"\u017D\u00E1dn\u00E1 hodnota\".";
var L_RangeError    = "Za\u010D\u00E1tek rozsahu nem\u016F\u017Ee b\u00FDt vy\u0161\u0161\u00ED ne\u017E konec rozsahu.";
var L_NoDateEntered = "Mus\u00EDte zadat datum.";
var L_Empty         = "Zadejte hodnotu.";

// Strings for filter dialog
var L_closeDialog="Zav\u0159\u00EDt okno";

var L_SetFilter = "Nastavit filtr";
var L_OK        = "OK";
var L_Cancel    = "Storno";

 /* Crystal Decisions Confidential Proprietary Information */
