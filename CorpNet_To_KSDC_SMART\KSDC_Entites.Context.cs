﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CorpNet_To_KSDC_SMART
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    using System.Data.Entity.Core.Objects;
    using System.Linq;
    
    public partial class KSDC_Entities : DbContext
    {
        public KSDC_Entities()
            : base("name=KSDC_Entities")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
    
        public virtual int Delete_All_tbl_Logs_By_Id()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Delete_All_tbl_Logs_By_Id");
        }
    
        public virtual int Delete_From_tbl_Agency(Nullable<int> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Delete_From_tbl_Agency", idParameter);
        }
    
        public virtual int Delete_From_tbl_KSDC_SMART_Issues(Nullable<int> issue_Id)
        {
            var issue_IdParameter = issue_Id.HasValue ?
                new ObjectParameter("Issue_Id", issue_Id) :
                new ObjectParameter("Issue_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Delete_From_tbl_KSDC_SMART_Issues", issue_IdParameter);
        }
    
        public virtual int Delete_From_tbl_Loan_App_Issue(Nullable<int> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Delete_From_tbl_Loan_App_Issue", idParameter);
        }
    
        public virtual int Delete_From_tbl_Projects(Nullable<int> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Delete_From_tbl_Projects", idParameter);
        }
    
        public virtual int Delete_From_tbl_Roles(Nullable<int> id, Nullable<int> updated_By)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            var updated_ByParameter = updated_By.HasValue ?
                new ObjectParameter("Updated_By", updated_By) :
                new ObjectParameter("Updated_By", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Delete_From_tbl_Roles", idParameter, updated_ByParameter);
        }
    
        public virtual int Delete_From_tbl_Schemes(Nullable<int> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Delete_From_tbl_Schemes", idParameter);
        }
    
        public virtual int Delete_From_tbl_Sectors(Nullable<int> id, Nullable<int> updatedBy)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            var updatedByParameter = updatedBy.HasValue ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Delete_From_tbl_Sectors", idParameter, updatedByParameter);
        }
    
        public virtual int Delete_From_tbl_Users(Nullable<int> id, Nullable<int> updated_By)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            var updated_ByParameter = updated_By.HasValue ?
                new ObjectParameter("Updated_By", updated_By) :
                new ObjectParameter("Updated_By", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Delete_From_tbl_Users", idParameter, updated_ByParameter);
        }
    
        public virtual int Delete_tbl_Logs_By_Id(Nullable<int> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Delete_tbl_Logs_By_Id", idParameter);
        }
    
        public virtual ObjectResult<GetMenuItems_Result> GetMenuItems(Nullable<int> role_Id)
        {
            var role_IdParameter = role_Id.HasValue ?
                new ObjectParameter("Role_Id", role_Id) :
                new ObjectParameter("Role_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetMenuItems_Result>("GetMenuItems", role_IdParameter);
        }
    
        public virtual int Insert_To_tbl_Agency(string name, string description, string cast, Nullable<int> isActive, Nullable<int> createdBy)
        {
            var nameParameter = name != null ?
                new ObjectParameter("Name", name) :
                new ObjectParameter("Name", typeof(string));
    
            var descriptionParameter = description != null ?
                new ObjectParameter("Description", description) :
                new ObjectParameter("Description", typeof(string));
    
            var castParameter = cast != null ?
                new ObjectParameter("Cast", cast) :
                new ObjectParameter("Cast", typeof(string));
    
            var isActiveParameter = isActive.HasValue ?
                new ObjectParameter("IsActive", isActive) :
                new ObjectParameter("IsActive", typeof(int));
    
            var createdByParameter = createdBy.HasValue ?
                new ObjectParameter("CreatedBy", createdBy) :
                new ObjectParameter("CreatedBy", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_Agency", nameParameter, descriptionParameter, castParameter, isActiveParameter, createdByParameter);
        }
    
        public virtual int Insert_To_tbl_agriloan(Nullable<int> int_loanappid, Nullable<int> int_schemeid, string vchr_sector, string vchr_project, Nullable<decimal> int_amt_est, Nullable<decimal> int_amt_hand, string vchr_projdetails, string vchr_ext_prop)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(int));
    
            var int_schemeidParameter = int_schemeid.HasValue ?
                new ObjectParameter("int_schemeid", int_schemeid) :
                new ObjectParameter("int_schemeid", typeof(int));
    
            var vchr_sectorParameter = vchr_sector != null ?
                new ObjectParameter("vchr_sector", vchr_sector) :
                new ObjectParameter("vchr_sector", typeof(string));
    
            var vchr_projectParameter = vchr_project != null ?
                new ObjectParameter("vchr_project", vchr_project) :
                new ObjectParameter("vchr_project", typeof(string));
    
            var int_amt_estParameter = int_amt_est.HasValue ?
                new ObjectParameter("int_amt_est", int_amt_est) :
                new ObjectParameter("int_amt_est", typeof(decimal));
    
            var int_amt_handParameter = int_amt_hand.HasValue ?
                new ObjectParameter("int_amt_hand", int_amt_hand) :
                new ObjectParameter("int_amt_hand", typeof(decimal));
    
            var vchr_projdetailsParameter = vchr_projdetails != null ?
                new ObjectParameter("vchr_projdetails", vchr_projdetails) :
                new ObjectParameter("vchr_projdetails", typeof(string));
    
            var vchr_ext_propParameter = vchr_ext_prop != null ?
                new ObjectParameter("vchr_ext_prop", vchr_ext_prop) :
                new ObjectParameter("vchr_ext_prop", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_agriloan", int_loanappidParameter, int_schemeidParameter, vchr_sectorParameter, vchr_projectParameter, int_amt_estParameter, int_amt_handParameter, vchr_projdetailsParameter, vchr_ext_propParameter);
        }
    
        public virtual int Insert_To_tbl_BankDetails(Nullable<decimal> int_loanappid, string int_BankAccNo, string vchr_IFSC, string vchr_Bank, string vchr_Branch, string vchr_user, Nullable<decimal> int_modify)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var int_BankAccNoParameter = int_BankAccNo != null ?
                new ObjectParameter("int_BankAccNo", int_BankAccNo) :
                new ObjectParameter("int_BankAccNo", typeof(string));
    
            var vchr_IFSCParameter = vchr_IFSC != null ?
                new ObjectParameter("vchr_IFSC", vchr_IFSC) :
                new ObjectParameter("vchr_IFSC", typeof(string));
    
            var vchr_BankParameter = vchr_Bank != null ?
                new ObjectParameter("vchr_Bank", vchr_Bank) :
                new ObjectParameter("vchr_Bank", typeof(string));
    
            var vchr_BranchParameter = vchr_Branch != null ?
                new ObjectParameter("vchr_Branch", vchr_Branch) :
                new ObjectParameter("vchr_Branch", typeof(string));
    
            var vchr_userParameter = vchr_user != null ?
                new ObjectParameter("vchr_user", vchr_user) :
                new ObjectParameter("vchr_user", typeof(string));
    
            var int_modifyParameter = int_modify.HasValue ?
                new ObjectParameter("int_modify", int_modify) :
                new ObjectParameter("int_modify", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_BankDetails", int_loanappidParameter, int_BankAccNoParameter, vchr_IFSCParameter, vchr_BankParameter, vchr_BranchParameter, vchr_userParameter, int_modifyParameter);
        }
    
        public virtual int Insert_To_tbl_BankTransaction(Nullable<System.DateTime> dt_TDate, string int_Loanno, Nullable<decimal> int_loanamt, string int_posted, string vchr_offid, string vchr_bank, Nullable<int> trNumber)
        {
            var dt_TDateParameter = dt_TDate.HasValue ?
                new ObjectParameter("dt_TDate", dt_TDate) :
                new ObjectParameter("dt_TDate", typeof(System.DateTime));
    
            var int_LoannoParameter = int_Loanno != null ?
                new ObjectParameter("Int_Loanno", int_Loanno) :
                new ObjectParameter("Int_Loanno", typeof(string));
    
            var int_loanamtParameter = int_loanamt.HasValue ?
                new ObjectParameter("int_loanamt", int_loanamt) :
                new ObjectParameter("int_loanamt", typeof(decimal));
    
            var int_postedParameter = int_posted != null ?
                new ObjectParameter("int_posted", int_posted) :
                new ObjectParameter("int_posted", typeof(string));
    
            var vchr_offidParameter = vchr_offid != null ?
                new ObjectParameter("vchr_offid", vchr_offid) :
                new ObjectParameter("vchr_offid", typeof(string));
    
            var vchr_bankParameter = vchr_bank != null ?
                new ObjectParameter("vchr_bank", vchr_bank) :
                new ObjectParameter("vchr_bank", typeof(string));
    
            var trNumberParameter = trNumber.HasValue ?
                new ObjectParameter("TrNumber", trNumber) :
                new ObjectParameter("TrNumber", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_BankTransaction", dt_TDateParameter, int_LoannoParameter, int_loanamtParameter, int_postedParameter, vchr_offidParameter, vchr_bankParameter, trNumberParameter);
        }
    
        public virtual int Insert_To_tbl_Department(string vchr_deptname)
        {
            var vchr_deptnameParameter = vchr_deptname != null ?
                new ObjectParameter("vchr_deptname", vchr_deptname) :
                new ObjectParameter("vchr_deptname", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_Department", vchr_deptnameParameter);
        }
    
        public virtual int Insert_To_tbl_disbursement(string vchr_regno, Nullable<decimal> mny_InstallAmt, Nullable<decimal> mny_disbamt, Nullable<System.DateTime> dt_disbdate, string vchr_chkno, string vchr_loanno)
        {
            var vchr_regnoParameter = vchr_regno != null ?
                new ObjectParameter("vchr_regno", vchr_regno) :
                new ObjectParameter("vchr_regno", typeof(string));
    
            var mny_InstallAmtParameter = mny_InstallAmt.HasValue ?
                new ObjectParameter("mny_InstallAmt", mny_InstallAmt) :
                new ObjectParameter("mny_InstallAmt", typeof(decimal));
    
            var mny_disbamtParameter = mny_disbamt.HasValue ?
                new ObjectParameter("mny_disbamt", mny_disbamt) :
                new ObjectParameter("mny_disbamt", typeof(decimal));
    
            var dt_disbdateParameter = dt_disbdate.HasValue ?
                new ObjectParameter("dt_disbdate", dt_disbdate) :
                new ObjectParameter("dt_disbdate", typeof(System.DateTime));
    
            var vchr_chknoParameter = vchr_chkno != null ?
                new ObjectParameter("vchr_chkno", vchr_chkno) :
                new ObjectParameter("vchr_chkno", typeof(string));
    
            var vchr_loannoParameter = vchr_loanno != null ?
                new ObjectParameter("vchr_loanno", vchr_loanno) :
                new ObjectParameter("vchr_loanno", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_disbursement", vchr_regnoParameter, mny_InstallAmtParameter, mny_disbamtParameter, dt_disbdateParameter, vchr_chknoParameter, vchr_loannoParameter);
        }
    
        public virtual int Insert_To_tbl_disbursement_Modify(string vchr_regno, Nullable<int> int_instalNo, Nullable<decimal> mny_InstallAmt, Nullable<decimal> mny_disbamt, Nullable<decimal> mny_disbamtbal, Nullable<System.DateTime> dt_disbdate, string vchr_vouchrno, string vchr_chkno, string vchr_accno, string vchr_loanno, string vchr_verify, Nullable<System.DateTime> dt_verify, string int_BankAccNo, string vchr_IFSC, Nullable<System.DateTime> dt_UCDate, Nullable<int> int_Disb)
        {
            var vchr_regnoParameter = vchr_regno != null ?
                new ObjectParameter("vchr_regno", vchr_regno) :
                new ObjectParameter("vchr_regno", typeof(string));
    
            var int_instalNoParameter = int_instalNo.HasValue ?
                new ObjectParameter("int_instalNo", int_instalNo) :
                new ObjectParameter("int_instalNo", typeof(int));
    
            var mny_InstallAmtParameter = mny_InstallAmt.HasValue ?
                new ObjectParameter("mny_InstallAmt", mny_InstallAmt) :
                new ObjectParameter("mny_InstallAmt", typeof(decimal));
    
            var mny_disbamtParameter = mny_disbamt.HasValue ?
                new ObjectParameter("mny_disbamt", mny_disbamt) :
                new ObjectParameter("mny_disbamt", typeof(decimal));
    
            var mny_disbamtbalParameter = mny_disbamtbal.HasValue ?
                new ObjectParameter("mny_disbamtbal", mny_disbamtbal) :
                new ObjectParameter("mny_disbamtbal", typeof(decimal));
    
            var dt_disbdateParameter = dt_disbdate.HasValue ?
                new ObjectParameter("dt_disbdate", dt_disbdate) :
                new ObjectParameter("dt_disbdate", typeof(System.DateTime));
    
            var vchr_vouchrnoParameter = vchr_vouchrno != null ?
                new ObjectParameter("vchr_vouchrno", vchr_vouchrno) :
                new ObjectParameter("vchr_vouchrno", typeof(string));
    
            var vchr_chknoParameter = vchr_chkno != null ?
                new ObjectParameter("vchr_chkno", vchr_chkno) :
                new ObjectParameter("vchr_chkno", typeof(string));
    
            var vchr_accnoParameter = vchr_accno != null ?
                new ObjectParameter("vchr_accno", vchr_accno) :
                new ObjectParameter("vchr_accno", typeof(string));
    
            var vchr_loannoParameter = vchr_loanno != null ?
                new ObjectParameter("vchr_loanno", vchr_loanno) :
                new ObjectParameter("vchr_loanno", typeof(string));
    
            var vchr_verifyParameter = vchr_verify != null ?
                new ObjectParameter("vchr_verify", vchr_verify) :
                new ObjectParameter("vchr_verify", typeof(string));
    
            var dt_verifyParameter = dt_verify.HasValue ?
                new ObjectParameter("dt_verify", dt_verify) :
                new ObjectParameter("dt_verify", typeof(System.DateTime));
    
            var int_BankAccNoParameter = int_BankAccNo != null ?
                new ObjectParameter("int_BankAccNo", int_BankAccNo) :
                new ObjectParameter("int_BankAccNo", typeof(string));
    
            var vchr_IFSCParameter = vchr_IFSC != null ?
                new ObjectParameter("vchr_IFSC", vchr_IFSC) :
                new ObjectParameter("vchr_IFSC", typeof(string));
    
            var dt_UCDateParameter = dt_UCDate.HasValue ?
                new ObjectParameter("dt_UCDate", dt_UCDate) :
                new ObjectParameter("dt_UCDate", typeof(System.DateTime));
    
            var int_DisbParameter = int_Disb.HasValue ?
                new ObjectParameter("int_Disb", int_Disb) :
                new ObjectParameter("int_Disb", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_disbursement_Modify", vchr_regnoParameter, int_instalNoParameter, mny_InstallAmtParameter, mny_disbamtParameter, mny_disbamtbalParameter, dt_disbdateParameter, vchr_vouchrnoParameter, vchr_chknoParameter, vchr_accnoParameter, vchr_loannoParameter, vchr_verifyParameter, dt_verifyParameter, int_BankAccNoParameter, vchr_IFSCParameter, dt_UCDateParameter, int_DisbParameter);
        }
    
        public virtual int Insert_To_tbl_disbursement_New(string vchr_regno, Nullable<int> int_instalNo, Nullable<decimal> mny_InstallAmt, Nullable<decimal> mny_disbamt, Nullable<decimal> mny_disbamtbal, Nullable<System.DateTime> dt_disbdate, string vchr_vouchrno, string vchr_chkno, string vchr_accno, string vchr_loanno, string vchr_verify, Nullable<System.DateTime> dt_verify, string int_BankAccNo, string vchr_IFSC, Nullable<System.DateTime> dt_UCDate, Nullable<int> int_Disb)
        {
            var vchr_regnoParameter = vchr_regno != null ?
                new ObjectParameter("vchr_regno", vchr_regno) :
                new ObjectParameter("vchr_regno", typeof(string));
    
            var int_instalNoParameter = int_instalNo.HasValue ?
                new ObjectParameter("int_instalNo", int_instalNo) :
                new ObjectParameter("int_instalNo", typeof(int));
    
            var mny_InstallAmtParameter = mny_InstallAmt.HasValue ?
                new ObjectParameter("mny_InstallAmt", mny_InstallAmt) :
                new ObjectParameter("mny_InstallAmt", typeof(decimal));
    
            var mny_disbamtParameter = mny_disbamt.HasValue ?
                new ObjectParameter("mny_disbamt", mny_disbamt) :
                new ObjectParameter("mny_disbamt", typeof(decimal));
    
            var mny_disbamtbalParameter = mny_disbamtbal.HasValue ?
                new ObjectParameter("mny_disbamtbal", mny_disbamtbal) :
                new ObjectParameter("mny_disbamtbal", typeof(decimal));
    
            var dt_disbdateParameter = dt_disbdate.HasValue ?
                new ObjectParameter("dt_disbdate", dt_disbdate) :
                new ObjectParameter("dt_disbdate", typeof(System.DateTime));
    
            var vchr_vouchrnoParameter = vchr_vouchrno != null ?
                new ObjectParameter("vchr_vouchrno", vchr_vouchrno) :
                new ObjectParameter("vchr_vouchrno", typeof(string));
    
            var vchr_chknoParameter = vchr_chkno != null ?
                new ObjectParameter("vchr_chkno", vchr_chkno) :
                new ObjectParameter("vchr_chkno", typeof(string));
    
            var vchr_accnoParameter = vchr_accno != null ?
                new ObjectParameter("vchr_accno", vchr_accno) :
                new ObjectParameter("vchr_accno", typeof(string));
    
            var vchr_loannoParameter = vchr_loanno != null ?
                new ObjectParameter("vchr_loanno", vchr_loanno) :
                new ObjectParameter("vchr_loanno", typeof(string));
    
            var vchr_verifyParameter = vchr_verify != null ?
                new ObjectParameter("vchr_verify", vchr_verify) :
                new ObjectParameter("vchr_verify", typeof(string));
    
            var dt_verifyParameter = dt_verify.HasValue ?
                new ObjectParameter("dt_verify", dt_verify) :
                new ObjectParameter("dt_verify", typeof(System.DateTime));
    
            var int_BankAccNoParameter = int_BankAccNo != null ?
                new ObjectParameter("int_BankAccNo", int_BankAccNo) :
                new ObjectParameter("int_BankAccNo", typeof(string));
    
            var vchr_IFSCParameter = vchr_IFSC != null ?
                new ObjectParameter("vchr_IFSC", vchr_IFSC) :
                new ObjectParameter("vchr_IFSC", typeof(string));
    
            var dt_UCDateParameter = dt_UCDate.HasValue ?
                new ObjectParameter("dt_UCDate", dt_UCDate) :
                new ObjectParameter("dt_UCDate", typeof(System.DateTime));
    
            var int_DisbParameter = int_Disb.HasValue ?
                new ObjectParameter("int_Disb", int_Disb) :
                new ObjectParameter("int_Disb", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_disbursement_New", vchr_regnoParameter, int_instalNoParameter, mny_InstallAmtParameter, mny_disbamtParameter, mny_disbamtbalParameter, dt_disbdateParameter, vchr_vouchrnoParameter, vchr_chknoParameter, vchr_accnoParameter, vchr_loannoParameter, vchr_verifyParameter, dt_verifyParameter, int_BankAccNoParameter, vchr_IFSCParameter, dt_UCDateParameter, int_DisbParameter);
        }
    
        public virtual int Insert_To_tbl_eduloanapp(Nullable<decimal> int_loanappid, string vchr_stuname, string vchr_stufathername, string vchr_stumothername, Nullable<System.DateTime> dte_studob, Nullable<decimal> int_stuage, string vchr_caste, string sub_Cast, string vchr_presenthsename, string vchr_presentlane1, string vchr_presentlane2, string vchr_presentpost, Nullable<decimal> int_presentpin, string vchr_presentphno, string vchr_permhsename, string vchr_permlane1, string vchr_permlane2, string vchr_permpost, Nullable<decimal> int_permpin, string vchr_permphno, string vchr_coursename, string vchr_instname, string vchr_affluni, string vchr_affldetails, string vchr_duration, string vchr_month_commencement, Nullable<decimal> int_yr_commencement, string vchr_month_completion, Nullable<decimal> int_yr_completion, string vchr_parent_fullname, string vchr_parent_presenthsename, string vchr_parent_presentlane1, string vchr_parent_presentlane2, string vchr_parent_presentpost, Nullable<decimal> int_parent_presentpin, string vchr_parent_presentphno, string vchr_parent_permhsename, string vchr_parent_permlane1, string vchr_parent_permlane2, string vchr_parent_permpost, Nullable<decimal> int_parent_permpin, string vchr_parent_permphno, string vchr_parent_relationship, string vchr_employed, string vchr_parentfathername, Nullable<decimal> int_parentage, string vchr_sex, string vchr_country, string vchr_state)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var vchr_stunameParameter = vchr_stuname != null ?
                new ObjectParameter("vchr_stuname", vchr_stuname) :
                new ObjectParameter("vchr_stuname", typeof(string));
    
            var vchr_stufathernameParameter = vchr_stufathername != null ?
                new ObjectParameter("vchr_stufathername", vchr_stufathername) :
                new ObjectParameter("vchr_stufathername", typeof(string));
    
            var vchr_stumothernameParameter = vchr_stumothername != null ?
                new ObjectParameter("vchr_stumothername", vchr_stumothername) :
                new ObjectParameter("vchr_stumothername", typeof(string));
    
            var dte_studobParameter = dte_studob.HasValue ?
                new ObjectParameter("dte_studob", dte_studob) :
                new ObjectParameter("dte_studob", typeof(System.DateTime));
    
            var int_stuageParameter = int_stuage.HasValue ?
                new ObjectParameter("int_stuage", int_stuage) :
                new ObjectParameter("int_stuage", typeof(decimal));
    
            var vchr_casteParameter = vchr_caste != null ?
                new ObjectParameter("vchr_caste", vchr_caste) :
                new ObjectParameter("vchr_caste", typeof(string));
    
            var sub_CastParameter = sub_Cast != null ?
                new ObjectParameter("Sub_Cast", sub_Cast) :
                new ObjectParameter("Sub_Cast", typeof(string));
    
            var vchr_presenthsenameParameter = vchr_presenthsename != null ?
                new ObjectParameter("vchr_presenthsename", vchr_presenthsename) :
                new ObjectParameter("vchr_presenthsename", typeof(string));
    
            var vchr_presentlane1Parameter = vchr_presentlane1 != null ?
                new ObjectParameter("vchr_presentlane1", vchr_presentlane1) :
                new ObjectParameter("vchr_presentlane1", typeof(string));
    
            var vchr_presentlane2Parameter = vchr_presentlane2 != null ?
                new ObjectParameter("vchr_presentlane2", vchr_presentlane2) :
                new ObjectParameter("vchr_presentlane2", typeof(string));
    
            var vchr_presentpostParameter = vchr_presentpost != null ?
                new ObjectParameter("vchr_presentpost", vchr_presentpost) :
                new ObjectParameter("vchr_presentpost", typeof(string));
    
            var int_presentpinParameter = int_presentpin.HasValue ?
                new ObjectParameter("int_presentpin", int_presentpin) :
                new ObjectParameter("int_presentpin", typeof(decimal));
    
            var vchr_presentphnoParameter = vchr_presentphno != null ?
                new ObjectParameter("vchr_presentphno", vchr_presentphno) :
                new ObjectParameter("vchr_presentphno", typeof(string));
    
            var vchr_permhsenameParameter = vchr_permhsename != null ?
                new ObjectParameter("vchr_permhsename", vchr_permhsename) :
                new ObjectParameter("vchr_permhsename", typeof(string));
    
            var vchr_permlane1Parameter = vchr_permlane1 != null ?
                new ObjectParameter("vchr_permlane1", vchr_permlane1) :
                new ObjectParameter("vchr_permlane1", typeof(string));
    
            var vchr_permlane2Parameter = vchr_permlane2 != null ?
                new ObjectParameter("vchr_permlane2", vchr_permlane2) :
                new ObjectParameter("vchr_permlane2", typeof(string));
    
            var vchr_permpostParameter = vchr_permpost != null ?
                new ObjectParameter("vchr_permpost", vchr_permpost) :
                new ObjectParameter("vchr_permpost", typeof(string));
    
            var int_permpinParameter = int_permpin.HasValue ?
                new ObjectParameter("int_permpin", int_permpin) :
                new ObjectParameter("int_permpin", typeof(decimal));
    
            var vchr_permphnoParameter = vchr_permphno != null ?
                new ObjectParameter("vchr_permphno", vchr_permphno) :
                new ObjectParameter("vchr_permphno", typeof(string));
    
            var vchr_coursenameParameter = vchr_coursename != null ?
                new ObjectParameter("vchr_coursename", vchr_coursename) :
                new ObjectParameter("vchr_coursename", typeof(string));
    
            var vchr_instnameParameter = vchr_instname != null ?
                new ObjectParameter("vchr_instname", vchr_instname) :
                new ObjectParameter("vchr_instname", typeof(string));
    
            var vchr_affluniParameter = vchr_affluni != null ?
                new ObjectParameter("vchr_affluni", vchr_affluni) :
                new ObjectParameter("vchr_affluni", typeof(string));
    
            var vchr_affldetailsParameter = vchr_affldetails != null ?
                new ObjectParameter("vchr_affldetails", vchr_affldetails) :
                new ObjectParameter("vchr_affldetails", typeof(string));
    
            var vchr_durationParameter = vchr_duration != null ?
                new ObjectParameter("vchr_duration", vchr_duration) :
                new ObjectParameter("vchr_duration", typeof(string));
    
            var vchr_month_commencementParameter = vchr_month_commencement != null ?
                new ObjectParameter("vchr_month_commencement", vchr_month_commencement) :
                new ObjectParameter("vchr_month_commencement", typeof(string));
    
            var int_yr_commencementParameter = int_yr_commencement.HasValue ?
                new ObjectParameter("int_yr_commencement", int_yr_commencement) :
                new ObjectParameter("int_yr_commencement", typeof(decimal));
    
            var vchr_month_completionParameter = vchr_month_completion != null ?
                new ObjectParameter("vchr_month_completion", vchr_month_completion) :
                new ObjectParameter("vchr_month_completion", typeof(string));
    
            var int_yr_completionParameter = int_yr_completion.HasValue ?
                new ObjectParameter("int_yr_completion", int_yr_completion) :
                new ObjectParameter("int_yr_completion", typeof(decimal));
    
            var vchr_parent_fullnameParameter = vchr_parent_fullname != null ?
                new ObjectParameter("vchr_parent_fullname", vchr_parent_fullname) :
                new ObjectParameter("vchr_parent_fullname", typeof(string));
    
            var vchr_parent_presenthsenameParameter = vchr_parent_presenthsename != null ?
                new ObjectParameter("vchr_parent_presenthsename", vchr_parent_presenthsename) :
                new ObjectParameter("vchr_parent_presenthsename", typeof(string));
    
            var vchr_parent_presentlane1Parameter = vchr_parent_presentlane1 != null ?
                new ObjectParameter("vchr_parent_presentlane1", vchr_parent_presentlane1) :
                new ObjectParameter("vchr_parent_presentlane1", typeof(string));
    
            var vchr_parent_presentlane2Parameter = vchr_parent_presentlane2 != null ?
                new ObjectParameter("vchr_parent_presentlane2", vchr_parent_presentlane2) :
                new ObjectParameter("vchr_parent_presentlane2", typeof(string));
    
            var vchr_parent_presentpostParameter = vchr_parent_presentpost != null ?
                new ObjectParameter("vchr_parent_presentpost", vchr_parent_presentpost) :
                new ObjectParameter("vchr_parent_presentpost", typeof(string));
    
            var int_parent_presentpinParameter = int_parent_presentpin.HasValue ?
                new ObjectParameter("int_parent_presentpin", int_parent_presentpin) :
                new ObjectParameter("int_parent_presentpin", typeof(decimal));
    
            var vchr_parent_presentphnoParameter = vchr_parent_presentphno != null ?
                new ObjectParameter("vchr_parent_presentphno", vchr_parent_presentphno) :
                new ObjectParameter("vchr_parent_presentphno", typeof(string));
    
            var vchr_parent_permhsenameParameter = vchr_parent_permhsename != null ?
                new ObjectParameter("vchr_parent_permhsename", vchr_parent_permhsename) :
                new ObjectParameter("vchr_parent_permhsename", typeof(string));
    
            var vchr_parent_permlane1Parameter = vchr_parent_permlane1 != null ?
                new ObjectParameter("vchr_parent_permlane1", vchr_parent_permlane1) :
                new ObjectParameter("vchr_parent_permlane1", typeof(string));
    
            var vchr_parent_permlane2Parameter = vchr_parent_permlane2 != null ?
                new ObjectParameter("vchr_parent_permlane2", vchr_parent_permlane2) :
                new ObjectParameter("vchr_parent_permlane2", typeof(string));
    
            var vchr_parent_permpostParameter = vchr_parent_permpost != null ?
                new ObjectParameter("vchr_parent_permpost", vchr_parent_permpost) :
                new ObjectParameter("vchr_parent_permpost", typeof(string));
    
            var int_parent_permpinParameter = int_parent_permpin.HasValue ?
                new ObjectParameter("int_parent_permpin", int_parent_permpin) :
                new ObjectParameter("int_parent_permpin", typeof(decimal));
    
            var vchr_parent_permphnoParameter = vchr_parent_permphno != null ?
                new ObjectParameter("vchr_parent_permphno", vchr_parent_permphno) :
                new ObjectParameter("vchr_parent_permphno", typeof(string));
    
            var vchr_parent_relationshipParameter = vchr_parent_relationship != null ?
                new ObjectParameter("vchr_parent_relationship", vchr_parent_relationship) :
                new ObjectParameter("vchr_parent_relationship", typeof(string));
    
            var vchr_employedParameter = vchr_employed != null ?
                new ObjectParameter("vchr_employed", vchr_employed) :
                new ObjectParameter("vchr_employed", typeof(string));
    
            var vchr_parentfathernameParameter = vchr_parentfathername != null ?
                new ObjectParameter("vchr_parentfathername", vchr_parentfathername) :
                new ObjectParameter("vchr_parentfathername", typeof(string));
    
            var int_parentageParameter = int_parentage.HasValue ?
                new ObjectParameter("int_parentage", int_parentage) :
                new ObjectParameter("int_parentage", typeof(decimal));
    
            var vchr_sexParameter = vchr_sex != null ?
                new ObjectParameter("vchr_sex", vchr_sex) :
                new ObjectParameter("vchr_sex", typeof(string));
    
            var vchr_countryParameter = vchr_country != null ?
                new ObjectParameter("vchr_country", vchr_country) :
                new ObjectParameter("vchr_country", typeof(string));
    
            var vchr_stateParameter = vchr_state != null ?
                new ObjectParameter("vchr_state", vchr_state) :
                new ObjectParameter("vchr_state", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_eduloanapp", int_loanappidParameter, vchr_stunameParameter, vchr_stufathernameParameter, vchr_stumothernameParameter, dte_studobParameter, int_stuageParameter, vchr_casteParameter, sub_CastParameter, vchr_presenthsenameParameter, vchr_presentlane1Parameter, vchr_presentlane2Parameter, vchr_presentpostParameter, int_presentpinParameter, vchr_presentphnoParameter, vchr_permhsenameParameter, vchr_permlane1Parameter, vchr_permlane2Parameter, vchr_permpostParameter, int_permpinParameter, vchr_permphnoParameter, vchr_coursenameParameter, vchr_instnameParameter, vchr_affluniParameter, vchr_affldetailsParameter, vchr_durationParameter, vchr_month_commencementParameter, int_yr_commencementParameter, vchr_month_completionParameter, int_yr_completionParameter, vchr_parent_fullnameParameter, vchr_parent_presenthsenameParameter, vchr_parent_presentlane1Parameter, vchr_parent_presentlane2Parameter, vchr_parent_presentpostParameter, int_parent_presentpinParameter, vchr_parent_presentphnoParameter, vchr_parent_permhsenameParameter, vchr_parent_permlane1Parameter, vchr_parent_permlane2Parameter, vchr_parent_permpostParameter, int_parent_permpinParameter, vchr_parent_permphnoParameter, vchr_parent_relationshipParameter, vchr_employedParameter, vchr_parentfathernameParameter, int_parentageParameter, vchr_sexParameter, vchr_countryParameter, vchr_stateParameter);
        }
    
        public virtual int Insert_To_tbl_empsurety(Nullable<decimal> int_loanappid, string vchr_appreceivregno, string vchr_empname, string vchr_fathername, string vchr_spousename, string vchr_empdesig, string vchr_empoffname, string vchr_empofflane1, string vchr_empofflane2, string vchr_empoffpost, Nullable<decimal> vchr_empoffpin, string vchr_empoffphno, string vchr_presenthsename, string vchr_presentlane1, string vchr_presentlane2, string vchr_presentpost, string vchr_presentpin, string vchr_presentphno, string vchr_emppermhsename, string vchr_emppermlane1, string vchr_emppermlane2, string vchr_emppermpost, string vchr_emppermpin, string vchr_emppermphno, Nullable<System.DateTime> dte_dob, Nullable<System.DateTime> dte_eos, Nullable<System.DateTime> dte_empdateofretire, Nullable<decimal> int_basicpay, Nullable<decimal> int_netsal, Nullable<decimal> int_grosssal, string vchr_senior_empname, string vchr_senior_empdes, string vchr_senior_empoffname, string vchr_senior_emplane1, string vchr_senior_emplane2, string vchr_senior_emppost, string vchr_senior_emppin, string vchr_senior_empphone, string vchr_auditno, string vchr_depart, Nullable<bool> int_sr_officer, string vchr_scale, Nullable<System.DateTime> dte_conf_send_date, Nullable<System.DateTime> dte_conf_rec_date, string int_loanno, string vchr_oldno, string vchr_presentVillage, string vchr_presentTaluk, string vchr_presentDistrict, string vchr_PermVillage, string vchr_PermTaluk, string vchr_PermDistrict, string vchr_empoffname1, string vchr_empofflane11, string vchr_empofflane21, string vchr_empoffpost1, Nullable<decimal> vchr_empoffpin1, string vchr_empoffphno1, Nullable<bool> int_loanee, Nullable<bool> int_under, string vchr_Ashwas, string vchr_pen, string vchr_PanNo, Nullable<decimal> vchr_ITAckNo, string vchr_Aadhar, Nullable<decimal> int_personal, Nullable<decimal> int_eligibleloan, string vchr_scaleofpay, string vchr_PensionScheme, string vchr_PRAN, string vchr_PFNo, string type, string gaurantee_Type, string aadhar_No)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var vchr_appreceivregnoParameter = vchr_appreceivregno != null ?
                new ObjectParameter("vchr_appreceivregno", vchr_appreceivregno) :
                new ObjectParameter("vchr_appreceivregno", typeof(string));
    
            var vchr_empnameParameter = vchr_empname != null ?
                new ObjectParameter("vchr_empname", vchr_empname) :
                new ObjectParameter("vchr_empname", typeof(string));
    
            var vchr_fathernameParameter = vchr_fathername != null ?
                new ObjectParameter("vchr_fathername", vchr_fathername) :
                new ObjectParameter("vchr_fathername", typeof(string));
    
            var vchr_spousenameParameter = vchr_spousename != null ?
                new ObjectParameter("vchr_spousename", vchr_spousename) :
                new ObjectParameter("vchr_spousename", typeof(string));
    
            var vchr_empdesigParameter = vchr_empdesig != null ?
                new ObjectParameter("vchr_empdesig", vchr_empdesig) :
                new ObjectParameter("vchr_empdesig", typeof(string));
    
            var vchr_empoffnameParameter = vchr_empoffname != null ?
                new ObjectParameter("vchr_empoffname", vchr_empoffname) :
                new ObjectParameter("vchr_empoffname", typeof(string));
    
            var vchr_empofflane1Parameter = vchr_empofflane1 != null ?
                new ObjectParameter("vchr_empofflane1", vchr_empofflane1) :
                new ObjectParameter("vchr_empofflane1", typeof(string));
    
            var vchr_empofflane2Parameter = vchr_empofflane2 != null ?
                new ObjectParameter("vchr_empofflane2", vchr_empofflane2) :
                new ObjectParameter("vchr_empofflane2", typeof(string));
    
            var vchr_empoffpostParameter = vchr_empoffpost != null ?
                new ObjectParameter("vchr_empoffpost", vchr_empoffpost) :
                new ObjectParameter("vchr_empoffpost", typeof(string));
    
            var vchr_empoffpinParameter = vchr_empoffpin.HasValue ?
                new ObjectParameter("vchr_empoffpin", vchr_empoffpin) :
                new ObjectParameter("vchr_empoffpin", typeof(decimal));
    
            var vchr_empoffphnoParameter = vchr_empoffphno != null ?
                new ObjectParameter("vchr_empoffphno", vchr_empoffphno) :
                new ObjectParameter("vchr_empoffphno", typeof(string));
    
            var vchr_presenthsenameParameter = vchr_presenthsename != null ?
                new ObjectParameter("vchr_presenthsename", vchr_presenthsename) :
                new ObjectParameter("vchr_presenthsename", typeof(string));
    
            var vchr_presentlane1Parameter = vchr_presentlane1 != null ?
                new ObjectParameter("vchr_presentlane1", vchr_presentlane1) :
                new ObjectParameter("vchr_presentlane1", typeof(string));
    
            var vchr_presentlane2Parameter = vchr_presentlane2 != null ?
                new ObjectParameter("vchr_presentlane2", vchr_presentlane2) :
                new ObjectParameter("vchr_presentlane2", typeof(string));
    
            var vchr_presentpostParameter = vchr_presentpost != null ?
                new ObjectParameter("vchr_presentpost", vchr_presentpost) :
                new ObjectParameter("vchr_presentpost", typeof(string));
    
            var vchr_presentpinParameter = vchr_presentpin != null ?
                new ObjectParameter("vchr_presentpin", vchr_presentpin) :
                new ObjectParameter("vchr_presentpin", typeof(string));
    
            var vchr_presentphnoParameter = vchr_presentphno != null ?
                new ObjectParameter("vchr_presentphno", vchr_presentphno) :
                new ObjectParameter("vchr_presentphno", typeof(string));
    
            var vchr_emppermhsenameParameter = vchr_emppermhsename != null ?
                new ObjectParameter("vchr_emppermhsename", vchr_emppermhsename) :
                new ObjectParameter("vchr_emppermhsename", typeof(string));
    
            var vchr_emppermlane1Parameter = vchr_emppermlane1 != null ?
                new ObjectParameter("vchr_emppermlane1", vchr_emppermlane1) :
                new ObjectParameter("vchr_emppermlane1", typeof(string));
    
            var vchr_emppermlane2Parameter = vchr_emppermlane2 != null ?
                new ObjectParameter("vchr_emppermlane2", vchr_emppermlane2) :
                new ObjectParameter("vchr_emppermlane2", typeof(string));
    
            var vchr_emppermpostParameter = vchr_emppermpost != null ?
                new ObjectParameter("vchr_emppermpost", vchr_emppermpost) :
                new ObjectParameter("vchr_emppermpost", typeof(string));
    
            var vchr_emppermpinParameter = vchr_emppermpin != null ?
                new ObjectParameter("vchr_emppermpin", vchr_emppermpin) :
                new ObjectParameter("vchr_emppermpin", typeof(string));
    
            var vchr_emppermphnoParameter = vchr_emppermphno != null ?
                new ObjectParameter("vchr_emppermphno", vchr_emppermphno) :
                new ObjectParameter("vchr_emppermphno", typeof(string));
    
            var dte_dobParameter = dte_dob.HasValue ?
                new ObjectParameter("dte_dob", dte_dob) :
                new ObjectParameter("dte_dob", typeof(System.DateTime));
    
            var dte_eosParameter = dte_eos.HasValue ?
                new ObjectParameter("dte_eos", dte_eos) :
                new ObjectParameter("dte_eos", typeof(System.DateTime));
    
            var dte_empdateofretireParameter = dte_empdateofretire.HasValue ?
                new ObjectParameter("dte_empdateofretire", dte_empdateofretire) :
                new ObjectParameter("dte_empdateofretire", typeof(System.DateTime));
    
            var int_basicpayParameter = int_basicpay.HasValue ?
                new ObjectParameter("int_basicpay", int_basicpay) :
                new ObjectParameter("int_basicpay", typeof(decimal));
    
            var int_netsalParameter = int_netsal.HasValue ?
                new ObjectParameter("int_netsal", int_netsal) :
                new ObjectParameter("int_netsal", typeof(decimal));
    
            var int_grosssalParameter = int_grosssal.HasValue ?
                new ObjectParameter("int_grosssal", int_grosssal) :
                new ObjectParameter("int_grosssal", typeof(decimal));
    
            var vchr_senior_empnameParameter = vchr_senior_empname != null ?
                new ObjectParameter("vchr_senior_empname", vchr_senior_empname) :
                new ObjectParameter("vchr_senior_empname", typeof(string));
    
            var vchr_senior_empdesParameter = vchr_senior_empdes != null ?
                new ObjectParameter("vchr_senior_empdes", vchr_senior_empdes) :
                new ObjectParameter("vchr_senior_empdes", typeof(string));
    
            var vchr_senior_empoffnameParameter = vchr_senior_empoffname != null ?
                new ObjectParameter("vchr_senior_empoffname", vchr_senior_empoffname) :
                new ObjectParameter("vchr_senior_empoffname", typeof(string));
    
            var vchr_senior_emplane1Parameter = vchr_senior_emplane1 != null ?
                new ObjectParameter("vchr_senior_emplane1", vchr_senior_emplane1) :
                new ObjectParameter("vchr_senior_emplane1", typeof(string));
    
            var vchr_senior_emplane2Parameter = vchr_senior_emplane2 != null ?
                new ObjectParameter("vchr_senior_emplane2", vchr_senior_emplane2) :
                new ObjectParameter("vchr_senior_emplane2", typeof(string));
    
            var vchr_senior_emppostParameter = vchr_senior_emppost != null ?
                new ObjectParameter("vchr_senior_emppost", vchr_senior_emppost) :
                new ObjectParameter("vchr_senior_emppost", typeof(string));
    
            var vchr_senior_emppinParameter = vchr_senior_emppin != null ?
                new ObjectParameter("vchr_senior_emppin", vchr_senior_emppin) :
                new ObjectParameter("vchr_senior_emppin", typeof(string));
    
            var vchr_senior_empphoneParameter = vchr_senior_empphone != null ?
                new ObjectParameter("vchr_senior_empphone", vchr_senior_empphone) :
                new ObjectParameter("vchr_senior_empphone", typeof(string));
    
            var vchr_auditnoParameter = vchr_auditno != null ?
                new ObjectParameter("vchr_auditno", vchr_auditno) :
                new ObjectParameter("vchr_auditno", typeof(string));
    
            var vchr_departParameter = vchr_depart != null ?
                new ObjectParameter("vchr_depart", vchr_depart) :
                new ObjectParameter("vchr_depart", typeof(string));
    
            var int_sr_officerParameter = int_sr_officer.HasValue ?
                new ObjectParameter("int_sr_officer", int_sr_officer) :
                new ObjectParameter("int_sr_officer", typeof(bool));
    
            var vchr_scaleParameter = vchr_scale != null ?
                new ObjectParameter("vchr_scale", vchr_scale) :
                new ObjectParameter("vchr_scale", typeof(string));
    
            var dte_conf_send_dateParameter = dte_conf_send_date.HasValue ?
                new ObjectParameter("dte_conf_send_date", dte_conf_send_date) :
                new ObjectParameter("dte_conf_send_date", typeof(System.DateTime));
    
            var dte_conf_rec_dateParameter = dte_conf_rec_date.HasValue ?
                new ObjectParameter("dte_conf_rec_date", dte_conf_rec_date) :
                new ObjectParameter("dte_conf_rec_date", typeof(System.DateTime));
    
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            var vchr_oldnoParameter = vchr_oldno != null ?
                new ObjectParameter("vchr_oldno", vchr_oldno) :
                new ObjectParameter("vchr_oldno", typeof(string));
    
            var vchr_presentVillageParameter = vchr_presentVillage != null ?
                new ObjectParameter("vchr_presentVillage", vchr_presentVillage) :
                new ObjectParameter("vchr_presentVillage", typeof(string));
    
            var vchr_presentTalukParameter = vchr_presentTaluk != null ?
                new ObjectParameter("vchr_presentTaluk", vchr_presentTaluk) :
                new ObjectParameter("vchr_presentTaluk", typeof(string));
    
            var vchr_presentDistrictParameter = vchr_presentDistrict != null ?
                new ObjectParameter("vchr_presentDistrict", vchr_presentDistrict) :
                new ObjectParameter("vchr_presentDistrict", typeof(string));
    
            var vchr_PermVillageParameter = vchr_PermVillage != null ?
                new ObjectParameter("vchr_PermVillage", vchr_PermVillage) :
                new ObjectParameter("vchr_PermVillage", typeof(string));
    
            var vchr_PermTalukParameter = vchr_PermTaluk != null ?
                new ObjectParameter("vchr_PermTaluk", vchr_PermTaluk) :
                new ObjectParameter("vchr_PermTaluk", typeof(string));
    
            var vchr_PermDistrictParameter = vchr_PermDistrict != null ?
                new ObjectParameter("vchr_PermDistrict", vchr_PermDistrict) :
                new ObjectParameter("vchr_PermDistrict", typeof(string));
    
            var vchr_empoffname1Parameter = vchr_empoffname1 != null ?
                new ObjectParameter("vchr_empoffname1", vchr_empoffname1) :
                new ObjectParameter("vchr_empoffname1", typeof(string));
    
            var vchr_empofflane11Parameter = vchr_empofflane11 != null ?
                new ObjectParameter("vchr_empofflane11", vchr_empofflane11) :
                new ObjectParameter("vchr_empofflane11", typeof(string));
    
            var vchr_empofflane21Parameter = vchr_empofflane21 != null ?
                new ObjectParameter("vchr_empofflane21", vchr_empofflane21) :
                new ObjectParameter("vchr_empofflane21", typeof(string));
    
            var vchr_empoffpost1Parameter = vchr_empoffpost1 != null ?
                new ObjectParameter("vchr_empoffpost1", vchr_empoffpost1) :
                new ObjectParameter("vchr_empoffpost1", typeof(string));
    
            var vchr_empoffpin1Parameter = vchr_empoffpin1.HasValue ?
                new ObjectParameter("vchr_empoffpin1", vchr_empoffpin1) :
                new ObjectParameter("vchr_empoffpin1", typeof(decimal));
    
            var vchr_empoffphno1Parameter = vchr_empoffphno1 != null ?
                new ObjectParameter("vchr_empoffphno1", vchr_empoffphno1) :
                new ObjectParameter("vchr_empoffphno1", typeof(string));
    
            var int_loaneeParameter = int_loanee.HasValue ?
                new ObjectParameter("int_loanee", int_loanee) :
                new ObjectParameter("int_loanee", typeof(bool));
    
            var int_underParameter = int_under.HasValue ?
                new ObjectParameter("int_under", int_under) :
                new ObjectParameter("int_under", typeof(bool));
    
            var vchr_AshwasParameter = vchr_Ashwas != null ?
                new ObjectParameter("vchr_Ashwas", vchr_Ashwas) :
                new ObjectParameter("vchr_Ashwas", typeof(string));
    
            var vchr_penParameter = vchr_pen != null ?
                new ObjectParameter("vchr_pen", vchr_pen) :
                new ObjectParameter("vchr_pen", typeof(string));
    
            var vchr_PanNoParameter = vchr_PanNo != null ?
                new ObjectParameter("vchr_PanNo", vchr_PanNo) :
                new ObjectParameter("vchr_PanNo", typeof(string));
    
            var vchr_ITAckNoParameter = vchr_ITAckNo.HasValue ?
                new ObjectParameter("vchr_ITAckNo", vchr_ITAckNo) :
                new ObjectParameter("vchr_ITAckNo", typeof(decimal));
    
            var vchr_AadharParameter = vchr_Aadhar != null ?
                new ObjectParameter("vchr_Aadhar", vchr_Aadhar) :
                new ObjectParameter("vchr_Aadhar", typeof(string));
    
            var int_personalParameter = int_personal.HasValue ?
                new ObjectParameter("int_personal", int_personal) :
                new ObjectParameter("int_personal", typeof(decimal));
    
            var int_eligibleloanParameter = int_eligibleloan.HasValue ?
                new ObjectParameter("int_eligibleloan", int_eligibleloan) :
                new ObjectParameter("int_eligibleloan", typeof(decimal));
    
            var vchr_scaleofpayParameter = vchr_scaleofpay != null ?
                new ObjectParameter("vchr_scaleofpay", vchr_scaleofpay) :
                new ObjectParameter("vchr_scaleofpay", typeof(string));
    
            var vchr_PensionSchemeParameter = vchr_PensionScheme != null ?
                new ObjectParameter("vchr_PensionScheme", vchr_PensionScheme) :
                new ObjectParameter("vchr_PensionScheme", typeof(string));
    
            var vchr_PRANParameter = vchr_PRAN != null ?
                new ObjectParameter("vchr_PRAN", vchr_PRAN) :
                new ObjectParameter("vchr_PRAN", typeof(string));
    
            var vchr_PFNoParameter = vchr_PFNo != null ?
                new ObjectParameter("vchr_PFNo", vchr_PFNo) :
                new ObjectParameter("vchr_PFNo", typeof(string));
    
            var typeParameter = type != null ?
                new ObjectParameter("Type", type) :
                new ObjectParameter("Type", typeof(string));
    
            var gaurantee_TypeParameter = gaurantee_Type != null ?
                new ObjectParameter("Gaurantee_Type", gaurantee_Type) :
                new ObjectParameter("Gaurantee_Type", typeof(string));
    
            var aadhar_NoParameter = aadhar_No != null ?
                new ObjectParameter("Aadhar_No", aadhar_No) :
                new ObjectParameter("Aadhar_No", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_empsurety", int_loanappidParameter, vchr_appreceivregnoParameter, vchr_empnameParameter, vchr_fathernameParameter, vchr_spousenameParameter, vchr_empdesigParameter, vchr_empoffnameParameter, vchr_empofflane1Parameter, vchr_empofflane2Parameter, vchr_empoffpostParameter, vchr_empoffpinParameter, vchr_empoffphnoParameter, vchr_presenthsenameParameter, vchr_presentlane1Parameter, vchr_presentlane2Parameter, vchr_presentpostParameter, vchr_presentpinParameter, vchr_presentphnoParameter, vchr_emppermhsenameParameter, vchr_emppermlane1Parameter, vchr_emppermlane2Parameter, vchr_emppermpostParameter, vchr_emppermpinParameter, vchr_emppermphnoParameter, dte_dobParameter, dte_eosParameter, dte_empdateofretireParameter, int_basicpayParameter, int_netsalParameter, int_grosssalParameter, vchr_senior_empnameParameter, vchr_senior_empdesParameter, vchr_senior_empoffnameParameter, vchr_senior_emplane1Parameter, vchr_senior_emplane2Parameter, vchr_senior_emppostParameter, vchr_senior_emppinParameter, vchr_senior_empphoneParameter, vchr_auditnoParameter, vchr_departParameter, int_sr_officerParameter, vchr_scaleParameter, dte_conf_send_dateParameter, dte_conf_rec_dateParameter, int_loannoParameter, vchr_oldnoParameter, vchr_presentVillageParameter, vchr_presentTalukParameter, vchr_presentDistrictParameter, vchr_PermVillageParameter, vchr_PermTalukParameter, vchr_PermDistrictParameter, vchr_empoffname1Parameter, vchr_empofflane11Parameter, vchr_empofflane21Parameter, vchr_empoffpost1Parameter, vchr_empoffpin1Parameter, vchr_empoffphno1Parameter, int_loaneeParameter, int_underParameter, vchr_AshwasParameter, vchr_penParameter, vchr_PanNoParameter, vchr_ITAckNoParameter, vchr_AadharParameter, int_personalParameter, int_eligibleloanParameter, vchr_scaleofpayParameter, vchr_PensionSchemeParameter, vchr_PRANParameter, vchr_PFNoParameter, typeParameter, gaurantee_TypeParameter, aadhar_NoParameter);
        }
    
        public virtual int Insert_To_tbl_FDLICsurety(Nullable<decimal> int_loanappid, string vchr_apprecregno, string vchr_holdername, string vchr_nomineename, string vchr_hsename, string vchr_lane1, string vchr_lane2, string vchr_post, Nullable<decimal> int_pin, string vchr_phno, string vchr_village, string vchr_taluk, string vchr_district, string vchr_fdpolnum, string vchr_fdpoldetails, Nullable<System.DateTime> dt_fdpoldate, Nullable<System.DateTime> dt_mature_date, Nullable<System.DateTime> dte_conf_send_date, Nullable<System.DateTime> dte_conf_rec_date, string int_loanno, Nullable<decimal> int_MatureAmt, string vchr_type, Nullable<decimal> int_Amount)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var vchr_apprecregnoParameter = vchr_apprecregno != null ?
                new ObjectParameter("vchr_apprecregno", vchr_apprecregno) :
                new ObjectParameter("vchr_apprecregno", typeof(string));
    
            var vchr_holdernameParameter = vchr_holdername != null ?
                new ObjectParameter("vchr_holdername", vchr_holdername) :
                new ObjectParameter("vchr_holdername", typeof(string));
    
            var vchr_nomineenameParameter = vchr_nomineename != null ?
                new ObjectParameter("vchr_nomineename", vchr_nomineename) :
                new ObjectParameter("vchr_nomineename", typeof(string));
    
            var vchr_hsenameParameter = vchr_hsename != null ?
                new ObjectParameter("vchr_hsename", vchr_hsename) :
                new ObjectParameter("vchr_hsename", typeof(string));
    
            var vchr_lane1Parameter = vchr_lane1 != null ?
                new ObjectParameter("vchr_lane1", vchr_lane1) :
                new ObjectParameter("vchr_lane1", typeof(string));
    
            var vchr_lane2Parameter = vchr_lane2 != null ?
                new ObjectParameter("vchr_lane2", vchr_lane2) :
                new ObjectParameter("vchr_lane2", typeof(string));
    
            var vchr_postParameter = vchr_post != null ?
                new ObjectParameter("vchr_post", vchr_post) :
                new ObjectParameter("vchr_post", typeof(string));
    
            var int_pinParameter = int_pin.HasValue ?
                new ObjectParameter("int_pin", int_pin) :
                new ObjectParameter("int_pin", typeof(decimal));
    
            var vchr_phnoParameter = vchr_phno != null ?
                new ObjectParameter("vchr_phno", vchr_phno) :
                new ObjectParameter("vchr_phno", typeof(string));
    
            var vchr_villageParameter = vchr_village != null ?
                new ObjectParameter("vchr_village", vchr_village) :
                new ObjectParameter("vchr_village", typeof(string));
    
            var vchr_talukParameter = vchr_taluk != null ?
                new ObjectParameter("vchr_taluk", vchr_taluk) :
                new ObjectParameter("vchr_taluk", typeof(string));
    
            var vchr_districtParameter = vchr_district != null ?
                new ObjectParameter("vchr_district", vchr_district) :
                new ObjectParameter("vchr_district", typeof(string));
    
            var vchr_fdpolnumParameter = vchr_fdpolnum != null ?
                new ObjectParameter("vchr_fdpolnum", vchr_fdpolnum) :
                new ObjectParameter("vchr_fdpolnum", typeof(string));
    
            var vchr_fdpoldetailsParameter = vchr_fdpoldetails != null ?
                new ObjectParameter("vchr_fdpoldetails", vchr_fdpoldetails) :
                new ObjectParameter("vchr_fdpoldetails", typeof(string));
    
            var dt_fdpoldateParameter = dt_fdpoldate.HasValue ?
                new ObjectParameter("dt_fdpoldate", dt_fdpoldate) :
                new ObjectParameter("dt_fdpoldate", typeof(System.DateTime));
    
            var dt_mature_dateParameter = dt_mature_date.HasValue ?
                new ObjectParameter("dt_mature_date", dt_mature_date) :
                new ObjectParameter("dt_mature_date", typeof(System.DateTime));
    
            var dte_conf_send_dateParameter = dte_conf_send_date.HasValue ?
                new ObjectParameter("dte_conf_send_date", dte_conf_send_date) :
                new ObjectParameter("dte_conf_send_date", typeof(System.DateTime));
    
            var dte_conf_rec_dateParameter = dte_conf_rec_date.HasValue ?
                new ObjectParameter("dte_conf_rec_date", dte_conf_rec_date) :
                new ObjectParameter("dte_conf_rec_date", typeof(System.DateTime));
    
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            var int_MatureAmtParameter = int_MatureAmt.HasValue ?
                new ObjectParameter("int_MatureAmt", int_MatureAmt) :
                new ObjectParameter("int_MatureAmt", typeof(decimal));
    
            var vchr_typeParameter = vchr_type != null ?
                new ObjectParameter("vchr_type", vchr_type) :
                new ObjectParameter("vchr_type", typeof(string));
    
            var int_AmountParameter = int_Amount.HasValue ?
                new ObjectParameter("int_Amount", int_Amount) :
                new ObjectParameter("int_Amount", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_FDLICsurety", int_loanappidParameter, vchr_apprecregnoParameter, vchr_holdernameParameter, vchr_nomineenameParameter, vchr_hsenameParameter, vchr_lane1Parameter, vchr_lane2Parameter, vchr_postParameter, int_pinParameter, vchr_phnoParameter, vchr_villageParameter, vchr_talukParameter, vchr_districtParameter, vchr_fdpolnumParameter, vchr_fdpoldetailsParameter, dt_fdpoldateParameter, dt_mature_dateParameter, dte_conf_send_dateParameter, dte_conf_rec_dateParameter, int_loannoParameter, int_MatureAmtParameter, vchr_typeParameter, int_AmountParameter);
        }
    
        public virtual int Insert_To_tbl_Field_Visit(Nullable<int> district_Id, Nullable<int> sub_District_Id, Nullable<int> office_Id, string lonee_Name, string lonee_Location, Nullable<int> scheme_Id, Nullable<int> loan_Period, Nullable<decimal> eMI, string loan_No, Nullable<decimal> loan_Amount, Nullable<decimal> balance_Amount_Pay, Nullable<System.DateTime> disbursed_Date, string current_Loan_Status, Nullable<System.DateTime> visitted_Date, Nullable<System.DateTime> next_FollowUp_Date)
        {
            var district_IdParameter = district_Id.HasValue ?
                new ObjectParameter("District_Id", district_Id) :
                new ObjectParameter("District_Id", typeof(int));
    
            var sub_District_IdParameter = sub_District_Id.HasValue ?
                new ObjectParameter("Sub_District_Id", sub_District_Id) :
                new ObjectParameter("Sub_District_Id", typeof(int));
    
            var office_IdParameter = office_Id.HasValue ?
                new ObjectParameter("Office_Id", office_Id) :
                new ObjectParameter("Office_Id", typeof(int));
    
            var lonee_NameParameter = lonee_Name != null ?
                new ObjectParameter("Lonee_Name", lonee_Name) :
                new ObjectParameter("Lonee_Name", typeof(string));
    
            var lonee_LocationParameter = lonee_Location != null ?
                new ObjectParameter("Lonee_Location", lonee_Location) :
                new ObjectParameter("Lonee_Location", typeof(string));
    
            var scheme_IdParameter = scheme_Id.HasValue ?
                new ObjectParameter("Scheme_Id", scheme_Id) :
                new ObjectParameter("Scheme_Id", typeof(int));
    
            var loan_PeriodParameter = loan_Period.HasValue ?
                new ObjectParameter("Loan_Period", loan_Period) :
                new ObjectParameter("Loan_Period", typeof(int));
    
            var eMIParameter = eMI.HasValue ?
                new ObjectParameter("EMI", eMI) :
                new ObjectParameter("EMI", typeof(decimal));
    
            var loan_NoParameter = loan_No != null ?
                new ObjectParameter("Loan_No", loan_No) :
                new ObjectParameter("Loan_No", typeof(string));
    
            var loan_AmountParameter = loan_Amount.HasValue ?
                new ObjectParameter("Loan_Amount", loan_Amount) :
                new ObjectParameter("Loan_Amount", typeof(decimal));
    
            var balance_Amount_PayParameter = balance_Amount_Pay.HasValue ?
                new ObjectParameter("Balance_Amount_Pay", balance_Amount_Pay) :
                new ObjectParameter("Balance_Amount_Pay", typeof(decimal));
    
            var disbursed_DateParameter = disbursed_Date.HasValue ?
                new ObjectParameter("Disbursed_Date", disbursed_Date) :
                new ObjectParameter("Disbursed_Date", typeof(System.DateTime));
    
            var current_Loan_StatusParameter = current_Loan_Status != null ?
                new ObjectParameter("Current_Loan_Status", current_Loan_Status) :
                new ObjectParameter("Current_Loan_Status", typeof(string));
    
            var visitted_DateParameter = visitted_Date.HasValue ?
                new ObjectParameter("Visitted_Date", visitted_Date) :
                new ObjectParameter("Visitted_Date", typeof(System.DateTime));
    
            var next_FollowUp_DateParameter = next_FollowUp_Date.HasValue ?
                new ObjectParameter("Next_FollowUp_Date", next_FollowUp_Date) :
                new ObjectParameter("Next_FollowUp_Date", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_Field_Visit", district_IdParameter, sub_District_IdParameter, office_IdParameter, lonee_NameParameter, lonee_LocationParameter, scheme_IdParameter, loan_PeriodParameter, eMIParameter, loan_NoParameter, loan_AmountParameter, balance_Amount_PayParameter, disbursed_DateParameter, current_Loan_StatusParameter, visitted_DateParameter, next_FollowUp_DateParameter);
        }
    
        public virtual int Insert_To_tbl_foreignloan(Nullable<decimal> int_loanappid, Nullable<int> int_schemeid, string vchr_passportno, Nullable<System.DateTime> dte_duration, string vchr_forgn_orgname, string vchr_forgn_orgaddr, string vchr_visadetails)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var int_schemeidParameter = int_schemeid.HasValue ?
                new ObjectParameter("int_schemeid", int_schemeid) :
                new ObjectParameter("int_schemeid", typeof(int));
    
            var vchr_passportnoParameter = vchr_passportno != null ?
                new ObjectParameter("vchr_passportno", vchr_passportno) :
                new ObjectParameter("vchr_passportno", typeof(string));
    
            var dte_durationParameter = dte_duration.HasValue ?
                new ObjectParameter("dte_duration", dte_duration) :
                new ObjectParameter("dte_duration", typeof(System.DateTime));
    
            var vchr_forgn_orgnameParameter = vchr_forgn_orgname != null ?
                new ObjectParameter("vchr_forgn_orgname", vchr_forgn_orgname) :
                new ObjectParameter("vchr_forgn_orgname", typeof(string));
    
            var vchr_forgn_orgaddrParameter = vchr_forgn_orgaddr != null ?
                new ObjectParameter("vchr_forgn_orgaddr", vchr_forgn_orgaddr) :
                new ObjectParameter("vchr_forgn_orgaddr", typeof(string));
    
            var vchr_visadetailsParameter = vchr_visadetails != null ?
                new ObjectParameter("vchr_visadetails", vchr_visadetails) :
                new ObjectParameter("vchr_visadetails", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_foreignloan", int_loanappidParameter, int_schemeidParameter, vchr_passportnoParameter, dte_durationParameter, vchr_forgn_orgnameParameter, vchr_forgn_orgaddrParameter, vchr_visadetailsParameter);
        }
    
        public virtual int Insert_To_tbl_House_Loan(Nullable<decimal> int_loanappid, string vchr_coapp_name, string vchr_Father, string vchr_Mother, string vchr_Spouse, string vchr_Relation, Nullable<System.DateTime> dte_dob, Nullable<decimal> int_age, string vchr_Aadhaar, string vchr_electionid, string vchr_ration, Nullable<decimal> int_income_salary, Nullable<decimal> int_income_agr, Nullable<decimal> int_income_other, Nullable<decimal> int_income_total, string vchr_ownername, string vchr_extent, string vchr_surveyno, string vchr_Blockno, string vchr_TPno, string vchr_land_village, string vchr_land_taluk, string vchr_land_district, string vchr_deedtype, string vchr_deedno, string vchr_subregoffiname, string vchr_base_area, Nullable<decimal> int_no_floor, string vchr_carpet_area, string vchr_construction_type, Nullable<decimal> int_cost, string vchr_permit_no, Nullable<System.DateTime> dte_permit_date, string vchr_validity, Nullable<decimal> int_concession, string vchr_details, Nullable<decimal> int_concessionamt, Nullable<decimal> int_total_cost, Nullable<decimal> int_cash_hand, Nullable<decimal> int_subsidy, Nullable<decimal> int_loan_req, Nullable<decimal> int_repay_year)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var vchr_coapp_nameParameter = vchr_coapp_name != null ?
                new ObjectParameter("vchr_coapp_name", vchr_coapp_name) :
                new ObjectParameter("vchr_coapp_name", typeof(string));
    
            var vchr_FatherParameter = vchr_Father != null ?
                new ObjectParameter("vchr_Father", vchr_Father) :
                new ObjectParameter("vchr_Father", typeof(string));
    
            var vchr_MotherParameter = vchr_Mother != null ?
                new ObjectParameter("vchr_Mother", vchr_Mother) :
                new ObjectParameter("vchr_Mother", typeof(string));
    
            var vchr_SpouseParameter = vchr_Spouse != null ?
                new ObjectParameter("vchr_Spouse", vchr_Spouse) :
                new ObjectParameter("vchr_Spouse", typeof(string));
    
            var vchr_RelationParameter = vchr_Relation != null ?
                new ObjectParameter("vchr_Relation", vchr_Relation) :
                new ObjectParameter("vchr_Relation", typeof(string));
    
            var dte_dobParameter = dte_dob.HasValue ?
                new ObjectParameter("dte_dob", dte_dob) :
                new ObjectParameter("dte_dob", typeof(System.DateTime));
    
            var int_ageParameter = int_age.HasValue ?
                new ObjectParameter("int_age", int_age) :
                new ObjectParameter("int_age", typeof(decimal));
    
            var vchr_AadhaarParameter = vchr_Aadhaar != null ?
                new ObjectParameter("vchr_Aadhaar", vchr_Aadhaar) :
                new ObjectParameter("vchr_Aadhaar", typeof(string));
    
            var vchr_electionidParameter = vchr_electionid != null ?
                new ObjectParameter("vchr_electionid", vchr_electionid) :
                new ObjectParameter("vchr_electionid", typeof(string));
    
            var vchr_rationParameter = vchr_ration != null ?
                new ObjectParameter("vchr_ration", vchr_ration) :
                new ObjectParameter("vchr_ration", typeof(string));
    
            var int_income_salaryParameter = int_income_salary.HasValue ?
                new ObjectParameter("int_income_salary", int_income_salary) :
                new ObjectParameter("int_income_salary", typeof(decimal));
    
            var int_income_agrParameter = int_income_agr.HasValue ?
                new ObjectParameter("int_income_agr", int_income_agr) :
                new ObjectParameter("int_income_agr", typeof(decimal));
    
            var int_income_otherParameter = int_income_other.HasValue ?
                new ObjectParameter("int_income_other", int_income_other) :
                new ObjectParameter("int_income_other", typeof(decimal));
    
            var int_income_totalParameter = int_income_total.HasValue ?
                new ObjectParameter("int_income_total", int_income_total) :
                new ObjectParameter("int_income_total", typeof(decimal));
    
            var vchr_ownernameParameter = vchr_ownername != null ?
                new ObjectParameter("vchr_ownername", vchr_ownername) :
                new ObjectParameter("vchr_ownername", typeof(string));
    
            var vchr_extentParameter = vchr_extent != null ?
                new ObjectParameter("vchr_extent", vchr_extent) :
                new ObjectParameter("vchr_extent", typeof(string));
    
            var vchr_surveynoParameter = vchr_surveyno != null ?
                new ObjectParameter("vchr_surveyno", vchr_surveyno) :
                new ObjectParameter("vchr_surveyno", typeof(string));
    
            var vchr_BlocknoParameter = vchr_Blockno != null ?
                new ObjectParameter("vchr_Blockno", vchr_Blockno) :
                new ObjectParameter("vchr_Blockno", typeof(string));
    
            var vchr_TPnoParameter = vchr_TPno != null ?
                new ObjectParameter("vchr_TPno", vchr_TPno) :
                new ObjectParameter("vchr_TPno", typeof(string));
    
            var vchr_land_villageParameter = vchr_land_village != null ?
                new ObjectParameter("vchr_land_village", vchr_land_village) :
                new ObjectParameter("vchr_land_village", typeof(string));
    
            var vchr_land_talukParameter = vchr_land_taluk != null ?
                new ObjectParameter("vchr_land_taluk", vchr_land_taluk) :
                new ObjectParameter("vchr_land_taluk", typeof(string));
    
            var vchr_land_districtParameter = vchr_land_district != null ?
                new ObjectParameter("vchr_land_district", vchr_land_district) :
                new ObjectParameter("vchr_land_district", typeof(string));
    
            var vchr_deedtypeParameter = vchr_deedtype != null ?
                new ObjectParameter("vchr_deedtype", vchr_deedtype) :
                new ObjectParameter("vchr_deedtype", typeof(string));
    
            var vchr_deednoParameter = vchr_deedno != null ?
                new ObjectParameter("vchr_deedno", vchr_deedno) :
                new ObjectParameter("vchr_deedno", typeof(string));
    
            var vchr_subregoffinameParameter = vchr_subregoffiname != null ?
                new ObjectParameter("vchr_subregoffiname", vchr_subregoffiname) :
                new ObjectParameter("vchr_subregoffiname", typeof(string));
    
            var vchr_base_areaParameter = vchr_base_area != null ?
                new ObjectParameter("vchr_base_area", vchr_base_area) :
                new ObjectParameter("vchr_base_area", typeof(string));
    
            var int_no_floorParameter = int_no_floor.HasValue ?
                new ObjectParameter("int_no_floor", int_no_floor) :
                new ObjectParameter("int_no_floor", typeof(decimal));
    
            var vchr_carpet_areaParameter = vchr_carpet_area != null ?
                new ObjectParameter("vchr_carpet_area", vchr_carpet_area) :
                new ObjectParameter("vchr_carpet_area", typeof(string));
    
            var vchr_construction_typeParameter = vchr_construction_type != null ?
                new ObjectParameter("vchr_construction_type", vchr_construction_type) :
                new ObjectParameter("vchr_construction_type", typeof(string));
    
            var int_costParameter = int_cost.HasValue ?
                new ObjectParameter("int_cost", int_cost) :
                new ObjectParameter("int_cost", typeof(decimal));
    
            var vchr_permit_noParameter = vchr_permit_no != null ?
                new ObjectParameter("vchr_permit_no", vchr_permit_no) :
                new ObjectParameter("vchr_permit_no", typeof(string));
    
            var dte_permit_dateParameter = dte_permit_date.HasValue ?
                new ObjectParameter("dte_permit_date", dte_permit_date) :
                new ObjectParameter("dte_permit_date", typeof(System.DateTime));
    
            var vchr_validityParameter = vchr_validity != null ?
                new ObjectParameter("vchr_validity", vchr_validity) :
                new ObjectParameter("vchr_validity", typeof(string));
    
            var int_concessionParameter = int_concession.HasValue ?
                new ObjectParameter("int_concession", int_concession) :
                new ObjectParameter("int_concession", typeof(decimal));
    
            var vchr_detailsParameter = vchr_details != null ?
                new ObjectParameter("vchr_details", vchr_details) :
                new ObjectParameter("vchr_details", typeof(string));
    
            var int_concessionamtParameter = int_concessionamt.HasValue ?
                new ObjectParameter("int_concessionamt", int_concessionamt) :
                new ObjectParameter("int_concessionamt", typeof(decimal));
    
            var int_total_costParameter = int_total_cost.HasValue ?
                new ObjectParameter("int_total_cost", int_total_cost) :
                new ObjectParameter("int_total_cost", typeof(decimal));
    
            var int_cash_handParameter = int_cash_hand.HasValue ?
                new ObjectParameter("int_cash_hand", int_cash_hand) :
                new ObjectParameter("int_cash_hand", typeof(decimal));
    
            var int_subsidyParameter = int_subsidy.HasValue ?
                new ObjectParameter("int_subsidy", int_subsidy) :
                new ObjectParameter("int_subsidy", typeof(decimal));
    
            var int_loan_reqParameter = int_loan_req.HasValue ?
                new ObjectParameter("int_loan_req", int_loan_req) :
                new ObjectParameter("int_loan_req", typeof(decimal));
    
            var int_repay_yearParameter = int_repay_year.HasValue ?
                new ObjectParameter("int_repay_year", int_repay_year) :
                new ObjectParameter("int_repay_year", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_House_Loan", int_loanappidParameter, vchr_coapp_nameParameter, vchr_FatherParameter, vchr_MotherParameter, vchr_SpouseParameter, vchr_RelationParameter, dte_dobParameter, int_ageParameter, vchr_AadhaarParameter, vchr_electionidParameter, vchr_rationParameter, int_income_salaryParameter, int_income_agrParameter, int_income_otherParameter, int_income_totalParameter, vchr_ownernameParameter, vchr_extentParameter, vchr_surveynoParameter, vchr_BlocknoParameter, vchr_TPnoParameter, vchr_land_villageParameter, vchr_land_talukParameter, vchr_land_districtParameter, vchr_deedtypeParameter, vchr_deednoParameter, vchr_subregoffinameParameter, vchr_base_areaParameter, int_no_floorParameter, vchr_carpet_areaParameter, vchr_construction_typeParameter, int_costParameter, vchr_permit_noParameter, dte_permit_dateParameter, vchr_validityParameter, int_concessionParameter, vchr_detailsParameter, int_concessionamtParameter, int_total_costParameter, int_cash_handParameter, int_subsidyParameter, int_loan_reqParameter, int_repay_yearParameter);
        }
    
        public virtual int Insert_To_tbl_hsemaintanenceloan(Nullable<decimal> int_loanappid, Nullable<int> int_schemeid, string vchr_hse_workorgname, string vchr_hse_workorgaddr, string vchr_hse_designation, string vchr_hse_scaleofpay, Nullable<decimal> int_hse_netsal, string vchr_hseno, string vchr_surveyno, string vchr_propdetails, string vchr_hse_relation, Nullable<decimal> int_estamt, Nullable<decimal> int_amtreq, Nullable<decimal> int_gross)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var int_schemeidParameter = int_schemeid.HasValue ?
                new ObjectParameter("int_schemeid", int_schemeid) :
                new ObjectParameter("int_schemeid", typeof(int));
    
            var vchr_hse_workorgnameParameter = vchr_hse_workorgname != null ?
                new ObjectParameter("vchr_hse_workorgname", vchr_hse_workorgname) :
                new ObjectParameter("vchr_hse_workorgname", typeof(string));
    
            var vchr_hse_workorgaddrParameter = vchr_hse_workorgaddr != null ?
                new ObjectParameter("vchr_hse_workorgaddr", vchr_hse_workorgaddr) :
                new ObjectParameter("vchr_hse_workorgaddr", typeof(string));
    
            var vchr_hse_designationParameter = vchr_hse_designation != null ?
                new ObjectParameter("vchr_hse_designation", vchr_hse_designation) :
                new ObjectParameter("vchr_hse_designation", typeof(string));
    
            var vchr_hse_scaleofpayParameter = vchr_hse_scaleofpay != null ?
                new ObjectParameter("vchr_hse_scaleofpay", vchr_hse_scaleofpay) :
                new ObjectParameter("vchr_hse_scaleofpay", typeof(string));
    
            var int_hse_netsalParameter = int_hse_netsal.HasValue ?
                new ObjectParameter("int_hse_netsal", int_hse_netsal) :
                new ObjectParameter("int_hse_netsal", typeof(decimal));
    
            var vchr_hsenoParameter = vchr_hseno != null ?
                new ObjectParameter("vchr_hseno", vchr_hseno) :
                new ObjectParameter("vchr_hseno", typeof(string));
    
            var vchr_surveynoParameter = vchr_surveyno != null ?
                new ObjectParameter("vchr_surveyno", vchr_surveyno) :
                new ObjectParameter("vchr_surveyno", typeof(string));
    
            var vchr_propdetailsParameter = vchr_propdetails != null ?
                new ObjectParameter("vchr_propdetails", vchr_propdetails) :
                new ObjectParameter("vchr_propdetails", typeof(string));
    
            var vchr_hse_relationParameter = vchr_hse_relation != null ?
                new ObjectParameter("vchr_hse_relation", vchr_hse_relation) :
                new ObjectParameter("vchr_hse_relation", typeof(string));
    
            var int_estamtParameter = int_estamt.HasValue ?
                new ObjectParameter("int_estamt", int_estamt) :
                new ObjectParameter("int_estamt", typeof(decimal));
    
            var int_amtreqParameter = int_amtreq.HasValue ?
                new ObjectParameter("int_amtreq", int_amtreq) :
                new ObjectParameter("int_amtreq", typeof(decimal));
    
            var int_grossParameter = int_gross.HasValue ?
                new ObjectParameter("int_gross", int_gross) :
                new ObjectParameter("int_gross", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_hsemaintanenceloan", int_loanappidParameter, int_schemeidParameter, vchr_hse_workorgnameParameter, vchr_hse_workorgaddrParameter, vchr_hse_designationParameter, vchr_hse_scaleofpayParameter, int_hse_netsalParameter, vchr_hsenoParameter, vchr_surveynoParameter, vchr_propdetailsParameter, vchr_hse_relationParameter, int_estamtParameter, int_amtreqParameter, int_grossParameter);
        }
    
        public virtual int Insert_To_tbl_KSDC_SMART_Issues(Nullable<int> district_Id, Nullable<int> branch_Id, string office_Code, string issue_Title, string issue_Desc, string status, ObjectParameter insertedId)
        {
            var district_IdParameter = district_Id.HasValue ?
                new ObjectParameter("District_Id", district_Id) :
                new ObjectParameter("District_Id", typeof(int));
    
            var branch_IdParameter = branch_Id.HasValue ?
                new ObjectParameter("Branch_Id", branch_Id) :
                new ObjectParameter("Branch_Id", typeof(int));
    
            var office_CodeParameter = office_Code != null ?
                new ObjectParameter("Office_Code", office_Code) :
                new ObjectParameter("Office_Code", typeof(string));
    
            var issue_TitleParameter = issue_Title != null ?
                new ObjectParameter("Issue_Title", issue_Title) :
                new ObjectParameter("Issue_Title", typeof(string));
    
            var issue_DescParameter = issue_Desc != null ?
                new ObjectParameter("Issue_Desc", issue_Desc) :
                new ObjectParameter("Issue_Desc", typeof(string));
    
            var statusParameter = status != null ?
                new ObjectParameter("Status", status) :
                new ObjectParameter("Status", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_KSDC_SMART_Issues", district_IdParameter, branch_IdParameter, office_CodeParameter, issue_TitleParameter, issue_DescParameter, statusParameter, insertedId);
        }
    
        public virtual int Insert_To_tbl_KSDC_SMART_Issues_Attachments(Nullable<int> kSDC_SMART_Issues_Id, string attachment_Path)
        {
            var kSDC_SMART_Issues_IdParameter = kSDC_SMART_Issues_Id.HasValue ?
                new ObjectParameter("KSDC_SMART_Issues_Id", kSDC_SMART_Issues_Id) :
                new ObjectParameter("KSDC_SMART_Issues_Id", typeof(int));
    
            var attachment_PathParameter = attachment_Path != null ?
                new ObjectParameter("Attachment_Path", attachment_Path) :
                new ObjectParameter("Attachment_Path", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_KSDC_SMART_Issues_Attachments", kSDC_SMART_Issues_IdParameter, attachment_PathParameter);
        }
    
        public virtual int Insert_To_tbl_KSDC_SMART_Issues_Messages(Nullable<int> issue_Id, Nullable<int> office_Id, string message, Nullable<System.DateTime> created_DateTime)
        {
            var issue_IdParameter = issue_Id.HasValue ?
                new ObjectParameter("Issue_Id", issue_Id) :
                new ObjectParameter("Issue_Id", typeof(int));
    
            var office_IdParameter = office_Id.HasValue ?
                new ObjectParameter("Office_Id", office_Id) :
                new ObjectParameter("Office_Id", typeof(int));
    
            var messageParameter = message != null ?
                new ObjectParameter("Message", message) :
                new ObjectParameter("Message", typeof(string));
    
            var created_DateTimeParameter = created_DateTime.HasValue ?
                new ObjectParameter("Created_DateTime", created_DateTime) :
                new ObjectParameter("Created_DateTime", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_KSDC_SMART_Issues_Messages", issue_IdParameter, office_IdParameter, messageParameter, created_DateTimeParameter);
        }
    
        public virtual int Insert_To_tbl_landsurety(Nullable<decimal> int_loanappid, string vchr_apprecregno, string vchr_ownername, string vchr_hsename, string vchr_lane1, string vchr_lane2, string vchr_post, Nullable<decimal> int_pin, string vchr_phno, string vchr_deedtype, string vchr_deedno, string vchr_subregoffiname, string vchr_surveyno, string vchr_extent, string vchr_tpno, string vchr_village, string vchr_taluk, string vchr_district, string vchr_land_village, string vchr_land_taluk, string vchr_land_district, Nullable<System.DateTime> dte_conf_send_date, Nullable<System.DateTime> dte_conf_rec_date, string int_loanno, string vchr_fathername, string vchr_documentno, Nullable<decimal> int_ValAmt, string vchr_Ashwas, Nullable<decimal> int_LoaneeH, Nullable<bool> int_fair, Nullable<decimal> int_NotPledged)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var vchr_apprecregnoParameter = vchr_apprecregno != null ?
                new ObjectParameter("vchr_apprecregno", vchr_apprecregno) :
                new ObjectParameter("vchr_apprecregno", typeof(string));
    
            var vchr_ownernameParameter = vchr_ownername != null ?
                new ObjectParameter("vchr_ownername", vchr_ownername) :
                new ObjectParameter("vchr_ownername", typeof(string));
    
            var vchr_hsenameParameter = vchr_hsename != null ?
                new ObjectParameter("vchr_hsename", vchr_hsename) :
                new ObjectParameter("vchr_hsename", typeof(string));
    
            var vchr_lane1Parameter = vchr_lane1 != null ?
                new ObjectParameter("vchr_lane1", vchr_lane1) :
                new ObjectParameter("vchr_lane1", typeof(string));
    
            var vchr_lane2Parameter = vchr_lane2 != null ?
                new ObjectParameter("vchr_lane2", vchr_lane2) :
                new ObjectParameter("vchr_lane2", typeof(string));
    
            var vchr_postParameter = vchr_post != null ?
                new ObjectParameter("vchr_post", vchr_post) :
                new ObjectParameter("vchr_post", typeof(string));
    
            var int_pinParameter = int_pin.HasValue ?
                new ObjectParameter("int_pin", int_pin) :
                new ObjectParameter("int_pin", typeof(decimal));
    
            var vchr_phnoParameter = vchr_phno != null ?
                new ObjectParameter("vchr_phno", vchr_phno) :
                new ObjectParameter("vchr_phno", typeof(string));
    
            var vchr_deedtypeParameter = vchr_deedtype != null ?
                new ObjectParameter("vchr_deedtype", vchr_deedtype) :
                new ObjectParameter("vchr_deedtype", typeof(string));
    
            var vchr_deednoParameter = vchr_deedno != null ?
                new ObjectParameter("vchr_deedno", vchr_deedno) :
                new ObjectParameter("vchr_deedno", typeof(string));
    
            var vchr_subregoffinameParameter = vchr_subregoffiname != null ?
                new ObjectParameter("vchr_subregoffiname", vchr_subregoffiname) :
                new ObjectParameter("vchr_subregoffiname", typeof(string));
    
            var vchr_surveynoParameter = vchr_surveyno != null ?
                new ObjectParameter("vchr_surveyno", vchr_surveyno) :
                new ObjectParameter("vchr_surveyno", typeof(string));
    
            var vchr_extentParameter = vchr_extent != null ?
                new ObjectParameter("vchr_extent", vchr_extent) :
                new ObjectParameter("vchr_extent", typeof(string));
    
            var vchr_tpnoParameter = vchr_tpno != null ?
                new ObjectParameter("vchr_tpno", vchr_tpno) :
                new ObjectParameter("vchr_tpno", typeof(string));
    
            var vchr_villageParameter = vchr_village != null ?
                new ObjectParameter("vchr_village", vchr_village) :
                new ObjectParameter("vchr_village", typeof(string));
    
            var vchr_talukParameter = vchr_taluk != null ?
                new ObjectParameter("vchr_taluk", vchr_taluk) :
                new ObjectParameter("vchr_taluk", typeof(string));
    
            var vchr_districtParameter = vchr_district != null ?
                new ObjectParameter("vchr_district", vchr_district) :
                new ObjectParameter("vchr_district", typeof(string));
    
            var vchr_land_villageParameter = vchr_land_village != null ?
                new ObjectParameter("vchr_land_village", vchr_land_village) :
                new ObjectParameter("vchr_land_village", typeof(string));
    
            var vchr_land_talukParameter = vchr_land_taluk != null ?
                new ObjectParameter("vchr_land_taluk", vchr_land_taluk) :
                new ObjectParameter("vchr_land_taluk", typeof(string));
    
            var vchr_land_districtParameter = vchr_land_district != null ?
                new ObjectParameter("vchr_land_district", vchr_land_district) :
                new ObjectParameter("vchr_land_district", typeof(string));
    
            var dte_conf_send_dateParameter = dte_conf_send_date.HasValue ?
                new ObjectParameter("dte_conf_send_date", dte_conf_send_date) :
                new ObjectParameter("dte_conf_send_date", typeof(System.DateTime));
    
            var dte_conf_rec_dateParameter = dte_conf_rec_date.HasValue ?
                new ObjectParameter("dte_conf_rec_date", dte_conf_rec_date) :
                new ObjectParameter("dte_conf_rec_date", typeof(System.DateTime));
    
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            var vchr_fathernameParameter = vchr_fathername != null ?
                new ObjectParameter("vchr_fathername", vchr_fathername) :
                new ObjectParameter("vchr_fathername", typeof(string));
    
            var vchr_documentnoParameter = vchr_documentno != null ?
                new ObjectParameter("vchr_documentno", vchr_documentno) :
                new ObjectParameter("vchr_documentno", typeof(string));
    
            var int_ValAmtParameter = int_ValAmt.HasValue ?
                new ObjectParameter("int_ValAmt", int_ValAmt) :
                new ObjectParameter("int_ValAmt", typeof(decimal));
    
            var vchr_AshwasParameter = vchr_Ashwas != null ?
                new ObjectParameter("vchr_Ashwas", vchr_Ashwas) :
                new ObjectParameter("vchr_Ashwas", typeof(string));
    
            var int_LoaneeHParameter = int_LoaneeH.HasValue ?
                new ObjectParameter("int_LoaneeH", int_LoaneeH) :
                new ObjectParameter("int_LoaneeH", typeof(decimal));
    
            var int_fairParameter = int_fair.HasValue ?
                new ObjectParameter("int_fair", int_fair) :
                new ObjectParameter("int_fair", typeof(bool));
    
            var int_NotPledgedParameter = int_NotPledged.HasValue ?
                new ObjectParameter("int_NotPledged", int_NotPledged) :
                new ObjectParameter("int_NotPledged", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_landsurety", int_loanappidParameter, vchr_apprecregnoParameter, vchr_ownernameParameter, vchr_hsenameParameter, vchr_lane1Parameter, vchr_lane2Parameter, vchr_postParameter, int_pinParameter, vchr_phnoParameter, vchr_deedtypeParameter, vchr_deednoParameter, vchr_subregoffinameParameter, vchr_surveynoParameter, vchr_extentParameter, vchr_tpnoParameter, vchr_villageParameter, vchr_talukParameter, vchr_districtParameter, vchr_land_villageParameter, vchr_land_talukParameter, vchr_land_districtParameter, dte_conf_send_dateParameter, dte_conf_rec_dateParameter, int_loannoParameter, vchr_fathernameParameter, vchr_documentnoParameter, int_ValAmtParameter, vchr_AshwasParameter, int_LoaneeHParameter, int_fairParameter, int_NotPledgedParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> Insert_To_tbl_Loan_App_Issue(string office_Id, Nullable<int> district_Id, Nullable<int> subDistrict_id, string name_Applicant, string address_Applicant, Nullable<int> scheme_Id, Nullable<int> cast_Id, Nullable<int> sub_Cast_Id, string aadharNumber, Nullable<int> created_By, string remarks, string phoneNo, Nullable<int> user_Id)
        {
            var office_IdParameter = office_Id != null ?
                new ObjectParameter("Office_Id", office_Id) :
                new ObjectParameter("Office_Id", typeof(string));
    
            var district_IdParameter = district_Id.HasValue ?
                new ObjectParameter("District_Id", district_Id) :
                new ObjectParameter("District_Id", typeof(int));
    
            var subDistrict_idParameter = subDistrict_id.HasValue ?
                new ObjectParameter("SubDistrict_id", subDistrict_id) :
                new ObjectParameter("SubDistrict_id", typeof(int));
    
            var name_ApplicantParameter = name_Applicant != null ?
                new ObjectParameter("Name_Applicant", name_Applicant) :
                new ObjectParameter("Name_Applicant", typeof(string));
    
            var address_ApplicantParameter = address_Applicant != null ?
                new ObjectParameter("Address_Applicant", address_Applicant) :
                new ObjectParameter("Address_Applicant", typeof(string));
    
            var scheme_IdParameter = scheme_Id.HasValue ?
                new ObjectParameter("Scheme_Id", scheme_Id) :
                new ObjectParameter("Scheme_Id", typeof(int));
    
            var cast_IdParameter = cast_Id.HasValue ?
                new ObjectParameter("Cast_Id", cast_Id) :
                new ObjectParameter("Cast_Id", typeof(int));
    
            var sub_Cast_IdParameter = sub_Cast_Id.HasValue ?
                new ObjectParameter("Sub_Cast_Id", sub_Cast_Id) :
                new ObjectParameter("Sub_Cast_Id", typeof(int));
    
            var aadharNumberParameter = aadharNumber != null ?
                new ObjectParameter("AadharNumber", aadharNumber) :
                new ObjectParameter("AadharNumber", typeof(string));
    
            var created_ByParameter = created_By.HasValue ?
                new ObjectParameter("Created_By", created_By) :
                new ObjectParameter("Created_By", typeof(int));
    
            var remarksParameter = remarks != null ?
                new ObjectParameter("Remarks", remarks) :
                new ObjectParameter("Remarks", typeof(string));
    
            var phoneNoParameter = phoneNo != null ?
                new ObjectParameter("PhoneNo", phoneNo) :
                new ObjectParameter("PhoneNo", typeof(string));
    
            var user_IdParameter = user_Id.HasValue ?
                new ObjectParameter("User_Id", user_Id) :
                new ObjectParameter("User_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("Insert_To_tbl_Loan_App_Issue", office_IdParameter, district_IdParameter, subDistrict_idParameter, name_ApplicantParameter, address_ApplicantParameter, scheme_IdParameter, cast_IdParameter, sub_Cast_IdParameter, aadharNumberParameter, created_ByParameter, remarksParameter, phoneNoParameter, user_IdParameter);
        }
    
        public virtual int Insert_To_tbl_loanreg_NEW(string vchr_appreceivregno, string int_Loanno, Nullable<int> old_Scheme_Id, string old_Scheme_Abrivation, string old_GLHDNAME, Nullable<int> old_ACCHDCODE, string old_Scheme_Name, Nullable<System.DateTime> dte_agrement_date, Nullable<System.DateTime> dte_dateoforder, Nullable<decimal> mny_Loanamt, Nullable<decimal> mny_disbamt, Nullable<System.DateTime> dt_disbdate, Nullable<decimal> int_disb_inst, Nullable<decimal> int_inst_disbursed, Nullable<decimal> int_rate_int, Nullable<decimal> int_rate_penal, Nullable<decimal> mny_repayamt, Nullable<decimal> int_repay_inst, Nullable<decimal> mny_loanbal, Nullable<decimal> mny_prin_due, Nullable<decimal> mny_int_due, Nullable<decimal> mny_penal_due, Nullable<System.DateTime> dt_last_repay_date, Nullable<System.DateTime> dt_first_due_date, Nullable<System.DateTime> dt_next_due_date, Nullable<decimal> mny_intramt, Nullable<decimal> mny_intr_emi, Nullable<decimal> mny_intrbal, string vchr_stage, string vchr_offid, string vchr_oldloanno, Nullable<decimal> int_ots, Nullable<decimal> int_rvcd, Nullable<System.DateTime> dt_rvcd_date, string vchr_RRCno, Nullable<System.DateTime> dt_RR_Date, Nullable<decimal> int_RR, Nullable<decimal> int_Prefix, Nullable<decimal> int_ForClosing, Nullable<decimal> int_Green, string vchr_verify_remark, Nullable<decimal> int_rrdemand, string vchr_TRRCno, string vchr_offidC, Nullable<decimal> int_DefaultOts, string vchr_RRRemarks, Nullable<decimal> int_Ashwas, Nullable<decimal> int_MTR, Nullable<decimal> int_Ashwas_M, Nullable<decimal> int_repay_inst_Ext, Nullable<decimal> int_SR, Nullable<System.DateTime> dt_SRDate)
        {
            var vchr_appreceivregnoParameter = vchr_appreceivregno != null ?
                new ObjectParameter("vchr_appreceivregno", vchr_appreceivregno) :
                new ObjectParameter("vchr_appreceivregno", typeof(string));
    
            var int_LoannoParameter = int_Loanno != null ?
                new ObjectParameter("Int_Loanno", int_Loanno) :
                new ObjectParameter("Int_Loanno", typeof(string));
    
            var old_Scheme_IdParameter = old_Scheme_Id.HasValue ?
                new ObjectParameter("Old_Scheme_Id", old_Scheme_Id) :
                new ObjectParameter("Old_Scheme_Id", typeof(int));
    
            var old_Scheme_AbrivationParameter = old_Scheme_Abrivation != null ?
                new ObjectParameter("Old_Scheme_Abrivation", old_Scheme_Abrivation) :
                new ObjectParameter("Old_Scheme_Abrivation", typeof(string));
    
            var old_GLHDNAMEParameter = old_GLHDNAME != null ?
                new ObjectParameter("Old_GLHDNAME", old_GLHDNAME) :
                new ObjectParameter("Old_GLHDNAME", typeof(string));
    
            var old_ACCHDCODEParameter = old_ACCHDCODE.HasValue ?
                new ObjectParameter("Old_ACCHDCODE", old_ACCHDCODE) :
                new ObjectParameter("Old_ACCHDCODE", typeof(int));
    
            var old_Scheme_NameParameter = old_Scheme_Name != null ?
                new ObjectParameter("Old_Scheme_Name", old_Scheme_Name) :
                new ObjectParameter("Old_Scheme_Name", typeof(string));
    
            var dte_agrement_dateParameter = dte_agrement_date.HasValue ?
                new ObjectParameter("dte_agrement_date", dte_agrement_date) :
                new ObjectParameter("dte_agrement_date", typeof(System.DateTime));
    
            var dte_dateoforderParameter = dte_dateoforder.HasValue ?
                new ObjectParameter("dte_dateoforder", dte_dateoforder) :
                new ObjectParameter("dte_dateoforder", typeof(System.DateTime));
    
            var mny_LoanamtParameter = mny_Loanamt.HasValue ?
                new ObjectParameter("mny_Loanamt", mny_Loanamt) :
                new ObjectParameter("mny_Loanamt", typeof(decimal));
    
            var mny_disbamtParameter = mny_disbamt.HasValue ?
                new ObjectParameter("mny_disbamt", mny_disbamt) :
                new ObjectParameter("mny_disbamt", typeof(decimal));
    
            var dt_disbdateParameter = dt_disbdate.HasValue ?
                new ObjectParameter("dt_disbdate", dt_disbdate) :
                new ObjectParameter("dt_disbdate", typeof(System.DateTime));
    
            var int_disb_instParameter = int_disb_inst.HasValue ?
                new ObjectParameter("int_disb_inst", int_disb_inst) :
                new ObjectParameter("int_disb_inst", typeof(decimal));
    
            var int_inst_disbursedParameter = int_inst_disbursed.HasValue ?
                new ObjectParameter("int_inst_disbursed", int_inst_disbursed) :
                new ObjectParameter("int_inst_disbursed", typeof(decimal));
    
            var int_rate_intParameter = int_rate_int.HasValue ?
                new ObjectParameter("int_rate_int", int_rate_int) :
                new ObjectParameter("int_rate_int", typeof(decimal));
    
            var int_rate_penalParameter = int_rate_penal.HasValue ?
                new ObjectParameter("int_rate_penal", int_rate_penal) :
                new ObjectParameter("int_rate_penal", typeof(decimal));
    
            var mny_repayamtParameter = mny_repayamt.HasValue ?
                new ObjectParameter("mny_repayamt", mny_repayamt) :
                new ObjectParameter("mny_repayamt", typeof(decimal));
    
            var int_repay_instParameter = int_repay_inst.HasValue ?
                new ObjectParameter("int_repay_inst", int_repay_inst) :
                new ObjectParameter("int_repay_inst", typeof(decimal));
    
            var mny_loanbalParameter = mny_loanbal.HasValue ?
                new ObjectParameter("mny_loanbal", mny_loanbal) :
                new ObjectParameter("mny_loanbal", typeof(decimal));
    
            var mny_prin_dueParameter = mny_prin_due.HasValue ?
                new ObjectParameter("mny_prin_due", mny_prin_due) :
                new ObjectParameter("mny_prin_due", typeof(decimal));
    
            var mny_int_dueParameter = mny_int_due.HasValue ?
                new ObjectParameter("mny_int_due", mny_int_due) :
                new ObjectParameter("mny_int_due", typeof(decimal));
    
            var mny_penal_dueParameter = mny_penal_due.HasValue ?
                new ObjectParameter("mny_penal_due", mny_penal_due) :
                new ObjectParameter("mny_penal_due", typeof(decimal));
    
            var dt_last_repay_dateParameter = dt_last_repay_date.HasValue ?
                new ObjectParameter("dt_last_repay_date", dt_last_repay_date) :
                new ObjectParameter("dt_last_repay_date", typeof(System.DateTime));
    
            var dt_first_due_dateParameter = dt_first_due_date.HasValue ?
                new ObjectParameter("dt_first_due_date", dt_first_due_date) :
                new ObjectParameter("dt_first_due_date", typeof(System.DateTime));
    
            var dt_next_due_dateParameter = dt_next_due_date.HasValue ?
                new ObjectParameter("dt_next_due_date", dt_next_due_date) :
                new ObjectParameter("dt_next_due_date", typeof(System.DateTime));
    
            var mny_intramtParameter = mny_intramt.HasValue ?
                new ObjectParameter("mny_intramt", mny_intramt) :
                new ObjectParameter("mny_intramt", typeof(decimal));
    
            var mny_intr_emiParameter = mny_intr_emi.HasValue ?
                new ObjectParameter("mny_intr_emi", mny_intr_emi) :
                new ObjectParameter("mny_intr_emi", typeof(decimal));
    
            var mny_intrbalParameter = mny_intrbal.HasValue ?
                new ObjectParameter("mny_intrbal", mny_intrbal) :
                new ObjectParameter("mny_intrbal", typeof(decimal));
    
            var vchr_stageParameter = vchr_stage != null ?
                new ObjectParameter("vchr_stage", vchr_stage) :
                new ObjectParameter("vchr_stage", typeof(string));
    
            var vchr_offidParameter = vchr_offid != null ?
                new ObjectParameter("vchr_offid", vchr_offid) :
                new ObjectParameter("vchr_offid", typeof(string));
    
            var vchr_oldloannoParameter = vchr_oldloanno != null ?
                new ObjectParameter("vchr_oldloanno", vchr_oldloanno) :
                new ObjectParameter("vchr_oldloanno", typeof(string));
    
            var int_otsParameter = int_ots.HasValue ?
                new ObjectParameter("int_ots", int_ots) :
                new ObjectParameter("int_ots", typeof(decimal));
    
            var int_rvcdParameter = int_rvcd.HasValue ?
                new ObjectParameter("int_rvcd", int_rvcd) :
                new ObjectParameter("int_rvcd", typeof(decimal));
    
            var dt_rvcd_dateParameter = dt_rvcd_date.HasValue ?
                new ObjectParameter("dt_rvcd_date", dt_rvcd_date) :
                new ObjectParameter("dt_rvcd_date", typeof(System.DateTime));
    
            var vchr_RRCnoParameter = vchr_RRCno != null ?
                new ObjectParameter("vchr_RRCno", vchr_RRCno) :
                new ObjectParameter("vchr_RRCno", typeof(string));
    
            var dt_RR_DateParameter = dt_RR_Date.HasValue ?
                new ObjectParameter("dt_RR_Date", dt_RR_Date) :
                new ObjectParameter("dt_RR_Date", typeof(System.DateTime));
    
            var int_RRParameter = int_RR.HasValue ?
                new ObjectParameter("int_RR", int_RR) :
                new ObjectParameter("int_RR", typeof(decimal));
    
            var int_PrefixParameter = int_Prefix.HasValue ?
                new ObjectParameter("int_Prefix", int_Prefix) :
                new ObjectParameter("int_Prefix", typeof(decimal));
    
            var int_ForClosingParameter = int_ForClosing.HasValue ?
                new ObjectParameter("int_ForClosing", int_ForClosing) :
                new ObjectParameter("int_ForClosing", typeof(decimal));
    
            var int_GreenParameter = int_Green.HasValue ?
                new ObjectParameter("int_Green", int_Green) :
                new ObjectParameter("int_Green", typeof(decimal));
    
            var vchr_verify_remarkParameter = vchr_verify_remark != null ?
                new ObjectParameter("vchr_verify_remark", vchr_verify_remark) :
                new ObjectParameter("vchr_verify_remark", typeof(string));
    
            var int_rrdemandParameter = int_rrdemand.HasValue ?
                new ObjectParameter("int_rrdemand", int_rrdemand) :
                new ObjectParameter("int_rrdemand", typeof(decimal));
    
            var vchr_TRRCnoParameter = vchr_TRRCno != null ?
                new ObjectParameter("vchr_TRRCno", vchr_TRRCno) :
                new ObjectParameter("vchr_TRRCno", typeof(string));
    
            var vchr_offidCParameter = vchr_offidC != null ?
                new ObjectParameter("vchr_offidC", vchr_offidC) :
                new ObjectParameter("vchr_offidC", typeof(string));
    
            var int_DefaultOtsParameter = int_DefaultOts.HasValue ?
                new ObjectParameter("int_DefaultOts", int_DefaultOts) :
                new ObjectParameter("int_DefaultOts", typeof(decimal));
    
            var vchr_RRRemarksParameter = vchr_RRRemarks != null ?
                new ObjectParameter("vchr_RRRemarks", vchr_RRRemarks) :
                new ObjectParameter("vchr_RRRemarks", typeof(string));
    
            var int_AshwasParameter = int_Ashwas.HasValue ?
                new ObjectParameter("int_Ashwas", int_Ashwas) :
                new ObjectParameter("int_Ashwas", typeof(decimal));
    
            var int_MTRParameter = int_MTR.HasValue ?
                new ObjectParameter("int_MTR", int_MTR) :
                new ObjectParameter("int_MTR", typeof(decimal));
    
            var int_Ashwas_MParameter = int_Ashwas_M.HasValue ?
                new ObjectParameter("int_Ashwas_M", int_Ashwas_M) :
                new ObjectParameter("int_Ashwas_M", typeof(decimal));
    
            var int_repay_inst_ExtParameter = int_repay_inst_Ext.HasValue ?
                new ObjectParameter("int_repay_inst_Ext", int_repay_inst_Ext) :
                new ObjectParameter("int_repay_inst_Ext", typeof(decimal));
    
            var int_SRParameter = int_SR.HasValue ?
                new ObjectParameter("int_SR", int_SR) :
                new ObjectParameter("int_SR", typeof(decimal));
    
            var dt_SRDateParameter = dt_SRDate.HasValue ?
                new ObjectParameter("dt_SRDate", dt_SRDate) :
                new ObjectParameter("dt_SRDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_loanreg_NEW", vchr_appreceivregnoParameter, int_LoannoParameter, old_Scheme_IdParameter, old_Scheme_AbrivationParameter, old_GLHDNAMEParameter, old_ACCHDCODEParameter, old_Scheme_NameParameter, dte_agrement_dateParameter, dte_dateoforderParameter, mny_LoanamtParameter, mny_disbamtParameter, dt_disbdateParameter, int_disb_instParameter, int_inst_disbursedParameter, int_rate_intParameter, int_rate_penalParameter, mny_repayamtParameter, int_repay_instParameter, mny_loanbalParameter, mny_prin_dueParameter, mny_int_dueParameter, mny_penal_dueParameter, dt_last_repay_dateParameter, dt_first_due_dateParameter, dt_next_due_dateParameter, mny_intramtParameter, mny_intr_emiParameter, mny_intrbalParameter, vchr_stageParameter, vchr_offidParameter, vchr_oldloannoParameter, int_otsParameter, int_rvcdParameter, dt_rvcd_dateParameter, vchr_RRCnoParameter, dt_RR_DateParameter, int_RRParameter, int_PrefixParameter, int_ForClosingParameter, int_GreenParameter, vchr_verify_remarkParameter, int_rrdemandParameter, vchr_TRRCnoParameter, vchr_offidCParameter, int_DefaultOtsParameter, vchr_RRRemarksParameter, int_AshwasParameter, int_MTRParameter, int_Ashwas_MParameter, int_repay_inst_ExtParameter, int_SRParameter, dt_SRDateParameter);
        }
    
        public virtual int Insert_To_tbl_loanTrans(string int_loanno, string chr_dd_no, Nullable<decimal> int_amt, Nullable<decimal> int_prin_dues, string vchr_offidC, Nullable<System.DateTime> dt_transaction, string chr_mod_payment, string chr_transtype, string chr_remark, string chr_rec_no, Nullable<decimal> int_penal_amt, Nullable<decimal> int_prin_amt, Nullable<decimal> int_int_r, Nullable<decimal> int_penal_r, Nullable<decimal> int_int_dues, Nullable<decimal> int_penal_dues, Nullable<decimal> int_int_amt)
        {
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            var chr_dd_noParameter = chr_dd_no != null ?
                new ObjectParameter("chr_dd_no", chr_dd_no) :
                new ObjectParameter("chr_dd_no", typeof(string));
    
            var int_amtParameter = int_amt.HasValue ?
                new ObjectParameter("int_amt", int_amt) :
                new ObjectParameter("int_amt", typeof(decimal));
    
            var int_prin_duesParameter = int_prin_dues.HasValue ?
                new ObjectParameter("int_prin_dues", int_prin_dues) :
                new ObjectParameter("int_prin_dues", typeof(decimal));
    
            var vchr_offidCParameter = vchr_offidC != null ?
                new ObjectParameter("vchr_offidC", vchr_offidC) :
                new ObjectParameter("vchr_offidC", typeof(string));
    
            var dt_transactionParameter = dt_transaction.HasValue ?
                new ObjectParameter("dt_transaction", dt_transaction) :
                new ObjectParameter("dt_transaction", typeof(System.DateTime));
    
            var chr_mod_paymentParameter = chr_mod_payment != null ?
                new ObjectParameter("chr_mod_payment", chr_mod_payment) :
                new ObjectParameter("chr_mod_payment", typeof(string));
    
            var chr_transtypeParameter = chr_transtype != null ?
                new ObjectParameter("chr_transtype", chr_transtype) :
                new ObjectParameter("chr_transtype", typeof(string));
    
            var chr_remarkParameter = chr_remark != null ?
                new ObjectParameter("chr_remark", chr_remark) :
                new ObjectParameter("chr_remark", typeof(string));
    
            var chr_rec_noParameter = chr_rec_no != null ?
                new ObjectParameter("chr_rec_no", chr_rec_no) :
                new ObjectParameter("chr_rec_no", typeof(string));
    
            var int_penal_amtParameter = int_penal_amt.HasValue ?
                new ObjectParameter("int_penal_amt", int_penal_amt) :
                new ObjectParameter("int_penal_amt", typeof(decimal));
    
            var int_prin_amtParameter = int_prin_amt.HasValue ?
                new ObjectParameter("int_prin_amt", int_prin_amt) :
                new ObjectParameter("int_prin_amt", typeof(decimal));
    
            var int_int_rParameter = int_int_r.HasValue ?
                new ObjectParameter("int_int_r", int_int_r) :
                new ObjectParameter("int_int_r", typeof(decimal));
    
            var int_penal_rParameter = int_penal_r.HasValue ?
                new ObjectParameter("int_penal_r", int_penal_r) :
                new ObjectParameter("int_penal_r", typeof(decimal));
    
            var int_int_duesParameter = int_int_dues.HasValue ?
                new ObjectParameter("int_int_dues", int_int_dues) :
                new ObjectParameter("int_int_dues", typeof(decimal));
    
            var int_penal_duesParameter = int_penal_dues.HasValue ?
                new ObjectParameter("int_penal_dues", int_penal_dues) :
                new ObjectParameter("int_penal_dues", typeof(decimal));
    
            var int_int_amtParameter = int_int_amt.HasValue ?
                new ObjectParameter("int_int_amt", int_int_amt) :
                new ObjectParameter("int_int_amt", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_loanTrans", int_loannoParameter, chr_dd_noParameter, int_amtParameter, int_prin_duesParameter, vchr_offidCParameter, dt_transactionParameter, chr_mod_paymentParameter, chr_transtypeParameter, chr_remarkParameter, chr_rec_noParameter, int_penal_amtParameter, int_prin_amtParameter, int_int_rParameter, int_penal_rParameter, int_int_duesParameter, int_penal_duesParameter, int_int_amtParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> Insert_To_tbl_loanTrans_Repayment(string int_loanno, string chr_dd_no, Nullable<decimal> int_amt, Nullable<decimal> int_prin_dues, string vchr_offidC, Nullable<System.DateTime> dt_transaction, string chr_mod_payment, string chr_transtype, string chr_remark, string chr_rec_no, Nullable<decimal> int_penal_amt, Nullable<decimal> int_prin_amt, Nullable<decimal> int_int_r, Nullable<decimal> int_penal_r, Nullable<decimal> int_int_dues, Nullable<decimal> int_penal_dues, Nullable<decimal> int_int_amt)
        {
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            var chr_dd_noParameter = chr_dd_no != null ?
                new ObjectParameter("chr_dd_no", chr_dd_no) :
                new ObjectParameter("chr_dd_no", typeof(string));
    
            var int_amtParameter = int_amt.HasValue ?
                new ObjectParameter("int_amt", int_amt) :
                new ObjectParameter("int_amt", typeof(decimal));
    
            var int_prin_duesParameter = int_prin_dues.HasValue ?
                new ObjectParameter("int_prin_dues", int_prin_dues) :
                new ObjectParameter("int_prin_dues", typeof(decimal));
    
            var vchr_offidCParameter = vchr_offidC != null ?
                new ObjectParameter("vchr_offidC", vchr_offidC) :
                new ObjectParameter("vchr_offidC", typeof(string));
    
            var dt_transactionParameter = dt_transaction.HasValue ?
                new ObjectParameter("dt_transaction", dt_transaction) :
                new ObjectParameter("dt_transaction", typeof(System.DateTime));
    
            var chr_mod_paymentParameter = chr_mod_payment != null ?
                new ObjectParameter("chr_mod_payment", chr_mod_payment) :
                new ObjectParameter("chr_mod_payment", typeof(string));
    
            var chr_transtypeParameter = chr_transtype != null ?
                new ObjectParameter("chr_transtype", chr_transtype) :
                new ObjectParameter("chr_transtype", typeof(string));
    
            var chr_remarkParameter = chr_remark != null ?
                new ObjectParameter("chr_remark", chr_remark) :
                new ObjectParameter("chr_remark", typeof(string));
    
            var chr_rec_noParameter = chr_rec_no != null ?
                new ObjectParameter("chr_rec_no", chr_rec_no) :
                new ObjectParameter("chr_rec_no", typeof(string));
    
            var int_penal_amtParameter = int_penal_amt.HasValue ?
                new ObjectParameter("int_penal_amt", int_penal_amt) :
                new ObjectParameter("int_penal_amt", typeof(decimal));
    
            var int_prin_amtParameter = int_prin_amt.HasValue ?
                new ObjectParameter("int_prin_amt", int_prin_amt) :
                new ObjectParameter("int_prin_amt", typeof(decimal));
    
            var int_int_rParameter = int_int_r.HasValue ?
                new ObjectParameter("int_int_r", int_int_r) :
                new ObjectParameter("int_int_r", typeof(decimal));
    
            var int_penal_rParameter = int_penal_r.HasValue ?
                new ObjectParameter("int_penal_r", int_penal_r) :
                new ObjectParameter("int_penal_r", typeof(decimal));
    
            var int_int_duesParameter = int_int_dues.HasValue ?
                new ObjectParameter("int_int_dues", int_int_dues) :
                new ObjectParameter("int_int_dues", typeof(decimal));
    
            var int_penal_duesParameter = int_penal_dues.HasValue ?
                new ObjectParameter("int_penal_dues", int_penal_dues) :
                new ObjectParameter("int_penal_dues", typeof(decimal));
    
            var int_int_amtParameter = int_int_amt.HasValue ?
                new ObjectParameter("int_int_amt", int_int_amt) :
                new ObjectParameter("int_int_amt", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("Insert_To_tbl_loanTrans_Repayment", int_loannoParameter, chr_dd_noParameter, int_amtParameter, int_prin_duesParameter, vchr_offidCParameter, dt_transactionParameter, chr_mod_paymentParameter, chr_transtypeParameter, chr_remarkParameter, chr_rec_noParameter, int_penal_amtParameter, int_prin_amtParameter, int_int_rParameter, int_penal_rParameter, int_int_duesParameter, int_penal_duesParameter, int_int_amtParameter);
        }
    
        public virtual int Insert_To_tbl_Logs(string method_Name, string log_Details, Nullable<int> user_Id, Nullable<System.DateTime> log_DateTime, string type)
        {
            var method_NameParameter = method_Name != null ?
                new ObjectParameter("Method_Name", method_Name) :
                new ObjectParameter("Method_Name", typeof(string));
    
            var log_DetailsParameter = log_Details != null ?
                new ObjectParameter("Log_Details", log_Details) :
                new ObjectParameter("Log_Details", typeof(string));
    
            var user_IdParameter = user_Id.HasValue ?
                new ObjectParameter("User_Id", user_Id) :
                new ObjectParameter("User_Id", typeof(int));
    
            var log_DateTimeParameter = log_DateTime.HasValue ?
                new ObjectParameter("Log_DateTime", log_DateTime) :
                new ObjectParameter("Log_DateTime", typeof(System.DateTime));
    
            var typeParameter = type != null ?
                new ObjectParameter("Type", type) :
                new ObjectParameter("Type", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_Logs", method_NameParameter, log_DetailsParameter, user_IdParameter, log_DateTimeParameter, typeParameter);
        }
    
        public virtual int Insert_To_tbl_marriageloan(Nullable<decimal> int_loanappid, Nullable<int> int_schemeid, string vchr_bridename, string vchr_brideaddr, Nullable<int> int_brideage, Nullable<System.DateTime> dte_bridedob, string vchr_groomname, string vchr_groomaddr, Nullable<System.DateTime> dte_dt)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var int_schemeidParameter = int_schemeid.HasValue ?
                new ObjectParameter("int_schemeid", int_schemeid) :
                new ObjectParameter("int_schemeid", typeof(int));
    
            var vchr_bridenameParameter = vchr_bridename != null ?
                new ObjectParameter("vchr_bridename", vchr_bridename) :
                new ObjectParameter("vchr_bridename", typeof(string));
    
            var vchr_brideaddrParameter = vchr_brideaddr != null ?
                new ObjectParameter("vchr_brideaddr", vchr_brideaddr) :
                new ObjectParameter("vchr_brideaddr", typeof(string));
    
            var int_brideageParameter = int_brideage.HasValue ?
                new ObjectParameter("int_brideage", int_brideage) :
                new ObjectParameter("int_brideage", typeof(int));
    
            var dte_bridedobParameter = dte_bridedob.HasValue ?
                new ObjectParameter("dte_bridedob", dte_bridedob) :
                new ObjectParameter("dte_bridedob", typeof(System.DateTime));
    
            var vchr_groomnameParameter = vchr_groomname != null ?
                new ObjectParameter("vchr_groomname", vchr_groomname) :
                new ObjectParameter("vchr_groomname", typeof(string));
    
            var vchr_groomaddrParameter = vchr_groomaddr != null ?
                new ObjectParameter("vchr_groomaddr", vchr_groomaddr) :
                new ObjectParameter("vchr_groomaddr", typeof(string));
    
            var dte_dtParameter = dte_dt.HasValue ?
                new ObjectParameter("dte_dt", dte_dt) :
                new ObjectParameter("dte_dt", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_marriageloan", int_loanappidParameter, int_schemeidParameter, vchr_bridenameParameter, vchr_brideaddrParameter, int_brideageParameter, dte_bridedobParameter, vchr_groomnameParameter, vchr_groomaddrParameter, dte_dtParameter);
        }
    
        public virtual int Insert_To_tbl_personal_loan(Nullable<decimal> int_loanappid, Nullable<int> int_schemeid, string vchr_workorgname, string vchr_workorgaddr, string vchr_designation, string vchr_scaleofpay, Nullable<decimal> int_netsal, Nullable<decimal> int_workgrosssal, string vchr_Purpose)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var int_schemeidParameter = int_schemeid.HasValue ?
                new ObjectParameter("int_schemeid", int_schemeid) :
                new ObjectParameter("int_schemeid", typeof(int));
    
            var vchr_workorgnameParameter = vchr_workorgname != null ?
                new ObjectParameter("vchr_workorgname", vchr_workorgname) :
                new ObjectParameter("vchr_workorgname", typeof(string));
    
            var vchr_workorgaddrParameter = vchr_workorgaddr != null ?
                new ObjectParameter("vchr_workorgaddr", vchr_workorgaddr) :
                new ObjectParameter("vchr_workorgaddr", typeof(string));
    
            var vchr_designationParameter = vchr_designation != null ?
                new ObjectParameter("vchr_designation", vchr_designation) :
                new ObjectParameter("vchr_designation", typeof(string));
    
            var vchr_scaleofpayParameter = vchr_scaleofpay != null ?
                new ObjectParameter("vchr_scaleofpay", vchr_scaleofpay) :
                new ObjectParameter("vchr_scaleofpay", typeof(string));
    
            var int_netsalParameter = int_netsal.HasValue ?
                new ObjectParameter("int_netsal", int_netsal) :
                new ObjectParameter("int_netsal", typeof(decimal));
    
            var int_workgrosssalParameter = int_workgrosssal.HasValue ?
                new ObjectParameter("int_workgrosssal", int_workgrosssal) :
                new ObjectParameter("int_workgrosssal", typeof(decimal));
    
            var vchr_PurposeParameter = vchr_Purpose != null ?
                new ObjectParameter("vchr_Purpose", vchr_Purpose) :
                new ObjectParameter("vchr_Purpose", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_personal_loan", int_loanappidParameter, int_schemeidParameter, vchr_workorgnameParameter, vchr_workorgaddrParameter, vchr_designationParameter, vchr_scaleofpayParameter, int_netsalParameter, int_workgrosssalParameter, vchr_PurposeParameter);
        }
    
        public virtual int Insert_To_tbl_Projects(Nullable<int> agency_Id, Nullable<int> scheme_Id, Nullable<int> sector_Id, string project, Nullable<int> isActive, Nullable<int> createdBy)
        {
            var agency_IdParameter = agency_Id.HasValue ?
                new ObjectParameter("Agency_Id", agency_Id) :
                new ObjectParameter("Agency_Id", typeof(int));
    
            var scheme_IdParameter = scheme_Id.HasValue ?
                new ObjectParameter("Scheme_Id", scheme_Id) :
                new ObjectParameter("Scheme_Id", typeof(int));
    
            var sector_IdParameter = sector_Id.HasValue ?
                new ObjectParameter("Sector_Id", sector_Id) :
                new ObjectParameter("Sector_Id", typeof(int));
    
            var projectParameter = project != null ?
                new ObjectParameter("Project", project) :
                new ObjectParameter("Project", typeof(string));
    
            var isActiveParameter = isActive.HasValue ?
                new ObjectParameter("IsActive", isActive) :
                new ObjectParameter("IsActive", typeof(int));
    
            var createdByParameter = createdBy.HasValue ?
                new ObjectParameter("CreatedBy", createdBy) :
                new ObjectParameter("CreatedBy", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_Projects", agency_IdParameter, scheme_IdParameter, sector_IdParameter, projectParameter, isActiveParameter, createdByParameter);
        }
    
        public virtual int Insert_To_tbl_Roles(string name, string description, Nullable<int> created_By, Nullable<int> isActive)
        {
            var nameParameter = name != null ?
                new ObjectParameter("Name", name) :
                new ObjectParameter("Name", typeof(string));
    
            var descriptionParameter = description != null ?
                new ObjectParameter("Description", description) :
                new ObjectParameter("Description", typeof(string));
    
            var created_ByParameter = created_By.HasValue ?
                new ObjectParameter("Created_By", created_By) :
                new ObjectParameter("Created_By", typeof(int));
    
            var isActiveParameter = isActive.HasValue ?
                new ObjectParameter("IsActive", isActive) :
                new ObjectParameter("IsActive", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_Roles", nameParameter, descriptionParameter, created_ByParameter, isActiveParameter);
        }
    
        public virtual int Insert_To_tbl_Schemes(Nullable<int> agency_Id, string scheme, Nullable<decimal> loan_Amount, Nullable<double> loan_Interest, Nullable<int> loan_Period, Nullable<double> penal_Interest, Nullable<int> max_Age, Nullable<int> min_Age, Nullable<decimal> anninc_Rural_Max, Nullable<decimal> anninc_Urban_Max, Nullable<decimal> beneficiaryContribution, Nullable<int> isActive, Nullable<int> created_By)
        {
            var agency_IdParameter = agency_Id.HasValue ?
                new ObjectParameter("Agency_Id", agency_Id) :
                new ObjectParameter("Agency_Id", typeof(int));
    
            var schemeParameter = scheme != null ?
                new ObjectParameter("Scheme", scheme) :
                new ObjectParameter("Scheme", typeof(string));
    
            var loan_AmountParameter = loan_Amount.HasValue ?
                new ObjectParameter("Loan_Amount", loan_Amount) :
                new ObjectParameter("Loan_Amount", typeof(decimal));
    
            var loan_InterestParameter = loan_Interest.HasValue ?
                new ObjectParameter("Loan_Interest", loan_Interest) :
                new ObjectParameter("Loan_Interest", typeof(double));
    
            var loan_PeriodParameter = loan_Period.HasValue ?
                new ObjectParameter("Loan_Period", loan_Period) :
                new ObjectParameter("Loan_Period", typeof(int));
    
            var penal_InterestParameter = penal_Interest.HasValue ?
                new ObjectParameter("Penal_Interest", penal_Interest) :
                new ObjectParameter("Penal_Interest", typeof(double));
    
            var max_AgeParameter = max_Age.HasValue ?
                new ObjectParameter("Max_Age", max_Age) :
                new ObjectParameter("Max_Age", typeof(int));
    
            var min_AgeParameter = min_Age.HasValue ?
                new ObjectParameter("Min_Age", min_Age) :
                new ObjectParameter("Min_Age", typeof(int));
    
            var anninc_Rural_MaxParameter = anninc_Rural_Max.HasValue ?
                new ObjectParameter("Anninc_Rural_Max", anninc_Rural_Max) :
                new ObjectParameter("Anninc_Rural_Max", typeof(decimal));
    
            var anninc_Urban_MaxParameter = anninc_Urban_Max.HasValue ?
                new ObjectParameter("Anninc_Urban_Max", anninc_Urban_Max) :
                new ObjectParameter("Anninc_Urban_Max", typeof(decimal));
    
            var beneficiaryContributionParameter = beneficiaryContribution.HasValue ?
                new ObjectParameter("BeneficiaryContribution", beneficiaryContribution) :
                new ObjectParameter("BeneficiaryContribution", typeof(decimal));
    
            var isActiveParameter = isActive.HasValue ?
                new ObjectParameter("IsActive", isActive) :
                new ObjectParameter("IsActive", typeof(int));
    
            var created_ByParameter = created_By.HasValue ?
                new ObjectParameter("Created_By", created_By) :
                new ObjectParameter("Created_By", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_Schemes", agency_IdParameter, schemeParameter, loan_AmountParameter, loan_InterestParameter, loan_PeriodParameter, penal_InterestParameter, max_AgeParameter, min_AgeParameter, anninc_Rural_MaxParameter, anninc_Urban_MaxParameter, beneficiaryContributionParameter, isActiveParameter, created_ByParameter);
        }
    
        public virtual int Insert_To_tbl_Sectors(string sector, string code, Nullable<int> isActive, Nullable<int> createdBy)
        {
            var sectorParameter = sector != null ?
                new ObjectParameter("Sector", sector) :
                new ObjectParameter("Sector", typeof(string));
    
            var codeParameter = code != null ?
                new ObjectParameter("Code", code) :
                new ObjectParameter("Code", typeof(string));
    
            var isActiveParameter = isActive.HasValue ?
                new ObjectParameter("IsActive", isActive) :
                new ObjectParameter("IsActive", typeof(int));
    
            var createdByParameter = createdBy.HasValue ?
                new ObjectParameter("CreatedBy", createdBy) :
                new ObjectParameter("CreatedBy", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_Sectors", sectorParameter, codeParameter, isActiveParameter, createdByParameter);
        }
    
        public virtual int Insert_To_tbl_selfloan(Nullable<int> int_loanappid, Nullable<int> int_schemeid, string vchr_sector, string vchr_project, Nullable<decimal> int_amt_est, Nullable<decimal> int_amt_hand, string vchr_projdetails, string applicant_Experience)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(int));
    
            var int_schemeidParameter = int_schemeid.HasValue ?
                new ObjectParameter("int_schemeid", int_schemeid) :
                new ObjectParameter("int_schemeid", typeof(int));
    
            var vchr_sectorParameter = vchr_sector != null ?
                new ObjectParameter("vchr_sector", vchr_sector) :
                new ObjectParameter("vchr_sector", typeof(string));
    
            var vchr_projectParameter = vchr_project != null ?
                new ObjectParameter("vchr_project", vchr_project) :
                new ObjectParameter("vchr_project", typeof(string));
    
            var int_amt_estParameter = int_amt_est.HasValue ?
                new ObjectParameter("int_amt_est", int_amt_est) :
                new ObjectParameter("int_amt_est", typeof(decimal));
    
            var int_amt_handParameter = int_amt_hand.HasValue ?
                new ObjectParameter("int_amt_hand", int_amt_hand) :
                new ObjectParameter("int_amt_hand", typeof(decimal));
    
            var vchr_projdetailsParameter = vchr_projdetails != null ?
                new ObjectParameter("vchr_projdetails", vchr_projdetails) :
                new ObjectParameter("vchr_projdetails", typeof(string));
    
            var applicant_ExperienceParameter = applicant_Experience != null ?
                new ObjectParameter("Applicant_Experience", applicant_Experience) :
                new ObjectParameter("Applicant_Experience", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_selfloan", int_loanappidParameter, int_schemeidParameter, vchr_sectorParameter, vchr_projectParameter, int_amt_estParameter, int_amt_handParameter, vchr_projdetailsParameter, applicant_ExperienceParameter);
        }
    
        public virtual int Insert_To_tbl_Users(string name, string username, string password, Nullable<int> role_id, Nullable<int> office_Id, string emailId, string mobile, string address, Nullable<int> createdBy, Nullable<int> isActive)
        {
            var nameParameter = name != null ?
                new ObjectParameter("Name", name) :
                new ObjectParameter("Name", typeof(string));
    
            var usernameParameter = username != null ?
                new ObjectParameter("Username", username) :
                new ObjectParameter("Username", typeof(string));
    
            var passwordParameter = password != null ?
                new ObjectParameter("Password", password) :
                new ObjectParameter("Password", typeof(string));
    
            var role_idParameter = role_id.HasValue ?
                new ObjectParameter("Role_id", role_id) :
                new ObjectParameter("Role_id", typeof(int));
    
            var office_IdParameter = office_Id.HasValue ?
                new ObjectParameter("Office_Id", office_Id) :
                new ObjectParameter("Office_Id", typeof(int));
    
            var emailIdParameter = emailId != null ?
                new ObjectParameter("EmailId", emailId) :
                new ObjectParameter("EmailId", typeof(string));
    
            var mobileParameter = mobile != null ?
                new ObjectParameter("Mobile", mobile) :
                new ObjectParameter("Mobile", typeof(string));
    
            var addressParameter = address != null ?
                new ObjectParameter("Address", address) :
                new ObjectParameter("Address", typeof(string));
    
            var createdByParameter = createdBy.HasValue ?
                new ObjectParameter("CreatedBy", createdBy) :
                new ObjectParameter("CreatedBy", typeof(int));
    
            var isActiveParameter = isActive.HasValue ?
                new ObjectParameter("IsActive", isActive) :
                new ObjectParameter("IsActive", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_Users", nameParameter, usernameParameter, passwordParameter, role_idParameter, office_IdParameter, emailIdParameter, mobileParameter, addressParameter, createdByParameter, isActiveParameter);
        }
    
        public virtual int Insert_To_tbl_veh_hme_loan(Nullable<decimal> int_loanappid, Nullable<int> int_schemeid, string vchr_workorgname, string vchr_workorgaddr, string vchr_designation, string vchr_scaleofpay, Nullable<decimal> int_netsal, Nullable<decimal> int_workgrosssal, string vchr_vehname, Nullable<decimal> int_marketrate)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var int_schemeidParameter = int_schemeid.HasValue ?
                new ObjectParameter("int_schemeid", int_schemeid) :
                new ObjectParameter("int_schemeid", typeof(int));
    
            var vchr_workorgnameParameter = vchr_workorgname != null ?
                new ObjectParameter("vchr_workorgname", vchr_workorgname) :
                new ObjectParameter("vchr_workorgname", typeof(string));
    
            var vchr_workorgaddrParameter = vchr_workorgaddr != null ?
                new ObjectParameter("vchr_workorgaddr", vchr_workorgaddr) :
                new ObjectParameter("vchr_workorgaddr", typeof(string));
    
            var vchr_designationParameter = vchr_designation != null ?
                new ObjectParameter("vchr_designation", vchr_designation) :
                new ObjectParameter("vchr_designation", typeof(string));
    
            var vchr_scaleofpayParameter = vchr_scaleofpay != null ?
                new ObjectParameter("vchr_scaleofpay", vchr_scaleofpay) :
                new ObjectParameter("vchr_scaleofpay", typeof(string));
    
            var int_netsalParameter = int_netsal.HasValue ?
                new ObjectParameter("int_netsal", int_netsal) :
                new ObjectParameter("int_netsal", typeof(decimal));
    
            var int_workgrosssalParameter = int_workgrosssal.HasValue ?
                new ObjectParameter("int_workgrosssal", int_workgrosssal) :
                new ObjectParameter("int_workgrosssal", typeof(decimal));
    
            var vchr_vehnameParameter = vchr_vehname != null ?
                new ObjectParameter("vchr_vehname", vchr_vehname) :
                new ObjectParameter("vchr_vehname", typeof(string));
    
            var int_marketrateParameter = int_marketrate.HasValue ?
                new ObjectParameter("int_marketrate", int_marketrate) :
                new ObjectParameter("int_marketrate", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_veh_hme_loan", int_loanappidParameter, int_schemeidParameter, vchr_workorgnameParameter, vchr_workorgaddrParameter, vchr_designationParameter, vchr_scaleofpayParameter, int_netsalParameter, int_workgrosssalParameter, vchr_vehnameParameter, int_marketrateParameter);
        }
    
        public virtual int Insert_To_tbl_workcapital(Nullable<decimal> int_loanappid, Nullable<int> int_schemeid, string vchr_sector, string vchr_project, Nullable<decimal> int_amt_est, Nullable<decimal> int_amt_hand, string vchr_projdetails, string vchr_businessdet, string applicant_Experience)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var int_schemeidParameter = int_schemeid.HasValue ?
                new ObjectParameter("int_schemeid", int_schemeid) :
                new ObjectParameter("int_schemeid", typeof(int));
    
            var vchr_sectorParameter = vchr_sector != null ?
                new ObjectParameter("vchr_sector", vchr_sector) :
                new ObjectParameter("vchr_sector", typeof(string));
    
            var vchr_projectParameter = vchr_project != null ?
                new ObjectParameter("vchr_project", vchr_project) :
                new ObjectParameter("vchr_project", typeof(string));
    
            var int_amt_estParameter = int_amt_est.HasValue ?
                new ObjectParameter("int_amt_est", int_amt_est) :
                new ObjectParameter("int_amt_est", typeof(decimal));
    
            var int_amt_handParameter = int_amt_hand.HasValue ?
                new ObjectParameter("int_amt_hand", int_amt_hand) :
                new ObjectParameter("int_amt_hand", typeof(decimal));
    
            var vchr_projdetailsParameter = vchr_projdetails != null ?
                new ObjectParameter("vchr_projdetails", vchr_projdetails) :
                new ObjectParameter("vchr_projdetails", typeof(string));
    
            var vchr_businessdetParameter = vchr_businessdet != null ?
                new ObjectParameter("vchr_businessdet", vchr_businessdet) :
                new ObjectParameter("vchr_businessdet", typeof(string));
    
            var applicant_ExperienceParameter = applicant_Experience != null ?
                new ObjectParameter("Applicant_Experience", applicant_Experience) :
                new ObjectParameter("Applicant_Experience", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_workcapital", int_loanappidParameter, int_schemeidParameter, vchr_sectorParameter, vchr_projectParameter, int_amt_estParameter, int_amt_handParameter, vchr_projdetailsParameter, vchr_businessdetParameter, applicant_ExperienceParameter);
        }
    
        public virtual int Loan_Disbursement_By_LoanAppId(Nullable<decimal> int_loanappid, string loanNo, Nullable<int> instNo, Nullable<System.DateTime> disbDate, Nullable<System.DateTime> firstDueDate, Nullable<decimal> eMI, string voucher_No, string chk_No, Nullable<int> user_Id)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var loanNoParameter = loanNo != null ?
                new ObjectParameter("LoanNo", loanNo) :
                new ObjectParameter("LoanNo", typeof(string));
    
            var instNoParameter = instNo.HasValue ?
                new ObjectParameter("InstNo", instNo) :
                new ObjectParameter("InstNo", typeof(int));
    
            var disbDateParameter = disbDate.HasValue ?
                new ObjectParameter("DisbDate", disbDate) :
                new ObjectParameter("DisbDate", typeof(System.DateTime));
    
            var firstDueDateParameter = firstDueDate.HasValue ?
                new ObjectParameter("FirstDueDate", firstDueDate) :
                new ObjectParameter("FirstDueDate", typeof(System.DateTime));
    
            var eMIParameter = eMI.HasValue ?
                new ObjectParameter("EMI", eMI) :
                new ObjectParameter("EMI", typeof(decimal));
    
            var voucher_NoParameter = voucher_No != null ?
                new ObjectParameter("Voucher_No", voucher_No) :
                new ObjectParameter("Voucher_No", typeof(string));
    
            var chk_NoParameter = chk_No != null ?
                new ObjectParameter("Chk_No", chk_No) :
                new ObjectParameter("Chk_No", typeof(string));
    
            var user_IdParameter = user_Id.HasValue ?
                new ObjectParameter("User_Id", user_Id) :
                new ObjectParameter("User_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Loan_Disbursement_By_LoanAppId", int_loanappidParameter, loanNoParameter, instNoParameter, disbDateParameter, firstDueDateParameter, eMIParameter, voucher_NoParameter, chk_NoParameter, user_IdParameter);
        }
    
        public virtual ObjectResult<Login_Result> Login(string username, string password)
        {
            var usernameParameter = username != null ?
                new ObjectParameter("Username", username) :
                new ObjectParameter("Username", typeof(string));
    
            var passwordParameter = password != null ?
                new ObjectParameter("Password", password) :
                new ObjectParameter("Password", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Login_Result>("Login", usernameParameter, passwordParameter);
        }
    
        public virtual ObjectResult<New_Insert_To_tbl_loanapp_Result> New_Insert_To_tbl_loanapp(Nullable<int> loan_App_Issue_Id, string vchr_appreceivregno, Nullable<decimal> int_no, string vchr_applname, string vchr_hsename, string vchr_place1, string vchr_place2, string vchr_phno, string vchr_caste, string vchr_sex, string vchr_district, string vchr_subdistrict, Nullable<decimal> int_anninc, string vchr_offid, Nullable<int> int_schemeid, string subCast, Nullable<System.DateTime> dte_dob, Nullable<int> age, string vchr_post, Nullable<decimal> int_pincode, string status, string aadharNumber)
        {
            var loan_App_Issue_IdParameter = loan_App_Issue_Id.HasValue ?
                new ObjectParameter("Loan_App_Issue_Id", loan_App_Issue_Id) :
                new ObjectParameter("Loan_App_Issue_Id", typeof(int));
    
            var vchr_appreceivregnoParameter = vchr_appreceivregno != null ?
                new ObjectParameter("vchr_appreceivregno", vchr_appreceivregno) :
                new ObjectParameter("vchr_appreceivregno", typeof(string));
    
            var int_noParameter = int_no.HasValue ?
                new ObjectParameter("int_no", int_no) :
                new ObjectParameter("int_no", typeof(decimal));
    
            var vchr_applnameParameter = vchr_applname != null ?
                new ObjectParameter("vchr_applname", vchr_applname) :
                new ObjectParameter("vchr_applname", typeof(string));
    
            var vchr_hsenameParameter = vchr_hsename != null ?
                new ObjectParameter("vchr_hsename", vchr_hsename) :
                new ObjectParameter("vchr_hsename", typeof(string));
    
            var vchr_place1Parameter = vchr_place1 != null ?
                new ObjectParameter("vchr_place1", vchr_place1) :
                new ObjectParameter("vchr_place1", typeof(string));
    
            var vchr_place2Parameter = vchr_place2 != null ?
                new ObjectParameter("vchr_place2", vchr_place2) :
                new ObjectParameter("vchr_place2", typeof(string));
    
            var vchr_phnoParameter = vchr_phno != null ?
                new ObjectParameter("vchr_phno", vchr_phno) :
                new ObjectParameter("vchr_phno", typeof(string));
    
            var vchr_casteParameter = vchr_caste != null ?
                new ObjectParameter("vchr_caste", vchr_caste) :
                new ObjectParameter("vchr_caste", typeof(string));
    
            var vchr_sexParameter = vchr_sex != null ?
                new ObjectParameter("vchr_sex", vchr_sex) :
                new ObjectParameter("vchr_sex", typeof(string));
    
            var vchr_districtParameter = vchr_district != null ?
                new ObjectParameter("vchr_district", vchr_district) :
                new ObjectParameter("vchr_district", typeof(string));
    
            var vchr_subdistrictParameter = vchr_subdistrict != null ?
                new ObjectParameter("vchr_subdistrict", vchr_subdistrict) :
                new ObjectParameter("vchr_subdistrict", typeof(string));
    
            var int_annincParameter = int_anninc.HasValue ?
                new ObjectParameter("int_anninc", int_anninc) :
                new ObjectParameter("int_anninc", typeof(decimal));
    
            var vchr_offidParameter = vchr_offid != null ?
                new ObjectParameter("vchr_offid", vchr_offid) :
                new ObjectParameter("vchr_offid", typeof(string));
    
            var int_schemeidParameter = int_schemeid.HasValue ?
                new ObjectParameter("int_schemeid", int_schemeid) :
                new ObjectParameter("int_schemeid", typeof(int));
    
            var subCastParameter = subCast != null ?
                new ObjectParameter("SubCast", subCast) :
                new ObjectParameter("SubCast", typeof(string));
    
            var dte_dobParameter = dte_dob.HasValue ?
                new ObjectParameter("dte_dob", dte_dob) :
                new ObjectParameter("dte_dob", typeof(System.DateTime));
    
            var ageParameter = age.HasValue ?
                new ObjectParameter("Age", age) :
                new ObjectParameter("Age", typeof(int));
    
            var vchr_postParameter = vchr_post != null ?
                new ObjectParameter("vchr_post", vchr_post) :
                new ObjectParameter("vchr_post", typeof(string));
    
            var int_pincodeParameter = int_pincode.HasValue ?
                new ObjectParameter("int_pincode", int_pincode) :
                new ObjectParameter("int_pincode", typeof(decimal));
    
            var statusParameter = status != null ?
                new ObjectParameter("Status", status) :
                new ObjectParameter("Status", typeof(string));
    
            var aadharNumberParameter = aadharNumber != null ?
                new ObjectParameter("AadharNumber", aadharNumber) :
                new ObjectParameter("AadharNumber", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<New_Insert_To_tbl_loanapp_Result>("New_Insert_To_tbl_loanapp", loan_App_Issue_IdParameter, vchr_appreceivregnoParameter, int_noParameter, vchr_applnameParameter, vchr_hsenameParameter, vchr_place1Parameter, vchr_place2Parameter, vchr_phnoParameter, vchr_casteParameter, vchr_sexParameter, vchr_districtParameter, vchr_subdistrictParameter, int_annincParameter, vchr_offidParameter, int_schemeidParameter, subCastParameter, dte_dobParameter, ageParameter, vchr_postParameter, int_pincodeParameter, statusParameter, aadharNumberParameter);
        }
    
        public virtual int New_Update_To_tbl_loanapp(Nullable<decimal> int_loanappid, string vchr_applname, string vchr_Aadhaar, Nullable<int> int_schemeid, string vchr_hsename, string vchr_place1, string vchr_place2, string vchr_district, string vchr_subdistrict, Nullable<int> int_pincode, string vchr_post, string vchr_phno, string vchr_fathername, string vchr_spousename, string vchr_caste, string subCast, string vchr_sex, Nullable<System.DateTime> dte_dob, Nullable<int> age, string vchr_village, string vchr_taluk, Nullable<int> block_Id, Nullable<int> municipality_Id, string vchr_panchayat, Nullable<int> corporation_Id, Nullable<int> lokSabha_Id, Nullable<int> assemply_Id, Nullable<decimal> int_anninc, string vchr_ration, Nullable<decimal> int_loanamt_req, string chr_status, string landMark, string ward, Nullable<System.DateTime> retirement_Date, Nullable<decimal> int_amtsanction)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var vchr_applnameParameter = vchr_applname != null ?
                new ObjectParameter("vchr_applname", vchr_applname) :
                new ObjectParameter("vchr_applname", typeof(string));
    
            var vchr_AadhaarParameter = vchr_Aadhaar != null ?
                new ObjectParameter("vchr_Aadhaar", vchr_Aadhaar) :
                new ObjectParameter("vchr_Aadhaar", typeof(string));
    
            var int_schemeidParameter = int_schemeid.HasValue ?
                new ObjectParameter("int_schemeid", int_schemeid) :
                new ObjectParameter("int_schemeid", typeof(int));
    
            var vchr_hsenameParameter = vchr_hsename != null ?
                new ObjectParameter("vchr_hsename", vchr_hsename) :
                new ObjectParameter("vchr_hsename", typeof(string));
    
            var vchr_place1Parameter = vchr_place1 != null ?
                new ObjectParameter("vchr_place1", vchr_place1) :
                new ObjectParameter("vchr_place1", typeof(string));
    
            var vchr_place2Parameter = vchr_place2 != null ?
                new ObjectParameter("vchr_place2", vchr_place2) :
                new ObjectParameter("vchr_place2", typeof(string));
    
            var vchr_districtParameter = vchr_district != null ?
                new ObjectParameter("vchr_district", vchr_district) :
                new ObjectParameter("vchr_district", typeof(string));
    
            var vchr_subdistrictParameter = vchr_subdistrict != null ?
                new ObjectParameter("vchr_subdistrict", vchr_subdistrict) :
                new ObjectParameter("vchr_subdistrict", typeof(string));
    
            var int_pincodeParameter = int_pincode.HasValue ?
                new ObjectParameter("int_pincode", int_pincode) :
                new ObjectParameter("int_pincode", typeof(int));
    
            var vchr_postParameter = vchr_post != null ?
                new ObjectParameter("vchr_post", vchr_post) :
                new ObjectParameter("vchr_post", typeof(string));
    
            var vchr_phnoParameter = vchr_phno != null ?
                new ObjectParameter("vchr_phno", vchr_phno) :
                new ObjectParameter("vchr_phno", typeof(string));
    
            var vchr_fathernameParameter = vchr_fathername != null ?
                new ObjectParameter("vchr_fathername", vchr_fathername) :
                new ObjectParameter("vchr_fathername", typeof(string));
    
            var vchr_spousenameParameter = vchr_spousename != null ?
                new ObjectParameter("vchr_spousename", vchr_spousename) :
                new ObjectParameter("vchr_spousename", typeof(string));
    
            var vchr_casteParameter = vchr_caste != null ?
                new ObjectParameter("vchr_caste", vchr_caste) :
                new ObjectParameter("vchr_caste", typeof(string));
    
            var subCastParameter = subCast != null ?
                new ObjectParameter("SubCast", subCast) :
                new ObjectParameter("SubCast", typeof(string));
    
            var vchr_sexParameter = vchr_sex != null ?
                new ObjectParameter("vchr_sex", vchr_sex) :
                new ObjectParameter("vchr_sex", typeof(string));
    
            var dte_dobParameter = dte_dob.HasValue ?
                new ObjectParameter("dte_dob", dte_dob) :
                new ObjectParameter("dte_dob", typeof(System.DateTime));
    
            var ageParameter = age.HasValue ?
                new ObjectParameter("Age", age) :
                new ObjectParameter("Age", typeof(int));
    
            var vchr_villageParameter = vchr_village != null ?
                new ObjectParameter("vchr_village", vchr_village) :
                new ObjectParameter("vchr_village", typeof(string));
    
            var vchr_talukParameter = vchr_taluk != null ?
                new ObjectParameter("vchr_taluk", vchr_taluk) :
                new ObjectParameter("vchr_taluk", typeof(string));
    
            var block_IdParameter = block_Id.HasValue ?
                new ObjectParameter("Block_Id", block_Id) :
                new ObjectParameter("Block_Id", typeof(int));
    
            var municipality_IdParameter = municipality_Id.HasValue ?
                new ObjectParameter("Municipality_Id", municipality_Id) :
                new ObjectParameter("Municipality_Id", typeof(int));
    
            var vchr_panchayatParameter = vchr_panchayat != null ?
                new ObjectParameter("vchr_panchayat", vchr_panchayat) :
                new ObjectParameter("vchr_panchayat", typeof(string));
    
            var corporation_IdParameter = corporation_Id.HasValue ?
                new ObjectParameter("Corporation_Id", corporation_Id) :
                new ObjectParameter("Corporation_Id", typeof(int));
    
            var lokSabha_IdParameter = lokSabha_Id.HasValue ?
                new ObjectParameter("LokSabha_Id", lokSabha_Id) :
                new ObjectParameter("LokSabha_Id", typeof(int));
    
            var assemply_IdParameter = assemply_Id.HasValue ?
                new ObjectParameter("Assemply_Id", assemply_Id) :
                new ObjectParameter("Assemply_Id", typeof(int));
    
            var int_annincParameter = int_anninc.HasValue ?
                new ObjectParameter("int_anninc", int_anninc) :
                new ObjectParameter("int_anninc", typeof(decimal));
    
            var vchr_rationParameter = vchr_ration != null ?
                new ObjectParameter("vchr_ration", vchr_ration) :
                new ObjectParameter("vchr_ration", typeof(string));
    
            var int_loanamt_reqParameter = int_loanamt_req.HasValue ?
                new ObjectParameter("int_loanamt_req", int_loanamt_req) :
                new ObjectParameter("int_loanamt_req", typeof(decimal));
    
            var chr_statusParameter = chr_status != null ?
                new ObjectParameter("chr_status", chr_status) :
                new ObjectParameter("chr_status", typeof(string));
    
            var landMarkParameter = landMark != null ?
                new ObjectParameter("LandMark", landMark) :
                new ObjectParameter("LandMark", typeof(string));
    
            var wardParameter = ward != null ?
                new ObjectParameter("Ward", ward) :
                new ObjectParameter("Ward", typeof(string));
    
            var retirement_DateParameter = retirement_Date.HasValue ?
                new ObjectParameter("Retirement_Date", retirement_Date) :
                new ObjectParameter("Retirement_Date", typeof(System.DateTime));
    
            var int_amtsanctionParameter = int_amtsanction.HasValue ?
                new ObjectParameter("int_amtsanction", int_amtsanction) :
                new ObjectParameter("int_amtsanction", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("New_Update_To_tbl_loanapp", int_loanappidParameter, vchr_applnameParameter, vchr_AadhaarParameter, int_schemeidParameter, vchr_hsenameParameter, vchr_place1Parameter, vchr_place2Parameter, vchr_districtParameter, vchr_subdistrictParameter, int_pincodeParameter, vchr_postParameter, vchr_phnoParameter, vchr_fathernameParameter, vchr_spousenameParameter, vchr_casteParameter, subCastParameter, vchr_sexParameter, dte_dobParameter, ageParameter, vchr_villageParameter, vchr_talukParameter, block_IdParameter, municipality_IdParameter, vchr_panchayatParameter, corporation_IdParameter, lokSabha_IdParameter, assemply_IdParameter, int_annincParameter, vchr_rationParameter, int_loanamt_reqParameter, chr_statusParameter, landMarkParameter, wardParameter, retirement_DateParameter, int_amtsanctionParameter);
        }
    
        public virtual int New_Update_To_tbl_loanapp_Sanction(Nullable<decimal> int_loanappid, string vchr_applname, string vchr_Aadhaar, Nullable<int> int_schemeid, string vchr_hsename, string vchr_place1, string vchr_place2, string vchr_district, string vchr_subdistrict, Nullable<int> int_pincode, string vchr_post, string vchr_phno, string vchr_fathername, string vchr_spousename, string vchr_caste, string subCast, string vchr_sex, Nullable<System.DateTime> dte_dob, Nullable<int> age, string vchr_village, string vchr_taluk, Nullable<int> block_Id, Nullable<int> municipality_Id, string vchr_panchayat, Nullable<int> corporation_Id, Nullable<int> lokSabha_Id, Nullable<int> assemply_Id, Nullable<decimal> int_anninc, string vchr_ration, Nullable<decimal> int_loanamt_req, string chr_status, string landMark, string ward, Nullable<System.DateTime> retirement_Date, Nullable<int> verification_ID_Card, Nullable<int> verification_Ration_Card, string remarks_Assist_Manager, string remarks_Manager, Nullable<decimal> int_amtsanction)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var vchr_applnameParameter = vchr_applname != null ?
                new ObjectParameter("vchr_applname", vchr_applname) :
                new ObjectParameter("vchr_applname", typeof(string));
    
            var vchr_AadhaarParameter = vchr_Aadhaar != null ?
                new ObjectParameter("vchr_Aadhaar", vchr_Aadhaar) :
                new ObjectParameter("vchr_Aadhaar", typeof(string));
    
            var int_schemeidParameter = int_schemeid.HasValue ?
                new ObjectParameter("int_schemeid", int_schemeid) :
                new ObjectParameter("int_schemeid", typeof(int));
    
            var vchr_hsenameParameter = vchr_hsename != null ?
                new ObjectParameter("vchr_hsename", vchr_hsename) :
                new ObjectParameter("vchr_hsename", typeof(string));
    
            var vchr_place1Parameter = vchr_place1 != null ?
                new ObjectParameter("vchr_place1", vchr_place1) :
                new ObjectParameter("vchr_place1", typeof(string));
    
            var vchr_place2Parameter = vchr_place2 != null ?
                new ObjectParameter("vchr_place2", vchr_place2) :
                new ObjectParameter("vchr_place2", typeof(string));
    
            var vchr_districtParameter = vchr_district != null ?
                new ObjectParameter("vchr_district", vchr_district) :
                new ObjectParameter("vchr_district", typeof(string));
    
            var vchr_subdistrictParameter = vchr_subdistrict != null ?
                new ObjectParameter("vchr_subdistrict", vchr_subdistrict) :
                new ObjectParameter("vchr_subdistrict", typeof(string));
    
            var int_pincodeParameter = int_pincode.HasValue ?
                new ObjectParameter("int_pincode", int_pincode) :
                new ObjectParameter("int_pincode", typeof(int));
    
            var vchr_postParameter = vchr_post != null ?
                new ObjectParameter("vchr_post", vchr_post) :
                new ObjectParameter("vchr_post", typeof(string));
    
            var vchr_phnoParameter = vchr_phno != null ?
                new ObjectParameter("vchr_phno", vchr_phno) :
                new ObjectParameter("vchr_phno", typeof(string));
    
            var vchr_fathernameParameter = vchr_fathername != null ?
                new ObjectParameter("vchr_fathername", vchr_fathername) :
                new ObjectParameter("vchr_fathername", typeof(string));
    
            var vchr_spousenameParameter = vchr_spousename != null ?
                new ObjectParameter("vchr_spousename", vchr_spousename) :
                new ObjectParameter("vchr_spousename", typeof(string));
    
            var vchr_casteParameter = vchr_caste != null ?
                new ObjectParameter("vchr_caste", vchr_caste) :
                new ObjectParameter("vchr_caste", typeof(string));
    
            var subCastParameter = subCast != null ?
                new ObjectParameter("SubCast", subCast) :
                new ObjectParameter("SubCast", typeof(string));
    
            var vchr_sexParameter = vchr_sex != null ?
                new ObjectParameter("vchr_sex", vchr_sex) :
                new ObjectParameter("vchr_sex", typeof(string));
    
            var dte_dobParameter = dte_dob.HasValue ?
                new ObjectParameter("dte_dob", dte_dob) :
                new ObjectParameter("dte_dob", typeof(System.DateTime));
    
            var ageParameter = age.HasValue ?
                new ObjectParameter("Age", age) :
                new ObjectParameter("Age", typeof(int));
    
            var vchr_villageParameter = vchr_village != null ?
                new ObjectParameter("vchr_village", vchr_village) :
                new ObjectParameter("vchr_village", typeof(string));
    
            var vchr_talukParameter = vchr_taluk != null ?
                new ObjectParameter("vchr_taluk", vchr_taluk) :
                new ObjectParameter("vchr_taluk", typeof(string));
    
            var block_IdParameter = block_Id.HasValue ?
                new ObjectParameter("Block_Id", block_Id) :
                new ObjectParameter("Block_Id", typeof(int));
    
            var municipality_IdParameter = municipality_Id.HasValue ?
                new ObjectParameter("Municipality_Id", municipality_Id) :
                new ObjectParameter("Municipality_Id", typeof(int));
    
            var vchr_panchayatParameter = vchr_panchayat != null ?
                new ObjectParameter("vchr_panchayat", vchr_panchayat) :
                new ObjectParameter("vchr_panchayat", typeof(string));
    
            var corporation_IdParameter = corporation_Id.HasValue ?
                new ObjectParameter("Corporation_Id", corporation_Id) :
                new ObjectParameter("Corporation_Id", typeof(int));
    
            var lokSabha_IdParameter = lokSabha_Id.HasValue ?
                new ObjectParameter("LokSabha_Id", lokSabha_Id) :
                new ObjectParameter("LokSabha_Id", typeof(int));
    
            var assemply_IdParameter = assemply_Id.HasValue ?
                new ObjectParameter("Assemply_Id", assemply_Id) :
                new ObjectParameter("Assemply_Id", typeof(int));
    
            var int_annincParameter = int_anninc.HasValue ?
                new ObjectParameter("int_anninc", int_anninc) :
                new ObjectParameter("int_anninc", typeof(decimal));
    
            var vchr_rationParameter = vchr_ration != null ?
                new ObjectParameter("vchr_ration", vchr_ration) :
                new ObjectParameter("vchr_ration", typeof(string));
    
            var int_loanamt_reqParameter = int_loanamt_req.HasValue ?
                new ObjectParameter("int_loanamt_req", int_loanamt_req) :
                new ObjectParameter("int_loanamt_req", typeof(decimal));
    
            var chr_statusParameter = chr_status != null ?
                new ObjectParameter("chr_status", chr_status) :
                new ObjectParameter("chr_status", typeof(string));
    
            var landMarkParameter = landMark != null ?
                new ObjectParameter("LandMark", landMark) :
                new ObjectParameter("LandMark", typeof(string));
    
            var wardParameter = ward != null ?
                new ObjectParameter("Ward", ward) :
                new ObjectParameter("Ward", typeof(string));
    
            var retirement_DateParameter = retirement_Date.HasValue ?
                new ObjectParameter("Retirement_Date", retirement_Date) :
                new ObjectParameter("Retirement_Date", typeof(System.DateTime));
    
            var verification_ID_CardParameter = verification_ID_Card.HasValue ?
                new ObjectParameter("Verification_ID_Card", verification_ID_Card) :
                new ObjectParameter("Verification_ID_Card", typeof(int));
    
            var verification_Ration_CardParameter = verification_Ration_Card.HasValue ?
                new ObjectParameter("Verification_Ration_Card", verification_Ration_Card) :
                new ObjectParameter("Verification_Ration_Card", typeof(int));
    
            var remarks_Assist_ManagerParameter = remarks_Assist_Manager != null ?
                new ObjectParameter("Remarks_Assist_Manager", remarks_Assist_Manager) :
                new ObjectParameter("Remarks_Assist_Manager", typeof(string));
    
            var remarks_ManagerParameter = remarks_Manager != null ?
                new ObjectParameter("Remarks_Manager", remarks_Manager) :
                new ObjectParameter("Remarks_Manager", typeof(string));
    
            var int_amtsanctionParameter = int_amtsanction.HasValue ?
                new ObjectParameter("int_amtsanction", int_amtsanction) :
                new ObjectParameter("int_amtsanction", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("New_Update_To_tbl_loanapp_Sanction", int_loanappidParameter, vchr_applnameParameter, vchr_AadhaarParameter, int_schemeidParameter, vchr_hsenameParameter, vchr_place1Parameter, vchr_place2Parameter, vchr_districtParameter, vchr_subdistrictParameter, int_pincodeParameter, vchr_postParameter, vchr_phnoParameter, vchr_fathernameParameter, vchr_spousenameParameter, vchr_casteParameter, subCastParameter, vchr_sexParameter, dte_dobParameter, ageParameter, vchr_villageParameter, vchr_talukParameter, block_IdParameter, municipality_IdParameter, vchr_panchayatParameter, corporation_IdParameter, lokSabha_IdParameter, assemply_IdParameter, int_annincParameter, vchr_rationParameter, int_loanamt_reqParameter, chr_statusParameter, landMarkParameter, wardParameter, retirement_DateParameter, verification_ID_CardParameter, verification_Ration_CardParameter, remarks_Assist_ManagerParameter, remarks_ManagerParameter, int_amtsanctionParameter);
        }
    
        public virtual int New_Update_To_tbl_loanapp_Surety_Status_And_Type(Nullable<decimal> int_loanappid, string status, string type)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var statusParameter = status != null ?
                new ObjectParameter("Status", status) :
                new ObjectParameter("Status", typeof(string));
    
            var typeParameter = type != null ?
                new ObjectParameter("Type", type) :
                new ObjectParameter("Type", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("New_Update_To_tbl_loanapp_Surety_Status_And_Type", int_loanappidParameter, statusParameter, typeParameter);
        }
    
        public virtual int New_Update_To_tbl_loanapp_Verification(Nullable<decimal> int_loanappid, string vchr_applname, string vchr_Aadhaar, Nullable<int> int_schemeid, string vchr_hsename, string vchr_place1, string vchr_place2, string vchr_district, string vchr_subdistrict, Nullable<int> int_pincode, string vchr_post, string vchr_phno, string vchr_fathername, string vchr_spousename, string vchr_caste, string subCast, string vchr_sex, Nullable<System.DateTime> dte_dob, Nullable<int> age, string vchr_village, string vchr_taluk, Nullable<int> block_Id, Nullable<int> municipality_Id, string vchr_panchayat, Nullable<int> corporation_Id, Nullable<int> lokSabha_Id, Nullable<int> assemply_Id, Nullable<decimal> int_anninc, string vchr_ration, Nullable<decimal> int_loanamt_req, string chr_status, string landMark, string ward, Nullable<System.DateTime> retirement_Date, Nullable<int> verification_ID_Card, Nullable<int> verification_Ration_Card, string remarks_Assist_Manager)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var vchr_applnameParameter = vchr_applname != null ?
                new ObjectParameter("vchr_applname", vchr_applname) :
                new ObjectParameter("vchr_applname", typeof(string));
    
            var vchr_AadhaarParameter = vchr_Aadhaar != null ?
                new ObjectParameter("vchr_Aadhaar", vchr_Aadhaar) :
                new ObjectParameter("vchr_Aadhaar", typeof(string));
    
            var int_schemeidParameter = int_schemeid.HasValue ?
                new ObjectParameter("int_schemeid", int_schemeid) :
                new ObjectParameter("int_schemeid", typeof(int));
    
            var vchr_hsenameParameter = vchr_hsename != null ?
                new ObjectParameter("vchr_hsename", vchr_hsename) :
                new ObjectParameter("vchr_hsename", typeof(string));
    
            var vchr_place1Parameter = vchr_place1 != null ?
                new ObjectParameter("vchr_place1", vchr_place1) :
                new ObjectParameter("vchr_place1", typeof(string));
    
            var vchr_place2Parameter = vchr_place2 != null ?
                new ObjectParameter("vchr_place2", vchr_place2) :
                new ObjectParameter("vchr_place2", typeof(string));
    
            var vchr_districtParameter = vchr_district != null ?
                new ObjectParameter("vchr_district", vchr_district) :
                new ObjectParameter("vchr_district", typeof(string));
    
            var vchr_subdistrictParameter = vchr_subdistrict != null ?
                new ObjectParameter("vchr_subdistrict", vchr_subdistrict) :
                new ObjectParameter("vchr_subdistrict", typeof(string));
    
            var int_pincodeParameter = int_pincode.HasValue ?
                new ObjectParameter("int_pincode", int_pincode) :
                new ObjectParameter("int_pincode", typeof(int));
    
            var vchr_postParameter = vchr_post != null ?
                new ObjectParameter("vchr_post", vchr_post) :
                new ObjectParameter("vchr_post", typeof(string));
    
            var vchr_phnoParameter = vchr_phno != null ?
                new ObjectParameter("vchr_phno", vchr_phno) :
                new ObjectParameter("vchr_phno", typeof(string));
    
            var vchr_fathernameParameter = vchr_fathername != null ?
                new ObjectParameter("vchr_fathername", vchr_fathername) :
                new ObjectParameter("vchr_fathername", typeof(string));
    
            var vchr_spousenameParameter = vchr_spousename != null ?
                new ObjectParameter("vchr_spousename", vchr_spousename) :
                new ObjectParameter("vchr_spousename", typeof(string));
    
            var vchr_casteParameter = vchr_caste != null ?
                new ObjectParameter("vchr_caste", vchr_caste) :
                new ObjectParameter("vchr_caste", typeof(string));
    
            var subCastParameter = subCast != null ?
                new ObjectParameter("SubCast", subCast) :
                new ObjectParameter("SubCast", typeof(string));
    
            var vchr_sexParameter = vchr_sex != null ?
                new ObjectParameter("vchr_sex", vchr_sex) :
                new ObjectParameter("vchr_sex", typeof(string));
    
            var dte_dobParameter = dte_dob.HasValue ?
                new ObjectParameter("dte_dob", dte_dob) :
                new ObjectParameter("dte_dob", typeof(System.DateTime));
    
            var ageParameter = age.HasValue ?
                new ObjectParameter("Age", age) :
                new ObjectParameter("Age", typeof(int));
    
            var vchr_villageParameter = vchr_village != null ?
                new ObjectParameter("vchr_village", vchr_village) :
                new ObjectParameter("vchr_village", typeof(string));
    
            var vchr_talukParameter = vchr_taluk != null ?
                new ObjectParameter("vchr_taluk", vchr_taluk) :
                new ObjectParameter("vchr_taluk", typeof(string));
    
            var block_IdParameter = block_Id.HasValue ?
                new ObjectParameter("Block_Id", block_Id) :
                new ObjectParameter("Block_Id", typeof(int));
    
            var municipality_IdParameter = municipality_Id.HasValue ?
                new ObjectParameter("Municipality_Id", municipality_Id) :
                new ObjectParameter("Municipality_Id", typeof(int));
    
            var vchr_panchayatParameter = vchr_panchayat != null ?
                new ObjectParameter("vchr_panchayat", vchr_panchayat) :
                new ObjectParameter("vchr_panchayat", typeof(string));
    
            var corporation_IdParameter = corporation_Id.HasValue ?
                new ObjectParameter("Corporation_Id", corporation_Id) :
                new ObjectParameter("Corporation_Id", typeof(int));
    
            var lokSabha_IdParameter = lokSabha_Id.HasValue ?
                new ObjectParameter("LokSabha_Id", lokSabha_Id) :
                new ObjectParameter("LokSabha_Id", typeof(int));
    
            var assemply_IdParameter = assemply_Id.HasValue ?
                new ObjectParameter("Assemply_Id", assemply_Id) :
                new ObjectParameter("Assemply_Id", typeof(int));
    
            var int_annincParameter = int_anninc.HasValue ?
                new ObjectParameter("int_anninc", int_anninc) :
                new ObjectParameter("int_anninc", typeof(decimal));
    
            var vchr_rationParameter = vchr_ration != null ?
                new ObjectParameter("vchr_ration", vchr_ration) :
                new ObjectParameter("vchr_ration", typeof(string));
    
            var int_loanamt_reqParameter = int_loanamt_req.HasValue ?
                new ObjectParameter("int_loanamt_req", int_loanamt_req) :
                new ObjectParameter("int_loanamt_req", typeof(decimal));
    
            var chr_statusParameter = chr_status != null ?
                new ObjectParameter("chr_status", chr_status) :
                new ObjectParameter("chr_status", typeof(string));
    
            var landMarkParameter = landMark != null ?
                new ObjectParameter("LandMark", landMark) :
                new ObjectParameter("LandMark", typeof(string));
    
            var wardParameter = ward != null ?
                new ObjectParameter("Ward", ward) :
                new ObjectParameter("Ward", typeof(string));
    
            var retirement_DateParameter = retirement_Date.HasValue ?
                new ObjectParameter("Retirement_Date", retirement_Date) :
                new ObjectParameter("Retirement_Date", typeof(System.DateTime));
    
            var verification_ID_CardParameter = verification_ID_Card.HasValue ?
                new ObjectParameter("Verification_ID_Card", verification_ID_Card) :
                new ObjectParameter("Verification_ID_Card", typeof(int));
    
            var verification_Ration_CardParameter = verification_Ration_Card.HasValue ?
                new ObjectParameter("Verification_Ration_Card", verification_Ration_Card) :
                new ObjectParameter("Verification_Ration_Card", typeof(int));
    
            var remarks_Assist_ManagerParameter = remarks_Assist_Manager != null ?
                new ObjectParameter("Remarks_Assist_Manager", remarks_Assist_Manager) :
                new ObjectParameter("Remarks_Assist_Manager", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("New_Update_To_tbl_loanapp_Verification", int_loanappidParameter, vchr_applnameParameter, vchr_AadhaarParameter, int_schemeidParameter, vchr_hsenameParameter, vchr_place1Parameter, vchr_place2Parameter, vchr_districtParameter, vchr_subdistrictParameter, int_pincodeParameter, vchr_postParameter, vchr_phnoParameter, vchr_fathernameParameter, vchr_spousenameParameter, vchr_casteParameter, subCastParameter, vchr_sexParameter, dte_dobParameter, ageParameter, vchr_villageParameter, vchr_talukParameter, block_IdParameter, municipality_IdParameter, vchr_panchayatParameter, corporation_IdParameter, lokSabha_IdParameter, assemply_IdParameter, int_annincParameter, vchr_rationParameter, int_loanamt_reqParameter, chr_statusParameter, landMarkParameter, wardParameter, retirement_DateParameter, verification_ID_CardParameter, verification_Ration_CardParameter, remarks_Assist_ManagerParameter);
        }
    
        public virtual ObjectResult<Select_All_Agency_Result> Select_All_Agency()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Agency_Result>("Select_All_Agency");
        }
    
        public virtual ObjectResult<Select_All_Agency_By_Cast_And_Scheme_Result> Select_All_Agency_By_Cast_And_Scheme(string cast, Nullable<int> scheme_Id)
        {
            var castParameter = cast != null ?
                new ObjectParameter("Cast", cast) :
                new ObjectParameter("Cast", typeof(string));
    
            var scheme_IdParameter = scheme_Id.HasValue ?
                new ObjectParameter("Scheme_Id", scheme_Id) :
                new ObjectParameter("Scheme_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Agency_By_Cast_And_Scheme_Result>("Select_All_Agency_By_Cast_And_Scheme", castParameter, scheme_IdParameter);
        }
    
        public virtual ObjectResult<Select_All_Agency_By_ID_Result> Select_All_Agency_By_ID(Nullable<int> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Agency_By_ID_Result>("Select_All_Agency_By_ID", idParameter);
        }
    
        public virtual ObjectResult<Select_All_Agency_By_int_loanappid_Result> Select_All_Agency_By_int_loanappid(Nullable<decimal> int_loanappid)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Agency_By_int_loanappid_Result>("Select_All_Agency_By_int_loanappid", int_loanappidParameter);
        }
    
        public virtual ObjectResult<Select_All_Bank_Details_By_int_loanappid_Result> Select_All_Bank_Details_By_int_loanappid(Nullable<decimal> int_loanappid)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Bank_Details_By_int_loanappid_Result>("Select_All_Bank_Details_By_int_loanappid", int_loanappidParameter);
        }
    
        public virtual ObjectResult<Select_All_Block_By_District_Id_Result> Select_All_Block_By_District_Id(Nullable<int> district_Id)
        {
            var district_IdParameter = district_Id.HasValue ?
                new ObjectParameter("District_Id", district_Id) :
                new ObjectParameter("District_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Block_By_District_Id_Result>("Select_All_Block_By_District_Id", district_IdParameter);
        }
    
        public virtual ObjectResult<Select_All_Cast_Result> Select_All_Cast()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Cast_Result>("Select_All_Cast");
        }
    
        public virtual ObjectResult<Select_All_Cast_By_Id_Result> Select_All_Cast_By_Id(Nullable<int> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Cast_By_Id_Result>("Select_All_Cast_By_Id", idParameter);
        }
    
        public virtual ObjectResult<Select_All_Constituency_Result> Select_All_Constituency(Nullable<int> lok_Sabha_Id)
        {
            var lok_Sabha_IdParameter = lok_Sabha_Id.HasValue ?
                new ObjectParameter("Lok_Sabha_Id", lok_Sabha_Id) :
                new ObjectParameter("Lok_Sabha_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Constituency_Result>("Select_All_Constituency", lok_Sabha_IdParameter);
        }
    
        public virtual ObjectResult<Select_All_Corporation_By_District_Id_Result> Select_All_Corporation_By_District_Id(Nullable<int> district_Id)
        {
            var district_IdParameter = district_Id.HasValue ?
                new ObjectParameter("District_Id", district_Id) :
                new ObjectParameter("District_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Corporation_By_District_Id_Result>("Select_All_Corporation_By_District_Id", district_IdParameter);
        }
    
        public virtual ObjectResult<Select_All_Disbursement_Details_Result> Select_All_Disbursement_Details(Nullable<decimal> int_loanappid)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Disbursement_Details_Result>("Select_All_Disbursement_Details", int_loanappidParameter);
        }
    
        public virtual ObjectResult<Select_All_Districts_Result> Select_All_Districts()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Districts_Result>("Select_All_Districts");
        }
    
        public virtual ObjectResult<Select_All_Districts_By_Village_Id_Result> Select_All_Districts_By_Village_Id(Nullable<int> village_Id)
        {
            var village_IdParameter = village_Id.HasValue ?
                new ObjectParameter("Village_Id", village_Id) :
                new ObjectParameter("Village_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Districts_By_Village_Id_Result>("Select_All_Districts_By_Village_Id", village_IdParameter);
        }
    
        public virtual ObjectResult<Select_All_Lnaaccdtls_Result> Select_All_Lnaaccdtls(string dist_Code)
        {
            var dist_CodeParameter = dist_Code != null ?
                new ObjectParameter("Dist_Code", dist_Code) :
                new ObjectParameter("Dist_Code", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Lnaaccdtls_Result>("Select_All_Lnaaccdtls", dist_CodeParameter);
        }
    
        public virtual ObjectResult<Select_All_LoanDisburment_By_Lnaccid_Result> Select_All_LoanDisburment_By_Lnaccid(Nullable<int> lnaccid, string dist_Code)
        {
            var lnaccidParameter = lnaccid.HasValue ?
                new ObjectParameter("Lnaccid", lnaccid) :
                new ObjectParameter("Lnaccid", typeof(int));
    
            var dist_CodeParameter = dist_Code != null ?
                new ObjectParameter("Dist_Code", dist_Code) :
                new ObjectParameter("Dist_Code", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_LoanDisburment_By_Lnaccid_Result>("Select_All_LoanDisburment_By_Lnaccid", lnaccidParameter, dist_CodeParameter);
        }
    
        public virtual ObjectResult<Select_All_Loanee_Details_By_Lnaccid_Result> Select_All_Loanee_Details_By_Lnaccid(Nullable<int> lnaccid, string dist_Code)
        {
            var lnaccidParameter = lnaccid.HasValue ?
                new ObjectParameter("Lnaccid", lnaccid) :
                new ObjectParameter("Lnaccid", typeof(int));
    
            var dist_CodeParameter = dist_Code != null ?
                new ObjectParameter("Dist_Code", dist_Code) :
                new ObjectParameter("Dist_Code", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Loanee_Details_By_Lnaccid_Result>("Select_All_Loanee_Details_By_Lnaccid", lnaccidParameter, dist_CodeParameter);
        }
    
        public virtual ObjectResult<Select_All_LoanRepayment_Lnaccid_Result> Select_All_LoanRepayment_Lnaccid(Nullable<int> lnaccid, string dist_Code)
        {
            var lnaccidParameter = lnaccid.HasValue ?
                new ObjectParameter("Lnaccid", lnaccid) :
                new ObjectParameter("Lnaccid", typeof(int));
    
            var dist_CodeParameter = dist_Code != null ?
                new ObjectParameter("Dist_Code", dist_Code) :
                new ObjectParameter("Dist_Code", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_LoanRepayment_Lnaccid_Result>("Select_All_LoanRepayment_Lnaccid", lnaccidParameter, dist_CodeParameter);
        }
    
        public virtual ObjectResult<Select_All_loanTrans_LoanNo_Result> Select_All_loanTrans_LoanNo(string orderColumn, string orderDirection, Nullable<int> start, Nullable<int> length, string int_loanno)
        {
            var orderColumnParameter = orderColumn != null ?
                new ObjectParameter("orderColumn", orderColumn) :
                new ObjectParameter("orderColumn", typeof(string));
    
            var orderDirectionParameter = orderDirection != null ?
                new ObjectParameter("orderDirection", orderDirection) :
                new ObjectParameter("orderDirection", typeof(string));
    
            var startParameter = start.HasValue ?
                new ObjectParameter("start", start) :
                new ObjectParameter("start", typeof(int));
    
            var lengthParameter = length.HasValue ?
                new ObjectParameter("length", length) :
                new ObjectParameter("length", typeof(int));
    
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_loanTrans_LoanNo_Result>("Select_All_loanTrans_LoanNo", orderColumnParameter, orderDirectionParameter, startParameter, lengthParameter, int_loannoParameter);
        }
    
        public virtual ObjectResult<Select_All_loanTrans_LoanNo_Migration_Result> Select_All_loanTrans_LoanNo_Migration(string int_loanno)
        {
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_loanTrans_LoanNo_Migration_Result>("Select_All_loanTrans_LoanNo_Migration", int_loannoParameter);
        }
    
        public virtual ObjectResult<Select_All_Lok_Sabha_Result> Select_All_Lok_Sabha(Nullable<int> district_Id)
        {
            var district_IdParameter = district_Id.HasValue ?
                new ObjectParameter("District_Id", district_Id) :
                new ObjectParameter("District_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Lok_Sabha_Result>("Select_All_Lok_Sabha", district_IdParameter);
        }
    
        public virtual ObjectResult<Select_All_Municipality_By_District_Id_Result> Select_All_Municipality_By_District_Id(Nullable<int> district_Id)
        {
            var district_IdParameter = district_Id.HasValue ?
                new ObjectParameter("District_Id", district_Id) :
                new ObjectParameter("District_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Municipality_By_District_Id_Result>("Select_All_Municipality_By_District_Id", district_IdParameter);
        }
    
        public virtual ObjectResult<Select_All_Offices_Result> Select_All_Offices()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Offices_Result>("Select_All_Offices");
        }
    
        public virtual ObjectResult<Select_All_OtherRcpt_By_Lnaccid_Result> Select_All_OtherRcpt_By_Lnaccid(Nullable<int> lnaccid, string dist_Code)
        {
            var lnaccidParameter = lnaccid.HasValue ?
                new ObjectParameter("Lnaccid", lnaccid) :
                new ObjectParameter("Lnaccid", typeof(int));
    
            var dist_CodeParameter = dist_Code != null ?
                new ObjectParameter("Dist_Code", dist_Code) :
                new ObjectParameter("Dist_Code", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_OtherRcpt_By_Lnaccid_Result>("Select_All_OtherRcpt_By_Lnaccid", lnaccidParameter, dist_CodeParameter);
        }
    
        public virtual ObjectResult<Select_All_Panchayath_By_Id_Result> Select_All_Panchayath_By_Id(Nullable<int> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Panchayath_By_Id_Result>("Select_All_Panchayath_By_Id", idParameter);
        }
    
        public virtual ObjectResult<Select_All_Panjayath_By_Block_Id_Result> Select_All_Panjayath_By_Block_Id(Nullable<int> block_Id)
        {
            var block_IdParameter = block_Id.HasValue ?
                new ObjectParameter("Block_Id", block_Id) :
                new ObjectParameter("Block_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Panjayath_By_Block_Id_Result>("Select_All_Panjayath_By_Block_Id", block_IdParameter);
        }
    
        public virtual ObjectResult<Select_All_Personal_Ledger_Result> Select_All_Personal_Ledger(string orderColumn, string orderDirection, Nullable<int> start, Nullable<int> length, string old_Loan_No, string loan_No, string loanee_Name, string house_Name, string mobile_No, string office_Id)
        {
            var orderColumnParameter = orderColumn != null ?
                new ObjectParameter("orderColumn", orderColumn) :
                new ObjectParameter("orderColumn", typeof(string));
    
            var orderDirectionParameter = orderDirection != null ?
                new ObjectParameter("orderDirection", orderDirection) :
                new ObjectParameter("orderDirection", typeof(string));
    
            var startParameter = start.HasValue ?
                new ObjectParameter("start", start) :
                new ObjectParameter("start", typeof(int));
    
            var lengthParameter = length.HasValue ?
                new ObjectParameter("length", length) :
                new ObjectParameter("length", typeof(int));
    
            var old_Loan_NoParameter = old_Loan_No != null ?
                new ObjectParameter("Old_Loan_No", old_Loan_No) :
                new ObjectParameter("Old_Loan_No", typeof(string));
    
            var loan_NoParameter = loan_No != null ?
                new ObjectParameter("Loan_No", loan_No) :
                new ObjectParameter("Loan_No", typeof(string));
    
            var loanee_NameParameter = loanee_Name != null ?
                new ObjectParameter("Loanee_Name", loanee_Name) :
                new ObjectParameter("Loanee_Name", typeof(string));
    
            var house_NameParameter = house_Name != null ?
                new ObjectParameter("House_Name", house_Name) :
                new ObjectParameter("House_Name", typeof(string));
    
            var mobile_NoParameter = mobile_No != null ?
                new ObjectParameter("Mobile_No", mobile_No) :
                new ObjectParameter("Mobile_No", typeof(string));
    
            var office_IdParameter = office_Id != null ?
                new ObjectParameter("Office_Id", office_Id) :
                new ObjectParameter("Office_Id", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Personal_Ledger_Result>("Select_All_Personal_Ledger", orderColumnParameter, orderDirectionParameter, startParameter, lengthParameter, old_Loan_NoParameter, loan_NoParameter, loanee_NameParameter, house_NameParameter, mobile_NoParameter, office_IdParameter);
        }
    
        public virtual ObjectResult<Select_All_PostOffices_Result> Select_All_PostOffices(string pincode)
        {
            var pincodeParameter = pincode != null ?
                new ObjectParameter("Pincode", pincode) :
                new ObjectParameter("Pincode", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_PostOffices_Result>("Select_All_PostOffices", pincodeParameter);
        }
    
        public virtual ObjectResult<Select_All_Project_By_ID_Result> Select_All_Project_By_ID(Nullable<int> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Project_By_ID_Result>("Select_All_Project_By_ID", idParameter);
        }
    
        public virtual ObjectResult<Select_All_Projects_Result> Select_All_Projects(string orderColumn, string orderDirection, Nullable<int> start, Nullable<int> length)
        {
            var orderColumnParameter = orderColumn != null ?
                new ObjectParameter("orderColumn", orderColumn) :
                new ObjectParameter("orderColumn", typeof(string));
    
            var orderDirectionParameter = orderDirection != null ?
                new ObjectParameter("orderDirection", orderDirection) :
                new ObjectParameter("orderDirection", typeof(string));
    
            var startParameter = start.HasValue ?
                new ObjectParameter("start", start) :
                new ObjectParameter("start", typeof(int));
    
            var lengthParameter = length.HasValue ?
                new ObjectParameter("length", length) :
                new ObjectParameter("length", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Projects_Result>("Select_All_Projects", orderColumnParameter, orderDirectionParameter, startParameter, lengthParameter);
        }
    
        public virtual ObjectResult<Select_All_Projects_ACTIVE_Result> Select_All_Projects_ACTIVE()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Projects_ACTIVE_Result>("Select_All_Projects_ACTIVE");
        }
    
        public virtual ObjectResult<Select_All_Roles_Result> Select_All_Roles()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Roles_Result>("Select_All_Roles");
        }
    
        public virtual ObjectResult<Select_All_Roles_By_ID_Result> Select_All_Roles_By_ID(Nullable<int> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Roles_By_ID_Result>("Select_All_Roles_By_ID", idParameter);
        }
    
        public virtual ObjectResult<Select_All_Schemes_Result> Select_All_Schemes()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Schemes_Result>("Select_All_Schemes");
        }
    
        public virtual ObjectResult<Select_All_Schemes_By_ID_Result> Select_All_Schemes_By_ID(Nullable<int> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Schemes_By_ID_Result>("Select_All_Schemes_By_ID", idParameter);
        }
    
        public virtual ObjectResult<Select_All_Sector_By_ID_Result> Select_All_Sector_By_ID(Nullable<int> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Sector_By_ID_Result>("Select_All_Sector_By_ID", idParameter);
        }
    
        public virtual ObjectResult<Select_All_Sectors_Result> Select_All_Sectors()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Sectors_Result>("Select_All_Sectors");
        }
    
        public virtual ObjectResult<Select_All_Sub_Cast_Result> Select_All_Sub_Cast(Nullable<int> cast_Id)
        {
            var cast_IdParameter = cast_Id.HasValue ?
                new ObjectParameter("Cast_Id", cast_Id) :
                new ObjectParameter("Cast_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Sub_Cast_Result>("Select_All_Sub_Cast", cast_IdParameter);
        }
    
        public virtual ObjectResult<Select_All_Sub_Cast_By_Id_Result> Select_All_Sub_Cast_By_Id(Nullable<int> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Sub_Cast_By_Id_Result>("Select_All_Sub_Cast_By_Id", idParameter);
        }
    
        public virtual ObjectResult<Select_All_Sub_Districts_Result> Select_All_Sub_Districts(Nullable<int> district_Id)
        {
            var district_IdParameter = district_Id.HasValue ?
                new ObjectParameter("District_Id", district_Id) :
                new ObjectParameter("District_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Sub_Districts_Result>("Select_All_Sub_Districts", district_IdParameter);
        }
    
        public virtual ObjectResult<Select_All_Sub_Districts_By_Id_Result> Select_All_Sub_Districts_By_Id(Nullable<int> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Sub_Districts_By_Id_Result>("Select_All_Sub_Districts_By_Id", idParameter);
        }
    
        public virtual ObjectResult<Select_All_Surety_Confirmation_By_int_loanappid_Result> Select_All_Surety_Confirmation_By_int_loanappid(Nullable<decimal> int_loanappid)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Surety_Confirmation_By_int_loanappid_Result>("Select_All_Surety_Confirmation_By_int_loanappid", int_loanappidParameter);
        }
    
        public virtual ObjectResult<Select_All_Surety_Type_By_int_loanappid_Result> Select_All_Surety_Type_By_int_loanappid(Nullable<decimal> int_loanappid)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Surety_Type_By_int_loanappid_Result>("Select_All_Surety_Type_By_int_loanappid", int_loanappidParameter);
        }
    
        public virtual ObjectResult<Select_All_tbl_Department_Result> Select_All_tbl_Department()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_tbl_Department_Result>("Select_All_tbl_Department");
        }
    
        public virtual ObjectResult<Select_All_tbl_Department_int_deptid_Result> Select_All_tbl_Department_int_deptid(Nullable<decimal> int_deptid)
        {
            var int_deptidParameter = int_deptid.HasValue ?
                new ObjectParameter("int_deptid", int_deptid) :
                new ObjectParameter("int_deptid", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_tbl_Department_int_deptid_Result>("Select_All_tbl_Department_int_deptid", int_deptidParameter);
        }
    
        public virtual ObjectResult<Select_All_tbl_Designations_Result> Select_All_tbl_Designations()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_tbl_Designations_Result>("Select_All_tbl_Designations");
        }
    
        public virtual ObjectResult<Select_All_tbl_EmpSurety_By_LoanAppId_Result> Select_All_tbl_EmpSurety_By_LoanAppId(Nullable<decimal> int_loanappid)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_tbl_EmpSurety_By_LoanAppId_Result>("Select_All_tbl_EmpSurety_By_LoanAppId", int_loanappidParameter);
        }
    
        public virtual ObjectResult<Select_All_tbl_FDLICSurety_By_LoanAppId_Result> Select_All_tbl_FDLICSurety_By_LoanAppId(Nullable<decimal> int_loanappid)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_tbl_FDLICSurety_By_LoanAppId_Result>("Select_All_tbl_FDLICSurety_By_LoanAppId", int_loanappidParameter);
        }
    
        public virtual ObjectResult<Select_All_tbl_KSDC_SMART_Issues_Result> Select_All_tbl_KSDC_SMART_Issues(string orderColumn, string orderDirection, Nullable<int> start, Nullable<int> length, Nullable<int> branchId)
        {
            var orderColumnParameter = orderColumn != null ?
                new ObjectParameter("orderColumn", orderColumn) :
                new ObjectParameter("orderColumn", typeof(string));
    
            var orderDirectionParameter = orderDirection != null ?
                new ObjectParameter("orderDirection", orderDirection) :
                new ObjectParameter("orderDirection", typeof(string));
    
            var startParameter = start.HasValue ?
                new ObjectParameter("start", start) :
                new ObjectParameter("start", typeof(int));
    
            var lengthParameter = length.HasValue ?
                new ObjectParameter("length", length) :
                new ObjectParameter("length", typeof(int));
    
            var branchIdParameter = branchId.HasValue ?
                new ObjectParameter("BranchId", branchId) :
                new ObjectParameter("BranchId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_tbl_KSDC_SMART_Issues_Result>("Select_All_tbl_KSDC_SMART_Issues", orderColumnParameter, orderDirectionParameter, startParameter, lengthParameter, branchIdParameter);
        }
    
        public virtual ObjectResult<Select_All_tbl_KSDC_SMART_Issues_All_Branches_Result> Select_All_tbl_KSDC_SMART_Issues_All_Branches(string orderColumn, string orderDirection, Nullable<int> start, Nullable<int> length)
        {
            var orderColumnParameter = orderColumn != null ?
                new ObjectParameter("orderColumn", orderColumn) :
                new ObjectParameter("orderColumn", typeof(string));
    
            var orderDirectionParameter = orderDirection != null ?
                new ObjectParameter("orderDirection", orderDirection) :
                new ObjectParameter("orderDirection", typeof(string));
    
            var startParameter = start.HasValue ?
                new ObjectParameter("start", start) :
                new ObjectParameter("start", typeof(int));
    
            var lengthParameter = length.HasValue ?
                new ObjectParameter("length", length) :
                new ObjectParameter("length", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_tbl_KSDC_SMART_Issues_All_Branches_Result>("Select_All_tbl_KSDC_SMART_Issues_All_Branches", orderColumnParameter, orderDirectionParameter, startParameter, lengthParameter);
        }
    
        public virtual ObjectResult<Select_All_tbl_KSDC_SMART_Issues_Attachments_Result> Select_All_tbl_KSDC_SMART_Issues_Attachments(Nullable<int> issues_Id)
        {
            var issues_IdParameter = issues_Id.HasValue ?
                new ObjectParameter("Issues_Id", issues_Id) :
                new ObjectParameter("Issues_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_tbl_KSDC_SMART_Issues_Attachments_Result>("Select_All_tbl_KSDC_SMART_Issues_Attachments", issues_IdParameter);
        }
    
        public virtual ObjectResult<Select_All_tbl_LandSurety_By_LoanAppId_Result> Select_All_tbl_LandSurety_By_LoanAppId(Nullable<decimal> int_loanappid)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_tbl_LandSurety_By_LoanAppId_Result>("Select_All_tbl_LandSurety_By_LoanAppId", int_loanappidParameter);
        }
    
        public virtual ObjectResult<Select_All_tbl_Loan_App_Issue_Result> Select_All_tbl_Loan_App_Issue(string orderColumn, string orderDirection, Nullable<int> start, Nullable<int> length, string office_Id)
        {
            var orderColumnParameter = orderColumn != null ?
                new ObjectParameter("orderColumn", orderColumn) :
                new ObjectParameter("orderColumn", typeof(string));
    
            var orderDirectionParameter = orderDirection != null ?
                new ObjectParameter("orderDirection", orderDirection) :
                new ObjectParameter("orderDirection", typeof(string));
    
            var startParameter = start.HasValue ?
                new ObjectParameter("start", start) :
                new ObjectParameter("start", typeof(int));
    
            var lengthParameter = length.HasValue ?
                new ObjectParameter("length", length) :
                new ObjectParameter("length", typeof(int));
    
            var office_IdParameter = office_Id != null ?
                new ObjectParameter("Office_Id", office_Id) :
                new ObjectParameter("Office_Id", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_tbl_Loan_App_Issue_Result>("Select_All_tbl_Loan_App_Issue", orderColumnParameter, orderDirectionParameter, startParameter, lengthParameter, office_IdParameter);
        }
    
        public virtual ObjectResult<Select_All_tbl_Loan_App_Issue_By_ID_Result> Select_All_tbl_Loan_App_Issue_By_ID(Nullable<int> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_tbl_Loan_App_Issue_By_ID_Result>("Select_All_tbl_Loan_App_Issue_By_ID", idParameter);
        }
    
        public virtual ObjectResult<Select_All_tbl_LoanApp_By_Status_Result> Select_All_tbl_LoanApp_By_Status(string chr_status, string orderColumn, string orderDirection, Nullable<int> start, Nullable<int> length, string office_Id)
        {
            var chr_statusParameter = chr_status != null ?
                new ObjectParameter("chr_status", chr_status) :
                new ObjectParameter("chr_status", typeof(string));
    
            var orderColumnParameter = orderColumn != null ?
                new ObjectParameter("orderColumn", orderColumn) :
                new ObjectParameter("orderColumn", typeof(string));
    
            var orderDirectionParameter = orderDirection != null ?
                new ObjectParameter("orderDirection", orderDirection) :
                new ObjectParameter("orderDirection", typeof(string));
    
            var startParameter = start.HasValue ?
                new ObjectParameter("start", start) :
                new ObjectParameter("start", typeof(int));
    
            var lengthParameter = length.HasValue ?
                new ObjectParameter("length", length) :
                new ObjectParameter("length", typeof(int));
    
            var office_IdParameter = office_Id != null ?
                new ObjectParameter("Office_Id", office_Id) :
                new ObjectParameter("Office_Id", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_tbl_LoanApp_By_Status_Result>("Select_All_tbl_LoanApp_By_Status", chr_statusParameter, orderColumnParameter, orderDirectionParameter, startParameter, lengthParameter, office_IdParameter);
        }
    
        public virtual ObjectResult<Select_All_tbl_LoanApp_By_Status_Disbursement_Result> Select_All_tbl_LoanApp_By_Status_Disbursement(string orderColumn, string orderDirection, Nullable<int> start, Nullable<int> length, string office_Id)
        {
            var orderColumnParameter = orderColumn != null ?
                new ObjectParameter("orderColumn", orderColumn) :
                new ObjectParameter("orderColumn", typeof(string));
    
            var orderDirectionParameter = orderDirection != null ?
                new ObjectParameter("orderDirection", orderDirection) :
                new ObjectParameter("orderDirection", typeof(string));
    
            var startParameter = start.HasValue ?
                new ObjectParameter("start", start) :
                new ObjectParameter("start", typeof(int));
    
            var lengthParameter = length.HasValue ?
                new ObjectParameter("length", length) :
                new ObjectParameter("length", typeof(int));
    
            var office_IdParameter = office_Id != null ?
                new ObjectParameter("Office_Id", office_Id) :
                new ObjectParameter("Office_Id", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_tbl_LoanApp_By_Status_Disbursement_Result>("Select_All_tbl_LoanApp_By_Status_Disbursement", orderColumnParameter, orderDirectionParameter, startParameter, lengthParameter, office_IdParameter);
        }
    
        public virtual ObjectResult<Select_All_tbl_LoanApp_By_Status_Surety_Result> Select_All_tbl_LoanApp_By_Status_Surety(string chr_status, string orderColumn, string orderDirection, Nullable<int> start, Nullable<int> length, string office_Id)
        {
            var chr_statusParameter = chr_status != null ?
                new ObjectParameter("chr_status", chr_status) :
                new ObjectParameter("chr_status", typeof(string));
    
            var orderColumnParameter = orderColumn != null ?
                new ObjectParameter("orderColumn", orderColumn) :
                new ObjectParameter("orderColumn", typeof(string));
    
            var orderDirectionParameter = orderDirection != null ?
                new ObjectParameter("orderDirection", orderDirection) :
                new ObjectParameter("orderDirection", typeof(string));
    
            var startParameter = start.HasValue ?
                new ObjectParameter("start", start) :
                new ObjectParameter("start", typeof(int));
    
            var lengthParameter = length.HasValue ?
                new ObjectParameter("length", length) :
                new ObjectParameter("length", typeof(int));
    
            var office_IdParameter = office_Id != null ?
                new ObjectParameter("Office_Id", office_Id) :
                new ObjectParameter("Office_Id", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_tbl_LoanApp_By_Status_Surety_Result>("Select_All_tbl_LoanApp_By_Status_Surety", chr_statusParameter, orderColumnParameter, orderDirectionParameter, startParameter, lengthParameter, office_IdParameter);
        }
    
        public virtual ObjectResult<Select_All_tbl_LoanApp_By_Status_Surety_Confirm_Result> Select_All_tbl_LoanApp_By_Status_Surety_Confirm(string chr_status, string orderColumn, string orderDirection, Nullable<int> start, Nullable<int> length, string office_Id)
        {
            var chr_statusParameter = chr_status != null ?
                new ObjectParameter("chr_status", chr_status) :
                new ObjectParameter("chr_status", typeof(string));
    
            var orderColumnParameter = orderColumn != null ?
                new ObjectParameter("orderColumn", orderColumn) :
                new ObjectParameter("orderColumn", typeof(string));
    
            var orderDirectionParameter = orderDirection != null ?
                new ObjectParameter("orderDirection", orderDirection) :
                new ObjectParameter("orderDirection", typeof(string));
    
            var startParameter = start.HasValue ?
                new ObjectParameter("start", start) :
                new ObjectParameter("start", typeof(int));
    
            var lengthParameter = length.HasValue ?
                new ObjectParameter("length", length) :
                new ObjectParameter("length", typeof(int));
    
            var office_IdParameter = office_Id != null ?
                new ObjectParameter("Office_Id", office_Id) :
                new ObjectParameter("Office_Id", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_tbl_LoanApp_By_Status_Surety_Confirm_Result>("Select_All_tbl_LoanApp_By_Status_Surety_Confirm", chr_statusParameter, orderColumnParameter, orderDirectionParameter, startParameter, lengthParameter, office_IdParameter);
        }
    
        public virtual ObjectResult<Select_All_tbl_loanreg_By_LoanNo_Result> Select_All_tbl_loanreg_By_LoanNo(string int_loanno)
        {
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_tbl_loanreg_By_LoanNo_Result>("Select_All_tbl_loanreg_By_LoanNo", int_loannoParameter);
        }
    
        public virtual ObjectResult<Select_All_tbl_Logs_Result> Select_All_tbl_Logs()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_tbl_Logs_Result>("Select_All_tbl_Logs");
        }
    
        public virtual ObjectResult<Select_All_tbl_Projects_By_Sector_Id_Result> Select_All_tbl_Projects_By_Sector_Id(Nullable<int> sector_Id)
        {
            var sector_IdParameter = sector_Id.HasValue ?
                new ObjectParameter("Sector_Id", sector_Id) :
                new ObjectParameter("Sector_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_tbl_Projects_By_Sector_Id_Result>("Select_All_tbl_Projects_By_Sector_Id", sector_IdParameter);
        }
    
        public virtual ObjectResult<Select_All_tbl_Sectors_Result> Select_All_tbl_Sectors()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_tbl_Sectors_Result>("Select_All_tbl_Sectors");
        }
    
        public virtual ObjectResult<Select_All_tbl_Taluk_By_District_Id_Result> Select_All_tbl_Taluk_By_District_Id(Nullable<int> district_Id)
        {
            var district_IdParameter = district_Id.HasValue ?
                new ObjectParameter("District_Id", district_Id) :
                new ObjectParameter("District_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_tbl_Taluk_By_District_Id_Result>("Select_All_tbl_Taluk_By_District_Id", district_IdParameter);
        }
    
        public virtual ObjectResult<Select_All_tbl_Taluk_By_Id_Result> Select_All_tbl_Taluk_By_Id(Nullable<int> village_Id)
        {
            var village_IdParameter = village_Id.HasValue ?
                new ObjectParameter("Village_Id", village_Id) :
                new ObjectParameter("Village_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_tbl_Taluk_By_Id_Result>("Select_All_tbl_Taluk_By_Id", village_IdParameter);
        }
    
        public virtual ObjectResult<Select_All_Users_Result> Select_All_Users()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Users_Result>("Select_All_Users");
        }
    
        public virtual ObjectResult<Select_All_Users_By_ID_Result> Select_All_Users_By_ID(Nullable<int> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Users_By_ID_Result>("Select_All_Users_By_ID", idParameter);
        }
    
        public virtual ObjectResult<Select_All_Village_By_Id_Result> Select_All_Village_By_Id(Nullable<int> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Village_By_Id_Result>("Select_All_Village_By_Id", idParameter);
        }
    
        public virtual ObjectResult<Select_All_Village_By_Office_Id_Result> Select_All_Village_By_Office_Id(string office_Id)
        {
            var office_IdParameter = office_Id != null ?
                new ObjectParameter("Office_Id", office_Id) :
                new ObjectParameter("Office_Id", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Village_By_Office_Id_Result>("Select_All_Village_By_Office_Id", office_IdParameter);
        }
    
        public virtual ObjectResult<Select_All_Villages_Result> Select_All_Villages()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Villages_Result>("Select_All_Villages");
        }
    
        public virtual ObjectResult<Select_All_Villages_By_Taluk_Id_Result> Select_All_Villages_By_Taluk_Id(Nullable<int> taluk_Id)
        {
            var taluk_IdParameter = taluk_Id.HasValue ?
                new ObjectParameter("Taluk_Id", taluk_Id) :
                new ObjectParameter("Taluk_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_Villages_By_Taluk_Id_Result>("Select_All_Villages_By_Taluk_Id", taluk_IdParameter);
        }
    
        public virtual ObjectResult<Select_LoanApp_By_IssueId_Result> Select_LoanApp_By_IssueId(Nullable<int> loan_App_Issue_Id)
        {
            var loan_App_Issue_IdParameter = loan_App_Issue_Id.HasValue ?
                new ObjectParameter("Loan_App_Issue_Id", loan_App_Issue_Id) :
                new ObjectParameter("Loan_App_Issue_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_LoanApp_By_IssueId_Result>("Select_LoanApp_By_IssueId", loan_App_Issue_IdParameter);
        }
    
        public virtual ObjectResult<Select_Lonee_Details_And_Account_Result> Select_Lonee_Details_And_Account(string int_loanno)
        {
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_Lonee_Details_And_Account_Result>("Select_Lonee_Details_And_Account", int_loannoParameter);
        }
    
        public virtual ObjectResult<Select_Lonee_Details_By_Loan_No_Result> Select_Lonee_Details_By_Loan_No(string int_loanno)
        {
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_Lonee_Details_By_Loan_No_Result>("Select_Lonee_Details_By_Loan_No", int_loannoParameter);
        }
    
        public virtual ObjectResult<Select_Sub_Caste_Details_By_Sub_Caste_Name_Result> Select_Sub_Caste_Details_By_Sub_Caste_Name(string sub_Caste)
        {
            var sub_CasteParameter = sub_Caste != null ?
                new ObjectParameter("Sub_Caste", sub_Caste) :
                new ObjectParameter("Sub_Caste", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_Sub_Caste_Details_By_Sub_Caste_Name_Result>("Select_Sub_Caste_Details_By_Sub_Caste_Name", sub_CasteParameter);
        }
    
        public virtual ObjectResult<Select_tbl_agriloan_By_int_loanappid_Result> Select_tbl_agriloan_By_int_loanappid(Nullable<int> int_loanappid)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_tbl_agriloan_By_int_loanappid_Result>("Select_tbl_agriloan_By_int_loanappid", int_loanappidParameter);
        }
    
        public virtual ObjectResult<Select_tbl_BankDetails_By_int_loanappid_Result> Select_tbl_BankDetails_By_int_loanappid(Nullable<decimal> int_loanappid)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_tbl_BankDetails_By_int_loanappid_Result>("Select_tbl_BankDetails_By_int_loanappid", int_loanappidParameter);
        }
    
        public virtual ObjectResult<Select_tbl_disbursement_By_RegNo_Result> Select_tbl_disbursement_By_RegNo(string appreceivr_RegNo, string loanNo)
        {
            var appreceivr_RegNoParameter = appreceivr_RegNo != null ?
                new ObjectParameter("Appreceivr_RegNo", appreceivr_RegNo) :
                new ObjectParameter("Appreceivr_RegNo", typeof(string));
    
            var loanNoParameter = loanNo != null ?
                new ObjectParameter("LoanNo", loanNo) :
                new ObjectParameter("LoanNo", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_tbl_disbursement_By_RegNo_Result>("Select_tbl_disbursement_By_RegNo", appreceivr_RegNoParameter, loanNoParameter);
        }
    
        public virtual ObjectResult<Select_tbl_eduloanapp_By_int_loanappid_Result> Select_tbl_eduloanapp_By_int_loanappid(Nullable<decimal> int_loanappid)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_tbl_eduloanapp_By_int_loanappid_Result>("Select_tbl_eduloanapp_By_int_loanappid", int_loanappidParameter);
        }
    
        public virtual ObjectResult<Select_tbl_empsurety_By_Id_Result> Select_tbl_empsurety_By_Id(Nullable<decimal> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_tbl_empsurety_By_Id_Result>("Select_tbl_empsurety_By_Id", idParameter);
        }
    
        public virtual ObjectResult<Select_tbl_FDLICsurety_Id_Result> Select_tbl_FDLICsurety_Id(Nullable<decimal> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_tbl_FDLICsurety_Id_Result>("Select_tbl_FDLICsurety_Id", idParameter);
        }
    
        public virtual ObjectResult<Select_tbl_foreignloan_By_int_loanappid_Result> Select_tbl_foreignloan_By_int_loanappid(Nullable<decimal> int_loanappid)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_tbl_foreignloan_By_int_loanappid_Result>("Select_tbl_foreignloan_By_int_loanappid", int_loanappidParameter);
        }
    
        public virtual ObjectResult<Select_tbl_House_Loan_By_int_loanappid_Result> Select_tbl_House_Loan_By_int_loanappid(Nullable<decimal> int_loanappid)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_tbl_House_Loan_By_int_loanappid_Result>("Select_tbl_House_Loan_By_int_loanappid", int_loanappidParameter);
        }
    
        public virtual ObjectResult<Select_tbl_hsemaintanenceloan_By_int_loanappid_Result> Select_tbl_hsemaintanenceloan_By_int_loanappid(Nullable<decimal> int_loanappid)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_tbl_hsemaintanenceloan_By_int_loanappid_Result>("Select_tbl_hsemaintanenceloan_By_int_loanappid", int_loanappidParameter);
        }
    
        public virtual ObjectResult<Select_tbl_KSDC_SMART_Issues_By_Id_Result> Select_tbl_KSDC_SMART_Issues_By_Id(Nullable<int> issue_Id)
        {
            var issue_IdParameter = issue_Id.HasValue ?
                new ObjectParameter("Issue_Id", issue_Id) :
                new ObjectParameter("Issue_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_tbl_KSDC_SMART_Issues_By_Id_Result>("Select_tbl_KSDC_SMART_Issues_By_Id", issue_IdParameter);
        }
    
        public virtual ObjectResult<Select_tbl_KSDC_SMART_Issues_Messages_By_Issue_Id_Result> Select_tbl_KSDC_SMART_Issues_Messages_By_Issue_Id(Nullable<int> issue_Id)
        {
            var issue_IdParameter = issue_Id.HasValue ?
                new ObjectParameter("Issue_Id", issue_Id) :
                new ObjectParameter("Issue_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_tbl_KSDC_SMART_Issues_Messages_By_Issue_Id_Result>("Select_tbl_KSDC_SMART_Issues_Messages_By_Issue_Id", issue_IdParameter);
        }
    
        public virtual ObjectResult<Select_tbl_landsurety_By_Id_Result> Select_tbl_landsurety_By_Id(Nullable<decimal> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_tbl_landsurety_By_Id_Result>("Select_tbl_landsurety_By_Id", idParameter);
        }
    
        public virtual ObjectResult<Select_tbl_LoanApp_By_LoanAppId_Result> Select_tbl_LoanApp_By_LoanAppId(Nullable<int> loanappid)
        {
            var loanappidParameter = loanappid.HasValue ?
                new ObjectParameter("loanappid", loanappid) :
                new ObjectParameter("loanappid", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_tbl_LoanApp_By_LoanAppId_Result>("Select_tbl_LoanApp_By_LoanAppId", loanappidParameter);
        }
    
        public virtual ObjectResult<Select_tbl_loanapp_By_Reg_No_Result> Select_tbl_loanapp_By_Reg_No(string vchr_appreceivregno)
        {
            var vchr_appreceivregnoParameter = vchr_appreceivregno != null ?
                new ObjectParameter("vchr_appreceivregno", vchr_appreceivregno) :
                new ObjectParameter("vchr_appreceivregno", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_tbl_loanapp_By_Reg_No_Result>("Select_tbl_loanapp_By_Reg_No", vchr_appreceivregnoParameter);
        }
    
        public virtual ObjectResult<Select_tbl_loanreg_By_RegNo_Or_LoanNo_Result> Select_tbl_loanreg_By_RegNo_Or_LoanNo(string appreceivr_RegNo, string loanNo)
        {
            var appreceivr_RegNoParameter = appreceivr_RegNo != null ?
                new ObjectParameter("Appreceivr_RegNo", appreceivr_RegNo) :
                new ObjectParameter("Appreceivr_RegNo", typeof(string));
    
            var loanNoParameter = loanNo != null ?
                new ObjectParameter("LoanNo", loanNo) :
                new ObjectParameter("LoanNo", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_tbl_loanreg_By_RegNo_Or_LoanNo_Result>("Select_tbl_loanreg_By_RegNo_Or_LoanNo", appreceivr_RegNoParameter, loanNoParameter);
        }
    
        public virtual ObjectResult<Select_tbl_Logs_By_Id_Result> Select_tbl_Logs_By_Id(Nullable<int> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_tbl_Logs_By_Id_Result>("Select_tbl_Logs_By_Id", idParameter);
        }
    
        public virtual ObjectResult<Select_tbl_marriageloan_By_int_loanappid_Result> Select_tbl_marriageloan_By_int_loanappid(Nullable<decimal> int_loanappid)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_tbl_marriageloan_By_int_loanappid_Result>("Select_tbl_marriageloan_By_int_loanappid", int_loanappidParameter);
        }
    
        public virtual ObjectResult<Select_tbl_personal_loan_By_int_loanappid_Result> Select_tbl_personal_loan_By_int_loanappid(Nullable<decimal> int_loanappid)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_tbl_personal_loan_By_int_loanappid_Result>("Select_tbl_personal_loan_By_int_loanappid", int_loanappidParameter);
        }
    
        public virtual ObjectResult<Select_tbl_selfloan_By_loanappid_Result> Select_tbl_selfloan_By_loanappid(Nullable<int> int_loanappid)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_tbl_selfloan_By_loanappid_Result>("Select_tbl_selfloan_By_loanappid", int_loanappidParameter);
        }
    
        public virtual ObjectResult<Select_tbl_Sub_Reg_Office_By_District_Id_Result> Select_tbl_Sub_Reg_Office_By_District_Id(Nullable<int> district_Id)
        {
            var district_IdParameter = district_Id.HasValue ?
                new ObjectParameter("District_Id", district_Id) :
                new ObjectParameter("District_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_tbl_Sub_Reg_Office_By_District_Id_Result>("Select_tbl_Sub_Reg_Office_By_District_Id", district_IdParameter);
        }
    
        public virtual ObjectResult<Select_tbl_workcapital_By_int_loanappid_Result> Select_tbl_workcapital_By_int_loanappid(Nullable<decimal> int_loanappid)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_tbl_workcapital_By_int_loanappid_Result>("Select_tbl_workcapital_By_int_loanappid", int_loanappidParameter);
        }
    
        public virtual ObjectResult<Select_To_tbl_veh_hme_loan_By_int_loanappid_Result> Select_To_tbl_veh_hme_loan_By_int_loanappid(Nullable<decimal> int_loanappid)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_To_tbl_veh_hme_loan_By_int_loanappid_Result>("Select_To_tbl_veh_hme_loan_By_int_loanappid", int_loanappidParameter);
        }
    
        public virtual ObjectResult<Select_Year_End_Record_Result> Select_Year_End_Record(string int_loanno, Nullable<System.DateTime> dt_transaction)
        {
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            var dt_transactionParameter = dt_transaction.HasValue ?
                new ObjectParameter("dt_transaction", dt_transaction) :
                new ObjectParameter("dt_transaction", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_Year_End_Record_Result>("Select_Year_End_Record", int_loannoParameter, dt_transactionParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> Total_Count_Loan_Trans(string int_loanno)
        {
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("Total_Count_Loan_Trans", int_loannoParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> Total_Count_LoanApp_By_Status_Disbursement(string office_Id)
        {
            var office_IdParameter = office_Id != null ?
                new ObjectParameter("Office_Id", office_Id) :
                new ObjectParameter("Office_Id", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("Total_Count_LoanApp_By_Status_Disbursement", office_IdParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> Total_Count_Personal_Ledger(string old_Loan_No, string loan_No, string loanee_Name, string house_Name, string mobile_No, string office_Id)
        {
            var old_Loan_NoParameter = old_Loan_No != null ?
                new ObjectParameter("Old_Loan_No", old_Loan_No) :
                new ObjectParameter("Old_Loan_No", typeof(string));
    
            var loan_NoParameter = loan_No != null ?
                new ObjectParameter("Loan_No", loan_No) :
                new ObjectParameter("Loan_No", typeof(string));
    
            var loanee_NameParameter = loanee_Name != null ?
                new ObjectParameter("Loanee_Name", loanee_Name) :
                new ObjectParameter("Loanee_Name", typeof(string));
    
            var house_NameParameter = house_Name != null ?
                new ObjectParameter("House_Name", house_Name) :
                new ObjectParameter("House_Name", typeof(string));
    
            var mobile_NoParameter = mobile_No != null ?
                new ObjectParameter("Mobile_No", mobile_No) :
                new ObjectParameter("Mobile_No", typeof(string));
    
            var office_IdParameter = office_Id != null ?
                new ObjectParameter("Office_Id", office_Id) :
                new ObjectParameter("Office_Id", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("Total_Count_Personal_Ledger", old_Loan_NoParameter, loan_NoParameter, loanee_NameParameter, house_NameParameter, mobile_NoParameter, office_IdParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> Total_Count_tbl_KSDC_SMART_Issues(Nullable<int> branchId)
        {
            var branchIdParameter = branchId.HasValue ?
                new ObjectParameter("BranchId", branchId) :
                new ObjectParameter("BranchId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("Total_Count_tbl_KSDC_SMART_Issues", branchIdParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> Total_Count_tbl_KSDC_SMART_Issues_All_Branches()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("Total_Count_tbl_KSDC_SMART_Issues_All_Branches");
        }
    
        public virtual ObjectResult<Nullable<int>> Total_Count_tbl_Loan_App_Issue(string id, string reciept_No, string name_Applicant, string dustrict_Name, string scheme, string cast, string subCast, string application_Submitted_Date, string office_Id)
        {
            var idParameter = id != null ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(string));
    
            var reciept_NoParameter = reciept_No != null ?
                new ObjectParameter("Reciept_No", reciept_No) :
                new ObjectParameter("Reciept_No", typeof(string));
    
            var name_ApplicantParameter = name_Applicant != null ?
                new ObjectParameter("Name_Applicant", name_Applicant) :
                new ObjectParameter("Name_Applicant", typeof(string));
    
            var dustrict_NameParameter = dustrict_Name != null ?
                new ObjectParameter("Dustrict_Name", dustrict_Name) :
                new ObjectParameter("Dustrict_Name", typeof(string));
    
            var schemeParameter = scheme != null ?
                new ObjectParameter("Scheme", scheme) :
                new ObjectParameter("Scheme", typeof(string));
    
            var castParameter = cast != null ?
                new ObjectParameter("Cast", cast) :
                new ObjectParameter("Cast", typeof(string));
    
            var subCastParameter = subCast != null ?
                new ObjectParameter("SubCast", subCast) :
                new ObjectParameter("SubCast", typeof(string));
    
            var application_Submitted_DateParameter = application_Submitted_Date != null ?
                new ObjectParameter("Application_Submitted_Date", application_Submitted_Date) :
                new ObjectParameter("Application_Submitted_Date", typeof(string));
    
            var office_IdParameter = office_Id != null ?
                new ObjectParameter("Office_Id", office_Id) :
                new ObjectParameter("Office_Id", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("Total_Count_tbl_Loan_App_Issue", idParameter, reciept_NoParameter, name_ApplicantParameter, dustrict_NameParameter, schemeParameter, castParameter, subCastParameter, application_Submitted_DateParameter, office_IdParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> Total_Count_tbl_LoanApp_By_Status(string chr_status, string office_Id)
        {
            var chr_statusParameter = chr_status != null ?
                new ObjectParameter("chr_status", chr_status) :
                new ObjectParameter("chr_status", typeof(string));
    
            var office_IdParameter = office_Id != null ?
                new ObjectParameter("Office_Id", office_Id) :
                new ObjectParameter("Office_Id", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("Total_Count_tbl_LoanApp_By_Status", chr_statusParameter, office_IdParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> Total_Count_tbl_LoanApp_By_Surety(string chr_status, string office_Id)
        {
            var chr_statusParameter = chr_status != null ?
                new ObjectParameter("chr_status", chr_status) :
                new ObjectParameter("chr_status", typeof(string));
    
            var office_IdParameter = office_Id != null ?
                new ObjectParameter("Office_Id", office_Id) :
                new ObjectParameter("Office_Id", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("Total_Count_tbl_LoanApp_By_Surety", chr_statusParameter, office_IdParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> Total_Count_tbl_Projects()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("Total_Count_tbl_Projects");
        }
    
        public virtual int Update_Checklist_Approve_Status(Nullable<decimal> int_loanappid, string status, string remarks, Nullable<System.DateTime> agrDate, string regNo)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var statusParameter = status != null ?
                new ObjectParameter("Status", status) :
                new ObjectParameter("Status", typeof(string));
    
            var remarksParameter = remarks != null ?
                new ObjectParameter("Remarks", remarks) :
                new ObjectParameter("Remarks", typeof(string));
    
            var agrDateParameter = agrDate.HasValue ?
                new ObjectParameter("AgrDate", agrDate) :
                new ObjectParameter("AgrDate", typeof(System.DateTime));
    
            var regNoParameter = regNo != null ?
                new ObjectParameter("RegNo", regNo) :
                new ObjectParameter("RegNo", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_Checklist_Approve_Status", int_loanappidParameter, statusParameter, remarksParameter, agrDateParameter, regNoParameter);
        }
    
        public virtual int Update_Loan_Order_Status(Nullable<decimal> int_loanappid, string status, string regNo, Nullable<int> user_Id)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var statusParameter = status != null ?
                new ObjectParameter("Status", status) :
                new ObjectParameter("Status", typeof(string));
    
            var regNoParameter = regNo != null ?
                new ObjectParameter("RegNo", regNo) :
                new ObjectParameter("RegNo", typeof(string));
    
            var user_IdParameter = user_Id.HasValue ?
                new ObjectParameter("User_Id", user_Id) :
                new ObjectParameter("User_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_Loan_Order_Status", int_loanappidParameter, statusParameter, regNoParameter, user_IdParameter);
        }
    
        public virtual int Update_loanTrans_By_LoanNo(Nullable<int> int_transid, Nullable<decimal> int_prin_amt, Nullable<decimal> int_int_amt, Nullable<decimal> int_penal_amt, Nullable<decimal> int_int_r, Nullable<decimal> int_penal_r, Nullable<decimal> int_prin_dues, Nullable<decimal> int_int_dues, Nullable<decimal> int_penal_dues)
        {
            var int_transidParameter = int_transid.HasValue ?
                new ObjectParameter("int_transid", int_transid) :
                new ObjectParameter("int_transid", typeof(int));
    
            var int_prin_amtParameter = int_prin_amt.HasValue ?
                new ObjectParameter("int_prin_amt", int_prin_amt) :
                new ObjectParameter("int_prin_amt", typeof(decimal));
    
            var int_int_amtParameter = int_int_amt.HasValue ?
                new ObjectParameter("int_int_amt", int_int_amt) :
                new ObjectParameter("int_int_amt", typeof(decimal));
    
            var int_penal_amtParameter = int_penal_amt.HasValue ?
                new ObjectParameter("int_penal_amt", int_penal_amt) :
                new ObjectParameter("int_penal_amt", typeof(decimal));
    
            var int_int_rParameter = int_int_r.HasValue ?
                new ObjectParameter("int_int_r", int_int_r) :
                new ObjectParameter("int_int_r", typeof(decimal));
    
            var int_penal_rParameter = int_penal_r.HasValue ?
                new ObjectParameter("int_penal_r", int_penal_r) :
                new ObjectParameter("int_penal_r", typeof(decimal));
    
            var int_prin_duesParameter = int_prin_dues.HasValue ?
                new ObjectParameter("int_prin_dues", int_prin_dues) :
                new ObjectParameter("int_prin_dues", typeof(decimal));
    
            var int_int_duesParameter = int_int_dues.HasValue ?
                new ObjectParameter("int_int_dues", int_int_dues) :
                new ObjectParameter("int_int_dues", typeof(decimal));
    
            var int_penal_duesParameter = int_penal_dues.HasValue ?
                new ObjectParameter("int_penal_dues", int_penal_dues) :
                new ObjectParameter("int_penal_dues", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_loanTrans_By_LoanNo", int_transidParameter, int_prin_amtParameter, int_int_amtParameter, int_penal_amtParameter, int_int_rParameter, int_penal_rParameter, int_prin_duesParameter, int_int_duesParameter, int_penal_duesParameter);
        }
    
        public virtual int Update_Surety_Confirmation_By_Id(Nullable<int> id, string type, Nullable<System.DateTime> dte_conf_send_date, Nullable<System.DateTime> dte_conf_rec_date)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            var typeParameter = type != null ?
                new ObjectParameter("Type", type) :
                new ObjectParameter("Type", typeof(string));
    
            var dte_conf_send_dateParameter = dte_conf_send_date.HasValue ?
                new ObjectParameter("dte_conf_send_date", dte_conf_send_date) :
                new ObjectParameter("dte_conf_send_date", typeof(System.DateTime));
    
            var dte_conf_rec_dateParameter = dte_conf_rec_date.HasValue ?
                new ObjectParameter("dte_conf_rec_date", dte_conf_rec_date) :
                new ObjectParameter("dte_conf_rec_date", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_Surety_Confirmation_By_Id", idParameter, typeParameter, dte_conf_send_dateParameter, dte_conf_rec_dateParameter);
        }
    
        public virtual int Update_To_tbl_Agency(Nullable<int> id, string name, string description, string cast, Nullable<int> isActive, Nullable<int> updatedBy)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            var nameParameter = name != null ?
                new ObjectParameter("Name", name) :
                new ObjectParameter("Name", typeof(string));
    
            var descriptionParameter = description != null ?
                new ObjectParameter("Description", description) :
                new ObjectParameter("Description", typeof(string));
    
            var castParameter = cast != null ?
                new ObjectParameter("Cast", cast) :
                new ObjectParameter("Cast", typeof(string));
    
            var isActiveParameter = isActive.HasValue ?
                new ObjectParameter("IsActive", isActive) :
                new ObjectParameter("IsActive", typeof(int));
    
            var updatedByParameter = updatedBy.HasValue ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_Agency", idParameter, nameParameter, descriptionParameter, castParameter, isActiveParameter, updatedByParameter);
        }
    
        public virtual int Update_To_tbl_agriloan(Nullable<int> int_loanappid, Nullable<int> int_schemeid, string vchr_sector, string vchr_project, Nullable<decimal> int_amt_est, Nullable<decimal> int_amt_hand, string vchr_projdetails, string vchr_ext_prop)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(int));
    
            var int_schemeidParameter = int_schemeid.HasValue ?
                new ObjectParameter("int_schemeid", int_schemeid) :
                new ObjectParameter("int_schemeid", typeof(int));
    
            var vchr_sectorParameter = vchr_sector != null ?
                new ObjectParameter("vchr_sector", vchr_sector) :
                new ObjectParameter("vchr_sector", typeof(string));
    
            var vchr_projectParameter = vchr_project != null ?
                new ObjectParameter("vchr_project", vchr_project) :
                new ObjectParameter("vchr_project", typeof(string));
    
            var int_amt_estParameter = int_amt_est.HasValue ?
                new ObjectParameter("int_amt_est", int_amt_est) :
                new ObjectParameter("int_amt_est", typeof(decimal));
    
            var int_amt_handParameter = int_amt_hand.HasValue ?
                new ObjectParameter("int_amt_hand", int_amt_hand) :
                new ObjectParameter("int_amt_hand", typeof(decimal));
    
            var vchr_projdetailsParameter = vchr_projdetails != null ?
                new ObjectParameter("vchr_projdetails", vchr_projdetails) :
                new ObjectParameter("vchr_projdetails", typeof(string));
    
            var vchr_ext_propParameter = vchr_ext_prop != null ?
                new ObjectParameter("vchr_ext_prop", vchr_ext_prop) :
                new ObjectParameter("vchr_ext_prop", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_agriloan", int_loanappidParameter, int_schemeidParameter, vchr_sectorParameter, vchr_projectParameter, int_amt_estParameter, int_amt_handParameter, vchr_projdetailsParameter, vchr_ext_propParameter);
        }
    
        public virtual int Update_To_tbl_BankDetails(Nullable<decimal> int_loanappid, string int_BankAccNo, string vchr_IFSC, string vchr_Bank, string vchr_Branch, string vchr_user, Nullable<decimal> int_modify)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var int_BankAccNoParameter = int_BankAccNo != null ?
                new ObjectParameter("int_BankAccNo", int_BankAccNo) :
                new ObjectParameter("int_BankAccNo", typeof(string));
    
            var vchr_IFSCParameter = vchr_IFSC != null ?
                new ObjectParameter("vchr_IFSC", vchr_IFSC) :
                new ObjectParameter("vchr_IFSC", typeof(string));
    
            var vchr_BankParameter = vchr_Bank != null ?
                new ObjectParameter("vchr_Bank", vchr_Bank) :
                new ObjectParameter("vchr_Bank", typeof(string));
    
            var vchr_BranchParameter = vchr_Branch != null ?
                new ObjectParameter("vchr_Branch", vchr_Branch) :
                new ObjectParameter("vchr_Branch", typeof(string));
    
            var vchr_userParameter = vchr_user != null ?
                new ObjectParameter("vchr_user", vchr_user) :
                new ObjectParameter("vchr_user", typeof(string));
    
            var int_modifyParameter = int_modify.HasValue ?
                new ObjectParameter("int_modify", int_modify) :
                new ObjectParameter("int_modify", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_BankDetails", int_loanappidParameter, int_BankAccNoParameter, vchr_IFSCParameter, vchr_BankParameter, vchr_BranchParameter, vchr_userParameter, int_modifyParameter);
        }
    
        public virtual int Update_To_tbl_Department_By_int_deptid(Nullable<decimal> int_deptid, string vchr_deptname)
        {
            var int_deptidParameter = int_deptid.HasValue ?
                new ObjectParameter("int_deptid", int_deptid) :
                new ObjectParameter("int_deptid", typeof(decimal));
    
            var vchr_deptnameParameter = vchr_deptname != null ?
                new ObjectParameter("vchr_deptname", vchr_deptname) :
                new ObjectParameter("vchr_deptname", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_Department_By_int_deptid", int_deptidParameter, vchr_deptnameParameter);
        }
    
        public virtual int Update_To_tbl_disbursement_By_RegNo(string vchr_regno, Nullable<int> int_instalNo, Nullable<decimal> mny_InstallAmt, Nullable<decimal> mny_disbamt, Nullable<decimal> mny_disbamtbal, Nullable<System.DateTime> dt_disbdate, string vchr_vouchrno, string vchr_chkno, string vchr_accno, string vchr_loanno, string vchr_verify, Nullable<System.DateTime> dt_verify, string int_BankAccNo, string vchr_IFSC, Nullable<System.DateTime> dt_UCDate, Nullable<int> int_Disb)
        {
            var vchr_regnoParameter = vchr_regno != null ?
                new ObjectParameter("vchr_regno", vchr_regno) :
                new ObjectParameter("vchr_regno", typeof(string));
    
            var int_instalNoParameter = int_instalNo.HasValue ?
                new ObjectParameter("int_instalNo", int_instalNo) :
                new ObjectParameter("int_instalNo", typeof(int));
    
            var mny_InstallAmtParameter = mny_InstallAmt.HasValue ?
                new ObjectParameter("mny_InstallAmt", mny_InstallAmt) :
                new ObjectParameter("mny_InstallAmt", typeof(decimal));
    
            var mny_disbamtParameter = mny_disbamt.HasValue ?
                new ObjectParameter("mny_disbamt", mny_disbamt) :
                new ObjectParameter("mny_disbamt", typeof(decimal));
    
            var mny_disbamtbalParameter = mny_disbamtbal.HasValue ?
                new ObjectParameter("mny_disbamtbal", mny_disbamtbal) :
                new ObjectParameter("mny_disbamtbal", typeof(decimal));
    
            var dt_disbdateParameter = dt_disbdate.HasValue ?
                new ObjectParameter("dt_disbdate", dt_disbdate) :
                new ObjectParameter("dt_disbdate", typeof(System.DateTime));
    
            var vchr_vouchrnoParameter = vchr_vouchrno != null ?
                new ObjectParameter("vchr_vouchrno", vchr_vouchrno) :
                new ObjectParameter("vchr_vouchrno", typeof(string));
    
            var vchr_chknoParameter = vchr_chkno != null ?
                new ObjectParameter("vchr_chkno", vchr_chkno) :
                new ObjectParameter("vchr_chkno", typeof(string));
    
            var vchr_accnoParameter = vchr_accno != null ?
                new ObjectParameter("vchr_accno", vchr_accno) :
                new ObjectParameter("vchr_accno", typeof(string));
    
            var vchr_loannoParameter = vchr_loanno != null ?
                new ObjectParameter("vchr_loanno", vchr_loanno) :
                new ObjectParameter("vchr_loanno", typeof(string));
    
            var vchr_verifyParameter = vchr_verify != null ?
                new ObjectParameter("vchr_verify", vchr_verify) :
                new ObjectParameter("vchr_verify", typeof(string));
    
            var dt_verifyParameter = dt_verify.HasValue ?
                new ObjectParameter("dt_verify", dt_verify) :
                new ObjectParameter("dt_verify", typeof(System.DateTime));
    
            var int_BankAccNoParameter = int_BankAccNo != null ?
                new ObjectParameter("int_BankAccNo", int_BankAccNo) :
                new ObjectParameter("int_BankAccNo", typeof(string));
    
            var vchr_IFSCParameter = vchr_IFSC != null ?
                new ObjectParameter("vchr_IFSC", vchr_IFSC) :
                new ObjectParameter("vchr_IFSC", typeof(string));
    
            var dt_UCDateParameter = dt_UCDate.HasValue ?
                new ObjectParameter("dt_UCDate", dt_UCDate) :
                new ObjectParameter("dt_UCDate", typeof(System.DateTime));
    
            var int_DisbParameter = int_Disb.HasValue ?
                new ObjectParameter("int_Disb", int_Disb) :
                new ObjectParameter("int_Disb", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_disbursement_By_RegNo", vchr_regnoParameter, int_instalNoParameter, mny_InstallAmtParameter, mny_disbamtParameter, mny_disbamtbalParameter, dt_disbdateParameter, vchr_vouchrnoParameter, vchr_chknoParameter, vchr_accnoParameter, vchr_loannoParameter, vchr_verifyParameter, dt_verifyParameter, int_BankAccNoParameter, vchr_IFSCParameter, dt_UCDateParameter, int_DisbParameter);
        }
    
        public virtual int Update_To_tbl_eduloanapp(Nullable<decimal> int_loanappid, string vchr_stuname, string vchr_stufathername, string vchr_stumothername, Nullable<System.DateTime> dte_studob, Nullable<decimal> int_stuage, string vchr_caste, string sub_Cast, string vchr_presenthsename, string vchr_presentlane1, string vchr_presentlane2, string vchr_presentpost, Nullable<decimal> int_presentpin, string vchr_presentphno, string vchr_permhsename, string vchr_permlane1, string vchr_permlane2, string vchr_permpost, Nullable<decimal> int_permpin, string vchr_permphno, string vchr_coursename, string vchr_instname, string vchr_affluni, string vchr_affldetails, string vchr_duration, string vchr_month_commencement, Nullable<decimal> int_yr_commencement, string vchr_month_completion, Nullable<decimal> int_yr_completion, string vchr_parent_fullname, string vchr_parent_presenthsename, string vchr_parent_presentlane1, string vchr_parent_presentlane2, string vchr_parent_presentpost, Nullable<decimal> int_parent_presentpin, string vchr_parent_presentphno, string vchr_parent_permhsename, string vchr_parent_permlane1, string vchr_parent_permlane2, string vchr_parent_permpost, Nullable<decimal> int_parent_permpin, string vchr_parent_permphno, string vchr_parent_relationship, string vchr_employed, string vchr_parentfathername, Nullable<decimal> int_parentage, string vchr_sex, string vchr_country, string vchr_state)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var vchr_stunameParameter = vchr_stuname != null ?
                new ObjectParameter("vchr_stuname", vchr_stuname) :
                new ObjectParameter("vchr_stuname", typeof(string));
    
            var vchr_stufathernameParameter = vchr_stufathername != null ?
                new ObjectParameter("vchr_stufathername", vchr_stufathername) :
                new ObjectParameter("vchr_stufathername", typeof(string));
    
            var vchr_stumothernameParameter = vchr_stumothername != null ?
                new ObjectParameter("vchr_stumothername", vchr_stumothername) :
                new ObjectParameter("vchr_stumothername", typeof(string));
    
            var dte_studobParameter = dte_studob.HasValue ?
                new ObjectParameter("dte_studob", dte_studob) :
                new ObjectParameter("dte_studob", typeof(System.DateTime));
    
            var int_stuageParameter = int_stuage.HasValue ?
                new ObjectParameter("int_stuage", int_stuage) :
                new ObjectParameter("int_stuage", typeof(decimal));
    
            var vchr_casteParameter = vchr_caste != null ?
                new ObjectParameter("vchr_caste", vchr_caste) :
                new ObjectParameter("vchr_caste", typeof(string));
    
            var sub_CastParameter = sub_Cast != null ?
                new ObjectParameter("Sub_Cast", sub_Cast) :
                new ObjectParameter("Sub_Cast", typeof(string));
    
            var vchr_presenthsenameParameter = vchr_presenthsename != null ?
                new ObjectParameter("vchr_presenthsename", vchr_presenthsename) :
                new ObjectParameter("vchr_presenthsename", typeof(string));
    
            var vchr_presentlane1Parameter = vchr_presentlane1 != null ?
                new ObjectParameter("vchr_presentlane1", vchr_presentlane1) :
                new ObjectParameter("vchr_presentlane1", typeof(string));
    
            var vchr_presentlane2Parameter = vchr_presentlane2 != null ?
                new ObjectParameter("vchr_presentlane2", vchr_presentlane2) :
                new ObjectParameter("vchr_presentlane2", typeof(string));
    
            var vchr_presentpostParameter = vchr_presentpost != null ?
                new ObjectParameter("vchr_presentpost", vchr_presentpost) :
                new ObjectParameter("vchr_presentpost", typeof(string));
    
            var int_presentpinParameter = int_presentpin.HasValue ?
                new ObjectParameter("int_presentpin", int_presentpin) :
                new ObjectParameter("int_presentpin", typeof(decimal));
    
            var vchr_presentphnoParameter = vchr_presentphno != null ?
                new ObjectParameter("vchr_presentphno", vchr_presentphno) :
                new ObjectParameter("vchr_presentphno", typeof(string));
    
            var vchr_permhsenameParameter = vchr_permhsename != null ?
                new ObjectParameter("vchr_permhsename", vchr_permhsename) :
                new ObjectParameter("vchr_permhsename", typeof(string));
    
            var vchr_permlane1Parameter = vchr_permlane1 != null ?
                new ObjectParameter("vchr_permlane1", vchr_permlane1) :
                new ObjectParameter("vchr_permlane1", typeof(string));
    
            var vchr_permlane2Parameter = vchr_permlane2 != null ?
                new ObjectParameter("vchr_permlane2", vchr_permlane2) :
                new ObjectParameter("vchr_permlane2", typeof(string));
    
            var vchr_permpostParameter = vchr_permpost != null ?
                new ObjectParameter("vchr_permpost", vchr_permpost) :
                new ObjectParameter("vchr_permpost", typeof(string));
    
            var int_permpinParameter = int_permpin.HasValue ?
                new ObjectParameter("int_permpin", int_permpin) :
                new ObjectParameter("int_permpin", typeof(decimal));
    
            var vchr_permphnoParameter = vchr_permphno != null ?
                new ObjectParameter("vchr_permphno", vchr_permphno) :
                new ObjectParameter("vchr_permphno", typeof(string));
    
            var vchr_coursenameParameter = vchr_coursename != null ?
                new ObjectParameter("vchr_coursename", vchr_coursename) :
                new ObjectParameter("vchr_coursename", typeof(string));
    
            var vchr_instnameParameter = vchr_instname != null ?
                new ObjectParameter("vchr_instname", vchr_instname) :
                new ObjectParameter("vchr_instname", typeof(string));
    
            var vchr_affluniParameter = vchr_affluni != null ?
                new ObjectParameter("vchr_affluni", vchr_affluni) :
                new ObjectParameter("vchr_affluni", typeof(string));
    
            var vchr_affldetailsParameter = vchr_affldetails != null ?
                new ObjectParameter("vchr_affldetails", vchr_affldetails) :
                new ObjectParameter("vchr_affldetails", typeof(string));
    
            var vchr_durationParameter = vchr_duration != null ?
                new ObjectParameter("vchr_duration", vchr_duration) :
                new ObjectParameter("vchr_duration", typeof(string));
    
            var vchr_month_commencementParameter = vchr_month_commencement != null ?
                new ObjectParameter("vchr_month_commencement", vchr_month_commencement) :
                new ObjectParameter("vchr_month_commencement", typeof(string));
    
            var int_yr_commencementParameter = int_yr_commencement.HasValue ?
                new ObjectParameter("int_yr_commencement", int_yr_commencement) :
                new ObjectParameter("int_yr_commencement", typeof(decimal));
    
            var vchr_month_completionParameter = vchr_month_completion != null ?
                new ObjectParameter("vchr_month_completion", vchr_month_completion) :
                new ObjectParameter("vchr_month_completion", typeof(string));
    
            var int_yr_completionParameter = int_yr_completion.HasValue ?
                new ObjectParameter("int_yr_completion", int_yr_completion) :
                new ObjectParameter("int_yr_completion", typeof(decimal));
    
            var vchr_parent_fullnameParameter = vchr_parent_fullname != null ?
                new ObjectParameter("vchr_parent_fullname", vchr_parent_fullname) :
                new ObjectParameter("vchr_parent_fullname", typeof(string));
    
            var vchr_parent_presenthsenameParameter = vchr_parent_presenthsename != null ?
                new ObjectParameter("vchr_parent_presenthsename", vchr_parent_presenthsename) :
                new ObjectParameter("vchr_parent_presenthsename", typeof(string));
    
            var vchr_parent_presentlane1Parameter = vchr_parent_presentlane1 != null ?
                new ObjectParameter("vchr_parent_presentlane1", vchr_parent_presentlane1) :
                new ObjectParameter("vchr_parent_presentlane1", typeof(string));
    
            var vchr_parent_presentlane2Parameter = vchr_parent_presentlane2 != null ?
                new ObjectParameter("vchr_parent_presentlane2", vchr_parent_presentlane2) :
                new ObjectParameter("vchr_parent_presentlane2", typeof(string));
    
            var vchr_parent_presentpostParameter = vchr_parent_presentpost != null ?
                new ObjectParameter("vchr_parent_presentpost", vchr_parent_presentpost) :
                new ObjectParameter("vchr_parent_presentpost", typeof(string));
    
            var int_parent_presentpinParameter = int_parent_presentpin.HasValue ?
                new ObjectParameter("int_parent_presentpin", int_parent_presentpin) :
                new ObjectParameter("int_parent_presentpin", typeof(decimal));
    
            var vchr_parent_presentphnoParameter = vchr_parent_presentphno != null ?
                new ObjectParameter("vchr_parent_presentphno", vchr_parent_presentphno) :
                new ObjectParameter("vchr_parent_presentphno", typeof(string));
    
            var vchr_parent_permhsenameParameter = vchr_parent_permhsename != null ?
                new ObjectParameter("vchr_parent_permhsename", vchr_parent_permhsename) :
                new ObjectParameter("vchr_parent_permhsename", typeof(string));
    
            var vchr_parent_permlane1Parameter = vchr_parent_permlane1 != null ?
                new ObjectParameter("vchr_parent_permlane1", vchr_parent_permlane1) :
                new ObjectParameter("vchr_parent_permlane1", typeof(string));
    
            var vchr_parent_permlane2Parameter = vchr_parent_permlane2 != null ?
                new ObjectParameter("vchr_parent_permlane2", vchr_parent_permlane2) :
                new ObjectParameter("vchr_parent_permlane2", typeof(string));
    
            var vchr_parent_permpostParameter = vchr_parent_permpost != null ?
                new ObjectParameter("vchr_parent_permpost", vchr_parent_permpost) :
                new ObjectParameter("vchr_parent_permpost", typeof(string));
    
            var int_parent_permpinParameter = int_parent_permpin.HasValue ?
                new ObjectParameter("int_parent_permpin", int_parent_permpin) :
                new ObjectParameter("int_parent_permpin", typeof(decimal));
    
            var vchr_parent_permphnoParameter = vchr_parent_permphno != null ?
                new ObjectParameter("vchr_parent_permphno", vchr_parent_permphno) :
                new ObjectParameter("vchr_parent_permphno", typeof(string));
    
            var vchr_parent_relationshipParameter = vchr_parent_relationship != null ?
                new ObjectParameter("vchr_parent_relationship", vchr_parent_relationship) :
                new ObjectParameter("vchr_parent_relationship", typeof(string));
    
            var vchr_employedParameter = vchr_employed != null ?
                new ObjectParameter("vchr_employed", vchr_employed) :
                new ObjectParameter("vchr_employed", typeof(string));
    
            var vchr_parentfathernameParameter = vchr_parentfathername != null ?
                new ObjectParameter("vchr_parentfathername", vchr_parentfathername) :
                new ObjectParameter("vchr_parentfathername", typeof(string));
    
            var int_parentageParameter = int_parentage.HasValue ?
                new ObjectParameter("int_parentage", int_parentage) :
                new ObjectParameter("int_parentage", typeof(decimal));
    
            var vchr_sexParameter = vchr_sex != null ?
                new ObjectParameter("vchr_sex", vchr_sex) :
                new ObjectParameter("vchr_sex", typeof(string));
    
            var vchr_countryParameter = vchr_country != null ?
                new ObjectParameter("vchr_country", vchr_country) :
                new ObjectParameter("vchr_country", typeof(string));
    
            var vchr_stateParameter = vchr_state != null ?
                new ObjectParameter("vchr_state", vchr_state) :
                new ObjectParameter("vchr_state", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_eduloanapp", int_loanappidParameter, vchr_stunameParameter, vchr_stufathernameParameter, vchr_stumothernameParameter, dte_studobParameter, int_stuageParameter, vchr_casteParameter, sub_CastParameter, vchr_presenthsenameParameter, vchr_presentlane1Parameter, vchr_presentlane2Parameter, vchr_presentpostParameter, int_presentpinParameter, vchr_presentphnoParameter, vchr_permhsenameParameter, vchr_permlane1Parameter, vchr_permlane2Parameter, vchr_permpostParameter, int_permpinParameter, vchr_permphnoParameter, vchr_coursenameParameter, vchr_instnameParameter, vchr_affluniParameter, vchr_affldetailsParameter, vchr_durationParameter, vchr_month_commencementParameter, int_yr_commencementParameter, vchr_month_completionParameter, int_yr_completionParameter, vchr_parent_fullnameParameter, vchr_parent_presenthsenameParameter, vchr_parent_presentlane1Parameter, vchr_parent_presentlane2Parameter, vchr_parent_presentpostParameter, int_parent_presentpinParameter, vchr_parent_presentphnoParameter, vchr_parent_permhsenameParameter, vchr_parent_permlane1Parameter, vchr_parent_permlane2Parameter, vchr_parent_permpostParameter, int_parent_permpinParameter, vchr_parent_permphnoParameter, vchr_parent_relationshipParameter, vchr_employedParameter, vchr_parentfathernameParameter, int_parentageParameter, vchr_sexParameter, vchr_countryParameter, vchr_stateParameter);
        }
    
        public virtual int Update_To_tbl_empsurety(Nullable<int> int_empid, Nullable<decimal> int_loanappid, string vchr_appreceivregno, string vchr_empname, string vchr_fathername, string vchr_spousename, string vchr_empdesig, string vchr_empoffname, string vchr_empofflane1, string vchr_empofflane2, string vchr_empoffpost, Nullable<decimal> vchr_empoffpin, string vchr_empoffphno, string vchr_presenthsename, string vchr_presentlane1, string vchr_presentlane2, string vchr_presentpost, string vchr_presentpin, string vchr_presentphno, string vchr_emppermhsename, string vchr_emppermlane1, string vchr_emppermlane2, string vchr_emppermpost, string vchr_emppermpin, string vchr_emppermphno, Nullable<System.DateTime> dte_dob, Nullable<System.DateTime> dte_eos, Nullable<System.DateTime> dte_empdateofretire, Nullable<decimal> int_basicpay, Nullable<decimal> int_netsal, Nullable<decimal> int_grosssal, string vchr_senior_empname, string vchr_senior_empdes, string vchr_senior_empoffname, string vchr_senior_emplane1, string vchr_senior_emplane2, string vchr_senior_emppost, string vchr_senior_emppin, string vchr_senior_empphone, string vchr_auditno, string vchr_depart, Nullable<bool> int_sr_officer, string vchr_scale, Nullable<System.DateTime> dte_conf_send_date, Nullable<System.DateTime> dte_conf_rec_date, string int_loanno, string vchr_oldno, string vchr_presentVillage, string vchr_presentTaluk, string vchr_presentDistrict, string vchr_PermVillage, string vchr_PermTaluk, string vchr_PermDistrict, string vchr_empoffname1, string vchr_empofflane11, string vchr_empofflane21, string vchr_empoffpost1, Nullable<decimal> vchr_empoffpin1, string vchr_empoffphno1, Nullable<bool> int_loanee, Nullable<bool> int_under, string vchr_Ashwas, string vchr_pen, string vchr_PanNo, Nullable<decimal> vchr_ITAckNo, string vchr_Aadhar, Nullable<decimal> int_personal, Nullable<decimal> int_eligibleloan, string vchr_scaleofpay, string vchr_PensionScheme, string vchr_PRAN, string vchr_PFNo)
        {
            var int_empidParameter = int_empid.HasValue ?
                new ObjectParameter("int_empid", int_empid) :
                new ObjectParameter("int_empid", typeof(int));
    
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var vchr_appreceivregnoParameter = vchr_appreceivregno != null ?
                new ObjectParameter("vchr_appreceivregno", vchr_appreceivregno) :
                new ObjectParameter("vchr_appreceivregno", typeof(string));
    
            var vchr_empnameParameter = vchr_empname != null ?
                new ObjectParameter("vchr_empname", vchr_empname) :
                new ObjectParameter("vchr_empname", typeof(string));
    
            var vchr_fathernameParameter = vchr_fathername != null ?
                new ObjectParameter("vchr_fathername", vchr_fathername) :
                new ObjectParameter("vchr_fathername", typeof(string));
    
            var vchr_spousenameParameter = vchr_spousename != null ?
                new ObjectParameter("vchr_spousename", vchr_spousename) :
                new ObjectParameter("vchr_spousename", typeof(string));
    
            var vchr_empdesigParameter = vchr_empdesig != null ?
                new ObjectParameter("vchr_empdesig", vchr_empdesig) :
                new ObjectParameter("vchr_empdesig", typeof(string));
    
            var vchr_empoffnameParameter = vchr_empoffname != null ?
                new ObjectParameter("vchr_empoffname", vchr_empoffname) :
                new ObjectParameter("vchr_empoffname", typeof(string));
    
            var vchr_empofflane1Parameter = vchr_empofflane1 != null ?
                new ObjectParameter("vchr_empofflane1", vchr_empofflane1) :
                new ObjectParameter("vchr_empofflane1", typeof(string));
    
            var vchr_empofflane2Parameter = vchr_empofflane2 != null ?
                new ObjectParameter("vchr_empofflane2", vchr_empofflane2) :
                new ObjectParameter("vchr_empofflane2", typeof(string));
    
            var vchr_empoffpostParameter = vchr_empoffpost != null ?
                new ObjectParameter("vchr_empoffpost", vchr_empoffpost) :
                new ObjectParameter("vchr_empoffpost", typeof(string));
    
            var vchr_empoffpinParameter = vchr_empoffpin.HasValue ?
                new ObjectParameter("vchr_empoffpin", vchr_empoffpin) :
                new ObjectParameter("vchr_empoffpin", typeof(decimal));
    
            var vchr_empoffphnoParameter = vchr_empoffphno != null ?
                new ObjectParameter("vchr_empoffphno", vchr_empoffphno) :
                new ObjectParameter("vchr_empoffphno", typeof(string));
    
            var vchr_presenthsenameParameter = vchr_presenthsename != null ?
                new ObjectParameter("vchr_presenthsename", vchr_presenthsename) :
                new ObjectParameter("vchr_presenthsename", typeof(string));
    
            var vchr_presentlane1Parameter = vchr_presentlane1 != null ?
                new ObjectParameter("vchr_presentlane1", vchr_presentlane1) :
                new ObjectParameter("vchr_presentlane1", typeof(string));
    
            var vchr_presentlane2Parameter = vchr_presentlane2 != null ?
                new ObjectParameter("vchr_presentlane2", vchr_presentlane2) :
                new ObjectParameter("vchr_presentlane2", typeof(string));
    
            var vchr_presentpostParameter = vchr_presentpost != null ?
                new ObjectParameter("vchr_presentpost", vchr_presentpost) :
                new ObjectParameter("vchr_presentpost", typeof(string));
    
            var vchr_presentpinParameter = vchr_presentpin != null ?
                new ObjectParameter("vchr_presentpin", vchr_presentpin) :
                new ObjectParameter("vchr_presentpin", typeof(string));
    
            var vchr_presentphnoParameter = vchr_presentphno != null ?
                new ObjectParameter("vchr_presentphno", vchr_presentphno) :
                new ObjectParameter("vchr_presentphno", typeof(string));
    
            var vchr_emppermhsenameParameter = vchr_emppermhsename != null ?
                new ObjectParameter("vchr_emppermhsename", vchr_emppermhsename) :
                new ObjectParameter("vchr_emppermhsename", typeof(string));
    
            var vchr_emppermlane1Parameter = vchr_emppermlane1 != null ?
                new ObjectParameter("vchr_emppermlane1", vchr_emppermlane1) :
                new ObjectParameter("vchr_emppermlane1", typeof(string));
    
            var vchr_emppermlane2Parameter = vchr_emppermlane2 != null ?
                new ObjectParameter("vchr_emppermlane2", vchr_emppermlane2) :
                new ObjectParameter("vchr_emppermlane2", typeof(string));
    
            var vchr_emppermpostParameter = vchr_emppermpost != null ?
                new ObjectParameter("vchr_emppermpost", vchr_emppermpost) :
                new ObjectParameter("vchr_emppermpost", typeof(string));
    
            var vchr_emppermpinParameter = vchr_emppermpin != null ?
                new ObjectParameter("vchr_emppermpin", vchr_emppermpin) :
                new ObjectParameter("vchr_emppermpin", typeof(string));
    
            var vchr_emppermphnoParameter = vchr_emppermphno != null ?
                new ObjectParameter("vchr_emppermphno", vchr_emppermphno) :
                new ObjectParameter("vchr_emppermphno", typeof(string));
    
            var dte_dobParameter = dte_dob.HasValue ?
                new ObjectParameter("dte_dob", dte_dob) :
                new ObjectParameter("dte_dob", typeof(System.DateTime));
    
            var dte_eosParameter = dte_eos.HasValue ?
                new ObjectParameter("dte_eos", dte_eos) :
                new ObjectParameter("dte_eos", typeof(System.DateTime));
    
            var dte_empdateofretireParameter = dte_empdateofretire.HasValue ?
                new ObjectParameter("dte_empdateofretire", dte_empdateofretire) :
                new ObjectParameter("dte_empdateofretire", typeof(System.DateTime));
    
            var int_basicpayParameter = int_basicpay.HasValue ?
                new ObjectParameter("int_basicpay", int_basicpay) :
                new ObjectParameter("int_basicpay", typeof(decimal));
    
            var int_netsalParameter = int_netsal.HasValue ?
                new ObjectParameter("int_netsal", int_netsal) :
                new ObjectParameter("int_netsal", typeof(decimal));
    
            var int_grosssalParameter = int_grosssal.HasValue ?
                new ObjectParameter("int_grosssal", int_grosssal) :
                new ObjectParameter("int_grosssal", typeof(decimal));
    
            var vchr_senior_empnameParameter = vchr_senior_empname != null ?
                new ObjectParameter("vchr_senior_empname", vchr_senior_empname) :
                new ObjectParameter("vchr_senior_empname", typeof(string));
    
            var vchr_senior_empdesParameter = vchr_senior_empdes != null ?
                new ObjectParameter("vchr_senior_empdes", vchr_senior_empdes) :
                new ObjectParameter("vchr_senior_empdes", typeof(string));
    
            var vchr_senior_empoffnameParameter = vchr_senior_empoffname != null ?
                new ObjectParameter("vchr_senior_empoffname", vchr_senior_empoffname) :
                new ObjectParameter("vchr_senior_empoffname", typeof(string));
    
            var vchr_senior_emplane1Parameter = vchr_senior_emplane1 != null ?
                new ObjectParameter("vchr_senior_emplane1", vchr_senior_emplane1) :
                new ObjectParameter("vchr_senior_emplane1", typeof(string));
    
            var vchr_senior_emplane2Parameter = vchr_senior_emplane2 != null ?
                new ObjectParameter("vchr_senior_emplane2", vchr_senior_emplane2) :
                new ObjectParameter("vchr_senior_emplane2", typeof(string));
    
            var vchr_senior_emppostParameter = vchr_senior_emppost != null ?
                new ObjectParameter("vchr_senior_emppost", vchr_senior_emppost) :
                new ObjectParameter("vchr_senior_emppost", typeof(string));
    
            var vchr_senior_emppinParameter = vchr_senior_emppin != null ?
                new ObjectParameter("vchr_senior_emppin", vchr_senior_emppin) :
                new ObjectParameter("vchr_senior_emppin", typeof(string));
    
            var vchr_senior_empphoneParameter = vchr_senior_empphone != null ?
                new ObjectParameter("vchr_senior_empphone", vchr_senior_empphone) :
                new ObjectParameter("vchr_senior_empphone", typeof(string));
    
            var vchr_auditnoParameter = vchr_auditno != null ?
                new ObjectParameter("vchr_auditno", vchr_auditno) :
                new ObjectParameter("vchr_auditno", typeof(string));
    
            var vchr_departParameter = vchr_depart != null ?
                new ObjectParameter("vchr_depart", vchr_depart) :
                new ObjectParameter("vchr_depart", typeof(string));
    
            var int_sr_officerParameter = int_sr_officer.HasValue ?
                new ObjectParameter("int_sr_officer", int_sr_officer) :
                new ObjectParameter("int_sr_officer", typeof(bool));
    
            var vchr_scaleParameter = vchr_scale != null ?
                new ObjectParameter("vchr_scale", vchr_scale) :
                new ObjectParameter("vchr_scale", typeof(string));
    
            var dte_conf_send_dateParameter = dte_conf_send_date.HasValue ?
                new ObjectParameter("dte_conf_send_date", dte_conf_send_date) :
                new ObjectParameter("dte_conf_send_date", typeof(System.DateTime));
    
            var dte_conf_rec_dateParameter = dte_conf_rec_date.HasValue ?
                new ObjectParameter("dte_conf_rec_date", dte_conf_rec_date) :
                new ObjectParameter("dte_conf_rec_date", typeof(System.DateTime));
    
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            var vchr_oldnoParameter = vchr_oldno != null ?
                new ObjectParameter("vchr_oldno", vchr_oldno) :
                new ObjectParameter("vchr_oldno", typeof(string));
    
            var vchr_presentVillageParameter = vchr_presentVillage != null ?
                new ObjectParameter("vchr_presentVillage", vchr_presentVillage) :
                new ObjectParameter("vchr_presentVillage", typeof(string));
    
            var vchr_presentTalukParameter = vchr_presentTaluk != null ?
                new ObjectParameter("vchr_presentTaluk", vchr_presentTaluk) :
                new ObjectParameter("vchr_presentTaluk", typeof(string));
    
            var vchr_presentDistrictParameter = vchr_presentDistrict != null ?
                new ObjectParameter("vchr_presentDistrict", vchr_presentDistrict) :
                new ObjectParameter("vchr_presentDistrict", typeof(string));
    
            var vchr_PermVillageParameter = vchr_PermVillage != null ?
                new ObjectParameter("vchr_PermVillage", vchr_PermVillage) :
                new ObjectParameter("vchr_PermVillage", typeof(string));
    
            var vchr_PermTalukParameter = vchr_PermTaluk != null ?
                new ObjectParameter("vchr_PermTaluk", vchr_PermTaluk) :
                new ObjectParameter("vchr_PermTaluk", typeof(string));
    
            var vchr_PermDistrictParameter = vchr_PermDistrict != null ?
                new ObjectParameter("vchr_PermDistrict", vchr_PermDistrict) :
                new ObjectParameter("vchr_PermDistrict", typeof(string));
    
            var vchr_empoffname1Parameter = vchr_empoffname1 != null ?
                new ObjectParameter("vchr_empoffname1", vchr_empoffname1) :
                new ObjectParameter("vchr_empoffname1", typeof(string));
    
            var vchr_empofflane11Parameter = vchr_empofflane11 != null ?
                new ObjectParameter("vchr_empofflane11", vchr_empofflane11) :
                new ObjectParameter("vchr_empofflane11", typeof(string));
    
            var vchr_empofflane21Parameter = vchr_empofflane21 != null ?
                new ObjectParameter("vchr_empofflane21", vchr_empofflane21) :
                new ObjectParameter("vchr_empofflane21", typeof(string));
    
            var vchr_empoffpost1Parameter = vchr_empoffpost1 != null ?
                new ObjectParameter("vchr_empoffpost1", vchr_empoffpost1) :
                new ObjectParameter("vchr_empoffpost1", typeof(string));
    
            var vchr_empoffpin1Parameter = vchr_empoffpin1.HasValue ?
                new ObjectParameter("vchr_empoffpin1", vchr_empoffpin1) :
                new ObjectParameter("vchr_empoffpin1", typeof(decimal));
    
            var vchr_empoffphno1Parameter = vchr_empoffphno1 != null ?
                new ObjectParameter("vchr_empoffphno1", vchr_empoffphno1) :
                new ObjectParameter("vchr_empoffphno1", typeof(string));
    
            var int_loaneeParameter = int_loanee.HasValue ?
                new ObjectParameter("int_loanee", int_loanee) :
                new ObjectParameter("int_loanee", typeof(bool));
    
            var int_underParameter = int_under.HasValue ?
                new ObjectParameter("int_under", int_under) :
                new ObjectParameter("int_under", typeof(bool));
    
            var vchr_AshwasParameter = vchr_Ashwas != null ?
                new ObjectParameter("vchr_Ashwas", vchr_Ashwas) :
                new ObjectParameter("vchr_Ashwas", typeof(string));
    
            var vchr_penParameter = vchr_pen != null ?
                new ObjectParameter("vchr_pen", vchr_pen) :
                new ObjectParameter("vchr_pen", typeof(string));
    
            var vchr_PanNoParameter = vchr_PanNo != null ?
                new ObjectParameter("vchr_PanNo", vchr_PanNo) :
                new ObjectParameter("vchr_PanNo", typeof(string));
    
            var vchr_ITAckNoParameter = vchr_ITAckNo.HasValue ?
                new ObjectParameter("vchr_ITAckNo", vchr_ITAckNo) :
                new ObjectParameter("vchr_ITAckNo", typeof(decimal));
    
            var vchr_AadharParameter = vchr_Aadhar != null ?
                new ObjectParameter("vchr_Aadhar", vchr_Aadhar) :
                new ObjectParameter("vchr_Aadhar", typeof(string));
    
            var int_personalParameter = int_personal.HasValue ?
                new ObjectParameter("int_personal", int_personal) :
                new ObjectParameter("int_personal", typeof(decimal));
    
            var int_eligibleloanParameter = int_eligibleloan.HasValue ?
                new ObjectParameter("int_eligibleloan", int_eligibleloan) :
                new ObjectParameter("int_eligibleloan", typeof(decimal));
    
            var vchr_scaleofpayParameter = vchr_scaleofpay != null ?
                new ObjectParameter("vchr_scaleofpay", vchr_scaleofpay) :
                new ObjectParameter("vchr_scaleofpay", typeof(string));
    
            var vchr_PensionSchemeParameter = vchr_PensionScheme != null ?
                new ObjectParameter("vchr_PensionScheme", vchr_PensionScheme) :
                new ObjectParameter("vchr_PensionScheme", typeof(string));
    
            var vchr_PRANParameter = vchr_PRAN != null ?
                new ObjectParameter("vchr_PRAN", vchr_PRAN) :
                new ObjectParameter("vchr_PRAN", typeof(string));
    
            var vchr_PFNoParameter = vchr_PFNo != null ?
                new ObjectParameter("vchr_PFNo", vchr_PFNo) :
                new ObjectParameter("vchr_PFNo", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_empsurety", int_empidParameter, int_loanappidParameter, vchr_appreceivregnoParameter, vchr_empnameParameter, vchr_fathernameParameter, vchr_spousenameParameter, vchr_empdesigParameter, vchr_empoffnameParameter, vchr_empofflane1Parameter, vchr_empofflane2Parameter, vchr_empoffpostParameter, vchr_empoffpinParameter, vchr_empoffphnoParameter, vchr_presenthsenameParameter, vchr_presentlane1Parameter, vchr_presentlane2Parameter, vchr_presentpostParameter, vchr_presentpinParameter, vchr_presentphnoParameter, vchr_emppermhsenameParameter, vchr_emppermlane1Parameter, vchr_emppermlane2Parameter, vchr_emppermpostParameter, vchr_emppermpinParameter, vchr_emppermphnoParameter, dte_dobParameter, dte_eosParameter, dte_empdateofretireParameter, int_basicpayParameter, int_netsalParameter, int_grosssalParameter, vchr_senior_empnameParameter, vchr_senior_empdesParameter, vchr_senior_empoffnameParameter, vchr_senior_emplane1Parameter, vchr_senior_emplane2Parameter, vchr_senior_emppostParameter, vchr_senior_emppinParameter, vchr_senior_empphoneParameter, vchr_auditnoParameter, vchr_departParameter, int_sr_officerParameter, vchr_scaleParameter, dte_conf_send_dateParameter, dte_conf_rec_dateParameter, int_loannoParameter, vchr_oldnoParameter, vchr_presentVillageParameter, vchr_presentTalukParameter, vchr_presentDistrictParameter, vchr_PermVillageParameter, vchr_PermTalukParameter, vchr_PermDistrictParameter, vchr_empoffname1Parameter, vchr_empofflane11Parameter, vchr_empofflane21Parameter, vchr_empoffpost1Parameter, vchr_empoffpin1Parameter, vchr_empoffphno1Parameter, int_loaneeParameter, int_underParameter, vchr_AshwasParameter, vchr_penParameter, vchr_PanNoParameter, vchr_ITAckNoParameter, vchr_AadharParameter, int_personalParameter, int_eligibleloanParameter, vchr_scaleofpayParameter, vchr_PensionSchemeParameter, vchr_PRANParameter, vchr_PFNoParameter);
        }
    
        public virtual int Update_To_tbl_FDLICsurety(Nullable<int> int_licid, Nullable<decimal> int_loanappid, string vchr_apprecregno, string vchr_holdername, string vchr_nomineename, string vchr_hsename, string vchr_lane1, string vchr_lane2, string vchr_post, Nullable<decimal> int_pin, string vchr_phno, string vchr_village, string vchr_taluk, string vchr_district, string vchr_fdpolnum, string vchr_fdpoldetails, Nullable<System.DateTime> dt_fdpoldate, Nullable<System.DateTime> dt_mature_date, Nullable<System.DateTime> dte_conf_send_date, Nullable<System.DateTime> dte_conf_rec_date, string int_loanno, Nullable<decimal> int_MatureAmt, string vchr_type, Nullable<decimal> int_Amount)
        {
            var int_licidParameter = int_licid.HasValue ?
                new ObjectParameter("int_licid", int_licid) :
                new ObjectParameter("int_licid", typeof(int));
    
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var vchr_apprecregnoParameter = vchr_apprecregno != null ?
                new ObjectParameter("vchr_apprecregno", vchr_apprecregno) :
                new ObjectParameter("vchr_apprecregno", typeof(string));
    
            var vchr_holdernameParameter = vchr_holdername != null ?
                new ObjectParameter("vchr_holdername", vchr_holdername) :
                new ObjectParameter("vchr_holdername", typeof(string));
    
            var vchr_nomineenameParameter = vchr_nomineename != null ?
                new ObjectParameter("vchr_nomineename", vchr_nomineename) :
                new ObjectParameter("vchr_nomineename", typeof(string));
    
            var vchr_hsenameParameter = vchr_hsename != null ?
                new ObjectParameter("vchr_hsename", vchr_hsename) :
                new ObjectParameter("vchr_hsename", typeof(string));
    
            var vchr_lane1Parameter = vchr_lane1 != null ?
                new ObjectParameter("vchr_lane1", vchr_lane1) :
                new ObjectParameter("vchr_lane1", typeof(string));
    
            var vchr_lane2Parameter = vchr_lane2 != null ?
                new ObjectParameter("vchr_lane2", vchr_lane2) :
                new ObjectParameter("vchr_lane2", typeof(string));
    
            var vchr_postParameter = vchr_post != null ?
                new ObjectParameter("vchr_post", vchr_post) :
                new ObjectParameter("vchr_post", typeof(string));
    
            var int_pinParameter = int_pin.HasValue ?
                new ObjectParameter("int_pin", int_pin) :
                new ObjectParameter("int_pin", typeof(decimal));
    
            var vchr_phnoParameter = vchr_phno != null ?
                new ObjectParameter("vchr_phno", vchr_phno) :
                new ObjectParameter("vchr_phno", typeof(string));
    
            var vchr_villageParameter = vchr_village != null ?
                new ObjectParameter("vchr_village", vchr_village) :
                new ObjectParameter("vchr_village", typeof(string));
    
            var vchr_talukParameter = vchr_taluk != null ?
                new ObjectParameter("vchr_taluk", vchr_taluk) :
                new ObjectParameter("vchr_taluk", typeof(string));
    
            var vchr_districtParameter = vchr_district != null ?
                new ObjectParameter("vchr_district", vchr_district) :
                new ObjectParameter("vchr_district", typeof(string));
    
            var vchr_fdpolnumParameter = vchr_fdpolnum != null ?
                new ObjectParameter("vchr_fdpolnum", vchr_fdpolnum) :
                new ObjectParameter("vchr_fdpolnum", typeof(string));
    
            var vchr_fdpoldetailsParameter = vchr_fdpoldetails != null ?
                new ObjectParameter("vchr_fdpoldetails", vchr_fdpoldetails) :
                new ObjectParameter("vchr_fdpoldetails", typeof(string));
    
            var dt_fdpoldateParameter = dt_fdpoldate.HasValue ?
                new ObjectParameter("dt_fdpoldate", dt_fdpoldate) :
                new ObjectParameter("dt_fdpoldate", typeof(System.DateTime));
    
            var dt_mature_dateParameter = dt_mature_date.HasValue ?
                new ObjectParameter("dt_mature_date", dt_mature_date) :
                new ObjectParameter("dt_mature_date", typeof(System.DateTime));
    
            var dte_conf_send_dateParameter = dte_conf_send_date.HasValue ?
                new ObjectParameter("dte_conf_send_date", dte_conf_send_date) :
                new ObjectParameter("dte_conf_send_date", typeof(System.DateTime));
    
            var dte_conf_rec_dateParameter = dte_conf_rec_date.HasValue ?
                new ObjectParameter("dte_conf_rec_date", dte_conf_rec_date) :
                new ObjectParameter("dte_conf_rec_date", typeof(System.DateTime));
    
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            var int_MatureAmtParameter = int_MatureAmt.HasValue ?
                new ObjectParameter("int_MatureAmt", int_MatureAmt) :
                new ObjectParameter("int_MatureAmt", typeof(decimal));
    
            var vchr_typeParameter = vchr_type != null ?
                new ObjectParameter("vchr_type", vchr_type) :
                new ObjectParameter("vchr_type", typeof(string));
    
            var int_AmountParameter = int_Amount.HasValue ?
                new ObjectParameter("int_Amount", int_Amount) :
                new ObjectParameter("int_Amount", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_FDLICsurety", int_licidParameter, int_loanappidParameter, vchr_apprecregnoParameter, vchr_holdernameParameter, vchr_nomineenameParameter, vchr_hsenameParameter, vchr_lane1Parameter, vchr_lane2Parameter, vchr_postParameter, int_pinParameter, vchr_phnoParameter, vchr_villageParameter, vchr_talukParameter, vchr_districtParameter, vchr_fdpolnumParameter, vchr_fdpoldetailsParameter, dt_fdpoldateParameter, dt_mature_dateParameter, dte_conf_send_dateParameter, dte_conf_rec_dateParameter, int_loannoParameter, int_MatureAmtParameter, vchr_typeParameter, int_AmountParameter);
        }
    
        public virtual int Update_To_tbl_foreignloan(Nullable<decimal> int_loanappid, Nullable<int> int_schemeid, string vchr_passportno, Nullable<System.DateTime> dte_duration, string vchr_forgn_orgname, string vchr_forgn_orgaddr, string vchr_visadetails)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var int_schemeidParameter = int_schemeid.HasValue ?
                new ObjectParameter("int_schemeid", int_schemeid) :
                new ObjectParameter("int_schemeid", typeof(int));
    
            var vchr_passportnoParameter = vchr_passportno != null ?
                new ObjectParameter("vchr_passportno", vchr_passportno) :
                new ObjectParameter("vchr_passportno", typeof(string));
    
            var dte_durationParameter = dte_duration.HasValue ?
                new ObjectParameter("dte_duration", dte_duration) :
                new ObjectParameter("dte_duration", typeof(System.DateTime));
    
            var vchr_forgn_orgnameParameter = vchr_forgn_orgname != null ?
                new ObjectParameter("vchr_forgn_orgname", vchr_forgn_orgname) :
                new ObjectParameter("vchr_forgn_orgname", typeof(string));
    
            var vchr_forgn_orgaddrParameter = vchr_forgn_orgaddr != null ?
                new ObjectParameter("vchr_forgn_orgaddr", vchr_forgn_orgaddr) :
                new ObjectParameter("vchr_forgn_orgaddr", typeof(string));
    
            var vchr_visadetailsParameter = vchr_visadetails != null ?
                new ObjectParameter("vchr_visadetails", vchr_visadetails) :
                new ObjectParameter("vchr_visadetails", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_foreignloan", int_loanappidParameter, int_schemeidParameter, vchr_passportnoParameter, dte_durationParameter, vchr_forgn_orgnameParameter, vchr_forgn_orgaddrParameter, vchr_visadetailsParameter);
        }
    
        public virtual int Update_To_tbl_House_Loan(Nullable<decimal> int_loanappid, string vchr_coapp_name, string vchr_Father, string vchr_Mother, string vchr_Spouse, string vchr_Relation, Nullable<System.DateTime> dte_dob, Nullable<decimal> int_age, string vchr_Aadhaar, string vchr_electionid, string vchr_ration, Nullable<decimal> int_income_salary, Nullable<decimal> int_income_agr, Nullable<decimal> int_income_other, Nullable<decimal> int_income_total, string vchr_ownername, string vchr_extent, string vchr_surveyno, string vchr_Blockno, string vchr_TPno, string vchr_land_village, string vchr_land_taluk, string vchr_land_district, string vchr_deedtype, string vchr_deedno, string vchr_subregoffiname, string vchr_base_area, Nullable<decimal> int_no_floor, string vchr_carpet_area, string vchr_construction_type, Nullable<decimal> int_cost, string vchr_permit_no, Nullable<System.DateTime> dte_permit_date, string vchr_validity, Nullable<decimal> int_concession, string vchr_details, Nullable<decimal> int_concessionamt, Nullable<decimal> int_total_cost, Nullable<decimal> int_cash_hand, Nullable<decimal> int_subsidy, Nullable<decimal> int_loan_req, Nullable<decimal> int_repay_year)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var vchr_coapp_nameParameter = vchr_coapp_name != null ?
                new ObjectParameter("vchr_coapp_name", vchr_coapp_name) :
                new ObjectParameter("vchr_coapp_name", typeof(string));
    
            var vchr_FatherParameter = vchr_Father != null ?
                new ObjectParameter("vchr_Father", vchr_Father) :
                new ObjectParameter("vchr_Father", typeof(string));
    
            var vchr_MotherParameter = vchr_Mother != null ?
                new ObjectParameter("vchr_Mother", vchr_Mother) :
                new ObjectParameter("vchr_Mother", typeof(string));
    
            var vchr_SpouseParameter = vchr_Spouse != null ?
                new ObjectParameter("vchr_Spouse", vchr_Spouse) :
                new ObjectParameter("vchr_Spouse", typeof(string));
    
            var vchr_RelationParameter = vchr_Relation != null ?
                new ObjectParameter("vchr_Relation", vchr_Relation) :
                new ObjectParameter("vchr_Relation", typeof(string));
    
            var dte_dobParameter = dte_dob.HasValue ?
                new ObjectParameter("dte_dob", dte_dob) :
                new ObjectParameter("dte_dob", typeof(System.DateTime));
    
            var int_ageParameter = int_age.HasValue ?
                new ObjectParameter("int_age", int_age) :
                new ObjectParameter("int_age", typeof(decimal));
    
            var vchr_AadhaarParameter = vchr_Aadhaar != null ?
                new ObjectParameter("vchr_Aadhaar", vchr_Aadhaar) :
                new ObjectParameter("vchr_Aadhaar", typeof(string));
    
            var vchr_electionidParameter = vchr_electionid != null ?
                new ObjectParameter("vchr_electionid", vchr_electionid) :
                new ObjectParameter("vchr_electionid", typeof(string));
    
            var vchr_rationParameter = vchr_ration != null ?
                new ObjectParameter("vchr_ration", vchr_ration) :
                new ObjectParameter("vchr_ration", typeof(string));
    
            var int_income_salaryParameter = int_income_salary.HasValue ?
                new ObjectParameter("int_income_salary", int_income_salary) :
                new ObjectParameter("int_income_salary", typeof(decimal));
    
            var int_income_agrParameter = int_income_agr.HasValue ?
                new ObjectParameter("int_income_agr", int_income_agr) :
                new ObjectParameter("int_income_agr", typeof(decimal));
    
            var int_income_otherParameter = int_income_other.HasValue ?
                new ObjectParameter("int_income_other", int_income_other) :
                new ObjectParameter("int_income_other", typeof(decimal));
    
            var int_income_totalParameter = int_income_total.HasValue ?
                new ObjectParameter("int_income_total", int_income_total) :
                new ObjectParameter("int_income_total", typeof(decimal));
    
            var vchr_ownernameParameter = vchr_ownername != null ?
                new ObjectParameter("vchr_ownername", vchr_ownername) :
                new ObjectParameter("vchr_ownername", typeof(string));
    
            var vchr_extentParameter = vchr_extent != null ?
                new ObjectParameter("vchr_extent", vchr_extent) :
                new ObjectParameter("vchr_extent", typeof(string));
    
            var vchr_surveynoParameter = vchr_surveyno != null ?
                new ObjectParameter("vchr_surveyno", vchr_surveyno) :
                new ObjectParameter("vchr_surveyno", typeof(string));
    
            var vchr_BlocknoParameter = vchr_Blockno != null ?
                new ObjectParameter("vchr_Blockno", vchr_Blockno) :
                new ObjectParameter("vchr_Blockno", typeof(string));
    
            var vchr_TPnoParameter = vchr_TPno != null ?
                new ObjectParameter("vchr_TPno", vchr_TPno) :
                new ObjectParameter("vchr_TPno", typeof(string));
    
            var vchr_land_villageParameter = vchr_land_village != null ?
                new ObjectParameter("vchr_land_village", vchr_land_village) :
                new ObjectParameter("vchr_land_village", typeof(string));
    
            var vchr_land_talukParameter = vchr_land_taluk != null ?
                new ObjectParameter("vchr_land_taluk", vchr_land_taluk) :
                new ObjectParameter("vchr_land_taluk", typeof(string));
    
            var vchr_land_districtParameter = vchr_land_district != null ?
                new ObjectParameter("vchr_land_district", vchr_land_district) :
                new ObjectParameter("vchr_land_district", typeof(string));
    
            var vchr_deedtypeParameter = vchr_deedtype != null ?
                new ObjectParameter("vchr_deedtype", vchr_deedtype) :
                new ObjectParameter("vchr_deedtype", typeof(string));
    
            var vchr_deednoParameter = vchr_deedno != null ?
                new ObjectParameter("vchr_deedno", vchr_deedno) :
                new ObjectParameter("vchr_deedno", typeof(string));
    
            var vchr_subregoffinameParameter = vchr_subregoffiname != null ?
                new ObjectParameter("vchr_subregoffiname", vchr_subregoffiname) :
                new ObjectParameter("vchr_subregoffiname", typeof(string));
    
            var vchr_base_areaParameter = vchr_base_area != null ?
                new ObjectParameter("vchr_base_area", vchr_base_area) :
                new ObjectParameter("vchr_base_area", typeof(string));
    
            var int_no_floorParameter = int_no_floor.HasValue ?
                new ObjectParameter("int_no_floor", int_no_floor) :
                new ObjectParameter("int_no_floor", typeof(decimal));
    
            var vchr_carpet_areaParameter = vchr_carpet_area != null ?
                new ObjectParameter("vchr_carpet_area", vchr_carpet_area) :
                new ObjectParameter("vchr_carpet_area", typeof(string));
    
            var vchr_construction_typeParameter = vchr_construction_type != null ?
                new ObjectParameter("vchr_construction_type", vchr_construction_type) :
                new ObjectParameter("vchr_construction_type", typeof(string));
    
            var int_costParameter = int_cost.HasValue ?
                new ObjectParameter("int_cost", int_cost) :
                new ObjectParameter("int_cost", typeof(decimal));
    
            var vchr_permit_noParameter = vchr_permit_no != null ?
                new ObjectParameter("vchr_permit_no", vchr_permit_no) :
                new ObjectParameter("vchr_permit_no", typeof(string));
    
            var dte_permit_dateParameter = dte_permit_date.HasValue ?
                new ObjectParameter("dte_permit_date", dte_permit_date) :
                new ObjectParameter("dte_permit_date", typeof(System.DateTime));
    
            var vchr_validityParameter = vchr_validity != null ?
                new ObjectParameter("vchr_validity", vchr_validity) :
                new ObjectParameter("vchr_validity", typeof(string));
    
            var int_concessionParameter = int_concession.HasValue ?
                new ObjectParameter("int_concession", int_concession) :
                new ObjectParameter("int_concession", typeof(decimal));
    
            var vchr_detailsParameter = vchr_details != null ?
                new ObjectParameter("vchr_details", vchr_details) :
                new ObjectParameter("vchr_details", typeof(string));
    
            var int_concessionamtParameter = int_concessionamt.HasValue ?
                new ObjectParameter("int_concessionamt", int_concessionamt) :
                new ObjectParameter("int_concessionamt", typeof(decimal));
    
            var int_total_costParameter = int_total_cost.HasValue ?
                new ObjectParameter("int_total_cost", int_total_cost) :
                new ObjectParameter("int_total_cost", typeof(decimal));
    
            var int_cash_handParameter = int_cash_hand.HasValue ?
                new ObjectParameter("int_cash_hand", int_cash_hand) :
                new ObjectParameter("int_cash_hand", typeof(decimal));
    
            var int_subsidyParameter = int_subsidy.HasValue ?
                new ObjectParameter("int_subsidy", int_subsidy) :
                new ObjectParameter("int_subsidy", typeof(decimal));
    
            var int_loan_reqParameter = int_loan_req.HasValue ?
                new ObjectParameter("int_loan_req", int_loan_req) :
                new ObjectParameter("int_loan_req", typeof(decimal));
    
            var int_repay_yearParameter = int_repay_year.HasValue ?
                new ObjectParameter("int_repay_year", int_repay_year) :
                new ObjectParameter("int_repay_year", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_House_Loan", int_loanappidParameter, vchr_coapp_nameParameter, vchr_FatherParameter, vchr_MotherParameter, vchr_SpouseParameter, vchr_RelationParameter, dte_dobParameter, int_ageParameter, vchr_AadhaarParameter, vchr_electionidParameter, vchr_rationParameter, int_income_salaryParameter, int_income_agrParameter, int_income_otherParameter, int_income_totalParameter, vchr_ownernameParameter, vchr_extentParameter, vchr_surveynoParameter, vchr_BlocknoParameter, vchr_TPnoParameter, vchr_land_villageParameter, vchr_land_talukParameter, vchr_land_districtParameter, vchr_deedtypeParameter, vchr_deednoParameter, vchr_subregoffinameParameter, vchr_base_areaParameter, int_no_floorParameter, vchr_carpet_areaParameter, vchr_construction_typeParameter, int_costParameter, vchr_permit_noParameter, dte_permit_dateParameter, vchr_validityParameter, int_concessionParameter, vchr_detailsParameter, int_concessionamtParameter, int_total_costParameter, int_cash_handParameter, int_subsidyParameter, int_loan_reqParameter, int_repay_yearParameter);
        }
    
        public virtual int Update_To_tbl_hsemaintanenceloan(Nullable<decimal> int_loanappid, Nullable<int> int_schemeid, string vchr_hse_workorgname, string vchr_hse_workorgaddr, string vchr_hse_designation, string vchr_hse_scaleofpay, Nullable<decimal> int_hse_netsal, string vchr_hseno, string vchr_surveyno, string vchr_propdetails, string vchr_hse_relation, Nullable<decimal> int_estamt, Nullable<decimal> int_amtreq, Nullable<decimal> int_gross)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var int_schemeidParameter = int_schemeid.HasValue ?
                new ObjectParameter("int_schemeid", int_schemeid) :
                new ObjectParameter("int_schemeid", typeof(int));
    
            var vchr_hse_workorgnameParameter = vchr_hse_workorgname != null ?
                new ObjectParameter("vchr_hse_workorgname", vchr_hse_workorgname) :
                new ObjectParameter("vchr_hse_workorgname", typeof(string));
    
            var vchr_hse_workorgaddrParameter = vchr_hse_workorgaddr != null ?
                new ObjectParameter("vchr_hse_workorgaddr", vchr_hse_workorgaddr) :
                new ObjectParameter("vchr_hse_workorgaddr", typeof(string));
    
            var vchr_hse_designationParameter = vchr_hse_designation != null ?
                new ObjectParameter("vchr_hse_designation", vchr_hse_designation) :
                new ObjectParameter("vchr_hse_designation", typeof(string));
    
            var vchr_hse_scaleofpayParameter = vchr_hse_scaleofpay != null ?
                new ObjectParameter("vchr_hse_scaleofpay", vchr_hse_scaleofpay) :
                new ObjectParameter("vchr_hse_scaleofpay", typeof(string));
    
            var int_hse_netsalParameter = int_hse_netsal.HasValue ?
                new ObjectParameter("int_hse_netsal", int_hse_netsal) :
                new ObjectParameter("int_hse_netsal", typeof(decimal));
    
            var vchr_hsenoParameter = vchr_hseno != null ?
                new ObjectParameter("vchr_hseno", vchr_hseno) :
                new ObjectParameter("vchr_hseno", typeof(string));
    
            var vchr_surveynoParameter = vchr_surveyno != null ?
                new ObjectParameter("vchr_surveyno", vchr_surveyno) :
                new ObjectParameter("vchr_surveyno", typeof(string));
    
            var vchr_propdetailsParameter = vchr_propdetails != null ?
                new ObjectParameter("vchr_propdetails", vchr_propdetails) :
                new ObjectParameter("vchr_propdetails", typeof(string));
    
            var vchr_hse_relationParameter = vchr_hse_relation != null ?
                new ObjectParameter("vchr_hse_relation", vchr_hse_relation) :
                new ObjectParameter("vchr_hse_relation", typeof(string));
    
            var int_estamtParameter = int_estamt.HasValue ?
                new ObjectParameter("int_estamt", int_estamt) :
                new ObjectParameter("int_estamt", typeof(decimal));
    
            var int_amtreqParameter = int_amtreq.HasValue ?
                new ObjectParameter("int_amtreq", int_amtreq) :
                new ObjectParameter("int_amtreq", typeof(decimal));
    
            var int_grossParameter = int_gross.HasValue ?
                new ObjectParameter("int_gross", int_gross) :
                new ObjectParameter("int_gross", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_hsemaintanenceloan", int_loanappidParameter, int_schemeidParameter, vchr_hse_workorgnameParameter, vchr_hse_workorgaddrParameter, vchr_hse_designationParameter, vchr_hse_scaleofpayParameter, int_hse_netsalParameter, vchr_hsenoParameter, vchr_surveynoParameter, vchr_propdetailsParameter, vchr_hse_relationParameter, int_estamtParameter, int_amtreqParameter, int_grossParameter);
        }
    
        public virtual int Update_To_tbl_KSDC_SMART_Issues(Nullable<int> issue_Id, string issue_Title, string issue_Desc, string status)
        {
            var issue_IdParameter = issue_Id.HasValue ?
                new ObjectParameter("Issue_Id", issue_Id) :
                new ObjectParameter("Issue_Id", typeof(int));
    
            var issue_TitleParameter = issue_Title != null ?
                new ObjectParameter("Issue_Title", issue_Title) :
                new ObjectParameter("Issue_Title", typeof(string));
    
            var issue_DescParameter = issue_Desc != null ?
                new ObjectParameter("Issue_Desc", issue_Desc) :
                new ObjectParameter("Issue_Desc", typeof(string));
    
            var statusParameter = status != null ?
                new ObjectParameter("Status", status) :
                new ObjectParameter("Status", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_KSDC_SMART_Issues", issue_IdParameter, issue_TitleParameter, issue_DescParameter, statusParameter);
        }
    
        public virtual int Update_To_tbl_landsurety(Nullable<int> int_landid, Nullable<decimal> int_loanappid, string vchr_apprecregno, string vchr_ownername, string vchr_hsename, string vchr_lane1, string vchr_lane2, string vchr_post, Nullable<decimal> int_pin, string vchr_phno, string vchr_deedtype, string vchr_deedno, string vchr_subregoffiname, string vchr_surveyno, string vchr_extent, string vchr_tpno, string vchr_village, string vchr_taluk, string vchr_district, string vchr_land_village, string vchr_land_taluk, string vchr_land_district, Nullable<System.DateTime> dte_conf_send_date, Nullable<System.DateTime> dte_conf_rec_date, string int_loanno, string vchr_fathername, string vchr_documentno, Nullable<decimal> int_ValAmt, string vchr_Ashwas, Nullable<decimal> int_LoaneeH, Nullable<bool> int_fair, Nullable<decimal> int_NotPledged)
        {
            var int_landidParameter = int_landid.HasValue ?
                new ObjectParameter("int_landid", int_landid) :
                new ObjectParameter("int_landid", typeof(int));
    
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var vchr_apprecregnoParameter = vchr_apprecregno != null ?
                new ObjectParameter("vchr_apprecregno", vchr_apprecregno) :
                new ObjectParameter("vchr_apprecregno", typeof(string));
    
            var vchr_ownernameParameter = vchr_ownername != null ?
                new ObjectParameter("vchr_ownername", vchr_ownername) :
                new ObjectParameter("vchr_ownername", typeof(string));
    
            var vchr_hsenameParameter = vchr_hsename != null ?
                new ObjectParameter("vchr_hsename", vchr_hsename) :
                new ObjectParameter("vchr_hsename", typeof(string));
    
            var vchr_lane1Parameter = vchr_lane1 != null ?
                new ObjectParameter("vchr_lane1", vchr_lane1) :
                new ObjectParameter("vchr_lane1", typeof(string));
    
            var vchr_lane2Parameter = vchr_lane2 != null ?
                new ObjectParameter("vchr_lane2", vchr_lane2) :
                new ObjectParameter("vchr_lane2", typeof(string));
    
            var vchr_postParameter = vchr_post != null ?
                new ObjectParameter("vchr_post", vchr_post) :
                new ObjectParameter("vchr_post", typeof(string));
    
            var int_pinParameter = int_pin.HasValue ?
                new ObjectParameter("int_pin", int_pin) :
                new ObjectParameter("int_pin", typeof(decimal));
    
            var vchr_phnoParameter = vchr_phno != null ?
                new ObjectParameter("vchr_phno", vchr_phno) :
                new ObjectParameter("vchr_phno", typeof(string));
    
            var vchr_deedtypeParameter = vchr_deedtype != null ?
                new ObjectParameter("vchr_deedtype", vchr_deedtype) :
                new ObjectParameter("vchr_deedtype", typeof(string));
    
            var vchr_deednoParameter = vchr_deedno != null ?
                new ObjectParameter("vchr_deedno", vchr_deedno) :
                new ObjectParameter("vchr_deedno", typeof(string));
    
            var vchr_subregoffinameParameter = vchr_subregoffiname != null ?
                new ObjectParameter("vchr_subregoffiname", vchr_subregoffiname) :
                new ObjectParameter("vchr_subregoffiname", typeof(string));
    
            var vchr_surveynoParameter = vchr_surveyno != null ?
                new ObjectParameter("vchr_surveyno", vchr_surveyno) :
                new ObjectParameter("vchr_surveyno", typeof(string));
    
            var vchr_extentParameter = vchr_extent != null ?
                new ObjectParameter("vchr_extent", vchr_extent) :
                new ObjectParameter("vchr_extent", typeof(string));
    
            var vchr_tpnoParameter = vchr_tpno != null ?
                new ObjectParameter("vchr_tpno", vchr_tpno) :
                new ObjectParameter("vchr_tpno", typeof(string));
    
            var vchr_villageParameter = vchr_village != null ?
                new ObjectParameter("vchr_village", vchr_village) :
                new ObjectParameter("vchr_village", typeof(string));
    
            var vchr_talukParameter = vchr_taluk != null ?
                new ObjectParameter("vchr_taluk", vchr_taluk) :
                new ObjectParameter("vchr_taluk", typeof(string));
    
            var vchr_districtParameter = vchr_district != null ?
                new ObjectParameter("vchr_district", vchr_district) :
                new ObjectParameter("vchr_district", typeof(string));
    
            var vchr_land_villageParameter = vchr_land_village != null ?
                new ObjectParameter("vchr_land_village", vchr_land_village) :
                new ObjectParameter("vchr_land_village", typeof(string));
    
            var vchr_land_talukParameter = vchr_land_taluk != null ?
                new ObjectParameter("vchr_land_taluk", vchr_land_taluk) :
                new ObjectParameter("vchr_land_taluk", typeof(string));
    
            var vchr_land_districtParameter = vchr_land_district != null ?
                new ObjectParameter("vchr_land_district", vchr_land_district) :
                new ObjectParameter("vchr_land_district", typeof(string));
    
            var dte_conf_send_dateParameter = dte_conf_send_date.HasValue ?
                new ObjectParameter("dte_conf_send_date", dte_conf_send_date) :
                new ObjectParameter("dte_conf_send_date", typeof(System.DateTime));
    
            var dte_conf_rec_dateParameter = dte_conf_rec_date.HasValue ?
                new ObjectParameter("dte_conf_rec_date", dte_conf_rec_date) :
                new ObjectParameter("dte_conf_rec_date", typeof(System.DateTime));
    
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            var vchr_fathernameParameter = vchr_fathername != null ?
                new ObjectParameter("vchr_fathername", vchr_fathername) :
                new ObjectParameter("vchr_fathername", typeof(string));
    
            var vchr_documentnoParameter = vchr_documentno != null ?
                new ObjectParameter("vchr_documentno", vchr_documentno) :
                new ObjectParameter("vchr_documentno", typeof(string));
    
            var int_ValAmtParameter = int_ValAmt.HasValue ?
                new ObjectParameter("int_ValAmt", int_ValAmt) :
                new ObjectParameter("int_ValAmt", typeof(decimal));
    
            var vchr_AshwasParameter = vchr_Ashwas != null ?
                new ObjectParameter("vchr_Ashwas", vchr_Ashwas) :
                new ObjectParameter("vchr_Ashwas", typeof(string));
    
            var int_LoaneeHParameter = int_LoaneeH.HasValue ?
                new ObjectParameter("int_LoaneeH", int_LoaneeH) :
                new ObjectParameter("int_LoaneeH", typeof(decimal));
    
            var int_fairParameter = int_fair.HasValue ?
                new ObjectParameter("int_fair", int_fair) :
                new ObjectParameter("int_fair", typeof(bool));
    
            var int_NotPledgedParameter = int_NotPledged.HasValue ?
                new ObjectParameter("int_NotPledged", int_NotPledged) :
                new ObjectParameter("int_NotPledged", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_landsurety", int_landidParameter, int_loanappidParameter, vchr_apprecregnoParameter, vchr_ownernameParameter, vchr_hsenameParameter, vchr_lane1Parameter, vchr_lane2Parameter, vchr_postParameter, int_pinParameter, vchr_phnoParameter, vchr_deedtypeParameter, vchr_deednoParameter, vchr_subregoffinameParameter, vchr_surveynoParameter, vchr_extentParameter, vchr_tpnoParameter, vchr_villageParameter, vchr_talukParameter, vchr_districtParameter, vchr_land_villageParameter, vchr_land_talukParameter, vchr_land_districtParameter, dte_conf_send_dateParameter, dte_conf_rec_dateParameter, int_loannoParameter, vchr_fathernameParameter, vchr_documentnoParameter, int_ValAmtParameter, vchr_AshwasParameter, int_LoaneeHParameter, int_fairParameter, int_NotPledgedParameter);
        }
    
        public virtual int Update_To_tbl_Loan_App_Issue(Nullable<int> id, Nullable<int> district_Id, Nullable<int> subDistrict_id, string name_Applicant, string address_Applicant, string aadharNumber, Nullable<int> scheme_Id, Nullable<int> cast_Id, Nullable<int> sub_Cast_Id, Nullable<int> updated_By, string remarks, string phoneNo)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            var district_IdParameter = district_Id.HasValue ?
                new ObjectParameter("District_Id", district_Id) :
                new ObjectParameter("District_Id", typeof(int));
    
            var subDistrict_idParameter = subDistrict_id.HasValue ?
                new ObjectParameter("SubDistrict_id", subDistrict_id) :
                new ObjectParameter("SubDistrict_id", typeof(int));
    
            var name_ApplicantParameter = name_Applicant != null ?
                new ObjectParameter("Name_Applicant", name_Applicant) :
                new ObjectParameter("Name_Applicant", typeof(string));
    
            var address_ApplicantParameter = address_Applicant != null ?
                new ObjectParameter("Address_Applicant", address_Applicant) :
                new ObjectParameter("Address_Applicant", typeof(string));
    
            var aadharNumberParameter = aadharNumber != null ?
                new ObjectParameter("AadharNumber", aadharNumber) :
                new ObjectParameter("AadharNumber", typeof(string));
    
            var scheme_IdParameter = scheme_Id.HasValue ?
                new ObjectParameter("Scheme_Id", scheme_Id) :
                new ObjectParameter("Scheme_Id", typeof(int));
    
            var cast_IdParameter = cast_Id.HasValue ?
                new ObjectParameter("Cast_Id", cast_Id) :
                new ObjectParameter("Cast_Id", typeof(int));
    
            var sub_Cast_IdParameter = sub_Cast_Id.HasValue ?
                new ObjectParameter("Sub_Cast_Id", sub_Cast_Id) :
                new ObjectParameter("Sub_Cast_Id", typeof(int));
    
            var updated_ByParameter = updated_By.HasValue ?
                new ObjectParameter("Updated_By", updated_By) :
                new ObjectParameter("Updated_By", typeof(int));
    
            var remarksParameter = remarks != null ?
                new ObjectParameter("Remarks", remarks) :
                new ObjectParameter("Remarks", typeof(string));
    
            var phoneNoParameter = phoneNo != null ?
                new ObjectParameter("PhoneNo", phoneNo) :
                new ObjectParameter("PhoneNo", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_Loan_App_Issue", idParameter, district_IdParameter, subDistrict_idParameter, name_ApplicantParameter, address_ApplicantParameter, aadharNumberParameter, scheme_IdParameter, cast_IdParameter, sub_Cast_IdParameter, updated_ByParameter, remarksParameter, phoneNoParameter);
        }
    
        public virtual int Update_To_tbl_loanapp_Agreement_Status(Nullable<decimal> int_loanappid, string status, string fund, Nullable<System.DateTime> agrDate, Nullable<decimal> int_amtsanction)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var statusParameter = status != null ?
                new ObjectParameter("Status", status) :
                new ObjectParameter("Status", typeof(string));
    
            var fundParameter = fund != null ?
                new ObjectParameter("Fund", fund) :
                new ObjectParameter("Fund", typeof(string));
    
            var agrDateParameter = agrDate.HasValue ?
                new ObjectParameter("AgrDate", agrDate) :
                new ObjectParameter("AgrDate", typeof(System.DateTime));
    
            var int_amtsanctionParameter = int_amtsanction.HasValue ?
                new ObjectParameter("int_amtsanction", int_amtsanction) :
                new ObjectParameter("int_amtsanction", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_loanapp_Agreement_Status", int_loanappidParameter, statusParameter, fundParameter, agrDateParameter, int_amtsanctionParameter);
        }
    
        public virtual int Update_To_tbl_loanapp_HOLD(Nullable<decimal> int_loanappid, string status, string hold_Reason)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var statusParameter = status != null ?
                new ObjectParameter("Status", status) :
                new ObjectParameter("Status", typeof(string));
    
            var hold_ReasonParameter = hold_Reason != null ?
                new ObjectParameter("Hold_Reason", hold_Reason) :
                new ObjectParameter("Hold_Reason", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_loanapp_HOLD", int_loanappidParameter, statusParameter, hold_ReasonParameter);
        }
    
        public virtual int Update_To_tbl_loanapp_Status(Nullable<decimal> int_loanappid, string status)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var statusParameter = status != null ?
                new ObjectParameter("Status", status) :
                new ObjectParameter("Status", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_loanapp_Status", int_loanappidParameter, statusParameter);
        }
    
        public virtual int Update_To_tbl_loanreg_By_RegNo(string vchr_appreceivregno, string int_Loanno, Nullable<int> old_Scheme_Id, string old_Scheme_Abrivation, string old_GLHDNAME, Nullable<int> old_ACCHDCODE, string old_Scheme_Name, Nullable<System.DateTime> dte_agrement_date, Nullable<System.DateTime> dte_dateoforder, Nullable<decimal> mny_Loanamt, Nullable<decimal> mny_disbamt, Nullable<System.DateTime> dt_disbdate, Nullable<decimal> int_disb_inst, Nullable<decimal> int_inst_disbursed, Nullable<decimal> int_rate_int, Nullable<decimal> int_rate_penal, Nullable<decimal> mny_repayamt, Nullable<decimal> int_repay_inst, Nullable<decimal> mny_loanbal, Nullable<decimal> mny_prin_due, Nullable<decimal> mny_int_due, Nullable<decimal> mny_penal_due, Nullable<System.DateTime> dt_last_repay_date, Nullable<System.DateTime> dt_first_due_date, Nullable<System.DateTime> dt_next_due_date, Nullable<decimal> mny_intramt, Nullable<decimal> mny_intr_emi, Nullable<decimal> mny_intrbal, string vchr_stage, string vchr_offid, string vchr_oldloanno, Nullable<decimal> int_ots, Nullable<decimal> int_rvcd, Nullable<System.DateTime> dt_rvcd_date, string vchr_RRCno, Nullable<System.DateTime> dt_RR_Date, Nullable<decimal> int_RR, Nullable<decimal> int_Prefix, Nullable<decimal> int_ForClosing, Nullable<decimal> int_Green, string vchr_verify_remark, Nullable<decimal> int_rrdemand, string vchr_TRRCno, string vchr_offidC, Nullable<decimal> int_DefaultOts, string vchr_RRRemarks, Nullable<decimal> int_Ashwas, Nullable<decimal> int_MTR, Nullable<decimal> int_Ashwas_M, Nullable<decimal> int_repay_inst_Ext, Nullable<decimal> int_SR, Nullable<System.DateTime> dt_SRDate)
        {
            var vchr_appreceivregnoParameter = vchr_appreceivregno != null ?
                new ObjectParameter("vchr_appreceivregno", vchr_appreceivregno) :
                new ObjectParameter("vchr_appreceivregno", typeof(string));
    
            var int_LoannoParameter = int_Loanno != null ?
                new ObjectParameter("Int_Loanno", int_Loanno) :
                new ObjectParameter("Int_Loanno", typeof(string));
    
            var old_Scheme_IdParameter = old_Scheme_Id.HasValue ?
                new ObjectParameter("Old_Scheme_Id", old_Scheme_Id) :
                new ObjectParameter("Old_Scheme_Id", typeof(int));
    
            var old_Scheme_AbrivationParameter = old_Scheme_Abrivation != null ?
                new ObjectParameter("Old_Scheme_Abrivation", old_Scheme_Abrivation) :
                new ObjectParameter("Old_Scheme_Abrivation", typeof(string));
    
            var old_GLHDNAMEParameter = old_GLHDNAME != null ?
                new ObjectParameter("Old_GLHDNAME", old_GLHDNAME) :
                new ObjectParameter("Old_GLHDNAME", typeof(string));
    
            var old_ACCHDCODEParameter = old_ACCHDCODE.HasValue ?
                new ObjectParameter("Old_ACCHDCODE", old_ACCHDCODE) :
                new ObjectParameter("Old_ACCHDCODE", typeof(int));
    
            var old_Scheme_NameParameter = old_Scheme_Name != null ?
                new ObjectParameter("Old_Scheme_Name", old_Scheme_Name) :
                new ObjectParameter("Old_Scheme_Name", typeof(string));
    
            var dte_agrement_dateParameter = dte_agrement_date.HasValue ?
                new ObjectParameter("dte_agrement_date", dte_agrement_date) :
                new ObjectParameter("dte_agrement_date", typeof(System.DateTime));
    
            var dte_dateoforderParameter = dte_dateoforder.HasValue ?
                new ObjectParameter("dte_dateoforder", dte_dateoforder) :
                new ObjectParameter("dte_dateoforder", typeof(System.DateTime));
    
            var mny_LoanamtParameter = mny_Loanamt.HasValue ?
                new ObjectParameter("mny_Loanamt", mny_Loanamt) :
                new ObjectParameter("mny_Loanamt", typeof(decimal));
    
            var mny_disbamtParameter = mny_disbamt.HasValue ?
                new ObjectParameter("mny_disbamt", mny_disbamt) :
                new ObjectParameter("mny_disbamt", typeof(decimal));
    
            var dt_disbdateParameter = dt_disbdate.HasValue ?
                new ObjectParameter("dt_disbdate", dt_disbdate) :
                new ObjectParameter("dt_disbdate", typeof(System.DateTime));
    
            var int_disb_instParameter = int_disb_inst.HasValue ?
                new ObjectParameter("int_disb_inst", int_disb_inst) :
                new ObjectParameter("int_disb_inst", typeof(decimal));
    
            var int_inst_disbursedParameter = int_inst_disbursed.HasValue ?
                new ObjectParameter("int_inst_disbursed", int_inst_disbursed) :
                new ObjectParameter("int_inst_disbursed", typeof(decimal));
    
            var int_rate_intParameter = int_rate_int.HasValue ?
                new ObjectParameter("int_rate_int", int_rate_int) :
                new ObjectParameter("int_rate_int", typeof(decimal));
    
            var int_rate_penalParameter = int_rate_penal.HasValue ?
                new ObjectParameter("int_rate_penal", int_rate_penal) :
                new ObjectParameter("int_rate_penal", typeof(decimal));
    
            var mny_repayamtParameter = mny_repayamt.HasValue ?
                new ObjectParameter("mny_repayamt", mny_repayamt) :
                new ObjectParameter("mny_repayamt", typeof(decimal));
    
            var int_repay_instParameter = int_repay_inst.HasValue ?
                new ObjectParameter("int_repay_inst", int_repay_inst) :
                new ObjectParameter("int_repay_inst", typeof(decimal));
    
            var mny_loanbalParameter = mny_loanbal.HasValue ?
                new ObjectParameter("mny_loanbal", mny_loanbal) :
                new ObjectParameter("mny_loanbal", typeof(decimal));
    
            var mny_prin_dueParameter = mny_prin_due.HasValue ?
                new ObjectParameter("mny_prin_due", mny_prin_due) :
                new ObjectParameter("mny_prin_due", typeof(decimal));
    
            var mny_int_dueParameter = mny_int_due.HasValue ?
                new ObjectParameter("mny_int_due", mny_int_due) :
                new ObjectParameter("mny_int_due", typeof(decimal));
    
            var mny_penal_dueParameter = mny_penal_due.HasValue ?
                new ObjectParameter("mny_penal_due", mny_penal_due) :
                new ObjectParameter("mny_penal_due", typeof(decimal));
    
            var dt_last_repay_dateParameter = dt_last_repay_date.HasValue ?
                new ObjectParameter("dt_last_repay_date", dt_last_repay_date) :
                new ObjectParameter("dt_last_repay_date", typeof(System.DateTime));
    
            var dt_first_due_dateParameter = dt_first_due_date.HasValue ?
                new ObjectParameter("dt_first_due_date", dt_first_due_date) :
                new ObjectParameter("dt_first_due_date", typeof(System.DateTime));
    
            var dt_next_due_dateParameter = dt_next_due_date.HasValue ?
                new ObjectParameter("dt_next_due_date", dt_next_due_date) :
                new ObjectParameter("dt_next_due_date", typeof(System.DateTime));
    
            var mny_intramtParameter = mny_intramt.HasValue ?
                new ObjectParameter("mny_intramt", mny_intramt) :
                new ObjectParameter("mny_intramt", typeof(decimal));
    
            var mny_intr_emiParameter = mny_intr_emi.HasValue ?
                new ObjectParameter("mny_intr_emi", mny_intr_emi) :
                new ObjectParameter("mny_intr_emi", typeof(decimal));
    
            var mny_intrbalParameter = mny_intrbal.HasValue ?
                new ObjectParameter("mny_intrbal", mny_intrbal) :
                new ObjectParameter("mny_intrbal", typeof(decimal));
    
            var vchr_stageParameter = vchr_stage != null ?
                new ObjectParameter("vchr_stage", vchr_stage) :
                new ObjectParameter("vchr_stage", typeof(string));
    
            var vchr_offidParameter = vchr_offid != null ?
                new ObjectParameter("vchr_offid", vchr_offid) :
                new ObjectParameter("vchr_offid", typeof(string));
    
            var vchr_oldloannoParameter = vchr_oldloanno != null ?
                new ObjectParameter("vchr_oldloanno", vchr_oldloanno) :
                new ObjectParameter("vchr_oldloanno", typeof(string));
    
            var int_otsParameter = int_ots.HasValue ?
                new ObjectParameter("int_ots", int_ots) :
                new ObjectParameter("int_ots", typeof(decimal));
    
            var int_rvcdParameter = int_rvcd.HasValue ?
                new ObjectParameter("int_rvcd", int_rvcd) :
                new ObjectParameter("int_rvcd", typeof(decimal));
    
            var dt_rvcd_dateParameter = dt_rvcd_date.HasValue ?
                new ObjectParameter("dt_rvcd_date", dt_rvcd_date) :
                new ObjectParameter("dt_rvcd_date", typeof(System.DateTime));
    
            var vchr_RRCnoParameter = vchr_RRCno != null ?
                new ObjectParameter("vchr_RRCno", vchr_RRCno) :
                new ObjectParameter("vchr_RRCno", typeof(string));
    
            var dt_RR_DateParameter = dt_RR_Date.HasValue ?
                new ObjectParameter("dt_RR_Date", dt_RR_Date) :
                new ObjectParameter("dt_RR_Date", typeof(System.DateTime));
    
            var int_RRParameter = int_RR.HasValue ?
                new ObjectParameter("int_RR", int_RR) :
                new ObjectParameter("int_RR", typeof(decimal));
    
            var int_PrefixParameter = int_Prefix.HasValue ?
                new ObjectParameter("int_Prefix", int_Prefix) :
                new ObjectParameter("int_Prefix", typeof(decimal));
    
            var int_ForClosingParameter = int_ForClosing.HasValue ?
                new ObjectParameter("int_ForClosing", int_ForClosing) :
                new ObjectParameter("int_ForClosing", typeof(decimal));
    
            var int_GreenParameter = int_Green.HasValue ?
                new ObjectParameter("int_Green", int_Green) :
                new ObjectParameter("int_Green", typeof(decimal));
    
            var vchr_verify_remarkParameter = vchr_verify_remark != null ?
                new ObjectParameter("vchr_verify_remark", vchr_verify_remark) :
                new ObjectParameter("vchr_verify_remark", typeof(string));
    
            var int_rrdemandParameter = int_rrdemand.HasValue ?
                new ObjectParameter("int_rrdemand", int_rrdemand) :
                new ObjectParameter("int_rrdemand", typeof(decimal));
    
            var vchr_TRRCnoParameter = vchr_TRRCno != null ?
                new ObjectParameter("vchr_TRRCno", vchr_TRRCno) :
                new ObjectParameter("vchr_TRRCno", typeof(string));
    
            var vchr_offidCParameter = vchr_offidC != null ?
                new ObjectParameter("vchr_offidC", vchr_offidC) :
                new ObjectParameter("vchr_offidC", typeof(string));
    
            var int_DefaultOtsParameter = int_DefaultOts.HasValue ?
                new ObjectParameter("int_DefaultOts", int_DefaultOts) :
                new ObjectParameter("int_DefaultOts", typeof(decimal));
    
            var vchr_RRRemarksParameter = vchr_RRRemarks != null ?
                new ObjectParameter("vchr_RRRemarks", vchr_RRRemarks) :
                new ObjectParameter("vchr_RRRemarks", typeof(string));
    
            var int_AshwasParameter = int_Ashwas.HasValue ?
                new ObjectParameter("int_Ashwas", int_Ashwas) :
                new ObjectParameter("int_Ashwas", typeof(decimal));
    
            var int_MTRParameter = int_MTR.HasValue ?
                new ObjectParameter("int_MTR", int_MTR) :
                new ObjectParameter("int_MTR", typeof(decimal));
    
            var int_Ashwas_MParameter = int_Ashwas_M.HasValue ?
                new ObjectParameter("int_Ashwas_M", int_Ashwas_M) :
                new ObjectParameter("int_Ashwas_M", typeof(decimal));
    
            var int_repay_inst_ExtParameter = int_repay_inst_Ext.HasValue ?
                new ObjectParameter("int_repay_inst_Ext", int_repay_inst_Ext) :
                new ObjectParameter("int_repay_inst_Ext", typeof(decimal));
    
            var int_SRParameter = int_SR.HasValue ?
                new ObjectParameter("int_SR", int_SR) :
                new ObjectParameter("int_SR", typeof(decimal));
    
            var dt_SRDateParameter = dt_SRDate.HasValue ?
                new ObjectParameter("dt_SRDate", dt_SRDate) :
                new ObjectParameter("dt_SRDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_loanreg_By_RegNo", vchr_appreceivregnoParameter, int_LoannoParameter, old_Scheme_IdParameter, old_Scheme_AbrivationParameter, old_GLHDNAMEParameter, old_ACCHDCODEParameter, old_Scheme_NameParameter, dte_agrement_dateParameter, dte_dateoforderParameter, mny_LoanamtParameter, mny_disbamtParameter, dt_disbdateParameter, int_disb_instParameter, int_inst_disbursedParameter, int_rate_intParameter, int_rate_penalParameter, mny_repayamtParameter, int_repay_instParameter, mny_loanbalParameter, mny_prin_dueParameter, mny_int_dueParameter, mny_penal_dueParameter, dt_last_repay_dateParameter, dt_first_due_dateParameter, dt_next_due_dateParameter, mny_intramtParameter, mny_intr_emiParameter, mny_intrbalParameter, vchr_stageParameter, vchr_offidParameter, vchr_oldloannoParameter, int_otsParameter, int_rvcdParameter, dt_rvcd_dateParameter, vchr_RRCnoParameter, dt_RR_DateParameter, int_RRParameter, int_PrefixParameter, int_ForClosingParameter, int_GreenParameter, vchr_verify_remarkParameter, int_rrdemandParameter, vchr_TRRCnoParameter, vchr_offidCParameter, int_DefaultOtsParameter, vchr_RRRemarksParameter, int_AshwasParameter, int_MTRParameter, int_Ashwas_MParameter, int_repay_inst_ExtParameter, int_SRParameter, dt_SRDateParameter);
        }
    
        public virtual int Update_To_tbl_Logs(string method_Name, string log_Details, Nullable<int> user_Id, Nullable<System.DateTime> log_DateTime, string type)
        {
            var method_NameParameter = method_Name != null ?
                new ObjectParameter("Method_Name", method_Name) :
                new ObjectParameter("Method_Name", typeof(string));
    
            var log_DetailsParameter = log_Details != null ?
                new ObjectParameter("Log_Details", log_Details) :
                new ObjectParameter("Log_Details", typeof(string));
    
            var user_IdParameter = user_Id.HasValue ?
                new ObjectParameter("User_Id", user_Id) :
                new ObjectParameter("User_Id", typeof(int));
    
            var log_DateTimeParameter = log_DateTime.HasValue ?
                new ObjectParameter("Log_DateTime", log_DateTime) :
                new ObjectParameter("Log_DateTime", typeof(System.DateTime));
    
            var typeParameter = type != null ?
                new ObjectParameter("Type", type) :
                new ObjectParameter("Type", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_Logs", method_NameParameter, log_DetailsParameter, user_IdParameter, log_DateTimeParameter, typeParameter);
        }
    
        public virtual int Update_To_tbl_marriageloan(Nullable<decimal> int_loanappid, Nullable<int> int_schemeid, string vchr_bridename, string vchr_brideaddr, Nullable<int> int_brideage, Nullable<System.DateTime> dte_bridedob, string vchr_groomname, string vchr_groomaddr, Nullable<System.DateTime> dte_dt)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var int_schemeidParameter = int_schemeid.HasValue ?
                new ObjectParameter("int_schemeid", int_schemeid) :
                new ObjectParameter("int_schemeid", typeof(int));
    
            var vchr_bridenameParameter = vchr_bridename != null ?
                new ObjectParameter("vchr_bridename", vchr_bridename) :
                new ObjectParameter("vchr_bridename", typeof(string));
    
            var vchr_brideaddrParameter = vchr_brideaddr != null ?
                new ObjectParameter("vchr_brideaddr", vchr_brideaddr) :
                new ObjectParameter("vchr_brideaddr", typeof(string));
    
            var int_brideageParameter = int_brideage.HasValue ?
                new ObjectParameter("int_brideage", int_brideage) :
                new ObjectParameter("int_brideage", typeof(int));
    
            var dte_bridedobParameter = dte_bridedob.HasValue ?
                new ObjectParameter("dte_bridedob", dte_bridedob) :
                new ObjectParameter("dte_bridedob", typeof(System.DateTime));
    
            var vchr_groomnameParameter = vchr_groomname != null ?
                new ObjectParameter("vchr_groomname", vchr_groomname) :
                new ObjectParameter("vchr_groomname", typeof(string));
    
            var vchr_groomaddrParameter = vchr_groomaddr != null ?
                new ObjectParameter("vchr_groomaddr", vchr_groomaddr) :
                new ObjectParameter("vchr_groomaddr", typeof(string));
    
            var dte_dtParameter = dte_dt.HasValue ?
                new ObjectParameter("dte_dt", dte_dt) :
                new ObjectParameter("dte_dt", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_marriageloan", int_loanappidParameter, int_schemeidParameter, vchr_bridenameParameter, vchr_brideaddrParameter, int_brideageParameter, dte_bridedobParameter, vchr_groomnameParameter, vchr_groomaddrParameter, dte_dtParameter);
        }
    
        public virtual int Update_To_tbl_personal_loan(Nullable<decimal> int_loanappid, Nullable<int> int_schemeid, string vchr_workorgname, string vchr_workorgaddr, string vchr_designation, string vchr_scaleofpay, Nullable<decimal> int_netsal, Nullable<decimal> int_workgrosssal, string vchr_Purpose)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var int_schemeidParameter = int_schemeid.HasValue ?
                new ObjectParameter("int_schemeid", int_schemeid) :
                new ObjectParameter("int_schemeid", typeof(int));
    
            var vchr_workorgnameParameter = vchr_workorgname != null ?
                new ObjectParameter("vchr_workorgname", vchr_workorgname) :
                new ObjectParameter("vchr_workorgname", typeof(string));
    
            var vchr_workorgaddrParameter = vchr_workorgaddr != null ?
                new ObjectParameter("vchr_workorgaddr", vchr_workorgaddr) :
                new ObjectParameter("vchr_workorgaddr", typeof(string));
    
            var vchr_designationParameter = vchr_designation != null ?
                new ObjectParameter("vchr_designation", vchr_designation) :
                new ObjectParameter("vchr_designation", typeof(string));
    
            var vchr_scaleofpayParameter = vchr_scaleofpay != null ?
                new ObjectParameter("vchr_scaleofpay", vchr_scaleofpay) :
                new ObjectParameter("vchr_scaleofpay", typeof(string));
    
            var int_netsalParameter = int_netsal.HasValue ?
                new ObjectParameter("int_netsal", int_netsal) :
                new ObjectParameter("int_netsal", typeof(decimal));
    
            var int_workgrosssalParameter = int_workgrosssal.HasValue ?
                new ObjectParameter("int_workgrosssal", int_workgrosssal) :
                new ObjectParameter("int_workgrosssal", typeof(decimal));
    
            var vchr_PurposeParameter = vchr_Purpose != null ?
                new ObjectParameter("vchr_Purpose", vchr_Purpose) :
                new ObjectParameter("vchr_Purpose", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_personal_loan", int_loanappidParameter, int_schemeidParameter, vchr_workorgnameParameter, vchr_workorgaddrParameter, vchr_designationParameter, vchr_scaleofpayParameter, int_netsalParameter, int_workgrosssalParameter, vchr_PurposeParameter);
        }
    
        public virtual int Update_To_tbl_Projects(Nullable<int> id, Nullable<int> agency_Id, Nullable<int> scheme_Id, Nullable<int> sector_Id, string project, Nullable<int> isActive, Nullable<int> updatedBy)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            var agency_IdParameter = agency_Id.HasValue ?
                new ObjectParameter("Agency_Id", agency_Id) :
                new ObjectParameter("Agency_Id", typeof(int));
    
            var scheme_IdParameter = scheme_Id.HasValue ?
                new ObjectParameter("Scheme_Id", scheme_Id) :
                new ObjectParameter("Scheme_Id", typeof(int));
    
            var sector_IdParameter = sector_Id.HasValue ?
                new ObjectParameter("Sector_Id", sector_Id) :
                new ObjectParameter("Sector_Id", typeof(int));
    
            var projectParameter = project != null ?
                new ObjectParameter("Project", project) :
                new ObjectParameter("Project", typeof(string));
    
            var isActiveParameter = isActive.HasValue ?
                new ObjectParameter("IsActive", isActive) :
                new ObjectParameter("IsActive", typeof(int));
    
            var updatedByParameter = updatedBy.HasValue ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_Projects", idParameter, agency_IdParameter, scheme_IdParameter, sector_IdParameter, projectParameter, isActiveParameter, updatedByParameter);
        }
    
        public virtual int Update_To_tbl_Roles(Nullable<int> id, string name, string description, Nullable<int> updated_By, Nullable<int> isActive)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            var nameParameter = name != null ?
                new ObjectParameter("Name", name) :
                new ObjectParameter("Name", typeof(string));
    
            var descriptionParameter = description != null ?
                new ObjectParameter("Description", description) :
                new ObjectParameter("Description", typeof(string));
    
            var updated_ByParameter = updated_By.HasValue ?
                new ObjectParameter("Updated_By", updated_By) :
                new ObjectParameter("Updated_By", typeof(int));
    
            var isActiveParameter = isActive.HasValue ?
                new ObjectParameter("IsActive", isActive) :
                new ObjectParameter("IsActive", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_Roles", idParameter, nameParameter, descriptionParameter, updated_ByParameter, isActiveParameter);
        }
    
        public virtual int Update_To_tbl_Schemes(Nullable<int> id, Nullable<int> agency_Id, string scheme, Nullable<decimal> loan_Amount, Nullable<double> loan_Interest, Nullable<int> loan_Period, Nullable<double> penal_Interest, Nullable<int> max_Age, Nullable<int> min_Age, Nullable<decimal> anninc_Rural_Max, Nullable<decimal> anninc_Urban_Max, Nullable<decimal> beneficiaryContribution, Nullable<int> isActive, Nullable<int> updated_By)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            var agency_IdParameter = agency_Id.HasValue ?
                new ObjectParameter("Agency_Id", agency_Id) :
                new ObjectParameter("Agency_Id", typeof(int));
    
            var schemeParameter = scheme != null ?
                new ObjectParameter("Scheme", scheme) :
                new ObjectParameter("Scheme", typeof(string));
    
            var loan_AmountParameter = loan_Amount.HasValue ?
                new ObjectParameter("Loan_Amount", loan_Amount) :
                new ObjectParameter("Loan_Amount", typeof(decimal));
    
            var loan_InterestParameter = loan_Interest.HasValue ?
                new ObjectParameter("Loan_Interest", loan_Interest) :
                new ObjectParameter("Loan_Interest", typeof(double));
    
            var loan_PeriodParameter = loan_Period.HasValue ?
                new ObjectParameter("Loan_Period", loan_Period) :
                new ObjectParameter("Loan_Period", typeof(int));
    
            var penal_InterestParameter = penal_Interest.HasValue ?
                new ObjectParameter("Penal_Interest", penal_Interest) :
                new ObjectParameter("Penal_Interest", typeof(double));
    
            var max_AgeParameter = max_Age.HasValue ?
                new ObjectParameter("Max_Age", max_Age) :
                new ObjectParameter("Max_Age", typeof(int));
    
            var min_AgeParameter = min_Age.HasValue ?
                new ObjectParameter("Min_Age", min_Age) :
                new ObjectParameter("Min_Age", typeof(int));
    
            var anninc_Rural_MaxParameter = anninc_Rural_Max.HasValue ?
                new ObjectParameter("Anninc_Rural_Max", anninc_Rural_Max) :
                new ObjectParameter("Anninc_Rural_Max", typeof(decimal));
    
            var anninc_Urban_MaxParameter = anninc_Urban_Max.HasValue ?
                new ObjectParameter("Anninc_Urban_Max", anninc_Urban_Max) :
                new ObjectParameter("Anninc_Urban_Max", typeof(decimal));
    
            var beneficiaryContributionParameter = beneficiaryContribution.HasValue ?
                new ObjectParameter("BeneficiaryContribution", beneficiaryContribution) :
                new ObjectParameter("BeneficiaryContribution", typeof(decimal));
    
            var isActiveParameter = isActive.HasValue ?
                new ObjectParameter("IsActive", isActive) :
                new ObjectParameter("IsActive", typeof(int));
    
            var updated_ByParameter = updated_By.HasValue ?
                new ObjectParameter("Updated_By", updated_By) :
                new ObjectParameter("Updated_By", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_Schemes", idParameter, agency_IdParameter, schemeParameter, loan_AmountParameter, loan_InterestParameter, loan_PeriodParameter, penal_InterestParameter, max_AgeParameter, min_AgeParameter, anninc_Rural_MaxParameter, anninc_Urban_MaxParameter, beneficiaryContributionParameter, isActiveParameter, updated_ByParameter);
        }
    
        public virtual int Update_To_tbl_Sectors(Nullable<int> id, string sector, string code, Nullable<int> isActive, Nullable<int> updatedBy)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            var sectorParameter = sector != null ?
                new ObjectParameter("Sector", sector) :
                new ObjectParameter("Sector", typeof(string));
    
            var codeParameter = code != null ?
                new ObjectParameter("Code", code) :
                new ObjectParameter("Code", typeof(string));
    
            var isActiveParameter = isActive.HasValue ?
                new ObjectParameter("IsActive", isActive) :
                new ObjectParameter("IsActive", typeof(int));
    
            var updatedByParameter = updatedBy.HasValue ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_Sectors", idParameter, sectorParameter, codeParameter, isActiveParameter, updatedByParameter);
        }
    
        public virtual int Update_To_tbl_selfloan(Nullable<int> int_loanappid, Nullable<int> int_schemeid, string vchr_sector, string vchr_project, Nullable<decimal> int_amt_est, Nullable<decimal> int_amt_hand, string vchr_projdetails, string applicant_Experience)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(int));
    
            var int_schemeidParameter = int_schemeid.HasValue ?
                new ObjectParameter("int_schemeid", int_schemeid) :
                new ObjectParameter("int_schemeid", typeof(int));
    
            var vchr_sectorParameter = vchr_sector != null ?
                new ObjectParameter("vchr_sector", vchr_sector) :
                new ObjectParameter("vchr_sector", typeof(string));
    
            var vchr_projectParameter = vchr_project != null ?
                new ObjectParameter("vchr_project", vchr_project) :
                new ObjectParameter("vchr_project", typeof(string));
    
            var int_amt_estParameter = int_amt_est.HasValue ?
                new ObjectParameter("int_amt_est", int_amt_est) :
                new ObjectParameter("int_amt_est", typeof(decimal));
    
            var int_amt_handParameter = int_amt_hand.HasValue ?
                new ObjectParameter("int_amt_hand", int_amt_hand) :
                new ObjectParameter("int_amt_hand", typeof(decimal));
    
            var vchr_projdetailsParameter = vchr_projdetails != null ?
                new ObjectParameter("vchr_projdetails", vchr_projdetails) :
                new ObjectParameter("vchr_projdetails", typeof(string));
    
            var applicant_ExperienceParameter = applicant_Experience != null ?
                new ObjectParameter("Applicant_Experience", applicant_Experience) :
                new ObjectParameter("Applicant_Experience", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_selfloan", int_loanappidParameter, int_schemeidParameter, vchr_sectorParameter, vchr_projectParameter, int_amt_estParameter, int_amt_handParameter, vchr_projdetailsParameter, applicant_ExperienceParameter);
        }
    
        public virtual int Update_To_tbl_Users(Nullable<int> id, string name, string username, string password, Nullable<int> role_id, Nullable<int> office_Id, string emailId, string mobile, string address, Nullable<int> updatedBy, Nullable<int> isActive)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("Id", id) :
                new ObjectParameter("Id", typeof(int));
    
            var nameParameter = name != null ?
                new ObjectParameter("Name", name) :
                new ObjectParameter("Name", typeof(string));
    
            var usernameParameter = username != null ?
                new ObjectParameter("Username", username) :
                new ObjectParameter("Username", typeof(string));
    
            var passwordParameter = password != null ?
                new ObjectParameter("Password", password) :
                new ObjectParameter("Password", typeof(string));
    
            var role_idParameter = role_id.HasValue ?
                new ObjectParameter("Role_id", role_id) :
                new ObjectParameter("Role_id", typeof(int));
    
            var office_IdParameter = office_Id.HasValue ?
                new ObjectParameter("Office_Id", office_Id) :
                new ObjectParameter("Office_Id", typeof(int));
    
            var emailIdParameter = emailId != null ?
                new ObjectParameter("EmailId", emailId) :
                new ObjectParameter("EmailId", typeof(string));
    
            var mobileParameter = mobile != null ?
                new ObjectParameter("Mobile", mobile) :
                new ObjectParameter("Mobile", typeof(string));
    
            var addressParameter = address != null ?
                new ObjectParameter("Address", address) :
                new ObjectParameter("Address", typeof(string));
    
            var updatedByParameter = updatedBy.HasValue ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(int));
    
            var isActiveParameter = isActive.HasValue ?
                new ObjectParameter("IsActive", isActive) :
                new ObjectParameter("IsActive", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_Users", idParameter, nameParameter, usernameParameter, passwordParameter, role_idParameter, office_IdParameter, emailIdParameter, mobileParameter, addressParameter, updatedByParameter, isActiveParameter);
        }
    
        public virtual int Update_To_tbl_veh_hme_loan(Nullable<decimal> int_loanappid, Nullable<int> int_schemeid, string vchr_workorgname, string vchr_workorgaddr, string vchr_designation, string vchr_scaleofpay, Nullable<decimal> int_netsal, Nullable<decimal> int_workgrosssal, string vchr_vehname, Nullable<decimal> int_marketrate)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var int_schemeidParameter = int_schemeid.HasValue ?
                new ObjectParameter("int_schemeid", int_schemeid) :
                new ObjectParameter("int_schemeid", typeof(int));
    
            var vchr_workorgnameParameter = vchr_workorgname != null ?
                new ObjectParameter("vchr_workorgname", vchr_workorgname) :
                new ObjectParameter("vchr_workorgname", typeof(string));
    
            var vchr_workorgaddrParameter = vchr_workorgaddr != null ?
                new ObjectParameter("vchr_workorgaddr", vchr_workorgaddr) :
                new ObjectParameter("vchr_workorgaddr", typeof(string));
    
            var vchr_designationParameter = vchr_designation != null ?
                new ObjectParameter("vchr_designation", vchr_designation) :
                new ObjectParameter("vchr_designation", typeof(string));
    
            var vchr_scaleofpayParameter = vchr_scaleofpay != null ?
                new ObjectParameter("vchr_scaleofpay", vchr_scaleofpay) :
                new ObjectParameter("vchr_scaleofpay", typeof(string));
    
            var int_netsalParameter = int_netsal.HasValue ?
                new ObjectParameter("int_netsal", int_netsal) :
                new ObjectParameter("int_netsal", typeof(decimal));
    
            var int_workgrosssalParameter = int_workgrosssal.HasValue ?
                new ObjectParameter("int_workgrosssal", int_workgrosssal) :
                new ObjectParameter("int_workgrosssal", typeof(decimal));
    
            var vchr_vehnameParameter = vchr_vehname != null ?
                new ObjectParameter("vchr_vehname", vchr_vehname) :
                new ObjectParameter("vchr_vehname", typeof(string));
    
            var int_marketrateParameter = int_marketrate.HasValue ?
                new ObjectParameter("int_marketrate", int_marketrate) :
                new ObjectParameter("int_marketrate", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_veh_hme_loan", int_loanappidParameter, int_schemeidParameter, vchr_workorgnameParameter, vchr_workorgaddrParameter, vchr_designationParameter, vchr_scaleofpayParameter, int_netsalParameter, int_workgrosssalParameter, vchr_vehnameParameter, int_marketrateParameter);
        }
    
        public virtual int Update_To_tbl_workcapital(Nullable<decimal> int_loanappid, Nullable<int> int_schemeid, string vchr_sector, string vchr_project, Nullable<decimal> int_amt_est, Nullable<decimal> int_amt_hand, string vchr_projdetails, string vchr_businessdet, string applicant_Experience)
        {
            var int_loanappidParameter = int_loanappid.HasValue ?
                new ObjectParameter("int_loanappid", int_loanappid) :
                new ObjectParameter("int_loanappid", typeof(decimal));
    
            var int_schemeidParameter = int_schemeid.HasValue ?
                new ObjectParameter("int_schemeid", int_schemeid) :
                new ObjectParameter("int_schemeid", typeof(int));
    
            var vchr_sectorParameter = vchr_sector != null ?
                new ObjectParameter("vchr_sector", vchr_sector) :
                new ObjectParameter("vchr_sector", typeof(string));
    
            var vchr_projectParameter = vchr_project != null ?
                new ObjectParameter("vchr_project", vchr_project) :
                new ObjectParameter("vchr_project", typeof(string));
    
            var int_amt_estParameter = int_amt_est.HasValue ?
                new ObjectParameter("int_amt_est", int_amt_est) :
                new ObjectParameter("int_amt_est", typeof(decimal));
    
            var int_amt_handParameter = int_amt_hand.HasValue ?
                new ObjectParameter("int_amt_hand", int_amt_hand) :
                new ObjectParameter("int_amt_hand", typeof(decimal));
    
            var vchr_projdetailsParameter = vchr_projdetails != null ?
                new ObjectParameter("vchr_projdetails", vchr_projdetails) :
                new ObjectParameter("vchr_projdetails", typeof(string));
    
            var vchr_businessdetParameter = vchr_businessdet != null ?
                new ObjectParameter("vchr_businessdet", vchr_businessdet) :
                new ObjectParameter("vchr_businessdet", typeof(string));
    
            var applicant_ExperienceParameter = applicant_Experience != null ?
                new ObjectParameter("Applicant_Experience", applicant_Experience) :
                new ObjectParameter("Applicant_Experience", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_To_tbl_workcapital", int_loanappidParameter, int_schemeidParameter, vchr_sectorParameter, vchr_projectParameter, int_amt_estParameter, int_amt_handParameter, vchr_projdetailsParameter, vchr_businessdetParameter, applicant_ExperienceParameter);
        }
    
        public virtual ObjectResult<Select_Scheme_By_Id_Result> Select_Scheme_By_Id(string dist_Code, Nullable<int> scheme_Id)
        {
            var dist_CodeParameter = dist_Code != null ?
                new ObjectParameter("Dist_Code", dist_Code) :
                new ObjectParameter("Dist_Code", typeof(string));
    
            var scheme_IdParameter = scheme_Id.HasValue ?
                new ObjectParameter("Scheme_Id", scheme_Id) :
                new ObjectParameter("Scheme_Id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_Scheme_By_Id_Result>("Select_Scheme_By_Id", dist_CodeParameter, scheme_IdParameter);
        }
    
        public virtual ObjectResult<Select_LoanAppId_By_LoanNo_Result> Select_LoanAppId_By_LoanNo(string loanNo)
        {
            var loanNoParameter = loanNo != null ?
                new ObjectParameter("LoanNo", loanNo) :
                new ObjectParameter("LoanNo", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_LoanAppId_By_LoanNo_Result>("Select_LoanAppId_By_LoanNo", loanNoParameter);
        }
    
        public virtual int Insert_To_tbl_loanapp(string vchr_appreceivregno, Nullable<decimal> int_no, string vchr_fund, string vchr_applname, string vchr_hsename, string vchr_phno, string vchr_caste, string vchr_religion, string vchr_sex, string vchr_village, string vchr_taluk, string vchr_district, Nullable<decimal> int_anninc, string vchr_remark, string vchr_oldno, string vchr_phno2, string int_loanno, string vchr_offid, Nullable<System.DateTime> dte_agrement_date, Nullable<int> int_schemeid, Nullable<int> block_Id, string chr_status, Nullable<int> subCaste, Nullable<int> lnaccid)
        {
            var vchr_appreceivregnoParameter = vchr_appreceivregno != null ?
                new ObjectParameter("vchr_appreceivregno", vchr_appreceivregno) :
                new ObjectParameter("vchr_appreceivregno", typeof(string));
    
            var int_noParameter = int_no.HasValue ?
                new ObjectParameter("int_no", int_no) :
                new ObjectParameter("int_no", typeof(decimal));
    
            var vchr_fundParameter = vchr_fund != null ?
                new ObjectParameter("vchr_fund", vchr_fund) :
                new ObjectParameter("vchr_fund", typeof(string));
    
            var vchr_applnameParameter = vchr_applname != null ?
                new ObjectParameter("vchr_applname", vchr_applname) :
                new ObjectParameter("vchr_applname", typeof(string));
    
            var vchr_hsenameParameter = vchr_hsename != null ?
                new ObjectParameter("vchr_hsename", vchr_hsename) :
                new ObjectParameter("vchr_hsename", typeof(string));
    
            var vchr_phnoParameter = vchr_phno != null ?
                new ObjectParameter("vchr_phno", vchr_phno) :
                new ObjectParameter("vchr_phno", typeof(string));
    
            var vchr_casteParameter = vchr_caste != null ?
                new ObjectParameter("vchr_caste", vchr_caste) :
                new ObjectParameter("vchr_caste", typeof(string));
    
            var vchr_religionParameter = vchr_religion != null ?
                new ObjectParameter("vchr_religion", vchr_religion) :
                new ObjectParameter("vchr_religion", typeof(string));
    
            var vchr_sexParameter = vchr_sex != null ?
                new ObjectParameter("vchr_sex", vchr_sex) :
                new ObjectParameter("vchr_sex", typeof(string));
    
            var vchr_villageParameter = vchr_village != null ?
                new ObjectParameter("vchr_village", vchr_village) :
                new ObjectParameter("vchr_village", typeof(string));
    
            var vchr_talukParameter = vchr_taluk != null ?
                new ObjectParameter("vchr_taluk", vchr_taluk) :
                new ObjectParameter("vchr_taluk", typeof(string));
    
            var vchr_districtParameter = vchr_district != null ?
                new ObjectParameter("vchr_district", vchr_district) :
                new ObjectParameter("vchr_district", typeof(string));
    
            var int_annincParameter = int_anninc.HasValue ?
                new ObjectParameter("int_anninc", int_anninc) :
                new ObjectParameter("int_anninc", typeof(decimal));
    
            var vchr_remarkParameter = vchr_remark != null ?
                new ObjectParameter("vchr_remark", vchr_remark) :
                new ObjectParameter("vchr_remark", typeof(string));
    
            var vchr_oldnoParameter = vchr_oldno != null ?
                new ObjectParameter("vchr_oldno", vchr_oldno) :
                new ObjectParameter("vchr_oldno", typeof(string));
    
            var vchr_phno2Parameter = vchr_phno2 != null ?
                new ObjectParameter("vchr_phno2", vchr_phno2) :
                new ObjectParameter("vchr_phno2", typeof(string));
    
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            var vchr_offidParameter = vchr_offid != null ?
                new ObjectParameter("vchr_offid", vchr_offid) :
                new ObjectParameter("vchr_offid", typeof(string));
    
            var dte_agrement_dateParameter = dte_agrement_date.HasValue ?
                new ObjectParameter("dte_agrement_date", dte_agrement_date) :
                new ObjectParameter("dte_agrement_date", typeof(System.DateTime));
    
            var int_schemeidParameter = int_schemeid.HasValue ?
                new ObjectParameter("int_schemeid", int_schemeid) :
                new ObjectParameter("int_schemeid", typeof(int));
    
            var block_IdParameter = block_Id.HasValue ?
                new ObjectParameter("Block_Id", block_Id) :
                new ObjectParameter("Block_Id", typeof(int));
    
            var chr_statusParameter = chr_status != null ?
                new ObjectParameter("chr_status", chr_status) :
                new ObjectParameter("chr_status", typeof(string));
    
            var subCasteParameter = subCaste.HasValue ?
                new ObjectParameter("SubCaste", subCaste) :
                new ObjectParameter("SubCaste", typeof(int));
    
            var lnaccidParameter = lnaccid.HasValue ?
                new ObjectParameter("Lnaccid", lnaccid) :
                new ObjectParameter("Lnaccid", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_loanapp", vchr_appreceivregnoParameter, int_noParameter, vchr_fundParameter, vchr_applnameParameter, vchr_hsenameParameter, vchr_phnoParameter, vchr_casteParameter, vchr_religionParameter, vchr_sexParameter, vchr_villageParameter, vchr_talukParameter, vchr_districtParameter, int_annincParameter, vchr_remarkParameter, vchr_oldnoParameter, vchr_phno2Parameter, int_loannoParameter, vchr_offidParameter, dte_agrement_dateParameter, int_schemeidParameter, block_IdParameter, chr_statusParameter, subCasteParameter, lnaccidParameter);
        }
    
        public virtual ObjectResult<Employee_Confirmation_Letter_Print_Result> Employee_Confirmation_Letter_Print(Nullable<int> int_empid)
        {
            var int_empidParameter = int_empid.HasValue ?
                new ObjectParameter("int_empid", int_empid) :
                new ObjectParameter("int_empid", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Employee_Confirmation_Letter_Print_Result>("Employee_Confirmation_Letter_Print", int_empidParameter);
        }
    
        public virtual ObjectResult<Select_All_EmpSurety_By_Lnaccid_Result> Select_All_EmpSurety_By_Lnaccid(Nullable<int> lnaccid, string dist_Code)
        {
            var lnaccidParameter = lnaccid.HasValue ?
                new ObjectParameter("Lnaccid", lnaccid) :
                new ObjectParameter("Lnaccid", typeof(int));
    
            var dist_CodeParameter = dist_Code != null ?
                new ObjectParameter("Dist_Code", dist_Code) :
                new ObjectParameter("Dist_Code", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_All_EmpSurety_By_Lnaccid_Result>("Select_All_EmpSurety_By_Lnaccid", lnaccidParameter, dist_CodeParameter);
        }
    
        public virtual ObjectResult<Select_Pricipale_Due_Result> Select_Pricipale_Due(string int_loanno, Nullable<System.DateTime> dt_transaction)
        {
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            var dt_transactionParameter = dt_transaction.HasValue ?
                new ObjectParameter("dt_transaction", dt_transaction) :
                new ObjectParameter("dt_transaction", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_Pricipale_Due_Result>("Select_Pricipale_Due", int_loannoParameter, dt_transactionParameter);
        }
    
        public virtual ObjectResult<Check_Year_End_Record_Exist_Result> Check_Year_End_Record_Exist(string int_loanno, Nullable<System.DateTime> dt_transaction)
        {
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            var dt_transactionParameter = dt_transaction.HasValue ?
                new ObjectParameter("dt_transaction", dt_transaction) :
                new ObjectParameter("dt_transaction", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Check_Year_End_Record_Exist_Result>("Check_Year_End_Record_Exist", int_loannoParameter, dt_transactionParameter);
        }
    
        public virtual ObjectResult<Select_Last_Reg_Details_Result> Select_Last_Reg_Details(string int_loanno)
        {
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_Last_Reg_Details_Result>("Select_Last_Reg_Details", int_loannoParameter);
        }
    
        public virtual ObjectResult<Select_Last_Loan_Trans_Details_Result> Select_Last_Loan_Trans_Details(string int_loanno)
        {
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Select_Last_Loan_Trans_Details_Result>("Select_Last_Loan_Trans_Details", int_loannoParameter);
        }
    
        public virtual int Update_tbl_loanreg_By_LoanNo_BC(string int_loanno, Nullable<decimal> int_prin_dues, Nullable<System.DateTime> dt_transaction, Nullable<decimal> int_int_dues, Nullable<decimal> int_penal_dues)
        {
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            var int_prin_duesParameter = int_prin_dues.HasValue ?
                new ObjectParameter("int_prin_dues", int_prin_dues) :
                new ObjectParameter("int_prin_dues", typeof(decimal));
    
            var dt_transactionParameter = dt_transaction.HasValue ?
                new ObjectParameter("dt_transaction", dt_transaction) :
                new ObjectParameter("dt_transaction", typeof(System.DateTime));
    
            var int_int_duesParameter = int_int_dues.HasValue ?
                new ObjectParameter("int_int_dues", int_int_dues) :
                new ObjectParameter("int_int_dues", typeof(decimal));
    
            var int_penal_duesParameter = int_penal_dues.HasValue ?
                new ObjectParameter("int_penal_dues", int_penal_dues) :
                new ObjectParameter("int_penal_dues", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_tbl_loanreg_By_LoanNo_BC", int_loannoParameter, int_prin_duesParameter, dt_transactionParameter, int_int_duesParameter, int_penal_duesParameter);
        }
    
        public virtual int Update_tbl_loanreg_By_LoanNo_DISBURSED(string int_loanno, Nullable<decimal> int_prin_dues, Nullable<System.DateTime> dt_transaction, Nullable<decimal> int_int_dues, Nullable<decimal> int_penal_dues)
        {
            var int_loannoParameter = int_loanno != null ?
                new ObjectParameter("int_loanno", int_loanno) :
                new ObjectParameter("int_loanno", typeof(string));
    
            var int_prin_duesParameter = int_prin_dues.HasValue ?
                new ObjectParameter("int_prin_dues", int_prin_dues) :
                new ObjectParameter("int_prin_dues", typeof(decimal));
    
            var dt_transactionParameter = dt_transaction.HasValue ?
                new ObjectParameter("dt_transaction", dt_transaction) :
                new ObjectParameter("dt_transaction", typeof(System.DateTime));
    
            var int_int_duesParameter = int_int_dues.HasValue ?
                new ObjectParameter("int_int_dues", int_int_dues) :
                new ObjectParameter("int_int_dues", typeof(decimal));
    
            var int_penal_duesParameter = int_penal_dues.HasValue ?
                new ObjectParameter("int_penal_dues", int_penal_dues) :
                new ObjectParameter("int_penal_dues", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Update_tbl_loanreg_By_LoanNo_DISBURSED", int_loannoParameter, int_prin_duesParameter, dt_transactionParameter, int_int_duesParameter, int_penal_duesParameter);
        }
    
        public virtual int Insert_To_tbl_loanreg(string vchr_appreceivregno, string int_Loanno, Nullable<int> old_Scheme_Id, string old_Scheme_Abrivation, string old_GLHDNAME, string old_ACCHDCODE, string old_Scheme_Name, Nullable<decimal> mny_Loanamt, Nullable<decimal> mny_disbamt, Nullable<System.DateTime> dt_disbdate, Nullable<decimal> int_rate_int, Nullable<decimal> int_rate_penal, Nullable<decimal> mny_repayamt, Nullable<decimal> int_repay_inst, Nullable<decimal> mny_loanbal, string vchr_offid, string vchr_offidC, Nullable<System.DateTime> dt_last_repay_date, Nullable<System.DateTime> dt_first_due_date, Nullable<decimal> int_disb_inst, Nullable<decimal> int_inst_disbursed, Nullable<decimal> int_RR, Nullable<System.DateTime> dt_RR_Date, string vchr_RRCno, Nullable<decimal> int_SR, Nullable<System.DateTime> dt_SRDate, string repay_Interval)
        {
            var vchr_appreceivregnoParameter = vchr_appreceivregno != null ?
                new ObjectParameter("vchr_appreceivregno", vchr_appreceivregno) :
                new ObjectParameter("vchr_appreceivregno", typeof(string));
    
            var int_LoannoParameter = int_Loanno != null ?
                new ObjectParameter("Int_Loanno", int_Loanno) :
                new ObjectParameter("Int_Loanno", typeof(string));
    
            var old_Scheme_IdParameter = old_Scheme_Id.HasValue ?
                new ObjectParameter("Old_Scheme_Id", old_Scheme_Id) :
                new ObjectParameter("Old_Scheme_Id", typeof(int));
    
            var old_Scheme_AbrivationParameter = old_Scheme_Abrivation != null ?
                new ObjectParameter("Old_Scheme_Abrivation", old_Scheme_Abrivation) :
                new ObjectParameter("Old_Scheme_Abrivation", typeof(string));
    
            var old_GLHDNAMEParameter = old_GLHDNAME != null ?
                new ObjectParameter("Old_GLHDNAME", old_GLHDNAME) :
                new ObjectParameter("Old_GLHDNAME", typeof(string));
    
            var old_ACCHDCODEParameter = old_ACCHDCODE != null ?
                new ObjectParameter("Old_ACCHDCODE", old_ACCHDCODE) :
                new ObjectParameter("Old_ACCHDCODE", typeof(string));
    
            var old_Scheme_NameParameter = old_Scheme_Name != null ?
                new ObjectParameter("Old_Scheme_Name", old_Scheme_Name) :
                new ObjectParameter("Old_Scheme_Name", typeof(string));
    
            var mny_LoanamtParameter = mny_Loanamt.HasValue ?
                new ObjectParameter("mny_Loanamt", mny_Loanamt) :
                new ObjectParameter("mny_Loanamt", typeof(decimal));
    
            var mny_disbamtParameter = mny_disbamt.HasValue ?
                new ObjectParameter("mny_disbamt", mny_disbamt) :
                new ObjectParameter("mny_disbamt", typeof(decimal));
    
            var dt_disbdateParameter = dt_disbdate.HasValue ?
                new ObjectParameter("dt_disbdate", dt_disbdate) :
                new ObjectParameter("dt_disbdate", typeof(System.DateTime));
    
            var int_rate_intParameter = int_rate_int.HasValue ?
                new ObjectParameter("int_rate_int", int_rate_int) :
                new ObjectParameter("int_rate_int", typeof(decimal));
    
            var int_rate_penalParameter = int_rate_penal.HasValue ?
                new ObjectParameter("int_rate_penal", int_rate_penal) :
                new ObjectParameter("int_rate_penal", typeof(decimal));
    
            var mny_repayamtParameter = mny_repayamt.HasValue ?
                new ObjectParameter("mny_repayamt", mny_repayamt) :
                new ObjectParameter("mny_repayamt", typeof(decimal));
    
            var int_repay_instParameter = int_repay_inst.HasValue ?
                new ObjectParameter("int_repay_inst", int_repay_inst) :
                new ObjectParameter("int_repay_inst", typeof(decimal));
    
            var mny_loanbalParameter = mny_loanbal.HasValue ?
                new ObjectParameter("mny_loanbal", mny_loanbal) :
                new ObjectParameter("mny_loanbal", typeof(decimal));
    
            var vchr_offidParameter = vchr_offid != null ?
                new ObjectParameter("vchr_offid", vchr_offid) :
                new ObjectParameter("vchr_offid", typeof(string));
    
            var vchr_offidCParameter = vchr_offidC != null ?
                new ObjectParameter("vchr_offidC", vchr_offidC) :
                new ObjectParameter("vchr_offidC", typeof(string));
    
            var dt_last_repay_dateParameter = dt_last_repay_date.HasValue ?
                new ObjectParameter("dt_last_repay_date", dt_last_repay_date) :
                new ObjectParameter("dt_last_repay_date", typeof(System.DateTime));
    
            var dt_first_due_dateParameter = dt_first_due_date.HasValue ?
                new ObjectParameter("dt_first_due_date", dt_first_due_date) :
                new ObjectParameter("dt_first_due_date", typeof(System.DateTime));
    
            var int_disb_instParameter = int_disb_inst.HasValue ?
                new ObjectParameter("int_disb_inst", int_disb_inst) :
                new ObjectParameter("int_disb_inst", typeof(decimal));
    
            var int_inst_disbursedParameter = int_inst_disbursed.HasValue ?
                new ObjectParameter("int_inst_disbursed", int_inst_disbursed) :
                new ObjectParameter("int_inst_disbursed", typeof(decimal));
    
            var int_RRParameter = int_RR.HasValue ?
                new ObjectParameter("int_RR", int_RR) :
                new ObjectParameter("int_RR", typeof(decimal));
    
            var dt_RR_DateParameter = dt_RR_Date.HasValue ?
                new ObjectParameter("dt_RR_Date", dt_RR_Date) :
                new ObjectParameter("dt_RR_Date", typeof(System.DateTime));
    
            var vchr_RRCnoParameter = vchr_RRCno != null ?
                new ObjectParameter("vchr_RRCno", vchr_RRCno) :
                new ObjectParameter("vchr_RRCno", typeof(string));
    
            var int_SRParameter = int_SR.HasValue ?
                new ObjectParameter("int_SR", int_SR) :
                new ObjectParameter("int_SR", typeof(decimal));
    
            var dt_SRDateParameter = dt_SRDate.HasValue ?
                new ObjectParameter("dt_SRDate", dt_SRDate) :
                new ObjectParameter("dt_SRDate", typeof(System.DateTime));
    
            var repay_IntervalParameter = repay_Interval != null ?
                new ObjectParameter("Repay_Interval", repay_Interval) :
                new ObjectParameter("Repay_Interval", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Insert_To_tbl_loanreg", vchr_appreceivregnoParameter, int_LoannoParameter, old_Scheme_IdParameter, old_Scheme_AbrivationParameter, old_GLHDNAMEParameter, old_ACCHDCODEParameter, old_Scheme_NameParameter, mny_LoanamtParameter, mny_disbamtParameter, dt_disbdateParameter, int_rate_intParameter, int_rate_penalParameter, mny_repayamtParameter, int_repay_instParameter, mny_loanbalParameter, vchr_offidParameter, vchr_offidCParameter, dt_last_repay_dateParameter, dt_first_due_dateParameter, int_disb_instParameter, int_inst_disbursedParameter, int_RRParameter, dt_RR_DateParameter, vchr_RRCnoParameter, int_SRParameter, dt_SRDateParameter, repay_IntervalParameter);
        }
    
        public virtual ObjectResult<Migration_Select_All_Transaction_Result> Migration_Select_All_Transaction(string dist_Code, string lnaccid)
        {
            var dist_CodeParameter = dist_Code != null ?
                new ObjectParameter("Dist_Code", dist_Code) :
                new ObjectParameter("Dist_Code", typeof(string));
    
            var lnaccidParameter = lnaccid != null ?
                new ObjectParameter("Lnaccid", lnaccid) :
                new ObjectParameter("Lnaccid", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Migration_Select_All_Transaction_Result>("Migration_Select_All_Transaction", dist_CodeParameter, lnaccidParameter);
        }
    }
}
