﻿<%@ Page Title="Application Data Entry - Marriage Loan | View" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="DE_Marriage_Loan_View.aspx.cs" Inherits="KSDCSCST_Portal.DE_Marriage_Loan" %>
<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>To be filled for Marriage Loan</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="Welcome.aspx">Home</a></li>
						  <li class="breadcrumb-item  "><a href="ApplicationDataEntry_List.aspx">Application Data Entry</a></li>
                        <li class="breadcrumb-item active">To be filled for Marriage Loan</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Application Register No*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtApplicationRegNo" disabled="disabled"  placeholder="Application Register No">
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Loanee Name*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtLoanee_Name" disabled="disabled" placeholder="Loanee Name">
                                        </div>
                                    </div>
									
									 
									 
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name of Bride*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtName_Bride" placeholder="Name of Bride">
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Address Of Bride*</label>
                                        <div class="col-sm-9">
                                             <textarea class="form-control" maxlength="200" id="txtAddress_Bride" placeholder="Address Of Bride"></textarea>
                                            <span class="validation_message">Maximum text length is 200</span>
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">Date Of Birth*</label>
                                        <div class="col-sm-9">
                                            <input     type="text" class="dateandtime form-control" id="txtDate_Of_Birth" placeholder="dd/mm/yyyy">
                                        </div>
                                    </div>
									
									
									 
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name of Groom*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtName_Groom" placeholder="Name of Groom">
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Address Of Groom*</label>
                                        <div class="col-sm-9">
                                             <textarea class="form-control" maxlength="200" id="txtAddress_Groom" placeholder="Address Of Groom"></textarea>
                                            <span class="validation_message">Maximum text length is 200</span>
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">Date Of Marriage*</label>
                                        <div class="col-sm-9">
                                            <input     type="text" class="dateandtime form-control" id="txtDate_Of_Marriage" placeholder="dd/mm/yyyy">
                                        </div>
                                    </div>
									 
							

                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                          
                                            <a onclick="Save();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 -->
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <!-- Bootstrap4 Duallistbox -->
    <script src="assets/plugins/bootstrap4-duallistbox/jquery.bootstrap-duallistbox.min.js"></script>
    <!-- InputMask -->
    <script src="assets/plugins/moment/moment.min.js"></script>
    <script src="assets/plugins/inputmask/jquery.inputmask.min.js"></script>
    <!-- date-range-picker -->
    <script src="assets/plugins/daterangepicker/daterangepicker.js"></script>
    <!-- bootstrap color picker -->
    <script src="assets/plugins/bootstrap-colorpicker/js/bootstrap-colorpicker.min.js"></script>
    <!-- Tempusdominus Bootstrap 4 -->
    <script src="assets/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js"></script>
    <!-- BS-Stepper -->
    <script src="assets/plugins/bs-stepper/js/bs-stepper.min.js"></script>
    <!-- dropzonejs -->
    <script src="assets/plugins/dropzone/min/dropzone.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script src="assets/js/jquery.dateandtime.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script>
        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;
        $(document).ready(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            // Get the current date
            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div

            //   Load_All_Districts();

            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            $('#txtDate_Of_Birth').dateAndTime();
            $('#txtDate_Of_Marriage').dateAndTime();

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }

            Load_All_Application_Issue_By_Id(getParameterByName("Id"));

        });

        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }

        function Load_All_Application_Issue_By_Id(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Id: Id }), // If you have parameters
                url: "WebService.asmx/Select_tbl_LoanApp_By_LoanAppId",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        $(".LABEL_APP_Status").text(value.chr_status);
                        var int_loanappid = value.int_loanappid;
                        var Name_Applicant = value.vchr_applname;
                        var Scheme_Id = value.int_schemeid;
                        Cast_Id = value.vchr_caste;
                        Sub_Cast_Id = value.SubCast;
                        var vchr_appreceivregno = value.vchr_appreceivregno;
                        $("#txtLoanee_Name").val(Name_Applicant);
                        $("#txtApplicationRegNo").val(vchr_appreceivregno);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                //Select_All_Districts();
                //  Load_All_Villages();
            });
        }

        function calculateAge(dateOfBirth) {
            // Parse the date of birth string into a Date object
            const dob = new Date(dateOfBirth);

            // Get the current date
            const currentDate = new Date();

            // Calculate the difference in milliseconds
            const ageInMilliseconds = currentDate - dob;

            // Convert the milliseconds to years
            const ageInYears = ageInMilliseconds / (365 * 24 * 60 * 60 * 1000);

            // Round down to the nearest whole number to get the age
            const age = Math.floor(ageInYears);

            return age;
        }

        function Save() {

            var Bride_Name = $("#txtName_Bride");
            var Bride_Address = $("#txtAddress_Bride");
            var Bride_DOB = $("#txtDate_Of_Birth");
            var Groom_Name = $("#txtName_Groom");
            var Groom_Address = $("#txtAddress_Groom");
            var DateOfMarriage = $("#txtDate_Of_Marriage");


            if (Bride_Name.val().trim() == "") {
                Focus_Error(Bride_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Name of Bride is required !'
                })
            }
            else if (!Is_Valid_Text(Bride_Name.val().trim())) {
                Focus_Error(Bride_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed !'
                })
            }
            else if (Bride_Address.val().trim() == "") {
                Focus_Error(Bride_Address);
                Toast.fire({
                    icon: 'error',
                    title: 'Address of Bride is required  !'
                })
            }
            else if (Bride_DOB.val().trim() == "") {
                Focus_Error(Bride_DOB);
                Toast.fire({
                    icon: 'error',
                    title: 'Date of Birth is required !'
                })
            }
            else if (Groom_Name.val().trim() == "") {
                Focus_Error(Groom_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Name of Groom is required !'
                })
            }
            else if (!Is_Valid_Text(Groom_Name.val().trim())) {
                Focus_Error(Groom_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed !'
                })
            }
            else if (Groom_Address.val().trim() == "") {
                Focus_Error(Groom_Address);
                Toast.fire({
                    icon: 'error',
                    title: 'Address of Grooom is required  !'
                })
            }
            else if (DateOfMarriage.val().trim() == "") {
                Focus_Error(DateOfMarriage);
                Toast.fire({
                    icon: 'error',
                    title: 'Date of Marriage is required !'
                })
            }

            else {

                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/Insert_To_tbl_marriageloan",
                    data: JSON.stringify({ int_loanappid: getParameterByName("Id"), int_schemeid: getParameterByName("Scheme_Id"), vchr_bridename: Bride_Name.val(), vchr_brideaddr: Bride_Address.val(), int_brideage: calculateAge(Bride_DOB.val()), dte_bridedob: Bride_DOB.val(), vchr_groomname: Groom_Name.val(), vchr_groomaddr: Groom_Address.val(), dte_dt: DateOfMarriage.val() }),
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {

                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        // Handle AJAX error
                        //   alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);

                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                        })

                    }

                }).done(function () {
                    // Update_Status();
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Application Data Entry Successfully Submitted!',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked

                            location.href = 'ApplicationDataEntry_List.aspx';
                            // Your code here
                        }
                    });
                });


            }







        }

        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }

        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }


        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }



    </script>



</asp:Content>
