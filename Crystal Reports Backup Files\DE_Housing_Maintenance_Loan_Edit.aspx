﻿<%@ Page Title="Application Data Entry - Housing Maintenance Loan | Edit" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="DE_Housing_Maintenance_Loan_Edit.aspx.cs" Inherits="KSDCSCST_Portal.DE_Housing_Maintenance_Loan_Edit" %>




<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <a href="DE_Housing_Maintenance_Loan_Edit.aspx">DE_Housing_Maintenance_Loan_Edit.aspx</a>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>To be filled for House Renovation Loan</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="Welcome.aspx">Home</a></li>
						  <li class="breadcrumb-item  "><a href="ApplicationDataEntry_List.aspx">Application Data Entry</a></li>
                        <li class="breadcrumb-item active">To be filled for House Renovation Loan</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">

                                    
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Application Register No*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtApplicationRegNo" disabled="disabled"  placeholder="Application Register No">
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Loanee Name*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtLoanee_Name" disabled="disabled" placeholder="Loanee Name">
                                        </div>
                                    </div>
									 
									
									 
									 
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name of current working organisation*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtName_Working_Organisation" placeholder="Name of current working organisation">
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Organisation Address*</label>
                                        <div class="col-sm-9">
                                             <textarea class="form-control" maxlength="200" id="txtOrganisation_Address" placeholder="Organisation Address"></textarea>
                                            <span class="validation_message">Maximum text length is 200</span>
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Current Designation*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtCurrent_Designation" placeholder="Current Designation">
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Pay Scale*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtPay_Scale" placeholder="Pay Scale">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Total Salary*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtTotal_Salary" placeholder="Total Salary">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">House Number*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtHouse_Number" placeholder="House Number">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Property Survey / Resurvey Number*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtProperty_Survey_Or_Resurvey_Number" placeholder="Current Designation">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Details of the property(Land Owners name,House owners name and address*</label>
                                        <div class="col-sm-9">
                                             <textarea class="form-control" maxlength="200" id="txtDetails_Of_Property" placeholder="Details of the property(Land Owners name,House owners name and address"></textarea>
                                            <span class="validation_message">Maximum text length is 200</span>
                                        </div>
                                    </div>
									
									 
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">If applicant is not the owner then their relation*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtRelation_With_Owner" placeholder="If applicant is not the owner then their relation">
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Estimate for the renovation*</label>
                                        <div class="col-sm-9">
                                            <input   type="number " class="form-control" id="txtEstimate_For_Renovation" placeholder="Estimate for the renovation">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Amount required as loan*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtLoan_Amount" placeholder="Amount required as loan">
                                        </div>
                                    </div>
									
									
									 
							

                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                            
                                            <a onclick="Save();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 -->
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <!-- Bootstrap4 Duallistbox -->
    <script src="assets/plugins/bootstrap4-duallistbox/jquery.bootstrap-duallistbox.min.js"></script>
    <!-- InputMask -->
    <script src="assets/plugins/moment/moment.min.js"></script>
    <script src="assets/plugins/inputmask/jquery.inputmask.min.js"></script>
    <!-- date-range-picker -->
    <script src="assets/plugins/daterangepicker/daterangepicker.js"></script>
    <!-- bootstrap color picker -->
    <script src="assets/plugins/bootstrap-colorpicker/js/bootstrap-colorpicker.min.js"></script>
    <!-- Tempusdominus Bootstrap 4 -->
    <script src="assets/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js"></script>
    <!-- BS-Stepper -->
    <script src="assets/plugins/bs-stepper/js/bs-stepper.min.js"></script>
    <!-- dropzonejs -->
    <script src="assets/plugins/dropzone/min/dropzone.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script src="assets/js/jquery.dateandtime.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script>
        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;
        $(document).ready(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            // Get the current date
            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtApplication_Date').val(formattedDate);

      
            //   Load_All_Districts();

            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }

            Load_All_Application_Issue_By_Id(getParameterByName("Id"));

        });


        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }

        function Load_All_Application_Issue_By_Id(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Id: Id }), // If you have parameters
                url: "WebService.asmx/Select_tbl_LoanApp_By_LoanAppId",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        $(".LABEL_APP_Status").text(value.chr_status);
                        var int_loanappid = value.int_loanappid;
                        var Name_Applicant = value.vchr_applname;
                        var Scheme_Id = value.int_schemeid;
                        Cast_Id = value.vchr_caste;
                        Sub_Cast_Id = value.SubCast;
                        var vchr_appreceivregno = value.vchr_appreceivregno;
                        $("#txtLoanee_Name").val(Name_Applicant);
                        $("#txtApplicationRegNo").val(vchr_appreceivregno);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Housing_Maintenance_Loan_By_Id(getParameterByName("Id"))
            });
        }


        function Load_All_Housing_Maintenance_Loan_By_Id(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ int_loanappid: Id }), // If you have parameters
                url: "WebService.asmx/Select_tbl_hsemaintanenceloan_By_int_loanappid",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        // $(".LABEL_APP_Status").text(value.chr_status);
                        $("#txtName_Working_Organisation").val(value.vchr_hse_workorgname);
                        $("#txtOrganisation_Address").val(value.vchr_hse_workorgaddr);
                        $("#txtCurrent_Designation").val(value.vchr_hse_designation);
                        $("#txtPay_Scale").val(value.vchr_hse_scaleofpay);
                        $("#txtTotal_Salary").val(value.int_hse_netsal);
                        $("#txtHouse_Number").val(value.vchr_hseno);
                        $("#txtProperty_Survey_Or_Resurvey_Number").val(value.vchr_surveyno);
                        $("#txtDetails_Of_Property").val(value.vchr_propdetails);
                        $("#txtRelation_With_Owner").val(value.vchr_hse_relation);
                        $("#txtEstimate_For_Renovation").val(value.int_estamt);
                        $("#txtLoan_Amount").val(value.int_amtreq);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }

        function Save() {

            var Working_Organisation = $("#txtName_Working_Organisation");
            var Organisation_Address = $("#txtOrganisation_Address");
            var Designation = $("#txtCurrent_Designation");
            var Pay_Scale = $("#txtPay_Scale");
            var Total_Salary = $("#txtTotal_Salary");
            var House_Number = $("#txtHouse_Number");
            var Survey_Number = $("#txtProperty_Survey_Or_Resurvey_Number");
            var Property_Details = $("#txtDetails_Of_Property");
            var Relation_With_Owner = $("#txtRelation_With_Owner");
            var Estimate_For_Renovation = $("#txtEstimate_For_Renovation");
            var Loan_Amount = $("#txtLoan_Amount");

            if (Working_Organisation.val().trim() == "") {
                Focus_Error(Working_Organisation);
                Toast.fire({
                    icon: 'error',
                    title: 'Name of Current Working Organisation is required !'
                })
            }

            else if (Organisation_Address.val().trim() == "") {
                Focus_Error(Organisation_Address);
                Toast.fire({
                    icon: 'error',
                    title: 'Address of Organisation is required  !'
                })
            }
            else if (Designation.val().trim() == "") {
                Focus_Error(Designation);
                Toast.fire({
                    icon: 'error',
                    title: 'Designation is required !'
                })
            }
            else if (Pay_Scale.val().trim() == "") {
                Focus_Error(Pay_Scale);
                Toast.fire({
                    icon: 'error',
                    title: 'Pay Scale is required !'
                })
            }
            else if (Total_Salary.val().trim() == "") {
                Focus_Error(Total_Salary);
                Toast.fire({
                    icon: 'error',
                    title: 'Total Salary is required !'
                })
            }
            else if (House_Number.val().trim() == "") {
                Focus_Error(House_Number);
                Toast.fire({
                    icon: 'error',
                    title: 'House Number is required !'
                })
            }
            else if (Survey_Number.val().trim() == "") {
                Focus_Error(Survey_Number);
                Toast.fire({
                    icon: 'error',
                    title: 'Survey / Resurvey Number is required  !'
                })
            }
            else if (Property_Details.val().trim() == "") {
                Focus_Error(Property_Details);
                Toast.fire({
                    icon: 'error',
                    title: 'Details of Property is required  !'
                })
            }
            else if (Estimate_For_Renovation.val().trim() == "") {
                Focus_Error(Estimate_For_Renovation);
                Toast.fire({
                    icon: 'error',
                    title: 'Estimate for the Renovation is required  !'
                })
            }
            else if (Loan_Amount.val().trim() == "") {
                Focus_Error(Loan_Amount);
                Toast.fire({
                    icon: 'error',
                    title: 'Amount required as Loan is required  !'
                })
            }
            else {

                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/Insert_To_tbl_hsemaintanenceloan",
                    data: JSON.stringify({ int_loanappid: getParameterByName("Id"), int_schemeid: getParameterByName("Scheme_Id"), vchr_hse_workorgname: Working_Organisation.val(), vchr_hse_workorgaddr: Organisation_Address.val(), vchr_hse_designation: Designation.val(), vchr_hse_scaleofpay: Pay_Scale.val(), int_hse_netsal: Total_Salary.val(), vchr_hseno: House_Number.val(), vchr_surveyno: Survey_Number.val(), vchr_propdetails: Property_Details.val(), vchr_hse_relation: Relation_With_Owner.val().trim(), int_estamt: Estimate_For_Renovation.val(), int_amtreq: Loan_Amount.val(), int_gross: 0 }),
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {

                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        // Handle AJAX error
                        //   alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);

                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                        })

                    }

                }).done(function () {
                    // Update_Status();
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Application Data Entry Successfully Updated!',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked

                            location.href = 'ApplicationDataEntry_Modification_List.aspx';
                            // Your code here
                        }
                    });
                });


            }







        }

        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }


        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }


        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }



    </script>



</asp:Content>

