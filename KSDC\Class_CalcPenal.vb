﻿Imports System.Data.SqlClient
Imports System.Security.AccessControl
Imports Microsoft.ApplicationBlocks.Data

Public Class Class_CalcPenal


    Dim strconn As String = "data source=localhost\SQLEXPRESS;initial catalog=KSDC_SMART_LIVE;user id=sa;password=************;Connect Timeout=300000;"
    'Dim strconn As String = "data source=.\SQL2019;initial catalog=KSDC_SMART_LIVE;user id=sa;password=********;Connect Timeout=300000;"
    ' Dim strconn As String = "data source=SQL5109.site4now.net;initial catalog=db_a7a28a_ksdc;user id=db_a7a28a_ksdc_admin;password=************;Connect Timeout=300000;"


    '  Dim strconn As String = "data source=SQL5109.site4now.net;initial catalog=db_a7a28a_ksdc;user id=db_a7a28a_ksdc_admin;password=************;Connect Timeout=30;"

    Function CalcPenal(ByVal Curdate As Date, ByVal loanno As String)
        Dim <PERSON>t, Dmdamt, instamt, Dbtamt, pnl, bamt, fr As Double
        Dim fdate, duedt, dte, lduedate, last_repay_date As Date
        Dim dt1 As DataTable
        Dim m, i, n, intgr
        Dim dr As DataRow
        Dim offid
        pnl = 0 : n = 0 : m = 0
        'offid = "0," & Session("officeid")
        Dim PeriodExt As Integer
        'Dim rvcdflag As Boolean
        'Dim rvcddate As Date
        'Dim strconn As String
        Dim ds As DataSet
        Dim txtr As String

        Dim pnlrate As Double
        txtr = ""

        ' strconn = "data source=DESKTOP-JP80BH3\SQLEXPRESS;initial catalog=ksdc_live_latest;user id=sa;password=*******;Connect Timeout=300000;"
        'strconn = "data source=WIN-0332MKNE9CR\SQLEXPRESS;initial catalog=KSDC;user id=sa;password=*******;Connect Timeout=300000;"
        '  strconn = "data source=VIVEK;initial catalog=ksdc_live_latest;user id=sa;password=*******;Connect Timeout=300000;"

        'strconn = "data source=VIVEK;initial catalog=ksdc_live_latest;user id=sa;password=*******;Connect Timeout=300000;"
        'strconn = "data source=SQL5109.site4now.net;initial catalog=db_a7a28a_ksdc;user id=db_a7a28a_ksdc_admin;password=************;Connect Timeout=30;"
        '------------------------------------------------------
        ds = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select * from tbl_loanreg where int_loanno='" & loanno & "' and dt_first_due_date is not null")
        If ds.Tables(0).Rows.Count > 0 Then
            dr = ds.Tables(0).Rows(0)
            fdate = dr("dt_first_due_date")
            last_repay_date = dr("dt_last_repay_date")
            instamt = dr("mny_repayamt")
            pnlrate = dr("int_rate_penal")
            lduedate = DateAdd(DateInterval.Month, dr("int_repay_inst") - 1, fdate)
            PeriodExt = dr("int_repay_inst_Ext")
            'If dr("int_rvcd") = 1 Then
            '    rvcdflag = True
            '    rvcddate = dr("dt_rvcd_date")
            'Else
            '    rvcdflag = False
            'End If
        End If

        Dim ds2 As DataSet = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select dt_Transaction from tbl_loanTrans where int_loanno = '" & loanno & "' and int_type<=20  and int_type not in (4,5,12) order by dt_transaction,int_transid") 'and chr_remark <> 'FINANCIAL YEAR END'
        If ds2.Tables(0).Rows.Count > 0 Then
            Dim drT As DataRow = ds2.Tables(0).Rows(ds2.Tables(0).Rows.Count - 1)
            last_repay_date = drT("dt_Transaction")
        End If

        If txtr = "" Then

            ds = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select sum(int_amt),sum(int_penal_r),sum(int_bank_charge_r) from tbl_loantrans where int_loanno='" & loanno & "' and chr_transtype='Receipt' and int_type in (1,2,3,6,15,22) and dt_transaction  <='" & (Curdate.ToString("yyyy-MM-dd HH:mm:ss.fff")) & "'")

            If ds.Tables.Count > 0 Then
                If ds.Tables(0).Rows.Count > 0 Then
                    For Each dr In ds.Tables(0).Rows
                        'If Not IsDBNull(dr(0)) Then Rcvamt = dr(0) - (dr(1) + dr(2))
                        If Not IsDBNull(dr(0)) Then Rcvamt = dr(0)
                        txtr = Rcvamt
                    Next
                End If
            End If

        End If
        '--------------------------

        If Month(Curdate) = 2 And Day(fdate) > 28 Then
            duedt = DateSerial(Year(Curdate), Month(Curdate), 28)
        ElseIf (Month(Curdate) = 4 Or Month(Curdate) = 6 Or Month(Curdate) = 9 Or Month(Curdate) = 11) And Day(fdate) > 30 Then
            duedt = DateSerial(Year(Curdate), Month(Curdate), 30)
        Else
            duedt = DateSerial(Year(Curdate), Month(Curdate), Day(fdate))
        End If
        'duedt = DateSerial(Year(Curdate), Month(Curdate), Day(fdate))

        If duedt > Curdate Then duedt = DateAdd(DateInterval.Month, -1, duedt)
        If duedt > lduedate Then duedt = lduedate

        ''Dmdamt = (DateDiff(DateInterval.Month, fdate, duedt) + 1) * instamt
        Dmdamt = ((DateDiff(DateInterval.Month, fdate, duedt) + 1) - PeriodExt) * instamt


        Rcvamt = CDbl(txtr)


        ' Need to Delete this

        'Rcvamt = 0


        If Rcvamt >= Dmdamt Then Exit Function
        'If Rcvamt - Dbtamt >= Dmdamt Then Exit Function

        '--------------------------
        ds = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "select * FROM tbl_holiday WHERE dte_date>='" & (fdate.ToString("yyyy-MM-dd HH:mm:ss.fff")) & "' and dte_date<='" & (Curdate.ToString("yyyy-MM-dd HH:mm:ss.fff")) & "'")
        Dim dColumn As DataColumn = ds.Tables(0).Columns("dte_date")
        ds.Tables(0).PrimaryKey = New DataColumn() {dColumn}
        '--------------------------
        'bamt = Dmdamt - (Rcvamt - Dbtamt)
        bamt = Dmdamt - (Rcvamt)
        'bamt = 7600
        intgr = Decimal.Truncate(bamt / instamt)
        fr = bamt Mod instamt
        If intgr > 0 And fr > 0 Then m = intgr + 1
        If intgr > 0 And fr = 0 Then m = intgr
        If intgr <= 0 And fr > 0 Then m = 1

        Dim ary(m - 1, 2)
        Dim j As Integer : j = 0
        '--------------------------
        If fr > 0 Then : i = 1 : Else : i = 0 : End If
        n = 0
        If intgr > 0 Then
            dte = duedt
            Do Until m - 1 < i
                If n = 1 Then dte = DateAdd("m", DateDiff(DateInterval.Month, fdate, dte) - 1, fdate)
                dte = DateAdd(DateInterval.Month, -(j), duedt)
                If dte <= last_repay_date Then
                    dte = last_repay_date
                End If
                n = 0
jmp2:           Dim dr2 As DataRow = ds.Tables(0).Rows.Find(dte)
                If Not dr2 Is Nothing Then
                    If Month(DateAdd("d", 1, dte)) > Month(dte) Then n = 1
                    dte = DateAdd("d", 1, dte)
                    GoTo jmp2
                End If
                pnl = pnl + (instamt * DateDiff(DateInterval.Day, dte, Curdate) * pnlrate / 36500)
                ary(m - 1, 0) = dte
                ary(m - 1, 1) = instamt
                ary(m - 1, 2) = (instamt * DateDiff(DateInterval.Day, dte, Curdate) * pnlrate / 36500)
                m = m - 1
                j = j + 1
            Loop
        End If
        '--------------------------

        If fr > 0 Then
            dte = DateAdd(DateInterval.Month, -(intgr), duedt)
            If dte <= last_repay_date Then
                dte = last_repay_date
            End If
jmp1:       Dim dr1 As DataRow = ds.Tables(0).Rows.Find(dte)
            If Not dr1 Is Nothing Then
                dte = DateAdd("d", 1, dte)
                n = 1
                GoTo jmp1
            End If
            pnl = pnl + (fr * DateDiff(DateInterval.Day, dte, Curdate) * pnlrate / 36500)
            ary(0, 0) = dte
            ary(0, 1) = fr
            ary(0, 2) = (fr * DateDiff(DateInterval.Day, dte, Curdate) * pnlrate / 36500)

        End If
        '--------------------------
        Return pnl
    End Function

    Function GetFinancialYearEndings(ByVal startDate As Date, ByVal endDate As Date) As List(Of Date)
        Dim financialYearEndings As New List(Of Date)

        Dim currentYear As Integer = startDate.Year
        Dim endYear As Integer = endDate.Year

        While currentYear <= endYear
            Dim financialYearEnd As Date
            Dim nextYearStartDate As Date = New Date(currentYear + 1, 4, 1)

            If startDate <= nextYearStartDate Then
                financialYearEnd = New Date(currentYear, 3, 31)
            Else
                financialYearEnd = New Date(currentYear + 1, 3, 31)
            End If

            If financialYearEnd >= startDate AndAlso financialYearEnd <= endDate Then
                financialYearEndings.Add(financialYearEnd)
            End If

            currentYear += 1
        End While

        Return financialYearEndings
    End Function

    Function SAV(ByVal Amount As String, ByVal RecptNo As String, ByVal LoanNo As String, ByVal Transaction_Date As DateTime, ByVal lst_type As String, ByVal lst_mode As String, ByVal Chequeno As String, ByVal Remark As String, ByVal UserName As String, ByVal LoneeeName As String, ByVal FCODE As String, ByVal OfficeId As String, ByVal Trans_Amt As String, ByVal Revenue As String, ByVal RRC As String, ByVal RRC_Perc As String)
        ' filldata()
        ' binddata()
        ' ltransdate = Session("lasttransdate")
        ''Dim len1 As Integer
        ''Dim genrecno As String
        'If CDbl(Amount) <= 0 Then Exit Sub
        If CDbl(Amount) <= 0 Then Exit Function
        Dim dr, dr1, dr2 As DataRow
        Dim rmtd_amt, int_r, int_t, pnl_r, pnl_t, eduemi, edubal, int_tD, int_tots, pnl_t_ots, DPPenal, WaiverAmount, PenalWaiver, InterestWaiver, CurrentPenal, CurrentInt As Double '//27/03/2024
        Dim bnk_r, bnk_b, prin_b, prin_r, blamt, g1 As Double
        Dim rcpt As String
        Dim g0, ENDDATEM As Date
        Dim i, m As Integer
        Dim q As Integer = 0
        pnl_t_ots = 0
        Dim per1, intdues As Integer
        per1 = 0
        intdues = 0
        CurrentInt = 0
        CurrentPenal = 0
        WaiverAmount = 0 '//27/03/2024
        PenalWaiver = 0 '//27/03/2024
        InterestWaiver = 0 '//27/03/2024
        Dim light As String
        Dim otsflag = False
        Dim disbdate, nxtduedate, lstdate, fduedt, ltransdate As Date
        Dim CurTransDate, PrevTransDate, PeriodOverDate As Date
        Dim loanbal, Tpnl, int_prv, bankchrge_b, notc_t, other_t, greencard, int_prvots, int_prv_penal, int_prefixbal, txtchrge_b, defaultintrate As Double
        Dim TransRemarks As String = Remark
        Dim int_BeforeDisb As Integer = 0 '25/06/2024
        '---------------------------------------------
        Dim total_Amount, paid_amount As Integer

        If Revenue = "on" Then


            total_Amount = CInt(Amount)
            paid_amount = CInt(Trans_Amt)
            Dim revenue_Recovery As Integer
            revenue_Recovery = paid_amount - total_Amount
        End If
        If lst_type = 2 Then
            SqlHelper.ExecuteNonQuery(strconn, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time,vchr_offidC)VALUES('" & RecptNo & "','" & "Payment" & "'," & "21501" & ",'" & "Revenue Recovery Charges" & "','" & lst_mode & "'," & revenue_Recovery & ",'" & CDate(Transaction_Date) & "','" & LoanNo & "','" & LoneeeName & "','" & "Revenue Recovery Charges" & "','" & Mid(LoanNo, 1, 4) & "','" & UserName & "',GETDATE(),'" & Mid(LoanNo, 1, 4) & "')")
        End If

        'Dim ENDDATE As Date = SqlHelper.ExecuteScalar(strconn, CommandType.Text, "Select GETDATE()")
        'Dim TDate() As String = Transaction_Date.Split("/")
        'Dim TDate As Date = Date.ParseExact(Transaction_Date, "dd/MM/yyyy", CultureInfo.InvariantCulture)
        'Dim ENDDATE As Date = (TDate(1) & "/" & TDate(0) & "/" & TDate(2))

        Dim ENDDATE As Date = CDate(Transaction_Date)

        Dim ds, ds2, ds3, ds1 As DataSet
        ' Dim strconn As String
        ' strconn = System.Configuration.ConfigurationManager.AppSettings("constr")
        'strconn = "data source=.\SQL2019;initial catalog=ksdc_live_latest;user id=sa;password=********;Connect Timeout=300000;"
        Dim conn As New SqlConnection(strconn)

        If RecptNo.Trim = "" Then
            ' myMsgBox("Please enter the Receipt No.")
            '  Exit Function 28/03/2024
        End If
        If Amount.Trim = "" Or Amount.Trim = 0 Then
            '  myMsgBox("Invalid Amount !!")
            Exit Function
        End If



        ''Closure Check

        'Dim totaldues As Integer 29/03/2024
        'totaldues = calc_ondate(LoanNo)

        'If Val(Amount) >= totaldues Then
        '    ds1 = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select int_ForClosing,int_Green,mny_repayamt from tbl_loanreg where  int_loanno ='" & LoanNo & "'")
        '    If ds1.Tables(0).Rows.Count > 0 Then
        '        dr1 = ds1.Tables(0).Rows(0)
        '        If dr1(0) = 0 Then
        '            If dr1(2) <> 0 Then
        '                'myMsgBox("Get Approval for Loan Closure from D.M/A.M !!!")
        '                Amount = 0
        '                'btnsave.Enabled = True
        '                Exit Function
        '            End If
        '        End If
        '    End If
        'End If


        'ENDDATE = CDate(txtm.Text + "/" + txtd.Text + "/" + txty.Text)
        ds = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select * from tbl_loanreg where int_loanno='" & LoanNo & "'")
        If ds.Tables.Count > 0 Then
            If ds.Tables(0).Rows.Count > 0 Then
                dr = ds.Tables(0).Rows(0)
                'fdate = dr("dt_first_due_date")
                'instamt = dr("mny_repayamt")
                'lduedate = DateAdd(DateInterval.Month, dr("int_repay_inst") - 1, fdate)
                'PeriodExt = 0
                OfficeId = dr("vchr_offid")
                disbdate = dr("dt_disbdate")
            End If
        End If

        If ENDDATE < disbdate Or ENDDATE > Date.Today Then
            '   myMsgBox("Invalid Date !!!. Please check")
            ' setFocus(txtd)
            Exit Function

        End If
        If ltransdate > ENDDATE Then
            ' myMsgBox("Invalid Date !!! Transaction on " & ltransdate.Day & "/" & ltransdate.Month & "/" & ltransdate.Year & " already exist. Please check")
            '  setFocus(txtd)
            Exit Function
        End If

        '''Receipt No - Migration 25/04/2024  ' UNCOMMENTED ON 05/01/2024 - SAV = SAV_M

        If lst_type = "3" Then
            ds = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "select JV_No FROM tbl_Sub_Districts WHERE Office_Code like '" & OfficeId & "'")
            If ds.Tables(0).Rows.Count > 0 Then
                dr = ds.Tables(0).Rows(0)
                RecptNo = dr("JV_No") + 1

            End If
        Else
            ds = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "select Last_Receipt_No FROM tbl_Sub_Districts WHERE Office_Code like '" & OfficeId & "'")
            If ds.Tables(0).Rows.Count > 0 Then
                dr = ds.Tables(0).Rows(0)
                RecptNo = dr("Last_Receipt_No") + 1

            End If
        End If





        '  Dim strconn2 As String
        '  strconn = System.Configuration.ConfigurationManager.AppSettings("constr")
        Dim fdate, duedt, dte, lduedate As Date
        Dim Rcvamt, Dmdamt, instamt, Dbtamt, pnl, amt, TxtBal, intrate, penalrate, TXTIB, TXTPNLB, txtdefaulttotal, intemi_bal As Double
        '------------------------
        ds = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select * from tbl_loanreg where int_loanno='" & LoanNo & "'")
        If ds.Tables.Count > 0 Then
            If ds.Tables(0).Rows.Count > 0 Then
                dr = ds.Tables(0).Rows(0)
                fdate = dr("dt_first_due_date")
                PeriodOverDate = fdate.AddMonths(dr("int_repay_inst")).AddDays(-1)
                instamt = dr("mny_repayamt")
                lduedate = DateAdd(DateInterval.Month, dr("int_repay_inst") - 1, fdate)
                'lduedate = Format(lduedate, "dd/MM/yyyy")
                TxtBal = Format(dr("mny_loanbal"), "0.00")
                ltransdate = dr("dt_last_repay_date")
                intrate = Format(dr("int_rate_int"), "0.00")
                penalrate = Format(dr("int_rate_penal"), "0.00")
                intemi_bal = Format(dr("mny_intrbal"), "0.00")
                defaultintrate = intrate + penalrate
                'PeriodExt = dr("int_repay_inst_Ext")
                'If PeriodExt > 0 And lbl_rr.Text.Trim <> "Moratorium Loan" Then
                '    lbl_rr.Text = lbl_rr.Text & "  Moratorium Loan"
                'End If
                'If dr("int_inst_disbursed") = dr("int_disb_inst") And dr("mny_repayamt") <> 0 Then
                '    dsbcomplt = 1
                'End If
                If dr("int_ots") = 1 Then
                    otsflag = True
                    ds1 = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select * from tbl_WaiverDetails where int_loanno='" & LoanNo & "'")
                    If ds1.Tables.Count > 0 Then
                        If ds1.Tables(0).Rows.Count > 0 Then
                            dr1 = ds1.Tables(0).Rows(0)
                            WaiverAmount = dr1("int_wavier_amt")
                        End If
                    End If
                End If
            End If
        End If



        '---------------------------------------------Case.1 Not fully Disbursed 25/06/2024
        If TransRemarks = "SUBSIDY" Then
            If SqlHelper.ExecuteScalar(strconn, CommandType.Text, "Select ISNULL(COUNT(*),0) FROM tbl_LOANTRANS WHERE INT_LOANNO ='" & LoanNo & "' AND int_type=0 AND chr_remark='LOAN DISBURSED'") <= 0 Then '26/06/2024
                int_BeforeDisb = 1
            End If

            If int_BeforeDisb = 1 Then
                conn.Open()
                Dim transBD As SqlTransaction = conn.BeginTransaction()

                Try
                    SqlHelper.ExecuteNonQuery(transBD, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_prin_dues,chr_remark, int_type, vchr_offidC)VALUES('" & LoanNo & "','" & CDate(ENDDATE) & "','" & RecptNo & "','" & lst_mode & "','" & Chequeno & "','Receipt'," & CDbl(Amount) & "," & CDbl(Amount) & "," & CDbl(TxtBal) - CDbl(Amount) & ",'SUBSIDY',0,'" & Mid(LoanNo, 1, 4) & "')")
                    If lst_type = "3" Then
                        SqlHelper.ExecuteNonQuery(transBD, CommandType.Text, "UPDATE tbl_Sub_Districts SET JV_No =JV_No + 1 WHERE Office_Code like '" & Mid(LoanNo, 1, 4) & "'")
                    Else
                        SqlHelper.ExecuteNonQuery(transBD, CommandType.Text, "UPDATE tbl_Sub_Districts SET Last_Receipt_No =Last_Receipt_No + 1 WHERE Office_Code like '" & Mid(LoanNo, 1, 4) & "'")
                    End If



                    ds = SqlHelper.ExecuteDataset(transBD, CommandType.Text, "SELECT * FROM TBL_ACCOUNT WHERE CHR_UNDER='" & FCODE & "' AND chr_head='Minor' and chr_type='Receipt' and chr_Name='PRINCIPAL' ")
                    If ds.Tables(0).Rows.Count > 0 Then
                        dr = ds.Tables(0).Rows(0)
                        SqlHelper.ExecuteNonQuery(transBD, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time,vchr_offidC)VALUES('" & RecptNo & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode & "'," & CDbl(Amount) & ",'" & CDate(ENDDATE) & "','" & LoanNo & "','" & Left(LoneeeName, 48) & "','SUBSIDY','" & Mid(LoanNo, 1, 4) & "','" & UserName & "',GETDATE(),'" & Mid(LoanNo, 1, 4) & "')")
                    End If

                    SqlHelper.ExecuteNonQuery(transBD, CommandType.Text, "UPDATE tbl_loanreg set mny_loanbal=" & CDbl(TxtBal) - CDbl(Amount) & ",mny_prin_due=" & CDbl(TxtBal) - CDbl(Amount) & ",dt_last_repay_date='" & CDate(ENDDATE) & "' where int_loanno='" & LoanNo & "'")

                    transBD.Commit()

                    conn.Close()
                    conn.Dispose()
                    transBD.Dispose()
                    Exit Function
                Catch ex As Exception
                    transBD.Rollback()
                    transBD.Dispose()
                    ' Response.Redirect("ErrorPage.aspx")
                    Throw ex
                Finally
                    conn.Close()
                    conn.Dispose()
                    transBD.Dispose()
                End Try
            End If
        End If

        '**********if credit balance

        If CDbl(TxtBal) = 0 Then
            '  txt1.Text = Amount
            '  txt10.Text = Amount
            conn.Open()
            Dim trans2 As SqlTransaction = conn.BeginTransaction()

            Try

                If lst_type = 0 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate(ENDDATE) & "','" & RecptNo & "','" & lst_mode & "','" & Chequeno & "','Receipt'," & CDbl(Amount) & ",0,0,0,0,0,0,0,0,0,0,'" & "''" & "','" & TransRemarks & "',1,'" & Mid(LoanNo, 1, 4) & "')")
                If lst_type = 1 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate(ENDDATE) & "','" & RecptNo & "','" & lst_mode & "','" & Chequeno & "','Receipt'," & CDbl(Amount) & ",0,0,0,0,0,0,0,0,0,0,'" & "''" & "','" & TransRemarks & "',6,'" & Mid(LoanNo, 1, 4) & "')")
                If lst_type = 2 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate(ENDDATE) & "','" & RecptNo & "','" & lst_mode & "','" & Chequeno & "','Receipt'," & CDbl(Amount) & ",0,0,0,0,0,0,0,0,0,0,'" & "''" & "','" & TransRemarks & "',15,'" & Mid(LoanNo, 1, 4) & "')")
                If lst_type = 3 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate(ENDDATE) & "','" & RecptNo & "','" & lst_mode & "','" & Chequeno & "','Receipt'," & CDbl(Amount) & ",0,0,0,0,0,0,0,0,0,0,'" & "''" & "','" & TransRemarks & "',1,'" & Mid(LoanNo, 1, 4) & "')")
                SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_balance(int_loanno,chr_rec_no,int_bal)VALUES('" & LoanNo & "','" & RecptNo & "'," & CDbl(Amount) & ")")

                If lst_type = "3" Then
                    SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "UPDATE tbl_Sub_Districts SET JV_No =JV_No + 1 WHERE Office_Code like '" & Mid(LoanNo, 1, 4) & "'")
                Else
                    SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "UPDATE tbl_Sub_Districts SET Last_Receipt_No =Last_Receipt_No + 1 WHERE Office_Code like '" & Mid(LoanNo, 1, 4) & "'")
                End If

                '  SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "UPDATE tbl_Sub_Districts SET Last_Receipt_No =Last_Receipt_No + 1 WHERE Office_Code like '" & Mid(LoanNo, 1, 4) & "'") ' 27-04-2024

                ds = SqlHelper.ExecuteDataset(trans2, CommandType.Text, "SELECT * FROM TBL_ACCOUNT WHERE CHR_UNDER='" & FCODE & "' AND chr_head='Minor' and chr_type='Receipt'")
                If ds.Tables(0).Rows.Count > 0 Then
                    dr = ds.Tables(0).Rows(0)
                    SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time,vchr_offidC)VALUES('" & RecptNo & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode & "'," & CDbl(Amount) & ",'" & CDate(ENDDATE) & "','" & LoanNo & "','" & Left(LoneeeName, 48) & "','REPAYMENT','" & Mid(LoanNo, 1, 4) & "','" & UserName & "',GETDATE(),'" & Mid(LoanNo, 1, 4) & "')")
                End If

                trans2.Commit()

                conn.Close()
                conn.Dispose()
                trans2.Dispose()
                Exit Function
            Catch ex As Exception
                trans2.Rollback()
                trans2.Dispose()
                ' Response.Redirect("ErrorPage.aspx")
                Throw ex
            Finally
                conn.Close()
                conn.Dispose()
                trans2.Dispose()
            End Try
        End If

        '**********Case.2(a) LEDGER POSTING ON 31 MARCH *************
        conn.Open()


        If fdate <> CDate("01/01/1900") Then
            If ENDDATE > PeriodOverDate Then

                PrevTransDate = ltransdate
                CurTransDate = PeriodOverDate

                Dim financialYearEndings As List(Of Date) = GetFinancialYearEndings(PrevTransDate, CurTransDate)
                financialYearEndings.Add(PeriodOverDate)
                financialYearEndings.Sort()
                For Each fyEnding In financialYearEndings

                    If fyEnding = PeriodOverDate Then
                        Dim trans3 As SqlTransaction = conn.BeginTransaction()
                        Try
                            ds = SqlHelper.ExecuteDataset(trans3, CommandType.Text, "select * FROM tbl_LOANTRANS WHERE INT_LOANNO ='" & LoanNo & "' AND dt_transaction='" & PeriodOverDate & "' AND chr_remark='PERIOD OVER'")
                            If ds.Tables(0).Rows.Count = 0 Then
                                int_t = 0
                                pnl_t = 0
                                pnl_t = CalcPenal(PeriodOverDate, LoanNo)
                                CurrentPenal = pnl_t
                                'If int_prv = 0 Then
                                ds2 = SqlHelper.ExecuteDataset(trans3, CommandType.Text, "Select int_int_dues as InterestDues,int_penal_dues as PenalDues from tbl_loanTrans where int_loanno = '" & LoanNo & "' and int_type<=20 and chr_remark <> 'FINANCIAL YEAR END1' and int_type not in (4,5,12) order by dt_transaction,int_transid")
                                dr1 = ds2.Tables(0).Rows(ds2.Tables(0).Rows.Count - 1)

                                int_prv = dr1("InterestDues")
                                int_prv_penal = dr1("PenalDues")
                                'End If
                                int_t = int_prv + System.Math.Round((CDbl(TxtBal) * (DateDiff(DateInterval.Day, ltransdate, PeriodOverDate) * intrate) / 36500), 2)
                                CurrentInt = System.Math.Round((CDbl(TxtBal) * (DateDiff(DateInterval.Day, ltransdate, PeriodOverDate) * intrate) / 36500), 2)
                                '05/01/2024
                                pnl_t = pnl_t + int_prv_penal
                                '
                                If lst_type = 0 Then SqlHelper.ExecuteNonQuery(trans3, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & PeriodOverDate & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','PERIOD OVER',10,'" & Mid(LoanNo, 1, 4) & "')")
                                If lst_type = 1 Then SqlHelper.ExecuteNonQuery(trans3, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & PeriodOverDate & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','PERIOD OVER',9,'" & Mid(LoanNo, 1, 4) & "')")
                                If lst_type = 2 Then SqlHelper.ExecuteNonQuery(trans3, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & PeriodOverDate & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','PERIOD OVER',18,'" & Mid(LoanNo, 1, 4) & "')")

                                SqlHelper.ExecuteNonQuery(trans3, CommandType.Text, "UPDATE tbl_loanreg set dt_last_repay_date='" & PeriodOverDate & "' where int_loanno='" & LoanNo & "'")
                                ''
                                ltransdate = PeriodOverDate
                                int_prv = int_t
                                '05/01/2024
                                int_prv = 0
                            End If
                            trans3.Commit()
                        Catch ex As Exception
                            trans3.Rollback()
                            trans3.Dispose()
                            'Response.Redirect("ErrorPage.aspx")
                            Throw ex
                        Finally
                            trans3.Dispose()
                        End Try
                    Else
                        CurTransDate = fyEnding.AddDays(1)

                        If Month(CurTransDate) > 3 And disbdate <= CDate("03/31/" & Year(CurTransDate)) And ltransdate < CDate("03/31/" & Year(CurTransDate)) Then


                            Dim trans2 As SqlTransaction = conn.BeginTransaction()

                            Try

                                ds = SqlHelper.ExecuteDataset(trans2, CommandType.Text, "select * FROM tbl_LOANTRANS WHERE INT_LOANNO ='" & LoanNo & "' AND dt_transaction='" & CDate("03/31/" & Year(CurTransDate)) & "'")
                                If ds.Tables(0).Rows.Count = 0 Then

                                    int_t = 0
                                    pnl_t = 0
                                    '----------------------------------
                                    pnl_t = CalcPenal(CDate("03/31/" & Year(CurTransDate)), LoanNo)
                                    CurrentPenal = pnl_t ''//01/04/2024


                                    'If pnl_t > 0 And pnl_t <= 1 Then //29/03/2024
                                    '    pnl_t = 1
                                    'Else
                                    '    pnl_t = System.Math.Round(pnl_t)
                                    'End If

                                    '----------------------------------
                                    '''int_prv
                                    'If int_prv = 0 Then 05/01/2024
                                    ds2 = SqlHelper.ExecuteDataset(trans2, CommandType.Text, "Select int_int_dues as InterestDues,int_penal_dues as PenalDues from tbl_loanTrans where int_loanno = '" & LoanNo & "' and int_type<=20 and chr_remark <> 'FINANCIAL YEAR END1' and int_type not in (4,5,12) order by dt_transaction,int_transid")
                                    dr1 = ds2.Tables(0).Rows(ds2.Tables(0).Rows.Count - 1)

                                    int_prv = dr1("InterestDues")
                                    int_prv_penal = dr1("PenalDues")
                                    'End If
                                    '''
                                    ' int_t = int_prv + System.Math.Round((CDbl(TxtBal) * (DateDiff(DateInterval.Day, ltransdate, CDate("3/31/2024")) * intrate) / 36500, 0))

                                    int_t = int_prv + System.Math.Round((CDbl(TxtBal) * (DateDiff(DateInterval.Day, ltransdate, CDate("03/31/" & Year(CurTransDate))) * intrate) / 36500), 2)
                                    CurrentInt = System.Math.Round((CDbl(TxtBal) * (DateDiff(DateInterval.Day, ltransdate, CDate("03/31/" & Year(CurTransDate))) * intrate) / 36500), 2)
                                    ' 05/01/2024
                                    pnl_t = pnl_t + int_prv_penal
                                    ' 
                                    ''''''''''''''''' Period Over Start''''''''''''''''''''''\\ 28/03/2024
                                    ds3 = SqlHelper.ExecuteDataset(trans2, CommandType.Text, "select * FROM tbl_loantrans WHERE INT_LOANNO ='" & LoanNo & "' and chr_remark in ('PERIOD OVER','RR Initiated')")
                                    If ds3.Tables(0).Rows.Count > 0 Then
                                        pnl_r = 0
                                        pnl_t = 0
                                        'int_t = int_prv + System.Math.Round((TxtBal * (DateDiff(DateInterval.Day, ltransdate, CDate("3/31/" & Year(CurTransDate))) * defaultintrate) / 36500), 0)
                                        int_t = int_prv + System.Math.Round(CDbl(TxtBal * (DateDiff(DateInterval.Day, ltransdate, CDate("03/31/" & Year(CurTransDate))) * defaultintrate) / 36500), 2)
                                        pnl_t = int_prv_penal
                                        int_prv_penal = pnl_t
                                        CurrentPenal = 0
                                        CurrentInt = System.Math.Round(CDbl(TxtBal * (DateDiff(DateInterval.Day, ltransdate, CDate("03/31/" & Year(CurTransDate))) * defaultintrate) / 36500), 2) ''//01/04/2024
                                    End If
                                    ''''''''''''''''' Period Over End''''''''''''''''''''''\\ 28/03/2024
                                    ''01/04/2024
                                    If lst_type = 0 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("03/31/" & Year(CurTransDate)) & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',10,'" & Mid(LoanNo, 1, 4) & "')")
                                    If lst_type = 1 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("03/31/" & Year(CurTransDate)) & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',9,'" & Mid(LoanNo, 1, 4) & "')")
                                    If lst_type = 2 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("03/31/" & Year(CurTransDate)) & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',18,'" & Mid(LoanNo, 1, 4) & "')")

                                    'If lst_type = 0 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("31/03/" & Year(CurTransDate)) & "','M" + Year(CurTransDate) + CStr(CInt(Mid(LoanNo, 5, Len(LoanNo) - 4))) + "','','','Receipt',0,0,0," & int_t & "," & pnl_t & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',10,'" & Mid(LoanNo, 1, 4) & "')")
                                    'If lst_type = 1 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("31/03/" & Year(CurTransDate)) & "','M" + Year(CurTransDate) + CStr(CInt(Mid(LoanNo, 5, Len(LoanNo) - 4))) + "','','','Receipt',0,0,0," & int_t & "," & pnl_t & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',9,'" & Mid(LoanNo, 1, 4) & "')")
                                    'If lst_type = 2 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("31/03/" & Year(CurTransDate)) & "','M" + Year(CurTransDate) + CStr(CInt(Mid(LoanNo, 5, Len(LoanNo) - 4))) + "','','','Receipt',0,0,0," & int_t & "," & pnl_t & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',18,'" & Mid(LoanNo, 1, 4) & "')")

                                    ''
                                    SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "UPDATE tbl_loanreg set dt_last_repay_date='" & ("03/31/" & Year(CurTransDate)) & "' where int_loanno='" & LoanNo & "'")
                                    ''
                                    ltransdate = CDate("03/31/" & Year(CurTransDate))
                                    int_prv = int_t

                                End If
                                trans2.Commit()
                                trans2.Dispose()
                            Catch ex As Exception
                                trans2.Rollback()
                                trans2.Dispose()
                                '   Response.Redirect("ErrorPage.aspx")
                                Throw ex
                            Finally
                                trans2.Dispose()
                            End Try
                        End If
                    End If

                Next

            End If
        End If


        If ltransdate > fdate And fdate <> CDate("01/01/1900") Then

            PrevTransDate = ltransdate
            CurTransDate = ENDDATE

            Dim financialYearEndings As List(Of Date) = GetFinancialYearEndings(PrevTransDate, CurTransDate)

            For Each fyEnding In financialYearEndings
                CurTransDate = fyEnding.AddDays(1)

                If Month(CurTransDate) > 3 And disbdate <= CDate("03/31/" & Year(CurTransDate)) And ltransdate < CDate("03/31/" & Year(CurTransDate)) Then


                    Dim trans2 As SqlTransaction = conn.BeginTransaction()

                    Try

                        ds = SqlHelper.ExecuteDataset(trans2, CommandType.Text, "select * FROM tbl_LOANTRANS WHERE INT_LOANNO ='" & LoanNo & "' AND dt_transaction='" & CDate("03/31/" & Year(CurTransDate)) & "'")
                        If ds.Tables(0).Rows.Count = 0 Then

                            int_t = 0
                            pnl_t = 0
                            '----------------------------------
                            pnl_t = CalcPenal(CDate("03/31/" & Year(CurTransDate)), LoanNo)
                            CurrentPenal = pnl_t ''//01/04/2024


                            'If pnl_t > 0 And pnl_t <= 1 Then //29/03/2024
                            '    pnl_t = 1
                            'Else
                            '    pnl_t = System.Math.Round(pnl_t)
                            'End If

                            '----------------------------------
                            '''int_prv
                            '''If int_prv = 0 Then '20/06/2024
                            ds2 = SqlHelper.ExecuteDataset(trans2, CommandType.Text, "Select int_int_dues as InterestDues,int_penal_dues as PenalDues from tbl_loanTrans where int_loanno = '" & LoanNo & "' and int_type<=20 and chr_remark <> 'FINANCIAL YEAR END1' and int_type not in (4,5,12) order by dt_transaction,int_transid")
                            dr1 = ds2.Tables(0).Rows(ds2.Tables(0).Rows.Count - 1)

                            int_prv = dr1("InterestDues")
                            int_prv_penal = dr1("PenalDues")
                            '''End If
                            '''
                            ' int_t = int_prv + System.Math.Round((CDbl(TxtBal) * (DateDiff(DateInterval.Day, ltransdate, CDate("3/31/2024")) * intrate) / 36500, 0))

                            int_t = int_prv + System.Math.Round((CDbl(TxtBal) * (DateDiff(DateInterval.Day, ltransdate, CDate("03/31/" & Year(CurTransDate))) * intrate) / 36500), 2)
                            CurrentInt = System.Math.Round((CDbl(TxtBal) * (DateDiff(DateInterval.Day, ltransdate, CDate("03/31/" & Year(CurTransDate))) * intrate) / 36500), 2)

                            pnl_t = pnl_t + int_prv_penal '20/06/2024

                            ''''''''''''''''' Period Over Start''''''''''''''''''''''\\ 28/03/2024
                            ds3 = SqlHelper.ExecuteDataset(trans2, CommandType.Text, "select * FROM tbl_loantrans WHERE INT_LOANNO ='" & LoanNo & "' and chr_remark in ('PERIOD OVER','RR Initiated')")
                            If ds3.Tables(0).Rows.Count > 0 Then
                                pnl_r = 0
                                pnl_t = 0
                                'int_t = int_prv + System.Math.Round((TxtBal * (DateDiff(DateInterval.Day, ltransdate, CDate("3/31/" & Year(CurTransDate))) * defaultintrate) / 36500), 0)
                                int_t = int_prv + System.Math.Round(CDbl(TxtBal * (DateDiff(DateInterval.Day, ltransdate, CDate("03/31/" & Year(CurTransDate))) * defaultintrate) / 36500), 2)
                                pnl_t = int_prv_penal
                                int_prv_penal = pnl_t
                                CurrentPenal = 0
                                CurrentInt = System.Math.Round(CDbl(TxtBal * (DateDiff(DateInterval.Day, ltransdate, CDate("03/31/" & Year(CurTransDate))) * defaultintrate) / 36500), 2) ''//01/04/2024
                            End If
                            ''''''''''''''''' Period Over End''''''''''''''''''''''\\ 28/03/2024
                            ''01/04/2024
                            If lst_type = 0 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("03/31/" & Year(CurTransDate)) & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',10,'" & Mid(LoanNo, 1, 4) & "')")
                            If lst_type = 1 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("03/31/" & Year(CurTransDate)) & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',9,'" & Mid(LoanNo, 1, 4) & "')")
                            If lst_type = 2 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("03/31/" & Year(CurTransDate)) & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',18,'" & Mid(LoanNo, 1, 4) & "')")

                            'If lst_type = 0 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("31/03/" & Year(CurTransDate)) & "','M" + Year(CurTransDate) + CStr(CInt(Mid(LoanNo, 5, Len(LoanNo) - 4))) + "','','','Receipt',0,0,0," & int_t & "," & pnl_t & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',10,'" & Mid(LoanNo, 1, 4) & "')")
                            'If lst_type = 1 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("31/03/" & Year(CurTransDate)) & "','M" + Year(CurTransDate) + CStr(CInt(Mid(LoanNo, 5, Len(LoanNo) - 4))) + "','','','Receipt',0,0,0," & int_t & "," & pnl_t & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',9,'" & Mid(LoanNo, 1, 4) & "')")
                            'If lst_type = 2 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("31/03/" & Year(CurTransDate)) & "','M" + Year(CurTransDate) + CStr(CInt(Mid(LoanNo, 5, Len(LoanNo) - 4))) + "','','','Receipt',0,0,0," & int_t & "," & pnl_t & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',18,'" & Mid(LoanNo, 1, 4) & "')")

                            ''
                            SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "UPDATE tbl_loanreg set dt_last_repay_date='" & CDate("03/31/" & Year(CurTransDate)) & "' where int_loanno='" & LoanNo & "'")
                            ''
                            ltransdate = CDate("03/31/" & Year(CurTransDate))
                            int_prv = int_t

                        End If
                        trans2.Commit()
                        trans2.Dispose()
                    Catch ex As Exception
                        trans2.Rollback()
                        trans2.Dispose()
                        '   Response.Redirect("ErrorPage.aspx")
                        Throw ex
                    Finally
                        trans2.Dispose()
                    End Try
                End If

            Next

        End If
        '---------------------------------------------


        '  strconn = System.Configuration.ConfigurationManager.AppSettings("constr")

        ds = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select dt_transaction as Date,chr_rec_no as RecNo,int_amt as Amount,int_debit as Debit,int_prin_amt as Principal,int_int_amt as Interest,int_penal_amt as Penal,int_bank_charge_r as bankchrg,int_eduint_amt as eduint,int_int_r as intr,int_penal_r as penlr,int_prin_dues as PrincipalDues,int_int_dues as InterestDues,int_penal_dues as PenalDues,chr_remark as Remarks,chr_transtype as Type,int_debit_b as DebitBal,int_transid,int_type from tbl_loanTrans where int_loanno = '" & LoanNo & "' and int_type<=20 and chr_remark <> 'FINANCIAL YEAR END1' order by dt_transaction,int_transid")

        If ds.Tables(0).Rows.Count > 0 Then

            notc_t = IIf(IsDBNull(ds.Tables(0).Compute("sum(DebitBal)", "DebitBal>=0 and Type='Payment' and (int_type=4 or int_type=8)")), 0, ds.Tables(0).Compute("sum(DebitBal)", "DebitBal>=0 and Type='Payment' and (int_type=4 or int_type=8)"))
            other_t = IIf(IsDBNull(ds.Tables(0).Compute("sum(DebitBal)", "DebitBal>=0 and Type='Payment' and int_type=12")), 0, ds.Tables(0).Compute("sum(DebitBal)", "DebitBal>=0 and Type='Payment' and int_type=12"))
            bankchrge_b = IIf(IsDBNull(ds.Tables(0).Compute("sum(DebitBal)", "DebitBal>=0 and Type='Payment' and (int_type=5 or int_type=7)")), 0, ds.Tables(0).Compute("sum(DebitBal)", "DebitBal>=0 and Type='Payment' and (int_type=5 or int_type=7)"))
            txtchrge_b = System.Math.Round(IIf(IsDBNull(ds.Tables(0).Compute("sum(DebitBal)", "DebitBal>=0 and Type='Payment'")), 0, ds.Tables(0).Compute("sum(DebitBal)", "DebitBal>=0 and Type='Payment'")), 0)
        End If

        'If int_prv = 0 Or int_prv_penal = 0 Then 22/06/2024
        ds2 = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select int_int_dues as InterestDues,int_penal_dues as PenalDues from tbl_loanTrans where int_loanno = '" & LoanNo & "' and int_type<=20 and chr_remark <> 'FINANCIAL YEAR END1' and int_type not in (4,5,12) order by dt_transaction,int_transid")
        dr1 = ds2.Tables(0).Rows(ds2.Tables(0).Rows.Count - 1)
        int_prv = dr1("InterestDues")
        int_prv_penal = dr1("PenalDues")
        'End If
        '---------------------------------------------Total Penal as on transaction date
        Dim trans1 As SqlTransaction = conn.BeginTransaction()

        Try


            pnl_t = 0
            pnl_t = CalcPenal(ENDDATE, LoanNo)
            pnl_t = Math.Round(pnl_t, 2)
            CurrentPenal = pnl_t
            pnl_t = pnl_t + int_prv_penal
            'If pnl_t > 0 And pnl_t <= 1 Then // 29/03/2024
            '    pnl_t = 1
            'ElseIf pnl_t < 0 Then
            '    pnl_t = 0
            'Else
            '    pnl_t = System.Math.Round(pnl_t)
            'End If

            Dim notc_r, notc_b, other_r, other_b As Double

            other_r = 0 : other_b = 0 : int_r = 0 : int_t = 0 : pnl_r = 0 : prin_b = 0 : prin_r = 0 : m = 0 : bnk_b = 0 : bnk_r = 0 : blamt = 0 : eduemi = 0 : notc_b = 0 : notc_r = 0 : int_tD = 0 : edubal = 0

            edubal = CDbl(intemi_bal)
            rmtd_amt = CDbl(Amount)
            int_t = (CDbl(TxtBal) * DateDiff(DateInterval.Day, ltransdate, (CDate(ENDDATE))) * intrate) / 36500
            int_t = Math.Round(int_t, 2)
            CurrentInt = int_t

            'int_tD = int_t - Int(int_t) // 29/03/2024
            'If int_tD < 0.5 Then
            '    int_t = int_t + 0.5
            'End If

            If rmtd_amt > bankchrge_b Then
                rmtd_amt = rmtd_amt - bankchrge_b
                bnk_r = bankchrge_b
                bnk_b = 0
                If rmtd_amt > notc_t Then
                    rmtd_amt = rmtd_amt - notc_t
                    notc_r = notc_t
                    notc_b = 0
                    If rmtd_amt > other_t Then
                        rmtd_amt = rmtd_amt - other_t
                        other_r = other_t
                        other_b = 0
                    Else
                        other_b = other_t - rmtd_amt
                        other_r = rmtd_amt
                        other_t = rmtd_amt
                        rmtd_amt = 0
                        blamt = 0
                        'int_t = System.Math.Round(int_t + int_prv, 0)
                        int_t = int_t + int_prv
                        GoTo rmt0
                    End If
                Else
                    notc_b = notc_t - rmtd_amt
                    notc_r = rmtd_amt
                    notc_t = rmtd_amt
                    rmtd_amt = 0
                    blamt = 0
                    'int_t = System.Math.Round(int_t + int_prv, 0)
                    int_t = int_t + int_prv
                    GoTo rmt0
                End If
            Else
                bnk_b = bankchrge_b - rmtd_amt
                bnk_r = rmtd_amt
                bankchrge_b = rmtd_amt
                rmtd_amt = 0
                blamt = 0
                'int_t = System.Math.Round(int_t + int_prv, 0)
                int_t = int_t + int_prv
                GoTo rmt0
            End If

            If rmtd_amt = 0 Then GoTo rmt1



            '''int_prv
            'If int_prv = 0 Then 23-04-2024
            ds2 = SqlHelper.ExecuteDataset(trans1, CommandType.Text, "Select int_int_dues as InterestDues,int_penal_dues as PenalDues from tbl_loanTrans where int_loanno = '" & LoanNo & "' and int_type<=20 and chr_remark <> 'FINANCIAL YEAR END1' and int_type not in (4,5,12) order by dt_transaction,int_transid")
            dr1 = ds2.Tables(0).Rows(ds2.Tables(0).Rows.Count - 1)
            int_prv = dr1("InterestDues")
            If int_prv_penal = 0 Then
                int_prv_penal = dr1("PenalDues")
            End If
            'End If

            pnl_r = pnl_t '+ int_prv_penal

            '''''''''''''''''Penal For Period Over Start''''''''''''''''''''''
            ds3 = SqlHelper.ExecuteDataset(trans1, CommandType.Text, "select * FROM tbl_loantrans WHERE INT_LOANNO ='" & LoanNo & "' and chr_remark in ('PERIOD OVER','RR Initiated')")
            If ds3.Tables(0).Rows.Count > 0 Then
                pnl_r = 0
                pnl_t = 0
                int_t = int_prv + System.Math.Round((CDbl(TxtBal) * DateDiff(DateInterval.Day, ltransdate, (CDate(ENDDATE))) * defaultintrate) / 36500, 2)
                pnl_t = int_prv_penal
                int_prv_penal = pnl_t
                pnl_r = pnl_t
                CurrentPenal = 0 ''//01/04/2024
                int_prv = 0 '25/04/2024
                CurrentInt = System.Math.Round((CDbl(TxtBal) * DateDiff(DateInterval.Day, ltransdate, (CDate(ENDDATE))) * defaultintrate) / 36500, 2) ''//01/04/2024
            End If
            '''''''''''''''''Penal For Period Over End''''''''''''''''''''''



            ''''''Light
            ' ''If otsflag = True Then
            ' ''    ds2 = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "select * FROM tbl_Light where int_loanno = '" & loanno & "' and int_type <> 9 and int_active=1")
            ' ''    If ds2.Tables(0).Rows.Count > 0 Then
            ' ''        dr2 = ds2.Tables(0).Rows(0)
            ' ''        per1 = dr2("int_per")
            ' ''        light = "L" & per1
            ' ''        per1 = 100 - per1

            ' ''    End If

            ' ''    'int_t = System.Math.Round(int_t + int_prv, 0)
            ' ''    'int_t = System.Math.Round(int_t / 2, 0)

            ' ''    ds3 = SqlHelper.ExecuteDataset(trans1, CommandType.Text, "Select int_prev,int_interest_BC,int_penal  from tbl_OTSwaiver where int_loanno = '" & loanno & "' order by int_otsid")
            ' ''    If ds3.Tables(0).Rows.Count > 0 Then
            ' ''        dr2 = ds3.Tables(0).Rows(ds3.Tables(0).Rows.Count - 1)
            ' ''        int_prvots = dr2("int_prev")
            ' ''        If int_prvots > 0 Then
            ' ''            int_tots = CDbl(TxtBal.Text) * DateDiff(DateInterval.Day, ltransdate, CDate(Date.Today)) * CDbl(TxtInt.Text) / 36500
            ' ''            'intdues = int_tots
            ' ''            int_t = System.Math.Round(Val(int_tots) * per1 / 100 + Val(int_prvots), 0)
            ' ''            intdues = dr2("int_interest_BC") + System.Math.Round(Val(int_tots) * per1 / 100, 0)
            ' ''        Else
            ' ''            int_t = System.Math.Round(int_t + int_prv, 0)
            ' ''            'intdues = int_t
            ' ''            int_t = System.Math.Round(int_t * per1 / 100, 0)
            ' ''            intdues = dr2("int_interest_BC") + System.Math.Round(int_t * per1 / 100, 0)
            ' ''        End If
            ' ''        If defaultloan = True Then
            ' ''            pnl_r = pnl_r + dr2("int_penal")
            ' ''        Else
            ' ''            pnl_r = pnl_r + dr2("int_penal")
            ' ''        End If
            ' ''    Else
            ' ''        int_t = System.Math.Round(int_t + int_prv, 0)
            ' ''        intdues = int_t
            ' ''        int_t = System.Math.Round(int_t * per1 / 100, 0)
            ' ''        intdues = intdues - int_t

            ' ''    End If
            ' ''Else
            int_t = (int_t + int_prv)   ''// 27/03/2024
            ' ''End If
            ' '' '''



            blamt = CDbl(Amount) - (bnk_r + notc_r + other_t)
            rmtd_amt = CDbl(Amount)
            ''''
            ''''
            ' ''If otsflag = True Then GoTo rmt2 ''// 27/03/2024
            '//27/03/2024
            If otsflag = True Then
                If WaiverAmount <= pnl_t Then
                    pnl_t = pnl_t - WaiverAmount
                    pnl_r = pnl_t '30/05/2024
                    PenalWaiver = WaiverAmount
                    InterestWaiver = 0
                    WaiverAmount = 0
                ElseIf WaiverAmount > pnl_t Then
                    WaiverAmount = WaiverAmount - pnl_t
                    PenalWaiver = pnl_t
                    pnl_t = 0
                    pnl_r = 0    '27/05/2024
                    If WaiverAmount <= int_t Then
                        int_t = int_t - WaiverAmount
                        InterestWaiver = WaiverAmount
                        WaiverAmount = 0
                    ElseIf WaiverAmount > int_t Then
                        WaiverAmount = WaiverAmount - int_t
                        InterestWaiver = int_t
                        int_t = 0
                    End If
                End If
            End If
            '//27/03/2024
            ''''
rmt0:       If pnl_t > 0 Then pnl_r = IIf(blamt >= pnl_r, pnl_r, blamt) : blamt = blamt - pnl_r
            ''//27/03/2024rmt2:           '''If edubal > 0 And NewPrefix = 0 Then eduemi = IIf(blamt >= CDbl(txtemi_int.Text), m * CDbl(txtemi_int.Text), blamt) : blamt = blamt - eduemi

            ' '' ''New Prefix Rule'''Start
            ' ''If edubal > 0 And NewPrefix = 1 Then eduemi = IIf(blamt >= edubal, edubal, blamt) : blamt = blamt - eduemi
            ' '' ''New Prefix Rule'''End

            ' ''If eduemi > edubal Then blamt = (blamt + eduemi) - edubal : eduemi = edubal


            If int_t > 0 Then int_r = IIf(blamt >= int_t, int_t, blamt) : blamt = blamt - int_r

            'prin_r = blamt
            ''Green
            ' ''If greencard > 0 Then
            ' ''    prin_r = System.Math.Round(IIf(blamt > CDbl(TxtBal.Text - greencard), CDbl(TxtBal.Text - greencard), blamt), 0)

            ' ''ElseIf greencard = 0 Then
            'prin_r = System.Math.Round(IIf(blamt > CDbl(TxtBal), CDbl(TxtBal), blamt), 0) //29/03/2024
            prin_r = System.Math.Round(IIf(blamt > CDbl(TxtBal), CDbl(TxtBal), blamt), 2)
            ' ''End If
            ''
            prin_b = System.Math.Round(IIf((CDbl(TxtBal) - prin_r) <= 0, 0, CDbl(TxtBal) - prin_r), 2)

            'Green
            ' ''If greencard > 0 Then
            ' ''    blamt = IIf((blamt - (CDbl(TxtBal.Text) - greencard)) > 0, CStr(System.Math.Round((blamt - (CDbl(TxtBal.Text) - greencard)), 0)), 0)

            ' ''ElseIf greencard = 0 Then
            blamt = IIf((blamt - CDbl(TxtBal)) > 0, CStr(System.Math.Round((blamt - CDbl(TxtBal)), 2)), 0) '// 29 / 3 / 2024
            'blamt = System.Math.Round(IIf((blamt - CDbl(TxtBal)) > 0, CStr(blamt - CDbl(TxtBal)), 0), 2)

            '' ''End If
            'blamt = IIf((blamt - CDbl(TxtBal.Text)) > 0, CStr(System.Math.Round((blamt - CDbl(TxtBal.Text)), 0)), 0)
            ''
            If (blamt) > 0 And (edubal - eduemi) > 0 Then

                If (edubal - eduemi) >= blamt Then
                    eduemi = eduemi + (blamt)
                    blamt = 0
                    '''''
                    If prin_b = 0 And eduemi < edubal Then
                        If eduemi + prin_r > edubal Then
                            eduemi = edubal
                            prin_r = (CDbl(Amount)) - (eduemi + pnl_r + int_r + bnk_r + notc_r + other_t)
                            ''prin_b = loanbal - prin_r
                            prin_b = CDbl(TxtBal) - prin_r
                        Else
                            eduemi = eduemi + prin_r
                            prin_b = prin_r
                            prin_r = 0
                        End If

                    End If
                    '''''
                    'txt10.Text = 0
                Else
                    blamt = blamt + eduemi
                    eduemi = edubal
                    blamt = (blamt - eduemi)
                End If
            End If
            If otsflag = False Then
                If blamt > 0 And (pnl_t - pnl_r) > 0 Then
                    If (blamt >= (pnl_t - pnl_r)) Then
                        blamt = blamt - (pnl_t - pnl_r)
                        pnl_r = pnl_t
                    Else
                        pnl_r = pnl_r + blamt
                        blamt = 0
                    End If

                End If
            End If
            If eduemi < 0 Then eduemi = 0


            '''Exit Sub
            '---------------------------------------------

            '------------------**********Saving Loan Transaction table - tbl_loantrans
            If prin_b < 0 Then prin_b = 0
            If prin_b <= 0 Then TransRemarks = "LOAN CLOSURE"
rmt1:
            ' If otsflag = True Then
            ' ''                    '''
            ' ''                    If prin_b <= 0 Then txtremark.Text = "LOAN CLOSURE(OTS)" Else txtremark.Text = "REPAYMENT(OTS)"
            ' ''                    If lst_type.SelectedIndex = 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "','" & txtrecptno.Text & "','" & lst_mode.SelectedItem.Text & "','" & txtchequeno.Text & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & int_t & ",0," & int_r & ",0," & prin_b & "," & int_t - int_r & ",0," & bnk_r + notc_r + other_t & ",'" & txtaccno.Text & "','" & txtremark.Text & "',1,'" & Mid(loanno, 1, 4) & "')")
            ' ''                    If lst_type.SelectedIndex = 1 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "','" & txtrecptno.Text & "','" & lst_mode.SelectedItem.Text & "','" & txtchequeno.Text & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & int_t & ",0," & int_r & ",0," & prin_b & "," & int_t - int_r & ",0," & bnk_r + notc_r + other_t & ",'" & txtaccno.Text & "','" & txtremark.Text & "',6,'" & Mid(loanno, 1, 4) & "')")
            ' ''                    If lst_type.SelectedIndex = 2 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "','" & txtrecptno.Text & "','" & lst_mode.SelectedItem.Text & "','" & txtchequeno.Text & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & int_t & ",0," & int_r & ",0," & prin_b & "," & int_t - int_r & ",0," & bnk_r + notc_r + other_t & ",'" & txtaccno.Text & "','" & txtremark.Text & "',15,'" & Mid(loanno, 1, 4) & "')")
            ' ''                    '''
            ' ''                Else
            '''''''Default OTS
            ' ''If defaultotsflag = True Then
            ' ''    If prin_b <= 0 Then txtremark.Text = "LOAN CLOSURE(DefOTS)" Else txtremark.Text = "REPAYMENT(DefOTS)"
            ' ''    If lst_type.SelectedIndex = 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "','" & txtrecptno.Text & "','" & lst_mode.SelectedItem.Text & "','" & txtchequeno.Text & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & int_t & "," & pnl_t & "," & int_r & "," & pnl_r & "," & prin_b & "," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'" & txtaccno.Text & "','" & txtremark.Text & "',1,'" & Mid(loanno, 1, 4) & "')")
            ' ''    If lst_type.SelectedIndex = 1 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "','" & txtrecptno.Text & "','" & lst_mode.SelectedItem.Text & "','" & txtchequeno.Text & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & int_t & "," & pnl_t & "," & int_r & "," & pnl_r & "," & prin_b & "," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'" & txtaccno.Text & "','" & txtremark.Text & "',6,'" & Mid(loanno, 1, 4) & "')")
            ' ''    If lst_type.SelectedIndex = 2 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "','" & txtrecptno.Text & "','" & lst_mode.SelectedItem.Text & "','" & txtchequeno.Text & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & int_t & "," & pnl_t & "," & int_r & "," & pnl_r & "," & prin_b & "," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'" & txtaccno.Text & "','" & txtremark.Text & "',15,'" & Mid(loanno, 1, 4) & "')")
            ' ''End If
            '''''''Default OTS

            'Green
            ' ''If greencard > 0 Then
            ' ''    If lst_type.SelectedIndex = 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "','" & txtrecptno.Text & "','" & lst_mode.SelectedItem.Text & "','" & txtchequeno.Text & "','Receipt'," & CDbl(Amount) & "," & prin_r + greencard & "," & eduemi & "," & int_t & "," & pnl_t & "," & int_r & "," & pnl_r & ",0," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'" & txtaccno.Text & "','LOAN CLOSURE',1,'" & Mid(loanno, 1, 4) & "')")
            ' ''    If lst_type.SelectedIndex = 1 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "','" & txtrecptno.Text & "','" & lst_mode.SelectedItem.Text & "','" & txtchequeno.Text & "','Receipt'," & CDbl(Amount) & "," & prin_r + greencard & "," & eduemi & "," & int_t & "," & pnl_t & "," & int_r & "," & pnl_r & ",0," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'" & txtaccno.Text & "','LOAN CLOSURE',6,'" & Mid(loanno, 1, 4) & "')")
            ' ''    If lst_type.SelectedIndex = 2 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "','" & txtrecptno.Text & "','" & lst_mode.SelectedItem.Text & "','" & txtchequeno.Text & "','Receipt'," & CDbl(Amount) & "," & prin_r + greencard & "," & eduemi & "," & int_t & "," & pnl_t & "," & int_r & "," & pnl_r & ",0," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'" & txtaccno.Text & "','LOAN CLOSURE',15,'" & Mid(loanno, 1, 4) & "')")
            ' ''    ''
            ' ''ElseIf greencard = 0 Then
            If OfficeId <> Mid(LoanNo, 1, 4) Then
                TransRemarks = "Repayment @ " & GetOffice(Mid(LoanNo, 1, 4))
            Else
                ' ''If Ashwas = 1 Or Ashwas_M = 1 Then
                ' ''    ds1 = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select * from  tbl_Ashwas where int_loanno='" & txtLoanno.Text.Trim & "'")
                ' ''    If ds1.Tables(0).Rows.Count > 0 Then
                ' ''        If ds1.Tables(0).Rows(0)("int_status") = 1 Then
                ' ''            remarks = "Remittance-AshwasaKiranam-" & ds1.Tables(0).Rows(0)("int_loanno_new")
                ' ''        Else
                ' ''            remarks = txtremark.Text
                ' ''        End If
                ' ''    Else
                ' ''        If Ashwas = 1 Then
                ' ''            remarks = "Remittance-AshwasaKiranam"
                ' ''            SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO  tbl_Ashwas(int_loanno,mny_dues,mny_remitamt,mny_loanamt, dt_tdate,int_type)VALUES('" & loanno & "'," & AswasBalRound + AswasRemit & "," & AswasRemit & "," & AswasBalRound & ",'" & CDate(ENDDATE) & "'," & AswasType & ")")
                ' ''        ElseIf Ashwas_M = 1 Then

                ' ''            ''''
                ' ''            remarks = "Remittance-AshwasaKiranam(PartPayment)"

                ' ''            ds1 = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select * from  tbl_Ashwas_M where int_loanno='" & txtLoanno.Text.Trim & "'")
                ' ''            If ds1.Tables(0).Rows.Count > 0 Then
                ' ''                If ds1.Tables(0).Rows(0)("mny_remitamt") - ds1.Tables(0).Rows(0)("mny_remitamt_bal") <= 0 Then
                ' ''                    SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Ashwas select  int_loanno,mny_dues,mny_remitamt,mny_loanamt, dt_tdate,int_type FROM tbl_Ashwas_M where  int_loanno ='" & loanno & "' ")
                ' ''                Else
                ' ''                    If ds1.Tables(0).Rows(0)("mny_remitamt_bal") + Val(Amount) >= ds1.Tables(0).Rows(0)("mny_remitamt") Then
                ' ''                        SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Ashwas select  int_loanno,mny_dues,mny_remitamt,mny_loanamt, dt_tdate,0,'',int_type FROM tbl_Ashwas_M where  int_loanno ='" & loanno & "' ")
                ' ''                    End If
                ' ''                    SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "Update tbl_Ashwas_M set mny_remitamt_bal = mny_remitamt_bal + " & CDbl(Amount) & " where  int_loanno ='" & loanno & "' ")

                ' ''                End If

                ' ''            Else
                ' ''                SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO  tbl_Ashwas_M(int_loanno,mny_dues,mny_remitamt,mny_loanamt, dt_tdate,int_type,mny_remitamt_bal)VALUES('" & loanno & "'," & AswasBalRound + AswasRemit & "," & AswasRemit & "," & AswasBalRound & ",'" & CDate(ENDDATE) & "'," & AswasType & "," & CDbl(Amount) & ")")
                ' ''            End If
                ' ''            ''''

                ' ''        End If
                ' ''    End If
                ' ''Else
                If Remark = "SUBSIDY" Then

                    TransRemarks = Remark
                ElseIf Remark = "Prathyasa" Then
                    TransRemarks = Remark
                Else
                    TransRemarks = "REPAYMENT"
                End If

                If prin_b <= 0 And Remark <> "SUBSIDY" Then TransRemarks = "LOAN CLOSURE"

                ' ''End If
            End If


            If Remark = "Prathyasa" Then
                TransRemarks = Remark

            End If

            ' ''If defaultotsflag = False Then
            ''' 01/04/2024

            If lst_type = 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate(ENDDATE) & "','" & RecptNo & "','" & lst_mode & "','" & Chequeno & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & CurrentInt & "," & CurrentPenal & "," & int_r & "," & pnl_r & "," & prin_b & "," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'','" & TransRemarks & "',1,'" & Mid(LoanNo, 1, 4) & "')")
            If lst_type = 1 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate(ENDDATE) & "','" & RecptNo & "','" & lst_mode & "','" & Chequeno & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & CurrentInt & "," & CurrentPenal & "," & int_r & "," & pnl_r & "," & prin_b & "," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'','" & TransRemarks & "',6,'" & Mid(LoanNo, 1, 4) & "')")
            If lst_type = 2 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate(ENDDATE) & "','" & RecptNo & "','" & lst_mode & "','" & Chequeno & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & CurrentInt & "," & CurrentPenal & "," & int_r & "," & pnl_r & "," & prin_b & "," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'','" & TransRemarks & "',15,'" & Mid(LoanNo, 1, 4) & "')")

            'If lst_type = 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & ENDDATE & "','" & RecptNo & "','" & lst_mode & "','" & Chequeno & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & int_t & "," & pnl_t & "," & int_r & "," & pnl_r & "," & prin_b & "," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'','" & Remark & "',1,'" & Mid(LoanNo, 1, 4) & "')")
            'If lst_type = 1 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & ENDDATE & "','" & RecptNo & "','" & lst_mode & "','" & Chequeno & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & int_t & "," & pnl_t & "," & int_r & "," & pnl_r & "," & prin_b & "," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'','" & Remark & "',6,'" & Mid(LoanNo, 1, 4) & "')")
            'If lst_type = 2 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & ENDDATE & "','" & RecptNo & "','" & lst_mode & "','" & Chequeno & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & int_t & "," & pnl_t & "," & int_r & "," & pnl_r & "," & prin_b & "," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'','" & Remark & "',15,'" & Mid(LoanNo, 1, 4) & "')")

            ' ''End If
            ' ''End If

            ' ''End If
            If blamt > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_balance(int_loanno,chr_rec_no,int_bal)VALUES('" & LoanNo & "','" & RecptNo & "'," & blamt & ")")
            'SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_subdistrict SET int_receiptno =int_receiptno + 1 WHERE int_distid like " & Mid(loanno, 1, 2) & "")
            SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE  tbl_Sub_Districts SET Last_Receipt_No =Last_Receipt_No + 1 WHERE Office_Code like '" & OfficeId & "'") ''27-04-2024

            '------------------***********For Bank charges/ Notice charges/ other charges

            If bnk_r > 0 And bnk_b = 0 Then
                SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loantrans SET int_debit_b=0 where INT_LOANNO='" & LoanNo & "' AND chr_transtype='Payment' and  int_type in (5,7) and int_debit_b>0")
            ElseIf bnk_r > 0 And bnk_b > 0 Then
                ds = SqlHelper.ExecuteDataset(trans1, CommandType.Text, "select * from tbl_loantrans where INT_LOANNO='" & LoanNo & "' AND chr_transtype='Payment' and int_type in (5,7) and int_debit_b>0  order by dt_transaction")

                If ds.Tables(0).Rows.Count > 0 Then
                    dr = ds.Tables(0).Rows(0)
                    g1 = bnk_r
                    For Each dr In ds.Tables(0).Rows
                        If g1 <= 0 Then Exit For
                        If (g1 > dr("int_debit_b")) Then
                            SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loantrans SET int_debit_b=0 where int_transid=" & dr("int_transid"))
                            g1 = g1 - dr("int_debit_b")
                        Else
                            SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loantrans SET int_debit_b=int_debit_b-" & g1 & " where int_transid=" & dr("int_transid"))
                            g1 = 0
                        End If
                    Next
                End If
            End If

            If notc_r > 0 And notc_b = 0 Then
                SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loantrans SET int_debit_b=0 where INT_LOANNO='" & LoanNo & "' AND chr_transtype='Payment' and int_type in (4,8) and int_debit_b>0")
            ElseIf notc_r > 0 And notc_b > 0 Then
                ds = SqlHelper.ExecuteDataset(trans1, CommandType.Text, "select * from tbl_loantrans where INT_LOANNO='" & LoanNo & "' AND chr_transtype='Payment' and int_type in (4,8) and int_debit_b>0  order by dt_transaction")
                If ds.Tables(0).Rows.Count > 0 Then
                    dr = ds.Tables(0).Rows(0)
                    g1 = notc_r
                    For Each dr In ds.Tables(0).Rows
                        If g1 <= 0 Then Exit For
                        If (g1 > dr("int_debit_b")) Then
                            SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loantrans SET int_debit_b=0 where int_transid=" & dr("int_transid"))
                            g1 = g1 - dr("int_debit_b")
                        Else
                            SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loantrans SET int_debit_b=int_debit_b-" & g1 & " where int_transid=" & dr("int_transid"))
                            g1 = 0
                        End If
                    Next
                End If
            End If
            If other_r > 0 And other_b = 0 Then
                SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loantrans SET int_debit_b=0 where INT_LOANNO='" & LoanNo & "' AND chr_transtype='Payment' and int_type =12 and int_debit_b>0")
            ElseIf other_r > 0 And other_b > 0 Then
                ds = SqlHelper.ExecuteDataset(trans1, CommandType.Text, "select * from tbl_loantrans where INT_LOANNO='" & LoanNo & "' AND chr_transtype='Payment' and int_type=12 and int_debit_b>0  order by dt_transaction")
                If ds.Tables(0).Rows.Count > 0 Then
                    dr = ds.Tables(0).Rows(0)
                    g1 = other_r
                    For Each dr In ds.Tables(0).Rows
                        If g1 <= 0 Then Exit For
                        If (g1 > dr("int_debit_b")) Then
                            SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loantrans SET int_debit_b=0 where int_transid=" & dr("int_transid"))
                            g1 = g1 - dr("int_debit_b")
                        Else
                            SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loantrans SET int_debit_b=int_debit_b-" & g1 & " where int_transid=" & dr("int_transid"))
                            g1 = 0
                        End If
                    Next
                End If
            End If
            '------------------*************Saving Accounts table - tbl_Account
            ds = SqlHelper.ExecuteDataset(trans1, CommandType.Text, "SELECT * FROM TBL_ACCOUNT WHERE CHR_UNDER='" & FCODE & "' AND chr_head='Minor' and chr_type='Receipt'")
            If ds.Tables(0).Rows.Count > 0 Then
                dr = ds.Tables(0).Rows(0)
                For Each dr In ds.Tables(0).Rows
                    '' ''If dr("chr_Name") = "PRINCIPAL" And prin_r > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time)VALUES('" & txtrecptno.Text & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode.SelectedItem.Text & "'," & prin_r & ",'" & CDate(ENDDATE) & "','" & loanno & "','" & TxtBName.Text & "','REPAYMENT','" & Mid(loanno, 1, 4) & "','" & Session("username") & "','" & DateTime.Now & "')")
                    '' ''If dr("chr_Name") = "INTEREST" And int_r + eduemi > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time)VALUES('" & txtrecptno.Text & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode.SelectedItem.Text & "'," & int_r + eduemi & ",'" & CDate(ENDDATE) & "','" & loanno & "','" & TxtBName.Text & "','REPAYMENT','" & Mid(loanno, 1, 4) & "','" & Session("username") & "','" & DateTime.Now & "')")
                    '' ''If dr("chr_Name") = "PENAL" And pnl_r > 0 And otsflag = False Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time)VALUES('" & txtrecptno.Text & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode.SelectedItem.Text & "'," & pnl_r & ",'" & CDate(ENDDATE) & "','" & loanno & "','" & TxtBName.Text & "','REPAYMENT','" & Mid(loanno, 1, 4) & "','" & Session("username") & "','" & DateTime.Now & "')")
                    '' ''If dr("chr_Name") = "NOTICECHARGES" And notc_r > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time)VALUES('" & txtrecptno.Text & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode.SelectedItem.Text & "'," & notc_r & ",'" & CDate(ENDDATE) & "','" & loanno & "','" & TxtBName.Text & "','REPAYMENT','" & Mid(loanno, 1, 4) & "','" & Session("username") & "','" & DateTime.Now & "')")
                    '' ''If dr("chr_Name") = "BANKCHARGES" And bnk_r > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time)VALUES('" & txtrecptno.Text & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode.SelectedItem.Text & "'," & bnk_r & ",'" & CDate(ENDDATE) & "','" & loanno & "','" & TxtBName.Text & "','REPAYMENT','" & Mid(loanno, 1, 4) & "','" & Session("username") & "','" & DateTime.Now & "')")
                    '' ''If dr("chr_Name") = "OTHERCHARGES" And other_r > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time)VALUES('" & txtrecptno.Text & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode.SelectedItem.Text & "'," & other_r & ",'" & CDate(ENDDATE) & "','" & loanno & "','" & TxtBName.Text & "','REPAYMENT','" & Mid(loanno, 1, 4) & "','" & Session("username") & "','" & DateTime.Now & "')")
                    '' ''If dr("chr_Name") = "CREDITBALANCE" And blamt > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time)VALUES('" & txtrecptno.Text & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode.SelectedItem.Text & "'," & blamt & ",'" & CDate(ENDDATE) & "','" & loanno & "','" & TxtBName.Text & "','REPAYMENT','" & Mid(loanno, 1, 4) & "','" & Session("username") & "','" & DateTime.Now & "')")
                    ' ''If Val(txtmtr.Text) > 0 Then
                    ' ''    If dr("chr_Name") = "PRINCIPAL" Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time,vchr_offidC)VALUES('" & txtrecptno.Text & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode.SelectedItem.Text & "'," & CDbl(Amount) & ",'" & CDate(ENDDATE) & "','" & loanno & "','" & LoneeeName & "','REPAYMENT','" & Session("officeid") & "','" & Session("username") & "','" & DateTime.Now & "','" & Mid(loanno, 1, 4) & "')")

                    ' ''Else
                    Dim Acc_Remarks As String
                    Dim Acc_Type As String

                    If lst_mode = "Prathyasa" Then
                        Acc_Remarks = "PRATHYASA"
                        Acc_Type = "Prathyasa"

                    Else
                        Acc_Remarks = "REPAYMENT"
                        Acc_Type = "Receipt"
                    End If
                    If dr("chr_Name") = "PRINCIPAL" And prin_r > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time,vchr_offidC)VALUES('" & RecptNo & "','" & Acc_Type & "'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode & "'," & prin_r & ",'" & CDate(ENDDATE) & "','" & LoanNo & "','" & LoneeeName & "','" & Acc_Remarks & "','" & Mid(LoanNo, 1, 4) & "','" & UserName & "',GETDATE(),'" & Mid(LoanNo, 1, 4) & "')")
                    If dr("chr_Name") = "INTEREST" And int_r + eduemi > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time,vchr_offidC)VALUES('" & RecptNo & "','" & Acc_Type & "'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode & "'," & int_r + eduemi & ",'" & CDate(ENDDATE) & "','" & LoanNo & "','" & LoneeeName & "','" & Acc_Remarks & "','" & Mid(LoanNo, 1, 4) & "','" & UserName & "',GETDATE(),'" & Mid(LoanNo, 1, 4) & "')")
                    If dr("chr_Name") = "PENAL" And pnl_r > 0 And otsflag = False Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time,vchr_offidC)VALUES('" & RecptNo & "','" & Acc_Type & "'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode & "'," & pnl_r & ",'" & CDate(ENDDATE) & "','" & LoanNo & "','" & LoneeeName & "','" & Acc_Remarks & "','" & Mid(LoanNo, 1, 4) & "','" & UserName & "',GETDATE(),'" & Mid(LoanNo, 1, 4) & "')")
                    If dr("chr_Name") = "NOTICECHARGES" And notc_r > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time,vchr_offidC)VALUES('" & RecptNo & "','" & Acc_Type & "'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode & "'," & notc_r & ",'" & CDate(ENDDATE) & "','" & LoanNo & "','" & LoneeeName & "','" & Acc_Remarks & "','" & Mid(LoanNo, 1, 4) & "','" & UserName & "',GETDATE(),'" & Mid(LoanNo, 1, 4) & "')")
                    If dr("chr_Name") = "BANKCHARGES" And bnk_r > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time,vchr_offidC)VALUES('" & RecptNo & "','" & Acc_Type & "'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode & "'," & bnk_r & ",'" & CDate(ENDDATE) & "','" & LoanNo & "','" & LoneeeName & "','" & Acc_Remarks & "','" & Mid(LoanNo, 1, 4) & "','" & UserName & "',GETDATE(),'" & Mid(LoanNo, 1, 4) & "')")
                    If dr("chr_Name") = "OTHERCHARGES" And other_r > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time,vchr_offidC)VALUES('" & RecptNo & "','" & Acc_Type & "'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode & "'," & other_r & ",'" & CDate(ENDDATE) & "','" & LoanNo & "','" & LoneeeName & "','" & Acc_Remarks & "','" & Mid(LoanNo, 1, 4) & "','" & UserName & "',GETDATE(),'" & Mid(LoanNo, 1, 4) & "')")
                    If dr("chr_Name") = "CREDITBALANCE" And blamt > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time,vchr_offidC)VALUES('" & RecptNo & "','" & Acc_Type & "'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode & "'," & blamt & ",'" & CDate(ENDDATE) & "','" & LoanNo & "','" & LoneeeName & "','" & Acc_Remarks & "','" & Mid(LoanNo, 1, 4) & "','" & UserName & "',GETDATE(),'" & Mid(LoanNo, 1, 4) & "')")
                    '" & Mid(LoanNo, 1, 4) & "','" & UserName & "','" & DateTime.Now & "','" & Mid(loanno, 1, 4) & "')")
                    ' ''End If



                Next
            End If

            nxtduedate = DateSerial(Year(ENDDATE), Month(ENDDATE), Day(fduedt))
            If ENDDATE >= nxtduedate Then nxtduedate = DateAdd(DateInterval.Month, 1, nxtduedate)

            '------------------*****************update Loan Register- tbl_loanreg
            ''Green
            ' ''If greencard > 0 Then
            ' ''    SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loanreg set mny_loanbal=0,dt_last_repay_date='" & CDate(ENDDATE) & "',dt_next_due_date='" & CDate(nxtduedate) & "',mny_intrbal=mny_intrbal-" & CDbl(eduemi) & " where int_loanno='" & loanno & "'")
            ' ''    ''
            ' ''    SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_LoanClosure set int_Closure=1,dt_datetimeClosure = '" & DateTime.Now & "' where int_loanno='" & loanno & "'")
            ' ''    ''
            '' ''ElseIf greencard = 0 Then
            SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loanreg set mny_loanbal=" & prin_b & ",dt_last_repay_date='" & CDate(ENDDATE) & "',dt_next_due_date='" & CDate(nxtduedate) & "',mny_intrbal=mny_intrbal-" & CDbl(eduemi) & " where int_loanno='" & LoanNo & "'")

            ' ''End If
            '
            ' SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loanreg set mny_loanbal=" & prin_b & ",dt_last_repay_date='" & CDate(ENDDATE) & "',dt_next_due_date='" & CDate(nxtduedate) & "',mny_intrbal=mny_intrbal-" & CDbl(eduemi) & " where int_loanno='" & loanno & "'")
            SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loanreg set vchr_stage='CLOSED' where int_loanno='" & LoanNo & "' and mny_loanbal=0 and mny_intrbal=0")
            ''
            If prin_b <= 0 Then
                ds1 = SqlHelper.ExecuteDataset(trans1, CommandType.Text, "Select * from tbl_LoanClosure where int_loanno='" & LoanNo & "'")
                If ds1.Tables(0).Rows.Count > 0 Then
                    SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_LoanClosure set int_Closure=1,dt_datetimeClosure = '" & DateTime.Now.ToString & "' where int_loanno='" & LoanNo & "'")
                Else
                    SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "Insert into tbl_LoanClosure(int_loanno,int_Closure,dt_datetimeClosure,vchr_offid) values('" & LoanNo & "',1,'" & DateTime.Now.ToString & "','" & Mid(LoanNo, 1, 4) & "')")

                End If

            End If
            ''
            '---------------------------------------------
            '''''''OTS
            '' ''Dim rmtamt, rmtint As Double
            '' ''If otsflag = True Then
            '' ''    ds = SqlHelper.ExecuteDataset(trans1, CommandType.Text, "select sum(int_amt),sum(int_int_r) from tbl_loantrans where INT_LOANNO='" & loanno & "' and chr_remark in('REPAYMENT(OTS)','LOAN CLOSURE(OTS)')")
            '' ''    If ds.Tables.Count > 0 Then
            '' ''        If ds.Tables(0).Rows.Count > 0 Then
            '' ''            For Each dr In ds.Tables(0).Rows
            '' ''                rmtamt = IIf(IsDBNull(dr(0)), 0, dr(0))
            '' ''                '''Light
            '' ''                rmtint = IIf(IsDBNull(dr(1)), 0, dr(1))
            '' ''                '''
            '' ''            Next
            '' ''        End If
            '' ''    End If
            '' ''End If
            '''''''OTS OLD'''
            ' '' '' If pnl_t > 0 And otsflag = True Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_OTSwaiver(int_loanno,chr_rec_no,int_Penal,dte_tdate,int_Remitamt)VALUES('" & loanno & "','" & txtrecptno.Text & "'," & pnl_t & ",'" & CDate(ENDDATE) & "'," & rmtamt & ")")
            '' '' '''
            ''''''''DefaultOTS
            '' ''If defaultotsflag = True Then
            '' ''    If pnl_t > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_OTSwaiver(int_loanno,chr_rec_no,int_Penal,dte_tdate,int_Remitamt,vchr_remark)VALUES('" & loanno & "','" & txtrecptno.Text & "'," & pnl_t_ots & ",'" & CDate(ENDDATE) & "'," & CDbl(Amount) & ",'DefaultOTS')")
            '' ''    SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loanreg set  int_DefaultOts =2 where int_loanno='" & loanno & "' and  int_DefaultOts =1")
            '' ''End If
            ''''''''DefaultOTS

            ''// 27/03/2024
            If otsflag = True Then
                SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_OTSwaiver(int_loanno,chr_rec_no,int_Penal,dte_tdate,int_Remitamt,int_Interest,vchr_remark)VALUES('" & LoanNo & "','" & RecptNo & "'," & PenalWaiver & ",'" & CDate(ENDDATE) & "'," & Amount & "," & InterestWaiver & ",'OTS')")
            End If
            ''// 27/03/2024


            ''''''''Light
            '' ''If otsflag = True Then
            '' ''    If int_t - int_r > 0 Then
            '' ''        rmtint = int_t
            '' ''    End If
            '' ''    If light.ToString.StartsWith("L0") = True Then
            '' ''        intdues = 0
            '' ''    End If
            '' ''    If pnl_t > 0 Or int_r > 0 Or rmtint > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_OTSwaiver(int_loanno,chr_rec_no,int_Penal,int_interest,dte_tdate,int_Remitamt,vchr_remark,int_prev,int_interest_BC)VALUES('" & loanno & "','" & txtrecptno.Text & "'," & pnl_r & "," & rmtint & ",'" & CDate(ENDDATE) & "'," & rmtamt & ",'" & light & "'," & int_t - int_r & "," & intdues & ")")

            '' ''End If
            '' '' '''
            '' '' '''

            '''GreenCard
            ' ''If greencard > 0 Then
            ' ''    ds = SqlHelper.ExecuteDataset(trans1, CommandType.Text, "select int_creceiptno FROM tbl_subdistrict WHERE vchr_offid ='" & Mid(loanno, 1, 4) & "'")
            ' ''    If ds.Tables(0).Rows.Count > 0 Then
            ' ''        dr = ds.Tables(0).Rows(0)
            ' ''        rcpt = "C" & dr("int_creceiptno")
            ' ''    End If
            ' ''    If Val(txtmtr.Text) > 0 Then
            ' ''        SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, int_amt,chr_rec_no, chr_mod_payment, chr_transtype,int_debit,int_debit_b,int_int_amt,int_int_dues,int_prin_dues,int_bank_charge_r,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "',0,'','ADJUSTMENT','Receipt',0,0,0,0,0," & greencard & ",'Moratorium',17,'" & Mid(loanno, 1, 4) & "')")
            ' ''    Else
            ' ''        SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, int_amt,chr_rec_no, chr_mod_payment, chr_transtype,int_debit,int_debit_b,int_int_amt,int_int_dues,int_prin_dues,int_bank_charge_r,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "',0,'" & rcpt & "','ADJUSTMENT','Receipt',0,0,0,0,0," & greencard & ",'GREEN CARD',17,'" & Mid(loanno, 1, 4) & "')")
            ' ''        SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_subdistrict SET int_creceiptno =int_creceiptno + 1 WHERE vchr_offid ='" & Mid(loanno, 1, 4) & "'")
            ' ''    End If
            ' ''End If
            ' '' '''
            ' ''FCODE = txtrecptno.Text
            trans1.Commit()



        Catch ex As Exception
            trans1.Rollback()
            trans1.Dispose()
            '  Response.Redirect("ErrorPage.aspx")
            Throw ex
        Finally
            conn.Close()
            conn.Dispose()
            trans1.Dispose()
        End Try

    End Function

    Function SAV_M(ByVal Amount As String, ByVal RecptNo As String, ByVal LoanNo As String, ByVal Transaction_Date As DateTime, ByVal lst_type As String, ByVal lst_mode As String, ByVal Chequeno As String, ByVal Remark As String, ByVal UserName As String, ByVal LoneeeName As String, ByVal FCODE As String, ByVal OfficeId As String)
        ' filldata()
        ' binddata()
        ' ltransdate = Session("lasttransdate")
        ''Dim len1 As Integer
        ''Dim genrecno As String
        'If CDbl(Amount) <= 0 Then Exit Sub
        If CDbl(Amount) <= 0 Then Exit Function
        Dim dr, dr1, dr2 As DataRow
        Dim rmtd_amt, int_r, int_t, pnl_r, pnl_t, eduemi, edubal, int_tD, int_tots, pnl_t_ots, DPPenal, WaiverAmount, PenalWaiver, InterestWaiver, CurrentPenal, CurrentInt As Double '//27/03/2024
        Dim bnk_r, bnk_b, prin_b, prin_r, blamt, g1 As Double
        Dim rcpt As String
        Dim g0, ENDDATEM As Date
        Dim i, m As Integer
        Dim q As Integer = 0
        pnl_t_ots = 0
        Dim per1, intdues As Integer
        per1 = 0
        intdues = 0
        CurrentInt = 0
        CurrentPenal = 0
        WaiverAmount = 0 '//27/03/2024
        PenalWaiver = 0 '//27/03/2024
        InterestWaiver = 0 '//27/03/2024
        Dim light As String
        Dim otsflag = False
        Dim disbdate, nxtduedate, lstdate, fduedt, ltransdate As Date
        Dim CurTransDate, PrevTransDate, PeriodOverDate As Date
        Dim loanbal, Tpnl, int_prv, bankchrge_b, notc_t, other_t, greencard, int_prvots, int_prv_penal, int_prefixbal, txtchrge_b, defaultintrate As Double
        Dim TransRemarks As String = Remark
        Dim int_BeforeDisb As Integer = 0 '25/06/2024
        Dim int_RR As Integer = 0, RRDate As Date, TransDate As Date '28/06/2024
        '---------------------------------------------

        'Dim ENDDATE As Date = SqlHelper.ExecuteScalar(strconn, CommandType.Text, "Select GETDATE()")
        'Dim TDate() As String = Transaction_Date.Split("/")
        'Dim TDate As Date = Date.ParseExact(Transaction_Date, "dd/MM/yyyy", CultureInfo.InvariantCulture)
        'Dim ENDDATE As Date = (TDate(1) & "/" & TDate(0) & "/" & TDate(2))

        Dim ENDDATE As Date = Transaction_Date

        Dim ds, ds2, ds3, ds1 As DataSet
        ' Dim strconn As String
        ' strconn = System.Configuration.ConfigurationManager.AppSettings("constr")
        'strconn = "data source=.\SQL2019;initial catalog=ksdc_live_latest;user id=sa;password=********;Connect Timeout=300000;"
        Dim conn As New SqlConnection(strconn)

        If RecptNo.Trim = "" Then
            ' myMsgBox("Please enter the Receipt No.")
            '  Exit Function 28/03/2024
        End If
        If Amount.Trim = "" Or Amount.Trim = 0 Then
            '  myMsgBox("Invalid Amount !!")
            Exit Function
        End If



        ''Closure Check

        'Dim totaldues As Integer 29/03/2024
        'totaldues = calc_ondate(LoanNo)

        'If Val(Amount) >= totaldues Then
        '    ds1 = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select int_ForClosing,int_Green,mny_repayamt from tbl_loanreg where  int_loanno ='" & LoanNo & "'")
        '    If ds1.Tables(0).Rows.Count > 0 Then
        '        dr1 = ds1.Tables(0).Rows(0)
        '        If dr1(0) = 0 Then
        '            If dr1(2) <> 0 Then
        '                'myMsgBox("Get Approval for Loan Closure from D.M/A.M !!!")
        '                Amount = 0
        '                'btnsave.Enabled = True
        '                Exit Function
        '            End If
        '        End If
        '    End If
        'End If


        'ENDDATE = CDate(txtm.Text + "/" + txtd.Text + "/" + txty.Text)
        ds = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select * from tbl_loanreg where int_loanno='" & LoanNo & "'")
        If ds.Tables.Count > 0 Then
            If ds.Tables(0).Rows.Count > 0 Then
                dr = ds.Tables(0).Rows(0)
                'fdate = dr("dt_first_due_date")
                'instamt = dr("mny_repayamt")
                'lduedate = DateAdd(DateInterval.Month, dr("int_repay_inst") - 1, fdate)
                'PeriodExt = 0
                OfficeId = dr("vchr_offid")
                disbdate = dr("dt_disbdate")
            End If
        End If

        If ENDDATE < disbdate Or ENDDATE > Date.Today Then
            '   myMsgBox("Invalid Date !!!. Please check")
            ' setFocus(txtd)
            If TransRemarks <> "SUBSIDY" Then   '29/06/2024
                Exit Function
            End If

        End If
        If ltransdate > ENDDATE Then
            ' myMsgBox("Invalid Date !!! Transaction on " & ltransdate.Day & "/" & ltransdate.Month & "/" & ltransdate.Year & " already exist. Please check")
            '  setFocus(txtd)
            Exit Function
        End If

        '''Receipt No - Migration 25/04/2024
        'ds = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "select Last_Receipt_No FROM tbl_Sub_Districts WHERE Office_Code like '" & OfficeId & "'")

        'If ds.Tables(0).Rows.Count > 0 Then
        '    dr = ds.Tables(0).Rows(0)
        '    RecptNo = dr("Last_Receipt_No") + 1

        'End If

        '  Dim strconn2 As String
        '  strconn = System.Configuration.ConfigurationManager.AppSettings("constr")
        Dim fdate, duedt, dte, lduedate As Date
        Dim Rcvamt, Dmdamt, instamt, Dbtamt, pnl, amt, TxtBal, intrate, penalrate, TXTIB, TXTPNLB, txtdefaulttotal, intemi_bal As Double
        '------------------------
        ds = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select * from tbl_loanreg where int_loanno='" & LoanNo & "'")
        If ds.Tables.Count > 0 Then
            If ds.Tables(0).Rows.Count > 0 Then
                dr = ds.Tables(0).Rows(0)
                fdate = dr("dt_first_due_date")
                PeriodOverDate = fdate.AddMonths(dr("int_repay_inst")).AddDays(-1)
                instamt = dr("mny_repayamt")
                lduedate = DateAdd(DateInterval.Month, dr("int_repay_inst") - 1, fdate)
                'lduedate = Format(lduedate, "dd/MM/yyyy")
                TxtBal = Format(dr("mny_loanbal"), "0.00")
                ltransdate = dr("dt_last_repay_date")
                intrate = Format(dr("int_rate_int"), "0.00")
                penalrate = Format(dr("int_rate_penal"), "0.00")
                intemi_bal = Format(dr("mny_intrbal"), "0.00")
                defaultintrate = intrate + penalrate
                'PeriodExt = dr("int_repay_inst_Ext")
                'If PeriodExt > 0 And lbl_rr.Text.Trim <> "Moratorium Loan" Then
                '    lbl_rr.Text = lbl_rr.Text & "  Moratorium Loan"
                'End If
                'If dr("int_inst_disbursed") = dr("int_disb_inst") And dr("mny_repayamt") <> 0 Then
                '    dsbcomplt = 1
                'End If

                int_RR = dr("int_RR")
                RRDate = dr("dt_RR_Date")

                If dr("int_ots") = 1 Then
                    otsflag = True
                    ds1 = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select * from tbl_WaiverDetails where int_loanno='" & LoanNo & "'")
                    If ds1.Tables.Count > 0 Then
                        If ds1.Tables(0).Rows.Count > 0 Then
                            dr1 = ds1.Tables(0).Rows(0)
                            WaiverAmount = dr1("int_wavier_amt")
                        End If
                    End If
                End If
            End If
        End If

        '---------------------------------------------Case.1 Not fully Disbursed 25/06/2024
        If TransRemarks = "SUBSIDY" Then
            If SqlHelper.ExecuteScalar(strconn, CommandType.Text, "Select ISNULL(COUNT(*),0) FROM tbl_LOANTRANS WHERE INT_LOANNO ='" & LoanNo & "' AND int_type=0 AND chr_remark='LOAN DISBURSED'") <= 0 Then  '26/06/2024
                int_BeforeDisb = 1
            End If

            If int_BeforeDisb = 1 Then
                conn.Open()
                Dim transBD As SqlTransaction = conn.BeginTransaction()

                Try
                    SqlHelper.ExecuteNonQuery(transBD, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_prin_dues,chr_remark, int_type, vchr_offidC)VALUES('" & LoanNo & "','" & CDate(ENDDATE) & "','" & RecptNo & "','" & lst_mode & "','" & Chequeno & "','Receipt'," & CDbl(Amount) & "," & CDbl(Amount) & "," & CDbl(TxtBal) - CDbl(Amount) & ",'SUBSIDY',0,'" & Mid(LoanNo, 1, 4) & "')")
                    ' SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "UPDATE tbl_Sub_Districts SET Last_Receipt_No =Last_Receipt_No + 1 WHERE Office_Code like '" & Mid(LoanNo, 1, 4) & "'") ' 27-04-2024

                    ds = SqlHelper.ExecuteDataset(transBD, CommandType.Text, "SELECT * FROM TBL_ACCOUNT WHERE CHR_UNDER='" & FCODE & "' AND chr_head='Minor' and chr_type='Receipt' and chr_Name='PRINCIPAL' ")
                    If ds.Tables(0).Rows.Count > 0 Then
                        dr = ds.Tables(0).Rows(0)
                        SqlHelper.ExecuteNonQuery(transBD, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time,vchr_offidC)VALUES('" & RecptNo & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode & "'," & CDbl(Amount) & ",'" & CDate(ENDDATE) & "','" & LoanNo & "','" & Left(LoneeeName, 48) & "','SUBSIDY','" & Mid(LoanNo, 1, 4) & "','" & UserName & "',GETDATE(),'" & Mid(LoanNo, 1, 4) & "')")
                    End If

                    SqlHelper.ExecuteNonQuery(transBD, CommandType.Text, "UPDATE tbl_loanreg set mny_loanbal=" & CDbl(TxtBal) - CDbl(Amount) & ",mny_prin_due=" & CDbl(TxtBal) - CDbl(Amount) & ",dt_last_repay_date='" & CDate(ENDDATE) & "' where int_loanno='" & LoanNo & "'")

                    transBD.Commit()

                    conn.Close()
                    conn.Dispose()
                    transBD.Dispose()
                    Exit Function
                Catch ex As Exception
                    transBD.Rollback()
                    transBD.Dispose()
                    ' Response.Redirect("ErrorPage.aspx")
                    Throw ex
                Finally
                    conn.Close()
                    conn.Dispose()
                    transBD.Dispose()
                End Try
            End If
        End If

        '**********if credit balance

        If CDbl(TxtBal) = 0 Then
            '  txt1.Text = Amount
            '  txt10.Text = Amount
            conn.Open()
            Dim trans2 As SqlTransaction = conn.BeginTransaction()

            Try

                If lst_type = 0 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate(ENDDATE) & "','" & RecptNo & "','" & lst_mode & "','" & Chequeno & "','Receipt'," & CDbl(Amount) & ",0,0,0,0,0,0,0,0,0,0,'" & "''" & "','" & TransRemarks & "',1,'" & Mid(LoanNo, 1, 4) & "')")
                If lst_type = 1 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate(ENDDATE) & "','" & RecptNo & "','" & lst_mode & "','" & Chequeno & "','Receipt'," & CDbl(Amount) & ",0,0,0,0,0,0,0,0,0,0,'" & "''" & "','" & TransRemarks & "',6,'" & Mid(LoanNo, 1, 4) & "')")
                If lst_type = 2 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate(ENDDATE) & "','" & RecptNo & "','" & lst_mode & "','" & Chequeno & "','Receipt'," & CDbl(Amount) & ",0,0,0,0,0,0,0,0,0,0,'" & "''" & "','" & TransRemarks & "',15,'" & Mid(LoanNo, 1, 4) & "')")
                SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_balance(int_loanno,chr_rec_no,int_bal)VALUES('" & LoanNo & "','" & RecptNo & "'," & CDbl(Amount) & ")")
                ' SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "UPDATE tbl_Sub_Districts SET Last_Receipt_No =Last_Receipt_No + 1 WHERE Office_Code like '" & Mid(LoanNo, 1, 4) & "'") ' 27-04-2024

                ds = SqlHelper.ExecuteDataset(trans2, CommandType.Text, "SELECT * FROM TBL_ACCOUNT WHERE CHR_UNDER='" & FCODE & "' AND chr_head='Minor' and chr_type='Receipt'")
                If ds.Tables(0).Rows.Count > 0 Then
                    dr = ds.Tables(0).Rows(0)
                    SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time,vchr_offidC)VALUES('" & RecptNo & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode & "'," & CDbl(Amount) & ",'" & CDate(ENDDATE) & "','" & LoanNo & "','" & Left(LoneeeName, 48) & "','REPAYMENT','" & Mid(LoanNo, 1, 4) & "','" & UserName & "',GETDATE(),'" & Mid(LoanNo, 1, 4) & "')")
                End If


                trans2.Commit()

                conn.Close()
                conn.Dispose()
                trans2.Dispose()
                Exit Function
            Catch ex As Exception
                trans2.Rollback()
                trans2.Dispose()
                ' Response.Redirect("ErrorPage.aspx")
                Throw ex
            Finally
                conn.Close()
                conn.Dispose()
                trans2.Dispose()
            End Try
        End If

        TransDate = PeriodOverDate

        If int_RR = 1 And RRDate <> "01/01/1900" Then
            If ENDDATE > RRDate Then
                TransDate = RRDate
            ElseIf ENDDATE > PeriodOverDate Then
                TransDate = PeriodOverDate
            End If
        End If

        '**********Case.2(a) LEDGER POSTING ON 31 MARCH *************
        conn.Open()


        If fdate <> CDate("01/01/1900") Then

            If ENDDATE > TransDate Then

                PrevTransDate = ltransdate
                CurTransDate = ENDDATE

                Dim financialYearEndings As List(Of Date) = GetFinancialYearEndings(PrevTransDate, CurTransDate)
                If ENDDATE > PeriodOverDate Then
                    financialYearEndings.Add(PeriodOverDate)
                End If
                If int_RR = 1 And RRDate <> "01/01/1900" And ENDDATE > RRDate Then
                    financialYearEndings.Add(RRDate)
                End If
                financialYearEndings.Sort()
                For Each fyEnding In financialYearEndings

                    If fyEnding = PeriodOverDate Then
                        Dim trans3 As SqlTransaction = conn.BeginTransaction()
                        Try
                            ds = SqlHelper.ExecuteDataset(trans3, CommandType.Text, "select * FROM tbl_LOANTRANS WHERE INT_LOANNO ='" & LoanNo & "' AND dt_transaction='" & PeriodOverDate & "' AND chr_remark='PERIOD OVER'")
                            If ds.Tables(0).Rows.Count = 0 Then
                                int_t = 0
                                pnl_t = 0
                                pnl_t = CalcPenal(PeriodOverDate, LoanNo)
                                CurrentPenal = pnl_t
                                'If int_prv = 0 Then
                                ds2 = SqlHelper.ExecuteDataset(trans3, CommandType.Text, "Select int_int_dues as InterestDues,int_penal_dues as PenalDues from tbl_loanTrans where int_loanno = '" & LoanNo & "' and int_type<=20 and chr_remark <> 'FINANCIAL YEAR END1' and int_type not in (4,5,12) order by dt_transaction,int_transid")
                                dr1 = ds2.Tables(0).Rows(ds2.Tables(0).Rows.Count - 1)

                                int_prv = dr1("InterestDues")
                                int_prv_penal = dr1("PenalDues")
                                'End If
                                int_t = int_prv + System.Math.Round((CDbl(TxtBal) * (DateDiff(DateInterval.Day, ltransdate, PeriodOverDate) * intrate) / 36500), 2)
                                CurrentInt = System.Math.Round((CDbl(TxtBal) * (DateDiff(DateInterval.Day, ltransdate, PeriodOverDate) * intrate) / 36500), 2)
                                '05/01/2024
                                pnl_t = pnl_t + int_prv_penal
                                '
                                ''''''''''''''''' RR Initiated ''''''''''''''''''''''\\ 28/06/2024
                                ds3 = SqlHelper.ExecuteDataset(trans3, CommandType.Text, "select * FROM tbl_loantrans WHERE INT_LOANNO ='" & LoanNo & "' and chr_remark in ('RR Initiated')")
                                If ds3.Tables(0).Rows.Count > 0 Then
                                    pnl_r = 0
                                    pnl_t = 0
                                    'int_t = int_prv + System.Math.Round((TxtBal * (DateDiff(DateInterval.Day, ltransdate, CDate("3/31/" & Year(CurTransDate))) * defaultintrate) / 36500), 0)
                                    int_t = int_prv + System.Math.Round(CDbl(TxtBal * (DateDiff(DateInterval.Day, ltransdate, PeriodOverDate) * defaultintrate) / 36500), 2)
                                    pnl_t = int_prv_penal
                                    int_prv_penal = pnl_t
                                    CurrentPenal = 0
                                    CurrentInt = System.Math.Round(CDbl(TxtBal * (DateDiff(DateInterval.Day, ltransdate, PeriodOverDate) * defaultintrate) / 36500), 2) ''//01/04/2024
                                End If
                                ''''''''''''''''' RR Initiated End''''''''''''''''''''''\\ 28/06/2024
                                '''
                                If lst_type = 0 Then SqlHelper.ExecuteNonQuery(trans3, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & PeriodOverDate & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','PERIOD OVER',10,'" & Mid(LoanNo, 1, 4) & "')")
                                If lst_type = 1 Then SqlHelper.ExecuteNonQuery(trans3, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & PeriodOverDate & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','PERIOD OVER',9,'" & Mid(LoanNo, 1, 4) & "')")
                                If lst_type = 2 Then SqlHelper.ExecuteNonQuery(trans3, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & PeriodOverDate & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','PERIOD OVER',18,'" & Mid(LoanNo, 1, 4) & "')")

                                SqlHelper.ExecuteNonQuery(trans3, CommandType.Text, "UPDATE tbl_loanreg set dt_last_repay_date='" & PeriodOverDate & "' where int_loanno='" & LoanNo & "'")
                                ''
                                ltransdate = PeriodOverDate
                                int_prv = int_t
                                '05/01/2024
                                int_prv = 0
                            End If
                            trans3.Commit()
                        Catch ex As Exception
                            trans3.Rollback()
                            trans3.Dispose()
                            'Response.Redirect("ErrorPage.aspx")
                            Throw ex
                        Finally
                            trans3.Dispose()
                        End Try
                    ElseIf fyEnding = RRDate Then
                        Dim trans3 As SqlTransaction = conn.BeginTransaction()
                        Try
                            ds = SqlHelper.ExecuteDataset(trans3, CommandType.Text, "select * FROM tbl_LOANTRANS WHERE INT_LOANNO ='" & LoanNo & "' AND dt_transaction='" & RRDate & "' AND chr_remark='RR Initiated'")
                            If ds.Tables(0).Rows.Count = 0 Then
                                int_t = 0
                                pnl_t = 0
                                pnl_t = CalcPenal(RRDate, LoanNo)
                                CurrentPenal = pnl_t
                                'If int_prv = 0 Then
                                ds2 = SqlHelper.ExecuteDataset(trans3, CommandType.Text, "Select int_int_dues as InterestDues,int_penal_dues as PenalDues from tbl_loanTrans where int_loanno = '" & LoanNo & "' and int_type<=20 and chr_remark <> 'FINANCIAL YEAR END1' and int_type not in (4,5,12) order by dt_transaction,int_transid")
                                dr1 = ds2.Tables(0).Rows(ds2.Tables(0).Rows.Count - 1)

                                int_prv = dr1("InterestDues")
                                int_prv_penal = dr1("PenalDues")
                                'End If
                                int_t = int_prv + System.Math.Round((CDbl(TxtBal) * (DateDiff(DateInterval.Day, ltransdate, RRDate) * intrate) / 36500), 2)
                                CurrentInt = System.Math.Round((CDbl(TxtBal) * (DateDiff(DateInterval.Day, ltransdate, RRDate) * intrate) / 36500), 2)
                                '05/01/2024
                                pnl_t = pnl_t + int_prv_penal
                                '
                                ''''''''''''''''' Period Over Start''''''''''''''''''''''\\ 28/06/2024
                                ds3 = SqlHelper.ExecuteDataset(trans3, CommandType.Text, "select * FROM tbl_loantrans WHERE INT_LOANNO ='" & LoanNo & "' and chr_remark in ('PERIOD OVER')")
                                If ds3.Tables(0).Rows.Count > 0 Then
                                    pnl_r = 0
                                    pnl_t = 0
                                    'int_t = int_prv + System.Math.Round((TxtBal * (DateDiff(DateInterval.Day, ltransdate, CDate("3/31/" & Year(CurTransDate))) * defaultintrate) / 36500), 0)
                                    int_t = int_prv + System.Math.Round(CDbl(TxtBal * (DateDiff(DateInterval.Day, ltransdate, RRDate) * defaultintrate) / 36500), 2)
                                    pnl_t = int_prv_penal
                                    int_prv_penal = pnl_t
                                    CurrentPenal = 0
                                    CurrentInt = System.Math.Round(CDbl(TxtBal * (DateDiff(DateInterval.Day, ltransdate, RRDate) * defaultintrate) / 36500), 2) ''//01/04/2024
                                End If
                                ''''''''''''''''' Period Over End''''''''''''''''''''''\\ 28/06/2024
                                '''
                                If lst_type = 0 Then SqlHelper.ExecuteNonQuery(trans3, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & RRDate & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','RR Initiated',10,'" & Mid(LoanNo, 1, 4) & "')")
                                If lst_type = 1 Then SqlHelper.ExecuteNonQuery(trans3, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & RRDate & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','RR Initiated',9,'" & Mid(LoanNo, 1, 4) & "')")
                                If lst_type = 2 Then SqlHelper.ExecuteNonQuery(trans3, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & RRDate & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','RR Initiated',18,'" & Mid(LoanNo, 1, 4) & "')")

                                SqlHelper.ExecuteNonQuery(trans3, CommandType.Text, "UPDATE tbl_loanreg set dt_last_repay_date='" & RRDate & "' where int_loanno='" & LoanNo & "'")
                                ''
                                ltransdate = RRDate
                                int_prv = int_t
                                '05/01/2024
                                int_prv = 0
                            End If
                            trans3.Commit()
                        Catch ex As Exception
                            trans3.Rollback()
                            trans3.Dispose()
                            'Response.Redirect("ErrorPage.aspx")
                            Throw ex
                        Finally
                            trans3.Dispose()
                        End Try
                    Else
                        CurTransDate = fyEnding.AddDays(1)

                        If Month(CurTransDate) > 3 And disbdate <= CDate("03/31/" & Year(CurTransDate)) And ltransdate < CDate("03/31/" & Year(CurTransDate)) Then


                            Dim trans2 As SqlTransaction = conn.BeginTransaction()

                            Try

                                ds = SqlHelper.ExecuteDataset(trans2, CommandType.Text, "select * FROM tbl_LOANTRANS WHERE INT_LOANNO ='" & LoanNo & "' AND dt_transaction='" & CDate("03/31/" & Year(CurTransDate)) & "'")
                                If ds.Tables(0).Rows.Count = 0 Then

                                    int_t = 0
                                    pnl_t = 0
                                    '----------------------------------
                                    pnl_t = CalcPenal(CDate("03/31/" & Year(CurTransDate)), LoanNo)
                                    CurrentPenal = pnl_t ''//01/04/2024


                                    'If pnl_t > 0 And pnl_t <= 1 Then //29/03/2024
                                    '    pnl_t = 1
                                    'Else
                                    '    pnl_t = System.Math.Round(pnl_t)
                                    'End If

                                    '----------------------------------
                                    '''int_prv
                                    'If int_prv = 0 Then 05/01/2024
                                    ds2 = SqlHelper.ExecuteDataset(trans2, CommandType.Text, "Select int_int_dues as InterestDues,int_penal_dues as PenalDues from tbl_loanTrans where int_loanno = '" & LoanNo & "' and int_type<=20 and chr_remark <> 'FINANCIAL YEAR END1' and int_type not in (4,5,12) order by dt_transaction,int_transid")
                                    dr1 = ds2.Tables(0).Rows(ds2.Tables(0).Rows.Count - 1)

                                    int_prv = dr1("InterestDues")
                                    int_prv_penal = dr1("PenalDues")
                                    'End If
                                    '''
                                    ' int_t = int_prv + System.Math.Round((CDbl(TxtBal) * (DateDiff(DateInterval.Day, ltransdate, CDate("3/31/2024")) * intrate) / 36500, 0))

                                    int_t = int_prv + System.Math.Round((CDbl(TxtBal) * (DateDiff(DateInterval.Day, ltransdate, CDate("03/31/" & Year(CurTransDate).ToString)) * intrate) / 36500), 2)
                                    CurrentInt = System.Math.Round((CDbl(TxtBal) * (DateDiff(DateInterval.Day, ltransdate, CDate("03/31/" & Year(CurTransDate).ToString)) * intrate) / 36500), 2)
                                    ' 05/01/2024
                                    pnl_t = pnl_t + int_prv_penal
                                    ' 
                                    ''''''''''''''''' Period Over Start''''''''''''''''''''''\\ 28/03/2024
                                    ds3 = SqlHelper.ExecuteDataset(trans2, CommandType.Text, "select * FROM tbl_loantrans WHERE INT_LOANNO ='" & LoanNo & "' and chr_remark in ('PERIOD OVER','RR Initiated')")
                                    If ds3.Tables(0).Rows.Count > 0 Then
                                        pnl_r = 0
                                        pnl_t = 0
                                        'int_t = int_prv + System.Math.Round((TxtBal * (DateDiff(DateInterval.Day, ltransdate, CDate("3/31/" & Year(CurTransDate))) * defaultintrate) / 36500), 0)
                                        int_t = int_prv + System.Math.Round(CDbl(TxtBal * (DateDiff(DateInterval.Day, ltransdate, CDate("03/31/" & Year(CurTransDate))) * defaultintrate) / 36500), 2)
                                        pnl_t = int_prv_penal
                                        int_prv_penal = pnl_t
                                        CurrentPenal = 0
                                        CurrentInt = System.Math.Round(CDbl(TxtBal * (DateDiff(DateInterval.Day, ltransdate, CDate("03/31/" & Year(CurTransDate))) * defaultintrate) / 36500), 2) ''//01/04/2024
                                    End If
                                    ''''''''''''''''' Period Over End''''''''''''''''''''''\\ 28/03/2024
                                    ''01/04/2024
                                    If lst_type = 0 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("03/31/" & Year(CurTransDate)) & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',10,'" & Mid(LoanNo, 1, 4) & "')")
                                    If lst_type = 1 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("03/31/" & Year(CurTransDate)) & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',9,'" & Mid(LoanNo, 1, 4) & "')")
                                    If lst_type = 2 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("03/31/" & Year(CurTransDate)) & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',18,'" & Mid(LoanNo, 1, 4) & "')")

                                    'If lst_type = 0 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("31/03/" & Year(CurTransDate)) & "','M" + Year(CurTransDate) + CStr(CInt(Mid(LoanNo, 5, Len(LoanNo) - 4))) + "','','','Receipt',0,0,0," & int_t & "," & pnl_t & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',10,'" & Mid(LoanNo, 1, 4) & "')")
                                    'If lst_type = 1 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("31/03/" & Year(CurTransDate)) & "','M" + Year(CurTransDate) + CStr(CInt(Mid(LoanNo, 5, Len(LoanNo) - 4))) + "','','','Receipt',0,0,0," & int_t & "," & pnl_t & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',9,'" & Mid(LoanNo, 1, 4) & "')")
                                    'If lst_type = 2 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("31/03/" & Year(CurTransDate)) & "','M" + Year(CurTransDate) + CStr(CInt(Mid(LoanNo, 5, Len(LoanNo) - 4))) + "','','','Receipt',0,0,0," & int_t & "," & pnl_t & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',18,'" & Mid(LoanNo, 1, 4) & "')")

                                    ''
                                    SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "UPDATE tbl_loanreg set dt_last_repay_date='" & CDate("03/31/" & Year(CurTransDate)) & "' where int_loanno='" & LoanNo & "'")
                                    ''
                                    ltransdate = CDate("03/31/" & Year(CurTransDate))
                                    int_prv = int_t

                                End If
                                trans2.Commit()
                                trans2.Dispose()
                            Catch ex As Exception
                                trans2.Rollback()
                                trans2.Dispose()
                                '   Response.Redirect("ErrorPage.aspx")
                                Throw ex
                            Finally
                                trans2.Dispose()
                            End Try
                        End If
                    End If

                Next

            End If
        End If


        If ltransdate > fdate And fdate <> CDate("01/01/1900") Then

            PrevTransDate = ltransdate
            CurTransDate = ENDDATE

            Dim financialYearEndings As List(Of Date) = GetFinancialYearEndings(PrevTransDate, CurTransDate)

            For Each fyEnding In financialYearEndings
                CurTransDate = fyEnding.AddDays(1)

                If Month(CurTransDate) > 3 And disbdate <= CDate("03/31/" & Year(CurTransDate)) And ltransdate < CDate("03/31/" & Year(CurTransDate)) Then

                    Dim trans2 As SqlTransaction = conn.BeginTransaction()

                    Try

                        ds = SqlHelper.ExecuteDataset(trans2, CommandType.Text, "select * FROM tbl_LOANTRANS WHERE INT_LOANNO ='" & LoanNo & "' AND dt_transaction='" & CDate("03/31/" & Year(CurTransDate)) & "'")
                        If ds.Tables(0).Rows.Count = 0 Then

                            int_t = 0
                            pnl_t = 0
                            '----------------------------------
                            pnl_t = CalcPenal(CDate("03/31/" & Year(CurTransDate)), LoanNo)
                            CurrentPenal = pnl_t ''//01/04/2024


                            'If pnl_t > 0 And pnl_t <= 1 Then //29/03/2024
                            '    pnl_t = 1
                            'Else
                            '    pnl_t = System.Math.Round(pnl_t)
                            'End If

                            '----------------------------------
                            '''int_prv
                            '''If int_prv = 0 Then '20/06/2024
                            ds2 = SqlHelper.ExecuteDataset(trans2, CommandType.Text, "Select int_int_dues as InterestDues,int_penal_dues as PenalDues from tbl_loanTrans where int_loanno = '" & LoanNo & "' and int_type<=20 and chr_remark <> 'FINANCIAL YEAR END1' and int_type not in (4,5,12) order by dt_transaction,int_transid")
                            dr1 = ds2.Tables(0).Rows(ds2.Tables(0).Rows.Count - 1)

                            int_prv = dr1("InterestDues")
                            int_prv_penal = dr1("PenalDues")
                            '''End If
                            '''
                            ' int_t = int_prv + System.Math.Round((CDbl(TxtBal) * (DateDiff(DateInterval.Day, ltransdate, CDate("3/31/2024")) * intrate) / 36500, 0))

                            int_t = int_prv + System.Math.Round((CDbl(TxtBal) * (DateDiff(DateInterval.Day, ltransdate, CDate("03/31/" & Year(CurTransDate).ToString)) * intrate) / 36500), 2)
                            CurrentInt = System.Math.Round((CDbl(TxtBal) * (DateDiff(DateInterval.Day, ltransdate, CDate("03/31/" & Year(CurTransDate).ToString)) * intrate) / 36500), 2)

                            pnl_t = pnl_t + int_prv_penal '20/06/2024
                            ''''''''''''''''' Period Over Start''''''''''''''''''''''\\ 28/03/2024
                            ds3 = SqlHelper.ExecuteDataset(trans2, CommandType.Text, "select * FROM tbl_loantrans WHERE INT_LOANNO ='" & LoanNo & "' and chr_remark in ('PERIOD OVER','RR Initiated')")
                            If ds3.Tables(0).Rows.Count > 0 Then
                                pnl_r = 0
                                pnl_t = 0
                                'int_t = int_prv + System.Math.Round((TxtBal * (DateDiff(DateInterval.Day, ltransdate, CDate("3/31/" & Year(CurTransDate))) * defaultintrate) / 36500), 0)
                                int_t = int_prv + System.Math.Round(CDbl(TxtBal * (DateDiff(DateInterval.Day, ltransdate, CDate("03/31/" & Year(CurTransDate))) * defaultintrate) / 36500), 2)
                                pnl_t = int_prv_penal
                                int_prv_penal = pnl_t
                                CurrentPenal = 0
                                CurrentInt = System.Math.Round(CDbl(TxtBal * (DateDiff(DateInterval.Day, ltransdate, CDate("03/31/" & Year(CurTransDate))) * defaultintrate) / 36500), 2) ''//01/04/2024
                            End If
                            ''''''''''''''''' Period Over End''''''''''''''''''''''\\ 28/03/2024
                            ''01/04/2024
                            If lst_type = 0 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("03/31/" & Year(CurTransDate)) & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',10,'" & Mid(LoanNo, 1, 4) & "')")
                            If lst_type = 1 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("03/31/" & Year(CurTransDate)) & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',9,'" & Mid(LoanNo, 1, 4) & "')")
                            If lst_type = 2 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("03/31/" & Year(CurTransDate)) & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',18,'" & Mid(LoanNo, 1, 4) & "')")

                            'If lst_type = 0 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("31/03/" & Year(CurTransDate)) & "','M" + Year(CurTransDate) + CStr(CInt(Mid(LoanNo, 5, Len(LoanNo) - 4))) + "','','','Receipt',0,0,0," & int_t & "," & pnl_t & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',10,'" & Mid(LoanNo, 1, 4) & "')")
                            'If lst_type = 1 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("31/03/" & Year(CurTransDate)) & "','M" + Year(CurTransDate) + CStr(CInt(Mid(LoanNo, 5, Len(LoanNo) - 4))) + "','','','Receipt',0,0,0," & int_t & "," & pnl_t & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',9,'" & Mid(LoanNo, 1, 4) & "')")
                            'If lst_type = 2 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("31/03/" & Year(CurTransDate)) & "','M" + Year(CurTransDate) + CStr(CInt(Mid(LoanNo, 5, Len(LoanNo) - 4))) + "','','','Receipt',0,0,0," & int_t & "," & pnl_t & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',18,'" & Mid(LoanNo, 1, 4) & "')")

                            ''
                            SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "UPDATE tbl_loanreg set dt_last_repay_date='" & CDate("03/31/" & Year(CurTransDate)) & "' where int_loanno='" & LoanNo & "'")
                            ''
                            ltransdate = CDate("03/31/" & Year(CurTransDate))
                            int_prv = int_t

                        End If
                        trans2.Commit()
                        trans2.Dispose()
                    Catch ex As Exception
                        trans2.Rollback()
                        trans2.Dispose()
                        '   Response.Redirect("ErrorPage.aspx")
                        Throw ex
                    Finally
                        trans2.Dispose()
                    End Try
                End If

            Next

        End If
        '---------------------------------------------

        If ltransdate < fdate And fdate <> CDate("01/01/1900") And ENDDATE >= ltransdate Then             ''14/05/2024

            PrevTransDate = ltransdate
            CurTransDate = ENDDATE

            Dim financialYearEndings As List(Of Date) = GetFinancialYearEndings(PrevTransDate, CurTransDate)

            For Each fyEnding In financialYearEndings
                CurTransDate = fyEnding.AddDays(1)

                If Month(CurTransDate) > 3 And disbdate <= CDate("03/31/" & Year(CurTransDate)) And ltransdate < CDate("03/31/" & Year(CurTransDate)) Then

                    Dim trans2 As SqlTransaction = conn.BeginTransaction()

                    Try

                        ds = SqlHelper.ExecuteDataset(trans2, CommandType.Text, "select * FROM tbl_LOANTRANS WHERE INT_LOANNO ='" & LoanNo & "' AND dt_transaction='" & CDate("03/31/" & Year(CurTransDate)) & "'")
                        If ds.Tables(0).Rows.Count = 0 Then

                            int_t = 0
                            pnl_t = 0
                            '----------------------------------
                            pnl_t = CalcPenal(CDate("03/31/" & Year(CurTransDate)), LoanNo)
                            CurrentPenal = pnl_t ''//01/04/2024


                            'If pnl_t > 0 And pnl_t <= 1 Then //29/03/2024
                            '    pnl_t = 1
                            'Else
                            '    pnl_t = System.Math.Round(pnl_t)
                            'End If

                            '----------------------------------
                            '''int_prv
                            '''If int_prv = 0 Then '20/06/2024
                            ds2 = SqlHelper.ExecuteDataset(trans2, CommandType.Text, "Select int_int_dues as InterestDues,int_penal_dues as PenalDues from tbl_loanTrans where int_loanno = '" & LoanNo & "' and int_type<=20 and chr_remark <> 'FINANCIAL YEAR END1' and int_type not in (4,5,12) order by dt_transaction,int_transid")
                            dr1 = ds2.Tables(0).Rows(ds2.Tables(0).Rows.Count - 1)

                            int_prv = dr1("InterestDues")
                            int_prv_penal = dr1("PenalDues")
                            '''End If  
                            '''
                            ' int_t = int_prv + System.Math.Round((CDbl(TxtBal) * (DateDiff(DateInterval.Day, ltransdate, CDate("3/31/2024")) * intrate) / 36500, 0))

                            int_t = int_prv + System.Math.Round((CDbl(TxtBal) * (DateDiff(DateInterval.Day, ltransdate, CDate("03/31/" & Year(CurTransDate).ToString)) * intrate) / 36500), 2)
                            CurrentInt = System.Math.Round((CDbl(TxtBal) * (DateDiff(DateInterval.Day, ltransdate, CDate("03/31/" & Year(CurTransDate).ToString)) * intrate) / 36500), 2)

                            pnl_t = pnl_t + int_prv_penal '20/06/2024

                            ''''''''''''''''' Period Over Start''''''''''''''''''''''\\ 28/03/2024
                            ds3 = SqlHelper.ExecuteDataset(trans2, CommandType.Text, "select * FROM tbl_loantrans WHERE INT_LOANNO ='" & LoanNo & "' and chr_remark in ('PERIOD OVER','RR Initiated')")
                            If ds3.Tables(0).Rows.Count > 0 Then
                                pnl_r = 0
                                pnl_t = 0
                                'int_t = int_prv + System.Math.Round((TxtBal * (DateDiff(DateInterval.Day, ltransdate, CDate("3/31/" & Year(CurTransDate))) * defaultintrate) / 36500), 0)
                                int_t = int_prv + System.Math.Round(CDbl(TxtBal * (DateDiff(DateInterval.Day, ltransdate, CDate("03/31/" & Year(CurTransDate))) * defaultintrate) / 36500), 2)
                                pnl_t = int_prv_penal
                                int_prv_penal = pnl_t
                                CurrentPenal = 0
                                CurrentInt = System.Math.Round(CDbl(TxtBal * (DateDiff(DateInterval.Day, ltransdate, CDate("03/31/" & Year(CurTransDate))) * defaultintrate) / 36500), 2) ''//01/04/2024
                            End If
                            ''''''''''''''''' Period Over End''''''''''''''''''''''\\ 28/03/2024
                            ''01/04/2024
                            If lst_type = 0 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("03/31/" & Year(CurTransDate)) & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',10,'" & Mid(LoanNo, 1, 4) & "')")
                            If lst_type = 1 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("03/31/" & Year(CurTransDate)) & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',9,'" & Mid(LoanNo, 1, 4) & "')")
                            If lst_type = 2 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("03/31/" & Year(CurTransDate)) & "','','','','Receipt',0,0,0," & CurrentInt & "," & CurrentPenal & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',18,'" & Mid(LoanNo, 1, 4) & "')")

                            'If lst_type = 0 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("31/03/" & Year(CurTransDate)) & "','M" + Year(CurTransDate) + CStr(CInt(Mid(LoanNo, 5, Len(LoanNo) - 4))) + "','','','Receipt',0,0,0," & int_t & "," & pnl_t & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',10,'" & Mid(LoanNo, 1, 4) & "')")
                            'If lst_type = 1 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("31/03/" & Year(CurTransDate)) & "','M" + Year(CurTransDate) + CStr(CInt(Mid(LoanNo, 5, Len(LoanNo) - 4))) + "','','','Receipt',0,0,0," & int_t & "," & pnl_t & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',9,'" & Mid(LoanNo, 1, 4) & "')")
                            'If lst_type = 2 Then SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate("31/03/" & Year(CurTransDate)) & "','M" + Year(CurTransDate) + CStr(CInt(Mid(LoanNo, 5, Len(LoanNo) - 4))) + "','','','Receipt',0,0,0," & int_t & "," & pnl_t & ",0,0," & CDbl(TxtBal) & "," & int_t & "," & pnl_t & ",0,'','FINANCIAL YEAR END',18,'" & Mid(LoanNo, 1, 4) & "')")

                            ''
                            SqlHelper.ExecuteNonQuery(trans2, CommandType.Text, "UPDATE tbl_loanreg set dt_last_repay_date='" & CDate("03/31/" & Year(CurTransDate)) & "' where int_loanno='" & LoanNo & "'")
                            ''
                            ltransdate = CDate("03/31/" & Year(CurTransDate))
                            int_prv = int_t

                        End If
                        trans2.Commit()
                        trans2.Dispose()
                    Catch ex As Exception
                        trans2.Rollback()
                        trans2.Dispose()
                        '   Response.Redirect("ErrorPage.aspx")
                        Throw ex
                    Finally
                        trans2.Dispose()
                    End Try
                End If

            Next

        End If

        '  strconn = System.Configuration.ConfigurationManager.AppSettings("constr")

        ds = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select dt_transaction as Date,chr_rec_no as RecNo,int_amt as Amount,int_debit as Debit,int_prin_amt as Principal,int_int_amt as Interest,int_penal_amt as Penal,int_bank_charge_r as bankchrg,int_eduint_amt as eduint,int_int_r as intr,int_penal_r as penlr,int_prin_dues as PrincipalDues,int_int_dues as InterestDues,int_penal_dues as PenalDues,chr_remark as Remarks,chr_transtype as Type,int_debit_b as DebitBal,int_transid,int_type from tbl_loanTrans where int_loanno = '" & LoanNo & "' and int_type<=20 and chr_remark <> 'FINANCIAL YEAR END1' order by dt_transaction,int_transid")

        If ds.Tables(0).Rows.Count > 0 Then

            notc_t = IIf(IsDBNull(ds.Tables(0).Compute("sum(DebitBal)", "DebitBal>=0 and Type='Payment' and (int_type=4 or int_type=8)")), 0, ds.Tables(0).Compute("sum(DebitBal)", "DebitBal>=0 and Type='Payment' and (int_type=4 or int_type=8)"))
            other_t = IIf(IsDBNull(ds.Tables(0).Compute("sum(DebitBal)", "DebitBal>=0 and Type='Payment' and int_type=12")), 0, ds.Tables(0).Compute("sum(DebitBal)", "DebitBal>=0 and Type='Payment' and int_type=12"))
            bankchrge_b = IIf(IsDBNull(ds.Tables(0).Compute("sum(DebitBal)", "DebitBal>=0 and Type='Payment' and (int_type=5 or int_type=7)")), 0, ds.Tables(0).Compute("sum(DebitBal)", "DebitBal>=0 and Type='Payment' and (int_type=5 or int_type=7)"))
            txtchrge_b = System.Math.Round(IIf(IsDBNull(ds.Tables(0).Compute("sum(DebitBal)", "DebitBal>=0 and Type='Payment'")), 0, ds.Tables(0).Compute("sum(DebitBal)", "DebitBal>=0 and Type='Payment'")), 0)
        End If

        'If int_prv = 0 Or int_prv_penal = 0 Then 22/06/2024
        ds2 = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select int_int_dues as InterestDues,int_penal_dues as PenalDues from tbl_loanTrans where int_loanno = '" & LoanNo & "' and int_type<=20 and chr_remark <> 'FINANCIAL YEAR END1' and int_type not in (4,5,12) order by dt_transaction,int_transid")
        dr1 = ds2.Tables(0).Rows(ds2.Tables(0).Rows.Count - 1)
        int_prv = dr1("InterestDues")
        int_prv_penal = dr1("PenalDues")
        'End If
        '---------------------------------------------Total Penal as on transaction date
        Dim trans1 As SqlTransaction = conn.BeginTransaction()

        Try


            pnl_t = 0
            pnl_t = CalcPenal(ENDDATE, LoanNo)
            pnl_t = Math.Round(pnl_t, 2)
            CurrentPenal = pnl_t
            pnl_t = pnl_t + int_prv_penal
            'If pnl_t > 0 And pnl_t <= 1 Then // 29/03/2024
            '    pnl_t = 1
            'ElseIf pnl_t < 0 Then
            '    pnl_t = 0
            'Else
            '    pnl_t = System.Math.Round(pnl_t)
            'End If

            Dim notc_r, notc_b, other_r, other_b As Double

            other_r = 0 : other_b = 0 : int_r = 0 : int_t = 0 : pnl_r = 0 : prin_b = 0 : prin_r = 0 : m = 0 : bnk_b = 0 : bnk_r = 0 : blamt = 0 : eduemi = 0 : notc_b = 0 : notc_r = 0 : int_tD = 0 : edubal = 0

            edubal = CDbl(intemi_bal)
            rmtd_amt = CDbl(Amount)
            int_t = (CDbl(TxtBal) * DateDiff(DateInterval.Day, ltransdate, (ENDDATE)) * intrate) / 36500
            int_t = Math.Round(int_t, 2)
            CurrentInt = int_t

            'int_tD = int_t - Int(int_t) // 29/03/2024
            'If int_tD < 0.5 Then
            '    int_t = int_t + 0.5
            'End If

            If rmtd_amt > bankchrge_b Then
                rmtd_amt = rmtd_amt - bankchrge_b
                bnk_r = bankchrge_b
                bnk_b = 0
                If rmtd_amt > notc_t Then
                    rmtd_amt = rmtd_amt - notc_t
                    notc_r = notc_t
                    notc_b = 0
                    If rmtd_amt > other_t Then
                        rmtd_amt = rmtd_amt - other_t
                        other_r = other_t
                        other_b = 0
                    Else
                        other_b = other_t - rmtd_amt
                        other_r = rmtd_amt
                        other_t = rmtd_amt
                        rmtd_amt = 0
                        blamt = 0
                        'int_t = System.Math.Round(int_t + int_prv, 0)
                        int_t = int_t + int_prv
                        GoTo rmt0
                    End If
                Else
                    notc_b = notc_t - rmtd_amt
                    notc_r = rmtd_amt
                    notc_t = rmtd_amt
                    rmtd_amt = 0
                    blamt = 0
                    'int_t = System.Math.Round(int_t + int_prv, 0)
                    int_t = int_t + int_prv
                    GoTo rmt0
                End If
            Else
                bnk_b = bankchrge_b - rmtd_amt
                bnk_r = rmtd_amt
                bankchrge_b = rmtd_amt
                rmtd_amt = 0
                blamt = 0
                'int_t = System.Math.Round(int_t + int_prv, 0)
                int_t = int_t + int_prv
                GoTo rmt0
            End If

            If rmtd_amt = 0 Then GoTo rmt1



            '''int_prv
            'If int_prv = 0 Then 23-04-2024
            ds2 = SqlHelper.ExecuteDataset(trans1, CommandType.Text, "Select int_int_dues as InterestDues,int_penal_dues as PenalDues from tbl_loanTrans where int_loanno = '" & LoanNo & "' and int_type<=20 and chr_remark <> 'FINANCIAL YEAR END1' and int_type not in (4,5,12) order by dt_transaction,int_transid")
            dr1 = ds2.Tables(0).Rows(ds2.Tables(0).Rows.Count - 1)
            int_prv = dr1("InterestDues")
            If int_prv_penal = 0 Then
                int_prv_penal = dr1("PenalDues")
            End If
            'End If

            pnl_r = pnl_t '+ int_prv_penal

            '''''''''''''''''Penal For Period Over Start''''''''''''''''''''''
            ds3 = SqlHelper.ExecuteDataset(trans1, CommandType.Text, "select * FROM tbl_loantrans WHERE INT_LOANNO ='" & LoanNo & "' and chr_remark in ('PERIOD OVER','RR Initiated')")
            If ds3.Tables(0).Rows.Count > 0 Then
                pnl_r = 0
                pnl_t = 0
                int_t = int_prv + System.Math.Round((CDbl(TxtBal) * DateDiff(DateInterval.Day, ltransdate, CDate(ENDDATE)) * defaultintrate) / 36500, 2)
                pnl_t = int_prv_penal
                int_prv_penal = pnl_t
                pnl_r = pnl_t
                CurrentPenal = 0 ''//01/04/2024
                int_prv = 0 '25/04/2024
                CurrentInt = System.Math.Round((CDbl(TxtBal) * DateDiff(DateInterval.Day, ltransdate, CDate(ENDDATE)) * defaultintrate) / 36500, 2) ''//01/04/2024
            End If
            '''''''''''''''''Penal For Period Over End''''''''''''''''''''''



            ''''''Light
            ' ''If otsflag = True Then
            ' ''    ds2 = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "select * FROM tbl_Light where int_loanno = '" & loanno & "' and int_type <> 9 and int_active=1")
            ' ''    If ds2.Tables(0).Rows.Count > 0 Then
            ' ''        dr2 = ds2.Tables(0).Rows(0)
            ' ''        per1 = dr2("int_per")
            ' ''        light = "L" & per1
            ' ''        per1 = 100 - per1

            ' ''    End If

            ' ''    'int_t = System.Math.Round(int_t + int_prv, 0)
            ' ''    'int_t = System.Math.Round(int_t / 2, 0)

            ' ''    ds3 = SqlHelper.ExecuteDataset(trans1, CommandType.Text, "Select int_prev,int_interest_BC,int_penal  from tbl_OTSwaiver where int_loanno = '" & loanno & "' order by int_otsid")
            ' ''    If ds3.Tables(0).Rows.Count > 0 Then
            ' ''        dr2 = ds3.Tables(0).Rows(ds3.Tables(0).Rows.Count - 1)
            ' ''        int_prvots = dr2("int_prev")
            ' ''        If int_prvots > 0 Then
            ' ''            int_tots = CDbl(TxtBal.Text) * DateDiff(DateInterval.Day, ltransdate, CDate(Date.Today)) * CDbl(TxtInt.Text) / 36500
            ' ''            'intdues = int_tots
            ' ''            int_t = System.Math.Round(Val(int_tots) * per1 / 100 + Val(int_prvots), 0)
            ' ''            intdues = dr2("int_interest_BC") + System.Math.Round(Val(int_tots) * per1 / 100, 0)
            ' ''        Else
            ' ''            int_t = System.Math.Round(int_t + int_prv, 0)
            ' ''            'intdues = int_t
            ' ''            int_t = System.Math.Round(int_t * per1 / 100, 0)
            ' ''            intdues = dr2("int_interest_BC") + System.Math.Round(int_t * per1 / 100, 0)
            ' ''        End If
            ' ''        If defaultloan = True Then
            ' ''            pnl_r = pnl_r + dr2("int_penal")
            ' ''        Else
            ' ''            pnl_r = pnl_r + dr2("int_penal")
            ' ''        End If
            ' ''    Else
            ' ''        int_t = System.Math.Round(int_t + int_prv, 0)
            ' ''        intdues = int_t
            ' ''        int_t = System.Math.Round(int_t * per1 / 100, 0)
            ' ''        intdues = intdues - int_t

            ' ''    End If
            ' ''Else
            int_t = (int_t + int_prv)   ''// 27/03/2024
            ' ''End If
            ' '' '''



            blamt = CDbl(Amount) - (bnk_r + notc_r + other_t)
            rmtd_amt = CDbl(Amount)
            ''''
            ''''
            ' ''If otsflag = True Then GoTo rmt2 ''// 27/03/2024
            '//27/03/2024
            If otsflag = True Then
                If WaiverAmount <= pnl_t Then
                    pnl_t = pnl_t - WaiverAmount
                    PenalWaiver = WaiverAmount
                    InterestWaiver = 0
                    WaiverAmount = 0
                ElseIf WaiverAmount > pnl_t Then
                    WaiverAmount = WaiverAmount - pnl_t
                    PenalWaiver = pnl_t
                    pnl_t = 0
                    If WaiverAmount <= int_t Then
                        int_t = int_t - WaiverAmount
                        InterestWaiver = WaiverAmount
                        WaiverAmount = 0
                    ElseIf WaiverAmount > int_t Then
                        WaiverAmount = WaiverAmount - int_t
                        InterestWaiver = int_t
                        int_t = 0
                    End If
                End If
            End If
            '//27/03/2024
            ''''
rmt0:       If pnl_t > 0 Then pnl_r = IIf(blamt >= pnl_r, pnl_r, blamt) : blamt = blamt - pnl_r
            ''//27/03/2024rmt2:           '''If edubal > 0 And NewPrefix = 0 Then eduemi = IIf(blamt >= CDbl(txtemi_int.Text), m * CDbl(txtemi_int.Text), blamt) : blamt = blamt - eduemi

            ' '' ''New Prefix Rule'''Start
            ' ''If edubal > 0 And NewPrefix = 1 Then eduemi = IIf(blamt >= edubal, edubal, blamt) : blamt = blamt - eduemi
            ' '' ''New Prefix Rule'''End

            ' ''If eduemi > edubal Then blamt = (blamt + eduemi) - edubal : eduemi = edubal


            If int_t > 0 Then int_r = IIf(blamt >= int_t, int_t, blamt) : blamt = blamt - int_r

            'prin_r = blamt
            ''Green
            ' ''If greencard > 0 Then
            ' ''    prin_r = System.Math.Round(IIf(blamt > CDbl(TxtBal.Text - greencard), CDbl(TxtBal.Text - greencard), blamt), 0)

            ' ''ElseIf greencard = 0 Then
            'prin_r = System.Math.Round(IIf(blamt > CDbl(TxtBal), CDbl(TxtBal), blamt), 0) //29/03/2024
            prin_r = System.Math.Round(IIf(blamt > CDbl(TxtBal), CDbl(TxtBal), blamt), 2)
            ' ''End If
            ''
            prin_b = System.Math.Round(IIf((CDbl(TxtBal) - prin_r) <= 0, 0, CDbl(TxtBal) - prin_r), 2)

            'Green
            ' ''If greencard > 0 Then
            ' ''    blamt = IIf((blamt - (CDbl(TxtBal.Text) - greencard)) > 0, CStr(System.Math.Round((blamt - (CDbl(TxtBal.Text) - greencard)), 0)), 0)

            ' ''ElseIf greencard = 0 Then
            blamt = IIf((blamt - CDbl(TxtBal)) > 0, CStr(System.Math.Round((blamt - CDbl(TxtBal)), 2)), 0) '// 29 / 3 / 2024
            'blamt = System.Math.Round(IIf((blamt - CDbl(TxtBal)) > 0, CStr(blamt - CDbl(TxtBal)), 0), 2)

            '' ''End If
            'blamt = IIf((blamt - CDbl(TxtBal.Text)) > 0, CStr(System.Math.Round((blamt - CDbl(TxtBal.Text)), 0)), 0)
            ''
            If (blamt) > 0 And (edubal - eduemi) > 0 Then

                If (edubal - eduemi) >= blamt Then
                    eduemi = eduemi + (blamt)
                    blamt = 0
                    '''''
                    If prin_b = 0 And eduemi < edubal Then
                        If eduemi + prin_r > edubal Then
                            eduemi = edubal
                            prin_r = (CDbl(Amount)) - (eduemi + pnl_r + int_r + bnk_r + notc_r + other_t)
                            ''prin_b = loanbal - prin_r
                            prin_b = CDbl(TxtBal) - prin_r
                        Else
                            eduemi = eduemi + prin_r
                            prin_b = prin_r
                            prin_r = 0
                        End If

                    End If
                    '''''
                    'txt10.Text = 0
                Else
                    blamt = blamt + eduemi
                    eduemi = edubal
                    blamt = (blamt - eduemi)
                End If
            End If
            If otsflag = False Then
                If blamt > 0 And (pnl_t - pnl_r) > 0 Then
                    If (blamt >= (pnl_t - pnl_r)) Then
                        blamt = blamt - (pnl_t - pnl_r)
                        pnl_r = pnl_t
                    Else
                        pnl_r = pnl_r + blamt
                        blamt = 0
                    End If

                End If
            End If
            If eduemi < 0 Then eduemi = 0


            '''Exit Sub
            '---------------------------------------------

            '------------------**********Saving Loan Transaction table - tbl_loantrans
            If prin_b < 0 Then prin_b = 0
            If prin_b <= 0 Then TransRemarks = "LOAN CLOSURE"
rmt1:
            ' If otsflag = True Then
            ' ''                    '''
            ' ''                    If prin_b <= 0 Then txtremark.Text = "LOAN CLOSURE(OTS)" Else txtremark.Text = "REPAYMENT(OTS)"
            ' ''                    If lst_type.SelectedIndex = 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "','" & txtrecptno.Text & "','" & lst_mode.SelectedItem.Text & "','" & txtchequeno.Text & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & int_t & ",0," & int_r & ",0," & prin_b & "," & int_t - int_r & ",0," & bnk_r + notc_r + other_t & ",'" & txtaccno.Text & "','" & txtremark.Text & "',1,'" & Mid(loanno, 1, 4) & "')")
            ' ''                    If lst_type.SelectedIndex = 1 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "','" & txtrecptno.Text & "','" & lst_mode.SelectedItem.Text & "','" & txtchequeno.Text & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & int_t & ",0," & int_r & ",0," & prin_b & "," & int_t - int_r & ",0," & bnk_r + notc_r + other_t & ",'" & txtaccno.Text & "','" & txtremark.Text & "',6,'" & Mid(loanno, 1, 4) & "')")
            ' ''                    If lst_type.SelectedIndex = 2 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "','" & txtrecptno.Text & "','" & lst_mode.SelectedItem.Text & "','" & txtchequeno.Text & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & int_t & ",0," & int_r & ",0," & prin_b & "," & int_t - int_r & ",0," & bnk_r + notc_r + other_t & ",'" & txtaccno.Text & "','" & txtremark.Text & "',15,'" & Mid(loanno, 1, 4) & "')")
            ' ''                    '''
            ' ''                Else
            '''''''Default OTS
            ' ''If defaultotsflag = True Then
            ' ''    If prin_b <= 0 Then txtremark.Text = "LOAN CLOSURE(DefOTS)" Else txtremark.Text = "REPAYMENT(DefOTS)"
            ' ''    If lst_type.SelectedIndex = 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "','" & txtrecptno.Text & "','" & lst_mode.SelectedItem.Text & "','" & txtchequeno.Text & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & int_t & "," & pnl_t & "," & int_r & "," & pnl_r & "," & prin_b & "," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'" & txtaccno.Text & "','" & txtremark.Text & "',1,'" & Mid(loanno, 1, 4) & "')")
            ' ''    If lst_type.SelectedIndex = 1 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "','" & txtrecptno.Text & "','" & lst_mode.SelectedItem.Text & "','" & txtchequeno.Text & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & int_t & "," & pnl_t & "," & int_r & "," & pnl_r & "," & prin_b & "," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'" & txtaccno.Text & "','" & txtremark.Text & "',6,'" & Mid(loanno, 1, 4) & "')")
            ' ''    If lst_type.SelectedIndex = 2 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "','" & txtrecptno.Text & "','" & lst_mode.SelectedItem.Text & "','" & txtchequeno.Text & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & int_t & "," & pnl_t & "," & int_r & "," & pnl_r & "," & prin_b & "," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'" & txtaccno.Text & "','" & txtremark.Text & "',15,'" & Mid(loanno, 1, 4) & "')")
            ' ''End If
            '''''''Default OTS

            'Green
            ' ''If greencard > 0 Then
            ' ''    If lst_type.SelectedIndex = 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "','" & txtrecptno.Text & "','" & lst_mode.SelectedItem.Text & "','" & txtchequeno.Text & "','Receipt'," & CDbl(Amount) & "," & prin_r + greencard & "," & eduemi & "," & int_t & "," & pnl_t & "," & int_r & "," & pnl_r & ",0," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'" & txtaccno.Text & "','LOAN CLOSURE',1,'" & Mid(loanno, 1, 4) & "')")
            ' ''    If lst_type.SelectedIndex = 1 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "','" & txtrecptno.Text & "','" & lst_mode.SelectedItem.Text & "','" & txtchequeno.Text & "','Receipt'," & CDbl(Amount) & "," & prin_r + greencard & "," & eduemi & "," & int_t & "," & pnl_t & "," & int_r & "," & pnl_r & ",0," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'" & txtaccno.Text & "','LOAN CLOSURE',6,'" & Mid(loanno, 1, 4) & "')")
            ' ''    If lst_type.SelectedIndex = 2 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "','" & txtrecptno.Text & "','" & lst_mode.SelectedItem.Text & "','" & txtchequeno.Text & "','Receipt'," & CDbl(Amount) & "," & prin_r + greencard & "," & eduemi & "," & int_t & "," & pnl_t & "," & int_r & "," & pnl_r & ",0," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'" & txtaccno.Text & "','LOAN CLOSURE',15,'" & Mid(loanno, 1, 4) & "')")
            ' ''    ''
            ' ''ElseIf greencard = 0 Then
            If OfficeId <> Mid(LoanNo, 1, 4) Then
                TransRemarks = "Repayment @ " & GetOffice(Mid(LoanNo, 1, 4))
            Else
                ' ''If Ashwas = 1 Or Ashwas_M = 1 Then
                ' ''    ds1 = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select * from  tbl_Ashwas where int_loanno='" & txtLoanno.Text.Trim & "'")
                ' ''    If ds1.Tables(0).Rows.Count > 0 Then
                ' ''        If ds1.Tables(0).Rows(0)("int_status") = 1 Then
                ' ''            remarks = "Remittance-AshwasaKiranam-" & ds1.Tables(0).Rows(0)("int_loanno_new")
                ' ''        Else
                ' ''            remarks = txtremark.Text
                ' ''        End If
                ' ''    Else
                ' ''        If Ashwas = 1 Then
                ' ''            remarks = "Remittance-AshwasaKiranam"
                ' ''            SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO  tbl_Ashwas(int_loanno,mny_dues,mny_remitamt,mny_loanamt, dt_tdate,int_type)VALUES('" & loanno & "'," & AswasBalRound + AswasRemit & "," & AswasRemit & "," & AswasBalRound & ",'" & CDate(ENDDATE) & "'," & AswasType & ")")
                ' ''        ElseIf Ashwas_M = 1 Then

                ' ''            ''''
                ' ''            remarks = "Remittance-AshwasaKiranam(PartPayment)"

                ' ''            ds1 = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select * from  tbl_Ashwas_M where int_loanno='" & txtLoanno.Text.Trim & "'")
                ' ''            If ds1.Tables(0).Rows.Count > 0 Then
                ' ''                If ds1.Tables(0).Rows(0)("mny_remitamt") - ds1.Tables(0).Rows(0)("mny_remitamt_bal") <= 0 Then
                ' ''                    SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Ashwas select  int_loanno,mny_dues,mny_remitamt,mny_loanamt, dt_tdate,int_type FROM tbl_Ashwas_M where  int_loanno ='" & loanno & "' ")
                ' ''                Else
                ' ''                    If ds1.Tables(0).Rows(0)("mny_remitamt_bal") + Val(Amount) >= ds1.Tables(0).Rows(0)("mny_remitamt") Then
                ' ''                        SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Ashwas select  int_loanno,mny_dues,mny_remitamt,mny_loanamt, dt_tdate,0,'',int_type FROM tbl_Ashwas_M where  int_loanno ='" & loanno & "' ")
                ' ''                    End If
                ' ''                    SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "Update tbl_Ashwas_M set mny_remitamt_bal = mny_remitamt_bal + " & CDbl(Amount) & " where  int_loanno ='" & loanno & "' ")

                ' ''                End If

                ' ''            Else
                ' ''                SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO  tbl_Ashwas_M(int_loanno,mny_dues,mny_remitamt,mny_loanamt, dt_tdate,int_type,mny_remitamt_bal)VALUES('" & loanno & "'," & AswasBalRound + AswasRemit & "," & AswasRemit & "," & AswasBalRound & ",'" & CDate(ENDDATE) & "'," & AswasType & "," & CDbl(Amount) & ")")
                ' ''            End If
                ' ''            ''''

                ' ''        End If
                ' ''    End If
                ' ''Else
                If Remark = "SUBSIDY" Then
                    TransRemarks = Remark
                Else
                    TransRemarks = "REPAYMENT"
                End If

                If prin_b <= 0 And Remark <> "SUBSIDY" Then TransRemarks = "LOAN CLOSURE"

                ' ''End If
            End If

            ' ''If defaultotsflag = False Then
            ''' 01/04/2024

            If lst_type = 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate(ENDDATE) & "','" & RecptNo & "','" & lst_mode & "','" & Chequeno & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & CurrentInt & "," & CurrentPenal & "," & int_r & "," & pnl_r & "," & prin_b & "," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'','" & TransRemarks & "',1,'" & Mid(LoanNo, 1, 4) & "')")
            If lst_type = 1 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate(ENDDATE) & "','" & RecptNo & "','" & lst_mode & "','" & Chequeno & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & CurrentInt & "," & CurrentPenal & "," & int_r & "," & pnl_r & "," & prin_b & "," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'','" & TransRemarks & "',6,'" & Mid(LoanNo, 1, 4) & "')")
            If lst_type = 2 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & CDate(ENDDATE) & "','" & RecptNo & "','" & lst_mode & "','" & Chequeno & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & CurrentInt & "," & CurrentPenal & "," & int_r & "," & pnl_r & "," & prin_b & "," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'','" & TransRemarks & "',15,'" & Mid(LoanNo, 1, 4) & "')")

            'If lst_type = 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & ENDDATE & "','" & RecptNo & "','" & lst_mode & "','" & Chequeno & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & int_t & "," & pnl_t & "," & int_r & "," & pnl_r & "," & prin_b & "," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'','" & Remark & "',1,'" & Mid(LoanNo, 1, 4) & "')")
            'If lst_type = 1 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & ENDDATE & "','" & RecptNo & "','" & lst_mode & "','" & Chequeno & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & int_t & "," & pnl_t & "," & int_r & "," & pnl_r & "," & prin_b & "," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'','" & Remark & "',6,'" & Mid(LoanNo, 1, 4) & "')")
            'If lst_type = 2 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, chr_rec_no, chr_mod_payment, chr_dd_no, chr_transtype, int_amt, int_prin_amt, int_eduint_amt,int_int_amt, int_penal_amt, int_int_r, int_penal_r, int_prin_dues, int_int_dues, int_penal_dues,  int_bank_charge_r, chr_bankaccno,chr_remark,int_type,vchr_offidC)VALUES('" & LoanNo & "','" & ENDDATE & "','" & RecptNo & "','" & lst_mode & "','" & Chequeno & "','Receipt'," & CDbl(Amount) & "," & prin_r & "," & eduemi & "," & int_t & "," & pnl_t & "," & int_r & "," & pnl_r & "," & prin_b & "," & int_t - int_r & "," & pnl_t - pnl_r & "," & bnk_r + notc_r + other_t & ",'','" & Remark & "',15,'" & Mid(LoanNo, 1, 4) & "')")

            ' ''End If
            ' ''End If

            ' ''End If
            If blamt > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_balance(int_loanno,chr_rec_no,int_bal)VALUES('" & LoanNo & "','" & RecptNo & "'," & blamt & ")")
            'SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_subdistrict SET int_receiptno =int_receiptno + 1 WHERE int_distid like " & Mid(loanno, 1, 2) & "")
            ' SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE  tbl_Sub_Districts SET Last_Receipt_No =Last_Receipt_No + 1 WHERE Office_Code like '" & OfficeId & "'") ''27-04-2024

            '------------------***********For Bank charges/ Notice charges/ other charges

            If bnk_r > 0 And bnk_b = 0 Then
                SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loantrans SET int_debit_b=0 where INT_LOANNO='" & LoanNo & "' AND chr_transtype='Payment' and  int_type in (5,7) and int_debit_b>0")
            ElseIf bnk_r > 0 And bnk_b > 0 Then
                ds = SqlHelper.ExecuteDataset(trans1, CommandType.Text, "select * from tbl_loantrans where INT_LOANNO='" & LoanNo & "' AND chr_transtype='Payment' and int_type in (5,7) and int_debit_b>0  order by dt_transaction")

                If ds.Tables(0).Rows.Count > 0 Then
                    dr = ds.Tables(0).Rows(0)
                    g1 = bnk_r
                    For Each dr In ds.Tables(0).Rows
                        If g1 <= 0 Then Exit For
                        If (g1 > dr("int_debit_b")) Then
                            SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loantrans SET int_debit_b=0 where int_transid=" & dr("int_transid"))
                            g1 = g1 - dr("int_debit_b")
                        Else
                            SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loantrans SET int_debit_b=int_debit_b-" & g1 & " where int_transid=" & dr("int_transid"))
                            g1 = 0
                        End If
                    Next
                End If
            End If

            If notc_r > 0 And notc_b = 0 Then
                SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loantrans SET int_debit_b=0 where INT_LOANNO='" & LoanNo & "' AND chr_transtype='Payment' and int_type in (4,8) and int_debit_b>0")
            ElseIf notc_r > 0 And notc_b > 0 Then
                ds = SqlHelper.ExecuteDataset(trans1, CommandType.Text, "select * from tbl_loantrans where INT_LOANNO='" & LoanNo & "' AND chr_transtype='Payment' and int_type in (4,8) and int_debit_b>0  order by dt_transaction")
                If ds.Tables(0).Rows.Count > 0 Then
                    dr = ds.Tables(0).Rows(0)
                    g1 = notc_r
                    For Each dr In ds.Tables(0).Rows
                        If g1 <= 0 Then Exit For
                        If (g1 > dr("int_debit_b")) Then
                            SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loantrans SET int_debit_b=0 where int_transid=" & dr("int_transid"))
                            g1 = g1 - dr("int_debit_b")
                        Else
                            SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loantrans SET int_debit_b=int_debit_b-" & g1 & " where int_transid=" & dr("int_transid"))
                            g1 = 0
                        End If
                    Next
                End If
            End If
            If other_r > 0 And other_b = 0 Then
                SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loantrans SET int_debit_b=0 where INT_LOANNO='" & LoanNo & "' AND chr_transtype='Payment' and int_type =12 and int_debit_b>0")
            ElseIf other_r > 0 And other_b > 0 Then
                ds = SqlHelper.ExecuteDataset(trans1, CommandType.Text, "select * from tbl_loantrans where INT_LOANNO='" & LoanNo & "' AND chr_transtype='Payment' and int_type=12 and int_debit_b>0  order by dt_transaction")
                If ds.Tables(0).Rows.Count > 0 Then
                    dr = ds.Tables(0).Rows(0)
                    g1 = other_r
                    For Each dr In ds.Tables(0).Rows
                        If g1 <= 0 Then Exit For
                        If (g1 > dr("int_debit_b")) Then
                            SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loantrans SET int_debit_b=0 where int_transid=" & dr("int_transid"))
                            g1 = g1 - dr("int_debit_b")
                        Else
                            SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loantrans SET int_debit_b=int_debit_b-" & g1 & " where int_transid=" & dr("int_transid"))
                            g1 = 0
                        End If
                    Next
                End If
            End If
            '------------------*************Saving Accounts table - tbl_Account
            ds = SqlHelper.ExecuteDataset(trans1, CommandType.Text, "SELECT * FROM TBL_ACCOUNT WHERE CHR_UNDER='" & FCODE & "' AND chr_head='Minor' and chr_type='Receipt'")
            If ds.Tables(0).Rows.Count > 0 Then
                dr = ds.Tables(0).Rows(0)
                For Each dr In ds.Tables(0).Rows
                    '' ''If dr("chr_Name") = "PRINCIPAL" And prin_r > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time)VALUES('" & txtrecptno.Text & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode.SelectedItem.Text & "'," & prin_r & ",'" & CDate(ENDDATE) & "','" & loanno & "','" & TxtBName.Text & "','REPAYMENT','" & Mid(loanno, 1, 4) & "','" & Session("username") & "','" & DateTime.Now & "')")
                    '' ''If dr("chr_Name") = "INTEREST" And int_r + eduemi > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time)VALUES('" & txtrecptno.Text & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode.SelectedItem.Text & "'," & int_r + eduemi & ",'" & CDate(ENDDATE) & "','" & loanno & "','" & TxtBName.Text & "','REPAYMENT','" & Mid(loanno, 1, 4) & "','" & Session("username") & "','" & DateTime.Now & "')")
                    '' ''If dr("chr_Name") = "PENAL" And pnl_r > 0 And otsflag = False Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time)VALUES('" & txtrecptno.Text & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode.SelectedItem.Text & "'," & pnl_r & ",'" & CDate(ENDDATE) & "','" & loanno & "','" & TxtBName.Text & "','REPAYMENT','" & Mid(loanno, 1, 4) & "','" & Session("username") & "','" & DateTime.Now & "')")
                    '' ''If dr("chr_Name") = "NOTICECHARGES" And notc_r > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time)VALUES('" & txtrecptno.Text & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode.SelectedItem.Text & "'," & notc_r & ",'" & CDate(ENDDATE) & "','" & loanno & "','" & TxtBName.Text & "','REPAYMENT','" & Mid(loanno, 1, 4) & "','" & Session("username") & "','" & DateTime.Now & "')")
                    '' ''If dr("chr_Name") = "BANKCHARGES" And bnk_r > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time)VALUES('" & txtrecptno.Text & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode.SelectedItem.Text & "'," & bnk_r & ",'" & CDate(ENDDATE) & "','" & loanno & "','" & TxtBName.Text & "','REPAYMENT','" & Mid(loanno, 1, 4) & "','" & Session("username") & "','" & DateTime.Now & "')")
                    '' ''If dr("chr_Name") = "OTHERCHARGES" And other_r > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time)VALUES('" & txtrecptno.Text & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode.SelectedItem.Text & "'," & other_r & ",'" & CDate(ENDDATE) & "','" & loanno & "','" & TxtBName.Text & "','REPAYMENT','" & Mid(loanno, 1, 4) & "','" & Session("username") & "','" & DateTime.Now & "')")
                    '' ''If dr("chr_Name") = "CREDITBALANCE" And blamt > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time)VALUES('" & txtrecptno.Text & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode.SelectedItem.Text & "'," & blamt & ",'" & CDate(ENDDATE) & "','" & loanno & "','" & TxtBName.Text & "','REPAYMENT','" & Mid(loanno, 1, 4) & "','" & Session("username") & "','" & DateTime.Now & "')")
                    ' ''If Val(txtmtr.Text) > 0 Then
                    ' ''    If dr("chr_Name") = "PRINCIPAL" Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time,vchr_offidC)VALUES('" & txtrecptno.Text & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode.SelectedItem.Text & "'," & CDbl(Amount) & ",'" & CDate(ENDDATE) & "','" & loanno & "','" & LoneeeName & "','REPAYMENT','" & Session("officeid") & "','" & Session("username") & "','" & DateTime.Now & "','" & Mid(loanno, 1, 4) & "')")

                    ' ''Else
                    If dr("chr_Name") = "PRINCIPAL" And prin_r > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time,vchr_offidC)VALUES('" & RecptNo & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode & "'," & prin_r & ",'" & CDate(ENDDATE) & "','" & LoanNo & "','" & LoneeeName & "','REPAYMENT','" & Mid(LoanNo, 1, 4) & "','" & UserName & "',GETDATE(),'" & Mid(LoanNo, 1, 4) & "')")
                    If dr("chr_Name") = "INTEREST" And int_r + eduemi > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time,vchr_offidC)VALUES('" & RecptNo & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode & "'," & int_r + eduemi & ",'" & CDate(ENDDATE) & "','" & LoanNo & "','" & LoneeeName & "','REPAYMENT','" & Mid(LoanNo, 1, 4) & "','" & UserName & "',GETDATE(),'" & Mid(LoanNo, 1, 4) & "')")
                    If dr("chr_Name") = "PENAL" And pnl_r > 0 And otsflag = False Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time,vchr_offidC)VALUES('" & RecptNo & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode & "'," & pnl_r & ",'" & CDate(ENDDATE) & "','" & LoanNo & "','" & LoneeeName & "','REPAYMENT','" & Mid(LoanNo, 1, 4) & "','" & UserName & "',GETDATE(),'" & Mid(LoanNo, 1, 4) & "')")
                    If dr("chr_Name") = "NOTICECHARGES" And notc_r > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time,vchr_offidC)VALUES('" & RecptNo & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode & "'," & notc_r & ",'" & CDate(ENDDATE) & "','" & LoanNo & "','" & LoneeeName & "','REPAYMENT','" & Mid(LoanNo, 1, 4) & "','" & UserName & "',GETDATE(),'" & Mid(LoanNo, 1, 4) & "')")
                    If dr("chr_Name") = "BANKCHARGES" And bnk_r > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time,vchr_offidC)VALUES('" & RecptNo & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode & "'," & bnk_r & ",'" & CDate(ENDDATE) & "','" & LoanNo & "','" & LoneeeName & "','REPAYMENT','" & Mid(LoanNo, 1, 4) & "','" & UserName & "',GETDATE(),'" & Mid(LoanNo, 1, 4) & "')")
                    If dr("chr_Name") = "OTHERCHARGES" And other_r > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time,vchr_offidC)VALUES('" & RecptNo & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode & "'," & other_r & ",'" & CDate(ENDDATE) & "','" & LoanNo & "','" & LoneeeName & "','REPAYMENT','" & Mid(LoanNo, 1, 4) & "','" & UserName & "',GETDATE(),'" & Mid(LoanNo, 1, 4) & "')")
                    If dr("chr_Name") = "CREDITBALANCE" And blamt > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_Acctrans(vchr_TransNo,chr_Trans_Type,int_Code,chr_Acc_Name,chr_Type,int_rec, dt_TDate, int_loanno, chr_Name, vchr_remarks,vchr_offid,vchr_uname,dte_time,vchr_offidC)VALUES('" & RecptNo & "','Receipt'," & dr("int_code") & ",'" & dr("chr_Name") & "','" & lst_mode & "'," & blamt & ",'" & CDate(ENDDATE) & "','" & LoanNo & "','" & LoneeeName & "','REPAYMENT','" & Mid(LoanNo, 1, 4) & "','" & UserName & "',GETDATE(),'" & Mid(LoanNo, 1, 4) & "')")
                    '" & Mid(LoanNo, 1, 4) & "','" & UserName & "','" & DateTime.Now & "','" & Mid(loanno, 1, 4) & "')")
                    ' ''End If
                Next
            End If

            nxtduedate = DateSerial(Year(ENDDATE), Month(ENDDATE), Day(fduedt))
            If ENDDATE >= nxtduedate Then nxtduedate = DateAdd(DateInterval.Month, 1, nxtduedate)

            '------------------*****************update Loan Register- tbl_loanreg
            ''Green
            ' ''If greencard > 0 Then
            ' ''    SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loanreg set mny_loanbal=0,dt_last_repay_date='" & CDate(ENDDATE) & "',dt_next_due_date='" & CDate(nxtduedate) & "',mny_intrbal=mny_intrbal-" & CDbl(eduemi) & " where int_loanno='" & loanno & "'")
            ' ''    ''
            ' ''    SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_LoanClosure set int_Closure=1,dt_datetimeClosure = '" & DateTime.Now & "' where int_loanno='" & loanno & "'")
            ' ''    ''
            '' ''ElseIf greencard = 0 Then
            SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loanreg set mny_loanbal=" & prin_b & ",dt_last_repay_date='" & CDate(ENDDATE) & "',dt_next_due_date='" & CDate(nxtduedate) & "',mny_intrbal=mny_intrbal-" & CDbl(eduemi) & " where int_loanno='" & LoanNo & "'")

            ' ''End If
            '
            ' SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loanreg set mny_loanbal=" & prin_b & ",dt_last_repay_date='" & CDate(ENDDATE) & "',dt_next_due_date='" & CDate(nxtduedate) & "',mny_intrbal=mny_intrbal-" & CDbl(eduemi) & " where int_loanno='" & loanno & "'")
            SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loanreg set vchr_stage='CLOSED' where int_loanno='" & LoanNo & "' and mny_loanbal=0 and mny_intrbal=0")
            ''
            If prin_b <= 0 Then
                ds1 = SqlHelper.ExecuteDataset(trans1, CommandType.Text, "Select * from tbl_LoanClosure where int_loanno='" & LoanNo & "'")
                If ds1.Tables(0).Rows.Count > 0 Then
                    SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_LoanClosure set int_Closure=1,dt_datetimeClosure = '" & DateTime.Now.ToString & "' where int_loanno='" & LoanNo & "'")
                Else
                    SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "Insert into tbl_LoanClosure(int_loanno,int_Closure,dt_datetimeClosure,vchr_offid) values('" & LoanNo & "',1,'" & DateTime.Now.ToString() & "','" & Mid(LoanNo, 1, 4) & "')")

                End If

            End If
            ''
            '---------------------------------------------
            '''''''OTS
            '' ''Dim rmtamt, rmtint As Double
            '' ''If otsflag = True Then
            '' ''    ds = SqlHelper.ExecuteDataset(trans1, CommandType.Text, "select sum(int_amt),sum(int_int_r) from tbl_loantrans where INT_LOANNO='" & loanno & "' and chr_remark in('REPAYMENT(OTS)','LOAN CLOSURE(OTS)')")
            '' ''    If ds.Tables.Count > 0 Then
            '' ''        If ds.Tables(0).Rows.Count > 0 Then
            '' ''            For Each dr In ds.Tables(0).Rows
            '' ''                rmtamt = IIf(IsDBNull(dr(0)), 0, dr(0))
            '' ''                '''Light
            '' ''                rmtint = IIf(IsDBNull(dr(1)), 0, dr(1))
            '' ''                '''
            '' ''            Next
            '' ''        End If
            '' ''    End If
            '' ''End If
            '''''''OTS OLD'''
            ' '' '' If pnl_t > 0 And otsflag = True Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_OTSwaiver(int_loanno,chr_rec_no,int_Penal,dte_tdate,int_Remitamt)VALUES('" & loanno & "','" & txtrecptno.Text & "'," & pnl_t & ",'" & CDate(ENDDATE) & "'," & rmtamt & ")")
            '' '' '''
            ''''''''DefaultOTS
            '' ''If defaultotsflag = True Then
            '' ''    If pnl_t > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_OTSwaiver(int_loanno,chr_rec_no,int_Penal,dte_tdate,int_Remitamt,vchr_remark)VALUES('" & loanno & "','" & txtrecptno.Text & "'," & pnl_t_ots & ",'" & CDate(ENDDATE) & "'," & CDbl(Amount) & ",'DefaultOTS')")
            '' ''    SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_loanreg set  int_DefaultOts =2 where int_loanno='" & loanno & "' and  int_DefaultOts =1")
            '' ''End If
            ''''''''DefaultOTS

            ''// 27/03/2024
            If otsflag = True Then
                SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_OTSwaiver(int_loanno,chr_rec_no,int_Penal,dte_tdate,int_Remitamt,int_Interest,vchr_remark)VALUES('" & LoanNo & "','" & RecptNo & "'," & PenalWaiver & ",'" & CDate(ENDDATE) & "'," & Amount & "," & InterestWaiver & ",'OTS')")
            End If
            ''// 27/03/2024


            ''''''''Light
            '' ''If otsflag = True Then
            '' ''    If int_t - int_r > 0 Then
            '' ''        rmtint = int_t
            '' ''    End If
            '' ''    If light.ToString.StartsWith("L0") = True Then
            '' ''        intdues = 0
            '' ''    End If
            '' ''    If pnl_t > 0 Or int_r > 0 Or rmtint > 0 Then SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_OTSwaiver(int_loanno,chr_rec_no,int_Penal,int_interest,dte_tdate,int_Remitamt,vchr_remark,int_prev,int_interest_BC)VALUES('" & loanno & "','" & txtrecptno.Text & "'," & pnl_r & "," & rmtint & ",'" & CDate(ENDDATE) & "'," & rmtamt & ",'" & light & "'," & int_t - int_r & "," & intdues & ")")

            '' ''End If
            '' '' '''
            '' '' '''

            '''GreenCard
            ' ''If greencard > 0 Then
            ' ''    ds = SqlHelper.ExecuteDataset(trans1, CommandType.Text, "select int_creceiptno FROM tbl_subdistrict WHERE vchr_offid ='" & Mid(loanno, 1, 4) & "'")
            ' ''    If ds.Tables(0).Rows.Count > 0 Then
            ' ''        dr = ds.Tables(0).Rows(0)
            ' ''        rcpt = "C" & dr("int_creceiptno")
            ' ''    End If
            ' ''    If Val(txtmtr.Text) > 0 Then
            ' ''        SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, int_amt,chr_rec_no, chr_mod_payment, chr_transtype,int_debit,int_debit_b,int_int_amt,int_int_dues,int_prin_dues,int_bank_charge_r,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "',0,'','ADJUSTMENT','Receipt',0,0,0,0,0," & greencard & ",'Moratorium',17,'" & Mid(loanno, 1, 4) & "')")
            ' ''    Else
            ' ''        SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "INSERT INTO tbl_loantrans(int_loanno, dt_transaction, int_amt,chr_rec_no, chr_mod_payment, chr_transtype,int_debit,int_debit_b,int_int_amt,int_int_dues,int_prin_dues,int_bank_charge_r,chr_remark,int_type,vchr_offidC)VALUES('" & loanno & "','" & CDate(ENDDATE) & "',0,'" & rcpt & "','ADJUSTMENT','Receipt',0,0,0,0,0," & greencard & ",'GREEN CARD',17,'" & Mid(loanno, 1, 4) & "')")
            ' ''        SqlHelper.ExecuteNonQuery(trans1, CommandType.Text, "UPDATE tbl_subdistrict SET int_creceiptno =int_creceiptno + 1 WHERE vchr_offid ='" & Mid(loanno, 1, 4) & "'")
            ' ''    End If
            ' ''End If
            ' '' '''
            ' ''FCODE = txtrecptno.Text
            trans1.Commit()



        Catch ex As Exception
            trans1.Rollback()
            trans1.Dispose()
            '  Response.Redirect("ErrorPage.aspx")
            Throw ex
        Finally
            conn.Close()
            conn.Dispose()
            trans1.Dispose()
        End Try

    End Function
    Public Function GetOffice(ByVal Offid As String) As String

        If Offid = "0101" Then
            GetOffice = "Trivandrum"
        ElseIf Offid = "0201" Then
            GetOffice = "Kollam"
        ElseIf Offid = "0301" Then
            GetOffice = "Pathanamthitta"
        ElseIf Offid = "0401" Then
            GetOffice = "Alappuzha"
        ElseIf Offid = "0501" Then
            GetOffice = "Kottayam"
        ElseIf Offid = "0601" Then
            GetOffice = "Idukki"
        ElseIf Offid = "0701" Then
            GetOffice = "Ernakulam"
        ElseIf Offid = "0801" Then
            GetOffice = "Thrissur"
        ElseIf Offid = "0901" Then
            GetOffice = "Palakkad"
        ElseIf Offid = "1001" Then
            GetOffice = "Malappuram"
        ElseIf Offid = "1101" Then
            GetOffice = "Kozhikode"
        ElseIf Offid = "1201" Then
            GetOffice = "Wayanad"
        ElseIf Offid = "1301" Then
            GetOffice = "Kannur"
        ElseIf Offid = "1401" Then
            GetOffice = "kasargod"
        ElseIf Offid = "1002" Then
            GetOffice = "Vandoor"
        ElseIf Offid = "1003" Then
            GetOffice = "Tirur"
        ElseIf Offid = "0802" Then
            GetOffice = "Chelakkara"
        ElseIf Offid = "0902" Then
            GetOffice = "Pattambi"
        ElseIf Offid = "0102" Then
            GetOffice = "Varkala"
        ElseIf Offid = "0402" Then
            GetOffice = "Haripad"
        ElseIf Offid = "1102" Then
            GetOffice = "Perambra"
        ElseIf Offid = "0903" Then
            GetOffice = "Vadakkencherry"
        ElseIf Offid = "0103" Then
            GetOffice = "Neyyattinkara"
        ElseIf Offid = "0702" Then
            GetOffice = "Muvattupuzha"
        ElseIf Offid = "0403" Then
            GetOffice = "Cherthala"
        ElseIf Offid = "0202" Then
            GetOffice = "Karunagapally"
        ElseIf Offid = "0602" Then
            GetOffice = "Nedumkandam"
        ElseIf Offid = "1103" Then
            GetOffice = "Nadapuram"
        ElseIf Offid = "1402" Then
            GetOffice = "Kanhangad"
        ElseIf Offid = "0203" Then
            GetOffice = "Pathanapuram"
        ElseIf Offid = "0302" Then
            GetOffice = "Adoor"
        ElseIf Offid = "0502" Then
            GetOffice = "Kanjirappally"
        ElseIf Offid = "1202" Then
            GetOffice = "Mananthavady"
        ElseIf Offid = "1302" Then
            GetOffice = "Thalassery"
        ElseIf Offid = "1501" Then
            GetOffice = "HeadOffice"
        End If

    End Function
    Function calc_ondate(ByVal loanno As String)
        Dim dr, dr1, dr2 As DataRow
        Dim ds, ds2, ds3, ds4, ds5, ds6 As DataSet
        Dim per1 As Integer
        per1 = 0
        Dim dsbcomplt As Integer = 0
        Dim fdate, duedt, dte, lduedate, ltransdate As Date
        Dim Rcvamt, Dmdamt, instamt, Dbtamt, pnl, amt, TxtBal, intrate, penalrate, TXTIB, TXTPNLB, txtdefaulttotal As Double
        Dim lbl_balance As String

        Dim loanbal, Tpnl, int_prv, bankchrge_b, notc_t, other_t, greencard, int_prvots, int_prv_penal, int_prefixbal As Double
        ' Dim int_t, pnl_t, int_tD, int_prvots, int_tots As Double

        ' Dim strconn As String
        ' strconn2 = System.Configuration.ConfigurationManager.AppSettings("constr")
        '   strconn = "data source=VIVEK;initial catalog=ksdc_live_latest;user id=sa;password=*******;Connect Timeout=300000;"

        '------------------------
        ds = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select * from tbl_loanreg where int_loanno='" & loanno & "'")
        If ds.Tables.Count > 0 Then
            If ds.Tables(0).Rows.Count > 0 Then
                dr = ds.Tables(0).Rows(0)
                fdate = dr("dt_first_due_date")
                instamt = dr("mny_repayamt")
                lduedate = DateAdd(DateInterval.Month, dr("int_repay_inst") - 1, fdate)
                lduedate = Format(lduedate, "dd/MM/yyyy")
                TxtBal = Format(dr("mny_loanbal"), "0")
                ltransdate = dr("dt_last_repay_date")
                intrate = Format(dr("int_rate_int"), "0.00")
                penalrate = Format(dr("int_rate_penal"), "0.00")
                'PeriodExt = dr("int_repay_inst_Ext")
                'If PeriodExt > 0 And lbl_rr.Text.Trim <> "Moratorium Loan" Then
                '    lbl_rr.Text = lbl_rr.Text & "  Moratorium Loan"
                'End If
                If dr("int_inst_disbursed") = dr("int_disb_inst") And dr("mny_repayamt") <> 0 Then
                    dsbcomplt = 1
                End If
            End If
        End If
        '------------------------

        ds2 = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select int_int_dues as InterestDues,int_penal_dues as PenalDues from tbl_loanTrans where int_loanno = '" & loanno & "' and int_type<=20 and chr_remark <> 'FINANCIAL YEAR END1' and int_type not in (4,5,12) order by dt_transaction,int_transid")
        dr1 = ds2.Tables(0).Rows(ds2.Tables(0).Rows.Count - 1)
        int_prv = dr1("InterestDues")
        int_prv_penal = dr1("PenalDues")


        If TxtBal = 0 Then Exit Function
        Dim int_t, pnl_t, int_tD, int_tots As Double : int_t = 0 : pnl_t = 0 : int_prvots = 0 : int_tots = 0

        TXTIB = System.Math.Round(int_prv)
        If ltransdate > CDate(Date.Today) Then Exit Function

        int_t = TxtBal * DateDiff(DateInterval.Day, ltransdate, CDate(Date.Today)) * intrate / 36500
        '''
        int_tD = int_t - Int(int_t)
        If int_tD < 0.5 Then
            int_t = int_t + 0.5
        End If
        '''
        int_t = System.Math.Round(int_prv + int_t, 0)
        TXTIB = int_t

        If dsbcomplt = 0 Then Exit Function

        '----------------------------------Penal Interest
        pnl_t = CalcPenal(Date.Today, loanno)

        If pnl_t > 0 And pnl_t <= 1 Then
            pnl_t = 1
        Else
            pnl_t = System.Math.Round(pnl_t)
        End If


        '''''''''''''''''Penal For Period Over Start''''''''''''''''''''''



        '''''''''''''''''Penal For Period Over End''''''''''''''''''''''

        TXTPNLB = pnl_t
        txtdefaulttotal = pnl_t + CalcDefaultAmt(loanno)

        If Val(TXTIB) + Val(TXTPNLB) + Val(TxtBal) < Val(txtdefaulttotal) Then
            txtdefaulttotal = Val(TXTIB) + Val(TXTPNLB) + Val(TxtBal)
        End If

        '''Default for period Over

        Dim ds1 As DataSet
        '  Dim strconn1 As String
        ' strconn1 = System.Configuration.ConfigurationManager.AppSettings("constr")
        ds1 = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "SELECT  *  FROM dbo.tbl_Default WHERE  Int_Loanno = '" & loanno & "'") '//Check

        If ds1.Tables(0).Rows.Count > 0 Then
            txtdefaulttotal = Val(TXTIB) + Val(TXTPNLB) + Val(TxtBal)
        End If

        'lbl_balance = "Balance Amount For Loan Closure as per Online Register - " & Val(txtdefaulttotal) & " /- "
        lbl_balance = Val(txtdefaulttotal)

        '''Waiver 
        ' ''If greenflag = True Then
        ' ''    Dim per
        ' ''    per = System.Math.Floor((Val(TXTIR.Text) + Val(TXTIB.Text)) * rate / 100)
        ' ''    If Val(txttotal1.Text) - per > 0 Then
        ' ''        lbl_green.Text = "Green Card Benefit Enabled...Pay Rs. " & Val(txttotal1.Text) - per & "/- For Loan Closure"
        ' ''    Else
        ' ''        lbl_green.Text = "Green Card Benefit Enabled...Pay Rs.0/- For Loan Closure"
        ' ''    End If
        ' ''    lbl_green.Visible = True
        ' ''    lbl_balance.Visible = False
        ' ''Else
        ' ''    lbl_balance.Visible = True
        ' ''End If
        '''

        'last2:


        Return lbl_balance

    End Function

    Function CalcDefaultAmt(ByVal loanno As String)
        Dim dr, dr1 As DataRow
        Dim ds, ds2, ds3 As DataSet
        '  Dim strconn As String
        Dim fdate, duedt, lduedate, ltransdate As Date
        Dim Rcvamt, Dmdamt, instamt, PrinBal, int_prv, int_prv_penal, int_t, intrate, penalrate, pnl_t As Double
        Dim PeriodExt As Integer
        Dim defamt As Double = 0
        Dim curdate As DateTime = DateTime.Now
        '--------------------------
        '   strconn = System.Configuration.ConfigurationManager.AppSettings("constr")
        ' Dim strconn As String
        ' strconn = "data source=VIVEK;initial catalog=ksdc_live_latest;user id=sa;password=*******;Connect Timeout=300000;"
        '------------------------
        ds = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select * from tbl_loanreg where int_loanno='" & loanno & "'")
        If ds.Tables.Count > 0 Then
            If ds.Tables(0).Rows.Count > 0 Then
                dr = ds.Tables(0).Rows(0)
                fdate = dr("dt_first_due_date")
                instamt = dr("mny_repayamt")
                PrinBal = dr("mny_loanbal")
                lduedate = DateAdd(DateInterval.Month, dr("int_repay_inst") - 1, fdate)
                PeriodExt = 0
                ''PeriodExt = dr("int_repay_inst_Ext")
                ''If PeriodExt > 0 And lbl_rr.Text.Trim <> "Moratorium Loan" Then
                ''    lbl_rr.Text = lbl_rr.Text & "  Moratorium Loan"
                ''End If
                ltransdate = dr("dt_last_repay_date")
                intrate = Format(dr("int_rate_int"), "0.00")
                penalrate = Format(dr("int_rate_penal"), "0.00")
            End If
        End If
        '------------------------




        ds = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select sum(int_amt),sum(int_penal_r),sum(int_bank_charge_r) from tbl_loantrans where int_loanno='" & loanno & "' and chr_transtype='Receipt' and int_type in (1,2,3,6,15,22) and dt_transaction  <='" & (curdate.ToString("yyyy-MM-dd HH:mm:ss.fff")) & "'")
        If ds.Tables.Count > 0 Then
            If ds.Tables(0).Rows.Count > 0 Then
                For Each dr In ds.Tables(0).Rows
                    If Not IsDBNull(dr(0)) Then Rcvamt = dr(0) - dr(2)
                Next
            End If
        End If

        If Month(curdate) = 2 And Day(fdate) > 28 Then
            duedt = DateSerial(Year(curdate), Month(curdate), 28)
        ElseIf (Month(curdate) = 4 Or Month(curdate) = 6 Or Month(curdate) = 9 Or Month(curdate) = 11) And Day(fdate) > 30 Then
            duedt = DateSerial(Year(curdate), Month(curdate), 30)
        Else
            duedt = DateSerial(Year(curdate), Month(curdate), Day(fdate))
        End If
        If duedt > curdate Then duedt = DateAdd(DateInterval.Month, -1, duedt)
        If duedt > lduedate Then duedt = lduedate

        Dmdamt = ((DateDiff(DateInterval.Month, fdate, duedt) + 1) - PeriodExt) * instamt
        defamt = IIf(Dmdamt - Rcvamt >= 0, Dmdamt - Rcvamt, 0)
        Dim definst As Integer = defamt / instamt

        '''''''''''''''''Penal For Period Over Start''''''''''''''''''''''
        Dim AgeMonth As Integer = 0
        Dim ReceivedMonth As Integer = 0
        Dim PeriodOverDefInst As Integer = 0
        ds3 = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "select * FROM tbl_loantrans WHERE INT_LOANNO ='" & loanno & "' and chr_remark in ('PERIOD OVER','RR Initiated')")
        If ds3.Tables(0).Rows.Count > 0 Then
            AgeMonth = DateDiff(DateInterval.Month, fdate, Date.Today)
            ReceivedMonth = Math.Round((Rcvamt / instamt), 0)
            PeriodOverDefInst = AgeMonth - ReceivedMonth
            ds2 = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select int_int_dues as InterestDues,int_penal_dues as PenalDues from tbl_loanTrans where int_loanno = '" & loanno & "' and int_type<=20 and chr_remark <> 'FINANCIAL YEAR END1' and int_type not in (4,5,12) order by dt_transaction,int_transid")
            dr1 = ds2.Tables(0).Rows(ds2.Tables(0).Rows.Count - 1)
            int_prv = dr1("InterestDues")
            int_prv_penal = dr1("PenalDues")
            intrate = intrate + penalrate
            int_t = int_prv + (CDbl(PrinBal) * (DateDiff(DateInterval.Day, ltransdate, Date.Today) * intrate) / 36500)
            pnl_t = int_prv_penal
            defamt = PrinBal + int_t + pnl_t
            definst = PeriodOverDefInst
        End If
        '''''''''''''''''Penal For Period Over End''''''''''''''''''''''

        Return defamt & "," & definst

    End Function

End Class
