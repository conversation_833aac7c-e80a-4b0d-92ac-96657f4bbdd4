// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "Aujourd'hui";
var L_January   = "Janvier";
var L_February  = "F\u00e9vrier";
var L_March     = "Mars";
var L_April     = "Avril";
var L_May       = "Mai";
var L_June      = "Juin";
var L_July      = "Juillet";
var L_August    = "Ao\u00fbt";
var L_September = "Septembre";
var L_October   = "Octobre";
var L_November  = "Novembre";
var L_December  = "D\u00e9cembre";
var L_Su        = "di";
var L_Mo        = "lu";
var L_Tu        = "ma";
var L_We        = "me";
var L_Th        = "je";
var L_Fr        = "ve";
var L_Sa        = "sa";

// strings for dt_param.js
var L_TIME_SEPARATOR = ":";
var L_AM_DESIGNATOR = "AM";
var L_PM_DESIGNATOR = "PM";

// strings for range parameter
var L_FROM = "De {0}";
var L_TO = "A {0}";
var L_AFTER = "Apr\u00e8s {0}";
var L_BEFORE = "Avant {0}";
var L_FROM_TO = "De {0} \u00e0 {1}";
var L_FROM_BEFORE = "De {0} \u00e0 avant {1}";
var L_AFTER_TO = "Apr\u00e8s {0} \u00e0 {1}";
var L_AFTER_BEFORE = "Apr\u00e8s {0} \u00e0 avant {1}";

// Strings for prompts.js and prompts_param.js
var L_BadNumber		= "Un param\u00e8tre de type \"Nombre\" peut uniquement contenir un signe n\u00e9gatif, des chiffres (\"0-9\"), des symboles de regroupement de chiffres ou un s\u00e9parateur d\u00e9cimal. Veuillez corriger la valeur saisie pour ce param\u00e8tre.";
var L_BadCurrency	= "Un param\u00e8tre de type \"Devise\"  peut uniquement contenir un signe n\u00e9gatif, des chiffres (\"0-9\"), des symboles de regroupement de chiffres ou un s\u00e9parateur d\u00e9cimal. Veuillez corriger la valeur saisie pour ce param\u00e8tre.";
var L_BadDate		= "Un param\u00e8tre de type \"Date\" doit se pr\u00e9senter sous la forme \"Date(aaaa,mm,jj)\", \"aaaa\" correspondant aux quatre chiffres de l'ann\u00e9e, \"mm\" au mois et \"jj\" au jour du mois donn\u00e9.";
var L_BadDateTime   = "Un param\u00e8tre de type \"DateHeure\" doit se pr\u00e9senter sous la forme \"DateTime(aaaa,mm,jj,hh,mm,ss)\",  \"aaaa\" correspondant aux quatre chiffres de l'ann\u00e9e, \"mm\" au mois, \"jj\" au jour du mois, \"hh\" au nombre d'heures en mode 24H, \"mm\" au nombre de minutes et \"ss\" au nombre de secondes.";
var L_BadTime       = "Un param\u00e8tre de type \"Heure\" doit se pr\u00e9senter sous la forme \"Time(hh,mm,ss)\", \"hh\" correspondant au nombre d'heures en mode 24H, \"mm\" au nombre de minutes et \"ss\" au nombre de secondes.";
var L_NoValue       = "Aucune valeur";
var L_BadValue      = "Pour d\u00e9finir \"Aucune valeur\", vous devez d\u00e9finir les deux valeurs De et A \u00e0 \"Aucune valeur\".";
var L_BadBound      = "Vous ne pouvez pas d\u00e9finir \"Aucune limite inf\u00e9rieure\" en m\u00eame temps que \"Aucune limite sup\u00e9rieure\".";
var L_NoValueAlready = "Ce param\u00e8tre a d\u00e9j\u00e0 la valeur \"Aucune valeur\". Supprimez \"Aucune valeur\" avant d'ajouter d'autres valeurs";
var L_RangeError    = "Le d\u00e9but de la plage ne peut pas \u00eatre sup\u00e9rieur \u00e0 la fin de la plage.";
var L_NoDateEntered = "Vous devez saisir une date.";

// Strings for ../html/crystalexportdialog.htm
var L_ExportOptions     = "Options d'exportation";
var L_PrintOptions      = "Options d'impression";
var L_PrintPageTitle    = "Imprimer le rapport";
var L_ExportPageTitle   = "Exporter le rapport";
var L_OK                = "OK";
var L_PrintPageRange    = "Saisissez la plage de pages \u00e0 imprimer.";
var L_ExportPageRange   = "Saisissez la plage de pages \u00e0 exporter.";
var L_InvalidPageRange  = "Les valeurs de la plage de pages sont incorrectes. Veuillez recommencer.";
var L_ExportFormat      = "Veuillez s\u00e9lectionner un format d'exportation dans la liste.";
var L_Formats           = "Formats :";
var L_All               = "Tout";
var L_Pages             = "Pages";
var L_From              = "De :";
var L_To                = "A :";
var L_PrintStep0        = "Pour imprimer :";
var L_PrintStep1        = "1.  Dans la bo\u00eete de dialogue qui s'affiche, s\u00e9lectionnez l'option \"Ouvrir ce fichier\" et cliquez sur OK.";
var L_PrintStep2        = "2.  Cliquez sur l'ic\u00f4ne d'impression d'Acrobat Reader plut\u00f4t que sur le bouton d'impression de votre navigateur.";
var L_RTFFormat         = "Format RTF";
var L_AcrobatFormat     = "Format Acrobat (PDF)";
var L_CrystalRptFormat  = "Crystal Reports (RPT)";
var L_WordFormat        = "MS Word";
var L_ExcelFormat       = "MS Excel 97-2000";
var L_ExcelRecordFormat = "MS Excel 97-2000 (donn\u00e9es uniquement)";
if(typeof(Sys)!=='undefined')Sys.Application.notifyScriptLoaded();
