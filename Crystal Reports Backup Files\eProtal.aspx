﻿<%@ Page Title="eProtal" Language="C#" AutoEventWireup="true" CodeBehind="eProtal.aspx.cs" Inherits="KSDCSCST_Portal.eProtal" %>


<!DOCTYPE html>

<html lang="en">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><%: Page.Title %> - KSDCSCST</title>

    <link rel="icon" href="assets/img/fav_32.png" sizes="32x32" />
    <link rel="icon" href="assets/img/fav_192.png" sizes="192x192" />

    <link rel="stylesheet" href="assets/css/main.css">
    <style>
        input[type="number"]::-webkit-inner-spin-button,
        input[type="number"]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        input[type="number"] {
            -moz-appearance: textfield;
        }



        ul.navbar-nav-dynamic {
            list-style: none;
            padding: 0;
            margin: 0;
        }

            /* Style top-level menu items */
            ul.navbar-nav-dynamic > li.nav-item {
                display: inline-block;
                position: relative;
            }

                ul.navbar-nav-dynamic > li.nav-item > a.nav-link {
                    text-decoration: none;
                    padding: 12px 20px;
                    color: rgba(0,0,0,.5);
                    display: block;
                    transition: background-color 0.2s ease-in-out;
                }

                    /* Hover effect for top-level menu items */
                    ul.navbar-nav-dynamic > li.nav-item > a.nav-link:hover {
                        color: #000;
                    }

            /* Style submenus */
            ul.navbar-nav-dynamic ul.navbar-nav-dynamic {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                background-color: #fff;
                border: 1px solid #ccc;
                z-index: 1;
            }

                ul.navbar-nav-dynamic ul.navbar-nav-dynamic li.nav-item {
                    display: block;
                }

                ul.navbar-nav-dynamic ul.navbar-nav-dynamic a.nav-link {
                    padding: 10px 20px;
                    color: #333;
                    display: block;
                    text-decoration: none;
                    min-width: 250px;
                    width: 100%;
                    border-bottom: 1px solid #dedede;
                }



            /* Show submenus on hover */
            ul.navbar-nav-dynamic li.nav-item:hover > ul.navbar-nav-dynamic {
                display: block;
            }

        /* Add a right arrow indicator to menu items with submenus */
        .dropdown > a::after {
            display: inline-block;
            margin-left: 0.255em;
            vertical-align: 0.255em;
            content: "";
            border-top: 0.3em solid;
            border-right: 0.3em solid transparent;
            border-bottom: 0;
            border-left: 0.3em solid transparent;
        }



        /* Remove spacing for the last menu item */
        ul.navbar-nav-dynamic li.nav-item:last-child {
            margin-right: 0;
        }

        /* Style for active/current page */
        ul.navbar-nav-dynamic a.active {
            background-color: #ddd;
        }

        .dropdown-submenu > a::after {
            border-top: 0.3em solid transparent;
            border-right: 0;
            border-bottom: 0.3em solid transparent;
            border-left: 0.3em solid;
            float: right;
            margin-left: 0.5rem;
            margin-top: 0.5rem;
        }

        ul.navbar-nav-dynamic.submenu {
        }

        li.nav-item.dropdown.dropdown-submenu.dropdown-hover ul {
            position: absolute;
            left: 100%;
            top: 10%;
        }

        .dropdown-submenu:not(:has(ul)) a::after {
            display: none;
        }

        .nav-item.dropdown:not(:has(ul)) a::after {
            display: none;
        }

        .dropdown-menu {
            border-radius: 0.25rem;
        }




        #loader {
            position: absolute;
            left: 50%;
            top: 50%;
            z-index: 1;
            width: 120px;
            height: 120px;
            margin: -76px 0 0 -76px;
            border: 16px solid #f3f3f3;
            border-radius: 50%;
            border-top: 16px solid #3498db;
            -webkit-animation: spin 2s linear infinite;
            animation: spin 2s linear infinite;
        }

        @-webkit-keyframes spin {
            0% {
                -webkit-transform: rotate(0deg);
            }

            100% {
                -webkit-transform: rotate(360deg);
            }
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Add animation to "page content" */
        .animate-bottom {
            position: relative;
            -webkit-animation-name: animatebottom;
            -webkit-animation-duration: 1s;
            animation-name: animatebottom;
            animation-duration: 1s
        }

        @-webkit-keyframes animatebottom {
            from {
                bottom: -100px;
                opacity: 0
            }

            to {
                bottom: 0px;
                opacity: 1
            }
        }

        @keyframes animatebottom {
            from {
                bottom: -100px;
                opacity: 0
            }

            to {
                bottom: 0;
                opacity: 1
            }
        }
    </style>


</head>
<body class="layout-top-nav layout-fixed">
    <div class="wrapper animate-bottom" id="myDiv">
        <div class="content-wrapper">

            <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
            <!-- Font Awesome -->
            <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
            <!-- Theme style -->
            <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">
            <style>
                @media print {
                    html, body {
                        width: 210mm;
                        height: 297mm;
                        margin: 0;
                    }

                    /* Set landscape orientation */
                    @page {
                        size: A4 portrait;
                    }
                }
            </style>

            <!-- Content Header (Page header) -->
            <section class="content-header">
                <div class="container-fluid">
                    <div class="row mb-2">
                        
                        <div class="col-sm-12" style="text-align:center;">
                              <img src="assets/img/logo.png" />
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->
            </section>
            <section class="content-header">
                <div class="container-fluid">
                    <div class="row mb-2">
                        <div class="col-sm-12">
                            <h1  style="text-align:center;">Fetch Loan Details</h1>
                        </div>
                        <div class="col-sm-6">
                            <ol class="breadcrumb float-sm-right">
                                <li class="breadcrumb-item"><a href="#">Home</a></li>
                                <li class="breadcrumb-item active">Leads</li>
                            </ol>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->
            </section>

            <!-- Main content -->
            <section class="content">
                <div class="container-fluid">
                    <div class="row">
                        <!-- /.col -->
                        <div class="col-md-6 mx-auto">
                            <div class="card">

                                <div class="card-body">
                                    <div class="tab-content">

                                        <div class="active tab-pane" id="settings">
 
                                            <div class="form-group row">
                                                <label for="inputName2" class="col-sm-3 col-form-label">Enter Loan No/Reg No*</label>
                                                <div class="col-sm-9">
                                                    <input type="text" max="12" class="form-control" id="txtLoanNo" placeholder="Enter Loan No/Reg No">
                                                </div>
                                            </div>
 
                                            <div class="form-group row">
                                                <div class="col-sm-12 text-right">

                                                    <a href="http://ksdcscst.kerala.gov.in/" class="btn btn-dark">Back</a>
                                                    <%--   <a onclick="Print();" class="btn btn-info">Print</a>--%>
                                                    <a onclick="Save();" class="btn btn-success">Submit</a> 
                                                </div>
                                            </div>

                                        </div>
                                        <!-- /.tab-pane -->
                                    </div>
                                    <!-- /.tab-content -->
                                </div>
                                <!-- /.card-body -->
                            </div>
                            <!-- /.card -->
                        </div>
                        <!-- /.col -->
                    </div>
                    <!-- /.row -->
                </div>
                <!-- /.container-fluid -->
            </section>

            <div id="Print_Section" style="display: none;">

                <div style="padding: 0px; float: left; width: 100%;">

                    <div style="margin: 20px;">
                        <div style="width: 100%">
                            <div style="width: 30%; float: left;">
                                <img style="width: 220px;" src="assets/img/Logo_Black_And_White.jpg" />
                                <p style="font-size: 12px; font-weight: bold; margin: 0; margin-top: 9px;">A GOVT.OF KERALA UNDER TAKING</p>
                            </div>
                            <div style="width: 70%; float: left;">
                                <p style="font-size: 16px; font-weight: bold; line-height: 1.5; margin-bottom: 0px !important; margin-top: 0px;">കേരള സംസ്ഥാന പട്ടികജാതി  പട്ടിക വർഗ്ഗ വികസന  കോർപ്പറേഷൻ  ലിമിറ്റഡ്</p>
                                <p style="margin-bottom: 0; font-size: 14px; margin-top: 5px;">CIN: U91990KL1972SGC002466</p>
                                <p style="margin-bottom: 0; font-size: 12px; margin-top: 5px;">
                                    <b>CORPORATE OFFICE:</b> P.B. No. 523, Town Hall Road, Thrissur - 680020 |
                                                       
                                                        <b>Off.Ph:</b> 0487 2331064 |
                                                            <br />
                                    <b>Email : </b><EMAIL> | Website : ksdcscst.kerala.gov.in
                                </p>
                            </div>
                        </div>

                        <div style="width: 100%; float: left; margin-top: 5px;">
                            <div style="width: 25%; float: left;">
                                <div style="text-align: left; font-weight: bold;">
                                    <span id="span_Receipt_No" style="font-size: 14px;">Receipt No: </span>
                                    <label style="font-size: 14px; font-weight: normal !important;"></label>
                                </div>
                            </div>
                            <div style="width: 50%; float: left;">
                                <div style="text-align: center; font-weight: bold;">
                                    <p style="font-size: 14px; text-decoration: underline;">DISTRICT OFFICE :  <span id="span_Office_Name"></span></p>
                                </div>
                            </div>
                            <div style="width: 25%; float: left;">
                                <div style="text-align: right; font-weight: bold;">
                                    <span style="font-size: 14px;">Date : </span>
                                    <label id="lblDate" style="font-size: 14px; font-weight: normal !important;"></label>
                                </div>
                            </div>
                        </div>


                        <hr style="float: left; width: 100%; margin-top: 0px;" />
                        <div style="width: 100%; float: left; margin-top: 5px;">
                            <div style="width: 100%; float: left;">
                                <div style="text-align: left; font-weight: bold;">
                                    <span style="font-size: 14px; float: left; font-weight: normal !important;">Received From </span>
                                    <label id="lblName" style="font-size: 14px; float: left; margin-left: 5px; margin-right: 5px;"></label>
                                    <span style="font-size: 14px; float: left; font-weight: normal !important;">the sum of Rupees </span>
                                    <label style="font-size: 14px; float: left; margin-left: 5px; margin-right: 5px;">Ten Rupees Only</label>

                                </div>
                            </div>
                            <div style="width: 100%; float: left;">
                                <span style="font-size: 14px; font-weight: bold;">in cash towards the following </span>
                            </div>
                            <div style="width: 100%; float: left;">
                                <table border="1" style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                                    <tr>
                                        <td rowspan="2" style="text-align: center; font-weight: bold; font-size: 12px;">തിയ്യതി</td>
                                        <td rowspan="2" style="text-align: center; font-weight: bold; font-size: 12px;">ഇനം</td>
                                        <td colspan="2" style="text-align: center; font-weight: bold; font-size: 12px;">തുക</td>

                                    </tr>
                                    <tr>

                                        <td style="text-align: center; font-weight: bold;">Rs.</td>
                                        <td style="text-align: center; font-weight: bold;">Ps.</td>

                                    </tr>
                                    <tr>
                                        <td><span id="span_Date"></span></td>
                                        <td style="font-size: 12px;">അപേക്ഷ ഫോറം</td>

                                        <td style="text-align: right;">10</td>
                                        <td style="text-align: right;">0</td>
                                    </tr>
                                    <tr>
                                        <td colspan="2" style="padding-right: 10px; font-weight: bold; text-align: right; font-size: 12px;">ആകെ</td>

                                        <td style="font-weight: bold; text-align: right;" colspan="2">10.00</td>

                                    </tr>
                                    <tr>
                                        <td style="font-weight: bold;">Prepared</td>
                                        <td style="font-weight: bold;">Checked</td>
                                        <td style="text-align: right;" colspan="2"></td>

                                    </tr>
                                </table>
                            </div>
                            <div style="width: 100%; float: right;">
                                <span style="width: 45%; font-size: 14px; text-align: right; line-height: 1.5; margin-bottom: 0px !important; margin-top: 10px; float: right;">For The Kerala State Development Corporation For Scheduled Castes & Scheduled Tribes Limited </span>

                            </div>
                            <div style="width: 100%; float: right;">
                                <span style="width: 45%; font-size: 14px; text-align: right; line-height: 1.5; margin-bottom: 0px !important; margin-top: 65px; float: right; font-weight: bold;">ജില്ലാ മാനേജർ</span>

                            </div>
                            <div style="width: 100%; float: left; text-align: center;">
                                <span style="font-size: 14px; text-align: center; line-height: 1.5; margin-bottom: 0px !important; margin-top: 10px;">This Receipt is valid only on realisation of Cheque/Draft</span>

                            </div>
                        </div>
                    </div>

                </div>

            </div>
            <!-- /.content -->

            <!-- jQuery -->
            <script src="assets/plugins/jquery/jquery.min.js"></script>
            <!-- Bootstrap 4 -->
            <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
            <!-- AdminLTE App -->
            <script src="assets/dist/js/adminlte.min.js"></script>
            <!-- AdminLTE for demo purposes -->
            <script src="assets/dist/js/demo.js"></script>
            <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
            <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
            <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
            <script src="assets/plugins/select2/js/select2.full.min.js"></script>
            <script src="assets/plugins/toastr/toastr.min.js"></script>



            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.js"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/inputmask/5.0.4/jquery.inputmask.min.js"></script>
            <script>
                var $hiddenForm;
                var Toast;
                var Is_Valid_Phone_Number = false;
                $(document).ready(function () {
                    
                    Toast = Swal.mixin({
                        toast: true,
                        position: 'top-end',
                        showConfirmButton: false,
                        timer: 4000
                    });

                    // Get the current date
                    var currentDate = new Date();

                    // Format the date as desired (e.g., "MM/DD/YYYY")
                    var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

                    // Display the formatted date in the "currentDate" div
                    $('#txtApplication_Date').val(formattedDate);

          //  $("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
          //  $("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");
           // Load_All_Schemes();
          //  Load_All_Districts();

            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }


            $(".form-control").on("focusout", function () {
                $(".form-control").removeAttr("style");

                var This_Id = $(this).attr("id");
                var This_Control = $(this);
                if (This_Id == "txtApplicant_Name") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Name of Applicant is required !'
                        })
                    }
                    else if (!Is_Valid_Text(This_Control.val().trim())) {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Special characters or Numbers not allowed in Name of Applicant !'
                        })
                    }
                }
                else if (This_Id == "dropScheme") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Loan Scheme is required !'
                        })
                    }
                }
                else if (This_Id == "dropCast") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Caste is required !'
                        })
                    }
                }
                else if (This_Id == "dropSubCast") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Sub Caste is required !'
                        })
                    }
                }
                else if (This_Id == "txtPhoneNo") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Phone No is required !'
                        })
                    }
                }
                //else if (This_Id == "txtAadharNumber") {
                //    if (This_Control.val().trim() == "") {
                //        Focus_Error(This_Control);
                //        Toast.fire({
                //            icon: 'error',
                //            title: 'Aadhar No is required !'
                //        })
                //    }
                //}



                // Your common focusout function code goes here
                //   alert("Input value changed: " + value);
            });



            //$(document).on("input", ".form-control", function () {
            //    // Get the current value of the input
            //    var currentValue = $(this).val();

            //    // Convert the value to propcase
            //    var propcaseValue = currentValue.replace(/\w\S*/g, function (txt) {
            //        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
            //    });

            //    // Update the input with the propcase value
            //    $(this).val(propcaseValue);
            //});



        });


                function Print() {
                    var printWindow = window.open('', '_blank');
                    printWindow.document.open();
                    printWindow.document.write('<html><head><title>Print</title></head><body style="width: 210mm;height: 297mm;margin: 0;  ">');
                    printWindow.document.write($("#Print_Section").html());
                    printWindow.document.write('</body></html>');
                    printWindow.document.close();
                    printWindow.print();
                    if (confirm('Print dialog initiated. Please close the window after printing.')) {
                        printWindow.close();
                    }
                }

                function Load_All_Districts() {

                    $.ajax({
                        type: "POST",
                        dataType: "json",
                        url: "WebService.asmx/Select_All_Districts",
                        contentType: "application/json; charset=utf-8",
                        success: function (data) {
                            $('#dropDistrict').empty();
                            $('#dropDistrict').append('<option value="0">Select</option>');

                            $.each(data.d, function (key, value) {
                                var Id = value.Id;
                                var District_Name = value.District_Name;
                                var html = '<option value="' + Id + '">' + District_Name + '</option>';
                                if (Id != 15) {
                                    $('#dropDistrict').append(html);
                                }
                            });


                        },
                        error: function (error) {


                            // alert("error" + error.responseText);
                            Swal.fire({
                                icon: 'error',
                                title: 'Oops...',
                                text: 'Something went wrong!',

                            })
                        }
                    }).done(function () {

                        $('#dropDistrict').change(function () {
                            Load_All_Sub_Districts($(this).val());
                        });

                    });

                }
                function Load_All_Sub_Districts(District_Id) {

                    $.ajax({
                        type: "POST",
                        dataType: "json",
                        url: "WebService.asmx/Select_All_Sub_Districts",
                        data: JSON.stringify({ District_Id: District_Id }), // If you have parameters

                        contentType: "application/json; charset=utf-8",
                        success: function (data) {
                            $('#dropSubDistrict').empty();
                            $('#dropSubDistrict').append('<option value="0">Select</option>');

                            $.each(data.d, function (key, value) {
                                var Id = value.Id;
                                var Sub_District_Name = value.Sub_District_Name;
                                var html = '<option value="' + Id + '">' + Sub_District_Name + '</option>';

                                $('#dropSubDistrict').append(html);

                            });


                        },
                        error: function (error) {


                            // alert("error" + error.responseText);
                            Swal.fire({
                                icon: 'error',
                                title: 'Oops...',
                                text: 'Something went wrong!',

                            })
                        }
                    }).done(function () {



                    });

                }

                function Load_All_Schemes() {

                    $.ajax({
                        type: "POST",
                        dataType: "json",
                        url: "WebService.asmx/Select_All_Schemes",
                        contentType: "application/json; charset=utf-8",
                        success: function (data) {
                            $('#dropScheme').empty();
                            $('#dropScheme').append('<option value="0">Select</option>');

                            $.each(data.d, function (key, value) {
                                var Id = value.Id;
                                var IsActive = value.IsActive;
                                var Scheme = value.Scheme;
                                var html = '<option value="' + Id + '">' + Scheme + '</option>';
                                if (IsActive == 1) {
                                    $('#dropScheme').append(html);
                                }
                            });


                        },
                        error: function (error) {


                            // alert("error" + error.responseText);
                            Swal.fire({
                                icon: 'error',
                                title: 'Oops...',
                                text: 'Something went wrong!',

                            })
                        }
                    }).done(function () {

                        Load_All_Cast();

                    });

                }

                function Load_All_Cast() {

                    $.ajax({
                        type: "POST",
                        dataType: "json",
                        url: "WebService.asmx/Select_All_Cast",
                        contentType: "application/json; charset=utf-8",
                        success: function (data) {
                            $('#dropCast').empty();
                            $('#dropCast').append('<option value="0">Select</option>');

                            $.each(data.d, function (key, value) {
                                var Id = value.Id;

                                var Cast = value.Cast;
                                var html = '<option value="' + Id + '">' + Cast + '</option>';

                                $('#dropCast').append(html);

                            });


                        },
                        error: function (error) {


                            // alert("error" + error.responseText);
                            Swal.fire({
                                icon: 'error',
                                title: 'Oops...',
                                text: 'Something went wrong!',

                            })
                        }
                    }).done(function () {

                        $('#dropCast').change(function () {
                            Load_All_SubCast($(this).val());
                        });

                    });

                }


                function Load_All_SubCast(Cast_Id) {

                    $.ajax({
                        type: "POST",
                        dataType: "json",
                        url: "WebService.asmx/Select_All_Sub_Cast",
                        data: JSON.stringify({ Cast_Id: Cast_Id }), // If you have parameters

                        contentType: "application/json; charset=utf-8",
                        success: function (data) {
                            $('#dropSubCast').empty();
                            $('#dropSubCast').append('<option value="0">Select</option>');

                            $.each(data.d, function (key, value) {
                                var Id = value.Id;

                                var SubCast = value.SubCast;
                                var html = '<option value="' + Id + '">' + SubCast + '</option>';

                                $('#dropSubCast').append(html);

                            });


                        },
                        error: function (error) {


                            // alert("error" + error.responseText);
                            Swal.fire({
                                icon: 'error',
                                title: 'Oops...',
                                text: 'Something went wrong!',

                            })
                        }
                    }).done(function () {


                    });

                }
            
                function Save() {
                    //alert(Counter);
                   // if (confirm('Are you sure you want to perform this action?')) {



                        var LoanNo = $("#txtLoanNo");

                        




                        $(".form-control").removeAttr("style");



                        if (LoanNo.val().trim() == "") {
                            Focus_Error(LoanNo);
                            Toast.fire({
                                icon: 'error',
                                title: 'Loan No is required !'
                            })
                        }
                        

                        else {
                           

                            $.ajax({
                                type: "POST",
                                dataType: "json",
                                url: "WebService.asmx/Select_Lonee_Details_By_Loan_No",
                                data: JSON.stringify({ int_loanno: LoanNo.val() }), // If you have parameters
                                contentType: "application/json; charset=utf-8",
                                success: function (data) {
                                    if (data.d.length == 0) {
                                        Search_Reg_No();
                                    }
                                    else {
                                        $.each(data.d, function (key, value) {
                                            var Reg_No = value.vchr_appreceivregno;
                                            var Name = value.vchr_applname;
                                            var Loan_No = value.int_loanno;
                                            location.href = 'eProtal_View?loan_no=' + Loan_No ;
                                             
                                        });
                                    }
                                },
                                error: function (jqXHR, textStatus, errorThrown) {
                                    // Handle AJAX error
                                    //   alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);

                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Oops...',
                                        text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                                    })

                                }

                            }).done(function () {
                                //Swal.fire({
                                //    icon: 'success',
                                //    title: 'Message',
                                //    text: 'We will contact you as soon as possible !',

                                //}).then((result) => {
                                //    if (result.isConfirmed) {
                                //        // OK button clicked

                                //        location.href = "http://ksdcscst.kerala.gov.in/";
                                //        // Your code here
                                //    }
                                //});

                            });


                        }
                   //}
                   //else {
                   //    // User clicked "Cancel", do nothing
                   //    alert('Action was cancelled.');
                   //}



                }

                function Search_Reg_No() {
                    var LoanNo = $("#txtLoanNo");


                    $.ajax({
                        type: "POST",
                        dataType: "json",
                        url: "WebService.asmx/Select_tbl_loanapp_By_Reg_No",
                        data: JSON.stringify({ Reg_No: LoanNo.val() }), // If you have parameters
                        contentType: "application/json; charset=utf-8",
                        success: function (data) {
                            if (data.d.length == 0) {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Oops...',
                                    text: 'Invalid Register Number/Loan Number !',

                                })
                            }
                            else {
                                $.each(data.d, function (key, value) {
                                    var Reg_No = value.vchr_appreceivregno;
                                    var Name = value.vchr_applname;
                                    var Loan_No = value.int_loanno;
                                    location.href = 'eProtal_View?loan_no=' + Loan_No;

                                });
                            }
                        },
                        error: function (jqXHR, textStatus, errorThrown) {
                            // Handle AJAX error
                            //   alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);

                            Swal.fire({
                                icon: 'error',
                                title: 'Oops...',
                                text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                            })

                        }

                    }).done(function () {
                        //Swal.fire({
                        //    icon: 'success',
                        //    title: 'Message',
                        //    text: 'We will contact you as soon as possible !',

                        //}).then((result) => {
                        //    if (result.isConfirmed) {
                        //        // OK button clicked

                        //        location.href = "http://ksdcscst.kerala.gov.in/";
                        //        // Your code here
                        //    }
                        //});

                    });
                }

                function Focus_Error(Control) {
                    Control.css("border", "2px solid red");
                    Control.focus();
                }

                function Is_Valid_Text(inputText) {


                    var regex = /^[a-zA-Z\s]+$/;

                    if (regex.test(inputText)) {
                        // The input contains only alphabetical characters
                        return true;
                    } else {
                        // The input contains non-alphabetical characters
                        return false;
                    }

                }


                function Is_Valid_Mobile_Number(mobile_number) {
                    // Regex to check valid
                    // mobile_number  
                    let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

                    // if mobile_number 
                    // is empty return false
                    if (mobile_number == null) {
                        Is_Valid_Phone_Number = false;
                        return false;
                    }

                    // Return true if the mobile_number
                    // matched the ReGex
                    if (regex.test(mobile_number) == true) {
                        Is_Valid_Phone_Number = true;
                        return true;
                    }
                    else {
                        Is_Valid_Phone_Number = false;
                        return false;
                    }
                }



            </script>


        </div>
        <!-- /.content-wrapper -->
        <footer class="main-footer" style="text-align: center;">
            <strong>Copyright &copy; 2023 <a href="#">KSDC</a>.</strong>
            All rights reserved.
   
           
        </footer>

        <!-- Control Sidebar -->
        <aside class="control-sidebar control-sidebar-dark">
            <!-- Control sidebar content goes here -->
        </aside>
        <!-- /.control-sidebar -->
    </div>

    <!--Start of Tawk.to Script-->
    <!--Start of Tawk.to Script-->
    <%--<script type="text/javascript">
    var Tawk_API = Tawk_API || {}, Tawk_LoadStart = new Date();
    (function () {
        var s1 = document.createElement("script"), s0 = document.getElementsByTagName("script")[0];
        s1.async = true;
        s1.src = 'https://embed.tawk.to/655947c9d600b968d314a7bd/1hfic0r4g';
        s1.charset = 'UTF-8';
        s1.setAttribute('crossorigin', '*');
        s0.parentNode.insertBefore(s1, s0);
    })();
</script>--%>
    <!--End of Tawk.to Script-->
    <!--End of Tawk.to Script-->

    <%--      <script type="text/javascript" language="javascript">
          function DisableBackButton() {
              window.history.forward()
          }
          DisableBackButton();
          window.onload = DisableBackButton;
          window.onpageshow = function (evt) { if (evt.persisted) DisableBackButton() }
          window.onunload = function () { void (0) }
      </script>--%>



    <script type="text/javascript">





        $(document).ready(function () {
            // Make an AJAX request to fetch menu data from the server

            $('input[type="text"]').on('input', function () {
                $(this).val($(this).val().toUpperCase());
            });


            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/GetMenuItems",
                contentType: "application/json; charset=utf-8",
                success: function (data) {




                    var menu = createMenu(data.d, null);
                    $('#menu-container').append(menu);

                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });


        });

        function createMenu(menuItems, parentId) {
            var ul = $('<ul class="navbar-nav-dynamic"></ul>');
            $.each(menuItems, function (index, item) {
                if (item.ParentMenuItemID === parentId) {
                    var li = $('<li class="nav-item dropdown"></li>');

                    if (item.URL) {
                        var link = $('<a   aria-haspopup="true" aria-expanded="false" class="nav-link dropdown-item"></a>').attr('href', item.URL).text(item.Label);
                        li.append(link);
                    } else {
                        var link = $('<a aria-haspopup="true" aria-expanded="false" class="nav-link dropdown-item"></a>').attr('href', item.URL).text(item.Label);

                        li.append(link);
                    }
                    var subMenu = createsubMenu(menuItems, item.Id);
                    if (subMenu.children().length > 0) {
                        li.append(subMenu);

                    }
                    ul.append(li);
                }
            });
            return ul;
        }

        function createsubMenu(menuItems, parentId) {
            var ul = $('<ul class="navbar-nav-dynamic dropdown-menu border-0 shadow"></ul>');
            $.each(menuItems, function (index, item) {
                if (item.ParentMenuItemID === parentId) {
                    var li = $('<li class="nav-item dropdown dropdown-submenu dropdown-hover"></li>');

                    if (item.URL) {
                        var link = $('<a   aria-haspopup="true" aria-expanded="false" class="nav-link dropdown-item"></a>').attr('href', item.URL).text(item.Label);
                        li.append(link);
                    } else {
                        var link = $('<a aria-haspopup="true" aria-expanded="false" class="nav-link dropdown-item"></a>').attr('href', item.URL).text(item.Label);

                        li.append(link);
                    }
                    var subMenu = createsubMenu(menuItems, item.Id);
                    if (subMenu.children().length > 0) {
                        li.append(subMenu);

                    }
                    ul.append(li);
                }
            });
            return ul;
        }



    </script>
    <script>

        $(function () {
            $('input[type="number"]').on('wheel', function (e) {
                e.preventDefault();
            });
            // GetMenuItems();
        });

        function GetMenuItems() {
            var Menu_HTML = "";
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/GetMenuItems",
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    var filteredData = filterDataById(data.d, 1); // Replace '3' with your desired id

                    // Now 'filteredData' contains the filtered JSON objects
                    console.log(filteredData);


                    Menu_HTML = Menu_HTML + '<ul id="Main_UL" class="navbar-nav">';
                    $.each(data.d, function (key, value) {
                        if (value.ParentMenuItemID == null) {
                            var filteredData = filterDataById(data.d, value.Id);
                            if (filteredData.length == 0) {
                                Menu_HTML = Menu_HTML + '<li class="nav-item" id="LI_' + value.Id + '"><a href="Dashboard.aspx" aria-haspopup="true" aria-expanded="false" class="nav-link"> ' + value.Label + '</a>';

                            }
                            else {
                                Menu_HTML = Menu_HTML + '<li class="nav-item dropdown" id="LI_' + value.Id + '"><a href="#" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" class="nav-link dropdown-toggle"> ' + value.Label + '</a>';

                            }

                            if (filteredData.length != 0) {
                                Menu_HTML = Menu_HTML + '<ul aria-labelledby="dropdownSubMenu1" class="dropdown-menu border-0 shadow" style="left: 0px; right: inherit;"">';

                                for (var i = 0; i < filteredData.length; i++) {
                                    Menu_HTML = Menu_HTML + '<li class="nav-item dropdown" id="LI_' + filteredData[i].Id + '" > <a href="Dashboard.aspx" aria-haspopup="true" aria-expanded="false" class="nav-link"> ' + filteredData[i].Label + '</a></li > ';
                                    if (i <= filteredData.len2th - 2) {
                                        Menu_HTML = Menu_HTML + ' <li class="dropdown-divider"></li>';
                                    }


                                }
                                Menu_HTML = Menu_HTML + '</ul > ';


                            }
                            Menu_HTML = Menu_HTML + '</li>';
                        }
                        // else {
                        //     $('#LI_' + value.ParentMenuItemID).append('<ul class="navbar-nav">'
                        //         + '<li class="nav-item dropdown" id="LI_' + value.Id + '"> <a href="Dashboard.aspx" aria-haspopup="true" aria-expanded="false" class="nav-link"> ' + value.Label + '</a></li>'
                        //         + '</ul > ');
                        // }
                        // alert(value.Id);
                    });

                    // var menu = constructMenu(data.d);
                    // $('#menu-container').append(menu);

                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Menu_HTML = Menu_HTML + '</ul>';
                $('#menu-container').append(Menu_HTML);
            });

        }

        function filterDataById(data, id) {
            return data.filter(function (item) {
                return item.ParentMenuItemID === id;
            });
        }

        var myVar;

        function myFunction() {
            myVar = setTimeout(showPage, 1000);
        }

        function showPage() {
            document.getElementById("loader").style.display = "none";
            document.getElementById("myDiv").style.display = "block";
        }



    </script>
</body>
</html>
