﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Surety_Employee_Edit.aspx.cs" Inherits="KSDCSCST_Portal.Surety_Employee_Edit" %>


<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
 <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- daterange picker -->
    <link rel="stylesheet" href="assets/plugins/daterangepicker/daterangepicker.css">
    <!-- iCheck for checkboxes and radio inputs -->
    <link rel="stylesheet" href="assets/plugins/icheck-bootstrap/icheck-bootstrap.min.css">
    <!-- Bootstrap Color Picker -->
    <link rel="stylesheet" href="assets/plugins/bootstrap-colorpicker/css/bootstrap-colorpicker.min.css">
    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet" href="assets/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css">
    <!-- Select2 -->
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">
    <!-- Bootstrap4 Duallistbox -->
    <link rel="stylesheet" href="assets/plugins/bootstrap4-duallistbox/bootstrap-duallistbox.min.css">
    <!-- BS Stepper -->
    <link rel="stylesheet" href="assets/plugins/bs-stepper/css/bs-stepper.min.css">
    <!-- dropzonejs -->
    <link rel="stylesheet" href="assets/plugins/dropzone/min/dropzone.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">
    <style>
        input:disabled {
            border: 0;
        }
    </style>
    <style>
        /* Styles for the search input */
        txtAddress_Post_Office {
            width: 300px;
            padding: 10px;
            font-size: 16px;
        }
        txtPresent_Post_Office {
            width: 300px;
            padding: 10px;
            font-size: 16px;
        }
        txtPermanent_Post_Office {
            width: 300px;
            padding: 10px;
            font-size: 16px;
        }
        /* Styles for the search results container */
        #search-results {
            list-style: none;
            padding: 0;
            margin-top: 5px;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
        }

            /* Styles for individual search result items */
            #search-results li {
                padding: 5px;
                cursor: pointer;
            }

                /* Highlight the selected result */
                #search-results li:hover {
                    background-color: #f2f2f2;
                }

                 #search-results-pre-pin {
            list-style: none;
            padding: 0;
            margin-top: 5px;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
        }

            /* Styles for individual search result items */
            #search-results-pre-pin li {
                padding: 5px;
                cursor: pointer;
            }

                /* Highlight the selected result */
                #search-results-pre-pin li:hover {
                    background-color: #f2f2f2;
                }

                        #search-results-per-pin {
            list-style: none;
            padding: 0;
            margin-top: 5px;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
        }

            /* Styles for individual search result items */
            #search-results-per-pin li {
                padding: 5px;
                cursor: pointer;
            }

                /* Highlight the selected result */
                #search-results-per-pin li:hover {
                    background-color: #f2f2f2;
                }
                 #search-results-dofficer-pin {
            list-style: none;
            padding: 0;
            margin-top: 5px;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
        }

            /* Styles for individual search result items */
            #search-results-dofficer-pin li {
                padding: 5px;
                cursor: pointer;
            }

                /* Highlight the selected result */
                #search-results-dofficer-pin li:hover {
                    background-color: #f2f2f2;
                }
    </style>


    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Guarantor Employee Modification</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">Guarantor Employee Modification</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">
                                    <div class="Sub_Header">Employee Surety Details</div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Application Register No*</label>
                                        <div class="col-sm-9">
                                            <input disabled="disabled" type="text" class="form-control" id="txtApplicationRegNo" placeholder="Receipt No">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Applicant Name*</label>
                                        <div class="col-sm-9">
                                            <input type="text" disabled="disabled" class="form-control" id="txtApplicant_Name" placeholder="Applicant Name">
                                        </div>
                                    </div>
									<div class="Sub_Header">Surety details</div>
									
                                    
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name Of Employee*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtName_Of_Employee" placeholder="Name Of Employee">
                                        </div>
                                    </div>
									
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Father's Name*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtName_Of_Father" placeholder="Father's Name">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Spouse's Name*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtName_Of_Spouse" placeholder="Spouse's Name">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Designation*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtDesignation" placeholder="Designation">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">PEN No/Employee Code*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtPEN_No_Employee_Code" placeholder="PEN No/Employee Code">
                                        </div>
                                    </div>
                                    <div class="Sub_Header">Address Details</div>



                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Office Name*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtAddress_Office_Name" placeholder="Office Name">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Lane1*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtAddress_Lane1" placeholder="Lane1">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Lane2*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtAddress_Lane2" placeholder="Lane2">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Pincode*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtAddress_Pincode" placeholder="Search...">
                                            <ul id="search-results"></ul>
                                        </div>
                                    </div>
                                                                 

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Post Office*</label>
                                        <div class="col-sm-9">
                                            <input type="text"  disabled="disabled"  class="form-control" id="txtAddress_Post_Office" placeholder="Post Office">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Phone*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtAddress_Phone" placeholder="Phone">
                                        </div>


                                    </div>

                                 
									<div class="Sub_Header">Present Address</div>

                                 	
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">House Name/No.*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtPresent_House_Name_Or_No" placeholder="House Name/No.">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">Lane1*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtPresent_Lane1" placeholder="Lane1">
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">Lane2*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtPresent_Lane2" placeholder="Lane2">
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">Pincode*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtPresent_Pincode" placeholder="Pincode">
                                                                         <ul id="search-results-pre-pin"></ul>
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">Post Office*</label>
                                        <div class="col-sm-9">
                                            <input   type="text"  disabled="disabled"  class="form-control" id="txtPresent_Post_Office" placeholder="Post Office">
               
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">phone*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtPresent_Phone" placeholder="phone">
                                        </div>
                                    </div>
									
									 <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Village*</label>
                                        <div class="col-sm-9">
                                            <select id="dropPresent_Village"   class="form-control">
                                            </select>
                                        </div>
                                    </div>
									
									
									   <div class="Sub_Header">Permanent Address
                                        <div style="float: right;">
                                             <input style="margin-right: 6px;margin-top: 4px;" type="checkbox" name="chk_Same_As_Present_Address" id="chk_Same_As_Present_Address">Same As Present Address 
                                        </div>
                                    </div>


									
									 
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">House Name/No.*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtPermanent_House_Name_Or_No" placeholder="House Name/No.">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">Lane1*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtPermanent_Lane1" placeholder="Lane1">
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">Lane2*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtPermanent_Lane2" placeholder="Lane2">
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">Pincode*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtPermanent_Pincode" placeholder="Pincode">
                                             <ul id="search-results-per-pin"></ul>
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">Post Office*</label>
                                        <div class="col-sm-9">
                                            <input disabled="disabled"   type="text" class="form-control" id="txtPermanent_Post_Office" placeholder="Post Office">
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">phone*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtPermanent_Phone" placeholder="phone">
                                        </div>
                                    </div>
									 
								 
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Village*</label>
                                        <div class="col-sm-9">
                                            <select id="dropPermanent_Village" class="form-control">
                                            </select>
                                        </div>
                                    </div>

 
                                    <div class="Sub_Header">Service Details</div>



                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Date of Birth*</label>
                                        <div class="col-sm-9">
                                            <input type="date" class="dateandtime form-control" id="txtDate_Of_Birth" >
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Service Entry Date*</label>
                                        <div class="col-sm-9">
                                            <input type="date" class="dateandtime form-control" id="txtService_Entry_Date"  >
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Date of Retirement*</label>
                                        <div class="col-sm-9">
                                            <input type="date" class="dateandtime form-control" id="txtDate_Of_Retirement"  >
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Scale Of Pay*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtScale_Of_Pay" placeholder="Scale Of Pay">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Pension Scheme*</label>
                                        <div class="col-sm-9">
                                            <select id="dropPension_Scheme"   class="form-control">
                                                  <option value="0">[Select]</option>
                                                        <option value="NPS">NPS</option>
                                                        <option value="STATUTORY">STATUTORY</option>
                                                        <option value="OTHERS">OTHERS</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group row" id="NPS_PRAN">
                                        <label class="col-sm-3 col-form-label">PRAN*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtPRAN" placeholder="PRAN">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">PF No*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtPF_No" placeholder="PF No*">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Basic Pay*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtBasic_Pay" placeholder="Basic Pay">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Net Salary*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtNet_Salary" placeholder="Net Salary">
                                        </div>
                                    </div>
                                    <div class="form-group row">
									 <div class="col-sm-6">
									  <div class="form-group row">
                                        <label class="col-sm-6 col-form-label">Gross Salary*</label>
                                        <div class="col-sm-6">
                                            <input type="text" class="form-control" id="txtGross_Salary" placeholder="Gross Salary">
                                        </div>
                                    </div>
									 </div>
									  <div class="col-sm-6">
									  <div class="form-group row">
                                        <label class="col-sm-6 col-form-label">Gazetted Officer</label>
                                        <div class="col-sm-6">
                                           <input class="col-sm-0 form-check-input" style="margin: 0;position: relative;" type="checkbox" id="checkGazetted_Officer" >
                                        </div>
                                    </div>
									 </div>
									 
									 
									 
                                    </div>
									
                                                                        <div class="form-group row" id="GazettedOfficer">
                                        <label class="col-sm-3 col-form-label">Audit No / SDO No*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtAuditNoSDONo" placeholder="Audit No / SDO No">
                                        </div>
                                    </div>



								 <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Department*</label>
                                        <div class="col-sm-9">
                                            <select id="dropDepartment"   class="form-control">
                                            </select>
                                            <input type="hidden" id="Department" name="Department" value="">

                                        </div>
                                    </div>
									
									
									

									
									



                                 <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name Of Drawing Officer/Higher Office*</label>
                                        <div class="col-sm-5">
                                            <input type="text" class="form-control" id="txtName_Of_Drawing_Officer_Or_sHigher_Office" placeholder="Name Of Drawing Officer/Higher Office">
                                        </div>
										
										
                                    </div>
									
									<div class="form-group row" style="display:none;">
									 <label class="col-sm-3 col-form-label"></label>
                                        <div class="col-sm-9">
										 <label class="col-sm-8 col-form-label">Drawing Officer Same as Surety</label>
                                     
                                            <input class="col-sm-1 form-check-input" style="margin: 0;position: relative;" type="checkbox" id="checkBDrawing_Officer_Same_As_Surety" >
                                        </div>
									    
                                        

                                    </div>
			 
	

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Designation*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtDrawing_Or_Higher_Officer_Designation" placeholder="Designation">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Office Name*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtDrawing_Officer_Or_Higher_Office_OfficeName" placeholder="Office Name">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Lane1*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtDrawing_Or_Higher_Office_Lane1" placeholder="Lane1">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Lane2*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtDrawing_Or_Higher_Office_Lane2" placeholder="Lane2">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Pincode*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtDrawing_Or_Higher_Office_Pincode" placeholder="Pincode">
                                            <ul id="search-results-dofficer-pin"></ul>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Post Office*</label>
                                        <div class="col-sm-9">
                                            <input type="text" disabled="disabled" class="form-control" id="txtDrawing_Or_Higher_Office_Post_Office" placeholder="Post Office">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Phone*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtDrawing_Or_Higher_Office_Phone" placeholder="Phone">
                                        </div>
                                    </div>
									<div class="form-group row">
									 <label class="col-sm-9 col-form-label">Salary Deduction Undertaking Given (uncheck if undertaking not given)</label>
                                        <div class="col-sm-9">
									 <label class="col-sm-9 col-form-label"></label>
                                            <input class="col-sm-0 form-check-input" style="margin: 0;position: relative;" checked type="checkbox" id="checkUndertaking" >
                                        </div>
                                    </div>

 



                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                             
                                            <a onclick="Save();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <script src="assets/plugins/toastr/toastr.min.js"></script>



    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.js"></script>

    <script>
        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;
        const $searchInput = $('#txtAddress_Pincode');
        const $searchResults = $('#search-results');
        const $searchInputPrePin = $('#txtPresent_Pincode');
        const $searchResultsPrePin = $('#search-results-pre-pin');
        const $searchInputPerPin = $('#txtPermanent_Pincode');
        const $searchResultsPerPin = $('#search-results-per-pin');
        const $searchInputDOfficerPin = $('#txtDrawing_Or_Higher_Office_Pincode');
        const $searchResultsDOfficerPin = $('#search-results-dofficer-pin');

        $('#search-results').hide();
        $('#search-results-pre-pin').hide();
        $('#search-results-per-pin').hide();
        $('#search-results-dofficer-pin').hide();


        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }

        $(function () {

            $searchInput.on('input', function () {
                const query = $searchInput.val();
                fetchAutocompleteResults(query);
            });

            // Event handler for selecting a result
            $searchResults.on('click', 'li', function () {
                $("#txtAddress_Pincode").val($(this).attr("data-pincode"));
                $("#txtAddress_Post_Office").val($(this).html());
                //  const selectedResult = $(this).text();
                //  $searchInput.val(selectedResult);
                $searchResults.hide();
            });

            $searchInputPrePin.on('input', function () {
                const query = $searchInputPrePin.val();
                fetchAutocompleteResultsPrePin(query);
            });

            // Event handler for selecting a result
            $searchResultsPrePin.on('click', 'li', function () {
                $("#txtPresent_Pincode").val($(this).attr("data-pincode"));
                $("#txtPresent_Post_Office").val($(this).html());
                //  const selectedResult = $(this).text();
                //  $searchInput.val(selectedResult);
                $searchResultsPrePin.hide();
            });

            $searchInputPerPin.on('input', function () {
                const query = $searchInputPerPin.val();
                fetchAutocompleteResultsPerPin(query);
            });

            // Event handler for selecting a result
            $searchResultsPerPin.on('click', 'li', function () {
                $("#txtPermanent_Pincode").val($(this).attr("data-pincode"));
                $("#txtPermanent_Post_Office").val($(this).html());
                //  const selectedResult = $(this).text();
                //  $searchInput.val(selectedResult);
                $searchResultsPerPin.hide();
            });
            $searchInputDOfficerPin.on('input', function () {
                const query = $searchInputDOfficerPin.val();
                fetchAutocompleteResultsDOfficerPin(query);
            });

            // Event handler for selecting a result
            $searchResultsDOfficerPin.on('click', 'li', function () {
                $("#txtDrawing_Or_Higher_Office_Pincode").val($(this).attr("data-pincode"));
                $("#txtDrawing_Or_Higher_Office_Post_Office").val($(this).html());
                //  const selectedResult = $(this).text();
                //  $searchInput.val(selectedResult);
                $searchResultsDOfficerPin.hide();
            });
        })

        $(document).ready(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            // Get the current date
            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtApplication_Date').val(formattedDate);

            $("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
            $("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");
           
            //   Load_All_Districts();

            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            function getParameterByName(name, url = window.location.href) {
                name = name.replace(/[\[\]]/g, '\\$&');
                var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                    results = regex.exec(url);
                if (!results) return null;
                if (!results[2]) return '';
                return decodeURIComponent(results[2].replace(/\+/g, ' '));
            }

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }

            $('#GazettedOfficer').hide();
            $('#NPS_PRAN').hide();

            Load_All_Application_Issue_By_Id(getParameterByName("LoanappId"));
           
        });

        function fetchAutocompleteResults(query) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ pincode: query }), // If you have parameters
                url: "WebService.asmx/Select_All_PostOffices",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $searchResults.empty();
                    if (data.d.length > 0) {

                        $searchResults.show();
                    } else {
                        $searchResults.hide();
                        $("#txtAddress_Post_Office").val('');
                    }
                    $.each(data.d, function (key, value) {
                        $searchResults.append('<li data-pincode="' + value.Pincode + '">' + value.Post_office + '</li>');
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });
 

        }

        function fetchAutocompleteResultsPrePin(query) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ pincode: query }), // If you have parameters
                url: "WebService.asmx/Select_All_PostOffices",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $searchResultsPrePin.empty();
                    if (data.d.length > 0) {

                        $searchResultsPrePin.show();
                    } else {
                        $searchResultsPrePin.hide();
                        $("#txtPresent_Post_Office").val('');
                    }
                    $.each(data.d, function (key, value) {
                        $searchResultsPrePin.append('<li data-pincode="' + value.Pincode + '">' + value.Post_office + '</li>');
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });


        }
        function fetchAutocompleteResultsPerPin(query) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ pincode: query }), // If you have parameters
                url: "WebService.asmx/Select_All_PostOffices",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $searchResultsPerPin.empty();
                    if (data.d.length > 0) {

                        $searchResultsPerPin.show();
                    } else {
                        $searchResultsPerPin.hide();
                        $("#txtPermanent_Post_Office").val('');
                    }
                    $.each(data.d, function (key, value) {
                        $searchResultsPerPin.append('<li data-pincode="' + value.Pincode + '">' + value.Post_office + '</li>');
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });


        }
        function fetchAutocompleteResultsDOfficerPin(query) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ pincode: query }), // If you have parameters
                url: "WebService.asmx/Select_All_PostOffices",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $searchResultsDOfficerPin.empty();
                    if (data.d.length > 0) {

                        $searchResultsDOfficerPin.show();
                    } else {
                        $searchResultsDOfficerPin.hide();
                        $("#txtDrawing_Or_Higher_Office_Post_Office").val('');
                    }
                    $.each(data.d, function (key, value) {
                        $searchResultsDOfficerPin.append('<li data-pincode="' + value.Pincode + '">' + value.Post_office + '</li>');
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });


        }
        function Load_All_Application_Issue_By_Id(LoanappId) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Id: LoanappId }), // If you have parameters
                url: "WebService.asmx/Select_tbl_LoanApp_By_LoanAppId",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        $(".LABEL_APP_Status").text(value.chr_status);
                        var int_loanappid = value.int_loanappid;
                        var Name_Applicant = value.vchr_applname;
                        var Scheme_Id = value.int_schemeid;
                        Cast_Id = value.vchr_caste;
                        Sub_Cast_Id = value.SubCast;
                        var vchr_appreceivregno = value.vchr_appreceivregno;
                        $("#txtApplicant_Name").val(Name_Applicant);
                        $("#txtApplicationRegNo").val(vchr_appreceivregno);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Select_EmpSurety_By_Loanappid(getParameterByName("Id"))
            });
        }

        function getFormattedDate(date) {
            date = date.split('/')[2] + "-" + date.split('/')[1] + "-" + date.split('/')[0];
            return date;
        }

        var Present_Village_Id;
        var Permanent_Village_Id;
        var Present_District_Id;
        var Permanent_District_Id;
        var Present_Taluk;
        var Permanent_Taluk;
        var Present_District;
        var Permanent_District;
        var Department;

        function Select_EmpSurety_By_Loanappid(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Id: Id }), // If you have parameters
                url: "WebService.asmx/Select_tbl_empsurety_By_Id",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        $("#txtName_Of_Employee").val(value.vchr_empname);
                        $("#txtName_Of_Father").val(value.vchr_fathername);
                        $("#txtName_Of_Spouse").val(value.vchr_spousename);
                        $("#txtDesignation").val(value.vchr_empdesig);

                        $("#txtAddress_Office_Name").val(value.vchr_empoffname);
                        $("#txtAddress_Lane1").val(value.vchr_empofflane1);
                        $("#txtAddress_Lane2").val(value.vchr_empofflane2);
                        $("#txtAddress_Pincode").val(value.vchr_empoffpin);
                        $("#txtAddress_Post_Office").val(value.vchr_empoffpost);
                        $("#txtAddress_Phone").val(value.vchr_empoffphno);

                        $("#txtPresent_House_Name_Or_No").val(value.vchr_presenthsename);
                        $("#txtPresent_Lane1").val(value.vchr_presentlane1);
                        $("#txtPresent_Lane2").val(value.vchr_presentlane2);
                        $("#txtPresent_Pincode").val(value.vchr_presentpin);
                        $("#txtPresent_Post_Office").val(value.vchr_presentpost);
                        $("#txtPresent_Phone").val(value.vchr_presentphno);
                        Present_Village_Id = value.vchr_presentVillage;

                        $("#txtPermanent_House_Name_Or_No").val(value.vchr_emppermhsename);
                        $("#txtPermanent_Lane1").val(value.vchr_emppermlane1);
                        $("#txtPermanent_Lane2").val(value.vchr_emppermlane2);
                        $("#txtPermanent_Pincode").val(value.vchr_emppermpin);
                        $("#txtPermanent_Post_Office").val(value.vchr_emppermpost);
                        $('#txtPermanent_Phone').val(value.vchr_emppermphno);
                        Permanent_Village_Id = value.vchr_PermVillage;

                        $("#txtDate_Of_Birth").val(getFormattedDate(value.dte_dob));
                        $("#txtService_Entry_Date").val(getFormattedDate(value.dte_eos));
                        $("#txtDate_Of_Retirement").val(getFormattedDate(value.dte_empdateofretire));
                        $("#txtScale_Of_Pay").val(value.vchr_scaleofpay);
                        var PensionScheme = value.vchr_PensionScheme;
                        $("#dropPension_Scheme").val(PensionScheme);
                        if (PensionScheme == "NPS") {
                            $("#dropPension_Scheme").trigger('change');
                            $("#txtPRAN").val(value.vchr_PRAN);
                        }
                        $("#txtPF_No").val(value.vchr_PFNo);
                        $("#txtBasic_Pay").val(value.int_basicpay);
                        $("#txtNet_Salary").val(value.int_netsal);
                        $("#txtGross_Salary").val(value.int_grosssal);
                        $("#txtName_Of_Drawing_Officer_Or_sHigher_Office").val(value.vchr_senior_empname);
                        $("#txtDrawing_Or_Higher_Officer_Designation").val(value.vchr_senior_empdes);
                        $("#txtDrawing_Officer_Or_Higher_Office_OfficeName").val(value.vchr_senior_empoffname);
                        $("#txtDrawing_Or_Higher_Office_Lane1").val(value.vchr_senior_emplane1);
                        $("#txtDrawing_Or_Higher_Office_Lane2").val(value.vchr_senior_emplane2);
                        $("#txtDrawing_Or_Higher_Office_Pincode").val(value.vchr_senior_emppin);
                        $("#txtDrawing_Or_Higher_Office_Post_Office").val(value.vchr_senior_emppost);
                        $("#txtDrawing_Or_Higher_Office_Phone").val(value.vchr_senior_empphone);
                        var AuditNoSDONo = value.vchr_auditno;
                        if (AuditNoSDONo != "") {
                            $('#checkGazetted_Officer').prop('checked', true);
                            $("#checkGazetted_Officer").trigger('change');
                            $("#txtAuditNoSDONo").val(AuditNoSDONo);
                        }
                        else {
                            $("#txtAuditNoSDONo").val('');
                            $('#checkGazetted_Officer').prop('checked', false);
                            $("#checkGazetted_Officer").trigger('change');
                        }
                        
                        var DOSameAsSurety = value.int_sr_officer;
                        if (DOSameAsSurety == "1") {
                            $('#checkBDrawing_Officer_Same_As_Surety').prop('checked', true);
                        }
                        else {
                            $('#checkBDrawing_Officer_Same_As_Surety').prop('checked', false);
                        }

                        var Undertaking = value.int_under
                        if (Undertaking == "1") {
                            $('#checkUndertaking').prop('checked', true);
                        }
                        else {
                            $('#checkUndertaking').prop('checked', false);
                        }
                        $("#txtPEN_No_Employee_Code").val(value.vchr_pen);
                        $("#Department").val(value.vchr_depart);
                        
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Villages(Present_Village_Id, Permanent_Village_Id);
            });
        }

        function Load_All_Villages(Present_Village_Id, Permanent_Village_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Villages",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropPresent_Village').empty();
                    //$('#dropPresent_Village').append('<option value="0">Select</option>');
                    $('#dropPermanent_Village').empty();
                   // $('#dropPermanent_Village').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Village = value.Village;
                        var html = '<option value="' + Id + '">' + Village + '</option>';
                       
                        $('#dropPresent_Village').append(html);
                        $('#dropPermanent_Village').append(html);
                    });


                },
                error: function (error) {


                    alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $("#dropPresent_Village option:contains(" + Present_Village_Id + ")").attr('selected', 'selected');
                $("#dropPermanent_Village option:contains(" + Permanent_Village_Id + ")").attr('selected', 'selected');
               Present_Village_Id = $('#dropPresent_Village').val();
               Permanent_Village_Id = $('#dropPermanent_Village').val();
                Load_All_tbl_Taluk_By_Present_Village_Id(Present_Village_Id);
               Load_All_tbl_Taluk_By_Permanent_Village_Id(Permanent_Village_Id);
               Select_All_Districts_By_Present_Village_Id(Present_Village_Id);
             Select_All_Districts_By_Permanent_Village_Id(Permanent_Village_Id);
                Load_All_Departments();
            });

        }
 
        $('#dropPresent_Village').change(function () {
            var optionSelected = $(this).find("option:selected");
            var valueSelected = optionSelected.val();
           Load_All_tbl_Taluk_By_Present_Village_Id(valueSelected);
           Select_All_Districts_By_Present_Village_Id(valueSelected);
            
        });
        $('#dropPermanent_Village').change(function () {
            var optionSelected = $(this).find("option:selected");
            var valueSelected = optionSelected.val();
            Load_All_tbl_Taluk_By_Permanent_Village_Id(valueSelected);
            Select_All_Districts_By_Permanent_Village_Id(valueSelected);
        });

        function Select_All_Districts_By_Present_Village_Id(Present_Village_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts_By_Village_Id",
                data: JSON.stringify({ Village_Id: Present_Village_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    Present_District = $('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var District_Name = value.District_Name;
                        Present_District = District_Name;
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
  
                    })
                }
            }).done(function () {
                //alert(Present_District);
            });
        }
        function Select_All_Districts_By_Permanent_Village_Id(Permanent_Village_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts_By_Village_Id",
                data: JSON.stringify({ Village_Id: Permanent_Village_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    Permanent_District = $('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var DistrictName = value.District_Name;
                        Permanent_District=DistrictName;
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
  
                    })
                }
            }).done(function () {

            });
        }
        function Load_All_tbl_Taluk_By_Present_Village_Id(Present_Village_Id) {
            
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_Taluk_By_Id",
                data: JSON.stringify({ Village_Id: Present_Village_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
 
                    Present_Taluk = $('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Taluk_Name = value.Taluk;
                        Present_Taluk=Taluk_Name;
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
  
                    })
                }
            }).done(function () {
              
            });
        }
        function Load_All_tbl_Taluk_By_Permanent_Village_Id(Permanent_Village_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_Taluk_By_Id",
                data: JSON.stringify({ Village_Id: Permanent_Village_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    Permanent_Taluk = $('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Taluk_Name = value.Taluk;
                        Permanent_Taluk = Taluk_Name;
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
             
            });
        }
         
        $(document).on('click', '#chk_Same_As_Present_Address', function () {
            var ckbox = $('#chk_Same_As_Present_Address');
            if (ckbox.is(':checked')) {
                $('#txtPermanent_House_Name_Or_No').val($('#txtPresent_House_Name_Or_No').val());
                $('#txtPermanent_Lane1').val($('#txtPresent_Lane1').val());
                $('#txtPermanent_Lane2').val($('#txtPresent_Lane2').val());
                $('#txtPermanent_Pincode').val($('#txtPresent_Pincode').val());
                $('#txtPermanent_Post_Office').val($('#txtPresent_Post_Office').val());
                $('#txtPermanent_Phone').val($('#txtPresent_Phone').val());
                $('#dropPermanent_Village').val($('#dropPresent_Village').val());
                Permanent_Taluk = Present_Taluk;
                Permanent_District = Present_District;

                $('#txtPermanent_House_Name_Or_No').attr('readonly', 'readonly');
                $('#txtPermanent_Lane1').attr('readonly', 'readonly');
                $('#txtPermanent_Lane2').attr('readonly', 'readonly');
                $('#txtPermanent_Pincode').attr('readonly', 'readonly');
                $('#txtPermanent_Post_Office').attr('readonly', 'readonly');
                $('#txtPermanent_Phone').attr('readonly', 'readonly');
                $('#dropPermanent_Village').attr('disabled', 'disabled');

            }
            else {
                $('#txtPermanent_House_Name_Or_No').val('');
                $('#txtPermanent_Lane1').val('');
                $('#txtPermanent_Lane2').val('');
                $('#txtPermanent_Pincode').val('');
                $('#txtPermanent_Post_Office').val('');
                $('#txtPermanent_Phone').val('');
                $('#dropPermanent_Village').prop('selectedIndex', 0);
                $('#txtPermanent_House_Name_Or_No').removeAttr('readonly');
                $('#txtPermanent_Lane1').removeAttr('readonly');
                $('#txtPermanent_Lane2').removeAttr('readonly');
                $('#txtPermanent_Pincode').removeAttr('readonly');
                $('#txtPermanent_Post_Office').removeAttr('readonly');
                $('#txtPermanent_Phone').removeAttr('readonly');
                $('#dropPermanent_Village').removeAttr('disabled');
                var valueSelected = $('#dropPermanent_Village').val();
                Load_All_tbl_Taluk_By_Permanent_Village_Id(valueSelected);
                Select_All_Districts_By_Permanent_Village_Id(valueSelected);
            }
        });

        $(document).on('change', '#checkGazetted_Officer', function () {
            var ckboxG = $('#checkGazetted_Officer');
            if (ckboxG.is(':checked')) {
                $('#GazettedOfficer').show();
            }
            else {
                $('#GazettedOfficer').hide();
                $("#txtAuditNoSDONo").val('');
            }
        });

        $('#dropPension_Scheme').change(function () {
            var optionSelected = $(this).find("option:selected");
            var valueSelected = optionSelected.val();
            if (valueSelected == 'NPS') {
                $('#NPS_PRAN').show();
                $('#txtPRAN').val('');
            }
            else {
                $('#NPS_PRAN').hide();
                $('#txtPRAN').val('');
            }
        });
        
        function Load_All_Departments() {
           
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_Department",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropDepartment').empty();
                    $('#dropDepartment').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.int_deptid;
                        var Department = value.vchr_deptname;
                        var html = '<option value="' + Id + '">' + Department + '</option>';
                       
                            $('#dropDepartment').append(html);
                        
                    });


                },
                error: function (error) {


                    alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                var Department = $("#Department").val();
                if (Department != "") {
                    $("#dropDepartment option:contains(" + Department + ")").attr('selected', 'selected');
                    $("#Department").val('');
                }
            });

        }
         
        function Save() {
            //alert(Counter);
            var loanapp_Id = getParameterByName("LoanappId");
            var emp_Id = getParameterByName("Id");

            var ApplicationRegNo = $("#txtApplicationRegNo");
            var Name_Of_Employe = $("#txtName_Of_Employee");
            var Name_Of_Father = $("#txtName_Of_Father");
            var Name_Of_Spouse = $("#txtName_Of_Spouse");
            var Emp_Designation = $("#txtDesignation");

            var EmpOfficeName = $("#txtAddress_Office_Name");
            var EmpOfficeLane1 = $("#txtAddress_Lane1");
            var EmpOfficeLane2 = $("#txtAddress_Lane2");
            var EmpOfficePin = $("#txtAddress_Pincode");
            var EmpOfficePost = $("#txtAddress_Post_Office");
            var EmpOfficePhone = $("#txtAddress_Phone");

            var PresentHNameNo = $("#txtPresent_House_Name_Or_No");
            var PresentLane1 = $("#txtPresent_Lane1");
            var PresentLane2 = $("#txtPresent_Lane2");
            var PresentPin = $("#txtPresent_Pincode");
            var PresentPost = $("#txtPresent_Post_Office");
            var PresentPhone = $("#txtPresent_Phone");
            var PresentVillage = $("#dropPresent_Village option:selected");

            var PermanentHNameNo = $("#txtPermanent_House_Name_Or_No");
            var PermanentLane1 = $("#txtPermanent_Lane1");
            var PermanentLane2 = $("#txtPermanent_Lane2");
            var PermanentPin = $("#txtPermanent_Pincode");
            var PermanentPost = $("#txtPermanent_Post_Office");
            var PermanentPhone = $('#txtPermanent_Phone');
            var PermanentVillage = $("#dropPermanent_Village option:selected");

            var DOB = $("#txtDate_Of_Birth");
            var EOS = $("#txtService_Entry_Date");
            var DOR = $("#txtDate_Of_Retirement");
            var ScaleOfPay = $("#txtScale_Of_Pay");
            var PensionScheme = $("#dropPension_Scheme");
            var PFNo = $("#txtPF_No");
            var BasicPay = $("#txtBasic_Pay");
            var NetSalary = $("#txtNet_Salary");
            var GrossSalary = $("#txtGross_Salary");

            var SeniorEmpName = $("#txtName_Of_Drawing_Officer_Or_sHigher_Office");
            var SeniorEmpDesignation = $("#txtDrawing_Or_Higher_Officer_Designation");
            var SeniorEmpOfficeName = $("#txtDrawing_Officer_Or_Higher_Office_OfficeName");
            var SeniorEmpOfficeLane1 = $("#txtDrawing_Or_Higher_Office_Lane1");
            var SeniorEmpOfficeLane2 = $("#txtDrawing_Or_Higher_Office_Lane2");
            var SeniorEmpOfficePin = $("#txtDrawing_Or_Higher_Office_Pincode");
            var SeniorEmpOfficePost = $("#txtDrawing_Or_Higher_Office_Post_Office");
            var SeniorEmpOfficePhone = $("#txtDrawing_Or_Higher_Office_Phone");

            var ckboxG = $('#checkGazetted_Officer');
            var AuditNoSDONo = '';
            if (ckboxG.is(':checked')) {
                AuditNoSDONo = $("#txtAuditNoSDONo").val();
            }

            var ckboxDO = $('#checkBDrawing_Officer_Same_As_Surety');
            var DOSameAsSurety = 0;
            if (ckboxDO.is(':checked')) {
                DOSameAsSurety = 1;
            }

            var ckboxSDU = $('#checkUndertaking');
            var Undertaking = 0;
            if (ckboxSDU.is(':checked')) {
                Undertaking = 1;
            }

            var Department = $("#dropDepartment option:selected");
            var PENNoEmpCode = $("#txtPEN_No_Employee_Code");

            var PRAN = "";

            if ($('#dropPension_Scheme').val() == 'NPS') {
                PRAN = $("#txtPRAN").val();
            }

            var EligibleLoan = 0;

            if (GrossSalary.val() * 8 > NetSalary.val() * 15) {
                EligibleLoan = NetSalary.val() * 15;
            }
            else {
                EligibleLoan = GrossSalary.val() * 8;
            }
             

            if (EmpOfficePhone.val().trim() == "") {

                Toast.fire({
                    icon: 'error',
                    title: 'Phone No is required !'
                })
            }

            else {
                //if (EmpOfficePhone.val().trim() != "") {

                //    var values = EmpOfficePhone.val().trim().split(",");
                //    for (var i = 0; i < values.length; i++) {
                //        if (values[values.length - 1] != "") {
                //            if (!Is_Valid_Mobile_Number(values[i])) {
                //                Toast.fire({
                //                    icon: 'error',
                //                    title: 'Enter valid phone number !'
                //                })
                //                return;
                //            }
                //        }
                //    }
                //}
                $("#btnSave").css("background-color", "#000");
                $("#btnSave").attr("disabled", "disabled");
                $("#btnSave").html("Please wait !");
                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/Update_To_tbl_empsurety",
                    data: JSON.stringify({ Id: emp_Id, int_empid: emp_Id, int_loanappid: loanapp_Id, vchr_appreceivregno: ApplicationRegNo.val(), vchr_empname: Name_Of_Employe.val(), vchr_fathername: Name_Of_Father.val(), vchr_spousename: Name_Of_Spouse.val(), vchr_empdesig: Emp_Designation.val(), vchr_empoffname: EmpOfficeName.val(), vchr_empofflane1: EmpOfficeLane1.val(), vchr_empofflane2: EmpOfficeLane2.val(), vchr_empoffpost: EmpOfficePost.val(), vchr_empoffpin: EmpOfficePin.val(), vchr_empoffphno: EmpOfficePhone.val(), vchr_presenthsename: PresentHNameNo.val(), vchr_presentlane1: PresentLane1.val(), vchr_presentlane2: PresentLane2.val(), vchr_presentpost: PresentPost.val(), vchr_presentpin: PresentPin.val(), vchr_presentphno: PresentPhone.val(), vchr_emppermhsename: PermanentHNameNo.val(), vchr_emppermlane1: PermanentLane1.val(), vchr_emppermlane2: PermanentLane2.val(), vchr_emppermpost: PermanentPost.val(), vchr_emppermpin: PermanentPin.val(), vchr_emppermphno: PermanentPhone.val(), dte_dob: DOB.val(), dte_eos: EOS.val(), dte_empdateofretire: DOR.val(), int_basicpay: BasicPay.val(), int_netsal: NetSalary.val(), int_grosssal: GrossSalary.val(), vchr_senior_empname: SeniorEmpName.val(), vchr_senior_empdes: SeniorEmpDesignation.val(), vchr_senior_empoffname: SeniorEmpOfficeName.val(), vchr_senior_emplane1: SeniorEmpOfficeLane1.val(), vchr_senior_emplane2: SeniorEmpOfficeLane2.val(), vchr_senior_emppost: SeniorEmpOfficePost.val(), vchr_senior_emppin: SeniorEmpOfficePin.val(), vchr_senior_empphone: SeniorEmpOfficePhone.val(), vchr_auditno: AuditNoSDONo, vchr_depart: Department.text(), int_sr_officer: DOSameAsSurety, vchr_scale: "", dte_conf_send_date: "01-01-0001", dte_conf_rec_date: "01-01-0001", int_loanno: 0, vchr_oldno: 0, vchr_presentVillage: PresentVillage.text(), vchr_presentTaluk: Present_Taluk, vchr_presentDistrict: Present_District, vchr_PermVillage: PermanentVillage.text(), vchr_PermTaluk: Permanent_Taluk, vchr_PermDistrict: Permanent_District, vchr_empoffname1: "", vchr_empofflane11: "", vchr_empofflane21: "", vchr_empoffpost1: "", vchr_empoffpin1: 0, vchr_empoffphno1: 0, int_loanee: 0, int_under: Undertaking, vchr_Ashwas: "", vchr_pen: PENNoEmpCode.val(), vchr_PanNo: "", vchr_ITAckNo: 0, vchr_Aadhar: "", int_personal: 0, int_eligibleloan: EligibleLoan, vchr_scaleofpay: ScaleOfPay.val(), vchr_PensionScheme: PensionScheme.val(), vchr_PRAN: PRAN, vchr_PFNo: PFNo.val() }), // If you have parameters
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {

                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        // Handle AJAX error
                          alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                        })

                    }

                }).done(function () {

                    //  Update_Status();
                    $("#btnSave").css("background-color", "#c8328b");
                    $("#btnSave").removeAttr("disabled");
                    $("#btnSave").html("Submit");
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Guarantor Employee Details Successfully Updated !',
                        allowOutsideClick: false,
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked

                            location.href = 'Surety_Modification_List.aspx';
                            // Your code here
                        }
                    });

                });


            }




        }


        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }


        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }




    </script>
</asp:Content>


