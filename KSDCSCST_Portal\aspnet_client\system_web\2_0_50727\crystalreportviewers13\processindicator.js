if(typeof(dojo)!="undefined"){dojo.provide("MochiKit.Base")}if(typeof(MochiKit)=="undefined"){MochiKit={}}if(typeof(MochiKit.Base)=="undefined"){MochiKit.Base={}}MochiKit.Base.VERSION="1.4";MochiKit.Base.NAME="MochiKit.Base";MochiKit.Base.update=function(b,d){if(b===null){b={}}for(var c=1;c<arguments.length;c++){var e=arguments[c];if(typeof(e)!="undefined"&&e!==null){for(var a in e){b[a]=e[a]}}}return b};MochiKit.Base.update(MochiKit.Base,{__repr__:function(){return"["+this.NAME+" "+this.VERSION+"]"},toString:function(){return this.__repr__()},camelize:function(b){var a=b.split("-");var d=a[0];for(var c=1;c<a.length;c++){d+=a[c].charAt(0).toUpperCase()+a[c].substring(1)}return d},counter:function(a){if(arguments.length===0){a=1}return function(){return a++}},clone:function(b){var a=arguments.callee;if(arguments.length==1){a.prototype=b;return new a()}},extend:function(b,e,d){if(!d){d=0}if(e){var a=e.length;if(typeof(a)!="number"){if(typeof(MochiKit.Iter)!="undefined"){e=MochiKit.Iter.list(e);a=e.length}else{throw new TypeError("Argument not an array-like and MochiKit.Iter not present")}}if(!b){b=[]}for(var c=d;c<a;c++){b.push(e[c])}}return b},updatetree:function(c,e){if(c===null){c={}}for(var d=1;d<arguments.length;d++){var f=arguments[d];if(typeof(f)!="undefined"&&f!==null){for(var b in f){var a=f[b];if(typeof(c[b])=="object"&&typeof(a)=="object"){arguments.callee(c[b],a)}else{c[b]=a}}}}return c},setdefault:function(b,d){if(b===null){b={}}for(var c=1;c<arguments.length;c++){var e=arguments[c];for(var a in e){if(!(a in b)){b[a]=e[a]}}}return b},_newNamedError:function(b,a,c){c.prototype=new MochiKit.Base.NamedError(b.NAME+"."+a);b[a]=c},operator:{identity:function(b){return b}},forwardCall:function(a){return function(){return this[a].apply(this,arguments)}},typeMatcher:function(){var b={};for(var a=0;a<arguments.length;a++){var c=arguments[a];b[c]=c}return function(){for(var d=0;d<arguments.length;d++){if(!(typeof(arguments[d]) in b)){return false}}return true}},isNull:function(){for(var a=0;a<arguments.length;a++){if(arguments[a]!==null){return false}}return true},isUndefinedOrNull:function(){for(var a=0;a<arguments.length;a++){var b=arguments[a];if(!(typeof(b)=="undefined"||b===null)){return false}}return true},isEmpty:function(a){return !MochiKit.Base.isNotEmpty.apply(this,arguments)},isNotEmpty:function(b){for(var a=0;a<arguments.length;a++){var c=arguments[a];if(!(c&&c.length)){return false}}return true},isArrayLike:function(){for(var a=0;a<arguments.length;a++){var c=arguments[a];var b=typeof(c);if((b!="object"&&!(b=="function"&&typeof(c.item)=="function"))||c===null||typeof(c.length)!="number"||c.nodeType===3){return false}}return true},isDateLike:function(){for(var a=0;a<arguments.length;a++){var b=arguments[a];if(typeof(b)!="object"||b===null||typeof(b.getTime)!="function"){return false}}return true},xmap:function(b){if(b===null){return MochiKit.Base.extend(null,arguments,1)}var c=[];for(var a=1;a<arguments.length;a++){c.push(b(arguments[a]))}return c},map:function(p,f){var b=MochiKit.Base;var k=MochiKit.Iter;var q=b.isArrayLike;if(arguments.length<=2){if(!q(f)){if(k){f=k.list(f);if(p===null){return f}}else{throw new TypeError("Argument not an array-like and MochiKit.Iter not present")}}if(p===null){return b.extend(null,f)}var g=[];for(var e=0;e<f.length;e++){g.push(p(f[e]))}return g}else{if(p===null){p=Array}var a=null;for(e=1;e<arguments.length;e++){if(!q(arguments[e])){if(k){return k.list(k.imap.apply(null,arguments))}else{throw new TypeError("Argument not an array-like and MochiKit.Iter not present")}}var c=arguments[e].length;if(a===null||a>c){a=c}}g=[];for(e=0;e<a;e++){var n=[];for(var d=1;d<arguments.length;d++){n.push(arguments[d][e])}g.push(p.apply(this,n))}return g}},xfilter:function(b){var c=[];if(b===null){b=MochiKit.Base.operator.truth}for(var a=1;a<arguments.length;a++){var d=arguments[a];if(b(d)){c.push(d)}}return c},filter:function(e,b,c){var f=[];var a=MochiKit.Base;if(!a.isArrayLike(b)){if(MochiKit.Iter){b=MochiKit.Iter.list(b)}else{throw new TypeError("Argument not an array-like and MochiKit.Iter not present")}}if(e===null){e=a.operator.truth}if(typeof(Array.prototype.filter)=="function"){return Array.prototype.filter.call(b,e,c)}else{if(typeof(c)=="undefined"||c===null){for(var d=0;d<b.length;d++){var g=b[d];if(e(g)){f.push(g)}}}else{for(d=0;d<b.length;d++){g=b[d];if(e.call(c,g)){f.push(g)}}}}return f},_wrapDumbFunction:function(func){return function(){switch(arguments.length){case 0:return func();case 1:return func(arguments[0]);case 2:return func(arguments[0],arguments[1]);case 3:return func(arguments[0],arguments[1],arguments[2])}var args=[];for(var i=0;i<arguments.length;i++){args.push("arguments["+i+"]")}return eval("(func("+args.join(",")+"))")}},methodcaller:function(b){var a=MochiKit.Base.extend(null,arguments,1);if(typeof(b)=="function"){return function(c){return b.apply(c,a)}}else{return function(c){return c[b].apply(c,a)}}},method:function(b,c){var a=MochiKit.Base;return a.bind.apply(this,a.extend([c,b],arguments,2))},compose:function(b,f){var e=[];var a=MochiKit.Base;if(arguments.length===0){throw new TypeError("compose() requires at least one argument")}for(var c=0;c<arguments.length;c++){var d=arguments[c];if(typeof(d)!="function"){throw new TypeError(repr(d)+" is not a function")}e.push(d)}return function(){var g=arguments;for(var j=e.length-1;j>=0;j--){g=[e[j].apply(this,g)]}return g[0]}},bind:function(e,c){if(typeof(e)=="string"){e=c[e]}var d=e.im_func;var g=e.im_preargs;var b=e.im_self;var a=MochiKit.Base;if(typeof(e)=="function"&&typeof(e.apply)=="undefined"){e=a._wrapDumbFunction(e)}if(typeof(d)!="function"){d=e}if(typeof(c)!="undefined"){b=c}if(typeof(g)=="undefined"){g=[]}else{g=g.slice()}a.extend(g,arguments,2);var f=function(){var j=arguments;var k=arguments.callee;if(k.im_preargs.length>0){j=a.concat(k.im_preargs,j)}var i=k.im_self;if(!i){i=this}return k.im_func.apply(i,j)};f.im_self=b;f.im_func=d;f.im_preargs=g;return f},bindMethods:function(b){var d=MochiKit.Base.bind;for(var a in b){var c=b[a];if(typeof(c)=="function"){b[a]=d(c,b)}}},registerComparator:function(c,b,a,d){MochiKit.Base.comparatorRegistry.register(c,b,a,d)},_primitives:{"boolean":true,string:true,number:true},compare:function(j,d){if(j==d){return 0}var i=(typeof(j)=="undefined"||j===null);var k=(typeof(d)=="undefined"||d===null);if(i&&k){return 0}else{if(i){return -1}else{if(k){return 1}}}var c=MochiKit.Base;var g=c._primitives;if(!(typeof(j) in g&&typeof(d) in g)){try{return c.comparatorRegistry.match(j,d)}catch(l){if(l!=c.NotFound){throw l}}}if(j<d){return -1}else{if(j>d){return 1}}var f=c.repr;throw new TypeError(f(j)+" and "+f(d)+" can not be compared")},compareDateLike:function(d,c){return MochiKit.Base.compare(d.getTime(),c.getTime())},compareArrayLike:function(d,c){var j=MochiKit.Base.compare;var g=d.length;var k=0;if(g>c.length){k=1;g=c.length}else{if(g<c.length){k=-1}}for(var e=0;e<g;e++){var f=j(d[e],c[e]);if(f){return f}}return k},registerRepr:function(b,a,d,c){MochiKit.Base.reprRegistry.register(b,a,d,c)},repr:function(d){if(typeof(d)=="undefined"){return"undefined"}else{if(d===null){return"null"}}try{if(typeof(d.__repr__)=="function"){return d.__repr__()}else{if(typeof(d.repr)=="function"&&d.repr!=arguments.callee){return d.repr()}}return MochiKit.Base.reprRegistry.match(d)}catch(b){if(typeof(d.NAME)=="string"&&(d.toString==Function.prototype.toString||d.toString==Object.prototype.toString)){return d.NAME}}try{var c=(d+"")}catch(b){return"["+typeof(d)+"]"}if(typeof(d)=="function"){d=c.replace(/^\s+/,"");var a=d.indexOf("{");if(a!=-1){d=d.substr(0,a)+"{...}"}}return c},reprArrayLike:function(b){var a=MochiKit.Base;return"["+a.map(a.repr,b).join(", ")+"]"},reprString:function(a){return('"'+a.replace(/(["\\])/g,"\\$1")+'"').replace(/[\f]/g,"\\f").replace(/[\b]/g,"\\b").replace(/[\n]/g,"\\n").replace(/[\t]/g,"\\t").replace(/[\r]/g,"\\r")},reprNumber:function(a){return a+""},registerJSON:function(b,a,d,c){MochiKit.Base.jsonRegistry.register(b,a,d,c)},evalJSON:function(){return eval("("+arguments[0]+")")},serializeJSON:function(a){var q=typeof(a);if(q=="undefined"){return"undefined"}else{if(q=="number"||q=="boolean"){return a+""}else{if(a===null){return"null"}}}var c=MochiKit.Base;var r=c.reprString;if(q=="string"){return r(a)}var p=arguments.callee;var f;if(typeof(a.__json__)=="function"){f=a.__json__();if(a!==f){return p(f)}}if(typeof(a.json)=="function"){f=a.json();if(a!==f){return p(f)}}if(q!="function"&&typeof(a.length)=="number"){var n=[];for(var j=0;j<a.length;j++){var b=p(a[j]);if(typeof(b)!="string"){b="undefined"}n.push(b)}return"["+n.join(", ")+"]"}try{f=c.jsonRegistry.match(a);if(a!==f){return p(f)}}catch(l){if(l!=c.NotFound){throw l}}if(q=="function"){return null}n=[];for(var d in a){var g;if(typeof(d)=="number"){g='"'+d+'"'}else{if(typeof(d)=="string"){g=r(d)}else{continue}}b=p(a[d]);if(typeof(b)!="string"){continue}n.push(g+":"+b)}return"{"+n.join(", ")+"}"},objEqual:function(d,c){return(MochiKit.Base.compare(d,c)===0)},arrayEqual:function(b,a){if(b.length!=a.length){return false}return(MochiKit.Base.compare(b,a)===0)},concat:function(){var b=[];var c=MochiKit.Base.extend;for(var a=0;a<arguments.length;a++){c(b,arguments[a])}return b},keyComparator:function(b){var a=MochiKit.Base;var d=a.compare;if(arguments.length==1){return function(f,e){return d(f[b],e[b])}}var c=a.extend(null,arguments);return function(f,e){var k=0;for(var j=0;(k===0)&&(j<c.length);j++){var g=c[j];k=d(f[g],e[g])}return k}},reverseKeyComparator:function(b){var a=MochiKit.Base.keyComparator.apply(this,arguments);return function(d,c){return a(c,d)}},partial:function(b){var a=MochiKit.Base;return a.bind.apply(this,a.extend([b,undefined],arguments,1))},listMinMax:function(f,a){if(a.length===0){return null}var e=a[0];var c=MochiKit.Base.compare;for(var b=1;b<a.length;b++){var d=a[b];if(c(d,e)==f){e=d}}return e},objMax:function(){return MochiKit.Base.listMinMax(1,arguments)},objMin:function(){return MochiKit.Base.listMinMax(-1,arguments)},findIdentical:function(a,d,e,b){if(typeof(b)=="undefined"||b===null){b=a.length}if(typeof(e)=="undefined"||e===null){e=0}for(var c=e;c<b;c++){if(a[c]===d){return c}}return -1},mean:function(){var d=0;var a=MochiKit.Base;var b=a.extend(null,arguments);var e=b.length;while(b.length){var f=b.shift();if(f&&typeof(f)=="object"&&typeof(f.length)=="number"){e+=f.length-1;for(var c=f.length-1;c>=0;c--){d+=f[c]}}else{d+=f}}if(e<=0){throw new TypeError("mean() requires at least one argument")}return d/e},median:function(){var b=MochiKit.Base.flattenArguments(arguments);if(b.length===0){throw new TypeError("median() requires at least one argument")}b.sort(compare);if(b.length%2==0){var a=b.length/2;return(b[a]+b[a-1])/2}else{return b[(b.length-1)/2]}},findValue:function(a,e,f,b){if(typeof(b)=="undefined"||b===null){b=a.length}if(typeof(f)=="undefined"||f===null){f=0}var d=MochiKit.Base.compare;for(var c=f;c<b;c++){if(d(a[c],e)===0){return c}}return -1},nodeWalk:function(c,d){var a=[c];var e=MochiKit.Base.extend;while(a.length){var b=d(a.shift());if(b){e(a,b)}}},nameFunctions:function(b){var c=b.NAME;if(typeof(c)=="undefined"){c=""}else{c=c+"."}for(var a in b){var f=b[a];if(typeof(f)=="function"&&typeof(f.NAME)=="undefined"){try{f.NAME=c+a}catch(d){}}}},queryString:function(j,l){if(typeof(MochiKit.DOM)!="undefined"&&arguments.length==1&&(typeof(j)=="string"||(typeof(j.nodeType)!="undefined"&&j.nodeType>0))){var g=MochiKit.DOM.formContents(j);j=g[0];l=g[1]}else{if(arguments.length==1){var a=j;j=[];l=[];for(var b in a){var m=a[b];if(typeof(m)!="function"){j.push(b);l.push(m)}}}}var e=[];var f=Math.min(j.length,l.length);var c=MochiKit.Base.urlEncode;for(var d=0;d<f;d++){m=l[d];if(typeof(m)!="undefined"&&m!==null){e.push(c(j[d])+"="+c(m))}}return e.join("&")},parseQueryString:function(j,k){var c=j.replace(/\+/g,"%20").split("&");var d={};var a;if(typeof(decodeURIComponent)!="undefined"){a=decodeURIComponent}else{a=unescape}if(k){for(var f=0;f<c.length;f++){var e=c[f].split("=");var b=a(e[0]);var g=d[b];if(!(g instanceof Array)){g=[];d[b]=g}g.push(a(e[1]))}}else{for(f=0;f<c.length;f++){e=c[f].split("=");d[a(e[0])]=a(e[1])}}return d}});MochiKit.Base.AdapterRegistry=function(){this.pairs=[]};MochiKit.Base.AdapterRegistry.prototype={register:function(b,a,d,c){if(c){this.pairs.unshift([b,a,d])}else{this.pairs.push([b,a,d])}},match:function(){for(var a=0;a<this.pairs.length;a++){var b=this.pairs[a];if(b[1].apply(this,arguments)){return b[2].apply(this,arguments)}}throw MochiKit.Base.NotFound},unregister:function(a){for(var b=0;b<this.pairs.length;b++){var c=this.pairs[b];if(c[0]==a){this.pairs.splice(b,1);return true}}return false}};MochiKit.Base.EXPORT=["flattenArray","noop","camelize","counter","clone","extend","update","updatetree","setdefault","keys","items","NamedError","operator","forwardCall","itemgetter","typeMatcher","isCallable","isUndefined","isUndefinedOrNull","isNull","isEmpty","isNotEmpty","isArrayLike","isDateLike","xmap","map","xfilter","filter","methodcaller","compose","bind","bindMethods","NotFound","AdapterRegistry","registerComparator","compare","registerRepr","repr","objEqual","arrayEqual","concat","keyComparator","reverseKeyComparator","partial","merge","listMinMax","listMax","listMin","objMax","objMin","nodeWalk","zip","urlEncode","queryString","serializeJSON","registerJSON","evalJSON","parseQueryString","findValue","findIdentical","flattenArguments","method","average","mean","median"];MochiKit.Base.EXPORT_OK=["nameFunctions","comparatorRegistry","reprRegistry","jsonRegistry","compareDateLike","compareArrayLike","reprArrayLike","reprString","reprNumber"];MochiKit.Base._exportSymbols=function(d,b){if(typeof(MochiKit.__export__)=="undefined"){MochiKit.__export__=(MochiKit.__compat__||(typeof(JSAN)=="undefined"&&typeof(dojo)=="undefined"))}if(!MochiKit.__export__){return}var c=b.EXPORT_TAGS[":all"];for(var a=0;a<c.length;a++){d[c[a]]=b[c[a]]}};MochiKit.Base.__new__=function(){var a=this;a.noop=a.operator.identity;a.forward=a.forwardCall;a.find=a.findValue;if(typeof(encodeURIComponent)!="undefined"){a.urlEncode=function(c){return encodeURIComponent(c).replace(/\'/g,"%27")}}else{a.urlEncode=function(c){return escape(c).replace(/\+/g,"%2B").replace(/\"/g,"%22").rval.replace(/\'/g,"%27")}}a.NamedError=function(c){this.message=c;this.name=c};a.NamedError.prototype=new Error();a.update(a.NamedError.prototype,{repr:function(){if(this.message&&this.message!=this.name){return this.name+"("+a.repr(this.message)+")"}else{return this.name+"()"}},toString:a.forwardCall("repr")});a.NotFound=new a.NamedError("MochiKit.Base.NotFound");a.listMax=a.partial(a.listMinMax,1);a.listMin=a.partial(a.listMinMax,-1);a.isCallable=a.typeMatcher("function");a.isUndefined=a.typeMatcher("undefined");a.merge=a.partial(a.update,null);a.zip=a.partial(a.map,null);a.average=a.mean;a.comparatorRegistry=new a.AdapterRegistry();a.registerComparator("dateLike",a.isDateLike,a.compareDateLike);a.registerComparator("arrayLike",a.isArrayLike,a.compareArrayLike);a.reprRegistry=new a.AdapterRegistry();a.registerRepr("arrayLike",a.isArrayLike,a.reprArrayLike);a.registerRepr("string",a.typeMatcher("string"),a.reprString);a.registerRepr("numbers",a.typeMatcher("number","boolean"),a.reprNumber);a.jsonRegistry=new a.AdapterRegistry();var b=a.concat(a.EXPORT,a.EXPORT_OK);a.EXPORT_TAGS={":common":a.concat(a.EXPORT_OK),":all":b};a.nameFunctions(this)};MochiKit.Base.__new__();if(MochiKit.__export__){compare=MochiKit.Base.compare}MochiKit.Base._exportSymbols(this,MochiKit.Base);_userAgent=navigator.userAgent?navigator.userAgent.toLowerCase():null;_ie=(document.all!=null)?true:false;_dom=(document.getElementById!=null)?true:false;_isQuirksMode=(document.compatMode!="CSS1Compat");_dtd4=!_ie||(document.compatMode!="BackCompat");_show="visible";_hide="hidden";_hand=_ie?"hand":"pointer";_appVer=navigator.appVersion.toLowerCase();_webKit=(_userAgent.indexOf("safari")>=0)||(_userAgent.indexOf("applewebkit")>=0);_mac=(_appVer.indexOf("macintosh")>=0)||(_appVer.indexOf("macos")>=0);_opera=(_userAgent.indexOf("opera")!=-1);_userAgent=navigator.userAgent?navigator.userAgent.toLowerCase():null;_ctrl=0;_shift=1;_alt=2;var docMode=document.documentMode;var _ie6Up=(docMode>=6);var _ie8Up=(docMode>=8);var _ie10Up=(docMode>=10);var _ie11Up=(docMode>=11);_moz=_dom&&!_ie&&!_ie11Up;_saf=_moz&&(_userAgent.indexOf("safari")>=0);_small=(screen.height<=600);_curDoc=document;_curWin=self;_tooltipWin=self;_tooltipDx=0;_tooltipDy=0;_codeWinName="_CW";_leftBtn=(_ie||_saf)?1:0;_preloadArr=new Array;_widgets=new Array;_resizeW=_ie6Up?"col-resize":"E-resize";_resizeH=_ie6Up?"row-resize":"S-resize";_ddData=new Array;_dontNeedEncoding=null;_thex=null;_defaultButtonWidth=60;function initDom(d,c,e,b,a){_skin=d;_lang=e;_style=c;if(b){_curWin=b;_curDoc=b.document}_tooltipWin=_curWin;if(a){_codeWinName="_CW"+a}_curWin[_codeWinName]=self}function styleSheet(){includeCSS("style")}function isLayerDisplayed(a){var b=a?a.style:null;if(b){if(b.display=="none"||b.visibility=="hidden"){return false}else{var c=a.parentNode;if(c!=null){return isLayerDisplayed(c)}else{return true}}}else{return true}}function safeSetFocus(a){if(a&&a.focus&&isLayerDisplayed(a)){a.focus()}}function newWidget(b){var a=new Object;a.id=b;a.layer=null;a.css=null;a.getHTML=Widget_getHTML;a.beginHTML=Widget_getHTML;a.endHTML=Widget_getHTML;a.write=Widget_write;a.begin=Widget_begin;a.end=Widget_end;a.init=Widget_init;a.move=Widget_move;a.resize=Widget_resize;a.setBgColor=Widget_setBgColor;a.show=Widget_show;a.getWidth=Widget_getWidth;a.getHeight=Widget_getHeight;a.setHTML=Widget_setHTML;a.setDisabled=Widget_setDisabled;a.focus=Widget_focus;a.setDisplay=Widget_setDisplay;a.isDisplayed=Widget_isDisplayed;a.appendHTML=Widget_appendHTML;a.setTooltip=Widget_setTooltip;a.initialized=Widget_initialized;a.widx=_widgets.length;_widgets[a.widx]=a;return a}function new_Widget(a){return newWidget(a.id)}function getEvent(b,a){if(_ie&&(b==null)){b=a?a.event:_curWin.event}return b}function Widget_param(c,b,a){var d=c?c[b]:null;return d==null?a:d}function Widget_appendHTML(){append(_curDoc.body,this.getHTML())}function Widget_getHTML(){return""}function Widget_write(a){_curDoc.write(this.getHTML(a))}function Widget_begin(){_curDoc.write(this.beginHTML())}function Widget_end(){_curDoc.write(this.endHTML())}function Widget_init(){var a=this;a.layer=getLayer(a.id);a.css=a.layer.style;a.layer._widget=a.widx;if(a.initialHTML){a.setHTML(a.initialHTML)}}function Widget_move(a,d){var b=this.css;if(a!=null){if(_moz){b.left=""+a+"px"}else{b.pixelLeft=a}}if(d!=null){if(_moz){b.top=""+d+"px"}else{b.pixelTop=d}}}function Widget_focus(){safeSetFocus(this.layer)}function Widget_setBgColor(a){this.css.backgroundColor=a}function Widget_show(a){this.css.visibility=a?_show:_hide}function Widget_getWidth(){return this.layer.offsetWidth}function Widget_getHeight(){return this.layer.offsetHeight}function Widget_setHTML(a){var b=this;if(b.layer){b.layer.innerHTML=a}else{b.initialHTML=a}}function Widget_setDisplay(a){if(this.css){this.css.display=a?"":"none"}}function Widget_isDisplayed(){if(this.css.display=="none"){return false}else{return true}}function Widget_setDisabled(a){if(this.layer){this.layer.disabled=a}}function Widget_resize(a,b){if(a!=null){this.css.width=""+(Math.max(0,a))+"px"}if(b!=null){this.css.height=""+(Math.max(0,b))+"px"}}function Widget_setTooltip(a){this.layer.title=a}function Widget_initialized(){return this.layer!=null}function newGrabberWidget(a,d,g,f,i,e,c,b,j){o=newWidget(a);o.resizeCB=d;o.x=g;o.y=f;o.w=i;o.h=e;o.dx=0;o.dy=0;o.min=null;o.max=null;o.isHori=c;o.preloaded=new Image;o.preloaded.src=_skin+"../resizepattern.gif";o.buttonCB=b;o.allowGrab=true;o.collapsed=false;o.isFromButton=false;o.showGrab=GrabberWidget_showGrab;o.setCollapsed=GrabberWidget_setCollapsed;o.tooltipButton=j;o.getHTML=GrabberWidget_getHTML;o.enableGrab=GrabberWidget_enableGrab;o.setMinMax=GrabberWidget_setMinMax;if(window._allGrabbers==null){window._allGrabbers=new Array}o.index=_allGrabbers.length;_allGrabbers[o.index]=o;o.buttonLyr=null;o.setButtonImage=GrabberWidget_setButtonImage;o.getImgOffset=GrabberWidget_getImgOffset;return o}function GrabberWidget_setCollapsed(b,a){this.collapsed=b;this.setButtonImage(false,a)}function GrabberWidget_getImgOffset(a){var b=this;if(b.isHori){b.dx=(b.collapsed?12:0)+(a?6:0);b.dy=0}else{b.dy=(b.collapsed?12:0)+(a?6:0);b.dx=0}}function GrabberWidget_setButtonImage(a,b){var c=this;c.getImgOffset(a);c.tooltipButton=b;if(c.layer){if(c.buttonLyr==null){c.buttonLyr=getLayer("grabImg_"+c.id)}if(c.buttonLyr){changeSimpleOffset(c.buttonLyr,c.dx,c.dy,null,b)}}}function GrabberWidget_enableGrab(a){var b=this;b.allowGrab=a;if(b.css){b.css.cursor=b.allowGrab?(b.isHori?_resizeW:_resizeH):"default"}}function GrabberWidget_getHTML(){var b=this;var a=b.isHori?_resizeW:_resizeH;var d='onselectstart="return false" ondragstart="return false" onmousedown="'+_codeWinName+".GrabberWidget_down(event,'"+b.index+"',this);return false;\"";var c=_ie?('<img onselectstart="return false" ondragstart="return false" onmousedown="'+_codeWinName+'.eventCancelBubble(event)" border="0" hspace="0" vspace="0" src="'+_skin+'../transp.gif" id="modal_'+b.id+'" style="z-index:10000;display:none;position:absolute;top:0px;left:0px;width:1px;height:1px;cursor:'+a+'">'):('<div onselectstart="return false" ondragstart="return false" onmousedown="'+_codeWinName+'.eventCancelBubble(event)" border="0" hspace="0" vspace="0" id="modal_'+b.id+'" style="z-index:10000;display:none;position:absolute;top:0px;left:0px;width:1px;height:1px;cursor:'+a+'"></div>');return getBGIframe("grabIframe_"+b.id)+c+'<table cellpadding="0" cellspacing="0" border="0" '+d+' id="'+b.id+'" style="overflow:hidden;position:absolute;left:'+b.x+"px;top:"+b.y+"px;width:"+b.w+"px;height:"+b.h+"px;cursor:"+a+'"><tr><td></td></tr></table>'}function GrabberWidget_setMinMax(b,a){this.min=b;this.max=a}function GrabberWidget_button(e,index,lyr){var o=_allGrabbers[index];o.isFromButton=true;lyr.onmouseup=eval("_curWin."+_codeWinName+".GrabberWidget_buttonup")}function GrabberWidget_buttonover(c,b,a){var d=_allGrabbers[b];d.setButtonImage(true)}function GrabberWidget_buttonout(c,b,a){var d=_allGrabbers[b];d.setButtonImage(false)}function GrabberWidget_buttonup(a){GrabberWidget_up(a)}function GrabberWidget_showGrab(){var d=this,c=d.mod,e=d.iframe,b=d.layer.style,a=c.style;e.setDisplay(true)}function GrabberWidget_down(e,index,lyr){var o=_allGrabbers[index];window._theGrabber=o;if(o.mod==null){o.mod=getLayer("modal_"+o.id);o.iframe=newWidget("grabIframe_"+o.id);o.iframe.init()}o.mod.onmousemove=eval("_curWin."+_codeWinName+".GrabberWidget_move");o.mod.onmouseup=eval("_curWin."+_codeWinName+".GrabberWidget_up");o.grabStartPosx=parseInt(lyr.style.left);o.grabStartPosy=parseInt(lyr.style.top);o.grabStartx=eventGetX(e);o.grabStarty=eventGetY(e);var mod=o.mod,ifr=o.iframe,stl=o.layer.style,st=mod.style;stl.backgroundImage="url('"+_skin+"../resizepattern.gif')";o.prevZ=stl.zIndex;stl.zIndex=9999;ifr.css.zIndex=9998;st.width="100%";st.height="100%";mod.style.display="block";var p=getPos(o.layer);ifr.move(p.x,p.y);ifr.resize(o.getWidth(),o.getHeight());if(!o.isFromButton){o.showGrab()}return false}function GrabberWidget_move(g){var c=_theGrabber,f=c.layer,j=c.mod;if(c.isFromButton){if(c.isHori){var k=eventGetX(g),d=c.grabStartx;if((k<d-3)||(k>d+3)){c.isFromButton=false}}else{var a=eventGetY(g),b=c.grabStarty;if((i<b-3)||(i>b+3)){c.isFromButton=false}}if(!c.isFromButton){c.showGrab()}}if(!c.isFromButton){if(c.allowGrab){var k=c.isHori?Math.max(0,c.grabStartPosx-c.grabStartx+eventGetX(g)):null;var i=c.isHori?null:Math.max(0,c.grabStartPosy-c.grabStarty+eventGetY(g));if(c.isHori){if(c.min!=null){k=Math.max(k,c.min)}if(c.max!=null){k=Math.min(k,c.max)}}else{if(c.min!=null){i=Math.max(i,c.min)}if(c.max!=null){i=Math.min(i,c.max)}}eventCancelBubble(g);c.move(k,i);getPos(c.layer);if(c.buttonCB){var l=c.buttonLyr.style;if(l.display!="none"){l.display="none"}}c.iframe.move(k,i)}}}function GrabberWidget_up(f){var g=_theGrabber,b=g.layer,d=g.mod,c=b.style;c.backgroundImage="";c.zIndex=g.prevZ;var i=g.iframe;i.move(-100,-100);i.resize(1,1);i.setDisplay(false);eventCancelBubble(f);var a=d.style;a.display="none";a.width="0px";a.height="0px";if(g.buttonCB){g.buttonLyr.style.display=""}if(g&&(g.isFromButton)){if(g.buttonCB){g.buttonCB()}g.isFromButton=false}if(g.allowGrab&&(!g.isFromButton)){if(g.resizeCB){g.resizeCB(parseInt(b.style.left),parseInt(b.style.top))}}}function newButtonWidget(c,m,i,d,f,s,g,j,b,n,l,r,p,k,a,q){var e=newWidget(c);e.label=m;e.cb=i;e.width=d;e.hlp=f;e.tooltip=s;e.tabIndex=g;e.isGray=false;e.isDefault=false;e.txt=null;e.icn=null;e.margin=j?j:0;e.extraStyle="";e.isDelayCallback=true;if(b){e.url=b;e.w=n;e.h=l;e.dx=r;e.dy=p;e.disDx=(a!=null)?a:r;e.disDy=(q!=null)?q:p;e.imgRight=k?true:false}e.getHTML=ButtonWidget_getHTML;e.setDisabled=ButtonWidget_setDisabled;e.setText=ButtonWidget_setText;e.changeImg=ButtonWidget_changeImg;e.oldInit=e.init;e.init=ButtonWidget_init;e.isDisabled=ButtonWidget_isDisabled;e.setDefaultButton=ButtonWidget_setDefaultButton;e.executeCB=ButtonWidget_executeCB;e.setTooltip=ButtonWidget_setTooltip;e.setDelayCallback=ButtonWidget_setDelayCallback;e.instIndex=ButtonWidget_currInst;ButtonWidget_inst[ButtonWidget_currInst++]=e;return e}ButtonWidget_inst=new Array;ButtonWidget_currInst=0;function ButtonWidget_getHTML(){with(this){var clk=_codeWinName+".ButtonWidget_clickCB("+this.instIndex+');return false;"';var clcbs='onclick="'+clk+'" ';if(_ie){clcbs+='ondblclick="'+clk+'" '}var isDefaultSty=(this.isDefault&&!this.isGray);clcbs+='onkeydown=" return '+_codeWinName+".ButtonWidget_keydownCB(event,"+this.instIndex+');" ';var url1=_skin+"button.gif",addPar=' style="'+extraStyle+"cursor:"+_hand+";margin-left:"+margin+"px; margin-right:"+margin+'px; "'+clcbs+" ",tip=attr("title",tooltip),idText="theBttn"+id,idIcon="theBttnIcon"+id;var bg=backImgOffset(url1,0,isDefaultSty?105:42);var lnkB="<a "+attr("id",idText)+" "+tip+" "+attr("tabindex",tabIndex)+' href="javascript:void(0)" class="wizbutton" role="button">';var l=(label!=null);var im=(this.url?('<td align="'+(l?(this.imgRight?"right":"left"):"center")+'" style="'+bg+'" width="'+(!l&&(width!=null)?width+6:w+6)+'">'+(l?"":lnkB)+simpleImgOffset(url,w,h,this.isGray?disDs:dx,this.isGray?disDy:dy,idIcon,null,(l?"":tooltip),"cursor:"+_hand)+(l?"":"</a>")+"</td>"):"");return'<table onmouseover="return true" '+attr("id",id)+" "+addPar+' border="0" cellspacing="0" cellpadding="0"><tr valign="middle"><td height="21" width="5" style="'+backImgOffset(url1,0,isDefaultSty?63:0)+'"></td>'+(this.imgRight?"":im)+(l?("<td "+attr("width",width)+attr("id","theBttnCenterImg"+id)+' align="center" class="'+(this.isGray?"wizbuttongray":"wizbutton")+'" style="padding-left:3px;padding-right:3px;'+bg+'"><nobr>'+lnkB+label+"</a></nobr></td>"):"")+(this.imgRight?im:"")+'<td height="21" width="5" style="'+backImgOffset(url1,0,isDefaultSty?84:21)+'"></td></tr></table>'}}function ButtonWidget_setDelayCallback(a){this.isDelayCallback=(a==true)}function ButtonWidget_setDisabled(g){var f=this,e=g?"default":_hand;f.isGray=g;if(f.layer){var b=g?"wizbuttongray":"wizbutton";if(f.txt.className!=b){f.txt.className=b;f.txt.style.cursor=e;f.css.cursor=e;if(f.icn){changeSimpleOffset(f.icn,f.isGray?f.disDx:f.dx,f.isGray?f.disDy:f.dy);f.icn.style.cursor=e}if(f.isDefault){var a=!g,c=_skin+"button.gif";changeSimpleOffset(f.leftImg,0,a?63:0,c);changeOffset(f.centerImg,0,a?105:42,c);changeSimpleOffset(f.rightImg,0,a?84:21,c)}}}}function ButtonWidget_setDefaultButton(){var c=this;if(c.layer){var a=!c.isGray,b=_skin+"button.gif";changeSimpleOffset(c.leftImg,0,a?63:0,b);changeOffset(c.centerImg,0,a?105:42,b);changeSimpleOffset(c.rightImg,0,a?84:21,b)}c.isDefault=true}function ButtonWidget_isDisabled(){return this.isGray}function ButtonWidget_setText(a){this.txt.innerHTML=convStr(a)}function ButtonWidget_setTooltip(a){var b=this;b.tooltip=a;b.layer.title=a;if(b.txt){b.txt.title=a}if(b.icn){b.icn.title=a}}function ButtonWidget_init(){var b=this;b.oldInit();b.txt=getLayer("theBttn"+this.id);b.icn=getLayer("theBttnIcon"+this.id);b.leftImg=getLayer("theBttnLeftImg"+this.id);b.centerImg=getLayer("theBttnCenterImg"+this.id);b.rightImg=getLayer("theBttnRightImg"+this.id);var a=b.isGray?"wizbuttongray":"wizbutton";if(b.txt.className!=a){b.setDisabled(b.isGray)}}function ButtonWidget_changeImg(b,a,e,d,c,f){var g=this;if(c){g.url=c}if(b!=null){g.dx=b}if(a!=null){g.dy=a}if(e!=null){g.disDx=e}if(d!=null){g.disDy=d}if(f!=null){g.tooltip=f}if(g.icn){changeSimpleOffset(g.icn,g.isGray?g.disDx:g.dx,g.isGray?g.disDy:g.dy,g.url,g.tooltip)}}function ButtonWidget_clickCB(a){var b=ButtonWidget_inst[a];if(b&&!b.isGray){if(b.isDelayCallback){setTimeout("ButtonWidget_delayClickCB("+a+")",1)}else{ButtonWidget_delayClickCB(a)}}}function ButtonWidget_delayClickCB(a){var b=ButtonWidget_inst[a];b.executeCB()}function ButtonWidget_executeCB(){var o=this;if(o.cb){if(typeof o.cb!="string"){o.cb()}else{eval(o.cb)}}}function ButtonWidget_keydownCB(d,b){var a=eventGetKey(d);var c=ButtonWidget_inst[b];if(a==13&&c.cb){eventCancelBubble(d)}return true}function newScrolledZoneWidget(g,c,e,b,d,a){var f=newWidget(g);f.borderW=c;f.padding=e;f.w=b;f.h=d;f.oldResize=f.resize;f.beginHTML=ScrolledZoneWidget_beginHTML;f.endHTML=ScrolledZoneWidget_endHTML;f.resize=ScrolledZoneWidget_resize;f.bgClass=(a)?a:"insetBorder";return f}function ScrolledZoneWidget_beginHTML(){var a=this.w,b=this.h;var c=_moz?2*(this.borderW+this.padding):0;if(typeof(a)=="number"){if(_moz){a=Math.max(0,a-c)}a=""+a+"px"}if(typeof(b)=="number"){if(_moz){b=Math.max(0,b-c)}b=""+b+"px"}return'<div tabindex=-1 align="left" class="'+this.bgClass+'" id="'+this.id+'" style="border-width:'+this.borderW+"px;padding:"+this.padding+"px;"+sty("width",a)+sty("height",b)+'overflow:auto">'}function ScrolledZoneWidget_endHTML(){return"</div>"}function ScrolledZoneWidget_resize(a,b){if(_moz){var c=2*(this.borderW+this.padding);if(a!=null){a=Math.max(0,a-c)}if(b!=null){b=Math.max(0,b-c)}}this.oldResize(a,b)}function newComboWidget(f,e,a,b,c){var d=newWidget(f);d.tooltip=c;d.size=1;d.getHTML=ComboWidget_getHTML;d.beginHTML=ComboWidget_beginHTML;d.endHTML=ComboWidget_endHTML;d.changeCB=e;d.noMargin=a;d.width=b==null?null:""+b+"px";d.add=ComboWidget_add;d.del=ComboWidget_del;d.getSelection=ComboWidget_getSelection;d.select=ComboWidget_select;d.valueSelect=ComboWidget_valueSelect;d.getCount=ComboWidget_getCount;d.oldSetDisabled=d.setDisabled;d.setDisabled=ComboWidget_setDisabled;d.setUndefined=ComboWidget_setUndefined;d.delByID=ComboWidget_delByID;d.findByValue=ComboWidget_findByValue;d.findByText=ComboWidget_findByText;d.getValue=ComboWidget_getValue;d.isGrayed=ComboWidget_isGrayed;d.clearSelection=ComboWidget_clearSelection;d.isDisabled=false;d.multi=false;d.undef=false;d.isCombo=true;d.undefId=d.id+"__undef";d.disabledId=d.id+"__disabled";return d}_extrCmbS=(_moz?"font-size:12px;":"");function ComboWidget_beginHTML(){var b=this,a=((_moz&&!b.isCombo)?"font-size:12px;":"");return"<select "+(b.multi?"multiple":"")+" "+(b.noMargin?'style="'+sty("width",b.width)+a+'"':'style="'+sty("width",b.width)+"margin-left:10px;"+a+'"')+' class="listinputs" '+attr("onchange",_codeWinName+".ComboWidget_changeCB(event,this)")+attr("onclick",_codeWinName+".ComboWidget_clickCB(event,this)")+attr("ondblclick",_codeWinName+".ComboWidget_dblClickCB(event,this)")+attr("onkeyup",_codeWinName+".ComboWidget_keyUpCB(event,this)")+attr("onkeydown",_codeWinName+".ComboWidget_keyDownCB(event,this)")+attr("id",b.id)+attr("name",b.id)+attr("title",b.tooltip)+'size="'+b.size+'">'}function ComboWidget_clearSelection(){var a=this;if(a.layer){a.layer.selectedIndex=-1}}function ComboWidget_endHTML(){return"</select>"}function ComboWidget_getHTML(a){return this.beginHTML()+(a?a:"")+this.endHTML()}function ComboWidget_add(b,g,c,i,f){var d=this.layer,a=_curDoc.createElement("option");if(_ie){d.options.add(a)}else{d.appendChild(a)}if(a.innerText!=null){a.innerText=b}else{a.innerHTML=convStr(b)}a.value=g;if(i!=null){a.id=i}if(c){a.selected=true}if(f){a.style.color="gray"}return a}function ComboWidget_getSelection(){var c=this.layer,b=c.selectedIndex;if(b<0){return null}var a=new Object;a.index=b;a.value=c.options[b].value;a.text=c.options[b].text;return a}function ComboWidget_select(b){var d=this,c=d.layer,a=c.options.length;if(b==null){c.selectedIndex=-1}if((b<0)||(b>=a)){b=a-1}if(b>=0){c.selectedIndex=b}d.setUndefined(false)}function ComboWidget_valueSelect(b){var g=this,f=g.layer,d=f.options,a=d.length;for(var c=0;c<a;c++){if(d[c].value==b){d[c].selected=true;g.setUndefined(false);break}}}function ComboWidget_del(a){var b=this.layer;if(a==null){b.options.length=0}else{if(_ie){b.remove(a)}else{b.options[a]=null}this.select(a)}}function ComboWidget_changeCB(b,a){var c=getWidget(a);if(c.changeCB){c.changeCB(b)}}function ComboWidget_clickCB(b,a){var c=getWidget(a);if(c.clickCB){c.clickCB(b)}}function ComboWidget_dblClickCB(b,a){var c=getWidget(a);if(c.dblClickCB){c.dblClickCB(b)}}function ComboWidget_keyUpCB(b,a){var c=getWidget(a);if(c.keyUpCB){c.keyUpCB(b)}}function ComboWidget_keyDownCB(c,a){var b=eventGetKey(c);var d=getWidget(a);if(d.isCombo&&(b==27||b==13)){eventCancelBubble(c)}else{if(b==13&&d.keyUpCB){eventCancelBubble(c)}}}function ComboWidget_getCount(){return this.layer.options.length}function ComboWidget_delByID(b){var a=getLayer(b);if(a!=null){this.del(a.index)}a=null}function ComboWidget_setDisabled(e,b){var c=this;c.oldSetDisabled(e);c.isDisabled=e;if(e==true){var a=getLayer(c.disabledId);if(a==null){c.add("","",true,c.disabledId)}else{c.layer.selectedIndex=a.index}}else{c.delByID(c.disabledId)}}function ComboWidget_setUndefined(b){var c=this;c.undef=b;if(b==true){var a=getLayer(c.undefId);if(a==null){c.add("","",true,c.undefId)}else{c.layer.selectedIndex=a.index}}else{c.delByID(c.undefId)}}function ComboWidget_findByValue(j){var g=this,f=g.layer,d=f.options,a=d.length;for(var c=0;c<a;c++){if(d[c].value==j){var b=new Object;b.index=c;b.value=f.options[c].value;b.text=f.options[c].text;return b}}return null}function ComboWidget_findByText(b){var j=this,g=j.layer,f=g.options,a=f.length;for(var d=0;d<a;d++){if(f[d].text==b){var c=new Object;c.index=d;c.value=g.options[d].value;c.text=g.options[d].text;return c}}return null}function ComboWidget_getValue(c){var g=this,f=g.layer,d=f.options,a=d.length;if(c==null||c<0||c>a){return null}var b=new Object;b.index=c;b.value=f.options[c].value;return b}function ComboWidget_isGrayed(b){var f=this,d=f.layer,c=d.options,a=c.length;if(b==null||b<0||b>a){return false}return(d.options[b].style.color=="gray")}function newListWidget(a,f,e,b,k,j,g,d,i){var c=newComboWidget(a,f,true,b,j);c.clickCB=i;c.dblClickCB=g;c.keyUpCB=d;c.size=k;c.multi=e;c.getMultiSelection=ListWidget_getMultiSelection;c.setUndefined=ListWidget_setUndefined;c.isUndefined=ListWidget_isUndefined;c.change=ListWidget_change;c.isCombo=false;return c}function ListWidget_setUndefined(a){var b=this;b.undef=a;if(a==true){b.layer.selectedIndex=-1}}function ListWidget_isUndefined(){return(this.layer.selectedIndex==-1)}function ListWidget_getMultiSelection(){var g=this.layer,f=new Array,a=g.options.length;for(var d=0;d<a;d++){var c=g.options[d];if(c.selected){var b=new Object;b.index=d;b.value=c.value;b.text=c.text;f[f.length]=b}}return f}function ListWidget_change(b,a){var c=this;if(b!=null){c.multi=b;c.layer.multiple=b}if(a!=null){c.size=a;c.layer.size=a}}function newInfoWidget(f,e,b,d,a){var c=newWidget(f);c.title=e?e:"";c.boldTitle=b?b:"";c.text=d?d:"";c.height=(a!=null)?a:55;c.getHTML=InfoWidget_getHTML;c.setText=InfoWidget_setText;c.setTitle=InfoWidget_setTitle;c.setTitleBold=InfoWidget_setTitleBold;c.oldResize=c.resize;c.resize=InfoWidget_resize;c.textLayer=null;return c}function InfoWidget_setText(d,b){var c=this;d=d?d:"";c.text=d;if(c.layer){var a=c.textLayer;if(a==null){a=c.textLayer=getLayer("infozone_"+c.id)}if(a){a.innerHTML=b?d:convStr(d,false,true)}}}function InfoWidget_setTitle(c){var b=this;c=c?c:"";b.title=c;if(b.layer){var a=b.titleLayer;if(a==null){a=b.titleLayer=getLayer("infotitle_"+b.id)}if(a){a.innerHTML=convStr(c)}}}function InfoWidget_setTitleBold(c){var b=this;c=c?c:"";b.boldTitle=c;if(b.layer){var a=b.titleLayerBold;if(a==null){a=b.titleLayerBold=getLayer("infotitlebold_"+b.id)}if(a){a.innerHTML=convStr(c)}}}function InfoWidget_getHTML(){var a=this;return'<div class="dialogzone" align="left" style="overflow:hidden;'+sty("width",a.width)+sty("height",""+a.height+"px")+'" id="'+a.id+'"><nobr>'+img(_skin+"../help.gif",16,16,"top",null,_helpLab)+'<span class="dialogzone" style="padding-left:5px" id="infotitle_'+a.id+'">'+convStr(a.title)+'</span><span style="padding-left:5px" class="dialogzonebold" id="infotitlebold_'+a.id+'">'+convStr(a.boldTitle)+"</span></nobr><br>"+getSpace(1,2)+'<div class="infozone" align="left" id="infozone_'+a.id+'" style="height:'+(a.height-18-(_moz?10:0))+"px;overflow"+(_ie?"-y":"")+':auto">'+convStr(a.text,false,true)+"</div></div>"}function InfoWidget_resize(b,c){var d=this;if(b!=null){d.w=b}if(c!=null){d.h=c}d.oldResize(b,c);if(d.layer){var a=d.textLayer;if(a==null){a=d.textLayer=getLayer("infozone_"+d.id)}if(a){if(d.h!=null){a.style.height=""+Math.max(0,d.h-(_ie?18:28))+"px"}}}}function newCheckWidget(a,i,f,e,j,g,d,c){var b=newWidget(a);b.text=i;b.convText=c;b.changeCB=f;b.idCheckbox="check_"+a;b.checkbox=null;b.kind="checkbox";b.name=b.idCheckbox;b.bold=e;b.imgUrl=j;b.imgW=g;b.imgH=d;b.getHTML=CheckWidget_getHTML;b.setText=CheckWidget_setText;b.parentInit=Widget_init;b.init=CheckWidget_init;b.check=CheckWidget_check;b.isChecked=CheckWidget_isChecked;b.setDisabled=CheckWidget_setDisabled;b.isDisabled=CheckWidget_isDisabled;b.uncheckOthers=CheckWidget_uncheckOthers;b.isIndeterminate=CheckWidget_isIndeterminate;b.setIndeterminate=CheckWidget_setIndeterminate;b.layerClass=("dialogzone"+(b.bold?"bold":""));b.nobr=true;return b}function CheckWidget_getHTML(){var b=this,a=b.layerClass;return'<table border="0" onselectstart="return false" cellspacing="0" cellpadding="0" class="'+a+'"'+attr("id",b.id)+'><tr valign="middle"><td style="height:20px;width:21px"><input style="margin:'+(_moz?3:0)+'px" onclick="'+_codeWinName+'.CheckWidget_changeCB(event,this)" type="'+b.kind+'"'+attr("id",b.idCheckbox)+attr("name",b.name)+"></td>"+(b.imgUrl?'<td><label style="padding-left:2px" for="'+b.idCheckbox+'">'+img(b.imgUrl,b.imgW,b.imgH)+"</label></td>":"")+"<td>"+(b.nobr?"<nobr>":"")+'<label style="padding-left:'+(b.imgUrl?4:2)+'px" id="label_'+b.id+'" for="'+b.idCheckbox+'">'+(b.convText?convStr(b.text):b.text)+"</label>"+(b.nobr?"</nobr>":"")+"</td></tr></table>"}function CheckWidget_setText(a){var b=this;b.text=a;if(b.layer){if(b.labelLyr==null){b.labelLyr=getLayer("label_"+b.id)}b.labelLyr.innerHTML=b.convText?convStr(a):a}}function CheckWidget_init(){this.parentInit();this.checkbox=getLayer(this.idCheckbox)}function CheckWidget_check(a){this.checkbox.checked=a;if(a){this.uncheckOthers()}}function CheckWidget_isChecked(){return this.checkbox.checked}function CheckWidget_changeCB(b,a){var c=getWidget(a);c.uncheckOthers();if(c.changeCB){c.changeCB(b)}}function CheckWidget_setDisabled(a){this.checkbox.disabled=a;if(_moz){this.checkbox.className=(a?"dialogzone":"")}}function CheckWidget_isDisabled(){return this.checkbox.disabled}function CheckWidget_uncheckOthers(){}function CheckWidget_isIndeterminate(){return this.checkbox.indeterminate}function CheckWidget_setIndeterminate(a){this.checkbox.indeterminate=a}function newRadioWidget(a,k,l,i,f,m,j,d,c){var b=newCheckWidget(a,l,i,f,m,j,d,c);b.kind="radio";b.name=k;if(_RadioWidget_groups[k]==null){_RadioWidget_groups[k]=new Array}b.groupInstance=_RadioWidget_groups[k];var e=b.groupInstance;b.groupIdx=e.length;e[e.length]=b;b.uncheckOthers=RadioWidget_uncheckOthers;return b}var _RadioWidget_groups=new Array;function RadioWidget_uncheckOthers(){var e=this.groupInstance,b=this.groupIdx,a=e.length;for(var d=0;d<a;d++){if(d!=b){var f=e[d].checkbox;if(f){f.checked=false}}}}function newTextFieldWidget(c,g,k,f,j,b,l,d,a,i){var e=newWidget(c);e.tooltip=l;e.changeCB=g;e.maxChar=k;e.keyUpCB=f;e.enterCB=j;e.noMargin=b;e.width=d==null?null:""+d+"px";e.focusCB=a;e.blurCB=i;e.disabled=false;e.getHTML=TextFieldWidget_getHTML;e.getValue=TextFieldWidget_getValue;e.setValue=TextFieldWidget_setValue;e.intValue=TextFieldWidget_intValue;e.intPosValue=TextFieldWidget_intPosValue;e.select=TextFieldWidget_select;e.setDisabled=TextFieldWidget_setDisabled;e.beforeChange=null;e.wInit=e.init;e.init=TextFieldWidget_init;e.oldValue="";e.helpTxt="";e.isHelpTxt=false;e.setHelpTxt=TextFieldWidget_setHelpTxt;e.eraseHelpTxt=TextFieldWidget_eraseHelpTxt;e.enterCancelBubble=true;return e}function TextFieldWidget_setDisabled(b){var a=this;a.disabled=b;if(a.layer){a.layer.disabled=b}}function TextFieldWidget_init(){var a=this;a.wInit();a.layer.value=""+(a.oldValue!="")?a.oldValue:"";if(a.helpTxt&&!a.oldValue){a.setHelpTxt(a.helpTxt)}}function TextFieldWidget_getHTML(){var a=this;return"<input"+(a.disabled?" disabled":"")+' oncontextmenu="event.cancelBubble=true;return true" style="'+sty("width",this.width)+(_moz?"margin-top:1px;margin-bottom:1px;padding-left:5px;padding-right:2px;":"")+(_isQuirksMode?"height:20px;":"height:16px;")+"margin-left:"+(this.noMargin?0:10)+'px" onfocus="'+_codeWinName+'.TextFieldWidget_focus(this)" onblur="'+_codeWinName+'.TextFieldWidget_blur(this)" onchange="'+_codeWinName+'.TextFieldWidget_changeCB(event,this)" onkeydown=" return '+_codeWinName+'.TextFieldWidget_keyDownCB(event,this);" onkeyup=" return '+_codeWinName+'.TextFieldWidget_keyUpCB(event,this);" onkeypress=" return '+_codeWinName+'.TextFieldWidget_keyPressCB(event,this);" type="text" '+attr("maxLength",this.maxChar)+' ondragstart="event.cancelBubble=true;return true" onselectstart="event.cancelBubble=true;return true" class="textinputs" id="'+this.id+'" name="'+this.id+'"'+attr("title",this.tooltip)+' value="">'}function TextFieldWidget_getValue(){var a=this;if(a.isHelpTxt){return""}else{return a.layer?a.layer.value:a.oldValue}}function TextFieldWidget_setValue(a){var b=this;if(b.layer){b.eraseHelpTxt();b.layer.value=""+a}else{b.oldValue=a}}function TextFieldWidget_changeCB(b,a){var c=getWidget(a);c.eraseHelpTxt();if(c.beforeChange){c.beforeChange()}if(c.changeCB){c.changeCB(b)}}function TextFieldWidget_keyPressCB(b,a){var c=getWidget(a);if(eventGetKey(b)==13){c.enterKeyPressed=true;return false}else{c.enterKeyPressed=false}return true}function TextFieldWidget_keyUpCB(b,a){var c=getWidget(a);c.eraseHelpTxt();if(eventGetKey(b)==13&&c.enterKeyPressed){if(c.beforeChange){c.beforeChange()}if(c.enterCB){if(c.enterCancelBubble){eventCancelBubble(b)}c.enterCB(b)}return false}else{if(c.keyUpCB){c.keyUpCB(b)}}c.enterKeyPressed=false;return true}function TextFieldWidget_keyDownCB(b,a){var c=getWidget(a);c.eraseHelpTxt();c.enterKeyPressed=false;if(eventGetKey(b)==13){return true}else{if(eventGetKey(b)==8){eventCancelBubble(b)}}return true}function TextFieldWidget_eraseHelpTxt(){var a=this;if(a.isHelpTxt){a.layer.value=""}a.isHelpTxt=false;a.layer.style.color="black"}function TextFieldWidget_focus(a){var b=getWidget(a);b.eraseHelpTxt();if(b.focusCB){b.focusCB()}}function TextFieldWidget_blur(a){var b=getWidget(a);if(b.beforeChange){b.beforeChange()}if(b.blurCB){b.blurCB()}}function TextFieldWidget_intValue(a){var b=parseInt(this.getValue());return isNaN(b)?a:b}function TextFieldWidget_intPosValue(a){var b=this.intValue(a);return(b<0)?a:b}function TextFieldWidget_select(){this.layer.select()}function TextFieldWidget_setHelpTxt(a){var b=this;b.helpTxt=a;if(b.layer&&(b.layer.value=="")){b.isHelpTxt=true;b.layer.value=a;b.layer.style.color="#808080"}}function newIntFieldWidget(c,g,j,f,i,b,k,d,a){var e=newTextFieldWidget(c,g,j,f,i,b,k,d);e.min=-Number.MAX_VALUE;e.max=Number.MAX_VALUE;e.customCheckCB=a;e.setMin=IntFieldWidget_setMin;e.setMax=IntFieldWidget_setMax;e.setValue=IntFieldWidget_setValue;e.beforeChange=IntFieldWidget_checkChangeCB;e.value="";return e}function IntFieldWidget_setMin(a){if(!isNaN(a)){this.min=a}}function IntFieldWidget_setMax(a){if(!isNaN(a)){this.max=a}}function IntFieldWidget_setValue(b){var c=this,a=c.layer;b=""+b;if(b==""){if(a){a.value=""}c.oldValue="";return}var d=parseInt(b);value="";if(!isNaN(d)&&(d>=c.min)&&(d<=c.max)&&((c.customCheckCB==null)||c.customCheckCB(d))){value=d;c.oldValue=value}else{if(c.oldValue){value=c.oldValue}}if(a){a.value=""+value}}function IntFieldWidget_checkChangeCB(){var a=this;a.setValue(a.layer.value)}function newFrameZoneWidget(e,a,c,b){var d=newWidget(e);d.w=(a!=null)?""+Math.max(0,a-10)+"px":null;d.h=(c!=null)?""+Math.max(0,c-10)+"px":null;d.reverse=(b!=null)?b:false;d.cont=null;d.beginHTML=FrameZoneWidget_beginHTML;d.endHTML=FrameZoneWidget_endHTML;d.oldResize=d.resize;d.resize=FrameZoneWidget_resize;return d}function FrameZoneWidget_resize(a,b){var e=this;var c=e.layer.display!="none";if(c&_moz&&!_saf){e.setDisplay(false)}e.oldResize(a,b);if(c&_moz&&!_saf){e.setDisplay(true)}}function FrameZoneWidget_beginHTML(){var a=this;return'<table width="100%" style="'+sty("width",a.w)+sty("height",a.h)+'" id="'+a.id+'" cellspacing="0" cellpadding="4" border="0"><tbody><tr><td valign="top" class="dlgFrame" id="frame_cont_'+a.id+'" style="padding:5px">'}function FrameZoneWidget_endHTML(){var a=this;return"</td></tr></tbody></table>"}function arrayAdd(e,g,d,b){var f=e[g],a=f.length;if((b==null)||(typeof b!="number")){b=-1}if((b<0)||(b>a)){b=a}if(b!=a){var c=f.slice(b);f.length=b+1;f[b]=d;f=f.concat(c)}else{f[b]=d}e[g]=f;return b}function arrayRemove(d,f,a){var e=d[f],c=e.length-1;if(a==null){e.length=0;d[f]=e;return -1}if((a<0)||(a>c)){return -1}if(a==c){e.length=c}else{var b=e.slice(a+1);e.length=a;e=e.concat(b)}d[f]=e;return a}function getFrame(name,par){if(par==null){par=self}var frames=par.frames,w=eval("frames."+name);if(w==null){return w}var l=frames.length;for(var i=0;i<l;i++){w=frames[i];try{if(w.name==name){return w}}catch(exc){}}return null}function frameGetUrl(a){return a.location.href}function frameReload(a){var b=a.location;b.replace(b.href)}function setTopFrameset(){_curWin._topfs="topfs"}function getTopFrameset(a){if(a==null){a=self}if(a._topfs=="topfs"){return a}else{if(a!=top){return getTopFrameset(a.parent)}else{return null}}}function convStr(d,a,c){d=""+d;var b=d.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;");if(a){b=b.replace(/ /g,"&nbsp;")}if(c){b=b.replace(/\n/g,"<br>")}return b}function escapeCR(b){b=""+b;var a=b.replace(/\r/g,"").replace(/\n/g,"\\n");return a}function addDblClickCB(b,a){if(b.addEventListener&&!_saf){b.addEventListener("dblclick",a,false)}else{b.ondblclick=a}}function img(e,a,c,f,b,d){b=(b?b:"");if(d==null){d=""}return"<img"+attr("width",a)+attr("height",c)+attr("src",e)+attr("alt",d)+attr("align",f)+' border="0" hspace="0" vspace="0" '+(b?b:"")+">"}function imgOffset(a,g,d,k,j,b,f,c,i,e){return img(_skin+"../transp.gif",g,d,e,(f?f:"")+" "+attr("id",b)+' style="float:left;'+backImgOffset(a,k,j)+(i?i:"")+'"',c)}function simpleImgOffset(a,g,d,k,j,b,f,c,i,e){if(_ie){if(k==null){k=0}if(j==null){j=0}return"<div "+(f?f:"")+" "+attr("id",b)+' style="position:relative;padding:0px;width:'+g+"px;height:"+d+"px;overflow:hidden;"+(i?i:"")+'">'+img(a,null,null,(e?e:"top"),'style="margin:0px;position:relative;top:'+(-j)+"px;left:"+(-k)+'px" tabIndex="-1"',c)+"</div>"}else{return imgOffset(a,g,d,k,j,b,f,c,i,e)}}function changeSimpleOffset(e,b,a,d,f){if(_ie){e=e.childNodes[0];var c=e.style;if((d!=null)&&(d!=e.src)){e.src=d}if(b!=null){c.left=""+(-b)+"px"}if(a!=null){c.top=""+(-a)+"px"}if(f!=null){e.title=f;e.alt=f}}else{changeOffset(e,b,a,d,f)}}function backImgOffset(c,b,a){return"background-image:url('"+c+"');background-position:"+(-b)+"px "+(-a)+"px;"}function changeOffset(e,b,a,d,f){var c=e.style;if(c){if((b!=null)&&(a!=null)){c.backgroundPosition=""+(-b)+"px "+(-a)+"px"}if(d){c.backgroundImage="url('"+d+"')"}}if(f){e.title=f}}function includeCSS(b,c){if(typeof(_skin)=="string"&&_skin!=""){var a="";if(c){a=_skin+"../"+b}else{a=_skin+b}a+=".css";_curDoc.write('<link rel="stylesheet" type="text/css" href="'+a+'">')}}function getLayer(b){var a=null;if(typeof b=="object"){a=_curDoc.getElementById(b.id)}else{a=_curDoc.getElementById(b)}return a}function setLayerTransp(a,b){if(_ie){a.style.filter=(b==null)?"":"progid:DXImageTransform.Microsoft.Alpha( style=0,opacity="+b+")"}else{a.style.MozOpacity=(b==null)?1:b/100}}function getPos(b,a){a=a?a:null;for(var d=0,c=0;(b!=null)&&(b!=a);d+=b.offsetLeft,c+=b.offsetTop,b=b.offsetParent){}return{x:d,y:c}}function getPos2(b,a){var a=a?a:null;var d=0;var c=0;while(b.parentNode||b.offsetParent){if(b.offsetParent){d+=b.offsetLeft;c+=b.offsetTop;b=b.offsetParent}else{if(b.parentNode){if(b.style){if(b.style.left){d+=b.style.left}if(b.style.top){c+=b.style.top}}b=b.parentNode}else{break}}}if(a){relToCord=getPos2(a);d-=relToCord.x;c-=relToCord.y}return{x:d,y:c}}function getPosScrolled(c,b){b=b?b:null;if(_ie){for(var e=0,d=0;(c!=null)&&(c!=b);e+=c.offsetLeft-c.scrollLeft,d+=c.offsetTop-c.scrollTop,c=c.offsetParent){}}else{var a=c;for(var e=0,d=0;(c!=null)&&(c!=b);e+=c.offsetLeft,d+=c.offsetTop,c=c.offsetParent){}for(c=a;(c!=null)&&(c!=b);c=c.parentNode){if(c.scrollLeft!=null){e-=c.scrollLeft;d-=c.scrollTop}}}e+=getScrollX();d+=getScrollY();return{x:e,y:d}}function getWidget(b){if(b==null){return null}var a=b._widget;if(a!=null){return _widgets[a]}else{return getWidget(b.parentNode)}}function getWidgetFromID(b){if(b==null){return null}var a=getLayer(b);return getWidget(a)}function attr(a,b){return(b!=null?" "+a+'="'+b+'" ':"")}function sty(a,b){return(b!=null?a+":"+b+";":"")}function getSep(b,a){if(b==null){b=0}var c=b>0?'<td width="'+b+'">'+getSpace(b,1)+"</td>":"";return'<table style="margin-top:5px;margin-bottom:5px;" width="100%" cellspacing="0" cellpadding="0"><tr>'+c+'<td background="'+_skin+"sep"+(a?"_solid":"")+'.gif" class="smalltxt"><img alt="" src="'+_skin+'../transp.gif" width="10" height="2"></td>'+c+"</tr></table>"}function writeSep(b,a){_curDoc.write(getSep(b,a))}function getSpace(a,b){return'<table height="'+b+'" border="0" cellspacing="0" cellpadding="0"><tr><td>'+img(_skin+"../transp.gif",a,b)+"</td></tr></table>"}function writeSpace(a,b){_curDoc.write(getSpace(a,b))}function documentWidth(b){var b=b?b:_curWin;var a=Math.max(document.body.clientWidth,document.documentElement.clientWidth);a=Math.max(a,document.body.scrollWidth);return a}function documentHeight(b){var b=b?b:_curWin;var a=Math.max(document.body.clientHeight,document.documentElement.clientHeight);a=Math.max(a,document.body.scrollHeight);return a}function winWidth(b){var a;var b=b?b:_curWin;if(_ie){if(_isQuirksMode){a=b.document.body.clientWidth}else{a=b.document.documentElement.clientWidth}}else{a=b.innerWidth}return a}function winHeight(b){var b=b?b:_curWin;var a;if(_ie){if(_isQuirksMode){a=document.body.clientHeight}else{a=document.documentElement.clientHeight}}else{a=b.innerHeight}return a}function getScrollX(a){var b=0;var a=a?a:_curWin;if(typeof(a.scrollX)=="number"){b=a.scrollX}else{b=Math.max(a.document.body.scrollLeft,a.document.documentElement.scrollLeft)}return b}function getScrollY(b){var a=0;var b=b?b:_curWin;if(typeof(b.scrollY)=="number"){a=window.scrollY}else{a=Math.max(b.document.body.scrollTop,b.document.documentElement.scrollTop)}return a}function winScrollTo(a,c,b){b=b?b:_curWin;b.scrollTo(a,c)}function eventGetKey(b,a){a=a?a:_curWin;return _ie?a.event.keyCode:b.keyCode}function eventGetX(a){return _ie?_curWin.event.clientX:a.clientX?a.clientX:a.pageX}function eventGetY(a){return _ie?_curWin.event.clientY:a.clientY?a.clientY:a.pageY}function xpos(d,c,b,a){if((a==null)||(!_ie)){a=1}return((c.clientX/a)-getPos(d).x)+getScrollX()}function ypos(d,c,b,a){if((a==null)||(!_ie)){a=1}return((c.clientY/a)-getPos(d).y)+(_ie?b.body.scrollTop:0)}function absxpos(b,a){if((a==null)||(!_ie)){return b.clientX}else{return b.clientX/a}}function absypos(b,a){if((a==null)||(!_ie)){return b.clientY}else{return b.clientY/a}}function eventCancelBubble(c,b){b=b?b:_curWin;var a=_ie?b.event:c;if(a){a.cancelBubble=true;if(a.stopPropagation){a.stopPropagation()}}}function isHidden(b){if((b==null)||(b.tagName=="BODY")){return false}var a=b.style;if((a==null)||(a.visibility==_hide)||(a.display=="none")){return true}return isHidden(b.parentNode)}function opt(c,a,b){return'<option value="'+c+'" '+(b?"selected":"")+">"+convStr(""+a)+"</option>"}function lnk(c,d,a,f,b,e){if(d==null){d="return false"}b=b?b:"";return"<a"+attr("class",a)+attr("id",f)+attr("href","javascript:void(0)")+attr("onclick",d)+attr("ondblclick",e)+b+">"+c+"</a>"}_oldErrHandler=null;function localErrHandler(){return true}function canScanFrames(a){var b=true,e=null;if(_moz){_oldErrHandler=window.onerror;window.onerror=localErrHandler}try{e=a.document;b=false}catch(c){}if(_moz){window.onerror=_oldErrHandler}return(!b&&(e!=null))}function getBGIframe(a){return'<iframe id="'+a+'" name="'+a+'" style="display:none;left:0px;position:absolute;top:0px" src="'+_skin+'../../empty.html" frameBorder="0" scrolling="no"></iframe>'}function getDynamicBGIFrameLayer(){var a=false;if(_curWin.BGIFramePool){BGIFrames=_curWin.BGIFramePool.split(",");BGIFCount=BGIFrames.length;for(var b=0;b<BGIFCount;b++){if(BGIFrames[b]!="1"){a=true;break}}}else{b=0;BGIFrames=new Array}BGIFrames[b]="1";_curWin.BGIFramePool=BGIFrames.join(",");if(!a){targetApp(getBGIframe("BGIFramePool_"+b))}return getLayer("BGIFramePool_"+b)}function holdBGIFrame(b){var a=getLayer(b);if(a){a.style.display=""}id=parseInt(b.split("_")[1]);BGIFrames=_curWin.BGIFramePool.split(",");BGIFrames[id]=1;_curWin.BGIFramePool=BGIFrames.join(",")}function releaseBGIFrame(b){var a=getLayer(b);if(a){a.style.display="none"}id=parseInt(b.split("_")[1]);BGIFrames=_curWin.BGIFramePool.split(",");BGIFrames[id]=0;_curWin.BGIFramePool=BGIFrames.join(",")}function append(f,b,i){if(_ie){f.insertAdjacentHTML("BeforeEnd",b)}else{var a=i?i:_curDoc;var d=a.createRange();d.setStartBefore(f);var g=d.createContextualFragment(b);f.appendChild(g)}}function append2(f,b,i){if(_ie){f.insertAdjacentHTML("afterBegin",b)}else{var a=i?i:_curDoc;var d=a.createRange();d.setStartBefore(f);var g=d.createContextualFragment(b);f.appendChild(g)}}function insBefore(f,b,i){if(_ie){f.insertAdjacentHTML("BeforeBegin",b)}else{var a=i?i:_curDoc;var d=_curDoc.createRange();d.setEndBefore(f);var g=d.createContextualFragment(b);f.parentNode.insertBefore(g,f)}}function insBefore2(f,b,i){if(_ie){f.insertAdjacentHTML("BeforeBegin",b)}else{var a=i?i:_curDoc;var d=_curDoc.createRange();d.setStartBefore(f);var g=d.createContextualFragment(b);f.parentNode.insertBefore(g,f)}}function targetApp(a){append(_curDoc.body,a)}function preloadImg(b){var a=_preloadArr[_preloadArr.length]=new Image;a.src=b}_staticUnicBlockWhileWaitWidgetID="staticUnicBlockWhileWaitWidgetID";function hideBlockWhileWaitWidget(){var a=getLayer(_staticUnicBlockWhileWaitWidgetID);if(a){a.style.display="none"}}function newBlockWhileWaitWidget(a){if(window._BlockWhileWaitWidget!=null){return window._BlockWhileWaitWidget}var b=newWidget(_staticUnicBlockWhileWaitWidgetID);b.getPrivateHTML=BlockWhileWaitWidget_getPrivateHTML;b.init=BlockWhileWaitWidget_init;b.show=BlockWhileWaitWidget_show;window._BlockWhileWaitWidget=b;return b}function BlockWhileWaitWidget_init(){}function BlockWhileWaitWidget_getPrivateHTML(){return'<div id="'+this.id+'" onselectstart="return false" ondragstart="return false" onmousedown="'+_codeWinName+'.eventCancelBubble(event)" border="0" hspace="0" vspace="0"  style="background-image:url('+_skin+'../transp.gif);z-index:5000;cursor:wait;position:absolute;top:0px;left:0px;width:100%;height:100%"></div>'}function BlockWhileWaitWidget_show(a){var b=this;if(b.layer==null){b.layer=getLayer(b.id);if(b.layer==null){targetApp(b.getPrivateHTML());b.layer=getLayer(b.id);b.css=b.layer.style}else{b.css=b.layer.style}}b.setDisplay(a)}function isTextInput(a){var b=_ie?a.srcElement:a.target;var c=false;if(b.tagName=="TEXTAREA"){c=true}if((b.tagName=="INPUT")&&((b.type.toLowerCase()=="text")||(b.type.toLowerCase()=="password"))){c=true}return c}function isTextArea(a){var b=_ie?a.srcElement:a.target;if(b.tagName=="TEXTAREA"){return true}else{return false}}function LZ(a){return(a<0||a>9?"":"0")+a}if(bobj.crv.config.isDebug){localErrHandler=null}initDom(bobj.crvUri("../dhtmllib/images/")+bobj.crv.config.skin+"/","",bobj.crv.config.lang);styleSheet();if(window._DHTML_LIB_DIALOG_JS_LOADED==null){_DHTML_LIB_DIALOG_JS_LOADED=true;DialogBoxWidget_modals=new Array;DialogBoxWidget_instances=new Array;DialogBoxWidget_current=null;_promptDlgInfo=0;_promptDlgWarning=1;_promptDlgCritical=2;_dlgTitleLBorderToTxt=20;_dlgTitleHeight=25;_dlgTitleMarginBottom=4;_dlgTitleRBorderToClose=10;_dlgTitleCloseBtnImgFile="dialogtitle.gif";_dlgTitleCloseBtnW=11;_dlgTitleCloseBtnH=10;_dlgTitleCloseBtnDy=26;_dlgTitleCloseBtnHoverDy=37;_dlgBottomMargin=14}function newDialogBoxWidget(a,f,b,i,j,g,e,d){var c=newWidget(a);c.title=f;c.width=b;c.height=i;c.defaultCB=j;c.cancelCB=g;c.noCloseButton=e?e:false;c.isAlert=d;c.closeCB=null;c.resizeable=false;c.oldMouseDown=null;c.oldCurrent=null;c.modal=null;c.hiddenVis=new Array;c.lastLink=null;c.firstLink=null;c.titleLayer=null;c.defaultBtn=null;c.divLayer=null;c.oldInit=c.init;c.oldShow=c.show;c.init=DialogBoxWidget_init;c.setResize=DialogBoxWidget_setResize;c.beginHTML=DialogBoxWidget_beginHTML;c.endHTML=DialogBoxWidget_endHTML;c.show=DialogBoxWidget_Show;c.center=DialogBoxWidget_center;c.focus=DialogBoxWidget_focus;c.setTitle=DialogBoxWidget_setTitle;c.getContainerWidth=DialogBoxWidget_getContainerWidth;c.getContainerHeight=DialogBoxWidget_getContainerHeight;DialogBoxWidget_instances[a]=c;c.modal=newWidget("modal_"+a);c.placeIframe=DialogBoxWidget_placeIframe;c.oldResize=c.resize;c.resize=DialogBoxWidget_resize;c.attachDefaultButton=DialogBoxWidget_attachDefaultButton;c.unload=DialogBoxWidget_unload;c.close=DialogBoxWidget_close;c.setCloseCB=DialogBoxWidget_setCloseCB;c.setNoCloseButton=DialogBoxWidget_setNoCloseButton;if(!_ie){if(c.width!=null){c.width=Math.max(0,b+4)}if(c.height!=null){c.height=Math.max(0,i+4)}}return c}function DialogBoxWidget_setResize(a,c,d,b,f){var e=this;e.resizeable=true;e.resizeCB=a;e.minWidth=c?c:50;e.minHeight=d?d:50;e.noResizeW=b;e.noResizeH=f}function DialogBoxWidget_setTitle(b){var a=this;a.title=b;if(a.titleLayer==null){a.titleLayer=getLayer("titledialog_"+this.id)}a.titleLayer.innerHTML=convStr(b)}function DialogBoxWidget_setCloseIcon(a,b){changeOffset(a,0,(b==1?0:18))}function DialogBoxWidget_beginHTML(){with(this){var moveableCb=' onselectstart="return false" ondragstart="return false" onmousedown="'+_codeWinName+".DialogBoxWidget_down(event,'"+id+"',this,false);return false;\" ";var titleBG="background-image:url("+_skin+"dialogtitle.gif)";var mdl='<div onselectstart="return false" onmouseup="'+_codeWinName+".DialogBoxWidget_keepFocus('"+this.id+'\');" onmousedown="'+_codeWinName+'.eventCancelBubble(event)" border="0" hspace="0" vspace="0" src="'+_skin+'../transp.gif" id="modal_'+id+'" style="background-color:#888888;opacity:0.3;filter:alpha(opacity:30);position:absolute;top:0px;left:0px;width:1px;height:1px">'+(_ie?img(_skin+"../transp.gif","100%","100%",null):"")+"</div>";var btn="";if(_dtd4){btn='<td style="padding-right:'+_dlgTitleRBorderToClose+'px"><div id="dialogClose_'+id+'" class="dlgCloseBtn" title="'+_closeDialog+'"></div></td>'}else{btn='<td style="padding-right:'+_dlgTitleRBorderToClose+'px">'+simpleImgOffset(_skin+_dlgTitleCloseBtnImgFile,_dlgTitleCloseBtnW,_dlgTitleCloseBtnH,0,_dlgTitleCloseBtnDy,"dialogClose_"+id,null,_closeDialog)+"</td>"}var closeBtn='<td class="dlgCloseArea" align="left" valign="middle"><table border="0" cellspacing="0" cellpadding="0"><tbody><tr style="height:'+_dlgTitleHeight+'px">'+btn+"</tr></tbody></table></td>";var dlgtitle='<table style="height:'+_dlgTitleHeight+'" class="dlgTitle" width="100%"  border="0" cellspacing="0" cellpadding="0"><tr valign="top" style="height:'+_dlgTitleHeight+'px"><td '+moveableCb+' style="cursor:move;padding-left:'+_dlgTitleLBorderToTxt+'px;" width="100%" valign="middle" align="left"><nobr><span id="titledialog_'+id+'" tabIndex="0" class="titlezone">'+convStr(title)+"</span></nobr></td>"+closeBtn+"</tr></table>";var s="";s=mdl;var dims=sty("width",width?(""+width+"px"):null)+sty("height",height?(""+Math.max(0,height+(_moz?-2:0))+"px"):null);s+='<button style="position:absolute;left:-30px;top:-30px; visibility:hidden" id="firstLink_'+this.id+'" onfocus="'+_codeWinName+".DialogBoxWidget_keepFocus('"+this.id+"');return false;\" ></button>";s+='<table border="0" cellspacing="0" cellpadding="0" id="'+id+'" style="display:none;padding:0px;visibility:'+_hide+";position:absolute;top:-2000px;left:-2000px;"+dims+'" '+(isAlert?'role="alertdialog"':'role="dialog"')+">";s+="<tr><td "+(_moz?'style="'+dims+'" ':"")+'class="dialogbox" id="td_dialog_'+id+'" onresize="'+_codeWinName+".DialogBoxWidget_resizeIframeCB('"+id+'\',this)"  valign="top">';s+='<table class="dlgBox2" width="100%" border="0" cellspacing="0" cellpadding="0"><tbody>';s+='<tr><td height="'+_dlgTitleHeight+'" valign="top">'+dlgtitle+"</td></tr>";s+='<tr><td class="dlgBody" valign="top" id="div_dialog_'+id+'">';return s}}function DialogBoxWidget_endHTML(){var a="</td></tr>";a+='<tr><td style="height:'+_dlgBottomMargin+'px;"></td></tr>';a+="</tbody></table>";a+="</td></tr></table>";a+='<button style="position:absolute;left:-30px;top:-30px; visibility:hidden" id="lastLink_'+this.id+'" onfocus="'+_codeWinName+".DialogBoxWidget_keepFocus('"+this.id+"');return false;\" ></button>";return a}function DialogBoxWidget_getContainerWidth(){var a=this;return a.width-(2+2)}function DialogBoxWidget_getContainerHeight(){var a=this;return a.height-(2+18+2+2+2)}function DialogBoxWidget_close(b){var a=DialogBoxWidget_instances[b];if(a){a.show(false);if(a.cancelCB!=null){a.cancelCB()}}}function DialogBoxWidget_setCloseCB(a){this.closeCB=a}function DialogBoxWidget_setNoCloseButton(a){if(this.noCloseButton!==a){this.noCloseButton=a;if(this.initialized()){this.closeButton.style.visibility=this.noCloseButton?_hide:_show}}}function DialogBoxWidget_resizeIframeCB(b,a){DialogBoxWidget_instances[b].placeIframe()}function DialogBoxWidget_placeIframe(){var b=this;if(b.iframe){var a=b.td_lyr;if(a==null){b.td_lyr=a=getLayer("td_dialog_"+b.id)}b.iframe.resize(a.offsetWidth,a.offsetHeight);b.iframe.move(b.layer.offsetLeft,b.layer.offsetTop)}}function DialogBoxWidget_resize(a,b){var c=this;c.oldResize(a,b);if(c.iframe){c.iframe.resize(a,b)}}function DialogBoxWidget_init(){if(this.layer!=null){return}var a=this;a.oldInit();a.modal.init();a.lastLink=newWidget("lastLink_"+a.id);a.firstLink=newWidget("firstLink_"+a.id);a.lastLink.init();a.firstLink.init();if(_saf){a.webKitFocusElem=getLayer("webKitFocusElem"+a.id)}a.closeButton=getLayer("dialogClose_"+a.id);a.closeButton.style.visibility=a.noCloseButton?_hide:_show;a.closeButton.onmouseover=DialogBoxWidget_moverCloseBtn;a.closeButton.onmouseout=DialogBoxWidget_moverCloseBtn;a.closeButton.onclick=function(){a.close(a.id)}}function DialogBoxWidget_moverCloseBtn(a){var a=getEvent(a);var b=(a&&a.type=="mouseover")?true:false;if(_dtd4){this.className=b?"dlgCloseBtnHover":"dlgCloseBtn"}else{changeOffset(this,0,b?_dlgTitleCloseBtnHoverDy:_dlgTitleCloseBtnDy)}}function DialogBoxWidget_attachDefaultButton(a){this.defaultBtn=a;this.defaultBtn.setDefaultButton()}_theLYR=null;_dlgResize=null;function DialogBoxWidget_down(e,id,obj,isResize){_dlgResize=isResize;var o=DialogBoxWidget_instances[id],lyr=o.layer,mod=o.modal.layer;lyr.onmousemove=mod.onmousemove=eval("_curWin."+_codeWinName+".DialogBoxWidget_move");lyr.onmouseup=mod.onmouseup=eval("_curWin."+_codeWinName+".DialogBoxWidget_up");lyr.dlgStartPosx=mod.dlgStartPosx=parseInt(lyr.style.left);lyr.dlgStartPosy=mod.dlgStartPosy=parseInt(lyr.style.top);lyr.dlgStartx=mod.dlgStartx=eventGetX(e);lyr.dlgStarty=mod.dlgStarty=eventGetY(e);lyr.dlgStartw=mod.dlgStartw=o.getWidth();lyr.dlgStarth=mod.dlgStarth=o.getHeight();lyr._widget=mod._widget=o.widx;_theLYR=lyr;eventCancelBubble(e);if(lyr.setCapture){lyr.setCapture(true)}}function DialogBoxWidget_move(c){var f=_theLYR,d=getWidget(f);if(_dlgResize){var i=Math.max(d.minWidth,f.dlgStartw+eventGetX(c)-f.dlgStartx);var b=Math.max(d.minHeight,f.dlgStarth+eventGetY(c)-f.dlgStarty);d.resize(d.noResizeW?null:i,d.noResizeH?null:b);if(d.firstTR){if(!d.noResizeW){d.firstTR.style.width=i-4}if(!d.noResizeH){d.secondTR.style.height=b-44}}if(d.resizeCB){d.resizeCB(i,b)}}else{var a=Math.max(0,f.dlgStartPosx-f.dlgStartx+eventGetX(c));var g=Math.max(0,f.dlgStartPosy-f.dlgStarty+eventGetY(c));d.iframe.move(a,g);d.move(a,g)}eventCancelBubble(c);return false}function DialogBoxWidget_up(c){var d=getWidget(_theLYR),a=d.layer,b=d.modal.layer;a.onmousemove=b.onmousemove=null;a.onmouseup=b.onmouseup=null;if(a.releaseCapture){a.releaseCapture()}_theLYR=null}function DialogBoxWidget_keypress(a){eventCancelBubble(a);var b=DialogBoxWidget_current;if(b!=null){switch(eventGetKey(a)){case 13:if(b.yes&&!b.no){if(b.defaultCB){b.defaultCB()}return false}if(isTextArea(_ie?_curWin.event:a)){return true}if(b.defaultBtn!=null&&!b.defaultBtn.isDisabled()){b.defaultBtn.executeCB();return false}break;case 27:if(!b.noCloseButton){b.show(false);hideBlockWhileWaitWidget();if(b.cancelCB!=null){b.cancelCB()}}return false;break;case 8:return isTextInput(_ie?_curWin.event:a);break}}}function DialogBoxWidgetResizeModals(k){var j=DialogBoxWidget_current&&DialogBoxWidget_current.isDisplayed();if(j){DialogBoxWidget_current.setDisplay(false);DialogBoxWidget_current.iframe.setDisplay(false)}var f=[];for(var d=0,a=DialogBoxWidget_modals.length;d<a;d++){f[d]=DialogBoxWidget_modals[d].display;DialogBoxWidget_modals[d].display="none"}var c=documentWidth()+"px";var b=documentHeight()+"px";if(j){DialogBoxWidget_current.setDisplay(true);DialogBoxWidget_current.iframe.setDisplay(true)}for(var d=0,a=DialogBoxWidget_modals.length;d<a;d++){var g=DialogBoxWidget_modals[d];g.display=f[d];g.width=c;g.height=b}}function DialogBoxWidget_center(){var i=this;var f={modalDisplay:i.modal.css.display,layerDisplay:i.css.display};i.modal.css.display="none";i.css.display="none";var b=getScrollY(),c=getScrollX();i.modal.css.display=f.modalDisplay;i.css.display="block";var a=i.layer.offsetHeight,e=i.layer.offsetWidth;i.css.display=f.layerDisplay;var g=(winHeight()-a)/2;g=(g<0)?0:g;var d=(winWidth()-e)/2;d=(d<0)?0:d;i.move(Math.max(0,c+d),Math.max(0,b+g));i.placeIframe()}function DialogBoxWidget_Show(sh){with(this){m_sty=modal.css;l_sty=css;if(sh){if(!this.iframe){this.iframe=newWidget(getDynamicBGIFrameLayer().id);this.iframe.init()}oldCurrent=DialogBoxWidget_current;DialogBoxWidget_current=this;if(_ie){layer.onkeydown=eval("_curWin."+_codeWinName+".DialogBoxWidget_keypress");modal.layer.onkeydown=eval("_curWin."+_codeWinName+".DialogBoxWidget_keypress");window.attachEvent("onresize",eval("DialogBoxWidget_onWindowResize"))}else{_curDoc.addEventListener("keydown",eval("_curWin."+_codeWinName+".DialogBoxWidget_keypress"),false);window.addEventListener("resize",eval("DialogBoxWidget_onWindowResize"),false)}oldMouseDown=_curDoc.onmousedown;_curDoc.onmousedown=null;hideBlockWhileWaitWidget()}else{DialogBoxWidget_current=oldCurrent;oldCurrent=null;if(_ie){layer.onkeydown=null;modal.layer.onkeydown=null;window.detachEvent("onresize",eval("DialogBoxWidget_onWindowResize"))}else{_curDoc.removeEventListener("keydown",eval("_curWin."+_codeWinName+".DialogBoxWidget_keypress"),false);window.removeEventListener("resize",eval("DialogBoxWidget_onWindowResize"),false)}_curDoc.onmousedown=oldMouseDown}var sameState=(layer.isShown==sh);if(sameState){return}layer.isShown=sh;if(sh){if(_curWin.DialogBoxWidget_zindex==null){_curWin.DialogBoxWidget_zindex=1000}this.iframe.css.zIndex=_curWin.DialogBoxWidget_zindex++;m_sty.zIndex=_curWin.DialogBoxWidget_zindex++;l_sty.zIndex=_curWin.DialogBoxWidget_zindex++;DialogBoxWidget_modals[DialogBoxWidget_modals.length]=m_sty;m_sty.display="";l_sty.display="block";this.iframe.setDisplay(true);holdBGIFrame(this.iframe.id);DialogBoxWidgetResizeModals();this.height=layer.offsetHeight;this.width=layer.offsetWidth;if(_small&&height){if(divLayer==null){divLayer=getLayer("div_dialog_"+id)}if(divLayer){divLayer.style.overflow="auto";divLayer.style.height=(winHeight()<height)?(winHeight()-40):getContainerHeight();divLayer.style.width=(_moz?width+20:getContainerWidth())}resize(null,((winHeight()<height)?(winHeight()-10):null))}if(isHidden(layer)){this.center()}if(!_small&&this.resizeCB){this.resizeCB(width,height)}}else{var l=DialogBoxWidget_modals.length=Math.max(0,DialogBoxWidget_modals.length-1);m_sty.width="1px";m_sty.height="1px";m_sty.display="none";l_sty.display="none";if(this.iframe!=null){this.iframe.setDisplay(false);releaseBGIFrame(this.iframe.id)}}modal.show(sh);firstLink.show(sh);lastLink.show(sh);oldShow(sh);if(DialogBoxWidget_current!=null&&sh==true){DialogBoxWidget_current.focus()}if(!sh&&closeCB!=null){closeCB()}}}function DialogBoxWidget_onWindowResize(){DialogBoxWidgetResizeModals()}function DialogBoxWidget_unload(){if(this.iframe){releaseBGIFrame(this.iframe.id)}}function DialogBoxWidget_keepFocus(b){var a=DialogBoxWidget_instances[b];if(a){a.focus()}}function DialogBoxWidget_focus(){with(this){if(titleLayer==null){titleLayer=getLayer("titledialog_"+id)}if(_saf&&webKitFocusElem&&webKitFocusElem.focus){webKitFocusElem.focus()}else{if(titleLayer.focus){titleLayer.focus()}}}}function newPromptDialog(a,i,k,g,d,j,l,c,f,e){var b=newDialogBoxWidget(a,i,300,null,PromptDialog_defaultCB,PromptDialog_cancelCB,f,e);b.text=k;b.getHTML=PromptDialog_getHTML;b.yes=g?newButtonWidget(a+"_yesBtn",g,'PromptDialog_yesCB("'+b.id+'")',70):null;b.no=d?newButtonWidget(a+"_noBtn",d,'PromptDialog_noCB("'+b.id+'")',70):null;b.yesCB=l;b.noCB=c;b.promptType=j;b.txtLayer=null;b.imgLayer=null;b.setPromptType=PromptDialog_setPromptType;b.setText=PromptDialog_setText;if(b.yes){b.attachDefaultButton(b.yes)}else{if(b.no){b.attachDefaultButton(b.no)}}return b}function PromptDialog_getimgPath(b){var a=_skin;switch(b){case _promptDlgInfo:a+="information_icon.gif";break;case _promptDlgWarning:a+="warning_icon.gif";break;default:a+="critical_icon.gif";break}return a}function PromptDialog_getimgAlt(b){var a="";return a}function PromptDialog_setPromptType(b){var a=this;if(a.imgLayer==null){a.imgLayer=getLayer("dlg_img_"+a.id)}a.imgLayer.src=PromptDialog_getimgPath(b);a.imgLayer.alt=PromptDialog_getimgAlt(b)}function PromptDialog_setText(b){var a=this;a.text=b;if(a.txtLayer==null){a.txtLayer=getLayer("dlg_txt_"+a.id)}a.txtLayer.innerHTML='<div tabindex="0">'+convStr(b,false,true)+"</div>"}function PromptDialog_getHTML(){var c=this;var b=PromptDialog_getimgPath(c.promptType);var a=PromptDialog_getimgAlt(c.promptType);return c.beginHTML()+'<table class="dialogzone" width="290" cellpadding="0" cellspacing="5" border="0"><tr><td><table class="dialogzone" cellpadding="5" cellspacing="0" border="0"><tr><td align="right" width="32" >'+img(b,32,32,null,'id="dlg_img_'+c.id+'"',a)+'</td><td></td><td id="dlg_txt_'+c.id+'" align="left" tabindex="0">'+convStr(c.text,false,true)+"</td></tr></table></td></tr><tr><td>"+getSep()+'</td></tr><tr><td align="right"><table cellpadding="5" cellspacing="0" border="0"><tr>'+(c.yes?"<td>"+c.yes.getHTML()+"</td>":"")+(c.no?"<td>"+c.no.getHTML()+"</td>":"")+"</tr></table></td></tr></table>"+c.endHTML()}function PromptDialog_defaultCB(){var o=this;if(o.yesCB){if(typeof o.yesCB!="string"){o.yesCB()}else{eval(o.yesCB)}}this.show(false)}function PromptDialog_cancelCB(){var o=this;if(o.noCB){if(typeof o.noCB!="string"){o.noCB()}else{eval(o.noCB)}}this.show(false)}function PromptDialog_yesCB(a){DialogBoxWidget_instances[a].defaultCB()}function PromptDialog_noCB(a){DialogBoxWidget_instances[a].cancelCB()}function newWaitDialogBoxWidget(a,j,e,g,c,i,l,k,f){var d=250;var m=150;if(j<d){j=d}if(e<m){e=m}var b=newDialogBoxWidget(a,g,j,null,null,WaitDialogBoxWidget_cancelCB,f,true);b.pad=5;b.frZone=newFrameZoneWidget(a+"_frZone",null,null);b.showLabel=(l!=null)?l:false;b.showCancel=(c!=null)?c:false;b.label=newWidget(a+"_label");b.label.text=k;if(b.showCancel){b.cancelButton=newButtonWidget(a+"_cancelButton",_cancelButtonLab,CancelButton_cancelCB);b.cancelButton.par=b}else{b.cancelButton={};b.cancelButton.init=function(){};b.cancelButton.setDisplay=function(n){};b.cancelButton.setDisabled=function(n){};b.cancelButton.getHTML=function(){return""}}b.cancelCB=i;b.oldDialogBoxInit=b.init;b.init=WaitDialogBoxWidget_init;b.getHTML=WaitDialogBoxWidget_getHTML;b.setShowCancel=WaitDialogBoxWidget_setShowCancel;b.setShowLabel=WaitDialogBoxWidget_setShowLabel;return b}function WaitDialogBoxWidget_init(){var a=this;a.oldDialogBoxInit();a.frZone.init();a.label.init();a.label.setDisplay(a.showLabel);a.cancelButton.init();a.cancelButton.setDisplay(a.showCancel)}function WaitDialogBoxWidget_getHTML(){var b=this,a="";a+=b.beginHTML();a+='<table border="0" cellspacing="0" cellpadding="0" width="100%"><tbody>';a+='<tr><td align="center" valign="top">'+b.frZone.beginHTML();a+='<table border="0" cellspacing="0" cellpadding="0" width="100%"><tbody><tr><td align="center" style="padding-top:5px;">'+img(_skin+"wait01.gif",200,40)+'</td></tr><tr><td align="left" style="padding-left:2px;padding-right:2px;padding-top:5px;"><div id="'+b.label.id+'" class="iconText" style="wordWrap:break_word;text-align:center;">'+convStr(b.label.text,false,true)+"</div></td></tr></tbody></table>";a+=b.frZone.endHTML()+"</td></tr>";a+='<tr><td align="right" valign="middle" style="padding-top:5px;padding-right:9px">'+b.cancelButton.getHTML()+"</td></tr>";a+="</tbody></table>";a+=b.endHTML();return a}function WaitDialog_FrameZoneWidget_beginHTML(){var a=this;return'<table class="waitdialogzone" style="'+sty("width",a.w)+sty("height",a.h)+'" id="'+a.id+'" cellspacing="0" cellpadding="0" border="0"><tbody><tr><td valign="top" class="dialogzone" id="frame_cont_'+a.id+'">'}function WaitDialog_FrameZoneWidget_endHTML(){var a=this;return"</td></tr></tbody></table>"}function WaitDialogBoxWidget_setShowCancel(a,c){var b=this;b.showCancel=a;b.cancelButton.setDisabled(false);b.cancelButton.setDisplay(a);b.cancelCB=c}function WaitDialogBoxWidget_setShowLabel(a,c){var b=this;b.showLabel=a;b.label.text=c;b.label.setHTML(c);b.label.setDisplay(a)}function WaitDialogBoxWidget_cancelCB(){var a=this;if(a.cancelCB!=null){a.cancelCB();a.cancelButton.setDisabled(true)}}function CancelButton_cancelCB(){var a=this;if(a.par.cancelCB!=null){a.par.cancelCB();a.par.cancelButton.setDisabled(true)}}if(typeof bobj=="undefined"){bobj={}}if(typeof bobj.constants=="undefined"){bobj.constants={modalLayerIndex:1000}}bobj.uniqueId=function(){return"bobjid_"+(++bobj.uniqueId._count)};if(typeof bobj.uniqueId._count=="undefined"){bobj.uniqueId._count=new Date().getTime()}bobj.updateIf=function(g,c,e){if(c===null){c={}}for(var d=1,a=arguments.length;d<a;d++){var f=arguments[d];if(typeof(f)!="undefined"&&f!==null){for(var b in f){if(g(c,e,b)){c[b]=f[b]}}}}return c};bobj.fillIn=function(a,b){var c=function(e,f,d){return(typeof(e[d])=="undefined")};bobj.updateIf(c,a,b)};bobj.isObject=function(a){return(a&&typeof a=="object")};bobj.isArray=function(b){if(bobj.isObject(b)){try{return b.constructor==Array}catch(a){return false}}return false};bobj.isString=function(a){return(typeof(a)=="string")};bobj.isNumber=function(a){return typeof(a)=="number"&&isFinite(a)};bobj.isBoolean=function(a){return typeof a=="boolean"};bobj.isFunction=function(a){return typeof(a)=="function"};bobj.isBorderBoxModel=function(){if(typeof bobj.isBorderBoxModel._cachedValue=="undefined"){if(document.body){var a=document.createElement("div");a.style.width="10px";a.style.padding="1px";a.style.position="absolute";a.style.visibility="hidden";document.body.appendChild(a);bobj.isBorderBoxModel._cachedValue=(a.offsetWidth==10);document.body.removeChild(a)}else{return _ie&&bobj.isQuirksMode()}}return bobj.isBorderBoxModel._cachedValue};bobj.isQuirksMode=function(){return(document.compatMode!="CSS1Compat")};bobj.setVisualStyle=function(b,c){if(b===null||c===null){return}var a=b.style;if(c.className){b.className=c.className}MochiKit.Iter.forEach(["background","borderWidth","borderStyle","borderColor","fontFamily","fontStyle","fontSize","fontWeight","textDecoration","color","width","height","left","top"],function(d){if(c[d]){a[d]=c[d]}})};bobj.setOuterSize=function(e,b,d,c){var g=null;var a=e.style;if(a.display=="none"){g={visibility:a.visibility,position:a.position,display:"none"};a.visibility="hidden";a.position="absolute";a.display=""}function f(i){var j=MochiKit.DOM.getStyle(e,i);if(bobj.isString(j)&&j.substring(j.length-2=="px")){return(parseInt(j,10)||0)}return 0}if(bobj.isNumber(b)){if(!bobj.isBorderBoxModel()){b-=f("border-left-width");b-=f("border-right-width");b-=f("padding-left");b-=f("padding-right");if(c){b-=f("margin-left");b-=f("margin-right")}}a.width=Math.max(0,b)+"px"}if(bobj.isNumber(d)){if(!bobj.isBorderBoxModel()){if(c){d-=f("margin-top");d-=f("margin-bottom")}d-=f("border-top-width");d-=f("border-bottom-width");d-=f("padding-top");d-=f("padding-bottom")}a.height=Math.max(0,d)+"px"}if(g){a.display=g.display;a.position=g.position;a.visibility=g.visibility}};bobj.getContainer=function(a){if(a&&a.layer){return a.layer.parentNode}return null};bobj.checkParent=function(d,b){var a=false;if(d&&b){b=b.toUpperCase();var c=d.parentNode;while(c){if(c.tagName==b){a=true;break}c=c.parentNode}}return a};bobj.slice=function(a,f,c){if(bobj.isArray(a)){return a.slice(f,c)}else{if(MochiKit.Base.isArrayLike(a)){var b=[];var e=a.length;if(bobj.isNumber(c)&&c<e){e=c}f=Math.max(f,0);for(var d=f;d<e;++d){b.push(a[d])}return b}}return null};bobj.extractRange=function(d,g,a){if(d&&bobj.isNumber(g)){if(!bobj.isNumber(a)||a>d.length){a=d.length}g=Math.max(0,g);if(g<a){var c=0,f=g;var b=a,e=d.length;if(d.substring){return(d.substring(c,f)+d.substring(b,e))}else{return bobj.slice(d,c,f).concat(bobj.slice(d,b,e))}}}return d};bobj.unitValue=function(b,a){if(bobj.isNumber(b)){return b+(a||"px")}return b};bobj.evalInWindow=function(expression){if(window.execScript){return window.execScript(expression)}else{return MochiKit.Base.bind(eval,window,expression).call()}};bobj.loadJSResourceAndExecCallBack=function(d,e){if(!d||!e){return}if(!d.isLoaded){var b=function(g,i,f){g.isLoaded=true;bobj.evalInWindow(f.responseText);i.apply()};var c=MochiKit.Async.getXMLHttpRequest();c.open("GET",bobj.crvUri(d.path),true);c.setRequestHeader("Accept","application/x-javascript,application/javascript, text/javascript");var a=MochiKit.Async.sendXMLHttpRequest(c);a.addCallback(MochiKit.Base.bind(b,this,d,e))}else{setTimeout(function(){e.apply()},0)}};bobj.trimLeft=function(a){a=a||"";return a.replace(/^\s+/g,"")};bobj.trimRight=function(a){a=a||"";return a.replace(/\s+$/g,"")};bobj.trim=function(a){return bobj.trimLeft(bobj.trimRight(a))};bobj.equals=function(b,a){if(typeof(b)!=typeof(a)){return false}if(bobj.isObject(b)){var d=true;for(var c in b){d=d&&bobj.equals(b[c],a[c])}return d}else{return b==a}};bobj.includeLink=function(b){var c=document.getElementsByTagName("head")[0];var a=document.body;var d=document.createElement("link");d.setAttribute("rel","stylesheet");d.setAttribute("type","text/css");d.setAttribute("href",b);if(c){c.appendChild(d)}else{if(a){a.appendChild(d)}}};bobj.includeCSSLinksAndExecuteCallback=function(c,e){if(c==null||c.length<1){e.apply();return}var b=function(){var f=arguments.callee;var g=f.callback;f.hrefCount--;if(f.hrefCount==0){g.apply()}};b.hrefCount=c.length;b.callback=e;for(var d=0,a=c.length;d<a;d++){bobj.includeCSSLinkAndExecuteCallback(c[d],b)}};bobj.includeCSSLinkAndExecuteCallback=function(a,i){var d=encodeURIComponent(a);if(getLayer(d)){i.apply();return}var f=function(m,l,k){bobj.addStyleSheet(k.responseText,l);m.apply()};var c=function(k){k.apply()};var g=MochiKit.Async.getXMLHttpRequest();g.open("GET",a,true);g.setRequestHeader("Accept","text/css");var j=MochiKit.Async.sendXMLHttpRequest(g);var b=MochiKit.Base.bind(f,this,i,d);var e=MochiKit.Base.bind(c,this,i);j.addCallbacks(b,e)};bobj.addStyleSheet=function(c,e){var d=document.createElement("style");d.setAttribute("type","text/css");if(e){d.setAttribute("id",e)}if(d.styleSheet){d.styleSheet.cssText=c}else{d.appendChild(document.createTextNode(c))}var b=document.getElementsByTagName("head");var a=document.getElementsByTagName("body");if(b&&b[0]){b[0].appendChild(d)}else{if(a&&a[0]){a[0].appendChild(d)}}};bobj.removeAllChildElements=function(a){if(a){while(a.lastChild){a.removeChild(a.lastChild)}}};bobj.getValueHashCode=function(c,a){var b=bobj.crv.params.DataTypes;switch(c){case b.BOOLEAN:case b.CURRENCY:case b.NUMBER:case b.STRING:return""+a;case b.TIME:return""+a.h+","+a.min+","+a.s+","+a.ms;case b.DATE:return""+a.y+","+a.m+","+a.d;case b.DATE_TIME:return""+a.y+","+a.m+","+a.d+","+a.h+","+a.min+","+a.s+","+a.ms}};bobj.getElementByIdOrName=function(b){if(!b){return null}var c=document.getElementById(b);if(c){return c}var a=document.getElementsByName(b);if(a&&a.length>0){return a[0]}return null};bobj.getRect=function(d,b,a,c){return"rect("+d+"px, "+b+"px,"+a+"px,"+c+"px)"};bobj.getBodyScrollDimension=function(){var a=0;var c=0;var b=document.getElementsByTagName("Body");if(b&&b[0]){a=b[0].scrollWidth;c=b[0].scrollHeight}return{w:a,h:c}};bobj.disableTabbingKey=function(b,a){if(b){b.tabIndex=a?-1:0}};bobj.getStringWidth=function(b,a,e){if(document.body){var d=document.createElement("span");d.appendChild(document.createTextNode(b));d.style.position="absolute";d.style.visibility="hidden";if(a){d.style.fontFamily=a}if(e){d.style.fontSize=e}document.body.appendChild(d);var c=d.offsetWidth;document.body.removeChild(d);return c}return 0};bobj.deleteWidget=function(b){if(b&&b.widx){if(b.layer){b.layer.click=null;b.layer.onmouseup=null;b.layer.onmousedown=null;b.layer.onmouseover=null;b.layer.onmousemove=null;b.layer.onmouseout=null;b.layer.onchange=null;b.layer.onfocus=null;b.layer.onkeydown=null;b.layer.onkeyup=null;b.layer.onkeypress=null;var a=b.layer.parentNode;if(a){a.removeChild(b.layer)}delete b.layer}delete b.css;delete _widgets[b.widx];_widgets[b.widx]=null;delete b}};bobj.cloneArray=function(a){return a.slice()};bobj.bindFunctionToObject=function(a,b){return function(){return a.apply(b,arguments)}};bobj.extendClass=function(a,d,b){MochiKit.Base.update(a,d);a.superClass={};for(var c in b){a.superClass[c]=bobj.bindFunctionToObject(b[c],a)}};bobj.displayElementWithAnimation=function(a){if(a!=null){MochiKit.DOM.setOpacity(a,0);MochiKit.Style.setDisplayForElement("block",a);new MochiKit.Visual.appear(a,{duration:0.5})}};bobj.getHiddenElementDimensions=function(d){var c={w:0,h:0};if(d){var a=document.body;var e=d.cloneNode(true);var b=e.style;b.display="";b.visibility="hidden";b.width="";b.height="";b.position="absolute";b.left="-1000px";b.top="-1000px";a.appendChild(e);c={w:e.offsetWidth,h:e.offsetHeight};a.removeChild(e)}return c};bobj.hasPDFReaderWithJSFunctionality=function(){if(navigator.plugins){var b=navigator.plugins;for(var c=0,a=b.length;c<a;c++){if(b[c].description.indexOf("Adobe PDF Plug-In")!=-1){return true}}}try{var f=new ActiveXObject("AcroPDF.PDF.1");if(f){return true}}catch(d){}return false};if(typeof bobj.crv.PrintUI=="undefined"){bobj.crv.PrintUI={}}if(typeof bobj.crv.ExportUI=="undefined"){bobj.crv.ExportUI={}}if(typeof bobj.crv.ErrorDialog=="undefined"){bobj.crv.ErrorDialog={}}if(typeof bobj.crv.ReportProcessingUI=="undefined"){bobj.crv.ReportProcessingUI={}}bobj.crv.newPrintUI=function(c){if(!c.id){c=MochiKit.Base.update({id:bobj.uniqueId()},c)}var d=c.submitBtnLabel;if(!d){d=L_bobj_crv_submitBtnLbl}var b=c.infoTitle;if(!b){b=L_bobj_crv_PrintInfoTitle}var a=c.dialogTitle;if(!a){if(c.isActxPrinting){a=L_bobj_crv_ActiveXPrintDialogTitle}else{a=L_bobj_crv_PDFPrintDialogTitle}}var f=c.infoMsg;if(!f){f=L_bobj_crv_PrintInfo1;f+="\n";f+=L_bobj_crv_PrintInfo2}var e=newDialogBoxWidget(c.id+"_dialog",a,300,100,null,bobj.crv.PrintUI._cancel,false);e.infoMsg=f;e.infoTitle=b;e.actxId=e.id+"_actx";e.actxContainerId=e.id+"_actxdiv";e._processingPrinting=false;e._initOld=e.init;e._showOld=e.show;if(!c.isActxPrinting){e._fromBox=newIntFieldWidget(e.id+"_fromBox",null,null,null,null,true,"",50);e._fromBox.setDisabled=bobj.crv.PrintUI.disabledTextFieldWidget;e._toBox=newIntFieldWidget(e.id+"_toBox",null,null,null,null,true,"",50);e._toBox.setDisabled=bobj.crv.PrintUI.disabledTextFieldWidget;e._submitBtn=newButtonWidget(e.id+"_submitBtn",d,MochiKit.Base.bind(bobj.crv.PrintUI._submitBtnCB,e));e._submitBtn.setDelayCallback(false);e._allRadio=newRadioWidget(e.id+"_allRadio",e.id+"_grp",L_bobj_crv_PrintAllLbl,MochiKit.Base.bind(bobj.crv.PrintUI.disabledPageRange,e,true));e._allRadio.layerClass="dlgContent";e._rangeRadio=newRadioWidget(e.id+"_rangeRadio",e.id+"_grp",L_bobj_crv_PrintPagesLbl,MochiKit.Base.bind(bobj.crv.PrintUI.disabledPageRange,e,false));e._rangeRadio.layerClass="dlgContent"}e.widgetType="PrintUI";bobj.fillIn(e,c);MochiKit.Base.update(e,bobj.crv.PrintUI);return e};bobj.crv.PrintUI.disabledTextFieldWidget=function(a){TextFieldWidget_setDisabled.call(this,a);if(a){MochiKit.DOM.addElementClass(this.layer,"textDisabled")}else{MochiKit.DOM.removeElementClass(this.layer,"textDisabled")}};bobj.crv.PrintUI.disabledPageRange=function(a){if(this._fromBox&&this._toBox){this._fromBox.setDisabled(a);this._toBox.setDisabled(a)}};bobj.crv.PrintUI._submitBtnCB=function(){var b=null;var a=null;if(this._rangeRadio.isChecked()){b=parseInt(this._fromBox.getValue(),10);a=parseInt(this._toBox.getValue(),10);if(!b||!a||(b<0)||(b>a)){alert(L_bobj_crv_PrintPageRangeError);return}}if(this.widgetType=="PrintUI"){MochiKit.Signal.signal(this,"printSubmitted",b,a)}else{MochiKit.Signal.signal(this,"exportSubmitted",b,a,this._comboBox.getSelection().value)}this.show(false)};bobj.crv.PrintUI._getRPSafeURL=function(c){if(!c){return}if(c.indexOf("/")===0){return c}var b=window.location.href;var d=b.lastIndexOf("?");if(d>0){b=b.substring(0,d)}var a=b.lastIndexOf("/");if(a<0){return c}b=b.substring(0,a);return b+"/"+c};bobj.crv.PrintUI._getObjectTag=function(b){var c=[];c.push('<OBJECT width="0" height="0" ID="');c.push(this.actxId);c.push('" CLASSID="CLSID:');c.push(bobj.crv.ActxPrintControl_CLSID);c.push('" CODEBASE="');c.push(this._getRPSafeURL(this.codeBase));c.push("#Version=");c.push(bobj.crv.ActxPrintControl_Version);c.push('" VIEWASTEXT>');c.push('<PARAM NAME="PostBackData" VALUE="');c.push(b);c.push('">');c.push('<PARAM NAME="ServerResourceVersion" VALUE="');c.push(bobj.crv.ActxPrintControl_Version);c.push('">');if(this.lcid){c.push('<PARAM NAME="LocaleID" VALUE="');c.push(this.lcid);c.push('">')}if(this.url){c.push('<PARAM NAME="URL" VALUE="');c.push(this._getRPSafeURL(this.url));c.push('">')}if(this.title){c.push('<PARAM NAME="Title" VALUE="');c.push(this.title);c.push('">')}if(this.maxPage){c.push('<PARAM NAME="MaxPageNumber" VALUE="');c.push(this.maxPage);c.push('">')}if(this.paperOrientation){c.push('<PARAM NAME="PageOrientation" VALUE="');c.push(this.paperOrientation);c.push('">')}if(this.paperSize){c.push('<PARAM NAME="PaperSize" VALUE="');c.push(this.paperSize);c.push('">')}if(this.paperWidth){c.push('<PARAM NAME="PaperWidth" VALUE="');c.push(this.paperWidth);c.push('">')}if(this.paperLength){c.push('<PARAM NAME="PaperLength" VALUE="');c.push(this.paperLength);c.push('">')}if(this.driverName){c.push('<PARAM NAME="PrinterDriverName" VALUE="');c.push(this.driverName);c.push('">')}if(this.useDefPrinter){c.push('<PARAM NAME="UseDefaultPrinter" VALUE="');c.push(this.useDefPrinter);c.push('">')}if(this.useDefPrinterSettings){c.push('<PARAM NAME="UseDefaultPrinterSettings" VALUE="');c.push(this.useDefPrinterSettings);c.push('">')}if(this.sendPostDataOnce){c.push('<PARAM NAME="SendPostDataOnce" VALUE="');c.push(this.sendPostDataOnce);c.push('">')}c.push("</OBJECT>");c.push('<table id="');c.push(this.actxId);c.push('_wait" border="0" cellspacing="0" cellpadding="0" width="100%" ><tbody>');c.push('<tr><td align="center" valign="top">');var e=this;var d=e.getContainerWidth()-10;var a=e.getContainerHeight()-(2*e.pad+21+10);c.push('<table style="');c.push(sty("width",d));c.push(sty("height",a));c.push('" id="frame_table_');c.push(e.id);c.push('" cellspacing="0" cellpadding="0" border="0"><tbody><tr><td valign="top" class="dlgFrame" style="padding:5px" id="frame_cont_');c.push(e.id);c.push('">');c.push('<table border="0" cellspacing="0" cellpadding="0" width="100%"><tbody>');c.push('<tr><td align="center" style="padding-top:5px;">');c.push(img(_skin+"wait01.gif",200,40));c.push("</td></tr>");c.push('<tr><td align="left" style="padding-left:2px;padding-right:2px;padding-top:5px;">');c.push('<div class="icontext" style="wordWrap:break_word;">');c.push(convStr(L_bobj_crv_PrintControlProcessingMessage,false,true));c.push("</div></td></tr></tbody></table>");c.push("</td></tr></tbody></table>");c.push("</td></tr></tbody></table>");return c.join("")};bobj.crv.PrintUI._cancel=function(){if(this.isActxPrinting){document.getElementById(this.actxContainerId).innerHTML="";this._processingPrinting=false}};bobj.crv.PrintUI._processPrinting=function(){if(!this._processingPrinting){var b=document.getElementById(this.actxId);var a=document.getElementById(this.actxId+"_wait");if(b&&a){b.width="100%";b.height="100%";a.style.display="none"}this._processingPrinting=true}};bobj.crv.PrintUI.show=function(b,a){this._processingPrinting=false;if(b){if(!this.layer){targetApp(this.getHTML());this.init()}if(this.isActxPrinting){document.getElementById(this.actxContainerId).innerHTML=this._getObjectTag(a)}this._showOld(true)}else{if(this.layer){this._showOld(false)}}};bobj.crv.PrintUI.init=function(){this._initOld();if(!this.isActxPrinting){this._fromBox.init();this._toBox.init();this._submitBtn.init();this._allRadio.init();this._rangeRadio.init();this._allRadio.check(true);this._toBox.setDisabled(true);this._fromBox.setDisabled(true);if(this.widgetType=="ExportUI"){this._updateExportList()}}};bobj.crv.PrintUI.getHTML=function(){var b=bobj.html;var c=this;var a=c.beginHTML();if(!this.isActxPrinting){a+="<table cellspacing=0 cellpadding=0 border=0><tr><td><div class='dlgFrame'><table cellspacing=0 cellpadding=0 border=0 style='height:"+(this.height*0.9)+"px;width:"+this.width+"px;'><tr><td valign='top' class='naviBarFrame naviFrame'>"+(this.isExporting?this._getExportList():"")+"<fieldset style='border:0px;padding:0px'><legend style='position:relative;"+(_ie?"margin:0px -7px":"")+"'><table datatable='0' style='width:100%;line-height:10px;'><tr>"+(_ie?"<td class='dialogTitleLevel2'><label>":"<td class='dialogTitleLevel2'><label>")+L_bobj_crv_PrintRangeLbl+"</label></td></tr></table></legend><div style='margin:10px 25px;'>"+c._allRadio.getHTML()+c._rangeRadio.getHTML()+"<div style='padding-left:25px'><table class=dlgContent datatable='0'><tr><td align=right><label for='"+c._fromBox.id+"'> "+L_bobj_crv_PrintFromLbl+"</label></td><td align=left> "+c._fromBox.getHTML()+"</td></tr><tr><td align=right><label for='"+c._toBox.id+"'> "+L_bobj_crv_PrintToLbl+"</label></td><td align=left>"+c._toBox.getHTML()+"</td></tr></table></div></div></fieldset>"+(!this.isExporting?"<table style='width:100%;line-height:10px;'><tr><td class='dialogTitleLevel2' tabIndex=0><label>"+this.infoTitle+"</label></td></tr></table><div style='margin:10px 0px 10px 25px;' class='dlgHelpText'>"+this.infoMsg+"</div>":"")+"</td></tr></table></div></td></tr><tr><td align='right' valign='top'><table style='margin:6px 9px 0px 0px' cellspacing=0 cellpadding=0 border=0><tbody><tr><td>"+this._submitBtn.getHTML()+"</td></tbody></tr></table></td></tr></table>"}else{a+="<div id='"+this.actxContainerId+"'></div><script for=\""+this.actxId+'" EVENT="Finished(status, statusText)" language="javascript">getWidgetFromID("'+this.id+'").show(false);<\/script><script for="'+this.actxId+'" EVENT="PrintingProgress(pageNumber)" language="javascript">getWidgetFromID("'+this.id+'")._processPrinting();<\/script>'}a+=c.endHTML();a+=bobj.crv.getInitHTML(this.widx);return a};bobj.crv.newExportUI=function(a){a=MochiKit.Base.update({submitBtnLabel:L_bobj_crv_ExportBtnLbl,dialogTitle:L_bobj_crv_ExportDialogTitle,infoTitle:L_bobj_crv_ExportInfoTitle,infoMsg:L_bobj_crv_PrintInfo1,isExporting:true},a);var b=bobj.crv.newPrintUI(a);b._comboBox=newCustomCombo(b.id+"_combo",MochiKit.Base.bind(bobj.crv.ExportUI._onSelectFormat,b),false,270,L_bobj_crv_ExportFormatLbl,_skin+"../transp.gif",0,14);if(b._comboBox){b._comboBox.icon.border=0;b._comboBox.icon.h=14;b._comboBox.arrow.h=12;b._comboBox.arrow.dy+=2;b._comboBox.arrow.disDy+=2}b.widgetType="ExportUI";MochiKit.Base.update(b,bobj.crv.ExportUI);return b};bobj.crv.ExportUI._onSelectFormat=function(){var a=this._comboBox.getSelection().value;if(a=="CrystalReports"||a=="RPTR"||a=="RecordToMSExcel"||a=="RecordToMSExcel2007"||a=="CharacterSeparatedValues"||a=="XML"){this._fromBox.setDisabled(true);this._toBox.setDisabled(true);this._rangeRadio.check(false);this._rangeRadio.setDisabled(true);this._allRadio.check(true)}else{this._rangeRadio.setDisabled(false)}};bobj.crv.ExportUI.update=function(a){if(!a||a.cons!=="bobj.crv.newExportUI"){return}this.availableFormats=a.args.availableFormats;if(this._comboBox.initialized()){this._updateExportList()}};bobj.crv.ExportUI._updateExportList=function(){if(!this._comboBox.initialized()){this._comboBox.init()}this._updateComboItems();var a=this._comboBox.getItemByIndex(0);if(a!=null){this._comboBox.selectItem(a)}this._onSelectFormat()};bobj.crv.ExportUI._updateComboItems=function(){this._comboBox.removeAllMenuItems();var a=(bobj.isArray(this.availableFormats)?this.availableFormats.length:0);for(var b=0;b<a;b++){var c=this.availableFormats[b];this._comboBox.add(c.name,c.value,c.isSelected)}};bobj.crv.ExportUI._getExportList=function(){return"<table datatable='0' style='width:100%;line-height:10px;'><tr>"+(_ie?"<td class='dialogTitleLevel2'><label>":"<td class='dialogTitleLevel2'><label>")+L_bobj_crv_ExportFormatLbl+"</label></td></tr></table><div style='margin:10px 25px;'>"+this._comboBox.getHTML()+"</div>"};bobj.crv.ErrorDialog.getInstance=function(){if(!bobj.crv.ErrorDialog.__instance){bobj.crv.ErrorDialog.__instance=bobj.crv.newErrorDialog()}return bobj.crv.ErrorDialog.__instance};bobj.crv.newErrorDialog=function(a){a=MochiKit.Base.update({id:bobj.uniqueId(),title:L_bobj_crv_Error,text:null,detailText:null,okLabel:L_bobj_crv_OK,promptType:_promptDlgCritical},a);var b=newPromptDialog(a.id,a.title,a.text,a.okLabel,null,a.promptType,null,null,true,true);b.widgetType="ErrorDialog";bobj.fillIn(b,a);b._promptDlgInit=b.init;b._promptDialogSetText=b.setText;b._promptDialogShow=b.show;b._promptDialogSetTitle=b.setTitle;b._promptDialogSetPromptType=b.setPromptType;MochiKit.Base.update(b,bobj.crv.ErrorDialog);b.noCB=MochiKit.Base.bind(b._onClose,b);b.yesCB=b.noCB;b._detailBtn=newIconWidget(b.id+"_detailBtn",bobj.skinUri("../help.gif"),MochiKit.Base.bind(bobj.crv.ErrorDialog._onDetailBtnClick,b),L_bobj_crv_showDetails,L_bobj_crv_showDetails,16,16,0,0,22,0,true);return b};bobj.crv.ErrorDialog.init=function(){this._promptDlgInit();this._detailBtn.init();this._detailRow=document.getElementById(this.id+"_detRow");this._detailArea=document.getElementById(this.id+"_detArea");if(!this.detailText){this._detailBtn.show(false)}};bobj.crv.ErrorDialog.getHTML=function(){var j=bobj.html.TABLE;var b=bobj.html.TBODY;var e=bobj.html.TR;var l=bobj.html.TD;var m=bobj.html.PRE;var i=bobj.html.DIV;var k=PromptDialog_getimgPath(this.promptType);var f=PromptDialog_getimgAlt(this.promptType);var a="320";var d="300px";var g="100px";var c=j({"class":"dlgBody",width:a,cellpadding:"0",cellspacing:"5",border:"0"},b(null,e(null,l(null,j({"class":"dlgBody",cellpadding:"5",cellspacing:"0",border:"0"},b(null,e(null,l({align:"right",width:"32"},img(k,32,32,null,'id="dlg_img_'+this.id+'"',f)),l(),l({id:"dlg_txt_"+this.id,align:"left"},i({tabindex:"0"},convStr(this.text,false,true)))))))),e({id:this.id+"_detRow",style:{display:"none"}},l(null,i({"class":"infozone",style:{width:d,height:g,overflow:"auto"}},m({id:this.id+"_detArea"},this.detailText)))),e(null,l(null,getSep())),e(null,l(null,j({cellpadding:"5",cellspacing:"0",border:"0",width:"100%"},b(null,e(null,l({align:"left"},this._detailBtn.getHTML()),l({align:"right"},this.yes.getHTML()))))))));return this.beginHTML()+c+this.endHTML()};bobj.crv.ErrorDialog.setText=function(c,a){this.text=c;this.detailText=a;if(this.layer){this._promptDialogSetText(c||"");if(this._detailArea){this._detailArea.innerHTML=a||""}var b=a?true:false;this._detailBtn.show(b);if(!b){this.showDetails(false)}}};bobj.crv.ErrorDialog.setTitle=function(a){this.title=a;if(this.layer){this._promptDialogSetTitle(a||"")}};bobj.crv.ErrorDialog.setPromptType=function(a){this.promptType=a;if(this.layer){this._promptDialogSetPromptType(a)}};bobj.crv.ErrorDialog.show=function(a,b){if(typeof a=="undefined"){a=true}if(a){this._closeCB=b;if(!this.layer){targetApp(this.getHTML());this.init()}this.layer.onkeyup=DialogBoxWidget_keypress;DialogBoxWidget_keypress=MochiKit.Base.noop;this._promptDialogShow(true)}else{if(this.layer){this._closeCB=null;this._promptDialogShow(false)}}};bobj.crv.ErrorDialog.showDetails=function(a){if(typeof a=="undefined"){a=true}if(this._detailRow&&this._detailBtn){if(a){this._detailRow.style.display="";this._detailBtn.changeText(L_bobj_crv_hideDetails)}else{this._detailRow.style.display="none";this._detailBtn.changeText(L_bobj_crv_showDetails)}}};bobj.crv.ErrorDialog._onDetailBtnClick=function(){if(this._detailRow){this.showDetails(this._detailRow.style.display=="none")}};bobj.crv.ErrorDialog._onClose=function(){if(this._closeCB){this._closeCB();this._closeCB=null}DialogBoxWidget_keypress=this.layer.onkeyup;this.layer.onkeyup=null};bobj.crv.newReportProcessingUI=function(b){b=MochiKit.Base.update({id:bobj.uniqueId(),delay:250,message:L_bobj_crv_ReportProcessingMessage},b);var e=document.createElement("div");e.style.visibility="hidden";e.innerHTML=b.message;var a=e.innerHTML;e=null;var c=newWaitDialogBoxWidget(b.id,0,0,"",false,bobj.crv.ReportProcessingUI.cancelCB,true,a,true);c.widgetType="ReportProcessingUI";c.delay=b.delay;MochiKit.Base.update(c,bobj.crv.ReportProcessingUI);return c};bobj.crv.reportProcessingDialog=null;bobj.crv.timerID=null;bobj.crv.ReportProcessingUI.cancelCB=function(){bobj.crv.reportProcessingDialog.cancelled=true;if(bobj.crv.reportProcessingDialog.deferred!==null){bobj.crv.reportProcessingDialog.deferred.cancel()}bobj.crv.reportProcessingDialog.cancelShow()};bobj.crv.ReportProcessingUI.wasCancelled=function(){return bobj.crv.reportProcessingDialog.cancelled};bobj.crv.ReportProcessingUI._prepareToShow=function(){if(bobj.crv.reportProcessingDialog!==null){bobj.crv.reportProcessingDialog.cancelShow()}if(!this.layer){append2(document.body,this.getHTML());this.init()}this.deferred=null;bobj.crv.reportProcessingDialog=this};bobj.crv.ReportProcessingUI.Show=function(){this._prepareToShow();bobj.crv.reportProcessingDialog.show(true)};bobj.crv.ReportProcessingUI.delayedShow=function(){this._prepareToShow();bobj.crv.timerID=setTimeout("bobj.crv._showReportProcessingDialog ()",bobj.crv.reportProcessingDialog.delay)};bobj.crv.ReportProcessingUI.cancelShow=function(){if(bobj.crv.timerID){clearTimeout(bobj.crv.timerID)}if(bobj.crv.reportProcessingDialog){bobj.crv.reportProcessingDialog.show(false)}bobj.crv.reportProcessingDialog=null;bobj.crv.timerID=null};bobj.crv.ReportProcessingUI.setDeferred=function(a){bobj.crv.reportProcessingDialog.deferred=a;if(bobj.crv.reportProcessingDialog.wasCancelled()===true){a.cancel()}};bobj.crv._showReportProcessingDialog=function(){if(bobj.crv.reportProcessingDialog&&bobj.crv.reportProcessingDialog.delay!==0){bobj.crv.logger.info("ShowReportProcessingDialog");bobj.crv.reportProcessingDialog.show(true)}};