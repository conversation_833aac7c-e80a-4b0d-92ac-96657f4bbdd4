﻿<%@ Page Title="Application Data Entry - Education Loan | View" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="DE_Education_Loan_View.aspx.cs" Inherits="KSDCSCST_Portal.DE_Education_Loan" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">

    <style>
        input:disabled {
            border: 0;
        }
    </style>
    <style>
        /* Styles for the search input */
        txtAddress_Post_Office {
            width: 300px;
            padding: 10px;
            font-size: 16px;
        }

        txtPresent_Post_Office {
            width: 300px;
            padding: 10px;
            font-size: 16px;
        }

        txtPermanent_Post_Office {
            width: 300px;
            padding: 10px;
            font-size: 16px;
        }
        /* Styles for the search results container */
        #search-results {
            list-style: none;
            padding: 0;
            margin-top: 5px;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
        }

            /* Styles for individual search result items */
            #search-results li {
                padding: 5px;
                cursor: pointer;
            }

                /* Highlight the selected result */
                #search-results li:hover {
                    background-color: #f2f2f2;
                }

        #search-results-pre-pin {
            list-style: none;
            padding: 0;
            margin-top: 5px;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
        }

            /* Styles for individual search result items */
            #search-results-pre-pin li {
                padding: 5px;
                cursor: pointer;
            }

                /* Highlight the selected result */
                #search-results-pre-pin li:hover {
                    background-color: #f2f2f2;
                }

                #search-results-pgpre-pin {
            list-style: none;
            padding: 0;
            margin-top: 5px;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
        }

            /* Styles for individual search result items */
            #search-results-pgpre-pin li {
                padding: 5px;
                cursor: pointer;
            }

                /* Highlight the selected result */
                #search-results-pgpre-pin li:hover {
                    background-color: #f2f2f2;
                }

        #search-results-per-pin {
            list-style: none;
            padding: 0;
            margin-top: 5px;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
        }

            /* Styles for individual search result items */
            #search-results-per-pin li {
                padding: 5px;
                cursor: pointer;
            }

                /* Highlight the selected result */
                #search-results-per-pin li:hover {
                    background-color: #f2f2f2;
                }

                #search-results-pgper-pin {
            list-style: none;
            padding: 0;
            margin-top: 5px;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
        }

            /* Styles for individual search result items */
            #search-results-pgper-pin li {
                padding: 5px;
                cursor: pointer;
            }

                /* Highlight the selected result */
                #search-results-pgper-pin li:hover {
                    background-color: #f2f2f2;
                }

        #search-results-dofficer-pin {
            list-style: none;
            padding: 0;
            margin-top: 5px;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
        }

            /* Styles for individual search result items */
            #search-results-dofficer-pin li {
                padding: 5px;
                cursor: pointer;
            }

                /* Highlight the selected result */
                #search-results-dofficer-pin li:hover {
                    background-color: #f2f2f2;
                }
    </style>


    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>To be filled for Education Loan</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="Welcome.aspx">Home</a></li>
                        <li class="breadcrumb-item  "><a href="ApplicationDataEntry_List.aspx">Application DataEntry</a></li>
                        <li class="breadcrumb-item active">To be filled for Education Loan</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-8 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">

                                    <div class="Sub_Header">Particulars of Student*</div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Student Name*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtStudent_Name" placeholder="Student Name">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Father's Name*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtFather_Name" placeholder="Father's Name">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Mother's Name*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtMother_Name" placeholder="Mother's Name">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Date of Birth*</label>
                                        <div class="col-sm-9">
                                            <input type="date" class="dateandtime form-control" id="txtDate_Of_Birth">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Age*</label>
                                        <div class="col-sm-9">
                                            <input type="number" disabled="disabled" class="form-control" id="txtAge" placeholder="Age">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Sex*</label>
                                        <div class="col-sm-9">
                                            <select id="dropSex" class="form-control">
                                            <option value="0">Select</option>
                                            <option value="Male">Male</option>
                                            <option value="Female">Female</option>
                                            <option value="Other">Other</option>
                                        </select>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Caste*</label>
                                        <div class="col-sm-9">
                                            <select id="dropCaste" class="form-control">
                                                <option value="0" selected="selected">Select</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Sub Caste*</label>
                                        <div class="col-sm-9">
                                            <select id="dropSubCaste" class="form-control">
                                                <option value="0" selected="selected">Select</option>
                                            </select>
                                            <input type="hidden" id="SubCaste" name="SubCaste" value="">
                                        </div>
                                    </div>


                                    <div class="Sub_Header">Address Details*	</div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Present Address*</label>

                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">House Name/No.*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtPresent_House_Name_Or_No" placeholder="House Name/No.">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Lane1*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtPresent_Lane1" placeholder="Lane1">
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Lane2*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtPresent_Lane2" placeholder="Lane2">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                            <label class="col-sm-3 col-form-label">Pincode*</label>
                                            <div class="col-sm-9">
                                                <input type="number" class="form-control" id="txtPresent_Pincode" placeholder="Pincode">
                                                <ul id="search-results-pre-pin"></ul>
                                            </div>
                                        </div>

                                    <div class="form-group row">
                                            <label class="col-sm-3 col-form-label">Post Office*</label>
                                            <div class="col-sm-9">
                                                <input type="text" disabled="disabled" class="form-control" id="txtPresent_Post_Office" placeholder="Post Office">
                                            </div>
                                        </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Phone*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtPresent_phone" oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);" maxlength="10" placeholder="phone">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Permanent Address*</label>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">House Name/No.*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtPermanent_House_Name_Or_No" placeholder="House Name/No.">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Lane1*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtPermanent_Lane1" placeholder="Lane1">
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Lane2*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtPermanent_Lane2" placeholder="Lane2">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                            <label class="col-sm-3 col-form-label">Pincode*</label>
                                            <div class="col-sm-9">
                                                <input type="number" class="form-control" id="txtPermanent_Pincode" placeholder="Pincode">
                                                <ul id="search-results-per-pin"></ul>
                                            </div>
                                        </div>


                                        <div class="form-group row">
                                            <label class="col-sm-3 col-form-label">Post Office*</label>
                                            <div class="col-sm-9">
                                                <input disabled="disabled" type="text" class="form-control" id="txtPermanent_Post_Office" placeholder="Post Office">
                                            </div>
                                        </div>
                                     

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Phone*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtPermanent_phone" oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);" maxlength="10" placeholder="phone">
                                        </div>
                                    </div>

                                    <div class="Sub_Header">Particulars of Parent/Guardian*</div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name of Parent/Guardian*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtName_Of_Parent_Or_Guardian" placeholder="Name of Parent/Guardian">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Father of Parent*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtfather_Of_P_Or_G" placeholder="father of Parent">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Age*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtAge_Of_P_Or_G" oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);" maxlength="2" placeholder="Age">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Present Address*</label>

                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">House Name/No.*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtP_Or_G_Present_House_Name_Or_No" placeholder="House Name/No.">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Lane1*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtP_Or_G_Present_Lane1" placeholder="Lane1">
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Lane2*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtP_Or_G_Present_Lane2" placeholder="Lane2">
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Pincode*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtP_Or_G_Present_Pincode" placeholder="Pincode">
                                             <ul id="search-results-pgpre-pin"></ul>
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Post Office*</label>
                                        <div class="col-sm-9">
                                            <input type="text" disabled="disabled" class="form-control" id="txtP_Or_G_Present_Post_Office" placeholder="Post Office">
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">phone*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtP_Or_G_Present_phone"  oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);" maxlength="10"  placeholder="phone">
                                        </div>
                                    </div>




                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Permanent Address*</label>

                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">House Name/No.*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtP_Or_G_Permanent_House_Name_Or_No" placeholder="House Name/No.">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Lane1*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtP_Or_G_Permanent_Lane1" placeholder="Lane1">
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Lane2*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtP_Or_G_Permanent_Lane2" placeholder="Lane2">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                            <label class="col-sm-3 col-form-label">Pincode*</label>
                                            <div class="col-sm-9">
                                                <input type="number" class="form-control" id="txtP_Or_G_Permanent_Pincode" placeholder="Pincode">
                                                <ul id="search-results-pgper-pin"></ul>
                                            </div>
                                        </div>

                                    <div class="form-group row">
                                            <label class="col-sm-3 col-form-label">Post Office*</label>
                                            <div class="col-sm-9">
                                                <input disabled="disabled" type="text" class="form-control" id="txtP_Or_G_Permanent_Post_Office" placeholder="Post Office">
                                            </div>
                                        </div>
 

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Phone*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtP_Or_G_Permanent_phone"  oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);" maxlength="10" placeholder="phone">
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Relationship with the Student*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtRelationship_With_Student" placeholder="Relationship with the Student">
                                        </div>
                                    </div>



                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Whether Employed or not*</label>
                                        <div class="col-sm-9">
                                            <select id="dropEmployed_Or_Not" class="form-control">
                                                <option value="0" selected="selected">Select</option>
											<option value="Yes">Yes</option>
											<option value="No">No</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="Sub_Header">Particulars of Course*</div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name of Course*</label>
                                        <div class="col-sm-9">
                                            <select id="dropName_Of_Course" class="form-control">
                                                <option value="0" selected="selected">Select</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name of Institution*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtName_Of_Institution" placeholder="Name of Institution">
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Affiliated University*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtAffiliated_University" placeholder="Affiliated University">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Country*</label>
                                        <div class="col-sm-9">
                                            <select id="dropCountry" class="form-control">
                                                <option value="India" selected="selected">India</option>
                                                <option value="Abroad" >Abroad</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row" style="display:none;">
                                        <label class="col-sm-3 col-form-label">Rank*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtRank" />
                                        </div>
                                    </div>

                                    <div class="form-group row" id="State">
                                        <label class="col-sm-3 col-form-label">State*</label>
                                        <div class="col-sm-9">
                                            <select id="dropState" class="form-control">
                                               <option value="0" selected="selected">Select</option>
                                            </select>
                                            <input type="hidden" id="State" name="State" value="">
                                        </div>
                                    </div>

                                    <div class="form-group row"  id="Country">
                                        <label class="col-sm-3 col-form-label">Country*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtCountry" />
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Whether recoganised by AICTE / Medical Council or not ? Give Details*</label>
                                        <div class="col-sm-9">
                                            <textarea class="form-control" maxlength="200" id="txtDetails_Of_Institution"  oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);"  placeholder="Whether recoganised by AICTE / Medical Council or not ? Give Details"></textarea>
                                            <span class="validation_message">Maximum text length is 200</span>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Duration of Course*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtCourse_Duration" placeholder="Duration of Course">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Month and Year of Commencement*</label>
                                        <div class="col-sm-6">
                                            <select id="dropCommencement_Month" class="form-control">
                                               <option value="0" selected="selected">Select</option>
                                            </select>
                                        </div>

                                        <div class="col-sm-3">
                                            <select id="dropCommencement_Year" class="form-control">
                                                <option value="0" selected="selected">Select</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Month and Year of Completion*</label>
                                        <div class="col-sm-6">
                                            <select id="dropCompletion_Month" class="form-control">
                                                <option value="0" selected="selected">Select</option>
                                            </select>
                                        </div>

                                        <div class="col-sm-3">
                                            <select id="dropCompletion_Year" class="form-control">
                                               <option value="0" selected="selected">Select</option>
                                            </select>
                                        </div>

                                    </div>


                                    <div class="Sub_Header">Details of Financial Requirements and Amount required*</div>

                                    <div class="form-group row">
                                        <div class="col-sm-12">

                                            <table border="1" id="tbl_Fee">
                                                <thead>
                                                    <tr>
                                                        <th>Year of Study</th>
                                                        <th>Tution and Admission Fee</th>
                                                        <th>Text Book/Equipment</th>
                                                        <th>Living Expenses</th>
                                                        <th>Total</th>
                                                        <th>Fund Available Including Family</th>
                                                        <th>Loan required</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="Tbody_Fee">


                                                    <tr>
                                                        <td>
                                                            <select id="dropYear_of_Study_1" class="form-control">
                                                                <option value="0" selected="selected">Select</option>
                                                            </select>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtTution_And_Admission_Fee_1" >
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtText_Book_Or_Equipment_1" >
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtLiving_Expenses_1" >
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" disabled="disabled" class="form-control" id="txtTotal_1">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtFund_Available_Including_Family_1">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtLoan_Required_1">
                                                            </div>
                                                        </td>
                                                    </tr>

                                                    <tr>
                                                        <td>
                                                            <select id="dropYear_of_Study_2" class="form-control">
                                                                <option value="0" selected="selected">Select</option>
                                                            </select>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtTution_And_Admission_Fee_2">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtText_Book_Or_Equipment_2">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtLiving_Expenses_2">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" disabled="disabled" class="form-control" id="txtTotal_2">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtFund_Available_Including_Family_2">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtLoan_Required_2">
                                                            </div>
                                                        </td>
                                                    </tr>

                                                    <tr>
                                                        <td>
                                                            <select id="dropYear_of_Study_3" class="form-control">
                                                               <option value="0" selected="selected">Select</option>
                                                            </select>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtTution_And_Admission_Fee_3">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtText_Book_Or_Equipment_3">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtLiving_Expenses_3">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" disabled="disabled" class="form-control" id="txtTotal_3">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtFund_Available_Including_Family_3">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtLoan_Required_3">
                                                            </div>
                                                        </td>
                                                    </tr>

                                                    <tr>
                                                        <td>
                                                            <select id="dropYear_of_Study_4" class="form-control">
                                                                <option value="0" selected="selected">Select</option>
                                                            </select>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtTution_And_Admission_Fee_4">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtText_Book_Or_Equipment_4">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtLiving_Expenses_4">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" disabled="disabled" class="form-control" id="txtTotal_4">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtFund_Available_Including_Family_4">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtLoan_Required_4">
                                                            </div>
                                                        </td>
                                                    </tr>

                                                    <tr>
                                                        <td>
                                                            <select id="dropYear_of_Study_5" class="form-control">
                                                                <option value="0" selected="selected">Select</option>
                                                            </select>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtTution_And_Admission_Fee_5">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtText_Book_Or_Equipment_5">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtLiving_Expenses_5">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" disabled="disabled" class="form-control" id="txtTotal_5">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtFund_Available_Including_Family_5">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="col-sm-9">
                                                                <input type="number" class="form-control" id="txtLoan_Required_5">
                                                            </div>
                                                        </td>
                                                    </tr>


                                                </tbody>


                                            </table>

                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                            <a onclick="Save();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
    
    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 -->
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <!-- Bootstrap4 Duallistbox -->
    <script src="assets/plugins/bootstrap4-duallistbox/jquery.bootstrap-duallistbox.min.js"></script>
    <!-- InputMask -->
    <script src="assets/plugins/moment/moment.min.js"></script>
    <script src="assets/plugins/inputmask/jquery.inputmask.min.js"></script>
    <!-- date-range-picker -->
    <script src="assets/plugins/daterangepicker/daterangepicker.js"></script>
    <!-- bootstrap color picker -->
    <script src="assets/plugins/bootstrap-colorpicker/js/bootstrap-colorpicker.min.js"></script>
    <!-- Tempusdominus Bootstrap 4 -->
    <script src="assets/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js"></script>
    <!-- BS-Stepper -->
    <script src="assets/plugins/bs-stepper/js/bs-stepper.min.js"></script>
    <!-- dropzonejs -->
    <script src="assets/plugins/dropzone/min/dropzone.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script src="assets/js/jquery.dateandtime.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script>
        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;

        const $searchInputPrePin = $('#txtPresent_Pincode');
        const $searchInputPerPin = $('#txtPermanent_Pincode');
        const $searchInputPGPrePin = $('#txtP_Or_G_Present_Pincode');
        const $searchInputPGPerPin = $('#txtP_Or_G_Permanent_Pincode');

        const $searchResultsPrePin = $('#search-results-pre-pin');
        const $searchResultsPerPin = $('#search-results-per-pin');
        const $searchResultsPGPrePin = $('#search-results-pgpre-pin');
        const $searchResultsPGPerPin = $('#search-results-pgper-pin');

        $('#search-results-pre-pin').hide();
        $('#search-results-per-pin').hide();
        $('#search-results-pgpre-pin').hide();
        $('#search-results-pgper-pin').hide();

        $(function () {

            $searchInputPrePin.on('input', function () {
                const query = $searchInputPrePin.val();
                fetchAutocompleteResultsPrePin(query);
            });

            $searchResultsPrePin.on('click', 'li', function () {
                $("#txtPresent_Pincode").val($(this).attr("data-pincode"));
                $("#txtPresent_Post_Office").val($(this).html());
                $searchResultsPrePin.hide();
            });

            $searchInputPerPin.on('input', function () {
                const query = $searchInputPerPin.val();
                fetchAutocompleteResultsPerPin(query);
            });

            $searchResultsPerPin.on('click', 'li', function () {
                $("#txtPermanent_Pincode").val($(this).attr("data-pincode"));
                $("#txtPermanent_Post_Office").val($(this).html());
                $searchResultsPerPin.hide();
            });

            $searchInputPGPrePin.on('input', function () {
                const query = $searchInputPGPrePin.val();
                fetchAutocompleteResultsPGPrePin(query);
            });

            $searchResultsPGPrePin.on('click', 'li', function () {
                $("#txtP_Or_G_Present_Pincode").val($(this).attr("data-pincode"));
                $("#txtP_Or_G_Present_Post_Office").val($(this).html());
                $searchResultsPGPrePin.hide();
            });

            $searchInputPGPerPin.on('input', function () {
                const query = $searchInputPGPerPin.val();
                fetchAutocompleteResultsPGPerPin(query);
            });
            $searchResultsPGPerPin.on('click', 'li', function () {
                $("#txtP_Or_G_Permanent_Pincode").val($(this).attr("data-pincode"));
                $("#txtP_Or_G_Permanent_Post_Office").val($(this).html());
                $searchResultsPGPerPin.hide();
            });
        })



        $(document).ready(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            // Get the current date
            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtApplication_Date').val(formattedDate);

            //$("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
            //$("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");




            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }

            var previousValue = $("#txtDate_Of_Birth").val();

            $("#txtDate_Of_Birth").on("input", function () {
                var currentValue = $(this).val();

                // Check if the input value has changed
                if (currentValue !== previousValue) {
                    $("#txtAge").val(calculateAge(currentValue));
                    //alert("Date selected or changed:"+ currentValue);
                    // Add your custom code here to handle the selected date
                    previousValue = currentValue; // Update the previous value
                }
            });

            $("#txtTution_And_Admission_Fee_1").on("input", function () { Calc_Loan_Required_Year_1(); });
            $("#txtText_Book_Or_Equipment_1").on("input", function () { Calc_Loan_Required_Year_1(); });
            $("#txtLiving_Expenses_1").on("input", function () { Calc_Loan_Required_Year_1(); });

            $("#txtTution_And_Admission_Fee_2").on("input", function () { Calc_Loan_Required_Year_2(); });
            $("#txtText_Book_Or_Equipment_2").on("input", function () { Calc_Loan_Required_Year_2(); });
            $("#txtLiving_Expenses_2").on("input", function () { Calc_Loan_Required_Year_2(); });

            $("#txtTution_And_Admission_Fee_3").on("input", function () { Calc_Loan_Required_Year_3(); });
            $("#txtText_Book_Or_Equipment_3").on("input", function () { Calc_Loan_Required_Year_3(); });
            $("#txtLiving_Expenses_3").on("input", function () { Calc_Loan_Required_Year_3(); });

            $("#txtTution_And_Admission_Fee_4").on("input", function () { Calc_Loan_Required_Year_4(); });
            $("#txtText_Book_Or_Equipment_4").on("input", function () { Calc_Loan_Required_Year_4(); });
            $("#txtLiving_Expenses_4").on("input", function () { Calc_Loan_Required_Year_4(); });

            $("#txtTution_And_Admission_Fee_5").on("input", function () { Calc_Loan_Required_Year_5(); });
            $("#txtText_Book_Or_Equipment_5").on("input", function () { Calc_Loan_Required_Year_5(); });
            $("#txtLiving_Expenses_5").on("input", function () { Calc_Loan_Required_Year_5(); });

            $('#Country').hide();
            Load_Month_And_Year();
            Load_All_Cast();
            Load_All_Education_Loan_By_Id(getParameterByName("Id"));
        });

        function calculateAge(dateOfBirth) {
            // Parse the date of birth string into a Date object
            const dob = new Date(dateOfBirth);

            // Get the current date
            const currentDate = new Date();

            // Calculate the difference in milliseconds
            const ageInMilliseconds = currentDate - dob;

            // Convert the milliseconds to years
            const ageInYears = ageInMilliseconds / (365 * 24 * 60 * 60 * 1000);

            // Round down to the nearest whole number to get the age
            const age = Math.floor(ageInYears);

            return age;
        }

        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }

        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }

        function Calc_Loan_Required_Year_1() {
            var TutionAdmissionFee = $('#txtTution_And_Admission_Fee_1').val();
            var TextBookEquipment = $('#txtText_Book_Or_Equipment_1').val();
            var LivingExpenses = $('#txtLiving_Expenses_1').val();
            if (TutionAdmissionFee == "") { TutionAdmissionFee = 0; }
            if (TextBookEquipment == "") { TextBookEquipment = 0; }
            if (LivingExpenses == "") { LivingExpenses = 0; }
            var Total = parseInt(TutionAdmissionFee) + parseInt(TextBookEquipment) + parseInt(LivingExpenses);
            $('#txtTotal_1').val(Total);
        }

        function Calc_Loan_Required_Year_2() {
            var TutionAdmissionFee = $('#txtTution_And_Admission_Fee_2').val();
            var TextBookEquipment = $('#txtText_Book_Or_Equipment_2').val();
            var LivingExpenses = $('#txtLiving_Expenses_2').val();
            if (TutionAdmissionFee == "") { TutionAdmissionFee = 0; }
            if (TextBookEquipment == "") { TextBookEquipment = 0; }
            if (LivingExpenses == "") { LivingExpenses = 0; }
            var Total = parseInt(TutionAdmissionFee) + parseInt(TextBookEquipment) + parseInt(LivingExpenses);
            $('#txtTotal_2').val(Total);
        }

        function Calc_Loan_Required_Year_3() {
            var TutionAdmissionFee = $('#txtTution_And_Admission_Fee_3').val();
            var TextBookEquipment = $('#txtText_Book_Or_Equipment_3').val();
            var LivingExpenses = $('#txtLiving_Expenses_3').val();
            if (TutionAdmissionFee == "") { TutionAdmissionFee = 0; }
            if (TextBookEquipment == "") { TextBookEquipment = 0; }
            if (LivingExpenses == "") { LivingExpenses = 0; }
            var Total = parseInt(TutionAdmissionFee) + parseInt(TextBookEquipment) + parseInt(LivingExpenses);
            $('#txtTotal_3').val(Total);
        }

        function Calc_Loan_Required_Year_4() {
            var TutionAdmissionFee = $('#txtTution_And_Admission_Fee_4').val();
            var TextBookEquipment = $('#txtText_Book_Or_Equipment_4').val();
            var LivingExpenses = $('#txtLiving_Expenses_4').val();
            if (TutionAdmissionFee == "") { TutionAdmissionFee = 0; }
            if (TextBookEquipment == "") { TextBookEquipment = 0; }
            if (LivingExpenses == "") { LivingExpenses = 0; }
            var Total = parseInt(TutionAdmissionFee) + parseInt(TextBookEquipment) + parseInt(LivingExpenses);
            $('#txtTotal_4').val(Total);
        }

        function Calc_Loan_Required_Year_5() {
            var TutionAdmissionFee = $('#txtTution_And_Admission_Fee_5').val();
            var TextBookEquipment = $('#txtText_Book_Or_Equipment_5').val();
            var LivingExpenses = $('#txtLiving_Expenses_5').val();
            if (TutionAdmissionFee == "") { TutionAdmissionFee = 0; }
            if (TextBookEquipment == "") { TextBookEquipment = 0; }
            if (LivingExpenses == "") { LivingExpenses = 0; }
            var Total = parseInt(TutionAdmissionFee) + parseInt(TextBookEquipment) + parseInt(LivingExpenses);
            $('#txtTotal_5').val(Total);
        }

        function Load_All_Cast() {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Cast",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropCaste').empty();
                    $('#dropCaste').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var Cast = value.Cast;
                        var html = '<option value="' + Id + '">' + Cast + '</option>';

                        $('#dropCaste').append(html);

                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                    })
                }
            }).done(function () {
                Load_All_Courses();
            });
        }

        $('#dropCaste').change(function () {
            Load_All_SubCast($(this).val());
        });

        function Load_All_SubCast(Cast_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Cast",
                data: JSON.stringify({ Cast_Id: Cast_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubCaste').empty();
                    $('#dropSubCaste').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var SubCast = value.SubCast;
                        var html = '<option value="' + Id + '">' + SubCast + '</option>';

                        $('#dropSubCaste').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                var SubCaste = $("#SubCaste").val();
                if (SubCaste != "") {
                    $("#dropSubCaste").val(SubCaste);
                    $("#SubCaste").val('');
                }
            });
        }






        function fetchAutocompleteResultsPrePin(query) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ pincode: query }), // If you have parameters
                url: "WebService.asmx/Select_All_PostOffices",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $searchResultsPrePin.empty();
                    if (data.d.length > 0) {

                        $searchResultsPrePin.show();
                    } else {
                        $searchResultsPrePin.hide();
                        $("#txtPresent_Post_Office").val('');
                    }
                    $.each(data.d, function (key, value) {
                        $searchResultsPrePin.append('<li data-pincode="' + value.Pincode + '">' + value.Post_office + '</li>');
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });


        }

        function getFormattedDate(date) {
            date = date.split('/')[2] + "-" + date.split('/')[1] + "-" + date.split('/')[0];
            return date;
        }

        function fetchAutocompleteResultsPerPin(query) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ pincode: query }), // If you have parameters
                url: "WebService.asmx/Select_All_PostOffices",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $searchResultsPerPin.empty();
                    if (data.d.length > 0) {

                        $searchResultsPerPin.show();
                    } else {
                        $searchResultsPerPin.hide();
                        $("#txtPermanent_Post_Office").val('');
                    }
                    $.each(data.d, function (key, value) {
                        $searchResultsPerPin.append('<li data-pincode="' + value.Pincode + '">' + value.Post_office + '</li>');
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });


        }

        function fetchAutocompleteResultsPGPrePin(query) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ pincode: query }), // If you have parameters
                url: "WebService.asmx/Select_All_PostOffices",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $searchResultsPGPrePin.empty();
                    if (data.d.length > 0) {
                        $searchResultsPGPrePin.show();
                    } else {
                        $searchResultsPGPrePin.hide();
                        $("#txtP_Or_G_Present_Pincode").val('');
                    }
                    $.each(data.d, function (key, value) {
                        $searchResultsPGPrePin.append('<li data-pincode="' + value.Pincode + '">' + value.Post_office + '</li>');
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });


        }


        function fetchAutocompleteResultsPGPerPin(query) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ pincode: query }), // If you have parameters
                url: "WebService.asmx/Select_All_PostOffices",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $searchResultsPGPerPin.empty();
                    if (data.d.length > 0) {
                        $searchResultsPGPerPin.show();
                    } else {
                        $searchResultsPGPerPin.hide();
                        $("#txtP_Or_G_Permanent_Pincode").val('');
                    }
                    $.each(data.d, function (key, value) {
                        $searchResultsPGPerPin.append('<li data-pincode="' + value.Pincode + '">' + value.Post_office + '</li>');
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });


        }

        function Load_All_Courses() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Courses",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropName_Of_Course').empty();
                    $('#dropName_Of_Course').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.int_courseid;

                        var CourseName = value.vchr_coursename;
                        var html = '<option value="' + Id + '">' + CourseName + '</option>';

                        $('#dropName_Of_Course').append(html);

                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                    })
                }
            }).done(function () {

                Load_All_States();

            });
        }

        $('#dropCountry').change(function () {
            var optionSelected = $(this).find("option:selected");
            var valueSelected = optionSelected.val();
            if (valueSelected == "India") { Load_All_States(); $('#State').show(); $('#Country').hide(); }
            else { $('#State').hide(); $('#Country').show(); }

        });

        function Load_All_States() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_States",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropState').empty();
                    $('#dropState').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.int_sid;

                        var State = value.vchr_state;
                        var html = '<option value="' + Id + '">' + State + '</option>';

                        $('#dropState').append(html);

                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                    })
                }
            }).done(function () {

            });
        }

        function Load_All_Education_Loan_By_Id(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ int_loanappid: Id }), // If you have parameters
                url: "WebService.asmx/Select_tbl_eduloanapp_By_int_loanappid",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $.each(data.d, function (key, value) {
                        $("#SubCaste").val(value.Sub_Cast);
                        $("#State").val(value.vchr_state);
                        $("#txtStudent_Name").val(value.vchr_stuname);
                        $("#txtFather_Name").val(value.vchr_stufathername);
                        $("#txtMother_Name").val(value.vchr_stumothername);
                        $("#txtDate_Of_Birth").val(getFormattedDate(value.dte_studob));
                        $("#txtDate_Of_Birth").trigger('input');
                        $("#dropSex").val(value.vchr_sex);
                        $('#dropCaste').val(value.vchr_caste);
                        $('#dropCaste').trigger('change');
                        $("#txtPresent_House_Name_Or_No").val(value.vchr_presenthsename);
                        $("#txtPresent_Lane1").val(value.vchr_presentlane1);
                        $("#txtPresent_Lane2").val(value.vchr_presentlane2);
                        $("#txtPresent_Pincode").val(value.int_presentpin);
                        $("#txtPresent_Post_Office").val(value.vchr_presentpost);
                        $("#txtPresent_phone").val(value.vchr_presentphno);
                        $("#txtPermanent_House_Name_Or_No").val(value.vchr_permhsename);
                        $("#txtPermanent_Lane1").val(value.vchr_permlane1);
                        $("#txtPermanent_Lane2").val(value.vchr_permlane2);
                        $("#txtPermanent_Pincode").val(value.int_permpin);
                        $("#txtPermanent_Post_Office").val(value.vchr_permpost);
                        $("#txtPermanent_phone").val(value.vchr_permphno);
                        $("#txtName_Of_Parent_Or_Guardian").val(value.vchr_parent_fullname);
                        $("#txtfather_Of_P_Or_G").val(value.vchr_parentfathername);
                        $("#txtAge_Of_P_Or_G").val(value.int_parentage);
                        $("#txtP_Or_G_Present_House_Name_Or_No").val(value.vchr_parent_presenthsename);
                        $("#txtP_Or_G_Present_Lane1").val(value.vchr_parent_presentlane1);
                        $("#txtP_Or_G_Present_Lane2").val(value.vchr_parent_presentlane2);
                        $("#txtP_Or_G_Present_Pincode").val(value.int_parent_presentpin);
                        $("#txtP_Or_G_Present_Post_Office").val(value.vchr_parent_presentpost);
                        $("#txtP_Or_G_Present_phone").val(value.vchr_parent_presentphno);
                        $("#txtP_Or_G_Permanent_House_Name_Or_No").val(value.vchr_parent_permhsename);
                        $("#txtP_Or_G_Permanent_Lane1").val(value.vchr_parent_permlane1);
                        $("#txtP_Or_G_Permanent_Lane2").val(value.vchr_parent_permlane2);
                        $("#txtP_Or_G_Permanent_Pincode").val(value.int_parent_permpin);
                        $("#txtP_Or_G_Permanent_Post_Office").val(value.vchr_parent_permpost);
                        $("#txtP_Or_G_Permanent_phone").val(value.vchr_parent_permphno);
                        $("#txtRelationship_With_Student").val(value.vchr_parent_relationship);
                        $('#dropEmployed_Or_Not').val(value.vchr_employed);
                        $('#dropEmployed_Or_Not').val(value.vchr_employed);
                        $("#dropName_Of_Course").val(value.vchr_coursename);
                        $("#txtName_Of_Institution").val(value.vchr_instname);
                        $("#txtAffiliated_University").val(value.vchr_affluni);
                        if (value.vchr_country !== "India") { $("#dropCountry").val('Abroad'); $("#txtCountry").val(value.vchr_country); $("#State").val(''); $('#dropCountry').trigger('change'); }
                        else { $("#dropState").val(value.vchr_state); }
                        $("#txtDetails_Of_Institution").val(value.vchr_affldetails);
                        $("#txtCourse_Duration").val(value.vchr_duration);
                        $("#dropCommencement_Month").val(value.vchr_month_commencement);
                        $("#dropCommencement_Year").val(value.int_yr_commencement);
                        $("#dropCompletion_Month").val(value.vchr_month_completion);
                        $("#dropCompletion_Year").val(value.int_yr_completion);

                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Education_Loan_AmtRequest_By_Id(getParameterByName("Id"));
            });
        }

        function Load_All_Education_Loan_AmtRequest_By_Id(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ int_loanappid: Id }), // If you have parameters
                url: "WebService.asmx/Select_tbl_edu_amtreqest_By_int_loanappid",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    var i = 0;
                    $.each(data.d, function (key, value) {
                        i = i + 1;
                        $("#dropYear_of_Study_" + i).val(value.int_yearofstudy);
                        $("#txtTution_And_Admission_Fee_" + i).val(value.int_tutionfees);
                        $("#txtText_Book_Or_Equipment_" + i).val(value.int_equipmentfees);
                        $("#txtLiving_Expenses_" + i).val(value.int_livingexpence);
                        $("#txtTotal_" + i).val(value.int_totalfees);
                        $("#txtFund_Available_Including_Family_" + i).val(value.int_fundavailable);
                        $("#txtLoan_Required_" + i).val(value.int_loanreq);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }

        $('#dropCommencement_Month').change(function () {
            var optionSelected = $(this).find("option:selected");
            var valueSelected = optionSelected.val();
            if (valueSelected == "0") {
                $('#dropCommencement_Year').val(0);
            }
        });
        $('#dropCompletion_Month').change(function () {
            var optionSelected = $(this).find("option:selected");
            var valueSelected = optionSelected.val();
            if (valueSelected == "0") {
                $('#dropCompletion_Year').val(0);
            }
        });

        $('#dropCompletion_Year').change(function () {
            var optionSelected = $(this).find("option:selected");
            var valueSelected = optionSelected.val();
            if (valueSelected < $('#dropCommencement_Year').val().trim()) {
                $('#dropCompletion_Year').val(0);
                Focus_Error($('#dropCompletion_Year'));
                Toast.fire({
                    icon: 'error',
                    title: 'Please Verify Course Completion Year !'
                })
            }
        });

        function Load_Month_And_Year() {
            var months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];

            for (var i = 0; i < months.length; i++) {
                var monthNumber = i + 1;
                $('#dropCommencement_Month').append($('<option>', {
                    value: monthNumber,
                    text: months[i]
                }));

                $('#dropCompletion_Month').append($('<option>', {
                    value: monthNumber,
                    text: months[i]
                }));
            }

            var currentYear = new Date().getFullYear();
            var startYear = currentYear - 1;
            var endYear = currentYear + 6;

            for (var year = startYear; year <= endYear; year++) {
                $('#dropCommencement_Year').append($('<option>', {
                    value: year,
                    text: year
                }));
                $('#dropCompletion_Year').append($('<option>', {
                    value: year,
                    text: year
                }));
                $('#dropYear_of_Study_1').append($('<option>', {
                    value: year,
                    text: year
                }));
                $('#dropYear_of_Study_2').append($('<option>', {
                    value: year,
                    text: year
                }));
                $('#dropYear_of_Study_3').append($('<option>', {
                    value: year,
                    text: year
                }));
                $('#dropYear_of_Study_4').append($('<option>', {
                    value: year,
                    text: year
                }));
                $('#dropYear_of_Study_5').append($('<option>', {
                    value: year,
                    text: year
                }));
            }
        }

        function Add_Fee() {
            $("#Tbody_Fee").append('<tr>'
                + '    <td>'
                + '        <select id="dropYear_of_Study_1" class="form-control">'
                + '            <option>Select</option>'
                + '        </select>'
                + '    </td>'
                + '    <td>'
                + '        <div class="col-sm-9">'
                + '            <input type="text" class="form-control" id="txtTution_And_Admission_Fee_1">'
                + '        </div>'
                + '    </td>'
                + '    <td>'
                + '        <div class="col-sm-9">'
                + '            <input type="text" class="form-control" id="txtText_Book_Or_Equipment_1">'
                + '        </div>'
                + '    </td>'
                + '    <td>'
                + '        <div class="col-sm-9">'
                + '            <input type="text" class="form-control" id="txtLiving_Expenses_1">'
                + '        </div>'
                + '    </td>'
                + '    <td>'
                + '        <div class="col-sm-9">'
                + '            <input type="text" class="form-control" id="txtTotal_1">'
                + '        </div>'
                + '    </td>'
                + '    <td>'
                + '        <div class="col-sm-9">'
                + '            <input type="text" class="form-control" id="txtFund_Available_Including_Family_1">'
                + '        </div>'
                + '    </td>'
                + '    <td>'
                + '        <div class="col-sm-9">'
                + '            <input type="text" class="form-control" id="txtLoan_Required_1">'
                + '        </div>'
                + '    </td>'
                + '</tr>');
        }

        function Save() {

            var Applicant = $("#dropApplicant");
            var Student_Name = $("#txtStudent_Name");
            var Father_Name = $("#txtFather_Name");
            var Mother_Name = $("#txtMother_Name");
            var DOB = $("#txtDate_Of_Birth");
            var Age = $("#txtAge");
            var Sex = $("#dropSex");
            var Caste = $("#dropCaste");
            var SubCaste = $('#dropSubCaste');
            var Present_House_Name_No = $("#txtPresent_House_Name_Or_No");
            var Present_Lane1 = $("#txtPresent_Lane1");
            var Present_Lane2 = $("#txtPresent_Lane2");
            var Present_Pincode = $("#txtPresent_Pincode");
            var Present_Post_Office = $("#txtPresent_Post_Office");
            var Present_Phone = $("#txtPresent_phone");
            var Permanent_House_Name_No = $("#txtPermanent_House_Name_Or_No");
            var Permanent_Lane1 = $("#txtPermanent_Lane1");
            var Permanent_Lane2 = $("#txtPermanent_Lane2");
            var Permanent_Pincode = $("#txtPermanent_Pincode");
            var Permanent_Post_Office = $("#txtPermanent_Post_Office");
            var Permanent_Phone = $("#txtPermanent_phone");
            var Name_Of_Parent_Or_Guardian = $("#txtName_Of_Parent_Or_Guardian");
            var Father_Of_Parent_Or_Guardian = $("#txtfather_Of_P_Or_G");
            var Age_Of_Parent_Or_Guardian = $("#txtAge_Of_P_Or_G");
            var P_Or_G_Present_House_Name_No = $("#txtP_Or_G_Present_House_Name_Or_No");
            var P_Or_G_Present_Lane1 = $("#txtP_Or_G_Present_Lane1");
            var P_Or_G_Present_Lane2 = $("#txtP_Or_G_Present_Lane2");
            var P_Or_G_Present_Pincode = $("#txtP_Or_G_Present_Pincode");
            var P_Or_G_Present_Post_Office = $("#txtP_Or_G_Present_Post_Office");
            var P_Or_G_Present_Phone = $("#txtP_Or_G_Present_phone");
            var P_Or_G_Permanent_House_Name_No = $("#txtP_Or_G_Permanent_House_Name_Or_No");
            var P_Or_G_Permanent_Lane1 = $("#txtP_Or_G_Permanent_Lane1");
            var P_Or_G_Permanent_Lane2 = $("#txtP_Or_G_Permanent_Lane2");
            var P_Or_G_Permanent_Pincode = $("#txtP_Or_G_Permanent_Pincode");
            var P_Or_G_Permanent_Post_Office = $("#txtP_Or_G_Permanent_Post_Office");
            var P_Or_G_Permanent_Phone = $("#txtP_Or_G_Permanent_phone");
            var Relationship_With_Student = $("#txtRelationship_With_Student");
            var Employed_Or_Not = $("#dropEmployed_Or_Not");
            var Name_Of_Course = $("#dropName_Of_Course");
            var Name_Of_Institution = $("#txtName_Of_Institution");
            var Affiliated_University = $("#txtAffiliated_University");
            var CourseCountry = $("#dropCountry");
            var CourseState = $("#dropState");
            var AbroadCountry = $("#txtCountry");
            var Details_Of_Institution = $("#txtDetails_Of_Institution");
            var Course_Duration = $("#txtCourse_Duration");
            var Commencement_Month = $("#dropCommencement_Month");
            var Commencement_Year = $("#dropCommencement_Year");
            var Completion_Month = $("#dropCompletion_Month");
            var Completion_Year = $("#dropCompletion_Year");

            var Year_of_Study_1 = $('#dropYear_of_Study_1');
            var Tution_And_Admission_Fee_1 = $('#txtTution_And_Admission_Fee_1');
            var Text_Book_Or_Equipment_1 = $('#txtText_Book_Or_Equipment_1');
            var Living_Expenses_1 = $('#txtLiving_Expenses_1');
            var Total_1 = $('#txtTotal_1');
            var Fund_Available_Including_Family_1 = $('#txtFund_Available_Including_Family_1');
            var Loan_Required_1 = $('#txtLoan_Required_1');

            var Year_of_Study_2 = $('#dropYear_of_Study_2');
            var Tution_And_Admission_Fee_2 = $('#txtTution_And_Admission_Fee_2');
            var Text_Book_Or_Equipment_2 = $('#txtText_Book_Or_Equipment_2');
            var Living_Expenses_2 = $('#txtLiving_Expenses_2');
            var Total_2 = $('#txtTotal_2');
            var Fund_Available_Including_Family_2 = $('#txtFund_Available_Including_Family_2');
            var Loan_Required_2 = $('#txtLoan_Required_2');

            var Year_of_Study_3 = $('#dropYear_of_Study_3');
            var Tution_And_Admission_Fee_3 = $('#txtTution_And_Admission_Fee_3');
            var Text_Book_Or_Equipment_3 = $('#txtText_Book_Or_Equipment_3');
            var Living_Expenses_3 = $('#txtLiving_Expenses_3');
            var Total_3 = $('#txtTotal_3');
            var Fund_Available_Including_Family_3 = $('#txtFund_Available_Including_Family_3');
            var Loan_Required_3 = $('#txtLoan_Required_3');

            var Year_of_Study_4 = $('#dropYear_of_Study_4');
            var Tution_And_Admission_Fee_4 = $('#txtTution_And_Admission_Fee_4');
            var Text_Book_Or_Equipment_4 = $('#txtText_Book_Or_Equipment_4');
            var Living_Expenses_4 = $('#txtLiving_Expenses_4');
            var Total_4 = $('#txtTotal_4');
            var Fund_Available_Including_Family_4 = $('#txtFund_Available_Including_Family_4');
            var Loan_Required_4 = $('#txtLoan_Required_4');

            var Year_of_Study_5 = $('#dropYear_of_Study_5');
            var Tution_And_Admission_Fee_5 = $('#txtTution_And_Admission_Fee_5');
            var Text_Book_Or_Equipment_5 = $('#txtText_Book_Or_Equipment_5');
            var Living_Expenses_5 = $('#txtLiving_Expenses_5');
            var Total_5 = $('#txtTotal_5');
            var Fund_Available_Including_Family_5 = $('#txtFund_Available_Including_Family_5');
            var Loan_Required_5 = $('#txtLoan_Required_5');

            if (Applicant.val() == "0") {
                Focus_Error(Applicant);
                Toast.fire({
                    icon: 'error',
                    title: 'Applicant is required !'
                })
            }

            else if (Student_Name.val().trim() == "") {
                Focus_Error(Student_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Name of Student is required !'
                })
            }
            else if (!Is_Valid_Text(Student_Name.val().trim())) {
                Focus_Error(Student_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed !'
                })
            }
            else if (Father_Name.val().trim() == "") {
                Focus_Error(Father_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Name of Father is required !'
                })
            }
            else if (!Is_Valid_Text(Father_Name.val().trim())) {
                Focus_Error(Father_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed !'
                })
            }
            else if (Mother_Name.val().trim() == "") {
                Focus_Error(Mother_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Name of Mother is required !'
                })
            }
            else if (!Is_Valid_Text(Mother_Name.val().trim())) {
                Focus_Error(Mother_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed !'
                })
            }
            else if (DOB.val().trim() == "") {
                Focus_Error(DOB);
                Toast.fire({
                    icon: 'error',
                    title: 'Date of Birth is required !'
                })
            }
            else if (Age.val() == "0") {
                Focus_Error(Age);
                Toast.fire({
                    icon: 'error',
                    title: 'Age is required !'
                })
            }
            else if (Sex.val() == "0") {
                Focus_Error(Sex);
                Toast.fire({
                    icon: 'error',
                    title: 'Sex is required !'
                })
            }
            else if (Caste.val() == "0") {
                Focus_Error(Caste);
                Toast.fire({
                    icon: 'error',
                    title: 'Caste is required !'
                })
            }
            else if (SubCaste.val() == "0") {
                Focus_Error(SubCaste);
                Toast.fire({
                    icon: 'error',
                    title: 'Sub Caste is required !'
                })
            }

            else if (Present_House_Name_No.val().trim() == "") {
                Focus_Error(Present_House_Name_No);
                Toast.fire({
                    icon: 'error',
                    title: 'Present House Name / No is required !'
                })
            }
            else if (Present_Lane1.val().trim() == "") {
                Focus_Error(Present_Lane1);
                Toast.fire({
                    icon: 'error',
                    title: 'Present Lane1 is required !'
                })
            }
            else if (Present_Lane2.val().trim() == "") {
                Focus_Error(Present_Lane2);
                Toast.fire({
                    icon: 'error',
                    title: 'Present Lane2 is required !'
                })
            }
            else if (Present_Pincode.val().trim() == "") {
                Focus_Error(Present_Pincode);
                Toast.fire({
                    icon: 'error',
                    title: 'Present Pincode is required !'
                })
            }
            else if (Present_Post_Office.val().trim() == "") {
                Focus_Error(Present_Post_Office);
                Toast.fire({
                    icon: 'error',
                    title: 'Present Post Office is required !'
                })
            }
            else if (Present_Phone.val().trim() == "") {
                Focus_Error(Present_Phone);
                Toast.fire({
                    icon: 'error',
                    title: 'Present Phone Number is required !'
                })
            }
            else if (Permanent_House_Name_No.val().trim() == "") {
                Focus_Error(Permanent_House_Name_No);
                Toast.fire({
                    icon: 'error',
                    title: 'Permanent House Name / No is required !'
                })
            }
            else if (Permanent_Lane1.val().trim() == "") {
                Focus_Error(Permanent_Lane1);
                Toast.fire({
                    icon: 'error',
                    title: 'Permanent Lane1 is required !'
                })
            }
            else if (Permanent_Lane2.val().trim() == "") {
                Focus_Error(Permanent_Lane2);
                Toast.fire({
                    icon: 'error',
                    title: 'Permanent Lane2 is required !'
                })
            }
            else if (Permanent_Pincode.val().trim() == "") {
                Focus_Error(Permanent_Pincode);
                Toast.fire({
                    icon: 'error',
                    title: 'Permanent Pincode is required !'
                })
            }
            else if (Permanent_Post_Office.val().trim() == "") {
                Focus_Error(Permanent_Post_Office);
                Toast.fire({
                    icon: 'error',
                    title: 'Permanent Post Office is required !'
                })
            }
            else if (Permanent_Phone.val().trim() == "") {
                Focus_Error(Permanent_Phone);
                Toast.fire({
                    icon: 'error',
                    title: 'Permanent Phone Number is required !'
                })
            }
            else if (Name_Of_Parent_Or_Guardian.val().trim() == "") {
                Focus_Error(Name_Of_Parent_Or_Guardian);
                Toast.fire({
                    icon: 'error',
                    title: 'Name of Parent / Guardian is required !'
                })
            }
            else if (!Is_Valid_Text(Name_Of_Parent_Or_Guardian.val().trim())) {
                Focus_Error(Name_Of_Parent_Or_Guardian);
                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed !'
                })
            }
            else if (Father_Of_Parent_Or_Guardian.val().trim() == "") {
                Focus_Error(Father_Of_Parent_Or_Guardian);
                Toast.fire({
                    icon: 'error',
                    title: 'Father of Parent / Guardian is required !'
                })
            }
            else if (!Is_Valid_Text(Father_Of_Parent_Or_Guardian.val().trim())) {
                Focus_Error(Father_Of_Parent_Or_Guardian);
                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed !'
                })
            }
            else if (Age_Of_Parent_Or_Guardian.val() == "") {
                Focus_Error(Age_Of_Parent_Or_Guardian);
                Toast.fire({
                    icon: 'error',
                    title: 'Age Of Parent / Guardian is required !'
                })
            }
            else if (Age_Of_Parent_Or_Guardian.val() <= "0") {
                Focus_Error(Age_Of_Parent_Or_Guardian);
                Toast.fire({
                    icon: 'error',
                    title: 'Age Of Parent / Guardian is required !'
                })
            }
            else if (P_Or_G_Present_House_Name_No.val().trim() == "") {
                Focus_Error(P_Or_G_Present_House_Name_No);
                Toast.fire({
                    icon: 'error',
                    title: 'Present House Name / No Of Parent / Guardian is required !'
                })
            }
            else if (P_Or_G_Present_Lane1.val().trim() == "") {
                Focus_Error(P_Or_G_Present_Lane1);
                Toast.fire({
                    icon: 'error',
                    title: 'Present Lane1 Of Parent / Guardian is required !'
                })
            }
            else if (P_Or_G_Present_Lane2.val().trim() == "") {
                Focus_Error(P_Or_G_Present_Lane2);
                Toast.fire({
                    icon: 'error',
                    title: 'Present Lane2 Of Parent / Guardian is required !'
                })
            }
            else if (P_Or_G_Present_Pincode.val().trim() == "") {
                Focus_Error(P_Or_G_Present_Pincode);
                Toast.fire({
                    icon: 'error',
                    title: 'Present Pincode Of Parent / Guardian is required !'
                })
            }
            else if (P_Or_G_Present_Post_Office.val().trim() == "") {
                Focus_Error(P_Or_G_Present_Post_Office);
                Toast.fire({
                    icon: 'error',
                    title: 'Present Post Office Of Parent / Guardian is required !'
                })
            }
            else if (P_Or_G_Present_Phone.val().trim() == "") {
                Focus_Error(P_Or_G_Present_Phone);
                Toast.fire({
                    icon: 'error',
                    title: 'Present Phone Number Of Parent / Guardian is required !'
                })
            }
            else if (P_Or_G_Permanent_House_Name_No.val().trim() == "") {
                Focus_Error(P_Or_G_Permanent_House_Name_No);
                Toast.fire({
                    icon: 'error',
                    title: 'Permanent House Name / No Of Parent / Guardian is required !'
                })
            }
            else if (P_Or_G_Permanent_Lane1.val().trim() == "") {
                Focus_Error(P_Or_G_Permanent_Lane1);
                Toast.fire({
                    icon: 'error',
                    title: 'Permanent Lane1 Of Parent / Guardian is required !'
                })
            }
            else if (P_Or_G_Permanent_Lane2.val().trim() == "") {
                Focus_Error(P_Or_G_Permanent_Lane2);
                Toast.fire({
                    icon: 'error',
                    title: 'Permanent Lane2 Of Parent / Guardian is required !'
                })
            }
            else if (P_Or_G_Permanent_Pincode.val().trim() == "") {
                Focus_Error(P_Or_G_Permanent_Pincode);
                Toast.fire({
                    icon: 'error',
                    title: 'Permanent Pincode Of Parent / Guardian is required !'
                })
            }
            else if (P_Or_G_Permanent_Post_Office.val().trim() == "") {
                Focus_Error(P_Or_G_Permanent_Post_Office);
                Toast.fire({
                    icon: 'error',
                    title: 'Permanent Post Office Of Parent / Guardian is required !'
                })
            }
            else if (P_Or_G_Permanent_Phone.val().trim() == "") {
                Focus_Error(P_Or_G_Permanent_Phone);
                Toast.fire({
                    icon: 'error',
                    title: 'Permanent Phone Number Of Parent / Guardian is required !'
                })
            }
            else if (Relationship_With_Student.val().trim() == "") {
                Focus_Error(Relationship_With_Student);
                Toast.fire({
                    icon: 'error',
                    title: 'Relationship With Student is required !'
                })
            }

            else if (Employed_Or_Not.val() == "0") {
                Focus_Error(Employed_Or_Not);
                Toast.fire({
                    icon: 'error',
                    title: 'Employed or not is required !'
                })
            }
            else if (Name_Of_Course.val().trim() == "0") {
                Focus_Error(Name_Of_Course);
                Toast.fire({
                    icon: 'error',
                    title: 'Name of Course is required !'
                })
            }
            else if (Name_Of_Institution.val().trim() == "") {
                Focus_Error(Name_Of_Institution);
                Toast.fire({
                    icon: 'error',
                    title: 'Name Of Institution is required !'
                })
            }
            else if (Affiliated_University.val().trim() == "") {
                Focus_Error(Affiliated_University);
                Toast.fire({
                    icon: 'error',
                    title: 'Name Of Affiliated University is required !'
                })
            }
            else if ((CourseCountry.val() == "India") && (CourseState.val() == "0")) {
                Focus_Error(CourseState);
                Toast.fire({
                    icon: 'error',
                    title: 'State is required !'
                })
            }
            else if ((CourseCountry.val() == "Abroad") && (AbroadCountry.val() == "")) {
                Focus_Error(AbroadCountry);
                Toast.fire({
                    icon: 'error',
                    title: 'Country is required !'
                })
            }
            else if (Details_Of_Institution.val().trim() == "") {
                Focus_Error(Details_Of_Institution);
                Toast.fire({
                    icon: 'error',
                    title: 'Details Of Institution is required !'
                })
            }
            else if (Course_Duration.val().trim() == "0") {
                Focus_Error(Course_Duration);
                Toast.fire({
                    icon: 'error',
                    title: 'Duration of Course is required !'
                })
            }
            else if (Course_Duration.val().trim() == "") {
                Focus_Error(Course_Duration);
                Toast.fire({
                    icon: 'error',
                    title: 'Duration of Course is required !'
                })
            }
            else if (Commencement_Month.val().trim() == "0") {
                Focus_Error(Commencement_Month);
                Toast.fire({
                    icon: 'error',
                    title: 'Commencement Month of Course is required !'
                })
            }
            else if (Commencement_Year.val().trim() == "0") {
                Focus_Error(Commencement_Year);
                Toast.fire({
                    icon: 'error',
                    title: 'Commencement Year of Course is required !'
                })
            }
            else if (Completion_Month.val().trim() == "0") {
                Focus_Error(Completion_Month);
                Toast.fire({
                    icon: 'error',
                    title: 'Completion Month of Course is required !'
                })
            }
            else if (Completion_Year.val().trim() == "0") {
                Focus_Error(Completion_Year);
                Toast.fire({
                    icon: 'error',
                    title: 'Completion Year of Course is required !'
                })
            }


            else {
                if (Present_Phone.val().trim() != "") {
                    var values = Present_Phone.val().trim().split(",");
                    for (var i = 0; i < values.length; i++) {
                        if (values[values.length - 1] != "") {
                            if (!Is_Valid_Mobile_Number(values[i])) {
                                Focus_Error(Present_Phone);
                                Toast.fire({
                                    icon: 'error',
                                    title: 'Enter valid phone number !'
                                })
                                return;
                            }
                        }
                    }
                }
                if (Permanent_Phone.val().trim() != "") {
                    var values = Permanent_Phone.val().trim().split(",");
                    for (var i = 0; i < values.length; i++) {
                        if (values[values.length - 1] != "") {
                            if (!Is_Valid_Mobile_Number(values[i])) {
                                Focus_Error(Permanent_Phone);
                                Toast.fire({
                                    icon: 'error',
                                    title: 'Enter valid phone number !'
                                })
                                return;
                            }
                        }
                    }
                }
                if (P_Or_G_Present_Phone.val().trim() != "") {
                    var values = P_Or_G_Present_Phone.val().trim().split(",");
                    for (var i = 0; i < values.length; i++) {
                        if (values[values.length - 1] != "") {
                            if (!Is_Valid_Mobile_Number(values[i])) {
                                Focus_Error(P_Or_G_Present_Phone);
                                Toast.fire({
                                    icon: 'error',
                                    title: 'Enter valid phone number !'
                                })
                                return;
                            }
                        }
                    }
                }
                if (P_Or_G_Permanent_Phone.val().trim() != "") {
                    var values = P_Or_G_Permanent_Phone.val().trim().split(",");
                    for (var i = 0; i < values.length; i++) {
                        if (values[values.length - 1] != "") {
                            if (!Is_Valid_Mobile_Number(values[i])) {
                                Focus_Error(P_Or_G_Permanent_Phone);
                                Toast.fire({
                                    icon: 'error',
                                    title: 'Enter valid phone number !'
                                })
                                return;
                            }
                        }
                    }
                }

                var Country;
                var State;

                if (CourseCountry.val() == "India") {
                    State = CourseState.val();
                    Country = CourseCountry.val();
                }
                else if (CourseCountry.val() == "Abroad") {
                    State = '';
                    Country = AbroadCountry.val();
                }

                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/Update_To_tbl_eduloanapp",
                    data: JSON.stringify({ int_loanappid: getParameterByName("Id"), vchr_stuname: Student_Name.val(), vchr_stufathername: Father_Name.val(), vchr_stumothername: Mother_Name.val(), dte_studob: DOB.val(), int_stuage: Age.val(), vchr_caste: Caste.val(), sub_Cast: SubCaste.val(), vchr_presenthsename: Present_House_Name_No.val(), vchr_presentlane1: Present_Lane1.val(), vchr_presentlane2: Present_Lane2.val(), vchr_presentpost: Present_Post_Office.val(), int_presentpin: Present_Pincode.val(), vchr_presentphno: Present_Phone.val(), vchr_permhsename: Permanent_House_Name_No.val(), vchr_permlane1: Permanent_Lane1.val(), vchr_permlane2: Permanent_Lane2.val(), vchr_permpost: Permanent_Post_Office.val(), int_permpin: Permanent_Pincode.val(), vchr_permphno: Permanent_Phone.val(), vchr_coursename: Name_Of_Course.val(), vchr_instname: Name_Of_Institution.val(), vchr_affluni: Affiliated_University.val(), vchr_affldetails: Details_Of_Institution.val(), vchr_duration: Course_Duration.val(), vchr_month_commencement: Commencement_Month.val(), int_yr_commencement: Commencement_Year.val(), vchr_month_completion: Completion_Month.val(), int_yr_completion: Completion_Year.val(), vchr_parent_fullname: Name_Of_Parent_Or_Guardian.val(), vchr_parent_presenthsename: P_Or_G_Present_House_Name_No.val(), vchr_parent_presentlane1: P_Or_G_Present_Lane1.val(), vchr_parent_presentlane2: P_Or_G_Present_Lane2.val(), vchr_parent_presentpost: P_Or_G_Present_Post_Office.val(), int_parent_presentpin: P_Or_G_Present_Pincode.val(), vchr_parent_presentphno: P_Or_G_Present_Phone.val(), vchr_parent_permhsename: P_Or_G_Permanent_House_Name_No.val(), vchr_parent_permlane1: P_Or_G_Permanent_Lane1.val(), vchr_parent_permlane2: P_Or_G_Permanent_Lane2.val(), vchr_parent_permpost: P_Or_G_Permanent_Post_Office.val(), int_parent_permpin: P_Or_G_Permanent_Pincode.val(), vchr_parent_permphno: P_Or_G_Permanent_Phone.val(), vchr_parent_relationship: Relationship_With_Student.val(), vchr_employed: Employed_Or_Not.val(), vchr_parentfathername: Father_Of_Parent_Or_Guardian.val(), int_parentage: Age_Of_Parent_Or_Guardian.val(), vchr_sex: Sex.val(), vchr_country: Country, vchr_state: State }),
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {

                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        // Handle AJAX error
                        //   alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);

                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                        })

                    }

                }).done(function () {

                    for (var i = 1; i <= 5; i++) {
                        if (($("#dropYear_of_Study_" + i).val().trim() !== "0") &&
                            ($("#txtTution_And_Admission_Fee_" + i).val().trim() !== "") &&
                            ($("#txtText_Book_Or_Equipment_" + i).val().trim() !== "") &&
                            ($("#txtLiving_Expenses_" + i).val().trim() !== "") &&
                            ($("#txtTotal_" + i).val().trim() !== "") &&
                            ($("#txtFund_Available_Including_Family_" + i).val().trim() !== "") &&
                            ($("#txtLoan_Required_" + i).val().trim() !== "")) {

                            $.ajax({
                                type: "POST",
                                dataType: "json",
                                url: "WebService.asmx/Insert_To_tbl_edu_amtreqest",
                                data: JSON.stringify({
                                    int_loanappid: getParameterByName("Id"),
                                    int_yearofstudy: $("#dropYear_of_Study_" + i).val(),
                                    int_tutionfees: $("#txtTution_And_Admission_Fee_" + i).val(),
                                    int_equipmentfees: $("#txtText_Book_Or_Equipment_" + i).val(),
                                    int_livingexpence: $("#txtLiving_Expenses_" + i).val(),
                                    int_totalfees: $("#txtTotal_" + i).val(),
                                    int_fundavailable: $("#txtFund_Available_Including_Family_" + i).val(),
                                    int_loanreq: $("#txtLoan_Required_" + i).val()
                                }),
                                contentType: "application/json; charset=utf-8",
                                success: function (data) {

                                },
                                error: function (jqXHR, textStatus, errorThrown) {
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Oops...',
                                        text: 'Contact your administrator for more information, Email Id: <EMAIL>'
                                    });
                                }
                            }).done(function () {


                            });
                        }
                    }
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Application Data Entry Successfully Submitted!',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked
                            //  Next();
                            location.href = 'ApplicationIssue_List.aspx';
                            //  Your code here
                        }
                    });
                });
            }
        }


        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }


        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }



    </script>
</asp:Content>
