﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Disbursement_Agreement_CheckList_Approve_View.aspx.cs" Inherits="KSDCSCST_Portal.Disbursement_CheckList_Approve" %>




<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">



    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- daterange picker -->
    <link rel="stylesheet" href="assets/plugins/daterangepicker/daterangepicker.css">
    <!-- iCheck for checkboxes and radio inputs -->
    <link rel="stylesheet" href="assets/plugins/icheck-bootstrap/icheck-bootstrap.min.css">
    <!-- Bootstrap Color Picker -->
    <link rel="stylesheet" href="assets/plugins/bootstrap-colorpicker/css/bootstrap-colorpicker.min.css">
    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet" href="assets/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css">
    <!-- Select2 -->
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">
    <!-- Bootstrap4 Duallistbox -->
    <link rel="stylesheet" href="assets/plugins/bootstrap4-duallistbox/bootstrap-duallistbox.min.css">
    <!-- BS Stepper -->
    <link rel="stylesheet" href="assets/plugins/bs-stepper/css/bs-stepper.min.css">
    <!-- dropzonejs -->
    <link rel="stylesheet" href="assets/plugins/dropzone/min/dropzone.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">
    <style>
        table {
            border-collapse: collapse;
            width: 100%;
        }

        input:disabled {
            border: 0;
        }
        label{
            margin:0px !important;
        }
    </style>


    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Agreement Checklist Approve</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">Agreement Checklist Approve</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-10 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">
                                <div class="active tab-pane" id="Print_Area">
                                    <div class="row">
                                        <div class="col-sm-12">
                                        </div>

                                    </div>
                                    <div class="Sub_Header" style="text-align: center;">Agreement Checklist  Approve</div>

                                    <div class="row">
                                        <div class="col-sm-12">
                                            <table style="width: 50%; float: left;">
                                                <tr>
                                                    <td>Reg.No : 
                                                    </td>
                                                    <td style="text-align: right;">
                                                        <label id="txtReg_No"></label>
                                                    </td>


                                                </tr>

                                            </table>
                                            <table style="width: 50%; float: left;">
                                                <tr>


                                                    <td style="padding-left: 10px;">Date Of Agreement :</td>

                                                    <td style="text-align: right;">
                                                        <label id="txtDate_Of_Agreement"></label>
                                                    </td>
                                                </tr>

                                            </table>
                                        </div>

                                    </div>
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="row">
                                                <div class="col-sm-6">

                                                    <div class="Sub_Header" style="text-align: center;">Name & Address</div>

                                                    <table style="width: 100%;">
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Name</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtName"></label>
                                                            </td>

                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Father Name</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtFather_Name"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">House Name/No</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtouse_Name_Or_No"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Lane1</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtLane_1"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Lane2</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtLane_2"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Post Office</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtPost_Office"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Pin Code</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtPin_Code"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Phone Number</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtphone"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Ration_Card_Number</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtRation_Card_Number"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Aadhar No</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtAadhar_No"></label>
                                                            </td>

                                                        </tr>
                                                    </table>


                                                </div>


                                                <div class="col-sm-6">
                                                    <div class="Sub_Header" style="text-align: center;">Location</div>
                                                    <table style="width: 100%;">
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Village</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtVillage"></label>
                                                            </td>

                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Taluk</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtTaluk"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">District</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtDistrict"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Sub District</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtSub_District"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Locality</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtLocality"></label>
                                                            </td>
                                                        </tr>
                                                       <tr>
                                                            <td style="width: 49%; text-align: right;"><label id="lbl_txtLocality" ></label></td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtLocality_Name"></label>
                                                            </td>
                                                        </tr>

                                                    </table>
                                                </div>

                                            </div>
                                        </div>






                                    </div>
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="row">
                                                <div class="col-sm-6">
                                                    <div class="Sub_Header" style="text-align: center;">Dob,Caste & Income</div>
                                                    <table style="width: 100%;">
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Caste</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtCaste"></label>
                                                            </td>

                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Religion</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtReligion"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Sex</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtSex"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Date Of Birth</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtDate_Of_Birth"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Age</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtAge"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Annual Income</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtAnnual_Income"></label>
                                                            </td>
                                                        </tr>

                                                    </table>
                                                </div>
                                                <div class="col-sm-6">
                                                    <div class="Sub_Header" style="text-align: center;">Scheme</div>
                                                    <table style="width: 100%;">
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Fund</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtFund"></label>
                                                            </td>

                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Scheme</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtScheme"></label>
                                                            </td>
                                                        </tr>
                                                        <tr style="display: none;">
                                                            <td style="width: 49%; text-align: right;">Project</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtProject"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Max-Loan Amount</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtloan_Amount"></label>
                                                            </td>
                                                        </tr>
                                                         <tr>
                                                            <td style="width: 49%; text-align: right;">Requested-Loan Amount</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtRequested_Loan_Amount"></label>
                                                            </td>
                                                        </tr>
                                                         <tr>
                                                            <td style="width: 49%; text-align: right;">Sanctioned-Loan Amount</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtSanctioned_Loan_Amount"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Instalment</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtInstalment"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">EMI</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtEMI"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">period</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtperiod"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Rate Of Interest</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtRate_Of_Interest"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 49%; text-align: right;">Penal Interest</td>
                                                            <td style="width: 2%; text-align: center;">:</td>
                                                            <td style="width: 49%">
                                                                <label id="txtPenal_Interest"></label>
                                                            </td>
                                                        </tr>

                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-12">

                                            <div class="Sub_Header" style="text-align: center;">Bank Details</div>

                                            <table border="1">
                                                <tr>
                                                    <td style="width: 24%; text-align: center;">Account Number</td>
                                                    <td style="width: 2%; text-align: center;">:</td>
                                                    <td style="width: 24%; text-align: center;">
                                                        <label id="txtAccount_Number"></label>

                                                    </td>
                                                    <td style="width: 24%; text-align: center;">IFSC</td>
                                                    <td style="width: 2%; text-align: center;">:</td>
                                                    <td style="width: 24%; text-align: center;">


                                                        <label id="txtIFSC"></label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 24%; text-align: center;">Bank Name</td>
                                                    <td style="width: 2%; text-align: center;">:</td>
                                                    <td style="width: 24%; text-align: center;">
                                                        <label id="txtBank_Name"></label>

                                                    </td>
                                                    <td style="width: 24%; text-align: center;">Branch</td>
                                                    <td style="width: 2%; text-align: center;">:</td>
                                                    <td style="width: 24%; text-align: center;">


                                                        <label id="txtBranch"></label>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>

                                    <div class="row" style="margin-top: 20px;">
                                        <div class="col-sm-12">
                                            <div class="Sub_Header" style="text-align: center;">Surety</div>
                                        </div>
                                    </div>

                                    <div class="row" style="margin-top: 10px;" id="DIV_Land_Surety">
                                        <div class="col-sm-12">
                                            <div class="Sub_Header" style="text-align: center;">Land</div>
                                            <table border="1" style="width: 100%;">
                                                <thead>
                                                    <tr>
                                                        <th style="text-align: center;">Sl.No</th>
                                                        <th style="text-align: center;">Owner Name</th>
                                                        <th style="text-align: center;">Survey No.</th>
                                                        <th style="text-align: center;">Extend</th>
                                                        <th style="text-align: center;">Address</th>
                                                        <th style="text-align: center;">Village</th>
                                                        <th style="text-align: center;">Taluk</th>
                                                        <th style="text-align: center;">District</th>
                                                        <th style="text-align: center;">Val.Amt</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tbl_Land_Surety_List">
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="row" style="margin-top: 10px;" id="DIV_Employee_Surety">
                                        <div class="col-sm-12">
                                            <div class="Sub_Header" style="text-align: center;">Employee</div>
                                            <table border="1" style="width: 100%;">
                                                <thead>
                                                    <tr>
                                                        <th style="text-align: center;">Sl.No</th>
                                                        <th style="text-align: center;">Name</th>
                                                        <th style="text-align: center;">Designation</th>
                                                        <th style="text-align: center;">Office Name</th>
                                                        <th style="text-align: center;">Address</th>
                                                        <th style="text-align: center;">Net Salary</th>
                                                        <th style="text-align: center;">DOR</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tbl_Employee_Surety_List">
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="row" style="margin-top: 10px;" id="DIV_FDLIC_Surety">
                                        <div class="col-sm-12">
                                            <div class="Sub_Header" style="text-align: center;">FDLIC</div>
                                            <table border="1" style="width: 100%;">
                                                <thead>
                                                    <tr>
                                                        <th style="text-align: center;">Sl.No</th>
                                                        <th style="text-align: center;">Holder Name</th>
                                                        <th style="text-align: center;">Details</th>
                                                        <th style="text-align: center;">Maturity Amount</th>
                                                        <th style="text-align: center;">Maturity Date</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tbl_FDLIC_Surety_List">
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="row" style="margin-top: 10px;" id="DIV_Personal_Surety">
                                        <div class="col-sm-12">
                                            <div class="Sub_Header" style="text-align: center;">Personal</div>
                                            <table border="1" style="width: 100%;">
                                                <thead>
                                                    <tr>
                                                        <th style="text-align: center;">Sl.No</th>
                                                        <th style="text-align: center;">Name</th>
                                                        <th style="text-align: center;">Father Name</th>
                                                        <th style="text-align: center;">PAN No</th>
                                                        <th style="text-align: center;">Aadhaar No</th>
                                                        <th style="text-align: center;">Address</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tbl_Personal_Surety_List">
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                    <label class="col-sm-9 col-form-label" style="text-align: center;">(You Can Modify Agreement Date Here)</label>


                                    <div class="form-group row">
                                        <label for="txtDate_Of_Transaction" class="col-sm-3 col-form-label ">Agreement Date </label>
                                        <div class="col-sm-9">
                                            <input type="date" class="form-control" id="txtDate_Of_Transaction" placeholder="dd/mm/yyyy">
                                        </div>
                                    </div>

                                    <div class="form-group row" style="margin-top: 10px;">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Remarks*</label>
                                        <div class="col-sm-9">
                                            <textarea class="form-control" maxlength="200" id="txtRemarks_JS" placeholder="Remarks"></textarea>
                                            <span class="validation_message">Maximum text length is 200</span>
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">


                                            <a href="Disbursement_Agreement_CheckList_Approve_List.aspx" class="btn btn-dark">Cancel</a>
                                            <a onclick="Save();" class="btn btn-success" id="ChkListApprove">Approve</a>

                                        </div>
                                    </div>
                                </div>



                                <!-- /.tab-pane -->
                            </div>

                        </div>
                    </div>

                </div>
                <!-- /.card-body -->
            </div>
            <!-- /.card -->
        </div>
        <!-- /.col -->

        <!-- /.row -->

        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->


    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 -->
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <!-- Bootstrap4 Duallistbox -->
    <script src="assets/plugins/bootstrap4-duallistbox/jquery.bootstrap-duallistbox.min.js"></script>
    <!-- InputMask -->
    <script src="assets/plugins/moment/moment.min.js"></script>
    <script src="assets/plugins/inputmask/jquery.inputmask.min.js"></script>
    <!-- date-range-picker -->
    <script src="assets/plugins/daterangepicker/daterangepicker.js"></script>
    <!-- bootstrap color picker -->
    <script src="assets/plugins/bootstrap-colorpicker/js/bootstrap-colorpicker.min.js"></script>
    <!-- Tempusdominus Bootstrap 4 -->
    <script src="assets/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js"></script>
    <!-- BS-Stepper -->
    <script src="assets/plugins/bs-stepper/js/bs-stepper.min.js"></script>
    <!-- dropzonejs -->
    <script src="assets/plugins/dropzone/min/dropzone.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script src="assets/js/jquery.dateandtime.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>


    <script>

        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;

        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }
        function getFormattedDate(date) {
            date = date.split('/')[2] + "-" + date.split('/')[1] + "-" + date.split('/')[0];
            return date;
        }
        function Print() {
            var printWindow = window.open('', '_blank');
            printWindow.document.open();
            printWindow.document.write('<html><head><title>Print</title></head><body style="width: 210mm;height: 297mm;margin: 0;  ">');
            printWindow.document.write($("#Print_Area").html());
            printWindow.document.write('</body></html>');
            printWindow.document.close();
            printWindow.print();
            if (confirm('Print dialog initiated. Please close the window after printing.')) {
                printWindow.close();
            }
        }

        $(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }


            $("#DIV_Land_Surety").hide();
            $("#DIV_Employee_Surety").hide();
            $("#DIV_FDLIC_Surety").hide();
            $("#DIV_Personal_Surety").hide();

        })

        $(document).ready(function () {
            Load_All_Application_Issue_By_Id(getParameterByName("Id"));
        });

        var Caste_Id;
        var SubCaste_Id;
        var Village_Id;
        var SubDistrict_Id;
        var Scheme_Id;
        var RegNo;
        var LoanNo;
        var LoanAppId;
        var Locality;
        var LocalityId;

        function Load_All_Application_Issue_By_Id(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Id: Id }), // If you have parameters
                url: "WebService.asmx/Select_tbl_LoanApp_By_LoanAppId",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        LoanAppId = value.int_loanappid;
                        RegNo = value.vchr_appreceivregno;
                        LoanNo = value.int_loanno;
                        $("#txtReg_No").text(value.vchr_appreceivregno);
                        $("#txtDate_Of_Agreement").text(value.dte_agrement_date);
                        $("#txtName").text(value.vchr_applname);
                        $("#txtFather_Name").text(value.vchr_fathername);
                        $("#txtouse_Name_Or_No").text(value.vchr_hsename);
                        $("#txtLane_1").text(value.vchr_place1);
                        $("#txtLane_2").text(value.vchr_place2);
                        $("#txtPost_Office").text(value.vchr_post);
                        $("#txtPin_Code").text(value.int_pincode);
                        $("#txtphone").text(value.vchr_phno);
                        $("#txtRation_Card_Number").text(value.vchr_ration);
                        $("#txtAadhar_No").text(value.vchr_Aadhaar);
                        Caste_Id = value.vchr_caste;
                        SubCaste_Id = value.SubCast;
                        $("#txtSex").text(value.vchr_sex);
                        $("#txtDate_Of_Birth").text(getFormattedDate(value.dte_dob));
                        $("#txtAge").text(value.Age);
                        $("#txtAnnual_Income").text(value.int_anninc);
                        Village_Id = value.vchr_village;
                        SubDistrict_Id = value.vchr_subdistrict;
                        Scheme_Id = value.int_schemeid;
                        $("#txtFund").text(value.vchr_fund);
                        $("#txtDate_Of_Transaction").val(getFormattedDate(value.dte_agrement_date));


                        $("#txtRequested_Loan_Amount").text(value.int_loanamt_req);
                        $("#txtSanctioned_Loan_Amount").text(value.int_amtsanction);

                        $("#txtLocality").text(value.vchr_locality);
                        $("#lbl_txtLocality").text(value.vchr_locality);
                        Locality = value.vchr_locality;
                        if (Locality == "Panchayat") {
                            LocalityId = value.vchr_panchayat;
                        }
                        else if (Locality == "Municipality") {

                            LocalityId = value.Municipality_Id;
                        }
                        else if (Locality == "Corporation") {

                            LocalityId = value.Municipality_Id;
                        }


                        //console.log();
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Caste_By_Id(Caste_Id, SubCaste_Id);
                Load_All_Village_By_Id(Village_Id);
                Load_All_Sub_District_By_Id(SubDistrict_Id);
                Load_All_Schemes_By_Id(Scheme_Id);
                Load_All_tbl_loanreg_By_RegNo_Or_LoanNo(RegNo, LoanNo);
                Load_All_Bank_Details_By_int_loanappid(LoanAppId);
                if (Locality == 'Panchayat') { Load_All_Panchayath_By_Id(LocalityId); }
            });
        }
        var Sub_District_Count;
        function Load_All_Caste_By_Id(Caste_Id, SubCaste_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Cast_By_Id",
                data: JSON.stringify({ id: Caste_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Caste = value.Cast;
                        $("#txtCaste").text(Caste);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_SubCast_By_Id(SubCaste_Id);
            });

        }
        function Load_All_Panchayath_By_Id(LocalityId) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Panchayath_By_Id",
                data: JSON.stringify({ Id: LocalityId }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Panchayath = value.Panjayath;
                        $("#txtLocality_Name").text(Panchayath);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });

        }
        function Load_All_SubCast_By_Id(SubCaste_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Cast_By_Id",
                data: JSON.stringify({ id: SubCaste_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var SubCaste = value.SubCast;
                        $("#txtReligion").text(SubCaste);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });

        }
        function Load_All_Village_By_Id(Village_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Village_By_Id",
                data: JSON.stringify({ id: Village_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Village = value.Village;
                        $("#txtVillage").text(Village);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                    })
                }
            }).done(function () {
                Load_All_tbl_Taluk_By_Village_Id(Village_Id)
            });
        }

        function Load_All_tbl_Taluk_By_Village_Id(Village_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_Taluk_By_Id",
                data: JSON.stringify({ Village_Id: Village_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtTaluk').val('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Taluk = value.Taluk;
                        $('#txtTaluk').text(Taluk);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Districts_By_Village_Id(Village_Id);
            });
        }
        var LocDistrictId;
        function Load_All_Districts_By_Village_Id(Village_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts_By_Village_Id",
                data: JSON.stringify({ Village_Id: Village_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtDistrict').val('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        LocDistrictId = Id;
                        var District_Name = value.District_Name;
                        $('#txtDistrict').text(District_Name);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                if (Locality == 'Municipality') { Load_All_Municipality_By_District_Id(LocDistrictId, LocalityId); }
                if (Locality == 'Corporation') { Load_All_Corporation_By_District_Id(LocDistrictId, LocalityId); }
            });
        }
        function Load_All_Corporation_By_District_Id(LocDistrictId, LocalityId) {
            console.log(LocDistrictId);
            console.log(LocalityId);
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Corporation_By_District_Id",
                data: JSON.stringify({ District_Id: LocDistrictId }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtSub_District').val('');
                    $.each(data.d, function (key, value) {
                        if (value.Id == LocalityId) {
                            var Id = value.Id;
                            var Corporation = value.Corporation;
                            $('#txtLocality_Name').text(Corporation);
                        }
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                //alert(Present_District);
            });
        }
        function Load_All_Municipality_By_District_Id(LocDistrictId, LocalityId) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Municipality_By_District_Id",
                data: JSON.stringify({ District_Id: LocDistrictId }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtSub_District').val('');
                    $.each(data.d, function (key, value) {
                        if (value.Id == LocalityId) {
                            var Id = value.Id;
                            var Municipality = value.Municipality;
                            $('#txtLocality_Name').text(Municipality);
                        }
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                //alert(Present_District);
            });
        }
        function Load_All_Sub_District_By_Id(SubDistrict_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Districts_By_Id",
                data: JSON.stringify({ id: SubDistrict_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtSub_District').val('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var SubDistrict = value.Sub_District_Name;
                        $('#txtSub_District').text(SubDistrict);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                //alert(Present_District);
            });
        }
        function Load_All_Schemes_By_Id(Scheme_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Schemes_By_ID",
                data: JSON.stringify({ Id: Scheme_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtScheme').val('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Scheme = value.Scheme;
                        $('#txtScheme').text(Scheme);
                        $('#txtloan_Amount').text(value.Loan_Amount);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                //alert(Present_District);
            });

        }
        function Load_All_tbl_loanreg_By_RegNo_Or_LoanNo(RegNo, LoanNo) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_tbl_loanreg_By_RegNo_Or_LoanNo",
                data: JSON.stringify({ appreceivr_RegNo: RegNo, loanNo: LoanNo }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $('#txtInstalment').text('');
                    $('#txtEMI').text('');
                    $('#txtperiod').text('');
                    $('#txtRate_Of_Interest').text('');
                    $('#txtPenal_Interest').text('');
                    $.each(data.d, function (key, value) {

                        $('#txtInstalment').text(value.int_disb_inst);
                        $('#txtEMI').text(value.mny_repayamt);
                        $('#txtperiod').text(value.int_repay_inst);
                        $('#txtRate_Of_Interest').text(value.int_rate_int);
                        $('#txtPenal_Interest').text(value.int_rate_penal);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Surety_Type_By_int_loanappid(getParameterByName("Id"));
            });
        }
        var BankDetails = 0;
        function Load_All_Bank_Details_By_int_loanappid(LoanAppId) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Bank_Details_By_int_loanappid",
                data: JSON.stringify({ int_loanappid: LoanAppId }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtAccount_Number').val('');
                    $('#txtIFSC').val('');
                    $('#txtBank_Name').val('');
                    $('#txtBranch').val('');
                    $.each(data.d, function (key, value) {
                        $('#txtAccount_Number').text(value.int_BankAccNo);
                        $('#txtIFSC').text(value.vchr_IFSC);
                        $('#txtBank_Name').text(value.vchr_Bank);
                        $('#txtBranch').text(value.vchr_Branch);
                        BankDetails = 1;
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                if (BankDetails == 0) {
                    $('#ChkListApprove').hide();
                    Toast.fire({
                        icon: 'error',
                        title: 'Bank Details are not Entered !'
                    })
                }
            });
        }
        var Land_Surety = "";
        var Employee_Surety = "";
        var FDLIC_Surety = "";
        var Personal_Surety = "";

        function Load_All_Surety_Type_By_int_loanappid(LoanAppId) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Surety_Type_By_int_loanappid",
                data: JSON.stringify({ int_loanappid: LoanAppId }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $.each(data.d, function (key, value) {
                        if (value.SuretyType == "Land") { Land_Surety = "Land"; }
                        if (value.SuretyType == "FD_LIC") { FDLIC_Surety = "FD_LIC"; }
                        if (value.SuretyType == "Personal") { Personal_Surety = "Personal"; }
                        if (value.SuretyType == "Employee") { Employee_Surety = "Employee"; }
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                    })
                }
            }).done(function () {
                if (Land_Surety == "Land") { Load_All_tbl_LandSurety_By_LoanAppId(LoanAppId); }
                if (Employee_Surety == "Employee") { Load_All_tbl_EmployeeSurety_By_LoanAppId(LoanAppId); }
                if (FDLIC_Surety == "FD_LIC") { Load_All_tbl_FDLICSurety_By_LoanAppId(LoanAppId); }
                if (Personal_Surety == "Personal") { Load_All_tbl_PersonalSurety_By_LoanAppId(LoanAppId); }
            });
        }

        function Load_All_tbl_PersonalSurety_By_LoanAppId(LoanAppId) {
            var i = 0;
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_EmpSurety_By_LoanAppId",
                data: JSON.stringify({ int_loanappid: LoanAppId }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $("#tbl_Personal_Surety_List").empty();
                    $.each(data.d, function (key, value) {
                        if (value.int_personal == 1) {
                            i++;
                            $("#tbl_Personal_Surety_List").append('<tr>'
                                + '	<td style="text-align: center;">' + i + '</td>'
                                + '	<td style="text-align: center;">' + value.vchr_empname + '</td>'
                                + '	<td style="text-align: center;">' + value.vchr_fathername + '</td>'
                                + '	<td style="text-align: center;">' + value.vchr_PanNo + '</td>'
                                + '	<td style="text-align: center;">' + value.vchr_Aadhar + '</td>'
                                + '	<td style="text-align: center;">' + value.vchr_presenthsename + ', ' + value.vchr_presentlane1 + ', ' + value.vchr_presentlane2 + ', ' + value.vchr_presentpost + ' - ' + value.vchr_presentpin + '</td>'
                                + '</tr>');
                        }
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                    })
                }
            }).done(function () {
                $("#DIV_Personal_Surety").show();
            });
        }

        function Load_All_tbl_FDLICSurety_By_LoanAppId(LoanAppId) {
            var i = 0;
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_FDLICSurety_By_LoanAppId",
                data: JSON.stringify({ int_loanappid: LoanAppId }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $("#tbl_FDLIC_Surety_List").empty();
                    $.each(data.d, function (key, value) {
                        i++;
                        $("#tbl_FDLIC_Surety_List").append('<tr>'
                            + '	<td style="text-align: center;">' + i + '</td>'
                            + '	<td style="text-align: center;">' + value.vchr_holdername + '</td>'
                            + '	<td style="text-align: center;">' + value.vchr_fdpoldetails + '</td>'
                            + '	<td style="text-align: center;">' + value.int_MatureAmt + '</td>'
                            + ' <td style="text-align: center;">' + getFormattedDate(value.dt_mature_date) + '</td>'
                            + '</tr>');
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                    })
                }
            }).done(function () {
                $("#DIV_FDLIC_Surety").show();
            });
        }

        function Load_All_tbl_EmployeeSurety_By_LoanAppId(LoanAppId) {
            var EligibleLoanAmount = 0;
            var Gross = 0;
            var GrossRound = 0;
            var i = 0;
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_EmpSurety_By_LoanAppId",
                data: JSON.stringify({ int_loanappid: LoanAppId }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $("#tbl_Employee_Surety_List").empty();
                    $.each(data.d, function (key, value) {
                        if (value.int_personal == 0) {
                            i++;
                            if (EligibleLoanAmount == 0) { EligibleLoanAmount = value.int_eligibleloan }
                            else if (EligibleLoanAmount > value.int_eligibleloan) { EligibleLoanAmount = value.int_eligibleloan }
                            $("#tbl_Employee_Surety_List").append('<tr>'
                                + '	<td style="text-align: center;">' + i + '</td>'
                                + '	<td style="text-align: center;">' + value.vchr_empname + '</td>'
                                + '	<td style="text-align: center;">' + value.vchr_empdesig + '</td>'
                                + '	<td style="text-align: center;">' + value.vchr_empoffname + '</td>'
                                + '	<td style="text-align: center;">' + value.vchr_presenthsename + ', ' + value.vchr_presentlane1 + ', ' + value.vchr_presentlane2 + ', ' + value.vchr_presentpost + ' - ' + value.vchr_presentpin + '</td>'
                                + '	<td style="text-align: center;">' + value.int_netsal + '</td>'
                                + ' <td style="text-align: center;">' + getFormattedDate(value.dte_empdateofretire) + '</td>'
                                + '</tr>');
                        }
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                    })
                }
            }).done(function () {
                Gross = EligibleLoanAmount * i;
                if ($('#txtloan_Amount').val() > Gross) {
                    GrossRound = Math.floor(Gross);

                }
                $("#DIV_Employee_Surety").show();
            });
        }

        function Load_All_tbl_LandSurety_By_LoanAppId(LoanAppId) {
            var i = 0;
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_LandSurety_By_LoanAppId",
                data: JSON.stringify({ int_loanappid: LoanAppId }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $("#tbl_Land_Surety_List").empty();
                    $.each(data.d, function (key, value) {
                        i++;
                        $("#tbl_Land_Surety_List").append('<tr>'
                            + '	<td style="text-align: center;">' + i + '</td>'
                            + '	<td style="text-align: center;">' + value.vchr_ownername + '</td>'
                            + '	<td style="text-align: center;">' + value.vchr_surveyno + '</td>'
                            + '	<td style="text-align: center;">' + value.vchr_extent + '</td>'
                            + '	<td style="text-align: center;">' + value.vchr_hsename + ', ' + value.vchr_lane1 + ', ' + value.vchr_lane2 + ', ' + value.vchr_post + ' - ' + value.int_pin + '</td>'
                            + '	<td style="text-align: center;">' + value.Village + '</td>'
                            + ' <td style="text-align: center;">' + value.District + '</td>'
                            + '	<td style="text-align: center;">' + value.Taluk + '</td>'
                            + ' <td style="text-align: center;">' + value.int_ValAmt + '</td>'
                            + '</tr>');
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                    })
                }
            }).done(function () {
                $("#DIV_Land_Surety").show();
            });
        }

        function Save() {

            var LoanAppId = getParameterByName("Id");
            var AgrDate = $("#txtDate_Of_Transaction");
            var RegNo = $("#txtReg_No");
            var AgrRemarks = $("#txtRemarks_JS");


            if (AgrRemarks.val().trim() == "") {
                Focus_Error(AgrRemarks);
                Toast.fire({
                    icon: 'error',
                    title: 'Remarks is required !'
                })
            }
            else {

                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/Update_Checklist_Approve_Status",
                    data: JSON.stringify({ int_loanappid: LoanAppId, status: "Checklist Approved", remarks: AgrRemarks.val().trim(), agrDate: AgrDate.val(), regNo: RegNo.val() }),
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {

                    },
                    error: function (error) {
                        // alert("error" + error.responseText);
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',
                        })
                    }
                }).done(function () {
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Checklist Approved !',
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked
                            location.href = 'Disbursement_Agreement_CheckList_Approve_List.aspx';
                            // Your code here
                        }
                    });
                });
            }
        }

        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }
        function Is_Number(inputText) {


            if (isNaN(inputText)) {
                return false;
            } else {
                return true;
            }


        }

        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }

        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }



        function Check_Age_Limit(Age) {
            //var Age = $("#txtAge").val();
            var Min_Age = $("#dropScheme option:selected").attr("data-min_age");
            var Max_Age = $("#dropScheme option:selected").attr("data-max_age");

            Age = parseInt(Age);
            Min_Age = parseInt(Min_Age);
            Max_Age = parseInt(Max_Age);

            if (Min_Age <= Age && Max_Age >= Age) {
                return true;
            }
            else {
                return false;
            }

        }

        function Check_Annual_Income_Limit(Annual_Income) {
            //  var Annual_Income = $("#txtAnnualIncome").val();
            //    var Anninc_Rural_Max = $("#dropScheme option:selected").attr("data-anninc_rural_max");
            //    var Anninc_Urban_Max = $("#dropScheme option:selected").attr("data-anninc_urban_max");

            Annual_Income = parseInt(Annual_Income);
            Rural_Max = parseInt(Rural_Max);
            Urban_Max = parseInt(Urban_Max);

            if (Rural_Max >= Annual_Income && Urban_Max >= Annual_Income) {
                return true;
            }
            else {
                return false;
            }

        }


        function Check_Loan_Amount_Limit(Loan_Amount) {
            //   var Loan_Amount = $("#txtLoanAmount").val();
            var Req_Loan_Amount = $("#dropScheme option:selected").attr("data-loan_amount");


            Loan_Amount = parseInt(Loan_Amount);
            Req_Loan_Amount = parseInt(Req_Loan_Amount);

            if (Loan_Amount <= Req_Loan_Amount) {
                return true;
            }
            else {
                return false;
            }

        }

        function Is_Valid_Aadhar_No(Aadhar_No) {
            var aadharPattern = /^\d{12}$/;

            if (aadharPattern.test(Aadhar_No)) {
                return true;
            } else {
                return false;
            }
        }


        function Is_Valid_RationCard_No(Ration_Card_No) {
            var rationPattern = /^\d{10}$/;

            if (rationPattern.test(Ration_Card_No)) {
                return true;
            } else {
                return false;
            }
        }



    </script>



</asp:Content>



















