/* Copyright (c) Business Objects 2006. All rights reserved. */

// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "Dzisiaj";
var L_January   = "Stycze\u0144";
var L_February  = "Luty";
var L_March     = "Marzec";
var L_April     = "Kwiecie\u0144";
var L_May       = "Maj";
var L_June      = "Czerwiec";
var L_July      = "Lipiec";
var L_August    = "Sierpie\u0144";
var L_September = "Wrzesie\u0144";
var L_October   = "Pa\u017Adziernik";
var L_November  = "Listopad";
var L_December  = "Grudzie\u0144";
var L_Su        = "Ni";
var L_Mo        = "Pn";
var L_Tu        = "Wt";
var L_We        = "\u015Ar";
var L_Th        = "Cz";
var L_Fr        = "Pt";
var L_Sa        = "So";

// Strings for prompts.js and prompts_param.js
var L_YYYY          = "rrrr";
var L_MM            = "mm";
var L_DD            = "dd";
var L_BadNumber     = "Ten parametr jest typu \"Liczba\" i mo\u017Ce zawiera\u0107 wy\u0142\u0105cznie symbol znaku minus, cyfry (\"0-9\"), znaki separator\u00F3w grupowania cyfr oraz znak separatora dziesi\u0119tnego. Popraw wprowadzon\u0105 warto\u015B\u0107.";
var L_BadCurrency   = "Ten parametr jest typu \"Waluta\" i mo\u017Ce zawiera\u0107 wy\u0142\u0105cznie symbol znaku minus, cyfry (\"0-9\"), znaki separator\u00F3w grupowania cyfr oraz znak separatora dziesi\u0119tnego. Popraw wprowadzon\u0105 warto\u015B\u0107.";
var L_BadDate       = "Ten parametr jest typu \"Data\" i powinien mie\u0107 format \"%1\", gdzie \"rrrr\" to rok w formacie czterocyfrowym, \"mm\" \u2014 miesi\u0105c (np. stycze\u0144 = 1), a \"dd\" \u2014 dzie\u0144 miesi\u0105ca.";
var L_BadDateTime   = "Ten parametr jest typu \"Data/godzina\" i jego poprawny format to \"%1 gg:mm:ss\", gdzie \"rrrr\" to rok w formacie czterocyfrowym, \"mm\" \u2014 miesi\u0105c (np. stycze\u0144 = 1), \"dd\" \u2014 dzie\u0144 miesi\u0105ca, \"gg\" \u2014 godziny wg zegara 24-godzinnego, \"mm\" \u2014 minuty, a \"ss\" \u2014 sekundy.";
var L_BadTime       = "Ten parametr jest typu \"Godzina\" i powinien mie\u0107 format \"gg:mm:ss\", gdzie \"gg\" to godziny wg zegara 24-godzinnego, \"mm\" \u2014 minuty, a \"ss\" \u2014 sekundy.";
var L_NoValue       = "Brak warto\u015Bci";
var L_BadValue      = "Aby ustawi\u0107 warto\u015B\u0107 \"Brak warto\u015Bci\", trzeba j\u0105 wybra\u0107 zar\u00F3wno dla warto\u015Bci Od, jak i Do.";
var L_BadBound      = "Nie mo\u017Cna jednocze\u015Bnie ustawi\u0107 warto\u015Bci \"Brak dolnej granicy\" i \"Brak g\u00F3rnej granicy\".";
var L_NoValueAlready = "Ten parametr ma ju\u017C ustawion\u0105 warto\u015B\u0107 \"Brak warto\u015Bci\". Usu\u0144 warto\u015B\u0107 \"Brak warto\u015Bci\" przed dodaniem innych warto\u015Bci.";
var L_RangeError    = "Warto\u015B\u0107 pocz\u0105tku zakresu nie mo\u017Ce by\u0107 wi\u0119ksza ni\u017C warto\u015B\u0107 ko\u0144ca zakresu.";
var L_NoDateEntered = "Musisz wprowadzi\u0107 dat\u0119.";
var L_Empty         = "Wprowad\u017A warto\u015B\u0107.";

// Strings for filter dialog
var L_closeDialog="Zamknij okno";

var L_SetFilter = "Ustaw filtr";
var L_OK        = "OK ";
var L_Cancel    = "Anuluj";

 /* Crystal Decisions Confidential Proprietary Information */
