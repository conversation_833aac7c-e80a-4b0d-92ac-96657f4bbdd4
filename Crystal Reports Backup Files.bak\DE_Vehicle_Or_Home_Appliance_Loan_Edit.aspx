﻿<%@ Page Title="Application Data Entry - Vehicle Or Home Appliance Loan | Edit" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="DE_Vehicle_Or_Home_Appliance_Loan_Edit.aspx.cs" Inherits="KSDCSCST_Portal.DE_Vehicle_Or_Home_Appliance_Loan_Edit" %>




<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
      <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                 <div class="col-sm-6">
                    <h1>To be filled for Vehicle / Home Appliance Loan</h1>
                </div>
               <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="Welcome.aspx">Home</a></li>
						  <li class="breadcrumb-item  "><a href="ApplicationDataEntry_List.aspx">Application Data Entry</a><Application Issue</li>
                        <li class="breadcrumb-item active">To be filled for Vehicle / Home Appliance Loan</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">
								
								
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name of current working Organisation*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtName_Of_Organisation" placeholder="Name of current working Organisation">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Organisation Address*</label>
                                        <div class="col-sm-9">
                                             <textarea class="form-control" maxlength="200" id="txtOrganisation_Address" placeholder="Organisation Address"></textarea>
                                            <span class="validation_message">Maximum text length is 200</span>
										</div>
                                     </div>
									 
									 <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Current Designation*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtCurrent_Designation" placeholder="Current Designation">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Pay Scale*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtPay_Scale" placeholder="Pay Scale">
                                        </div>
                                    </div>
									

									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Gross Salary*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtGross_Salary" placeholder="Gross Salary">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Net Salary*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtNet_Salary" placeholder="Net Salary">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name of the vehicle/home appliance to be purchased*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtName_Of_Vehicle_Or_Home_Appliance" placeholder="Name of the vehicle/home appliance to be purchased">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Market Price*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtMarket_Price" placeholder="Market Price">
                                        </div>
                                    </div>
						
									
							

                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                          <%--  <a href="ApplicationIssue_List.aspx" class="btn btn-dark">Back</a>--%>
                                            <a onclick="Save();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <script src="assets/plugins/toastr/toastr.min.js"></script>
    
    
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.js"></script>

    <script>
        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;

        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }


        $(document).ready(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            // Get the current date
            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtApplication_Date').val(formattedDate);

            $("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
            $("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");

            //   Load_All_Districts();

            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }
            Load_All_Vehicle_Or_Home_Appliance();

        });




        function Load_All_Vehicle_Or_Home_Appliance() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_To_tbl_veh_hme_loan_By_int_loanappid",
                data: JSON.stringify({ int_loanappid: getParameterByName("Id") }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $.each(data.d, function (key, value) {
                        var Name_Of_Organisation = value.vchr_workorgname;
                        var Organisation_Address = value.vchr_workorgaddr;
                        var Current_Designation = value.vchr_designation;
                        var Pay_Scale = value.vchr_scaleofpay;
                        var Gross_Salary = value.int_workgrosssal;
                        var Net_Salary = value.int_netsal;
                        var Name_Of_Vehicle_Or_Home_Appliance = value.vchr_vehname;
                        var Market_Price = value.int_marketrate;


                        $("#txtName_Of_Organisation").val(Name_Of_Organisation);
                        $("#txtOrganisation_Address").val(Organisation_Address);
                        $("#txtCurrent_Designation").val(Current_Designation);
                        $("#txtPay_Scale").val(Pay_Scale);
                        $("#txtGross_Salary").val(Gross_Salary);
                        $("#txtNet_Salary").val(Net_Salary);
                        $("#txtName_Of_Vehicle_Or_Home_Appliance").val(Name_Of_Vehicle_Or_Home_Appliance);
                        $("#txtMarket_Price").val(Market_Price);
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });

        }

        function Save() {
            //alert(Counter);



            var Name_Of_Organisation = $("#txtName_Of_Organisation");
            var Organisation_Address = $("#txtOrganisation_Address");
            var Current_Designation = $("#txtCurrent_Designation");
            var Pay_Scale = $("#txtPay_Scale");
            var Gross_Salary = $("#txtGross_Salary");
            var Net_Salary = $("#txtNet_Salary");
            var Name_Of_Vehicle_Or_Home_Appliance = $("#txtName_Of_Vehicle_Or_Home_Appliance");
            var Market_Price = $("#txtMarket_Price");

            if (Name_Of_Organisation.val() == "") {
                Focus_Error(Name_Of_Organisation);
                Toast.fire({
                    icon: 'error',
                    title: 'Name of current working Organisation is required !'
                })
            }

            else if (Organisation_Address.val() == "") {
                Focus_Error(Organisation_Address);
                Toast.fire({
                    icon: 'error',
                    title: 'Organisation Address is required !'
                })
            }
            else if (Current_Designation.val() == "") {
                Focus_Error(Current_Designation);
                Toast.fire({
                    icon: 'error',
                    title: 'Current Designation is required !'
                })
            }
            else if (Pay_Scale.val() == "") {
                Focus_Error(Pay_Scale);
                Toast.fire({
                    icon: 'error',
                    title: 'Pay Scale is required !'
                })
            }
            else if (Gross_Salary.val() == "") {
                Focus_Error(Gross_Salary);
                Toast.fire({
                    icon: 'error',
                    title: 'Gross Salary is required !'
                })
            }
            else if (Net_Salary.val() == "") {
                Focus_Error(Net_Salary);
                Toast.fire({
                    icon: 'error',
                    title: 'Net Salary is required !'
                })
            }
            else if (Name_Of_Vehicle_Or_Home_Appliance.val() == "") {
                Focus_Error(Name_Of_Vehicle_Or_Home_Appliance);
                Toast.fire({
                    icon: 'error',
                    title: 'Name of the vehicle/home appliance to be purchased is required !'
                })
            }
            else if (Market_Price.val() == "") {
                Focus_Error(Market_Price);
                Toast.fire({
                    icon: 'error',
                    title: 'Market Price is required !'
                })
            }

            else {

                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/Update_To_tbl_veh_hme_loan",
                    data: JSON.stringify({ int_loanappid: getParameterByName("Id"), int_schemeid: getParameterByName("Scheme_Id"), vchr_workorgname: Name_Of_Organisation.val(), vchr_workorgaddr: Organisation_Address.val(), vchr_designation: Current_Designation.val(), vchr_scaleofpay: Pay_Scale.val(), int_netsal: Net_Salary.val(), int_workgrosssal: Gross_Salary.val(), vchr_vehname: Name_Of_Vehicle_Or_Home_Appliance.val(), int_marketrate: Market_Price.val() }), // If you have parameters
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {

                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        // Handle AJAX error
                        //   alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);

                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                        })

                    }

                }).done(function () {
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Application Modification Successfully Submitted !',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked

                            location.href = 'ApplicationDataEntry_Modification_List.aspx';
                            // Your code here
                        }
                    });

                });


            }




        }


        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }


        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }
        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }


    </script>
</asp:Content>

