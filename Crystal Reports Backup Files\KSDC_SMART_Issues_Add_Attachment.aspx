﻿<%@ Page Title="KSDC SMART Issues Add | Attachment" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="KSDC_SMART_Issues_Add_Attachment.aspx.cs" Inherits="KSDCSCST_Portal.KSDC_SMART_Issues_Add_Attachment" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">
    <link rel="stylesheet" href="assets/plugins/dropzone/min/dropzone.min.css">

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>KSDC- SMART Issue Tracking System - ITS</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item"><a href="KSDC_SMART_Issues_List.aspx">KSDC- SMART Issue Tracking System - ITS</a></li>
                        <li class="breadcrumb-item active">Add Attachments</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">
                                    <div class="row">
                                           <h3 class="card-title">Uploaded Attachments</h3>
                                        <div class="col-md-12" id="DIV_Attachments">

                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="card card-default">
                                                <div class="card-header">
                                                    <h3 class="card-title">Upload Attachments</h3>
                                                </div>
                                                <div class="card-body">




                                                    <div id="actions" class="row">
                                                        <div class="col-lg-6">
                                                            <div class="btn-group w-100">
                                                                <span class="btn btn-success col fileinput-button">
                                                                    <i class="fas fa-plus"></i>
                                                                    <span>Add files</span>
                                                                </span>
                                                                <button type="submit" class="btn btn-primary col start">
                                                                    <i class="fas fa-upload"></i>
                                                                    <span>Start upload</span>
                                                                </button>
                                                                <button style="display: none;" type="reset" class="btn btn-warning col cancel">
                                                                    <i class="fas fa-times-circle"></i>
                                                                    <span>Cancel</span>
                                                                </button>
                                                            </div>
                                                        </div>
                                                        <div class="col-lg-6 d-flex align-items-center">
                                                            <div class="fileupload-process w-100">
                                                                <div id="total-progress" class="progress progress-striped active" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0">
                                                                    <div class="progress-bar progress-bar-success" style="width: 0%;" data-dz-uploadprogress></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="table table-striped files" id="previews">
                                                        <div id="template" class="row mt-2">
                                                            <div class="col-auto">
                                                                <span class="preview">
                                                                    <img src="data:," alt="" data-dz-thumbnail /></span>
                                                            </div>
                                                            <div class="col d-flex align-items-center">
                                                                <p class="mb-0">
                                                                    <span class="lead" data-dz-name></span>
                                                                    (<span data-dz-size></span>)
                       
                                                                </p>
                                                                <strong class="error text-danger" data-dz-errormessage></strong>
                                                            </div>
                                                            <div class="col-4 d-flex align-items-center">
                                                                <div class="progress progress-striped active w-100" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0">
                                                                    <div class="progress-bar progress-bar-success" style="width: 0%;" data-dz-uploadprogress></div>
                                                                </div>
                                                            </div>
                                                            <div class="col-auto d-flex align-items-center">
                                                                <div class="btn-group">
                                                                    <button class="btn btn-primary start">
                                                                        <i class="fas fa-upload"></i>
                                                                        <span>Start</span>
                                                                    </button>
                                                                    <button data-dz-remove class="btn btn-warning cancel">
                                                                        <i class="fas fa-times-circle"></i>
                                                                        <span>Cancel</span>
                                                                    </button>
                                                                    <button data-dz-remove class="btn btn-danger delete">
                                                                        <i class="fas fa-trash"></i>
                                                                        <span>Delete</span>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- /.card-body -->
                                                <div class="card-footer">
                                                </div>
                                            </div>
                                            <!-- /.card -->
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                            <a onclick="Save();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <script src="assets/plugins/toastr/toastr.min.js"></script>



    <%-- <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/inputmask/5.0.4/jquery.inputmask.min.js"></script>--%>

    <script src="assets/plugins/dropzone/min/dropzone.min.js"></script>
    <script>
        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;

        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));


        }
        $(document).ready(function () {

            Load_All_Issues_Attachments();

            // DropzoneJS Demo Code Start
            Dropzone.autoDiscover = false

            // Get the template HTML and remove it from the doumenthe template HTML and remove it from the doument
            var previewNode = document.querySelector("#template")
            previewNode.id = ""
            var previewTemplate = previewNode.parentNode.innerHTML
            previewNode.parentNode.removeChild(previewNode)

            var myDropzone = new Dropzone(document.body, { // Make the whole body a dropzone
                url: "WebService.asmx/KSDC_SAMRT_Issue_UploadFile", // Set the url
                paramName: "file", // Name of the parameter that the server will use to receive the file
                maxFilesize: 5, // Max file size in MB
                acceptedFiles: ".jpg, .jpeg, .png, .gif, .xlx, .xlsx, .doc, .docx, .pdf", // Allowed file types
                addRemoveLinks: true, // Add remove links to preview images
                dictDefaultMessage: "Drop files here or click to upload",

                // Additional Dropzone options or callbacks if needed

                thumbnailWidth: 80,
                thumbnailHeight: 80,
                parallelUploads: 20,
                previewTemplate: previewTemplate,
                autoQueue: false, // Make sure the files aren't queued until manually added
                previewsContainer: "#previews", // Define the container to display the previews
                clickable: ".fileinput-button" // Define the element that should be used as click trigger to select files.
            })

            myDropzone.on("addedfile", function (file) {
                // Hookup the start button
                file.previewElement.querySelector(".start").onclick = function () { myDropzone.enqueueFile(file) }
            })

            // Update the total progress bar
            myDropzone.on("totaluploadprogress", function (progress) {
                document.querySelector("#total-progress .progress-bar").style.width = progress + "%"
            })

            myDropzone.on("sending", function (file) {
                // Show the total progress bar when upload starts
                document.querySelector("#total-progress").style.opacity = "1"
                // And disable the start button
                file.previewElement.querySelector(".start").setAttribute("disabled", "disabled")
            })

            // Hide the total progress bar when nothing's uploading anymore
            myDropzone.on("queuecomplete", function (progress) {
                document.querySelector("#total-progress").style.opacity = "0"
            })

            // Setup the buttons for all transfers
            // The "add files" button doesn't need to be setup because the config
            // `clickable` has already been specified.
            document.querySelector("#actions .start").onclick = function () {
                myDropzone.enqueueFiles(myDropzone.getFilesWithStatus(Dropzone.ADDED))
            }
            document.querySelector("#actions .cancel").onclick = function () {
                myDropzone.removeAllFiles(true)
            }
            // DropzoneJS Demo Code End



            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            // Get the current date
            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtApplication_Date').val(formattedDate);

            $("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
            $("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");
            Load_All_Schemes();
            //   Load_All_Districts();

            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }


            $(".form-control").on("focusout", function () {
                $(".form-control").removeAttr("style");

                var This_Id = $(this).attr("id");
                var This_Control = $(this);
                if (This_Id == "txtApplicant_Name") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Name of Applicant is required !'
                        })
                    }
                    else if (!Is_Valid_Text(This_Control.val().trim())) {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Special characters or Numbers not allowed in Name of Applicant !'
                        })
                    }
                }
                else if (This_Id == "dropScheme") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Loan Scheme is required !'
                        })
                    }
                }
                else if (This_Id == "dropCast") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Caste is required !'
                        })
                    }
                }
                else if (This_Id == "dropSubCast") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Sub Caste is required !'
                        })
                    }
                }
                else if (This_Id == "txtPhoneNo") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Phone No is required !'
                        })
                    }
                }
                //else if (This_Id == "txtAadharNumber") {
                //    if (This_Control.val().trim() == "") {
                //        Focus_Error(This_Control);
                //        Toast.fire({
                //            icon: 'error',
                //            title: 'Aadhar No is required !'
                //        })
                //    }
                //}



                // Your common focusout function code goes here
                //   alert("Input value changed: " + value);
            });



            //$(document).on("input", ".form-control", function () {
            //    // Get the current value of the input
            //    var currentValue = $(this).val();

            //    // Convert the value to propcase
            //    var propcaseValue = currentValue.replace(/\w\S*/g, function (txt) {
            //        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
            //    });

            //    // Update the input with the propcase value
            //    $(this).val(propcaseValue);
            //});



        });




        function Load_All_Issues_Attachments() {
            var html = "";
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_KSDC_SMART_Issues_Attachments",
                data: JSON.stringify({ issues_Id: getParameterByName("Id") }),
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    if (data.d.length != 0) {
                        $('#DIV_Attachments').empty();
                       

                        $.each(data.d, function (key, value) {
                            var Id = value.Id;
                            var Attachment_Path = value.Attachment_Path;
                            var Image_html = "";
                            var Str_Plit = Attachment_Path.split(".");
                      
                            if (Str_Plit[1] == "pdf") {
                                Image_html = '<img src="/assets/img/PDF.jpg" />';
                            }
                            else if (Str_Plit[1] == "jpg") {
                                Image_html = '<img src="/assets/img/Image.jpg" />';
                            }
                            else if (Str_Plit[1] == "jpeg") {
                                Image_html = '<img src="/assets/img/Image.jpg" />';
                            }
                            else if (Str_Plit[1] == "png") {
                                Image_html = '<img src="/assets/img/Image.jpg" />';
                            }
                            else if (Str_Plit[1] == "gif") {
                                Image_html = '<img src="/assets/img/Image.jpg" />';
                            }
                            else if (Str_Plit[1] == "xlx") {
                                Image_html = '<img src="/assets/img/Excel.jpg" />';
                            }
                            else if (Str_Plit[1] == "xlsx") {
                                Image_html = '<img src="/assets/img/Excel.jpg" />';
                            }
                            else if (Str_Plit[1] == "doc") {
                                Image_html = '<img src="/assets/img/Word.jpg" />';
                            }
                            else if (Str_Plit[1] == "docx") {
                                Image_html = '<img src="/assets/img/Word.jpg" />';
                            }

                            html = html + '<li><a href="' + Attachment_Path + '" target="_blank">' + Image_html+'</a></li>';
                           
                             //   $('#DIV_Attachments').append(html);
                            
                        });
                    }



                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                $('#DIV_Attachments').append("<ul>" + html+"</ul>");
              

            });

        }


        function Load_All_Districts() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropDistrict').empty();
                    $('#dropDistrict').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var District_Name = value.District_Name;
                        var html = '<option value="' + Id + '">' + District_Name + '</option>';
                        if (Id != 15) {
                            $('#dropDistrict').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $('#dropDistrict').change(function () {
                    Load_All_Sub_Districts($(this).val());
                });

            });

        }
        function Load_All_Sub_Districts(District_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Districts",
                data: JSON.stringify({ District_Id: District_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubDistrict').empty();
                    $('#dropSubDistrict').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Sub_District_Name = value.Sub_District_Name;
                        var html = '<option value="' + Id + '">' + Sub_District_Name + '</option>';

                        $('#dropSubDistrict').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });

        }

        function Load_All_Schemes() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Schemes",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropScheme').empty();
                    $('#dropScheme').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var IsActive = value.IsActive;
                        var Scheme = value.Scheme;
                        var html = '<option value="' + Id + '">' + Scheme + '</option>';
                        if (IsActive == 1) {
                            $('#dropScheme').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_All_Cast();

            });

        }

        function Load_All_Cast() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Cast",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropCast').empty();
                    $('#dropCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var Cast = value.Cast;
                        var html = '<option value="' + Id + '">' + Cast + '</option>';

                        $('#dropCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $('#dropCast').change(function () {
                    Load_All_SubCast($(this).val());
                });

            });

        }


        function Load_All_SubCast(Cast_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Cast",
                data: JSON.stringify({ Cast_Id: Cast_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubCast').empty();
                    $('#dropSubCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var SubCast = value.SubCast;
                        var html = '<option value="' + Id + '">' + SubCast + '</option>';

                        $('#dropSubCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {


            });

        }
        function Save() {
            Get_Session_District_Id();

              
        }
        


        function Get_Session_District_Id() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Get_Session_District_Id",
               

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    if (data.d == "15") {
                        Swal.fire({
                            icon: 'success',
                            title: 'Message',
                            text: 'Issue Successfully Submitted !',

                        }).then((result) => {
                            if (result.isConfirmed) {
                                // OK button clicked
                                location.href = 'KSDC_SMART_Issues_All_List.aspx';
                                // Your code here
                            }
                        });
                    }
                    else {
                        Swal.fire({
                            icon: 'success',
                            title: 'Message',
                            text: 'Issue Successfully Submitted !',

                        }).then((result) => {
                            if (result.isConfirmed) {
                                // OK button clicked
                                location.href = 'KSDC_SMART_Issues_List.aspx';
                                // Your code here
                            }
                        });
                    }


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });

        }



        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }

        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }


        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }



    </script>
</asp:Content>
