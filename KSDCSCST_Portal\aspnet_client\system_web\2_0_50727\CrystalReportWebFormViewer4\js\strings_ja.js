// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "\u4eca\u65e5";
var L_January   = "1 \u6708";
var L_February  = "2 \u6708";
var L_March     = "3 \u6708";
var L_April     = "4 \u6708";
var L_May       = "5 \u6708";
var L_June      = "6 \u6708";
var L_July      = "7 \u6708";
var L_August    = "8 \u6708";
var L_September = "9 \u6708";
var L_October   = "10 \u6708";
var L_November  = "11 \u6708";
var L_December  = "12 \u6708";
var L_Su        = "\u65e5";
var L_Mo        = "\u6708";
var L_Tu        = "\u706b";
var L_We        = "\u6c34";
var L_Th        = "\u6728";
var L_Fr        = "\u91d1";
var L_Sa        = "\u571f";

// strings for dt_param.js
var L_TIME_SEPARATOR = ":";
var L_AM_DESIGNATOR = "\u5348\u524d";
var L_PM_DESIGNATOR = "\u5348\u5f8c";

// strings for range parameter
var L_FROM = "\u958b\u59cb\u5024 : {0}";
var L_TO = "\u7d42\u4e86\u5024 : {0}";
var L_AFTER = "\u958b\u59cb\u5024 (\u6392\u4ed6) : {0}";
var L_BEFORE = "\u7d42\u4e86\u5024 (\u6392\u4ed6) : {0}";
var L_FROM_TO = "\u958b\u59cb\u5024 : {0} \u301c \u7d42\u4e86\u5024 : {1}";
var L_FROM_BEFORE = "\u958b\u59cb\u5024 : {0} \u301c \u7d42\u4e86\u5024 (\u6392\u4ed6) : {1}";
var L_AFTER_TO = "\u958b\u59cb\u5024 (\u6392\u4ed6) : {0} \u301c \u7d42\u4e86\u5024 : {1}";
var L_AFTER_BEFORE = "\u958b\u59cb\u5024 (\u6392\u4ed6) : {0} \u301c \u7d42\u4e86\u5024 (\u6392\u4ed6) : {1}";

// Strings for prompts.js and prompts_param.js
var L_BadNumber		= "\u3053\u306e\u30d1\u30e9\u30e1\u30fc\u30bf\u306e\u578b\u306f \"\u6570\u5024\" \u3067\u3001\u8ca0\u53f7\u3001\u6570\u5b57  (\"0-9\")\u3001\u304a\u3088\u3073\u3001\u5c0f\u6570\u70b9\u3092\u793a\u3059\u30d4\u30ea\u30aa\u30c9\u307e\u305f\u306f\u30ab\u30f3\u30de\u306e\u307f\u3092\u542b\u3080\u3053\u3068\u304c\u3067\u304d\u307e\u3059\u3002\u5165\u529b\u3055\u308c\u305f\u30d1\u30e9\u30e1\u30fc\u30bf\u5024\u3092\u4fee\u6b63\u3057\u3066\u304f\u3060\u3055\u3044\u3002";
var L_BadCurrency	= "\u3053\u306e\u30d1\u30e9\u30e1\u30fc\u30bf\u306e\u578b\u306f \"\u901a\u8ca8\" \u3067\u3001\u8ca0\u53f7\u3001\u6570\u5b57 (\"0-9\")\u3001\u304a\u3088\u3073\u3001\u5c0f\u6570\u70b9\u3092\u793a\u3059\u30d4\u30ea\u30aa\u30c9\u307e\u305f\u306f\u30ab\u30f3\u30de\u306e\u307f\u3092\u542b\u3080\u3053\u3068\u304c\u3067\u304d\u307e\u3059\u3002\u5165\u529b\u3055\u308c\u305f\u30d1\u30e9\u30e1\u30fc\u30bf\u5024\u3092\u4fee\u6b63\u3057\u3066\u304f\u3060\u3055\u3044\u3002";
var L_BadDate		= "\u3053\u306e\u30d1\u30e9\u30e1\u30fc\u30bf\u306e\u578b\u306f \"\u65e5\u4ed8\" \u3067\u3001\u305d\u306e\u5f62\u5f0f\u306f \"Date(yyyy,mm,dd)\" \u3067\u3059\u3002\"yyyy\" \u306f\u5e74\u3092\u8868\u3059 4 \u6841\u306e\u6570\u5b57\u3001\"mm\" \u306f\u6708 (\u4f8b : 1 \u6708 = 1)\u3001\"dd\" \u306f\u305d\u306e\u6708\u306e\u65e5\u3092\u8868\u3057\u307e\u3059\u3002";
var L_BadDateTime   = "\u3053\u306e\u30d1\u30e9\u30e1\u30fc\u30bf\u306e\u578b\u306f \"\u65e5\u6642\" \u3067\u3001\u305d\u306e\u5f62\u5f0f\u306f \"DateTime(yyyy,mm,dd,hh,mm,ss)\" \u3067\u3059\u3002\"yyyy\" \u306f\u5e74\u3092\u8868\u3059 4 \u6841\u306e\u6570\u5b57\u3001\"mm\" \u306f\u6708 (\u4f8b : 1 \u6708 = 1)\u3001\"dd\" \u306f\u65e5\u3001\"hh\" \u306f 24 \u6642\u9593\u8868\u793a\u306b\u3088\u308b\u6642\u9593\u3001\"mm\" \u306f\u5206\u3001\"ss\" \u306f\u79d2\u3092\u8868\u3057\u307e\u3059\u3002";
var L_BadTime       = "\u3053\u306e\u30d1\u30e9\u30e1\u30fc\u30bf\u306e\u578b\u306f \"\u6642\u523b\" \u3067\u3001\u305d\u306e\u5f62\u5f0f\u306f \"Time(hh,mm,ss)\" \u3067\u3059\u3002\"hh\" \u306f 24 \u6642\u9593\u8868\u793a\u306b\u3088\u308b\u6642\u9593\u3001\"mm\" \u306f\u5206\u3001\"ss\" \u306f\u79d2\u3092\u8868\u3057\u307e\u3059\u3002";
var L_NoValue       = "\u5024\u306a\u3057";
var L_BadValue      = "\"\u5024\u306a\u3057\" \u3068\u8a2d\u5b9a\u3059\u308b\u305f\u3081\u306b\u306f\u3001\u958b\u59cb\u5024\u3068\u7d42\u4e86\u5024\u306e\u4e21\u65b9\u3092 \"\u5024\u306a\u3057\" \u306b\u8a2d\u5b9a\u3059\u308b\u5fc5\u8981\u304c\u3042\u308a\u307e\u3059\u3002";
var L_BadBound      = "\"\u958b\u59cb\u5024\u3092\u6307\u5b9a\u3057\u306a\u3044\" \u3068 \"\u7d42\u4e86\u5024\u3092\u6307\u5b9a\u3057\u306a\u3044\" \u3092\u540c\u6642\u306b\u8a2d\u5b9a\u3059\u308b\u3053\u3068\u306f\u3067\u304d\u307e\u305b\u3093\u3002";
var L_NoValueAlready = "\u3053\u306e\u30d1\u30e9\u30e1\u30fc\u30bf\u306f\u65e2\u306b \"\u5024\u306a\u3057\" \u306b\u8a2d\u5b9a\u3055\u308c\u3066\u3044\u307e\u3059\u3002\u307b\u304b\u306e\u5024\u3092\u8a2d\u5b9a\u3059\u308b\u524d\u306b \"\u5024\u306a\u3057\" \u3092\u524a\u9664\u3057\u307e\u3059\u3002";
var L_RangeError    = "\u7bc4\u56f2\u306e\u958b\u59cb\u5024\u306f\u7d42\u4e86\u5024\u3088\u308a\u5c0f\u3055\u306a\u5024\u306b\u3059\u308b\u5fc5\u8981\u304c\u3042\u308a\u307e\u3059\u3002";
var L_NoDateEntered = "\u65e5\u4ed8\u3092\u5165\u529b\u3059\u308b\u5fc5\u8981\u304c\u3042\u308a\u307e\u3059\u3002";

// Strings for ../html/crystalexportdialog.htm
var L_ExportOptions     = "\u30a8\u30af\u30b9\u30dd\u30fc\u30c8 \u30aa\u30d7\u30b7\u30e7\u30f3";
var L_PrintOptions      = "\u5370\u5237\u30aa\u30d7\u30b7\u30e7\u30f3";
var L_PrintPageTitle    = "\u30ec\u30dd\u30fc\u30c8\u3092\u5370\u5237";
var L_ExportPageTitle   = "\u30ec\u30dd\u30fc\u30c8\u306e\u30a8\u30af\u30b9\u30dd\u30fc\u30c8";
var L_OK                = "OK";
var L_PrintPageRange    = "\u5370\u5237\u3059\u308b\u30da\u30fc\u30b8\u7bc4\u56f2\u3092\u5165\u529b\u3057\u307e\u3059\u3002";
var L_ExportPageRange   = "\u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u3059\u308b\u30da\u30fc\u30b8\u7bc4\u56f2\u3092\u5165\u529b\u3057\u307e\u3059\u3002";
var L_InvalidPageRange  = "\u30da\u30fc\u30b8\u7bc4\u56f2\u306e\u5024\u304c\u7121\u52b9\u3067\u3059\u3002\u6709\u52b9\u306a\u30da\u30fc\u30b8\u7bc4\u56f2\u3092\u5165\u529b\u3057\u3066\u304f\u3060\u3055\u3044\u3002";
var L_ExportFormat      = "\u4e00\u89a7\u304b\u3089\u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u5f62\u5f0f\u3092\u9078\u629e\u3057\u3066\u304f\u3060\u3055\u3044\u3002";
var L_Formats           = "\u5f62\u5f0f :";
var L_All               = "\u3059\u3079\u3066";
var L_Pages             = "\u30da\u30fc\u30b8\u6307\u5b9a";
var L_From              = "\u958b\u59cb :";
var L_To                = "\u7d42\u4e86 :";
var L_PrintStep0        = "\u5370\u5237\u3059\u308b\u306b\u306f :";
var L_PrintStep1        = "1.  \u6b21\u306b\u8868\u793a\u3055\u308c\u308b\u30c0\u30a4\u30a2\u30ed\u30b0\u3067\u3001\"\u3053\u306e\u30d5\u30a1\u30a4\u30eb\u3092\u958b\u304f\" \u30aa\u30d7\u30b7\u30e7\u30f3\u3092\u9078\u629e\u3057\u3001[OK] \u30dc\u30bf\u30f3\u3092\u30af\u30ea\u30c3\u30af\u3057\u307e\u3059\u3002";
var L_PrintStep2        = "2.  \u30d6\u30e9\u30a6\u30b6\u306e\u5370\u5237\u30dc\u30bf\u30f3\u3067\u306f\u306a\u304f\u3001Acrobat Reader \u30e1\u30cb\u30e5\u30fc\u306e\u5370\u5237\u30a2\u30a4\u30b3\u30f3\u3092\u30af\u30ea\u30c3\u30af\u3057\u307e\u3059\u3002";
var L_RTFFormat         = "\u30ea\u30c3\u30c1 \u30c6\u30ad\u30b9\u30c8\u5f62\u5f0f";
var L_AcrobatFormat     = "Acrobat \u5f62\u5f0f (PDF)";
var L_CrystalRptFormat  = "Crystal Reports (RPT)";
var L_WordFormat        = "MS Word";
var L_ExcelFormat       = "MS Excel 97-2000";
var L_ExcelRecordFormat = "MS Excel 97-2000 (\u30c7\u30fc\u30bf\u306e\u307f)";
if(typeof(Sys)!=='undefined')Sys.Application.notifyScriptLoaded();
