﻿<%@ Page Title="Leads | List" Language="C#" MasterPageFile="~/Main.Master"  AutoEventWireup="true" CodeBehind="Lead_List.aspx.cs" Inherits="KSDCSCST_Portal.Leads_List" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- DataTables -->
    <link rel="stylesheet" href="assets/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">


    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Leads List</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="Dashboard.aspx">Home</a></li>
                        <li class="breadcrumb-item active">Leads List</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">


                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">List All Leads</h3>
                            <div style="float: right;display:none;"><a href="ApplicationIssue_Add.aspx" style="background: #0f6caa; border-color: #0f6caa;" class="btn btn-block btn-success">Create New Application Issue</a></div>
                         <div style="float: right;display:none;"><a  onclick="GeneratePDF()" style="background: #0f6caa; border-color: #0f6caa;" class="btn btn-block btn-success">Generate PDF</a></div>
                      </div>
                        <!-- /.card-header -->
                        <div class="card-body">
                            <table id="Application_Issue_List" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                      
                                        <th>Applicant's Name</th>
                                      <th>Aadhar No</th>
                                        <th>Scheme</th>
                                        <th>Cast</th>
                                        <th>SubCast</th>
                                        <th>Phone No</th>
                                         <th>Submitted Date</th>
                                        
                                    </tr>
                                </thead>
                                <tbody id="tblBody">
                                </tbody>
                            </table>
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
     

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables  & Plugins -->
    <script src="assets/plugins/datatables/jquery.dataTables.min.js"></script>
    <script src="assets/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="assets/plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
    <script src="assets/plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
    <script src="assets/plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
    <script src="assets/plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
    <script src="assets/plugins/jszip/jszip.min.js"></script>
    <script src="assets/plugins/pdfmake/pdfmake.min.js"></script>
    <script src="assets/plugins/pdfmake/vfs_fonts.js"></script>
    <script src="assets/plugins/datatables-buttons/js/buttons.html5.min.js"></script>
    <script src="assets/plugins/datatables-buttons/js/buttons.print.min.js"></script>
    <script src="assets/plugins/datatables-buttons/js/buttons.colVis.min.js"></script>

        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
   
 <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.3.5/jspdf.min.js"></script> 
    <!-- Page specific script -->
      <script>

          function GeneratePDF() {
              const pdf = new jsPDF({
                  unit: 'px',
                  format: 'a4'
              });

              // Define the element to be converted to PDF
              const element = $("#Application_Issue_List")[0];

              // Generate the PDF from the HTML element
              pdf.fromHTML(element, 10, 10);

              // Save or display the PDF
              pdf.save("report.pdf");
          }


          $(function () {

              LoadApplicationIssues();





          });

          function getParameterByName(name, url = window.location.href) {
              name = name.replace(/[\[\]]/g, '\\$&');
              var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                  results = regex.exec(url);
              if (!results) return null;
              if (!results[2]) return '';
              return decodeURIComponent(results[2].replace(/\+/g, ' '));
          }

          function LoadApplicationIssues() {

              var table = $('#Application_Issue_List').DataTable({
                  "responsive": true, "lengthChange": false, "autoWidth": false,
                  "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"],
                  "processing": true,
                  "serverSide": true,
                  "ajax": {
                      "url": "WebService.asmx/Select_All_tbl_Leads", // Replace with your server-side API URL
                      "type": "POST",
                      "data": function (data) {
                          //  var jsonObject = JSON.parse(jsonData);

                          // Add the orderColumn parameter to the data being sent to the server
                          // Add the required parameters to the DataTables AJAX request


                          data.draw = data.draw || 1; // Example: Default value for draw parameter
                          data.start = data.start || 0;
                          data.length = data.length || 10;
                          data.orderColumn = 'Id';
                          data.orderDirection = 'asc';
                          data.Id = getParameterByName("Id");
                          data.Reciept_No = getParameterByName("Reciept_No");
                          data.Name_Applicant = getParameterByName("Name_Applicant");
                          data.AadharNumber = getParameterByName("AadharNumber");
                          data.Scheme = getParameterByName("Scheme");
                          data.Cast = getParameterByName("Cast");
                          data.SubCast = getParameterByName("SubCast");
                          data.Application_Submitted_Date = getParameterByName("Application_Submitted_Date");

                          data.search = $('#Application_Issue_List_filter label input').val();//{ value: $('#AgencyList_filter label input').val() }; // Send the search value to the server
                          //     alert($('#AgencyList_filter label input').val());

                          // Log the modified data object to the console
                          console.log(data);
                          // var jsonData = JSON.stringify(data);

                          // Return the modified data object
                          return data;
                      }
                  },
                  "columns": [
                     
                      { "data": "Name_Applicant" },
                      { "data": "AadharNumber" },
                      { "data": "Scheme" },
                      { "data": "Cast" },
                      { "data": "SubCast" },
                      { "data": "PhoneNo" },
                      
                      { "data": "Application_Submitted_Date" }
                     
                  ]
              });




          }

          function Delete(This_Val) {
              $.ajax({
                  type: "POST", // or "GET" depending on your web method
                  url: "WebService.asmx/Delete_From_tbl_Loan_App_Issue",
                  data: JSON.stringify({ Id: $(This_Val).attr("data-Id") }), // If you have parameters
                  contentType: "application/json; charset=utf-8",
                  dataType: "json",
                  success: function (response) {

                      // Handle the successful response from the web method
                      console.log(response.d); // "d" is the default property name for the response
                      Swal.fire({
                          icon: 'success',
                          title: 'Message',
                          text: 'Application Issue Successfully Deleted !',

                      }).then((result) => {
                          if (result.isConfirmed) {
                              // OK button clicked

                              location.href = 'ApplicationIssue_List.aspx';
                              // Your code here
                          }
                      });

                  },
                  error: function (xhr, status, error) {
                      // Handle the error response

                      Swal.fire({
                          icon: 'error',
                          title: 'Oops...',
                          text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                      })
                  },
                  done: function (response) {
                      // Handle the error response
                      alert(response);

                  }
              });
          }      </script>
</asp:Content>
