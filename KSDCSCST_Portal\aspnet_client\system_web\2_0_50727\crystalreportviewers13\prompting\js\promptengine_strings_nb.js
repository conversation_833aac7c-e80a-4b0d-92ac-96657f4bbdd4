/* Copyright (c) Business Objects 2006. All rights reserved. */

// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "I dag";
var L_January   = "Januar";
var L_February  = "Februar";
var L_March     = "Mars";
var L_April     = "April";
var L_May       = "Mai";
var L_June      = "Juni";
var L_July      = "Juli";
var L_August    = "August";
var L_September = "September";
var L_October   = "Oktober";
var L_November  = "November";
var L_December  = "Desember";
var L_Su        = "S\u00F8";
var L_Mo        = "Ma";
var L_Tu        = "Ti";
var L_We        = "On";
var L_Th        = "To";
var L_Fr        = "Fr";
var L_Sa        = "L\u00F8";

// Strings for prompts.js and prompts_param.js
var L_YYYY          = "\u00E5\u00E5\u00E5\u00E5";
var L_MM            = "mm";
var L_DD            = "dd";
var L_BadNumber     = "Denne parameteren er av typen Nummer og kan bare inneholde et negativt tegnsymbol, sifre (0-9), grupperingssymboler for sifre eller et desimalsymbol. Korriger den angitte parameterverdien.";
var L_BadCurrency   = "Denne parameteren er av typen Valuta og kan bare inneholde et symbol for negativt tegn, sifre (0-9), grupperingssymboler for sifre eller et desimalsymbol. Korriger den angitte parameterverdien.";
var L_BadDate       = "Denne parameteren er av typen Dato og m\u00E5 ha formatet %1, der \u00E5\u00E5\u00E5\u00E5 er \u00E5rstallet med fire sifre, mm er m\u00E5neden (f.eks. januar = 1) og dd er dagen i m\u00E5neden.";
var L_BadDateTime   = "Denne parameteren er av typen Dato/klokkeslett og m\u00E5 ha formatet %1 tt:mm:ss, der \u00E5\u00E5\u00E5\u00E5 er \u00E5rstallet med fire sifre, mm er m\u00E5neden (f.eks. januar = 1), dd er dagen i m\u00E5neden, tt er timer i en klokke med 24-timers format, mm er minutter og ss er sekunder.";
var L_BadTime       = "Denne parameteren er av typen Klokkeslett og m\u00E5 ha formatet tt:mm:ss, der tt er timer i en klokke med 24-timers format, mm er minutter og ss er sekunder.";
var L_NoValue       = "Ingen verdi";
var L_BadValue      = "Hvis du vil angi Ingen verdi, m\u00E5 du sette b\u00E5de Fra og Til til Ingen verdi.";
var L_BadBound      = "Du kan ikke angi Ingen nedre grense sammen med Ingen \u00F8vre grense.";
var L_NoValueAlready = "Denne parameteren er allerede satt til Ingen verdi. Fjern Ingen verdi f\u00F8r du legger til andre verdier.";
var L_RangeError    = "Starten p\u00E5 omr\u00E5det kan ikke v\u00E6re st\u00F8rre enn slutten p\u00E5 omr\u00E5det.";
var L_NoDateEntered = "Du m\u00E5 angi en dato.";
var L_Empty         = "Angi en verdi.";

// Strings for filter dialog
var L_closeDialog="Lukk vindu";

var L_SetFilter = "Angi filter";
var L_OK        = "OK";
var L_Cancel    = "Avbryt";

 /* Crystal Decisions Confidential Proprietary Information */
