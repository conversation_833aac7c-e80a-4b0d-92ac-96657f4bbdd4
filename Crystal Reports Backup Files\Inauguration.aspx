﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Inauguration.aspx.cs" Inherits="KSDCSCST_Portal.Inauguration" %>
 


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fullscreen Video with Button</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        .video-container {
            position: relative;
            width: 100%;
            height: 100%;
        }
        .video-container video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .video-container .button {
            position: absolute;
            top: 10px;
            left: 10px;
            padding: 10px 20px;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        .overlay-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 20px 40px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            cursor: pointer;
            font-size: 20px;
            display: none;
        }
		 #myButton {
     display: none;
     position: absolute;
     top: 80%;
     left: 50%;
     transform: translate(-50%, -50%);
     cursor: pointer;
     border: none;
     background: none;
     width: 315px;
 }
 #myButton img {
     width: 100%; /* Adjust size as needed */
     height: auto;
 }
 
 
 
  #playButton {
     
     position: absolute;
     top: 50%;
     left: 50%;
     transform: translate(-50%, -50%);
     cursor: pointer;
     border: none;
     background: none;
     width: 315px;
 }
 #playButton img {
     width: 100%; /* Adjust size as needed */
     height: auto;
 }
    </style>
</head>
<body>

<div class="video-container">
    <video id="myVideo">
        <source src="assets/videos/SMART_Intro_Video.mp4" type="video/mp4">
        Your browser does not support the video tag.
    </video>
	  <button id="myButton">
      <img src="assets/videos/Launch_Btn.png" alt="Button Image">
  </button> 
    <button  id="playButton" onclick="playVideo()">
	<img src="assets/videos/Start_Btn.png" alt="Button Image">
	</button>
</div>

<script>
    document.addEventListener('DOMContentLoaded', (event) => {
        const video = document.getElementById('myVideo');
        const playButton = document.getElementById('playButton');

        video.muted = false;

        // Check if video is allowed to autoplay with sound
        const promise = video.play();
        if (promise !== undefined) {
            promise.catch(() => {
                playButton.style.display = 'block';
            });
        }
    });

    const video = document.getElementById('myVideo');
    const button = document.getElementById('myButton');

    video.addEventListener('timeupdate', () => {

        if (video.currentTime >= 47) { // Show button at 47 seconds
            button.style.display = 'block';
        } else {
            button.style.display = 'none';
        }
    });

    button.addEventListener('click', () => {
        location.href = "http://ksdcsmart.kerala.gov.in/";
    });

    function playVideo() {
        const video = document.getElementById('myVideo');
        const playButton = document.getElementById('playButton');
        video.play();
        playButton.style.display = 'none';
    }
</script>

</body>
</html>



