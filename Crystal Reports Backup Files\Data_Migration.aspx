﻿<%@ Page Title="Data Migration" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Data_Migration.aspx.cs" Inherits="KSDCSCST_Portal.Data_Migration" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">
    <link rel="stylesheet" href="sweetalert2.min.css">
    <!-- Select2 -->
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">
    <!-- Content Header (Page header) -->
    <section class="content" >
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto" style="margin-top: 50px;">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="DIV_Agency">

                                 
                                     <div class="form-group row">
                                                <label for="inputEmail" class="col-sm-3 col-form-label">District Code</label>
                                                <div class="col-sm-9">
                                                    <select id="dropDistrictCode" class="form-control">
                                                          <option value="0">Please wait ...</option>
                                                       

                                                    </select>
                                                </div>
                                            </div>
                                   

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <div class="form-group row">
        
        <div class="col-sm-12 text-center" style="margin-top: 50px;">

            <a onclick="Data_Migration(this);" class="btn btn-success">Data Migration</a>
        </div>
    </div>


    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <script>

        $(document).ready(function () {

            Load_All_Districts();
            
        });

        function Load_All_Districts() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Offices",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropDistrictCode').empty();
                    $('#dropDistrictCode').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Office_Name = value.Office_Name;
                        var Code = value.Code;
                        var ShortCode = value.ShortCode;
                
                        var html = '<option data_district_Id="' + value.District_Id + '" data_office_code="' + value.Office_Code+'" value="' + ShortCode + '-' + Code + '">' + Office_Name + '</option>';
                        $('#dropDistrictCode').append(html);
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });

        }
        

        function Data_Migration(This_Val) {
            var Array_Dist_Code = $("#dropDistrictCode").val().split('-');
            var District_Id = $("#dropDistrictCode option:selected").attr("data_district_Id");
            var Office_Code = $("#dropDistrictCode option:selected").attr("data_office_code");
       
          //  alert(Array_Dist_Code[0]);
            $(This_Val).html("Please wait !");
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Data_Migration",
                data: JSON.stringify({ Dist_Code: Array_Dist_Code[0], Dist_Code_No: Array_Dist_Code[1], District_Id, Office_Code}), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                  //  alert(data);
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    // Handle AJAX error
                    //   alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);

                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                    })

                }
            }).done(function () {
                $(This_Val).html("Data Migration");
                Swal.fire({
                    icon: 'success',
                    title: 'Message',
                    text: 'Migration Done Successfully !',

                }).then((result) => {
                    if (result.isConfirmed) {
                        // OK button clicked

                        location.href = 'Data_Migration.aspx';
                        // Your code here
                    }
                });

            });




        }

    </script>
</asp:Content>
