<html>
	<head>
		<script language="javascript" src="../dom.js"></script>
		<script language="javascript" src="../menu.js"></script>
		<script language="javascript">
			var skin=parent._skin?parent._skin:"skin_standard";
			var lang=parent._lang?parent._lang:"en";
			
			initDom("../images/"+skin+"/",lang)
			styleSheet()
		</script>
			
		<script language="javascript">
		
			_globIDCont=0
		
			function cb()
			{
				switch (this.id)
				{
					case "addItem":
						this.par.add("gen"+(_globIDCont++),"Test item 1",cb,"imgtabs.gif",0,0)
						break
				
					case "addCheck":
						this.par.addCheck("gen"+(_globIDCont++),"check 1",cbCheck)
						break
				
					case "addCheckImg":
						this.par.addCheck("gen"+(_globIDCont++),"check 1",cb<PERSON><PERSON><PERSON>,"imgtabs.gif",0,0)
						break
				
					case "addSep":
						this.par.addSeparator()
						break

					case "removeItem":						
						this.par.removeByID("myItem");
						break
					
					case "removeSep":
						if(this.par.getItem(2).isSeparator)
							this.par.remove(2);
						break
						
					case "removeAll":						
							this.par.remove();
						break	
					
					case "insertItem":													
						this.par.insert(3,"myItem"+(_globIDCont++),"insert text");
						break
						
					case "insertSep":
						this.par.insertSeparator(5);
						break
						
					default:
						alert(this.id)
						break
				}
			}
			function cbCheck()
			{
				alert(this.id+" checked? "+this.isChecked())
			}
			
			function showMenu(e)
			{
				menu.show(true,eventGetX(e),eventGetY(e))
			}
			
			function disableSecond()
			{
				var dis=(this.id=="1b")
			
				var item=menu.getItem(1)
				item.setDisabled(dis)
			}
			function disableFirst()
			{
				var dis=(this.id=="1c")
			
				var item=menu.getItem(5)
				item.setDisabled(dis)
			}
		
			function showFirst()
			{
				var dis=(this.id=="0d")
			
				var item=menu.getItem(5)
				item.show(dis)
			}
			
			function cbDisCheck()
			{
				check1.setDisabled(this.id=="0")
			}
		
			menu=newMenuWidget("test")
			
			menu.add("id1","Test item 1",cb,"imgtabs.gif",0,0)
			menu.add("id2","Test item 2",cb)
			menu.add("id3","Test item plus int",cb)
			menu.add("id4","court",cb)
			menu.add("id5","Test item 1",cb)
			menu.add("id6","Test item 2",cb,"format.gif",48,0,true,48,16)
			theItem=menu.add("id7","Test des check boxes",cb,"format.gif",64,0)
			
			
			menu2=newMenuWidget("test2")
			menu2.add("id8","Test item 2",cb)
			menu2.add("id9","essai",cb)
			check1=menu2.addCheck("check1","check 1",cbCheck)
			check1.check(true)
			check2=menu2.addCheck("check2","check 2",cbCheck)
			check2.check(false)
			check3=menu2.addCheck("check3","check 3",cbCheck,"format.gif",80,0)
			check3.check(true)
			check4=menu2.addCheck("check4","check 4",cbCheck,"format.gif",64,0)
			check4.check(false)
			menu2.addSeparator()
			menu2.add("0","Disable check 1",cbDisCheck)
			menu2.add("1","Enable check 1",cbDisCheck)
			theItem.attachSubMenu(menu2)
			
			menu.addSeparator()
			menu.add("0b","Enable second item",disableSecond)
			menu.add("1b","Disable second item",disableSecond)
			
			menu.addSeparator()
			menu.add("0c","Enable 6th item",disableFirst)
			menu.add("1c","Disable 6th item",disableFirst)

			menu.add("0d","Show 6th item",showFirst)
			menu.add("1d","Hide 6th item",showFirst)


			menu.addSeparator()
			
			theItem=menu.add("id10","Ajout dynamique d'items",cb)
			
				menu3=newMenuWidget("test3")
				menu3.add("addItem","Add an Item",cb)
				menu3.add("addCheck","Add a Check",cb)
				menu3.add("addCheckImg","Add a Check with image",cb)
				menu3.add("addSep","Add a separator",cb)
				menu3.addSeparator()
				
			theItem.attachSubMenu(menu3)						

			theItem2=menu.add("theItem2","Remove/insert dynamique d'items",cb)			
			menu4=newMenuWidget("test4")
				menu4.add("removeItem","remove an Item",cb)
				menu4.add("removeSep","remove a sep",cb)
				menu4.addSeparator()
				menu4.add("removeAll","remove all",cb)
				menu4.addSeparator()
				menu4.add("myItem","item to be removed",cb)
				menu4.addSeparator()
				menu4.add("insertItem","insert an item",cb)
				menu4.add("insertSep","insert an sep",cb)				
			theItem2.attachSubMenu(menu4)
			
			menu.add("id19","Test item plus int",cb)
			menu.add("id20","court",cb)
					
			
			function loadCB()
			{
			}

		</script>
	</head>
	<body onload="loadCB()" onclick="showMenu(event)">
			sdfjgfds lkfjglk sfdkg dflskjg lsdfgldfsjlgk sdfkg lsfdj glsdfjg lksfdjklg sdflk g
			sdfjgfds lkfjglk sfdkg dflskjg lsdfgldfsjlgk sdfkg lsfdj glsdfjg lksfdjklg sdflk g
			sdfjgfds lkfjglk sfdkg dflskjg lsdfgldfsjlgk sdfkg lsfdj glsdfjg lksfdjklg sdflk g
			sdfjgfds lkfjglk sfdkg dflskjg lsdfgldfsjlgk sdfkg lsfdj glsdfjg lksfdjklg sdflk g
			sdfjgfds lkfjglk sfdkg dflskjg lsdfgldfsjlgk sdfkg lsfdj glsdfjg lksfdjklg sdflk g
			sdfjgfds lkfjglk sfdkg dflskjg lsdfgldfsjlgk sdfkg lsfdj glsdfjg lksfdjklg sdflk g
			sdfjgfds lkfjglk sfdkg dflskjg lsdfgldfsjlgk sdfkg lsfdj glsdfjg lksfdjklg sdflk g
			sdfjgfds lkfjglk sfdkg dflskjg lsdfgldfsjlgk sdfkg lsfdj glsdfjg lksfdjklg sdflk g
			sdfjgfds lkfjglk sfdkg dflskjg lsdfgldfsjlgk sdfkg lsfdj glsdfjg lksfdjklg sdflk g
			sdfjgfds lkfjglk sfdkg dflskjg lsdfgldfsjlgk sdfkg lsfdj glsdfjg lksfdjklg sdflk g
			sdfjgfds lkfjglk sfdkg dflskjg lsdfgldfsjlgk sdfkg lsfdj glsdfjg lksfdjklg sdflk g
			sdfjgfds lkfjglk sfdkg dflskjg lsdfgldfsjlgk sdfkg lsfdj glsdfjg lksfdjklg sdflk g
			sdfjgfds lkfjglk sfdkg dflskjg lsdfgldfsjlgk sdfkg lsfdj glsdfjg lksfdjklg sdflk g
			sdfjgfds lkfjglk sfdkg dflskjg lsdfgldfsjlgk sdfkg lsfdj glsdfjg lksfdjklg sdflk g
			sdfjgfds lkfjglk sfdkg dflskjg lsdfgldfsjlgk sdfkg lsfdj glsdfjg lksfdjklg sdflk g
			sdfjgfds lkfjglk sfdkg dflskjg lsdfgldfsjlgk sdfkg lsfdj glsdfjg lksfdjklg sdflk g
			sdfjgfds lkfjglk sfdkg dflskjg lsdfgldfsjlgk sdfkg lsfdj glsdfjg lksfdjklg sdflk g
		
	</body>
	
</html>
