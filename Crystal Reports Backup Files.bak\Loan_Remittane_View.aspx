﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Loan_Remittane_View.aspx.cs" Inherits="KSDCSCST_Portal.Loan_Remittane_View" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Loan Remittance</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">Loan Remittance</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-10 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">


                                    <button class="accordion">Loan Details</button>
                                    <div class="panel" style="border: 1px solid #d0c7c7;">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="row" style="margin: 1%;">
                                                    <div class="col-sm-12">
                                                        <div class="Sub_Header_Abstract">LOAN DETAILS</div>


                                                        <table class="table_Abstract" style="width: 100%;">
                                                            <tbody>
                                                                <tr>
                                                                    <td>Loanee Name</td>

                                                                    <td style="">
                                                                        <label id="txtLoanee_Name"></label>
                                                                    </td>



                                                                    <td>Address</td>

                                                                    <td>
                                                                        <label id="txtAddress"></label>
                                                                    </td>


                                                                    <td>Scheme Name</td>

                                                                    <td>
                                                                        <label id="txtScheme_Name"></label>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Old Agr.No</td>

                                                                    <td>
                                                                        <label id="txtOld_Agr_No"></label>
                                                                    </td>

                                                                    <td>Loan A/C No.</td>

                                                                    <td>
                                                                        <label id="txtLoan_Acc_No"></label>
                                                                    </td>

                                                                    <td>Due Date</td>

                                                                    <td>
                                                                        <label id="txtDue_Date"></label>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>EMI</td>

                                                                    <td>
                                                                        <label id="txtEMI"></label>
                                                                    </td>

                                                                    <td>Loan Duration</td>

                                                                    <td>
                                                                        <label id="txt_Loan_Duration"></label>
                                                                    </td>
                                                                    <td>New Loan No</td>

                                                                    <td>
                                                                        <label id="txt_New_Loan_No"></label>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Interest Rate (%)</td>

                                                                    <td>
                                                                        <label id="lbl_Interest_Rate"></label>
                                                                    </td>

                                                                    <td>Penal Rate (%)</td>

                                                                    <td>
                                                                        <label id="lbl_Penal_Rate"></label>
                                                                    </td>
                                                                    <td>Action Taken
                                                                    </td>
                                                                    <td>
                                                                        <label id="txtAction_Taken"></label>
                                                                    </td>
                                                                </tr>

                                                            </tbody>
                                                        </table>


                                                    </div>
                                                </div>
                                                <div class="row" style="margin: 1%;">


                                                    <%--  <div class="col-sm-6" style="display: none;">

                                                        <div class="Sub_Header_Abstract">ABSTRACT</div>


                                                        <table class="table_Abstract" style="width: 100%; height: 212px !important;">
                                                            <tbody>
                                                                <tr>
                                                                    <td>Principal</td>

                                                                    <td>
                                                                        <label id="txtTotal_Principal">500000</label>
                                                                    </td>

                                                                </tr>
                                                                <tr>
                                                                    <td>Interest</td>

                                                                    <td>
                                                                        <label id="txtTotal_Interest">138281.04</label>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Penal</td>

                                                                    <td>
                                                                        <label id="txtTotal_Penal">9652.91</label>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Remittance</td>

                                                                    <td>
                                                                        <label id="txtTotal_Remittance">304000</label>
                                                                    </td>
                                                                </tr>
                                                                <tr style="position: relative; height: 35px;">
                                                                    <td></td>

                                                                    <td></td>

                                                                </tr>
                                                            </tbody>
                                                        </table>

                                                        <div class="Sub_Header_Abstract">CHARGES/WAIVER/WRITE OFF</div>

                                                        <table class="table_Abstract" style="width: 100%;">
                                                            <tbody>
                                                                <tr>
                                                                    <td>Principal Adj.</td>

                                                                    <td>
                                                                        <label id="txtPrincipal_Adj">-15000</label>
                                                                    </td>

                                                                </tr>
                                                                <tr>
                                                                    <td>Interest Adj.</td>

                                                                    <td>
                                                                        <label id="txtInterest_Adj">0.00</label>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Penal Adj.</td>

                                                                    <td>
                                                                        <label id="txtPenal_Adj">0.00</label>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>C/B Adj.</td>

                                                                    <td>
                                                                        <label id="txtC/B_Adj">0.00</label>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Action Taken</td>

                                                                    <td>
                                                                        <label id="txtAction_Taken">No Action</label>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Referance Date</td>

                                                                    <td>
                                                                        <label id="txtReferance_Date"></label>
                                                                    </td>
                                                                </tr>

                                                            </tbody>
                                                        </table>


                                                    </div>--%>


                                                    <div class="col-sm-6">
                                                        <div class="Sub_Header_Abstract">RECOVERED AMOUNT DETAILS</div>


                                                        <table class="table_Abstract" style="width: 100%;">
                                                            <tbody>
                                                                <tr>
                                                                    <td>Principal</td>

                                                                    <td>
                                                                        <label id="txtRecovered_Principal"></label>
                                                                    </td>

                                                                </tr>
                                                                <tr>
                                                                    <td>Interest</td>

                                                                    <td>
                                                                        <label id="txtRecovered_Interest"></label>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Penal</td>

                                                                    <td>
                                                                        <label id="txtRecovered_Penal"></label>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>C/B Adj.</td>

                                                                    <td>
                                                                        <label id="txtRecovered_CB_Adj"></label>
                                                                    </td>
                                                                </tr>

                                                                <tr>
                                                                    <td>Total</td>

                                                                    <td>
                                                                        <label id="txtTotal_Recovered_Amount"></label>
                                                                    </td>
                                                                </tr>


                                                            </tbody>
                                                        </table>


                                                    </div>

                                                    <div class="col-sm-6">
                                                        <div class="Sub_Header_Abstract">AMOUNT OUTSTANDING</div>

                                                        <table class="table_Abstract" style="width: 100%;">
                                                            <tbody>
                                                                <tr>
                                                                    <td>Principal</td>

                                                                    <td>
                                                                        <label id="txtPrincipal_Outstanding"></label>
                                                                    </td>

                                                                </tr>
                                                                <tr>
                                                                    <td>Interest</td>

                                                                    <td>
                                                                        <label id="txtInterest_Outstanding"></label>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Penal</td>

                                                                    <td>
                                                                        <label id="txtPenal_Outstanding"></label>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>C/B Adj.</td>

                                                                    <td>
                                                                        <label id="txt_CB_Adj">0.00</label>
                                                                    </td>
                                                                </tr>

                                                                <tr>
                                                                    <td>Total</td>

                                                                    <td>
                                                                        <label id="txtTotal_Outatanding"></label>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td style="color: red; font-weight: bold;">Default Amount(Def.EMI+Penal)</td>

                                                                    <td>
                                                                        <label style="color: red; font-weight: bold; font-size: 14px !important;" id="lbl_Def_Amount"></label>
                                                                    </td>
                                                                </tr>

                                                                <tr>
                                                                    <td style="color: red; font-weight: bold;">Default Installement</td>

                                                                    <td>
                                                                        <label style="color: red; font-weight: bold; font-size: 14px !important;" id="lbl_Def_Installement"></label>
                                                                    </td>
                                                                </tr>

                                                            </tbody>
                                                        </table>

                                                    </div>



                                                </div>
                                            </div>
                                        </div>


                                    </div>

                                    <div class="row">
                                        <div class="col-12">
                                            <div class="form-group row" id="LoanClosure">
                                                <div class="col-sm-12" style="text-align: center;">
                                                    <span id="SPAN_LoanClosure" style="color: red; font-weight: bold;"></span>
                                                    <br />
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-12" style="text-align: center;">

                                                    <span id="SPAN_Balance" style="color: red; font-weight: bold;"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-12">

                                            <div class="form-group" style="display:none;"  >
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="chk_Prathyasa">
                                                    <label class="form-check-label">Is Prathyasa</label>
                                                </div>

                                            </div>


                                            <div class="form-group row">
                                                <label for="dropType" class="col-sm-3 col-form-label">Type*</label>
                                                <div class="col-sm-9">
                                                    <select id="dropType" class="form-control">
                                                        <option selected="selected" value="0">Normal Repayment</option>
                                                        <option value="1">Salary Recovery</option>
                                                        <option value="2">Revenue Recovery</option>
                                                       <option value="3">Prathyasa</option>
                                                        
                                                    </select>
                                                </div>
                                            </div>




                                            <div class="form-group row" style="display: none;">
                                                <label for="txtReceipt_Number" class="col-sm-3 col-form-label">Receipt Number*</label>
                                                <div class="col-sm-9">
                                                    <input type="text" id="txtReceipt_Number" disabled="disabled" class="form-control" />

                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label for="dropMode_Payment" class="col-sm-3 col-form-label">Mode of Payment*</label>
                                                <div class="col-sm-9">
                                                    <select id="dropMode_Payment" class="form-control">
                                                        <option value="Cheque">Cheque</option>
                                                        <option value="Bank">Bank</option>
                                                        <option value="DD">DD</option>
                                                        <option value="Cash" selected="selected">Cash</option>
                                                        <option value="MO">MO</option>
                                                        <option value="Treasury">Treasury</option>
                                                        <option value="Prathyasa">Prathyasa</option>


                                                    </select>
                                                </div>
                                            </div>

                                            <div class="form-group row" style="display: none;">
                                                <label for="txtDD_Or_Cheque_No" class="col-sm-3 col-form-label">DD / Cheque No.*</label>
                                                <div class="col-sm-9">
                                                    <input type="text" class="form-control" id="txtDD_Or_Cheque_No" placeholder="DD / Cheque No">
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label for="txtDate_Of_Transaction" class="col-sm-3 col-form-label">Date Of Transaction</label>
                                                <div class="col-sm-9">
                                                    <input type="text" class="form-control" id="txtDate_Of_Transaction" placeholder="dd/mm/yyyy">
                                                </div>
                                            </div>



                                            <div class="form-group row">
                                                <label for="txtAmount" class="col-sm-3 col-form-label">Amount*</label>
                                                <div class="col-sm-9">
                                                    <input type="number" class="form-control" id="txtAmount" placeholder="Amount">
                                                </div>
                                            </div>


                                            <div class="form-group row">
                                                <label for="inputName2" class="col-sm-3 col-form-label">Remarks*</label>
                                                <div class="col-sm-9">
                                                    <textarea class="form-control" maxlength="200" id="txtRemarks_JS" placeholder="Remarks"></textarea>
                                                    <span class="validation_message">Maximum text length is 200</span>
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <div class="col-sm-12 text-right">
                                                    <a style="display: none;" onclick="Loan_Details();" class="btn btn-success">Loanee details</a>
                                                    <a style="display: none;" onclick="Print();" class="btn btn-success">Print</a>
                                                    <a href="Receipt_Loan_Remittane_List.aspx" class="btn btn-dark">Back</a>
                                                    <a onclick="Save();" id="btn_Submit_Print" class="btn btn-success">Preview</a>
                                                </div>
                                            </div>


                                        </div>
                                    </div>
                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <input type="hidden" id="ClosingBalance" name="ClosingBalance" value="0">
    <input type="hidden" id="ClosureApproval" name="ClosureApproval" value="0">
    <input type="hidden" id="FCODE" name="FCODE" value="">
    <input type="hidden" id="Waiver" name="Waiver" value="0">

    <!-- /.content -->

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <script src="assets/plugins/toastr/toastr.min.js"></script>



    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.js"></script>
    <script>

        var acc = document.getElementsByClassName("accordion");
        var i;

        for (i = 0; i < acc.length; i++) {
            acc[i].addEventListener("click", function () {
                this.classList.toggle("active");
                var panel = this.nextElementSibling;
                if (panel.style.display === "block") {
                    panel.style.display = "none";
                } else {
                    panel.style.display = "block";
                }
            });
        }




        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;
        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }
        $(document).ready(function () {
            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtDate_Of_Transaction').val(formattedDate);

            $('#LoanClosure').hide();


            Select_Lonee_Details_And_Account();
            Load_RR_Details();


            Load_Lonee_Details();
            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            $('#txtAmount').on('copy paste', function (e) {
                e.preventDefault();
            });

            $("#txtAmount").on("input", function () {
                this.value = this.value.split('.')[0];
                var inputValue = $(this).val();
                var ClosingBalance = $("#ClosingBalance").val();
                var ClosureApproval = $("#ClosureApproval").val();
                if (ClosureApproval == "0") {
                    if (parseInt(inputValue) >= parseInt(ClosingBalance)) {
                        Toast.fire({
                            icon: 'error',
                            title: 'Get Approval for Loan Closure from Manager !!!'
                        })
                        $("#btn_Submit_Print").css("display", "none");
                    }
                    else {

                        $("#btn_Submit_Print").css("display", "inline");
                    }
                }
                //else if ((ClosureApproval == "1") && ($("#Waiver").val() == "0")) {
                //    if (parseInt(inputValue) < parseInt(ClosingBalance)) {
                //        Toast.fire({
                //            icon: 'error',
                //            title: 'Plz Remit Rs. ' + parseInt(ClosingBalance) + '/-  For Closure'
                //        })
                //        $("#btn_Submit_Print").css("display", "none");
                //    }
                //    else {

                //        $("#btn_Submit_Print").css("display", "inline");
                //    }
                //}
                else if ((ClosureApproval == "1") && ($("#Waiver").val() == "1")) {
                    if (parseInt(inputValue) != parseInt(ClosingBalance)) {
                        Toast.fire({
                            icon: 'error',
                            title: 'Waiver Applied, Repayment Amount Should be Equal to Closing Balance : ' + ClosingBalance
                        })
                        $("#btn_Submit_Print").css("display", "none");
                    }
                    else {

                        $("#btn_Submit_Print").css("display", "inline");
                    }
                }
            });

            // Get the current date
            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtApplication_Date').val(formattedDate);

            $("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
            $("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");
            Load_All_Schemes();
            //   Load_All_Districts();

            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }




            $(".form-control").on("focusout", function () {
                $(".form-control").removeAttr("style");

                var This_Id = $(this).attr("id");
                var This_Control = $(this);
                if (This_Id == "txtApplicant_Name") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Name of Applicant is required !'
                        })
                    }
                    else if (!Is_Valid_Text(This_Control.val().trim())) {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Special characters or Numbers not allowed in Name of Applicant !'
                        })
                    }
                }
                else if (This_Id == "dropScheme") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Loan Scheme is required !'
                        })
                    }
                }
                else if (This_Id == "dropCast") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Caste is required !'
                        })
                    }
                }
                else if (This_Id == "dropSubCast") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Sub Caste is required !'
                        })
                    }
                }
                else if (This_Id == "txtPhoneNo") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Phone No is required !'
                        })
                    }
                }
                //else if (This_Id == "txtAadharNumber") {
                //    if (This_Control.val().trim() == "") {
                //        Focus_Error(This_Control);
                //        Toast.fire({
                //            icon: 'error',
                //            title: 'Aadhar No is required !'
                //        })
                //    }
                //}



                // Your common focusout function code goes here
                //   alert("Input value changed: " + value);
            });



            //$(document).on("input", ".form-control", function () {
            //    // Get the current value of the input
            //    var currentValue = $(this).val();

            //    // Convert the value to propcase
            //    var propcaseValue = currentValue.replace(/\w\S*/g, function (txt) {
            //        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
            //    });

            //    // Update the input with the propcase value
            //    $(this).val(propcaseValue);
            //});


            $('#dropType').change(function () {
                var optionSelected = $(this).find("option:selected");
                var valueSelected = optionSelected.val();
                if (valueSelected == "3") {
                    $("#dropMode_Payment").attr("disabled", "disabled");
                    $("#dropMode_Payment").append('<option value="Prathyasa">Prathyasa</option>');
                    $("#dropMode_Payment").val("Prathyasa");
                }
                else {
                    $("#dropMode_Payment").removeAttr("disabled");
                    $("#dropMode_Payment option[value='Prathyasa']").remove();
                   
                    
                }
            });

        });




        function Load_All_Districts() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropDistrict').empty();
                    $('#dropDistrict').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var District_Name = value.District_Name;
                        var html = '<option value="' + Id + '">' + District_Name + '</option>';
                        if (Id != 15) {
                            $('#dropDistrict').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $('#dropDistrict').change(function () {
                    Load_All_Sub_Districts($(this).val());
                });

            });

        }
        function Load_All_Sub_Districts(District_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Districts",
                data: JSON.stringify({ District_Id: District_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubDistrict').empty();
                    $('#dropSubDistrict').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Sub_District_Name = value.Sub_District_Name;
                        var html = '<option value="' + Id + '">' + Sub_District_Name + '</option>';

                        $('#dropSubDistrict').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });

        }

        function Load_All_Schemes() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Schemes",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropScheme').empty();
                    $('#dropScheme').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var IsActive = value.IsActive;
                        var Scheme = value.Scheme;
                        var html = '<option value="' + Id + '">' + Scheme + '</option>';
                        if (IsActive == 1) {
                            $('#dropScheme').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_All_Cast();

            });

        }

        function Load_All_Cast() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Cast",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropCast').empty();
                    $('#dropCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var Cast = value.Cast;
                        var html = '<option value="' + Id + '">' + Cast + '</option>';

                        $('#dropCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $('#dropCast').change(function () {
                    Load_All_SubCast($(this).val());
                });

            });

        }


        function Load_All_SubCast(Cast_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Cast",
                data: JSON.stringify({ Cast_Id: Cast_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubCast').empty();
                    $('#dropSubCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var SubCast = value.SubCast;
                        var html = '<option value="' + Id + '">' + SubCast + '</option>';

                        $('#dropSubCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {


            });

        }
        var Amt;
        function Load_Loan_Wavier_Amount() {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Int_Loanno: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Get_Waiver_Details_By_Loan_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $("#Waiver").val(0);
                    $.each(data.d, function (key, value) {
                          Amt = parseFloat($("#txtTotal_Outatanding").text()) - parseFloat(value.int_wavier_amt);
                        //$("#SPAN_LoanClosure").text("Approved For Closure");
                        $("#SPAN_Balance").text("Balance Amount For Loan Closure as per Online Register - " + Math.ceil(Amt) + " /- ( After Eligible Wavier of Rs " + parseFloat(value.int_wavier_amt) + " )");
                        $("#ClosingBalance").val(Math.ceil(Amt));
                        $("#Waiver").val(1);
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }

        function Select_Loan_Closure_Approval_By_Loan_No() {

            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ int_Loanno: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Select_Loan_Closure_Approval_By_Loan_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $("#LoanClosure").hide();
                    $.each(data.d, function (key, value) {
                        var ForClosing = value.int_ForClosing
                        $("#ClosureApproval").val(ForClosing);
                        if (ForClosing == 0) { $("#LoanClosure").hide(); }
                        else if (ForClosing == 1) {
                            $("#LoanClosure").show();
                            $("#SPAN_LoanClosure").text("Approved For Closure");
                            Load_Loan_Wavier_Amount();
                        }
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }

        function formatDate(inputDate) {
            // Extract the timestamp from the input string
            var timestamp = parseInt(inputDate.match(/\d+/)[0]);
            // Convert to milliseconds
            var date = new Date(timestamp);
            // Extract year, month, and day
            var year = date.getFullYear();
            var month = ('0' + (date.getMonth() + 1)).slice(-2); // Months are zero based
            var day = ('0' + date.getDate()).slice(-2);
            // Format the date as mm/dd/yyyy
            var formattedDate = day + '/' + month + '/' + year;
            return formattedDate;
        }


        function Select_Lonee_Details_And_Account() {
            //txtNoInstalments
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ int_loanno: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Select_Lonee_Details_And_Account",
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $.each(data.d, function (key, value) {
                        $("#txt_New_Loan_No").text(value.int_loanno);
                        $("#txtLoanee_Name").text(value.vchr_applname);
                        $("#FCODE").val(value.vchr_fund);
                        $("#txtAddress").text(value.vchr_hsename);
                        $("#txtOld_Agr_No").text(value.vchr_remark);
                        $("#txtDue_Date").text(value.dt_first_due_date);
                        $("#txtLastRepayDate").text(value.Formated_dt_last_repay_date);
                        $("#txtDisbDate").text(value.Formated_dt_disbdate);
                        $("#txtEMI").text(value.mny_repayamt);
                        $("#txt_Loan_Duration").text(value.int_repay_inst + " Monthly");
                        $("#lbl_Interest_Rate").text(value.int_rate_int);
                        $("#lbl_Penal_Rate").text(value.int_rate_penal);
                        $("#SPAN_Principal").text(value.mny_loanbal);
                        $("#SPAN_Interest").text(value.Total_Interest);
                        $("#SPAN_Penal").text(value.Total_penal);
                        $("#SPAN_Remitance").text(value.Total_Remitance);
                        $("#txtScheme_Name").text(value.Scheme_Name);
                        $("#txtLoan_Acc_No").text(value.vchr_oldno);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });
        }


        function Load_Lonee_Details() {
            //txtNoInstalments
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ int_loanno: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Select_Lonee_Details_By_Loan_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $.each(data.d, function (key, value) {
                        $("#txtNoInstalments").val(value.int_repay_inst);
                        $("#txtOldLoanNumber").val(value.vchr_oldno);
                        $("#txtScheme").val(value.vchr_fund);
                        $("#txtReligion").val(value.vchr_caste);
                        $("#txtLoanAmount").val(value.mny_disbamt);
                        $("#txtTotalPreFixedInt").val('0');
                        $("#txtEMI").val(value.mny_repayamt);
                        $("#txtPreFixedIntInstal").val('0');
                        $("#txtInterestRate").val(value.int_rate_int);
                        $("#txtPenalIntRate").val(value.int_rate_penal);
                        $("#txtDisbursementDate").val(value.dt_disbdate);
                        $("#txtFirstDueDate").val(value.dt_first_due_date);
                        $("#txtRRCNo").val('');
                        $("#txtRRDate").val('');
                        $("#ADDRESS").val(value.vchr_hsename);
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Get_Current_Date_Entry();

            });
        }


        function Get_Current_Date_Entry() {
            //txtNoInstalments
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ LoanNumber: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Get_Current_Date_Entry",
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    // Get the current date
                    var currentDate = new Date();
                    var day = currentDate.getDate();
                    var month = (currentDate.getMonth() + 1);
                    if (day < 10) {
                        day = '0' + day;
                    }
                    if (month < 10) {
                        month = '0' + month;
                    }
                    // Format the date as desired (e.g., "MM/DD/YYYY")
                    var formattedDate = day + '/' + month + '/' + currentDate.getFullYear();
                    // alert(currentDate.getMonth() );



                    $.each(data.d, function (key, value) {
                        $("#txtPrincipal_Outstanding").text(value.int_prin_dues.toFixed(2));
                        $("#txtInterest_Outstanding").text(value.int_int_dues.toFixed(2));
                        $("#txtPenal_Outstanding").text(value.int_penal_dues.toFixed(2));
                        $("#txtCB_Adj_Outstanding").text(parseFloat(value.int_debit.toFixed(2)));

                        var Total = parseFloat(value.int_prin_dues.toFixed(2)) + parseFloat(value.int_int_dues.toFixed(2)) + parseFloat(value.int_penal_dues.toFixed(2)) + parseFloat(value.int_debit.toFixed(2));
                        $("#txtTotal_Outatanding").text(parseFloat(Total.toFixed(2)));


                        $("#txt_Total_Amount").val($("#txtTotal_Outatanding").text());

                        $("#txt_CB_Adj").text(parseFloat(value.int_debit.toFixed(2)));






                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {


                Get_Recovered_Amount_Details();
            });
        }

        function Get_Recovered_Amount_Details() {
            //txtNoInstalments
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Int_Loanno: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Get_Recovered_Amount_Details",
                contentType: "application/json; charset=utf-8",
                success: function (data) {



                    $.each(data.d, function (key, value) {
                        $("#txtRecovered_Principal").text(value.Princple_Amount.toFixed(2));
                        $("#txtRecovered_Interest").text(value.Interest_Remited.toFixed(2));
                        $("#txtRecovered_Penal").text(value.Penal_Remitted.toFixed(2));
                        $("#txtRecovered_CB_Adj").text(value.Bank_Charges.toFixed(2));
                        var Recovered_Amount = parseFloat(value.Princple_Amount.toFixed(2)) +
                            parseFloat(value.Interest_Remited.toFixed(2)) +
                            parseFloat(value.Penal_Remitted.toFixed(2)) +
                            parseFloat(value.Bank_Charges.toFixed(2))

                        $("#txtTotal_Recovered_Amount").text(parseFloat(Recovered_Amount).toFixed(2));







                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Check_Period_Over()

            });
        }
        function Load_RR_Details() {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ int_Loanno: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Select_RR_Details_By_Loan_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $.each(data.d, function (key, value) {

                        const inputDate = value.dt_RR_Date;
                        const parts = inputDate.split('/');
                        if (parts.length === 3) {
                            const yyyy = parts[2];
                            const mm = parts[1];
                            const dd = parts[0];

                            const formattedDate = `${yyyy}-${mm}-${dd}`;

                            // Set the value of the date input
                            $('#txtRRC_Date').val(formattedDate);
                        }

                        $("#txt_RR_Status").val("RR");
                        $("#txtRRC_No").val(value.vchr_RRCno);
                        $("#txtRR_Demand").val(value.int_rrdemand);
                        $("#txtTaluk_Ref_No").val(value.vchr_TRRCno);
                        $("#txtRemark_RR").val(value.vchr_RRRemarks);



                        //$("#txt_RR_Status").attr("disabled", "disabled");
                        //$("#txtRRC_No").attr("disabled", "disabled");
                        //$("#txtRR_Demand").attr("disabled", "disabled");
                        //$("#txtTaluk_Ref_No").attr("disabled", "disabled");
                        //$("#txtRemark_RR").attr("disabled", "disabled");

                        //$("#btn_RR_Save").css("display", "none");

                        if ($("#txtAction_Taken").text() == "No Action") {
                            $("#txtAction_Taken").text("RR");
                        }
                        else {
                            $("#txtAction_Taken").text($("#txtAction_Taken").text() + "," + "RR");
                        }




                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_SR_Details();

            });
        }


        function Load_SR_Details() {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ int_Loanno: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Select_SR_Details_By_Loan_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $.each(data.d, function (key, value) {

                        const inputDate = value.dt_SR_Date;
                        const parts = inputDate.split('/');
                        if (parts.length === 3) {
                            const yyyy = parts[2];
                            const mm = parts[1];
                            const dd = parts[0];

                            const formattedDate = `${yyyy}-${mm}-${dd}`;

                            // Set the value of the date input
                            $('#txtSR_Date').val(formattedDate);
                        }

                        $("#txt_SR_Status").val("SR");



                        $("#txt_SR_Status").attr("disabled", "disabled");


                        $("#btn_SR_Save").css("display", "none");

                        if ($("#txtAction_Taken").text() == "No Action") {
                            $("#txtAction_Taken").text("SR");
                        }
                        else {
                            $("#txtAction_Taken").text($("#txtAction_Taken").text());
                        }





                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });
        }


        function Check_Period_Over() {
            //txtNoInstalments
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Int_Loanno: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Select_Period_Over_Check",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    if (data.d.length != 0) {
                        $("#SPAN_PeriodOver").text('      (PERIOD OVER)      ');
                        $("#txtAction_Taken").text($("#txtAction_Taken").text() + "," + " PERIOD OVER");
                    }
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Get_Default_Amount()
            });
        }

        function Get_Default_Amount() {
            //txtNoInstalments
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Reg_No:'',Int_Loanno: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Get_Default_Amount",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $.each(data.d, function (key, value) {
                        //  $("#lbl_Def_Amount").text(parseFloat(value.defamt) + parseFloat($("#txtPenal_Outstanding").text()));

                        var Def_Amount = parseFloat(value.defamt) + parseFloat($("#txtPenal_Outstanding").text());
                        $("#lbl_Def_Amount").text(parseFloat(Def_Amount).toFixed(2));

                        $("#lbl_Def_Installement").text(value.definst);
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                var Amt = parseFloat($("#txtTotal_Outatanding").text());
                $("#SPAN_Balance").text("Balance Amount For Loan Closure as per Online Register - " + Math.ceil(Amt) + " /-");
                $("#ClosingBalance").val(Math.ceil(Amt));

                Select_Loan_Closure_Approval_By_Loan_No();
            });
        }

        function formatDateddMMyyyy(inputDate) {
            var parts = inputDate.split('/');
            // Extract day, month, and year
            var day = parts[0];
            var month = parts[1];
            var year = parts[2];
            // Pad the month with a leading zero if necessary
            if (parseInt(month).length === 1) {
                month = '0' + month;
            }
            // Reconstruct the date string in the format "dd/mm/yyyy"
            var formattedDate = day + '/' + month + '/' + year;
            return formattedDate;
        }

        function Save() {
            //alert(Counter);

            var ClosureApproval = $("#ClosureApproval").val();
            var ClosingBalance = $("#ClosingBalance").val();
            var Amount = $("#txtAmount").val();
            if ($("#txtAmount").val().trim() === "" || parseInt($("#txtAmount").val()) === 0) {
                Focus_Error($("#txtAmount"));
                Toast.fire({
                    icon: 'error',
                    title: 'Please Check Repayment Amount !'
                })
            }
            else if ((ClosureApproval == "1") && ($("#Waiver").val() == "1") && (parseInt(Amount) != parseInt(ClosingBalance))) {
                Focus_Error($("#txtAmount"));
                Toast.fire({
                    icon: 'error',
                    title: 'Waiver Applied, Repayment Amount Should be Equal to Closing Balance : ' + ClosingBalance
                })
            }
            else if ($("#dropMode_Payment").val() == "Prathyasa" && parseFloat($("#txtAmount").val().trim()) != Math.ceil(parseFloat($("#txtTotal_Outatanding").text()))) {
                
                Focus_Error($("#txtAmount"));
                Toast.fire({
                    icon: 'error',
                    title: 'For Prathyasa Scheme, Repayment Amount Should be Equal to Closing Balance : ' + ClosingBalance
                })
            }
            else {
                var Rec_No;

                var TransactionDate = formatDateddMMyyyy($("#txtDate_Of_Transaction").val());
                var LastRepayDate = formatDateddMMyyyy($("#txtLastRepayDate").text());
                var DisbDate = formatDateddMMyyyy($("#txtDisbDate").text());

                var TransactionDate_Parts = TransactionDate.split('/');
                var LastRepayDate_Parts = LastRepayDate.split('/');
                var DisbDate_Parts = DisbDate.split('/');

                var TDate = new Date(TransactionDate_Parts[2], TransactionDate_Parts[1] - 1, TransactionDate_Parts[0]); // months are 0-based in JavaScript Date object
                var LRPDate = new Date(LastRepayDate_Parts[2], LastRepayDate_Parts[1] - 1, LastRepayDate_Parts[0]);
                var DDate = new Date(DisbDate_Parts[2], DisbDate_Parts[1] - 1, DisbDate_Parts[0]);

                var Today = new Date();
                Today.setHours(0, 0, 0, 0);
                var Yesterday = new Date(Today);
                Yesterday.setDate(Today.getDate() - 1);

                //if (LRPDate > TDate) {
                //    Focus_Error($("#txtDate_Of_Transaction"));
                //    Toast.fire({
                //        icon: 'error',
                //        title: 'Invalid Date !!! Transaction on  ' + LastRepayDate + ' already exist. Please check !'
                //    })
                //}
                //else if (TDate < DDate) {
                //    Focus_Error($("#txtDate_Of_Transaction"));
                //    Toast.fire({
                //        icon: 'error',
                //        title: 'Invalid Date !!! Please check !'
                //    })
                //}
                //else if (TDate > Today) {
                //    Focus_Error($("#txtDate_Of_Transaction"));
                //    Toast.fire({
                //        icon: 'error',
                //        title: 'Invalid Date !!! Please check !'
                //    })
                //}
                //else if (TDate < Yesterday) {
                //    Focus_Error($("#txtDate_Of_Transaction"));
                //    Toast.fire({
                //        icon: 'error',
                //        title: 'Invalid Date !!! Please check !'
                //    })
                //}
               // else {

                    var Is_Prathyasa = $("#chk_Prathyasa").is(":checked") ? $("#chk_Prathyasa").val() : "off";
                    if (Is_Prathyasa == 'on') {
                        Is_Prathyasa = '1';
                    }
                    else {
                        Is_Prathyasa = '0';
                    }

                
          
                        
                        if ($("#dropMode_Payment").val() != "Prathyasa") {
                            location.href = 'Loan_Remittance_Print.aspx?Reg_No=' + getParameterByName("Reg_No") + '&Amount=' + $("#txtAmount").val() + '&Name=' + $("#txtLoanee_Name").text() + '&LoanNumber=' + getParameterByName("loan_no") + "&Date=" + TransactionDate + "&Type=" + $("#dropType").val() + "&Mode=" + $("#dropMode_Payment").val();

                        }
                        else {
                            location.href = 'Loan_Remittance_Prathyasa_Print.aspx?Reg_No=' + getParameterByName("Reg_No")  + '&Amount=' + $("#txtAmount").val() + '&Name=' + $("#txtLoanee_Name").text() + '&LoanNumber=' + getParameterByName("loan_no") + "&Date=" + TransactionDate;

                        }
                    }
               // }


            }
        

        //function Save() {
        //    //alert(Counter);



        //    var Rec_No;

        //    $.ajax({
        //        type: "POST",
        //        dataType: "json",
        //        url: "WebService.asmx/Loan_Repayment",
        //        data: JSON.stringify({ LoanNumber: getParameterByName("loan_no"), Trans_Amount: $("#txtAmount").val(), DueDate: $("#SPAN_DueDate").text(), Transaction_Date: $("#txtDate_Of_Transaction").val() }), // If you have parameters
        //        contentType: "application/json; charset=utf-8",
        //        success: function (data) {
        //            if (data.d.Status == "Success") {
        //                Rec_No = data.d.Rec_No;
        //            }
        //        },
        //        error: function (jqXHR, textStatus, errorThrown) {
        //            // Handle AJAX error
        //            //   alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);

        //            Swal.fire({
        //                icon: 'error',
        //                title: 'Oops...',
        //                text: 'Contact your administrator for more information, Email Id: <EMAIL>',

        //            })

        //        }

        //    }).done(function () {
        //        location.href = 'Loan_Remittance_Print.aspx?Rec_No=' + Rec_No + '&Amount=' + $("#txtAmount").val() + '&Name=' + $("#SPAN_Name").text() + '&LoanNumber=' + getParameterByName("loan_no");

        //    });




        //}

        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }

        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }


        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }



    </script>
</asp:Content>

