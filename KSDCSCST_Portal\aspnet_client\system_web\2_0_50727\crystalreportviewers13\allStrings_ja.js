/* Copyright (c) Business Objects 2006. All rights reserved. */

var L_bobj_crv_MainReport = "\u30E1\u30A4\u30F3\u30EC\u30DD\u30FC\u30C8";
// Viewer Toolbar tooltips
var L_bobj_crv_FirstPage = "\u6700\u521D\u306E\u30DA\u30FC\u30B8\u3078";
var L_bobj_crv_PrevPage = "\u524D\u306E\u30DA\u30FC\u30B8\u3078";
var L_bobj_crv_NextPage = "\u6B21\u306E\u30DA\u30FC\u30B8\u3078";
var L_bobj_crv_LastPage = "\u6700\u5F8C\u306E\u30DA\u30FC\u30B8\u3078";
var L_bobj_crv_ParamPanel = "\u30D1\u30E9\u30E1\u30FC\u30BF\u30D1\u30CD\u30EB";
var L_bobj_crv_Parameters = "\u30D1\u30E9\u30E1\u30FC\u30BF";
var L_bobj_crv_GroupTree = "\u30B0\u30EB\u30FC\u30D7\u30C4\u30EA\u30FC";
var L_bobj_crv_DrillUp = "\u30C9\u30EA\u30EB\u30A2\u30C3\u30D7";
var L_bobj_crv_Refresh = "\u30EC\u30DD\u30FC\u30C8\u3092\u6700\u65B0\u8868\u793A";
var L_bobj_crv_Zoom = "\u30BA\u30FC\u30E0";
var L_bobj_crv_PageNav = "\u30DA\u30FC\u30B8\u306E\u30CA\u30D3\u30B2\u30FC\u30C8";
var L_bobj_crv_SelectPage = "\u6307\u5B9A\u306E\u30DA\u30FC\u30B8\u3078\u30B8\u30E3\u30F3\u30D7";
var L_bobj_crv_SearchText = "\u30C6\u30AD\u30B9\u30C8\u306E\u691C\u7D22";
var L_bobj_crv_Export = "\u3053\u306E\u30EC\u30DD\u30FC\u30C8\u3092\u30A8\u30AF\u30B9\u30DD\u30FC\u30C8\u3059\u308B";
var L_bobj_crv_Print = "\u3053\u306E\u30EC\u30DD\u30FC\u30C8\u3092\u51FA\u529B\u3059\u308B";
var L_bobj_crv_TabList = "\u30BF\u30D6\u4E00\u89A7";
var L_bobj_crv_Close = "\u9589\u3058\u308B";
var L_bobj_crv_Logo=  "Business Objects \u30ED\u30B4";
var L_bobj_crv_FileMenu = "[\u30D5\u30A1\u30A4\u30EB] \u30E1\u30CB\u30E5\u30FC";

var L_bobj_crv_File = "\u30D5\u30A1\u30A4\u30EB";

var L_bobj_crv_Show = "\u8868\u793A";
var L_bobj_crv_Hide = "\u975E\u8868\u793A";

var L_bobj_crv_Find = "\u691C\u7D22 ...";
var L_bobj_crv_of = "%1 (\u5168\u4F53: %2)"; // Example: Page "1 of 3"

var L_bobj_crv_submitBtnLbl = "\u30A8\u30AF\u30B9\u30DD\u30FC\u30C8";
var L_bobj_crv_ActiveXPrintDialogTitle = "\u5370\u5237";
var L_bobj_crv_PDFPrintDialogTitle = "PDF \u306B\u51FA\u529B";
var L_bobj_crv_PrintRangeLbl = "\u30DA\u30FC\u30B8\u7BC4\u56F2:";
var L_bobj_crv_PrintAllLbl = "\u3059\u3079\u3066\u306E\u30DA\u30FC\u30B8";
var L_bobj_crv_PrintPagesLbl = "\u30DA\u30FC\u30B8\u306E\u9078\u629E";
var L_bobj_crv_PrintFromLbl = "\u958B\u59CB:";
var L_bobj_crv_PrintToLbl = "\u7D42\u4E86:";
var L_bobj_crv_PrintInfoTitle = "PDF \u306B\u51FA\u529B:";
var L_bobj_crv_PrintInfo1 = '\u30D3\u30E5\u30FC\u30A2\u306F PDF \u306B\u30A8\u30AF\u30B9\u30DD\u30FC\u30C8\u3057\u3066\u5370\u5237\u3059\u308B\u5FC5\u8981\u304C\u3042\u308A\u307E\u3059\u3002\u30C9\u30AD\u30E5\u30E1\u30F3\u30C8\u304C\u958B\u3044\u305F\u3089 PDF \u30EA\u30FC\u30C0\u30FC\u30A2\u30D7\u30EA\u30B1\u30FC\u30B7\u30E7\u30F3\u304B\u3089 [\u5370\u5237] \u30AA\u30D7\u30B7\u30E7\u30F3\u3092\u9078\u629E\u3057\u3066\u304F\u3060\u3055\u3044\u3002';
var L_bobj_crv_PrintInfo2 = '\u6CE8: \u5370\u5237\u3059\u308B\u306B\u306F\u3001PDF \u30EA\u30FC\u30C0\u30FC (Adobe Reader \u306A\u3069) \u304C\u30A4\u30F3\u30B9\u30C8\u30FC\u3055\u308C\u3066\u3044\u308B\u5FC5\u8981\u304C\u3042\u308A\u307E\u3059\u3002';
var L_bobj_crv_PrintPageRangeError = "\u6709\u52B9\u306A\u30DA\u30FC\u30B8\u7BC4\u56F2\u3092\u5165\u529B\u3057\u307E\u3059\u3002";

var L_bobj_crv_ExportBtnLbl = "\u30A8\u30AF\u30B9\u30DD\u30FC\u30C8";
var L_bobj_crv_ExportDialogTitle = "\u30A8\u30AF\u30B9\u30DD\u30FC\u30C8";
var L_bobj_crv_ExportFormatLbl = "\u30D5\u30A1\u30A4\u30EB\u5F62\u5F0F:";
var L_bobj_crv_ExportInfoTitle = "\u30A8\u30AF\u30B9\u30DD\u30FC\u30C8\u3059\u308B\u306B\u306F:";

var L_bobj_crv_ParamsApply = "\u9069\u7528";
var L_bobj_crv_ParamsAdvDlg = "\u30D1\u30E9\u30E1\u30FC\u30BF\u5024\u306E\u7DE8\u96C6";
var L_bobj_crv_ParamsDeleteTooltip = "\u30D1\u30E9\u30E1\u30FC\u30BF\u5024\u306E\u524A\u9664";
var L_bobj_crv_ParamsAddValue = "\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u8FFD\u52A0...";
var L_bobj_crv_ParamsApplyTip = "[\u9069\u7528] \u30DC\u30BF\u30F3 (\u6709\u52B9)";
var L_bobj_crv_ParamsApplyDisabledTip = "\uFF3B\u9069\u7528\uFF3D \u30DC\u30BF\u30F3 (\u7121\u52B9)";
var L_bobj_crv_ParamsDlgTitle = "\u5024\u306E\u5165\u529B";
var L_bobj_crv_ParamsCalBtn = "\uFF3B\u30AB\u30EC\u30F3\u30C0\uFF3D \u30DC\u30BF\u30F3";
var L_bobj_crv_Reset= "\u30EA\u30BB\u30C3\u30C8";
var L_bobj_crv_ResetTip = "[\u30EA\u30BB\u30C3\u30C8] \u30DC\u30BF\u30F3 (\u6709\u52B9)";
var L_bobj_crv_ResetDisabledTip = "\uFF3B\u30EA\u30BB\u30C3\u30C8\uFF3D \u30DC\u30BF\u30F3 (\u7121\u52B9)";
var L_bobj_crv_ParamsDirtyTip = "\u30D1\u30E9\u30E1\u30FC\u30BF\u5024\u304C\u5909\u66F4\u3055\u308C\u307E\u3057\u305F\u3002\u5909\u66F4\u3092\u9069\u7528\u3059\u308B\u306B\u306F \uFF3B\u9069\u7528\uFF3D \u30DC\u30BF\u30F3\u3092\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u304F\u3060\u3055\u3044\u3002";
var L_bobj_crv_ParamsDataTip = "\u3053\u308C\u306F\u3001\u30C7\u30FC\u30BF\u30D5\u30A7\u30C3\u30C1\u30D1\u30E9\u30E1\u30FC\u30BF\u3067\u3059";
var L_bobj_crv_ParamsMaxNumDefaultValues = "\u305D\u306E\u4ED6\u306E\u30A2\u30A4\u30C6\u30E0\u306B\u3064\u3044\u3066\u306F\u3053\u3053\u3092\u30AF\u30EA\u30C3\u30AF...";
var L_bobj_crv_paramsOpenAdvance = "\'%1\' \u306E\u8A73\u7D30\u30D7\u30ED\u30F3\u30D7\u30C8\u30DC\u30BF\u30F3";

var L_bobj_crv_ParamsInvalidTitle = "\u30D1\u30E9\u30E1\u30FC\u30BF\u5024\u304C\u7121\u52B9\u3067\u3059";
var L_bobj_crv_ParamsTooLong = "\u30D1\u30E9\u30E1\u30FC\u30BF\u5024\u306E\u9577\u3055\u306F %1 \u6587\u5B57\u307E\u3067\u3067\u3059";
var L_bobj_crv_ParamsTooShort = "\u30D1\u30E9\u30E1\u30FC\u30BF\u5024\u306E\u9577\u3055\u306F %1 \u6587\u5B57\u4EE5\u4E0A\u306B\u3059\u308B\u5FC5\u8981\u304C\u3042\u308A\u307E\u3059";
var L_bobj_crv_ParamsBadNumber = "\u3053\u306E\u30D1\u30E9\u30E1\u30FC\u30BF\u306E\u578B\u306F \"\u6570\u5024\" \u3067\u3001\u8CA0\u53F7\u3001\u6570\u5B57 (\"0-9\")\u3001\u304A\u3088\u3073\u5C0F\u6570\u70B9\u3092\u793A\u3059\u30D4\u30EA\u30AA\u30C9\u307E\u305F\u306F\u30AB\u30F3\u30DE\u306E\u307F\u3092\u542B\u3080\u3053\u3068\u304C\u3067\u304D\u307E\u3059\u3002\u5165\u529B\u3055\u308C\u305F\u30D1\u30E9\u30E1\u30FC\u30BF\u5024\u3092\u4FEE\u6B63\u3057\u3066\u304F\u3060\u3055\u3044\u3002";
var L_bobj_crv_ParamsBadCurrency = "\u3053\u306E\u30D1\u30E9\u30E1\u30FC\u30BF\u306E\u578B\u306F \"\u901A\u8CA8\" \u3067\u3001\u8CA0\u53F7\u3001\u6570\u5B57 (\"0-9\")\u3001\u304A\u3088\u3073\u5C0F\u6570\u70B9\u3092\u793A\u3059\u30D4\u30EA\u30AA\u30C9\u307E\u305F\u306F\u30AB\u30F3\u30DE\u306E\u307F\u3092\u542B\u3080\u3053\u3068\u304C\u3067\u304D\u307E\u3059\u3002\u5165\u529B\u3055\u308C\u305F\u30D1\u30E9\u30E1\u30FC\u30BF\u5024\u3092\u4FEE\u6B63\u3057\u3066\u304F\u3060\u3055\u3044\u3002";
var L_bobj_crv_ParamsBadDate = "\u3053\u306E\u30D1\u30E9\u30E1\u30FC\u30BF\u306E\u578B\u306F \"\u65E5\u4ED8\" \u3067\u3001\u305D\u306E\u5F62\u5F0F\u306F \"%1\" \u3067\u3059\u3002\"yyyy\" \u306F\u5E74\u3092\u8868\u3059 4 \u6841\u306E\u6570\u5B57\u3001\"mm\" \u306F\u6708 (\u4F8B: 1 \u6708 = 1)\u3001\"dd\" \u306F\u305D\u306E\u6708\u306E\u65E5\u3092\u8868\u3057\u307E\u3059\u3002";
var L_bobj_crv_ParamsBadTime = "\u3053\u306E\u30D1\u30E9\u30E1\u30FC\u30BF\u306E\u578B\u306F \"\u6642\u523B\" \u3067\u3001\u305D\u306E\u5F62\u5F0F\u306F \"hh:mm:ss\" \u3067\u3059\u3002\"hh\" \u306F 24 \u6642\u9593\u8868\u793A\u306B\u3088\u308B\u6642\u9593\u3001\"mm\" \u306F\u5206\u3001\"ss\" \u306F\u79D2\u3092\u8868\u3057\u307E\u3059\u3002";
var L_bobj_crv_ParamsBadDateTime = "\u3053\u306E\u30D1\u30E9\u30E1\u30FC\u30BF\u306E\u578B\u306F \"\u65E5\u6642\" \u3067\u3001\u305D\u306E\u5F62\u5F0F\u306F \"%1 hh:mm:ss\" \u3067\u3059\u3002\"yyyy\" \u306F\u5E74\u3092\u8868\u3059 4 \u6841\u306E\u6570\u5B57\u3001\"mm\" \u306F\u6708 (\u4F8B: 1 \u6708 = 1)\u3001\"dd\" \u306F\u65E5\u3001\"hh\" \u306F 24 \u6642\u9593\u8868\u793A\u306B\u3088\u308B\u6642\u9593\u3001\"mm\" \u306F\u5206\u3001\"ss\" \u306F\u79D2\u3092\u8868\u3057\u307E\u3059\u3002";
var L_bobj_crv_ParamsMinTooltip = "%2 \u4EE5\u4E0A\u306E %1 \u306E\u5024\u3092\u6307\u5B9A\u3057\u3066\u304F\u3060\u3055\u3044\u3002";
var L_bobj_crv_ParamsMaxTooltip = "%2 \u4EE5\u4E0B\u306E %1 \u306E\u5024\u3092\u6307\u5B9A\u3057\u3066\u304F\u3060\u3055\u3044\u3002";
var L_bobj_crv_ParamsMinAndMaxTooltip = "%2 \u304B\u3089 %3 \u306E\u9593\u306E %1 \u306E\u5024\u3092\u6307\u5B9A\u3057\u3066\u304F\u3060\u3055\u3044\u3002";
var L_bobj_crv_ParamsStringMinOrMaxTooltip = "\u3053\u306E\u30D5\u30A3\u30FC\u30EB\u30C9\u306E %1 \u306E\u9577\u3055\u306F %2 \u3067\u3059\u3002 ";
var L_bobj_crv_ParamsStringMinAndMaxTooltip = "%1 \uFF5E %2 \u306E\u9593\u306E\u5024\u3092\u6307\u5B9A\u3057\u3066\u304F\u3060\u3055\u3044\u3002";
var L_bobj_crv_ParamsYearToken = "yyyy";
var L_bobj_crv_ParamsMonthToken = "mm";
var L_bobj_crv_ParamsDayToken = "dd";
var L_bobj_crv_ParamsReadOnly = "\u3053\u306E\u30D1\u30E9\u30E1\u30FC\u30BF\u306E\u578B\u306F \"\u8AAD\u307F\u53D6\u308A\u5C02\u7528\" \u3067\u3059\u3002";
var L_bobj_crv_ParamsNoValue = "\u5024\u306A\u3057";
var L_bobj_crv_ParamsDuplicateValue = "\u91CD\u8907\u5024\u306F\u4F7F\u7528\u3067\u304D\u307E\u305B\u3093\u3002";
var L_bobj_crv_ParamsEnterOptional = "%1 \u3092\u5165\u529B (\u30AA\u30D7\u30B7\u30E7\u30F3)";
var L_bobj_crv_ParamsNoneSelected= "(\u9078\u629E\u304C\u3042\u308A\u307E\u305B\u3093\u3067\u3057\u305F)";
var L_bobj_crv_ParamsClearValues= "\u5024\u306E\u30AF\u30EA\u30A2";
var L_bobj_crv_ParamsMoreValues= "\u305D\u306E\u4ED6 %1 \u500B\u306E\u5024 ...";
var L_bobj_crv_ParamsMoreValue= "\u305D\u306E\u4ED6 %1 \u500B\u306E\u5024 ...";
var L_bobj_crv_Error = "\u30A8\u30E9\u30FC";
var L_bobj_crv_OK = "OK";
var L_bobj_crv_Cancel = "\u30AD\u30E3\u30F3\u30BB\u30EB";
var L_bobj_crv_showDetails = "\u8A73\u7D30\u306E\u8868\u793A";
var L_bobj_crv_hideDetails = "\u8A73\u7D30\u306E\u975E\u8868\u793A";
var L_bobj_crv_RequestError = "\u30EA\u30AF\u30A8\u30B9\u30C8\u3092\u51E6\u7406\u3067\u304D\u307E\u305B\u3093";
var L_bobj_crv_ServletMissing = "\u30D3\u30E5\u30FC\u30A2\u306F\u975E\u540C\u671F\u30EA\u30AF\u30A8\u30B9\u30C8\u3092\u51E6\u7406\u3059\u308B CrystalReportViewerServlet \u306B\u63A5\u7D9A\u3067\u304D\u307E\u305B\u3093\u3002\n\u30B5\u30FC\u30D6\u30EC\u30C3\u30C8\u3068\u30B5\u30FC\u30D6\u30EC\u30C3\u30C8\u30DE\u30C3\u30D4\u30F3\u30B0\u304C\u30A2\u30D7\u30EA\u30B1\u30FC\u30B7\u30E7\u30F3\u306E web.xml \u30D5\u30A1\u30A4\u30EB\u3067\u6B63\u3057\u304F\u5BA3\u8A00\u3055\u308C\u3066\u3044\u308B\u3053\u3068\u3092\u78BA\u8A8D\u3057\u3066\u304F\u3060\u3055\u3044\u3002";
var L_bobj_crv_FlashRequired = "\u3053\u306E\u30B3\u30F3\u30C6\u30F3\u30C4\u3092\u8868\u793A\u3059\u308B\u306B\u306F Adobe Flash Player 9 \u4EE5\u4E0A\u304C\u5FC5\u8981\u3067\u3059\u3002{0}\u30A4\u30F3\u30B9\u30C8\u30FC\u30EB\u3059\u308B\u5834\u5408\u306F\u3001\u3053\u3053\u3092\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u304F\u3060\u3055\u3044\u3002";
var L_bobj_crv_ReadOnlyInPanel= "\u3053\u306E\u30D1\u30E9\u30E1\u30FC\u30BF\u306F\u30D1\u30CD\u30EB\u3067\u306F\u7DE8\u96C6\u3067\u304D\u307E\u305B\u3093\u3002\u5024\u3092\u5909\u66F4\u3059\u308B\u306B\u306F\u8A73\u7D30\u30D7\u30ED\u30F3\u30D7\u30C8\u30C0\u30A4\u30A2\u30ED\u30B0\u3092\u958B\u3044\u3066\u304F\u3060\u3055\u3044\u3002";

var L_bobj_crv_Tree_Drilldown_Node = "\u30CE\u30FC\u30C9 %1 \u3092\u30C9\u30EA\u30EB\u30C0\u30A6\u30F3";

var L_bobj_crv_ReportProcessingMessage = "\u30C9\u30AD\u30E5\u30E1\u30F3\u30C8\u306E\u51E6\u7406\u4E2D\u3067\u3059\u3002\u3057\u3070\u3089\u304F\u304A\u5F85\u3061\u304F\u3060\u3055\u3044\u3002";
var L_bobj_crv_PrintControlProcessingMessage = "Crystal Reports \u30D7\u30EA\u30F3\u30C8\u30B3\u30F3\u30C8\u30ED\u30FC\u30EB \u3092\u30ED\u30FC\u30C9\u3057\u3066\u3044\u307E\u3059\u3002\u3057\u3070\u3089\u304F\u304A\u5F85\u3061\u304F\u3060\u3055\u3044\u3002";

var L_bobj_crv_SundayShort = "\u65E5";
var L_bobj_crv_MondayShort = "\u6708";
var L_bobj_crv_TuesdayShort = "\u706B";
var L_bobj_crv_WednesdayShort = "\u6C34";
var L_bobj_crv_ThursdayShort = "\u6728";
var L_bobj_crv_FridayShort = "\u91D1";
var L_bobj_crv_SaturdayShort = "\u571F";

var L_bobj_crv_Minimum = "\u6700\u5C0F";
var L_bobj_crv_Maximum = "\u6700\u5927";

var L_bobj_crv_Date = "\u65E5\u4ED8";
var L_bobj_crv_Time = "\u6642\u523B";
var L_bobj_crv_DateTime = "\u65E5\u6642";
var L_bobj_crv_Boolean = "\u8AD6\u7406\u5024";
var L_bobj_crv_Number = "\u6570\u5024";
var L_bobj_crv_Text = "\u30C6\u30AD\u30B9\u30C8";

var L_bobj_crv_InteractiveParam_NoAjax = "\u4F7F\u7528\u3057\u3066\u3044\u308B Web \u30D6\u30E9\u30A6\u30B6\u306F\u3001\u30D1\u30E9\u30E1\u30FC\u30BF\u30D1\u30CD\u30EB\u3092\u8868\u793A\u3059\u308B\u3088\u3046\u306B\u8A2D\u5B9A\u3055\u308C\u3066\u3044\u307E\u305B\u3093\u3002";
var L_bobj_crv_AdvancedDialog_NoAjax= "\u30D3\u30E5\u30FC\u30A2\u304C\u8A73\u7D30\u30D7\u30ED\u30F3\u30D7\u30C8\u30C0\u30A4\u30A2\u30ED\u30B0\u3092\u958B\u3051\u307E\u305B\u3093\u3002";

var L_bobj_crv_EnableAjax= "\u975E\u540C\u671F\u4F9D\u983C\u3092\u6709\u52B9\u306B\u3059\u308B\u306B\u306F\u3001\u7BA1\u7406\u8005\u306B\u554F\u3044\u5408\u308F\u305B\u3066\u304F\u3060\u3055\u3044\u3002";

var L_bobj_crv_LastRefreshed = "\u6700\u7D42\u6700\u65B0\u8868\u793A";

var L_bobj_crv_Collapse = "\u6298\u308A\u305F\u305F\u307F";

var L_bobj_crv_CatalystTip = "\u30AA\u30F3\u30E9\u30A4\u30F3\u30EA\u30BD\u30FC\u30B9";
// <script>
/*
=============================================================
WebIntelligence(r) Report Panel
Copyright(c) 2001-2003 Business Objects S.A.
All rights reserved

Use and support of this software is governed by the terms
and conditions of the software license agreement and support
policy of Business Objects S.A. and/or its subsidiaries. 
The Business Objects products and technology are protected
by the US patent number 5,555,403 and 6,247,008

File: labels.js


=============================================================
*/

_default="デフォルト"
_black="黒"
_brown="茶"
_oliveGreen="オリーブグリーン"
_darkGreen="濃い緑"
_darkTeal="濃い青緑"
_navyBlue="濃紺"
_indigo="インディゴ"
_darkGray="濃い灰色"
_darkRed="濃い赤"
_orange="オレンジ"
_darkYellow="濃い黄"
_green="緑"
_teal="青緑"
_blue="青"
_blueGray="灰青"
_mediumGray="灰色"
_red="赤"
_lightOrange="薄いオレンジ"
_lime="黄緑"
_seaGreen="シーグリーン"
_aqua="水色"
_lightBlue="明るい青"
_violet="紫"
_gray="灰色"
_magenta="濃いピンク"
_gold="ゴールド"
_yellow="黄"
_brightGreen="明るい緑"
_cyan="シアン"
_skyBlue="スカイブルー"
_plum="プラム"
_lightGray="薄い灰色"
_pink="ピンク"
_tan="ベージュ"
_lightYellow="薄い黄"
_lightGreen="ライトグリーン"
_lightTurquoise="ライトターコイズ"
_paleBlue="ペールブルー"
_lavender="ラベンダー"
_white="白"
_lastUsed="前回使用された色:"
_moreColors="その他の色..."

_month=new Array

_month[0]="1 月"
_month[1]="2 月"
_month[2]="3 月"
_month[3]="4 月"
_month[4]="5 月"
_month[5]="6 月"
_month[6]="7 月"
_month[7]="8 月"
_month[8]="9 月"
_month[9]="10 月"
_month[10]="11 月"
_month[11]="12 月"

_day=new Array
_day[0]="日"
_day[1]="月"
_day[2]="火"
_day[3]="水"
_day[4]="木"
_day[5]="金"
_day[6]="土"

_today="今日"

_AM="午前"
_PM="午後"

_closeDialog="ウィンドウを閉じる"

_lstMoveUpLab="上へ移動"
_lstMoveDownLab="下へ移動"
_lstMoveLeftLab="左へ移動" 
_lstMoveRightLab="右へ移動"
_lstNewNodeLab="ネストされたフィルタを追加"
_lstAndLabel="AND"
_lstOrLabel="OR"
_lstSelectedLabel="選択項目"
_lstQuickFilterLab="クイックフィルタの追加"

_openMenu="{0} オプションにアクセスするにはここをクリック"
_openCalendarLab="カレンダを開く"

_scroll_first_tab="最初のタブにスクロール"
_scroll_previous_tab="前のタブにスクロール"
_scroll_next_tab="次のタブにスクロール"
_scroll_last_tab="最後のタブにスクロール"

_expandedLab="展開"
_collapsedLab="折りたたみ"
_selectedLab="選択項目"

_expandNode="ノード %1 を展開"
_collapseNode="ノード %1 を折りたたむ"

_checkedPromptLab="設定"
_nocheckedPromptLab="設定しない"
_selectionPromptLab="と同じ値"
_noselectionPromptLab="値なし"

_lovTextFieldLab="ここに値を入力"
_lovCalendarLab="ここに日付を入力"
_lovPrevChunkLab="前のチャンクへ"
_lovNextChunkLab="次のチャンクへ"
_lovComboChunkLab="チャンク"
_lovRefreshLab="最新表示"
_lovSearchFieldLab="検索するテキストをここに入力"
_lovSearchLab="検索"
_lovNormalLab="普通"
_lovMatchCase="大文字と小文字を区別する"
_lovRefreshValuesLab="値の最新表示"

_calendarNextMonthLab="次の月へ"
_calendarPrevMonthLab="前の月へ"
_calendarNextYearLab="次の年へ"
_calendarPrevYearLab="前の年へ"
_calendarSelectionLab="選択された日"

_menuCheckLab="チェック済み"
_menuDisableLab="無効"
	
_level="レベル"
_closeTab="タブを閉じる"
_of=" of "

_RGBTxtBegin= "RGB("
_RGBTxtEnd= ")"

_helpLab="ヘルプ"

_waitTitleLab="お待ちください"
_cancelButtonLab="キャンセル"

_modifiers= new Array
_modifiers[0]="Ctrl+"
_modifiers[1]="Shift+"
_modifiers[2]="Alt+"

_bordersMoreColorsLabel="その他の罫線..."
_bordersTooltip=new Array
_bordersTooltip[0]="境界線なし"
_bordersTooltip[1]="左の境界線"
_bordersTooltip[2]="右の境界線"
_bordersTooltip[3]="下の境界線"
_bordersTooltip[4]="中太の下罫線"
_bordersTooltip[5]="太い下罫線"
_bordersTooltip[6]="上下の罫線"
_bordersTooltip[7]="上罫線および中太の下罫線"
_bordersTooltip[8]="上罫線および太い下罫線"
_bordersTooltip[9]="すべての罫線"
_bordersTooltip[10]="すべての中太の罫線"
_bordersTooltip[11]="すべての太い罫線"/* Copyright (c) Business Objects 2006. All rights reserved. */

// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "\u4ECA\u65E5";
var L_January   = "1 \u6708";
var L_February  = "2 \u6708";
var L_March     = "3 \u6708";
var L_April     = "4 \u6708";
var L_May       = "5 \u6708";
var L_June      = "6 \u6708";
var L_July      = "7 \u6708";
var L_August    = "8 \u6708";
var L_September = "9 \u6708";
var L_October   = "10 \u6708";
var L_November  = "11 \u6708";
var L_December  = "12 \u6708";
var L_Su        = "\u65E5";
var L_Mo        = "\u6708";
var L_Tu        = "\u706B";
var L_We        = "\u6C34";
var L_Th        = "\u6728";
var L_Fr        = "\u91D1";
var L_Sa        = "\u571F";

// Strings for prompts.js and prompts_param.js
var L_YYYY          = "yyyy";
var L_MM            = "mm";
var L_DD            = "dd";
var L_BadNumber     = "\u3053\u306E\u30D1\u30E9\u30E1\u30FC\u30BF\u306E\u578B\u306F \"\u6570\u5024\" \u3067\u3001\u8CA0\u53F7\u3001\u6570\u5B57 (\"0-9\")\u3001\u304A\u3088\u3073\u5C0F\u6570\u70B9\u3092\u793A\u3059\u30D4\u30EA\u30AA\u30C9\u307E\u305F\u306F\u30AB\u30F3\u30DE\u306E\u307F\u3092\u542B\u3080\u3053\u3068\u304C\u3067\u304D\u307E\u3059\u3002\u5165\u529B\u3055\u308C\u305F\u30D1\u30E9\u30E1\u30FC\u30BF\u5024\u3092\u4FEE\u6B63\u3057\u3066\u304F\u3060\u3055\u3044\u3002";
var L_BadCurrency   = "\u3053\u306E\u30D1\u30E9\u30E1\u30FC\u30BF\u306E\u578B\u306F \"\u901A\u8CA8\" \u3067\u3001\u8CA0\u53F7\u3001\u6570\u5B57 (\"0-9\")\u3001\u304A\u3088\u3073\u5C0F\u6570\u70B9\u3092\u793A\u3059\u30D4\u30EA\u30AA\u30C9\u307E\u305F\u306F\u30AB\u30F3\u30DE\u306E\u307F\u3092\u542B\u3080\u3053\u3068\u304C\u3067\u304D\u307E\u3059\u3002\u5165\u529B\u3055\u308C\u305F\u30D1\u30E9\u30E1\u30FC\u30BF\u5024\u3092\u4FEE\u6B63\u3057\u3066\u304F\u3060\u3055\u3044\u3002";
var L_BadDate       = "\u3053\u306E\u30D1\u30E9\u30E1\u30FC\u30BF\u306E\u578B\u306F \"\u65E5\u4ED8\" \u3067\u3001\u305D\u306E\u5F62\u5F0F\u306F \"%1\" \u3067\u3059\u3002\"yyyy\" \u306F\u5E74\u3092\u8868\u3059 4 \u6841\u306E\u6570\u5B57\u3001\"mm\" \u306F\u6708 (\u4F8B: 1 \u6708 = 1)\u3001\"dd\" \u306F\u305D\u306E\u6708\u306E\u65E5\u3092\u8868\u3057\u307E\u3059\u3002";
var L_BadDateTime   = "\u3053\u306E\u30D1\u30E9\u30E1\u30FC\u30BF\u306E\u578B\u306F \"\u65E5\u6642\" \u3067\u3001\u305D\u306E\u5F62\u5F0F\u306F \"%1 hh:mm:ss\" \u3067\u3059\u3002\"yyyy\" \u306F\u5E74\u3092\u8868\u3059 4 \u6841\u306E\u6570\u5B57\u3001 \"mm\" \u306F\u6708 (\u4F8B: 1 \u6708 = 1)\u3001\"dd\" \u306F\u65E5\u3001\"hh\" \u306F 24 \u6642\u9593\u8868\u793A\u306B\u3088\u308B\u6642\u9593\u3001\"mm\" \u306F\u5206\u3001\"ss\" \u306F\u79D2\u3092\u8868\u3057\u307E\u3059\u3002";
var L_BadTime       = "\u3053\u306E\u30D1\u30E9\u30E1\u30FC\u30BF\u306E\u578B\u306F \"\u6642\u523B\" \u3067\u3001\u305D\u306E\u5F62\u5F0F\u306F \"hh:mm:ss\" \u3067\u3059\u3002\"hh\" \u306F 24 \u6642\u9593\u8868\u793A\u306B\u3088\u308B\u6642\u9593\u3001\"mm\" \u306F\u5206\u3001\"ss\" \u306F\u79D2\u3092\u8868\u3057\u307E\u3059\u3002";
var L_NoValue       = "\u5024\u306A\u3057";
var L_BadValue      = "\"\u5024\u306A\u3057\" \u3068\u8A2D\u5B9A\u3059\u308B\u305F\u3081\u306B\u306F\u3001\u958B\u59CB\u5024\u3068\u7D42\u4E86\u5024\u306E\u4E21\u65B9\u3092 \"\u5024\u306A\u3057\" \u306B\u8A2D\u5B9A\u3059\u308B\u5FC5\u8981\u304C\u3042\u308A\u307E\u3059\u3002";
var L_BadBound      = "\"\u7BC4\u56F2\u306E\u958B\u59CB\u3092\u6307\u5B9A\u3057\u306A\u3044\" \u3068 \"\u7BC4\u56F2\u306E\u7D42\u4E86\u3092\u6307\u5B9A\u3057\u306A\u3044\" \u3092\u540C\u6642\u306B\u8A2D\u5B9A\u3059\u308B\u3053\u3068\u306F\u3067\u304D\u307E\u305B\u3093\u3002";
var L_NoValueAlready = "\u3053\u306E\u30D1\u30E9\u30E1\u30FC\u30BF\u306F\u3059\u3067\u306B \"\u5024\u306A\u3057\" \u306B\u8A2D\u5B9A\u3055\u308C\u3066\u3044\u307E\u3059\u3002\u4ED6\u306E\u5024\u3092\u8A2D\u5B9A\u3059\u308B\u524D\u306B \"\u5024\u306A\u3057\" \u3092\u524A\u9664\u3057\u307E\u3059\u3002";
var L_RangeError    = "\u7BC4\u56F2\u306E\u958B\u59CB\u5024\u306F\u7D42\u4E86\u5024\u3088\u308A\u5C0F\u3055\u306A\u5024\u306B\u3059\u308B\u5FC5\u8981\u304C\u3042\u308A\u307E\u3059\u3002";
var L_NoDateEntered = "\u65E5\u4ED8\u3092\u5165\u529B\u3059\u308B\u5FC5\u8981\u304C\u3042\u308A\u307E\u3059\u3002";
var L_Empty         = "\u5024\u3092\u5165\u529B\u3057\u3066\u304F\u3060\u3055\u3044\u3002";

// Strings for filter dialog
var L_closeDialog="\u30A6\u30A3\u30F3\u30C9\u30A6\u3092\u9589\u3058\u308B";

var L_SetFilter = "\u30D5\u30A3\u30EB\u30BF\u306E\u8A2D\u5B9A";
var L_OK        = "OK";
var L_Cancel    = "\u30AD\u30E3\u30F3\u30BB\u30EB";

 /* Crystal Decisions Confidential Proprietary Information */
