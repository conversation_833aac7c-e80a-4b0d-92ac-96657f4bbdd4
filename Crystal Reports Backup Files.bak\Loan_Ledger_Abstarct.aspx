﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Loan_Ledger_Abstarct.aspx.cs" Inherits="KSDCSCST_Portal.Loan_Ledger_Abstarct" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">



    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- daterange picker -->
    <link rel="stylesheet" href="assets/plugins/daterangepicker/daterangepicker.css">
    <!-- iCheck for checkboxes and radio inputs -->
    <link rel="stylesheet" href="assets/plugins/icheck-bootstrap/icheck-bootstrap.min.css">
    <!-- Bootstrap Color Picker -->
    <link rel="stylesheet" href="assets/plugins/bootstrap-colorpicker/css/bootstrap-colorpicker.min.css">
    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet" href="assets/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css">
    <!-- Select2 -->
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">
    <!-- Bootstrap4 Duallistbox -->
    <link rel="stylesheet" href="assets/plugins/bootstrap4-duallistbox/bootstrap-duallistbox.min.css">
    <!-- BS Stepper -->
    <link rel="stylesheet" href="assets/plugins/bs-stepper/css/bs-stepper.min.css">
    <!-- dropzonejs -->
    <link rel="stylesheet" href="assets/plugins/dropzone/min/dropzone.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">
    <style>
        table {
            border-collapse: collapse;
            width: 100%;
        }

        input:disabled {
            border: 0;
        }
    </style>


    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Agreement Checklist</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">Agreement Checklist</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-10 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">
                                <div class="active tab-pane" id="Print_Area">
                                    <div class="row">
                                        <div class="col-sm-12">
                                        </div>

                                    </div>
                                    <div class="Sub_Header" style="text-align: center;">LOAN LEDGER</div>






                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="row" style="margin: 1%;">
                                                <div class="col-sm-12">
                                                    <div class="Sub_Header_Abstract">LOAN DETAILS</div>


                                                    <table class="table_Abstract" style="width: 100%;">
                                                        <tr>
                                                            <td>Loanee Name</td>

                                                            <td style="">
                                                                <label id="txtLoanee_Name">Sri.RAJESH</label>
                                                            </td>



                                                            <td>Address</td>

                                                            <td>
                                                                <label id="txtAddress">S/o Chathankutty, Kariyarath(H)</label>
                                                            </td>


                                                            <td>Scheme Name</td>

                                                            <td>
                                                                <label id="txtScheme_Name">House Rennovation</label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>Old Agr.No</td>

                                                            <td>
                                                                <label id="txtOld_Agr_No">HR/7/MLP/18</label>
                                                            </td>

                                                            <td>Loan A/C No.</td>

                                                            <td>
                                                                <label id="txtLoan_Acc_No">7</label>
                                                            </td>

                                                            <td>Due Date</td>

                                                            <td>
                                                                <label id="txtDue_Date">01-03-2019</label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>EMI</td>

                                                            <td>
                                                                <label id="txtEMI">8300</label>
                                                            </td>

                                                            <td>Loan Duration</td>

                                                            <td colspan="3">
                                                                <label id="txtphone">72 Monthly</label>
                                                            </td>
                                                        </tr>

                                                    </table>


                                                </div>
                                            </div>
                                            <div class="row" style="margin: 1%;">


                                                <div class="col-sm-6">

                                                    <div class="Sub_Header_Abstract">ABSTRACT</div>


                                                    <table class="table_Abstract" style="width: 100%;">
                                                        <tr>
                                                            <td>Principal</td>

                                                            <td>
                                                                <label id="txtTotal_Principal">500000</label>
                                                            </td>

                                                        </tr>
                                                        <tr>
                                                            <td>Interest</td>

                                                            <td>
                                                                <label id="txtTotal_Interest">138281.04</label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>Penal</td>

                                                            <td>
                                                                <label id="txtTotal_Penal">9652.91</label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>Remittance</td>

                                                            <td>
                                                                <label id="txtTotal_Remittance">304000</label>
                                                            </td>
                                                        </tr>
                                                        <tr style="position: relative; height: 35px;">
                                                            <td></td>

                                                            <td></td>

                                                        </tr>
                                                    </table>

                                                    <div class="Sub_Header_Abstract">CHARGES/WAIVER/WRITE OFF</div>

                                                    <table class="table_Abstract" style="width: 100%;">
                                                        <tr>
                                                            <td>Principal Adj.</td>

                                                            <td>
                                                                <label id="txtPrincipal_Adj">-15000</label>
                                                            </td>

                                                        </tr>
                                                        <tr>
                                                            <td>Interest Adj.</td>

                                                            <td>
                                                                <label id="txtInterest_Adj">0.00</label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>Penal Adj.</td>

                                                            <td>
                                                                <label id="txtPenal_Adj">0.00</label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>C/B Adj.</td>

                                                            <td>
                                                                <label id="txtC/B_Adj">0.00</label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>Action Taken</td>

                                                            <td>
                                                                <label id="txtAction_Taken">No Action</label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>Referance Date</td>

                                                            <td>
                                                                <label id="txtReferance_Date"></label>
                                                            </td>
                                                        </tr>

                                                    </table>


                                                </div>


                                                <div class="col-sm-6">
                                                    <div class="Sub_Header_Abstract">RECOVERED AMOUNT DETAILS</div>


                                                    <table class="table_Abstract" style="width: 100%;">
                                                        <tr>
                                                            <td>Principal</td>

                                                            <td>
                                                                <label id="txtRecovered_Principal">178398.39</label>
                                                            </td>

                                                        </tr>
                                                        <tr>
                                                            <td>Interest</td>

                                                            <td>
                                                                <label id="txtRecovered_Interest">118700.54</label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>Penal</td>

                                                            <td>
                                                                <label id="txtRecovered_Penal">6901.07</label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>C/B Adj.</td>

                                                            <td>
                                                                <label id="txtRecovered_C/B_Adj">0.00</label>
                                                            </td>
                                                        </tr>

                                                        <tr>
                                                            <td>Total</td>

                                                            <td>
                                                                <label id="txtTotal_Recovered_Amount">304000</label>
                                                            </td>
                                                        </tr>


                                                    </table>
                                                    <div class="Sub_Header_Abstract">AMOUNT OUTSTANDING</div>

                                                    <table class="table_Abstract" style="width: 100%;">
                                                        <tr>
                                                            <td>Principal</td>

                                                            <td>
                                                                <label id="txtPrincipal_Outstanding">306601.61</label>
                                                            </td>

                                                        </tr>
                                                        <tr>
                                                            <td>Interest</td>

                                                            <td>
                                                                <label id="txtInterest_Outstanding">19580.50</label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>Penal</td>

                                                            <td>
                                                                <label id="txtPenal_Outstanding">2751.84</label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>C/B Adj.</td>

                                                            <td>
                                                                <label id="txtC/B_Adj_Outstanding">0.00</label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>Repayment</td>

                                                            <td>
                                                                <label id="txtrepayment">0.00</label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>Total</td>

                                                            <td>
                                                                <label id="txtTotal_Outatanding">328933.95</label>
                                                            </td>
                                                        </tr>

                                                    </table>


                                                </div>




                                            </div>
                                        </div>
                                    </div>






































                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">


                                            <a href="Disbursement_Agreement_CheckList_List.aspx" class="btn btn-dark">Back</a>

                                            <a onclick="Print();" class="btn btn-success">Print</a>

                                        </div>
                                    </div>
                                </div>



                                <!-- /.tab-pane -->
                            </div>

                        </div>
                    </div>

                </div>
                <!-- /.card-body -->
            </div>
            <!-- /.card -->
        </div>
        <!-- /.col -->

        <!-- /.row -->

        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->


    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 -->
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <!-- Bootstrap4 Duallistbox -->
    <script src="assets/plugins/bootstrap4-duallistbox/jquery.bootstrap-duallistbox.min.js"></script>
    <!-- InputMask -->
    <script src="assets/plugins/moment/moment.min.js"></script>
    <script src="assets/plugins/inputmask/jquery.inputmask.min.js"></script>
    <!-- date-range-picker -->
    <script src="assets/plugins/daterangepicker/daterangepicker.js"></script>
    <!-- bootstrap color picker -->
    <script src="assets/plugins/bootstrap-colorpicker/js/bootstrap-colorpicker.min.js"></script>
    <!-- Tempusdominus Bootstrap 4 -->
    <script src="assets/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js"></script>
    <!-- BS-Stepper -->
    <script src="assets/plugins/bs-stepper/js/bs-stepper.min.js"></script>
    <!-- dropzonejs -->
    <script src="assets/plugins/dropzone/min/dropzone.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script src="assets/js/jquery.dateandtime.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>


    <script>

        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;
        const $searchInput = $('#txtPincode');

        const $searchResults = $('#search-results');
        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }
        function getFormattedDate(date) {
            date = date.split('/')[2] + "-" + date.split('/')[1] + "-" + date.split('/')[0];
            return date;
        }
        function Print() {
            var printWindow = window.open('', '_blank');
            printWindow.document.open();
            printWindow.document.write('<html><head><title>Print</title></head><body style="width: 210mm;height: 297mm;margin: 0;  ">');
            printWindow.document.write($("#Print_Area").html());
            printWindow.document.write('</body></html>');
            printWindow.document.close();
            printWindow.print();
            if (confirm('Print dialog initiated. Please close the window after printing.')) {
                printWindow.close();
            }
        }

        $(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }


            $("#DIV_Land_Surety").hide();
            $("#DIV_Employee_Surety").hide();
            $("#DIV_FDLIC_Surety").hide();
            $("#DIV_Personal_Surety").hide();

        })

        $(document).ready(function () {
            // Load_All_Application_Issue_By_Id(getParameterByName("Id"));
        });

        var Caste_Id;
        var SubCaste_Id;
        var Village_Id;
        var SubDistrict_Id;
        var Scheme_Id;
        var RegNo;
        var LoanNo;
        var LoanAppId;
        var Locality;
        var LocalityId;

        function Load_All_Application_Issue_By_Id(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Id: Id }), // If you have parameters
                url: "WebService.asmx/Select_tbl_LoanApp_By_LoanAppId",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        LoanAppId = value.int_loanappid;
                        RegNo = value.vchr_appreceivregno;
                        LoanNo = value.int_loanno;
                        $("#txtReg_No").text(value.vchr_appreceivregno);
                        $("#txtDate_Of_Agreement").text(getFormattedDate(value.dte_agrement_date));
                        $("#txtName").text(value.vchr_applname);
                        $("#txtFather_Name").text(value.vchr_fathername);
                        $("#txtouse_Name_Or_No").text(value.vchr_hsename);
                        $("#txtLane_1").text(value.vchr_place1);
                        $("#txtLane_2").text(value.vchr_place2);
                        $("#txtPost_Office").text(value.vchr_post);
                        $("#txtPin_Code").text(value.int_pincode);
                        $("#txtphone").text(value.vchr_phno);
                        $("#txtRation_Card_Number").text(value.vchr_ration);
                        $("#txtAadhar_No").text(value.vchr_Aadhaar);
                        Caste_Id = value.vchr_caste;
                        SubCaste_Id = value.SubCast;
                        $("#txtSex").text(value.vchr_sex);
                        $("#txtDate_Of_Birth").text(getFormattedDate(value.dte_dob));
                        $("#txtAge").text(value.Age);
                        $("#txtAnnual_Income").text(value.int_anninc);
                        Village_Id = value.vchr_village;
                        SubDistrict_Id = value.vchr_subdistrict;
                        Scheme_Id = value.int_schemeid;
                        $("#txtFund").text(value.vchr_fund);

                        $("#txtRequested_Loan_Amount").text(value.int_loanamt_req);

                        $("#txtSanctioned_Loan_Amount").text(value.int_amtsanction);

                        $("#txtLocality").text(value.vchr_locality);
                        $("#lbl_txtLocality").text(value.vchr_locality);
                        Locality = value.vchr_locality;
                        LocalityId = value.vchr_panchayat;

                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Caste_By_Id(Caste_Id, SubCaste_Id);
                Load_All_Village_By_Id(Village_Id);
                Load_All_Sub_District_By_Id(SubDistrict_Id);
                Load_All_Schemes_By_Id(Scheme_Id);
                Load_All_tbl_loanreg_By_RegNo_Or_LoanNo(RegNo, LoanNo);
                Load_All_Bank_Details_By_int_loanappid(LoanAppId);
                Load_All_Surety_Type_By_int_loanappid(LoanAppId);
                if (Locality == 'Panchayat') { Load_All_Panchayath_By_Id(LocalityId); }
            });
        }
        var Sub_District_Count;
        function Load_All_Caste_By_Id(Caste_Id, SubCaste_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Cast_By_Id",
                data: JSON.stringify({ id: Caste_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Caste = value.Cast;
                        $("#txtCaste").text(Caste);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_SubCast_By_Id(SubCaste_Id);
            });

        }

        function Load_All_Panchayath_By_Id(LocalityId) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Panchayath_By_Id",
                data: JSON.stringify({ Id: LocalityId }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Panchayath = value.Panjayath;
                        $("#txtLocality_Name").text(Panchayath);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });

        }

        function Load_All_SubCast_By_Id(SubCaste_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Cast_By_Id",
                data: JSON.stringify({ id: SubCaste_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var SubCaste = value.SubCast;
                        $("#txtReligion").text(SubCaste);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });

        }
        function Load_All_Village_By_Id(Village_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Village_By_Id",
                data: JSON.stringify({ id: Village_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Village = value.Village;
                        $("#txtVillage").text(Village);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_tbl_Taluk_By_Village_Id(Village_Id)
            });
        }

        function Load_All_tbl_Taluk_By_Village_Id(Village_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_Taluk_By_Id",
                data: JSON.stringify({ Village_Id: Village_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtTaluk').val('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Taluk = value.Taluk;
                        $('#txtTaluk').text(Taluk);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Districts_By_Village_Id(Village_Id);
            });
        }
        var LocDistrictId;
        function Load_All_Districts_By_Village_Id(Village_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts_By_Village_Id",
                data: JSON.stringify({ Village_Id: Village_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtDistrict').val('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        LocDistrictId = Id;
                        var District_Name = value.District_Name;
                        $('#txtDistrict').text(District_Name);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                if (Locality == 'Municipality') { Load_All_Municipality_By_District_Id(LocDistrictId, LocalityId); }
                if (Locality == 'Corporation') { Load_All_Corporation_By_District_Id(LocDistrictId, LocalityId); }
            });
        }
        function Load_All_Corporation_By_District_Id(LocDistrictId, LocalityId) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Corporation_By_District_Id",
                data: JSON.stringify({ District_Id: LocDistrictId }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtSub_District').val('');
                    $.each(data.d, function (key, value) {
                        if (value.Id == LocalityId) {
                            var Id = value.Id;
                            var Corporation = value.Corporation;
                            $('#txtLocality_Name').text(Corporation);
                        }
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                //alert(Present_District);
            });
        }
        function Load_All_Municipality_By_District_Id(LocDistrictId, LocalityId) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Municipality_By_District_Id",
                data: JSON.stringify({ District_Id: LocDistrictId }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtSub_District').val('');
                    $.each(data.d, function (key, value) {
                        if (value.Id == LocalityId) {
                            var Id = value.Id;
                            var Municipality = value.Municipality;
                            $('#txtLocality_Name').text(Municipality);
                        }
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                //alert(Present_District);
            });
        }
        function Load_All_Sub_District_By_Id(SubDistrict_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Districts_By_Id",
                data: JSON.stringify({ id: SubDistrict_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtSub_District').val('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var SubDistrict = value.Sub_District_Name;
                        $('#txtSub_District').text(SubDistrict);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                //alert(Present_District);
            });
        }
        function Load_All_Schemes_By_Id(Scheme_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Schemes_By_ID",
                data: JSON.stringify({ Id: Scheme_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtScheme').val('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Scheme = value.Scheme;
                        $('#txtScheme').text(Scheme);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                //alert(Present_District);
            });

        }
        function Load_All_tbl_loanreg_By_RegNo_Or_LoanNo(RegNo, LoanNo) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_tbl_loanreg_By_RegNo_Or_LoanNo",
                data: JSON.stringify({ appreceivr_RegNo: RegNo, loanNo: LoanNo }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtloan_Amount').val('');
                    $('#txtInstalment').val('');
                    $('#txtEMI').val('');
                    $('#txtperiod').val('');
                    $('#txtRate_Of_Interest').val('');
                    $('#txtPenal_Interest').val('');
                    $.each(data.d, function (key, value) {
                        $('#txtloan_Amount').text(value.mny_Loanamt);
                        $('#txtInstalment').text(value.int_disb_inst);
                        $('#txtEMI').text(value.mny_repayamt);
                        $('#txtperiod').text(value.int_repay_inst);
                        $('#txtRate_Of_Interest').text(value.int_rate_int);
                        $('#txtPenal_Interest').text(value.int_rate_penal);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                //alert(Present_District);
            });
        }
        function Load_All_Bank_Details_By_int_loanappid(LoanAppId) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Bank_Details_By_int_loanappid",
                data: JSON.stringify({ int_loanappid: LoanAppId }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtAccount_Number').val('');
                    $('#txtIFSC').val('');
                    $('#txtBank_Name').val('');
                    $('#txtBranch').val('');
                    $.each(data.d, function (key, value) {
                        $('#txtAccount_Number').text(value.int_BankAccNo);
                        $('#txtIFSC').text(value.vchr_IFSC);
                        $('#txtBank_Name').text(value.vchr_Bank);
                        $('#txtBranch').text(value.vchr_Branch);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                //alert(Present_District);
            });
        }
        var Land_Surety = "";
        var Employee_Surety = "";
        var FDLIC_Surety = "";
        var Personal_Surety = "";

        function Load_All_Surety_Type_By_int_loanappid(LoanAppId) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Surety_Type_By_int_loanappid",
                data: JSON.stringify({ int_loanappid: LoanAppId }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $.each(data.d, function (key, value) {
                        if (value.SuretyType == "Land") { Land_Surety = "Land"; }
                        if (value.SuretyType == "FD_LIC") { FDLIC_Surety = "FD_LIC"; }
                        if (value.SuretyType == "Personal") { Personal_Surety = "Personal"; }
                        if (value.SuretyType == "Employee") { Employee_Surety = "Employee"; }
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                    })
                }
            }).done(function () {
                if (Land_Surety == "Land") { Load_All_tbl_LandSurety_By_LoanAppId(LoanAppId); }
                if (Employee_Surety == "Employee") { Load_All_tbl_EmployeeSurety_By_LoanAppId(LoanAppId); }
                if (FDLIC_Surety == "FD_LIC") { Load_All_tbl_FDLICSurety_By_LoanAppId(LoanAppId); }
                if (Personal_Surety == "Personal") { Load_All_tbl_PersonalSurety_By_LoanAppId(LoanAppId); }
            });
        }

        function Load_All_tbl_PersonalSurety_By_LoanAppId(LoanAppId) {
            var i = 0;
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_EmpSurety_By_LoanAppId",
                data: JSON.stringify({ int_loanappid: LoanAppId }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $("#tbl_Personal_Surety_List").empty();
                    $.each(data.d, function (key, value) {
                        if (value.int_personal == 1) {
                            i++;
                            $("#tbl_Personal_Surety_List").append('<tr>'
                                + '	<td style="text-align: center;">' + i + '</td>'
                                + '	<td style="text-align: center;">' + value.vchr_empname + '</td>'
                                + '	<td style="text-align: center;">' + value.vchr_fathername + '</td>'
                                + '	<td style="text-align: center;">' + value.vchr_PanNo + '</td>'
                                + '	<td style="text-align: center;">' + value.vchr_Aadhar + '</td>'
                                + '	<td style="text-align: center;">' + value.vchr_presenthsename + ', ' + value.vchr_presentlane1 + ', ' + value.vchr_presentlane2 + ', ' + value.vchr_presentpost + ' - ' + value.vchr_presentpin + '</td>'
                                + '</tr>');
                        }
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                    })
                }
            }).done(function () {
                $("#DIV_Personal_Surety").show();
            });
        }

        function Load_All_tbl_FDLICSurety_By_LoanAppId(LoanAppId) {
            var i = 0;
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_FDLICSurety_By_LoanAppId",
                data: JSON.stringify({ int_loanappid: LoanAppId }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $("#tbl_FDLIC_Surety_List").empty();
                    $.each(data.d, function (key, value) {
                        i++;
                        $("#tbl_FDLIC_Surety_List").append('<tr>'
                            + '	<td style="text-align: center;">' + i + '</td>'
                            + '	<td style="text-align: center;">' + value.vchr_holdername + '</td>'
                            + '	<td style="text-align: center;">' + value.vchr_fdpoldetails + '</td>'
                            + '	<td style="text-align: center;">' + value.int_MatureAmt + '</td>'
                            + ' <td style="text-align: center;">' + getFormattedDate(value.dt_mature_date) + '</td>'
                            + '</tr>');
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                    })
                }
            }).done(function () {
                $("#DIV_FDLIC_Surety").show();
            });
        }

        function Load_All_tbl_EmployeeSurety_By_LoanAppId(LoanAppId) {
            var i = 0;
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_EmpSurety_By_LoanAppId",
                data: JSON.stringify({ int_loanappid: LoanAppId }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $("#tbl_Employee_Surety_List").empty();
                    $.each(data.d, function (key, value) {

                        if (value.int_personal == 0) {
                            i++;
                            $("#tbl_Employee_Surety_List").append('<tr>'
                                + '	<td style="text-align: center;">' + i + '</td>'
                                + '	<td style="text-align: center;">' + value.vchr_empname + '</td>'
                                + '	<td style="text-align: center;">' + value.vchr_empdesig + '</td>'
                                + '	<td style="text-align: center;">' + value.vchr_empoffname + '</td>'
                                + '	<td style="text-align: center;">' + value.vchr_presenthsename + ', ' + value.vchr_presentlane1 + ', ' + value.vchr_presentlane2 + ', ' + value.vchr_presentpost + ' - ' + value.vchr_presentpin + '</td>'
                                + '	<td style="text-align: center;">' + value.int_netsal + '</td>'
                                + ' <td style="text-align: center;">' + getFormattedDate(value.dte_empdateofretire) + '</td>'
                                + '</tr>');
                        }
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                    })
                }
            }).done(function () {
                $("#DIV_Employee_Surety").show();
            });
        }

        function Load_All_tbl_LandSurety_By_LoanAppId(LoanAppId) {
            var i = 0;
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_LandSurety_By_LoanAppId",
                data: JSON.stringify({ int_loanappid: LoanAppId }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $("#tbl_Land_Surety_List").empty();
                    $.each(data.d, function (key, value) {
                        i++;
                        $("#tbl_Land_Surety_List").append('<tr>'
                            + '	<td style="text-align: center;">' + i + '</td>'
                            + '	<td style="text-align: center;">' + value.vchr_ownername + '</td>'
                            + '	<td style="text-align: center;">' + value.vchr_surveyno + '</td>'
                            + '	<td style="text-align: center;">' + value.vchr_extent + '</td>'
                            + '	<td style="text-align: center;">' + value.vchr_hsename + ', ' + value.vchr_lane1 + ', ' + value.vchr_lane2 + ', ' + value.vchr_post + ' - ' + value.int_pin + '</td>'
                            + '	<td style="text-align: center;">' + value.vchr_land_village + '</td>'
                            + ' <td style="text-align: center;">' + value.vchr_land_taluk + '</td>'
                            + '	<td style="text-align: center;">' + value.vchr_land_district + '</td>'
                            + ' <td style="text-align: center;">' + value.int_ValAmt + '</td>'
                            + '</tr>');
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                    })
                }
            }).done(function () {
                $("#DIV_Land_Surety").show();
            });
        }

        function Load_All_Block(district_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Block_By_District_Id",
                data: JSON.stringify({ district_Id: district_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropBlock').empty();
                    $('#dropBlock').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Block = value.Block;
                        var html = '<option value="' + Id + '">' + Block + '</option>';

                        $('#dropBlock').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Municipality(district_Id);
                $('#dropBlock').change(function () {

                    Load_All_Panjayath($(this).val());
                });
            });

        }


        function Load_All_Lok_Sabha() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Lok_Sabha_By_District_Id",
                // data: JSON.stringify({ block_Id: block_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropLokSabha').empty();
                    $('#dropLokSabha').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Lok_Sabha = value.Lok_Sabha;
                        var html = '<option value="' + Id + '">' + Lok_Sabha + '</option>';

                        $('#dropLokSabha').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });

        }



        function Load_All_Constituency(Lok_Sabha_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Constituency_By_Lok_Sabha_Id",
                data: JSON.stringify({ lok_Sabha_Id: Lok_Sabha_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropConstituency').empty();
                    $('#dropConstituency').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Constituency = value.Constituency;
                        var html = '<option value="' + Id + '">' + Constituency + '</option>';

                        $('#dropConstituency').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });

        }


        function Load_All_Panjayath(block_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Panjayath_By_Block_Id",
                data: JSON.stringify({ block_Id: block_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropPanchayat').empty();
                    $('#dropPanchayat').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Panjayath = value.Panjayath;
                        var html = '<option value="' + Id + '">' + Panjayath + '</option>';

                        $('#dropPanchayat').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });

        }

        function Load_All_Municipality(District_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Municipality_By_District_Id",
                data: JSON.stringify({ District_Id: District_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropMunicipality').empty();
                    $('#dropMunicipality').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Municipality = value.Municipality;
                        var html = '<option value="' + Id + '">' + Municipality + '</option>';

                        $('#dropMunicipality').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Corporation(District_Id);
            });

        }


        function Load_All_Corporation(District_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Corporation_By_District_Id",
                data: JSON.stringify({ District_Id: District_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropCorporation').empty();
                    $('#dropCorporation').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Corporation = value.Corporation;
                        var html = '<option value="' + Id + '">' + Corporation + '</option>';

                        $('#dropCorporation').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });

        }


        function Load_All_SubCast(Cast_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Cast",
                data: JSON.stringify({ Cast_Id: Cast_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubCast').empty();
                    $('#dropSubCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var SubCast = value.SubCast;
                        var html = '<option value="' + Id + '">' + SubCast + '</option>';

                        $('#dropSubCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {


            });

        }

        function Next() {
            var Scheme_Id = $("#dropScheme").val();
            if (Scheme_Id == "2" || Scheme_Id == "3" || Scheme_Id == "6" || Scheme_Id == "7" || Scheme_Id == "8" || Scheme_Id == "9" || Scheme_Id == "10" || Scheme_Id == "13" || Scheme_Id == "17" || Scheme_Id == "33" || Scheme_Id == "34" || Scheme_Id == "35" || Scheme_Id == "36" || Scheme_Id == "37" || Scheme_Id == "38" || Scheme_Id == "39" || Scheme_Id == "40" || Scheme_Id == "41" || Scheme_Id == "42" || Scheme_Id == "44" || Scheme_Id == "45" || Scheme_Id == "46") {
                location.href = 'DE_Self_Employement_Loan_View.aspx?Id=' + getParameterByName("Id");
            }
            else if (Scheme_Id == "2" || Scheme_Id == "3") {
                location.href = 'DE_Foreign_Loan.aspx?Id=' + getParameterByName("Id");
            }

            else if (Scheme_Id == "14" || Scheme_Id == "15" || Scheme_Id == "16") {
                location.href = 'DE_Education_Loan.aspx?Id=' + getParameterByName("Id");
            }
            else if (Scheme_Id == "18") {
                location.href = 'DE_Working_Capital_Or_Business_Development_Loan.aspx?Id=' + getParameterByName("Id");
            }
            else if (Scheme_Id == "19") {
                location.href = 'DE_Marriage_Loan.aspx.aspx?Id=' + getParameterByName("Id");
            }
            else if (Scheme_Id == "20" || Scheme_Id == "21" || Scheme_Id == "22" || Scheme_Id == "23") {
                location.href = 'DE_Housing_Loan.aspx.aspx?Id=' + getParameterByName("Id");
            }
            else if (Scheme_Id == "25" || Scheme_Id == "26") {
                location.href = 'DE_Housing_Maintenance_Loan.aspx?Id=' + getParameterByName("Id");
            }
            else if (Scheme_Id == "27" || Scheme_Id == "28" || Scheme_Id == "31") {
                location.href = 'DE_Vehicle_Or_Home_Appliance_Loan.aspx?Id=' + getParameterByName("Id");
            }
            else if (Scheme_Id == "29" || Scheme_Id == "30") {
                location.href = 'DE_Personal_Loan.aspx?Id=' + getParameterByName("Id");
            }
            else if (Scheme_Id == "32") {
                location.href = 'DE_Agriculture_Loan.aspx?Id=' + getParameterByName("Id");
            }
        }

        function Save() {

            var Application_Date = $("#txtApplication_Date");
            var ApplicationRegNo = $("#txtApplicationRegNo");
            var Applicant_Name = $("#txtApplicant_Name");
            var Scheme = $("#dropScheme");
            var HouseName = $("#txtHouseName");
            var Place1 = $("#txtPlace1");
            var Place2 = $("#txtPlace2");
            var District = $("#dropDistrict");
            var SubDistrict = $("#dropSubDistrict");
            var PostOffices = $("#txtPostOffices");
            var Pincode = $("#txtPincode");
            var PhoneNo = $("#txtPhoneNo");
            var Cast = $("#dropCast");
            var SubCast = $("#dropSubCast");
            var Age = $("#txtAge");
            var Annual_Income = $("#txtAnnualIncome");
            var Sex = $("#dropSex");
            var DOB_Input = $("#txtDOB");
            var Retirement_Date = $("#txtRetirement");

            var Father_Name = $("#txtFatherName");
            var Name_Of_Spouse = $("#txtNameOfSpouse");
            var Taluk = $("#dropTaluk");
            var Village = $("#dropVillage");
            var Block = $("#dropBlock");
            var LocalBody = $("#dropLocalBody");
            var Panchayat = $("#dropPanchayat");
            var Municipality = $("#dropMunicipality");
            var Corporation = $("#dropCorporation");
            var LokSabha = $("#dropLokSabha");
            var Constituency = $("#dropConstituency");
            var AadharNo = $("#txtAadharNumber");
            var RationCardNo = $("#txtRationCardNumber");
            var Other_Id = $("#dropOther");
            var Other_Id_No = $("#txtIDNumber");
            var Loan_Amount = $("#txtLoanAmount");
            var LandMark = $("#txtLandMark");
            var Ward = $("#txtWard");


            Loan_Period_In_Year = parseInt(Loan_Period) / 12;

            $(".form-control").removeAttr("style");








            if (Applicant_Name.val().trim() == "") {
                Focus_Error(Applicant_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Name of Applicant is required !'
                })
            }
            else if (!Is_Valid_Text(Applicant_Name.val().trim())) {
                Focus_Error(Applicant_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed !'
                })
            }
            else if (AadharNo.val() == "") {
                Focus_Error(AadharNo);
                Toast.fire({
                    icon: 'error',
                    title: 'Aadhar No is required !'
                })
            }
            else if (Scheme.val() == "0") {
                Focus_Error(Scheme);
                Toast.fire({
                    icon: 'error',
                    title: 'Loan Scheme is required !'
                })
            }
            else if (HouseName.val() == "") {
                Focus_Error(HouseName);
                Toast.fire({
                    icon: 'error',
                    title: 'House Name/No is required !'
                })
            }

            else if (Place1.val() == "") {
                Focus_Error(Place1);
                Toast.fire({
                    icon: 'error',
                    title: 'Lane1 is required !'
                })
            }

            else if (Place2.val() == "") {
                Focus_Error(Place2);
                Toast.fire({
                    icon: 'error',
                    title: 'Lane2 is required !'
                })
            }
            else if (Pincode.val() == "") {
                Focus_Error(Pincode);
                Toast.fire({
                    icon: 'error',
                    title: 'Pincode is required !'
                })
            }


            else if (Cast.val() == "0") {
                Focus_Error(Cast);
                Toast.fire({
                    icon: 'error',
                    title: 'Caste is required !'
                })
            }
            else if (SubCast.val() == "0") {
                Focus_Error(SubCast);
                Toast.fire({
                    icon: 'error',
                    title: 'Sub Caste is required !'
                })
            }
            else if (PhoneNo.val().trim() == "") {
                Focus_Error(PhoneNo);
                Toast.fire({
                    icon: 'error',
                    title: 'Phone No is required !'
                })
            }








            else if (Father_Name.val().trim() == "") {
                Focus_Error(Father_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Father Name is required !'
                })
            }
            else if (!Is_Valid_Text(Father_Name.val().trim())) {
                Focus_Error(Father_Name);

                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed !'
                })
            }
            //else if (Name_Of_Spouse.val().trim() == "") {
            //    Focus_Error(Name_Of_Spouse);
            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Spouse Name is required !'
            //    })
            //}
            //else if (!Is_Valid_Text(Name_Of_Spouse.val().trim())) {
            //    Focus_Error(Name_Of_Spouse);
            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Special characters or Numbers not allowed !'
            //    })
            //}
            else if (Sex.val().trim() == "") {
                Focus_Error(Sex);
                Toast.fire({
                    icon: 'error',
                    title: 'Sex is required !'
                })
            }
            else if (DOB_Input.val().trim() == "") {
                Focus_Error(DOB_Input);
                Toast.fire({
                    icon: 'error',
                    title: 'Date of Birth is required !'
                })
            }
            else if (Retirement_Date.val().trim() == "" && Max_Age == "0") {
                Focus_Error(Retirement_Date);
                Toast.fire({
                    icon: 'error',
                    title: 'Retirement Date is required !'
                })
            }
            else if ((parseInt($("#hdn_Retirement_Age").val()) + 1) < parseInt(Loan_Period_In_Year)) {

                Focus_Error(Retirement_Date);
                Toast.fire({
                    icon: 'error',
                    title: 'Due to your retirement date, you are not eligible to apply for a loan !'
                })
            }
            else if (Village.val() == "0") {
                Focus_Error(Village);
                Toast.fire({
                    icon: 'error',
                    title: 'Village is required !'
                })
            }

            else if (LocalBody.val() == "0") {
                Focus_Error(LocalBody);
                Toast.fire({
                    icon: 'error',
                    title: 'Local Body is required !'
                })
            }
            else if (Block.val() == "0" && LocalBody.val() == "1") {
                Focus_Error(Block);
                Toast.fire({
                    icon: 'error',
                    title: 'Block is required !'
                })
            }
            else if (Ward.val() == "") {
                Focus_Error(Ward);
                Toast.fire({
                    icon: 'error',
                    title: 'Ward Number is required !'
                })
            }
            else if (LokSabha.val() == "0") {
                Focus_Error(LokSabha);
                Toast.fire({
                    icon: 'error',
                    title: 'Lok-Sabha is required !'
                })
            }

            else if (Constituency.val() == "0") {
                Focus_Error(Constituency);
                Toast.fire({
                    icon: 'error',
                    title: 'Constituency is required !'
                })
            }


            //else if (Other_Id.val() != "0") {
            //    Focus_Error(Other_Id);
            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Other Id No is required !'
            //    })
            //}
            //
            else if (Loan_Amount.val() == "0") {
                Focus_Error(Loan_Amount);
                Toast.fire({
                    icon: 'error',
                    title: 'Loan Amount is required !'
                })
            }
            else if (!Is_Number(Loan_Amount.val().trim())) {
                Focus_Error(Loan_Amount);
                Toast.fire({
                    icon: 'error',
                    title: 'Loan Amount only accept numbers !'
                })
            }


            else if (LocalBody.val().trim() == "0") {
                Focus_Error(LocalBody);
                Toast.fire({
                    icon: 'error',
                    title: 'Select Panchayat/Municipality/Corporation is required !'
                })
            }
            else if (AadharNo.val().trim() == "") {
                Focus_Error(AadharNo);
                Toast.fire({
                    icon: 'error',
                    title: 'Aadhar No is required !'
                })
            }
            else if (!Is_Valid_Aadhar_No(AadharNo.val().trim())) {
                Focus_Error(AadharNo);
                Toast.fire({
                    icon: 'error',
                    title: 'Invalid Aadhar number. Please enter 12 digits !'
                })
            }

            else if (Annual_Income.val().trim() == "") {
                Focus_Error(Annual_Income);
                Toast.fire({
                    icon: 'error',
                    title: 'Annual Income is required !'
                })
            }
            else if (RationCardNo.val() == "") {
                Focus_Error(RationCardNo);
                Toast.fire({
                    icon: 'error',
                    title: 'Ration Card Number is required !'
                })
            }
            else if (!Is_Valid_RationCard_No(RationCardNo.val().trim())) {
                Focus_Error(RationCardNo);
                Toast.fire({
                    icon: 'error',
                    title: 'Invalid Ration Card Number. Please enter 10 digits !'
                })
            }
            else if (!Check_Loan_Amount_Limit(Loan_Amount.val().trim())) {

                Focus_Error(Loan_Amount);
                Toast.fire({
                    icon: 'error',
                    title: 'Required Loan amount is above the limit of the scheme you selected !'
                })
            }

            else {

                if (PhoneNo.val().trim() != "") {

                    var values = PhoneNo.val().trim().split(",");
                    for (var i = 0; i < values.length; i++) {
                        if (values[values.length - 1] != "") {
                            if (!Is_Valid_Mobile_Number(values[i])) {
                                Focus_Error(PhoneNo);
                                Toast.fire({
                                    icon: 'error',
                                    title: 'Enter valid phone number !'
                                })
                                return;
                            }
                        }
                    }
                }

                if (Max_Age != "0") {
                    if (!Check_Age_Limit(Age.val().trim()) && Max_Age != "0") {
                        Focus_Error(Age);
                        Toast.fire({
                            icon: 'error',
                            title: 'Age is below the limit of the scheme you selected !'
                        })
                        return;
                    }
                }



                if (LocalBody.val() == "1") {
                    if (Panchayat.val() == "0") {
                        Focus_Error(Panchayat);
                        Toast.fire({
                            icon: 'error',
                            title: 'Panchayat is required !'
                        })
                        return;
                    }
                }
                else if (LocalBody.val() == "2") {
                    if (Municipality.val() == "0") {
                        Focus_Error(Municipality);
                        Toast.fire({
                            icon: 'error',
                            title: 'Municipality is required !'
                        })
                        return;
                    }

                }
                else if (LocalBody.val() == "3") {
                    if (Corporation.val() == "0") {
                        Focus_Error(Corporation);
                        Toast.fire({
                            icon: 'error',
                            title: 'Corporation is required !'
                        })
                        return;
                    }
                }

                if (Rural_Max != "0" & Urban_Max != "0") {
                    if (!Check_Annual_Income_Limit(Annual_Income.val())) {
                        Focus_Error(Annual_Income);
                        Toast.fire({
                            icon: 'error',
                            title: 'Annual Income is below the limit of the scheme you selected !'
                        })
                        return;
                    }

                }



                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/New_Update_To_tbl_loanapp",
                    data: JSON.stringify({ int_loanappid: getParameterByName("Id"), vchr_applname: Applicant_Name.val().trim(), vchr_Aadhaar: AadharNo.val().trim(), int_schemeid: Scheme.val(), vchr_hsename: HouseName.val(), vchr_place1: Place1.val(), vchr_place2: Place2.val(), vchr_district: District.val(), vchr_subdistrict: SubDistrict.val(), int_pincode: Pincode.val().trim(), vchr_post: $("#txtPostOffices").val(), vchr_phno: PhoneNo.val(), vchr_fathername: Father_Name.val().trim(), vchr_spousename: Name_Of_Spouse.val().trim(), vchr_caste: Cast.val(), subCast: SubCast.val(), vchr_sex: Sex.val(), dte_dob: DOB_Input.val(), age: $("#txtAge").val(), vchr_village: Village.val(), vchr_taluk: Taluk.val(), block_Id: Block.val(), municipality_Id: Municipality.val(), vchr_panchayat: Panchayat.val(), corporation_Id: Corporation.val(), lokSabha_Id: LokSabha.val(), assemply_Id: Constituency.val(), int_anninc: Annual_Income.val(), vchr_ration: RationCardNo.val(), int_loanamt_req: Loan_Amount.val(), Status: "Application Data Entry", LandMark: LandMark.val(), Ward: Ward.val() }),

                    contentType: "application/json; charset=utf-8",
                    success: function (data) {

                    },
                    error: function (error) {


                        // alert("error" + error.responseText);
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                        })
                    }
                }).done(function () {
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Application Data Entry Successfully Updated !',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked
                            Next();
                            // location.href = 'ApplicationDataEntry_View.aspx';
                            // Your code here
                        }
                    });

                });


            }

        }

        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }
        function Is_Number(inputText) {


            if (isNaN(inputText)) {
                return false;
            } else {
                return true;
            }


        }

        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }

        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }



        function Check_Age_Limit(Age) {
            //var Age = $("#txtAge").val();
            var Min_Age = $("#dropScheme option:selected").attr("data-min_age");
            var Max_Age = $("#dropScheme option:selected").attr("data-max_age");

            Age = parseInt(Age);
            Min_Age = parseInt(Min_Age);
            Max_Age = parseInt(Max_Age);

            if (Min_Age <= Age && Max_Age >= Age) {
                return true;
            }
            else {
                return false;
            }

        }

        function Check_Annual_Income_Limit(Annual_Income) {
            //  var Annual_Income = $("#txtAnnualIncome").val();
            //    var Anninc_Rural_Max = $("#dropScheme option:selected").attr("data-anninc_rural_max");
            //    var Anninc_Urban_Max = $("#dropScheme option:selected").attr("data-anninc_urban_max");

            Annual_Income = parseInt(Annual_Income);
            Rural_Max = parseInt(Rural_Max);
            Urban_Max = parseInt(Urban_Max);

            if (Rural_Max >= Annual_Income && Urban_Max >= Annual_Income) {
                return true;
            }
            else {
                return false;
            }

        }


        function Check_Loan_Amount_Limit(Loan_Amount) {
            //   var Loan_Amount = $("#txtLoanAmount").val();
            var Req_Loan_Amount = $("#dropScheme option:selected").attr("data-loan_amount");


            Loan_Amount = parseInt(Loan_Amount);
            Req_Loan_Amount = parseInt(Req_Loan_Amount);

            if (Loan_Amount <= Req_Loan_Amount) {
                return true;
            }
            else {
                return false;
            }

        }

        function Is_Valid_Aadhar_No(Aadhar_No) {
            var aadharPattern = /^\d{12}$/;

            if (aadharPattern.test(Aadhar_No)) {
                return true;
            } else {
                return false;
            }
        }


        function Is_Valid_RationCard_No(Ration_Card_No) {
            var rationPattern = /^\d{10}$/;

            if (rationPattern.test(Ration_Card_No)) {
                return true;
            } else {
                return false;
            }
        }



    </script>



</asp:Content>



















