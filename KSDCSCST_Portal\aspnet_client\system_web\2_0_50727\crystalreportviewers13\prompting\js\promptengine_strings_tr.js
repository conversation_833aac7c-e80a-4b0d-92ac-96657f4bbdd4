/* Copyright (c) Business Objects 2006. All rights reserved. */

// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "Bug\u00FCn";
var L_January   = "Ocak";
var L_February  = "\u015Eubat";
var L_March     = "Mart";
var L_April     = "Nisan";
var L_May       = "May\u0131s";
var L_June      = "Haziran";
var L_July      = "Temmuz";
var L_August    = "A\u011Fustos";
var L_September = "Eyl\u00FCl";
var L_October   = "Ekim";
var L_November  = "Kas\u0131m";
var L_December  = "Aral\u0131k";
var L_Su        = "Pa";
var L_Mo        = "Pt";
var L_Tu        = "Sa";
var L_We        = "\u00C7a";
var L_Th        = "Pe";
var L_Fr        = "Cu";
var L_Sa        = "Ct";

// Strings for prompts.js and prompts_param.js
var L_YYYY          = "yyyy";
var L_MM            = "aa";
var L_DD            = "gg";
var L_BadNumber     = "Bu parametre \"Say\u0131\" t\u00FCr\u00FCndedir ve yaln\u0131zca eksi i\u015Fareti, tamsay\u0131lar (\"0-9\"), tamsay\u0131 basamakland\u0131rma i\u015Faretleri veya ondal\u0131k i\u015Fareti i\u00E7erebilir. L\u00FCtfen girilen parametre de\u011Ferini d\u00FCzeltin.";
var L_BadCurrency   = "Bu parametre \"Para Birimi\" t\u00FCr\u00FCndedir ve yaln\u0131zca eksi i\u015Fareti, tamsay\u0131lar (\"0-9\"), tamsay\u0131 basamakland\u0131rma i\u015Faretleri veya ondal\u0131k i\u015Fareti i\u00E7erebilir. L\u00FCtfen girilen parametre de\u011Ferini d\u00FCzeltin.";
var L_BadDate       = "Bu parametre \"Tarih\" t\u00FCr\u00FCndedir ve \"%1\" bi\u00E7iminde olmal\u0131d\u0131r; burada \"yyyy\" d\u00F6rt basamakl\u0131 y\u0131l, \"aa\" ay (\u00F6rne\u011Fin, Ocak = 1) ve \"gg\" de ilgili aydaki ka\u00E7\u0131nc\u0131 g\u00FCn oldu\u011Fudur.";
var L_BadDateTime   = "Bu parametre \"TarihSaat\" t\u00FCr\u00FCndedir ve do\u011Fru bi\u00E7im \"%1 ss:dd:ss\" \u015Feklindedir. Burada \"yyyy\" d\u00F6rt basamakl\u0131 y\u0131l\u0131, \"aa\" ay\u0131 (\u00F6rne\u011Fin, Ocak = 1) ve \"gg\" de ilgili aydaki ka\u00E7\u0131nc\u0131 g\u00FCn oldu\u011Funu; ilk \"ss\" 24 saat bi\u00E7iminde saati, \"dd\" dakikay\u0131 ve son \"ss\" saniyeyi g\u00F6sterir.";
var L_BadTime       = "Bu parametre \"Saat\" t\u00FCr\u00FCndedir ve \"hh:mm:ss\" bi\u00E7iminde olmal\u0131d\u0131r; buradaki ilk \"ss\" 24 saat bi\u00E7iminde saat, \"dd\" dakika ve son \"ss\" saniyedir.";
var L_NoValue       = "De\u011Fer Yok";
var L_BadValue      = "De\u011Feri \"De\u011Fer Yok\" olarak ayarlamak i\u00E7in, Ba\u015Flang\u0131\u00E7 ve Biti\u015F de\u011Ferlerini \"De\u011Fer Yok\" olarak ayarlamal\u0131s\u0131n\u0131z.";
var L_BadBound      = "\"\u00DCst S\u0131n\u0131r Yok\" ve \"Alt S\u0131n\u0131r Yok\" \u00F6zelliklerini ayn\u0131 anda ayarlayamazs\u0131n\u0131z.";
var L_NoValueAlready = "Bu parametre \u00F6nceden \"De\u011Fer Yok\" olarak belirlenmi\u015F. Ba\u015Fka de\u011Ferler eklemeden \u00F6nce \"De\u011Fer Yok\" \u00F6zelli\u011Fini kald\u0131r\u0131n.";
var L_RangeError    = "Aral\u0131k ba\u015Flang\u0131c\u0131 aral\u0131k sonundan daha b\u00FCy\u00FCk olamaz.";
var L_NoDateEntered = "Bir tarih girmeniz gerekir.";
var L_Empty         = "L\u00FCtfen bir de\u011Fer girin.";

// Strings for filter dialog
var L_closeDialog="Pencereyi Kapat";

var L_SetFilter = "Filtreyi Ayarla";
var L_OK        = "Tamam";
var L_Cancel    = "\u0130ptal";

 /* Crystal Decisions Confidential Proprietary Information */
