// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "Hoy";
var L_January   = "Enero";
var L_February  = "Febrero";
var L_March     = "Marzo";
var L_April     = "Abril";
var L_May       = "Mayo";
var L_June      = "Junio";
var L_July      = "Julio";
var L_August    = "Agosto";
var L_September = "Septiembre";
var L_October   = "Octubre";
var L_November  = "Noviembre";
var L_December  = "Diciembre";
var L_Su        = "Dom";
var L_Mo        = "Lun";
var L_Tu        = "Mar";
var L_We        = "Mi\u00e9";
var L_Th        = "Jue";
var L_Fr        = "Vie";
var L_Sa        = "S\u00e1b";

// strings for dt_param.js
var L_TIME_SEPARATOR = ":";
var L_AM_DESIGNATOR = "a.m.";
var L_PM_DESIGNATOR = "p.m.";

// strings for range parameter
var L_FROM = "Desde {0}";
var L_TO = "Hasta {0}";
var L_AFTER = "Despu\u00e9s {0}";
var L_BEFORE = "Antes {0}";
var L_FROM_TO = "De {0} a {1}";
var L_FROM_BEFORE = "De {0} hasta antes de {1}";
var L_AFTER_TO = "Despu\u00e9s de {0} hasta {1}";
var L_AFTER_BEFORE = "Despu\u00e9s de {0} hasta antes de {1}";

// Strings for prompts.js and prompts_param.js
var L_BadNumber		= "Este par\u00e1metro es de tipo \"N\u00famero\" y s\u00f3lo puede contener un s\u00edmbolo de signo negativo, los d\u00edgitos (\"0-9\"), s\u00edmbolos de agrupaci\u00f3n de d\u00edgitos o un s\u00edmbolo decimal. Corrija el valor del par\u00e1metro especificado.";
var L_BadCurrency	= "Este par\u00e1metro es de tipo \"Moneda\" y s\u00f3lo puede contener un s\u00edmbolo de signo negativo, los d\u00edgitos (\"0-9\"), s\u00edmbolos de agrupaci\u00f3n de d\u00edgitos o un s\u00edmbolo decimal. Corrija el valor del par\u00e1metro especificado.";
var L_BadDate		= "Este par\u00e1metro es de tipo \"Fecha\" y debe tener el siguiente formato \"Fecha(aaaa,mm,dd)\" donde \"aaaa\" es un a\u00f1o con cuatro d\u00edgitos, \"mm\" es el mes (por ejemplo, Enero= 1) y \"dd\" es el n\u00famero de d\u00edas del mes.";
var L_BadDateTime   = "Este par\u00e1metro es de tipo \"FechaHora\" y el formato correcto es \"FechaHora(aaaa,mm,dd,hh,mm,ss)\". \"aaaa\" es el a\u00f1o de cuatro d\u00edgitos, \"mm\" es el mes (p. ej. enero = 1), \"dd\" es el d\u00eda del mes, \"hh\" es la hora en formato de 24 horas, \"mm\" son los minutos y \"ss\" los segundos.";
var L_BadTime       = "Este par\u00e1metro es de tipo \"Hora\" y debe tener el formato \"hh:mm:ss\", donde \"hh\" son las horas en formato de 24 horas, \"mm\" son los minutos de la hora y \"ss\" los segundos del minuto.";
var L_NoValue       = "Ning\u00fan valor";
var L_BadValue      = "Para establecer \"Ning\u00fan valor\", debe establecer los valores Desde y Hasta en \"Ning\u00fan valor\".";
var L_BadBound      = "No puede establcer \"Ning\u00fan l\u00edmite inferior\" junto con \"Ning\u00fan l\u00edmite superior\".";
var L_NoValueAlready = "Este par\u00e1metro ya se ha establecido en \"Ning\u00fan valor\". Quite \"Ning\u00fan valor\" antes de agregar otros valores";
var L_RangeError    = "El inicio del rango no puede ser mayor que el final.";
var L_NoDateEntered = "Debe especificar una fecha.";

// Strings for ../html/crystalexportdialog.htm
var L_ExportOptions     = "Opciones de exportaci\u00f3n";
var L_PrintOptions      = "Opciones de impresi\u00f3n";
var L_PrintPageTitle    = "Imprimir el informe";
var L_ExportPageTitle   = "Exportar el informe";
var L_OK                = "Aceptar";
var L_PrintPageRange    = "Especifique el rango de p\u00e1gina que desea imprimir.";
var L_ExportPageRange   = "Especifique el rango de p\u00e1gina que desea exportar.";
var L_InvalidPageRange  = "Los valores del rango de p\u00e1ginas no son correctos. Especifique un rango de p\u00e1ginas v\u00e1lido.";
var L_ExportFormat      = "Seleccione un formato de exportaci\u00f3n de la lista.";
var L_Formats           = "Formatos:";
var L_All               = "Todos";
var L_Pages             = "P\u00e1ginas";
var L_From              = "Desde:";
var L_To                = "Hasta:";
var L_PrintStep0        = "Para imprimir:";
var L_PrintStep1        = "1.  En el siguiente cuadro de di\u00e1logo, seleccione la opci\u00f3n \"Abrir este archivo\" y haga clic en el bot\u00f3n Aceptar.";
var L_PrintStep2        = "2.  Haga clic en el icono de impresora del men\u00fa Acrobat Reader en lugar de utilizar el bot\u00f3n de impresi\u00f3n de su explorador de Internet.";
var L_RTFFormat         = "Formato RTF";
var L_AcrobatFormat     = "Formato Acrobat (PDF)";
var L_CrystalRptFormat  = "Crystal Reports (RPT)";
var L_WordFormat        = "MS Word";
var L_ExcelFormat       = "MS Excel 97-2000";
var L_ExcelRecordFormat = "MS Excel 97-2000 (s\u00f3lo datos)";
if(typeof(Sys)!=='undefined')Sys.Application.notifyScriptLoaded();
