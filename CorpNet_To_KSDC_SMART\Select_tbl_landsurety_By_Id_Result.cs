//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CorpNet_To_KSDC_SMART
{
    using System;
    
    public partial class Select_tbl_landsurety_By_Id_Result
    {
        public int int_landid { get; set; }
        public Nullable<decimal> int_loanappid { get; set; }
        public string vchr_apprecregno { get; set; }
        public string vchr_ownername { get; set; }
        public string vchr_hsename { get; set; }
        public string vchr_lane1 { get; set; }
        public string vchr_lane2 { get; set; }
        public string vchr_post { get; set; }
        public Nullable<decimal> int_pin { get; set; }
        public string vchr_phno { get; set; }
        public string vchr_deedtype { get; set; }
        public string vchr_deedno { get; set; }
        public string vchr_subregoffiname { get; set; }
        public string vchr_surveyno { get; set; }
        public string vchr_extent { get; set; }
        public string vchr_tpno { get; set; }
        public string vchr_village { get; set; }
        public string vchr_taluk { get; set; }
        public string vchr_district { get; set; }
        public string vchr_land_village { get; set; }
        public string vchr_land_taluk { get; set; }
        public string vchr_land_district { get; set; }
        public Nullable<System.DateTime> dte_conf_send_date { get; set; }
        public Nullable<System.DateTime> dte_conf_rec_date { get; set; }
        public string int_loanno { get; set; }
        public string vchr_fathername { get; set; }
        public string vchr_documentno { get; set; }
        public Nullable<decimal> int_ValAmt { get; set; }
        public string vchr_Ashwas { get; set; }
        public Nullable<decimal> int_LoaneeH { get; set; }
        public Nullable<bool> int_fair { get; set; }
        public Nullable<decimal> int_NotPledged { get; set; }
    }
}
