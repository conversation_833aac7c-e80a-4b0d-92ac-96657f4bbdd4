﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Disbursement_View.aspx.cs" Inherits="KSDCSCST_Portal.Disbursement_View" %>



<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">



    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- daterange picker -->
    <link rel="stylesheet" href="assets/plugins/daterangepicker/daterangepicker.css">
    <!-- iCheck for checkboxes and radio inputs -->
    <link rel="stylesheet" href="assets/plugins/icheck-bootstrap/icheck-bootstrap.min.css">
    <!-- Bootstrap Color Picker -->
    <link rel="stylesheet" href="assets/plugins/bootstrap-colorpicker/css/bootstrap-colorpicker.min.css">
    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet" href="assets/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css">
    <!-- Select2 -->
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">
    <!-- Bootstrap4 Duallistbox -->
    <link rel="stylesheet" href="assets/plugins/bootstrap4-duallistbox/bootstrap-duallistbox.min.css">
    <!-- BS Stepper -->
    <link rel="stylesheet" href="assets/plugins/bs-stepper/css/bs-stepper.min.css">
    <!-- dropzonejs -->
    <link rel="stylesheet" href="assets/plugins/dropzone/min/dropzone.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">
    <style>
        table {
            border-collapse: collapse;
            width: 100%;
        }

        input:disabled {
            border: 0;
        }
    </style>
    <style>
        /* Styles for the search input */
        txtPostOffices {
            width: 300px;
            padding: 10px;
            font-size: 16px;
        }

        /* Styles for the search results container */
        #search-results {
            list-style: none;
            padding: 0;
            margin-top: 5px;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
        }

            /* Styles for individual search result items */
            #search-results li {
                padding: 5px;
                cursor: pointer;
            }

                /* Highlight the selected result */
                #search-results li:hover {
                    background-color: #f2f2f2;
                }

        td {
            padding-left: 10px;
        }
    </style>

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">





                <div class="col-sm-6">
                    <h1>Loan Disbursement</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">Loan Disbursement</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-10 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <div class="Sub_Header">Loan Details</div>
                                            <div class="form-group row" style="display: none;">
                                                <label class="col-sm-3 col-form-label">Loan No.*</label>
                                                <div class="col-sm-9">
                                                    <input type="text" disabled="disabled" class="form-control" id="txtLoan_No" placeholder="Loan No.">
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-3 col-form-label">Fund*</label>
                                                <div class="col-sm-9">
                                                    <input type="text" disabled="disabled" class="form-control" id="txtFund" placeholder="Fund">
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label class="col-sm-3 col-form-label">Name*</label>
                                                <div class="col-sm-9">
                                                    <input type="text" disabled="disabled" class="form-control" id="txtName" placeholder="Name">
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-3 col-form-label">Scheme*</label>
                                                <div class="col-sm-9">
                                                    <input type="text" disabled="disabled" class="form-control" id="txtScheme" placeholder="Scheme">
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-3 col-form-label">Max-Loan Amount*</label>
                                                <div class="col-sm-9">
                                                    <input type="number" disabled="disabled" class="form-control" id="txtLoan_Amount" placeholder="Loan Amount">
                                                </div>
                                            </div>


                                            <div class="form-group row">
                                                <label class="col-sm-3 col-form-label">Requested Loan Amount*</label>
                                                <div class="col-sm-9">
                                                    <input type="number" disabled="disabled" class="form-control" id="txtRequested_Loan_Amount" placeholder="Requiested Loan Amount ">
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label class="col-sm-3 col-form-label">Sanctioned Loan Amount*</label>
                                                <div class="col-sm-9">
                                                    <input type="number" disabled="disabled" class="form-control" id="txtSanctioned_Loan_Amount" placeholder="Sanctioned Loan Amount">
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-3 col-form-label">Loan Interest*</label>
                                                <div class="col-sm-9">
                                                    <input type="number" disabled="disabled" class="form-control" id="txtLoan_Interest" placeholder="Loan Interest">
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label class="col-sm-3 col-form-label">Loan Period*</label>
                                                <div class="col-sm-9">
                                                    <input type="number" disabled="disabled" class="form-control" id="txtLoan_Period" placeholder="Loan Period">
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label class="col-sm-3 col-form-label">Installment Number*</label>
                                                <div class="col-sm-9">
                                                    <input type="number" disabled="disabled" class="form-control" id="txtInstallment_Number" placeholder="Installment Number">
                                                    <input type="hidden" id="TotalNoOfInst" name="TotalNoOfInst" value="">
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-3 col-form-label">Installment Amount*</label>
                                                <div class="col-sm-9">
                                                    <input type="number" disabled="disabled" class="form-control" id="txtInstallment_Amount" placeholder="Amount to be Disbursed">
                                                    <input type="hidden" id="LoanAmount" name="LoanAmount" value="">
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-3 col-form-label">Disbursement Date*</label>
                                                <div class="col-sm-9">
                                                    <input disabled="disabled" class="dateandtime form-control" id="txtDisbursement_Date" type="text" name="machin-b" value="">
                                                </div>
                                            </div>
                                            <div id="DIV_EMI_DUE_Date" style="display: none;">
                                                <div class="form-group row">
                                                    <label class="col-sm-3 col-form-label">EMI</label>
                                                    <div class="col-sm-9">
                                                        <input type="text" disabled="disabled" class="form-control" id="txtEMI" placeholder="EMI">
                                                    </div>
                                                </div>


                                                <div class="form-group row">
                                                    <label class="col-sm-3 col-form-label">First Due Date*</label>
                                                    <div class="col-sm-9">
                                                        <input disabled="disabled" class="dateandtime form-control" id="txtFirstDue_Date" type="text" name="machin-b" value="">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-3 col-form-label">Amount to be Disbursed*</label>
                                                <div class="col-sm-9">
                                                    <input type="number" disabled="disabled" class="form-control" id="txtAmountToBeDisbursed" placeholder="Amount to be Disbursed">
                                                </div>
                                            </div>
                                          
                                                <div class="form-group row">
                                                    <label class="col-sm-3 col-form-label">Voucher No*</label>
                                                    <div class="col-sm-9">
                                                        <input type="number" disabled="disabled" class="form-control" id="txtVoucher_No" placeholder="Voucher No">
                                                    </div>
                                                </div>

                                                <div class="form-group row">
                                                    <label class="col-sm-3 col-form-label">Cheque No.*</label>
                                                    <div class="col-sm-9">
                                                        <input type="number"   class="form-control" id="txtCheque_No" placeholder="Cheque No.">
                                                    </div>
                                                </div>

                                                <div class="form-group row" style="display: none;">
                                                    <label class="col-sm-3 col-form-label">Account No.*</label>
                                                    <div class="col-sm-9">
                                                        <input type="number" disabled="disabled" class="form-control" id="txtAcc_No" placeholder="Account No.">
                                                    </div>
                                                </div>

                                                <div class="form-group row" style="display: none;">
                                                    <label class="col-sm-0 col-form-label"></label>
                                                    <div class="col-sm-9">
                                                        <label class="col-sm-8 col-form-label">E-Payment</label>

                                                        <input class="col-sm-1 form-check-input" style="margin: 0; position: relative;" type="checkbox" id="checkE_payment">
                                                    </div>

                                                </div>
                                            
                                        </div>

                                        <div class="col-sm-6">

                                            <div class="row">
                                                <div class="col-sm-12">
                                                    <div class="Sub_Header">Installment Disbursed*</div>
                                                    <table border="1">
                                                        <thead>
                                                            <th style="text-align: center;">Installment No.</th>
                                                            <th style="text-align: center;">Installment Amount</th>
                                                            <th style="text-align: center;">Disb Amount</th>
                                                            <th style="text-align: center;">Disb Date</th>

                                                        </thead>
                                                        <tbody id="tbody_Installment_Disbursed">
                                                        </tbody>


                                                    </table>



                                                </div>
                                            </div>

                                            <div class="row" style="margin-top: 20px;">
                                                <div class="col-sm-12">
                                                    <div class="Sub_Header">Remittance*</div>

                                                    <table border="1" id="tbl_Remittance_Details">
                                                        <tr>
                                                            <td>Beneficiary Contribution</td>
                                                            <td>
                                                                <label id="txteneficiary_Contribution">0</label></td>
                                                        </tr>
                                                        <tr>
                                                            <td>Processing Fee</td>
                                                            <td>
                                                                <label id="txtProcessing_Fee">0</label></td>
                                                        </tr>
                                                        <tr>
                                                            <td>Legal Fee</td>
                                                            <td>
                                                                <label id="txtLegal_Fee">0</label></td>
                                                        </tr>
                                                        <tr>
                                                            <td>Land Verification Fee</td>
                                                            <td>
                                                                <label id="txtLand_Verification_Fee">0</label></td>
                                                        </tr>
                                                        <tr>
                                                            <td>Total</td>
                                                            <td>
                                                                <label id="txtTotal_Remittance">0</label></td>
                                                        </tr>
                                                        <tr id="tr_Remittance">
                                                            <td colspan="2" style="text-align:center;">
                                                                <span id="SPAN_Remittance" style="color: red; font-weight: bold;">Please remit all the Remittance !</span>
                                                            </td>
                                                                 
                                                        </tr>
                                                    </table>
                                                    <%--  <table border="1">
                                                        

                                                        
                                                        
                                                        <tr>
                                                            <td>Postage Fee</td>
                                                            <td>
                                                                <label id="txtPostage_Fee">0</label></td>
                                                        </tr>
                                                        <tr>
                                                            <td>Others</td>
                                                            <td>
                                                                <label id="txtOthers">0</label></td>
                                                        </tr>
                                                        



                                                    </table>--%>
                                                </div>
                                            </div>

                                            <div class="row" style="display: none;">
                                                <div class="col-sm-12">

                                                    <div class="form-group row">
                                                        <label class="col-sm-0 col-form-label"></label>
                                                        <div class="col-sm-9">
                                                            <input class="col-sm-1 form-check-input" style="margin: 0; position: relative;" type="checkbox" id="checkBeneficiary_Acc_Details">
                                                            <label class="col-sm-8 col-form-label">Beneficiary Account Details</label>


                                                        </div>

                                                    </div>

                                                </div>
                                            </div>




                                            <div class="row" style="margin-top: 20px;">
                                                <div class="col-sm-12">



                                                    <table border="1">
                                                        <tr>
                                                            <td>Bank Account Details</td>
                                                            <td>
                                                                <label id="txtBank_Acc_Details"></label>
                                                            </td>
                                                        </tr>

                                                        <tr>
                                                            <td>IFSC</td>
                                                            <td>
                                                                <label id="txtIFSC"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>bank Name</td>
                                                            <td>
                                                                <label id="txtbank_Name"></label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>Branch</td>
                                                            <td>
                                                                <label id="txtBranch"></label>
                                                            </td>
                                                        </tr>



                                                    </table>



                                                </div>
                                            </div>





                                            <label style="display: none;" class="col-sm-9 col-form-label">Student age is now 20 make sure the agreement is executed!!!</label>






                                            <input type="hidden" id="hdn_Retirement_Age" />
                                        </div>


                                    </div>
                                    <div class="row">
                                        <div class="col-sm-12">
                                        </div>


                                    </div>

                                </div>
                            </div>

                            <div class="row" style="margin-top: 20px;">
                                <div class="col-sm-12">
                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">

                                            <a href="Disbursement_List.aspx" class="btn btn-dark">Back</a>
                                            <a onclick="Save();" id="SubmitDisbursement" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
        </div>
        <!-- /.row -->

        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->


    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 -->
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <!-- Bootstrap4 Duallistbox -->
    <script src="assets/plugins/bootstrap4-duallistbox/jquery.bootstrap-duallistbox.min.js"></script>
    <!-- InputMask -->
    <script src="assets/plugins/moment/moment.min.js"></script>
    <script src="assets/plugins/inputmask/jquery.inputmask.min.js"></script>
    <!-- date-range-picker -->
    <script src="assets/plugins/daterangepicker/daterangepicker.js"></script>
    <!-- bootstrap color picker -->
    <script src="assets/plugins/bootstrap-colorpicker/js/bootstrap-colorpicker.min.js"></script>
    <!-- Tempusdominus Bootstrap 4 -->
    <script src="assets/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js"></script>
    <!-- BS-Stepper -->
    <script src="assets/plugins/bs-stepper/js/bs-stepper.min.js"></script>
    <!-- dropzonejs -->
    <script src="assets/plugins/dropzone/min/dropzone.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script src="assets/js/jquery.dateandtime.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>


    <script>

        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;
        const $searchInput = $('#txtPincode');

        var Rural_Max = "";
        var Urban_Max = "";
        var Min_Age = "";
        var Max_Age = "";
        var Loan_Amount = "";
        var Loan_Period = "";




        // Results container element
        const $searchResults = $('#search-results');
        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }
        $(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            $('#txtDisbursement_Date').val(formattedDate);

            // Display the formatted date in the "currentDate" div


        })

        $(document).ready(function () {
            Load_All_Application_Issue_By_Id(getParameterByName("Id"));
        });

        var Caste_Id;
        var SubCaste_Id;
        var Village_Id;
        var SubDistrict_Id;
        var Scheme_Id;
        var RegNo;
        var LoanNo;
        var LoanAppId;

        $("#tr_Remittance").hide();

        function Load_All_Application_Issue_By_Id(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Id: Id }), // If you have parameters
                url: "WebService.asmx/Select_tbl_LoanApp_By_LoanAppId",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        LoanAppId = value.int_loanappid;
                        RegNo = value.vchr_appreceivregno;
                        LoanNo = value.int_loanno;
                        $("#txtName").val(value.vchr_applname);
                        Caste_Id = value.vchr_caste;
                        SubCaste_Id = value.SubCast;
                        Village_Id = value.vchr_village;
                        SubDistrict_Id = value.vchr_subdistrict;
                        Scheme_Id = value.int_schemeid;
                        $("#txtFund").val(value.vchr_fund);
                        var OffId = value.vchr_offid;
                        $("#txtLoan_No").val(OffId + Id);
                        $("#txtRequested_Loan_Amount").val(value.int_loanamt_req);
                        $("#txtSanctioned_Loan_Amount").val(value.int_amtsanction);
                        $('#LoanAmount').val(value.int_amtsanction);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Schemes_By_Id(Scheme_Id, RegNo, LoanNo);
            });
        }
        function Load_All_Schemes_By_Id(Scheme_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Schemes_By_ID",
                data: JSON.stringify({ Id: Scheme_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtLoan_Scheme').val('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Scheme = value.Scheme;
                        $('#txtScheme').val(Scheme);
                        $('#txtLoan_Amount').val(value.Loan_Amount);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_tbl_loanreg_By_RegNo_Or_LoanNo(RegNo, LoanNo);
            });

        }
        var InstNo = 0;

        function Load_All_tbl_loanreg_By_RegNo_Or_LoanNo(RegNo, LoanNo) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_tbl_loanreg_By_RegNo_Or_LoanNo",
                data: JSON.stringify({ appreceivr_RegNo: RegNo, loanNo: LoanNo }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $('#txtInstallment_Number').val('');
                    $('#TotalNoOfInst').val('');
                    $('#txtEMI').val('');
                    $('#txtLoan_Period').val('');
                    $('#txtLoan_Interest').val('');
                    $('#LoanAmount').val('');
                    $.each(data.d, function (key, value) {

                        $('#txtInstallment_Number').val((value.int_inst_disbursed) + 1);
                        $('#TotalNoOfInst').val(value.int_disb_inst);
                        if (value.int_disb_inst != value.int_inst_disbursed) {

                            $('#DIV_EMI_DUE_Date').css("display", "none");
                        }
                        if (value.int_disb_inst == "1") {
                            $('#DIV_EMI_DUE_Date').css("display", "block");
                        }
                        InstNo = (value.int_inst_disbursed) + 1;
                        // $('#txtEMI').val(value.mny_repayamt);
                        $('#txtLoan_Period').val(value.int_repay_inst);
                        $('#txtLoan_Interest').val(value.int_rate_int);

                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Disbursement_By_RegNo(RegNo, InstNo);
            });
        }
        function Load_All_Disbursement_By_RegNo(App_Reg_No, InstNo) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_tbl_disbursement_By_RegNo",
                data: JSON.stringify({ appreceivr_RegNo: App_Reg_No, loanNo: "" }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $.each(data.d, function (key, value) {
                        if (value.int_instalNo == InstNo) {
                            $('#txtAmountToBeDisbursed').val(value.mny_InstallAmt);
                            $('#txtInstallment_Amount').val(value.mny_InstallAmt);
                        }
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                if (InstNo == $('#TotalNoOfInst').val()) {

                    Calculate_EMI_And_First_Due_Date();

                }
                Load_All_Bank_Details_By_int_loanappid(getParameterByName("Id"))

            });
        }
        function Load_All_Bank_Details_By_int_loanappid(LoanAppId) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Bank_Details_By_int_loanappid",
                data: JSON.stringify({ int_loanappid: LoanAppId }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtAccount_Number').val('');
                    $('#txtIFSC').val('');
                    $('#txtBank_Name').val('');
                    $('#txtBranch').val('');
                    $.each(data.d, function (key, value) {
                        $('#txtBank_Acc_Details').text(value.int_BankAccNo);
                        $('#txtIFSC').text(value.vchr_IFSC);
                        $('#txtbank_Name').text(value.vchr_Bank);
                        $('#txtBranch').text(value.vchr_Branch);

                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Disbursement_Details(LoanAppId);
            });
        }
        function Load_All_Disbursement_Details(LoanAppId) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Disbursement_Details",
                data: JSON.stringify({ Id: LoanAppId }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $('#tbody_Installment_Disbursed').empty();
                    $.each(data.d, function (key, value) {
                        var dateString = value.dt_disbdate;

                        // Parse the date string to create a Date object
                        var date = new Date(dateString);

                        // Check if the date is equal to 01/01/1900
                        if (date.getTime() === new Date("01/01/1900").getTime()) {
                            // If it matches, hide the element with a specific ID
                            dateString = '';
                        }
                        else {
                            dateString = value.dt_disbdate;
                        }


                        $('#tbody_Installment_Disbursed').append('<tr>'
                            + '    <td style="text-align: center;">'
                            + '         <span>' + value.int_instalNo + '</span>'
                            + '     </td>'
                            + '    <td style="text-align: center;">'
                            + '         <span>' + value.mny_InstallAmt + '</span>'
                            + '     </td>'
                            + '   <td style="text-align: center;">'
                            + '         <span>' + value.mny_disbamt + '</span>'
                            + '     </td>'
                            + '    <td style="text-align: center;">'
                            + '         <span>' + dateString + '</span>'
                            + '     </td>'
                            + '</tr>');

                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_Remittance_Details(RegNo);

            });
        }

        var BC_Amount = 0;
        var Flag_BC = false;
        var Total_Amount = 0;
        function Load_Remittance_Details(RegNo) {

            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ reg_No: RegNo }), // If you have parameters
                url: "WebService.asmx/Get_All_Remittance_By_Reg_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $.each(data.d, function (key, value) {

                        $('#txteneficiary_Contribution').text(value.BC);
                        $('#txtProcessing_Fee').text(value.ProcessingFee);
                        $('#txtLegal_Fee').text(value.LegalFee);
                        $('#txtLand_Verification_Fee').text(value.LandFee);
                        $('#txtTotal_Remittance').text(value.Total);
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                // Load_BC_Amount_By_Reg_No(RegNo);
                Load_Processing_Fee(RegNo);


            });
        }
        function Load_Processing_Fee(RegNo) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ reg_No: RegNo }), // If you have parameters
                url: "WebService.asmx/Get_Processing_Fee_By_Reg_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $.each(data.d, function (key, value) {
                        if (parseInt($("#txtProcessing_Fee").text()) != parseInt(value.Total_Processing_Fee)) {
                            $("#tr_Remittance").show();
                            $("#SubmitDisbursement").hide();
                            $("#txtProcessing_Fee").css("color", "red");
                        }
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }
        function Load_BC_Amount_By_Reg_No(RegNo) {

            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ reg_No: RegNo }), // If you have parameters
                url: "WebService.asmx/Get_BC_Amount_By_Reg_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $.each(data.d, function (key, value) {
                        if (Flag_BC == false) {
                            $("#tbl_Remittance_Details").append('<tr>'
                                + '	<td>Beneficiary Contribution</td>'
                                + '    <td><label style="color:red;">' + value.BC_Amount + '</label></td>'
                                + '</tr>');
                            BC_Amount = value.BC_Amount;
                        }

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $("#tbl_Remittance_Details").append('<tr>'
                    + '	<td>Total</td>'
                    + '    <td><label>' + Total_Amount + '</label></td>'
                    + '</tr>');

                if ($('#TotalNoOfInst').val() == 1) {
                    Calculate_EMI_And_First_Due_Date_Single(parseFloat($("#txtInstallment_Amount").val()) - parseFloat(BC_Amount));
                }

            });
        }

        function getFormattedDate(date) {
            date = date.split('/')[2] + "-" + date.split('/')[1] + "-" + date.split('/')[0];
            return date;
        }

        function Calculate_EMI_And_First_Due_Date() {
            var LoanAmount = $('#txtSanctioned_Loan_Amount').val();
            var interestRate = parseFloat($("#txtLoan_Interest").val()) / 1200; // Monthly interest rate
            var numberOfPayments = parseInt($("#txtLoan_Period").val());  // Number of payments (months)
            var monthlyPayment = calculatePMT(interestRate, numberOfPayments, LoanAmount);
            monthlyPayment = Math.ceil(monthlyPayment / 50) * 50;
            $("#txtEMI").val(monthlyPayment);

            var DisbDate = $('#txtDisbursement_Date').val();
            var dateObject = new Date(getFormattedDate(DisbDate));
            var dueDate = new Date(dateObject.getFullYear(), dateObject.getMonth() + 1, dateObject.getDate());
            if (dateObject.getDate() >= 28) {
                $('#txtFirstDue_Date').val("28/" + ("0" + (dueDate.getMonth() + 1)).slice(-2) + "/" + dueDate.getFullYear());
            } else {
                var formattedDueDate = ("0" + (dueDate.getDate())).slice(-2) + "/" + ("0" + (dueDate.getMonth() + 1)).slice(-2) + "/" + dueDate.getFullYear();
                $('#txtFirstDue_Date').val(formattedDueDate);
            }
        }
        function Calculate_EMI_And_First_Due_Date_Single(LoanAmount) {
            // var LoanAmount = $('#txtSanctioned_Loan_Amount').val();
            var interestRate = parseFloat($("#txtLoan_Interest").val()) / 1200; // Monthly interest rate
            var numberOfPayments = parseInt($("#txtLoan_Period").val());  // Number of payments (months)
            var monthlyPayment = calculatePMT(interestRate, numberOfPayments, LoanAmount);
            monthlyPayment = Math.ceil(monthlyPayment / 50) * 50;
            $("#txtEMI").val(monthlyPayment);

            var DisbDate = $('#txtDisbursement_Date').val();
            var dateObject = new Date(getFormattedDate(DisbDate));
            var dueDate = new Date(dateObject.getFullYear(), dateObject.getMonth() + 1, dateObject.getDate());
            if (dateObject.getDate() >= 28) {
                $('#txtFirstDue_Date').val("28/" + ("0" + (dueDate.getMonth() + 1)).slice(-2) + "/" + dueDate.getFullYear());
            } else {
                var formattedDueDate = ("0" + (dueDate.getDate())).slice(-2) + "/" + ("0" + (dueDate.getMonth() + 1)).slice(-2) + "/" + dueDate.getFullYear();
                $('#txtFirstDue_Date').val(formattedDueDate);
            }
        }

        function calculatePMT(rate, nper, pv) {
            var pmt = (pv * rate) / (1 - Math.pow(1 + rate, -nper));
            return pmt;
        }

        function Save() {

            var LoanAppId = getParameterByName("Id")
            var LoanNo = $("#txtLoan_No");
            var InstNo = $("#txtInstallment_Number");
            var DisbDate = $("#txtDisbursement_Date");
            var FirstDueDate = $("#txtFirstDue_Date").val();
            var EMI = $("#txtEMI");

            console.log(FirstDueDate);
            console.log(EMI.val());

            if (FirstDueDate == "") { FirstDueDate = "01/01/1900"; }

            var Loan_Number = "";
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Loan_Disbursement_By_LoanAppId",
                data: JSON.stringify({ int_loanappid: LoanAppId, loanNo: Loan_Number, instNo: InstNo.val(), disbDate: getFormattedDate(DisbDate.val()), firstDueDate: getFormattedDate(FirstDueDate), eMI: EMI.val(), voucher_No: "", chk_No: $("#txtCheque_No").val().trim() }),

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    if (data.d.Status == "Success") {
                        Loan_Number = data.d.Loan_Number;
                    }
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                    })
                }
            }).done(function () {
                Swal.fire({
                    icon: 'success',
                    title: 'Message',
                    text: 'Loan Disbursed - New Loan Number : ' + Loan_Number,
                    allowOutsideClick: false,
                }).then((result) => {
                    if (result.isConfirmed) {
                        // OK button clicked
                        location.href = 'Disbursement_List.aspx';
                        // Your code here
                    }
                });

            });

        }


        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }
        function Is_Number(inputText) {


            if (isNaN(inputText)) {
                return false;
            } else {
                return true;
            }


        }

        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }

        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }



        function Check_Age_Limit(Age) {
            //var Age = $("#txtAge").val();
            var Min_Age = $("#dropScheme option:selected").attr("data-min_age");
            var Max_Age = $("#dropScheme option:selected").attr("data-max_age");

            Age = parseInt(Age);
            Min_Age = parseInt(Min_Age);
            Max_Age = parseInt(Max_Age);

            if (Min_Age <= Age && Max_Age >= Age) {
                return true;
            }
            else {
                return false;
            }

        }

        function Check_Annual_Income_Limit(Annual_Income) {
            //  var Annual_Income = $("#txtAnnualIncome").val();
            //    var Anninc_Rural_Max = $("#dropScheme option:selected").attr("data-anninc_rural_max");
            //    var Anninc_Urban_Max = $("#dropScheme option:selected").attr("data-anninc_urban_max");

            Annual_Income = parseInt(Annual_Income);
            Rural_Max = parseInt(Rural_Max);
            Urban_Max = parseInt(Urban_Max);

            if (Rural_Max >= Annual_Income && Urban_Max >= Annual_Income) {
                return true;
            }
            else {
                return false;
            }

        }


        function Check_Loan_Amount_Limit(Loan_Amount) {
            //   var Loan_Amount = $("#txtLoanAmount").val();
            var Req_Loan_Amount = $("#dropScheme option:selected").attr("data-loan_amount");


            Loan_Amount = parseInt(Loan_Amount);
            Req_Loan_Amount = parseInt(Req_Loan_Amount);

            if (Loan_Amount <= Req_Loan_Amount) {
                return true;
            }
            else {
                return false;
            }

        }

        function Is_Valid_Aadhar_No(Aadhar_No) {
            var aadharPattern = /^\d{12}$/;

            if (aadharPattern.test(Aadhar_No)) {
                return true;
            } else {
                return false;
            }
        }


        function Is_Valid_RationCard_No(Ration_Card_No) {
            var rationPattern = /^\d{10}$/;

            if (rationPattern.test(Ration_Card_No)) {
                return true;
            } else {
                return false;
            }
        }



    </script>



</asp:Content>




















