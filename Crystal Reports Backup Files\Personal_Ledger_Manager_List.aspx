﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Personal_Ledger_Manager_List.aspx.cs" Inherits="KSDCSCST_Portal.Personal_Ledger_Manager_List" %>




<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- DataTables -->
    <link rel="stylesheet" href="assets/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">


    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Personal Ledger List</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="Dashboard.aspx">Home</a></li>
                        <li class="breadcrumb-item active">Personal Ledger List</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">


                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">List All Personal Ledger</h3>
                            <div style="float: right;"><a href="Personal_Ledger_Search.aspx" class="btn btn-dark">Back to Search</a></div>
                        </div>
                        <!-- /.card-header -->
                        <div class="card-body">
                            <div id="DIV_District" style="display:none;">

                            <div class="row">
                                <div class="form-group row">
                                    <label for="inputEmail" class="col-sm-12 col-form-label">District Offices</label>
                                    <div class="col-sm-9">
                                        <select class="form-control" id="dropOffices">
                                           


                                        </select>
                                    </div>
                                </div>
                            </div>

                                
                            </div>
                            <table id="AgencyList" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Sl No</th>
                                        <th>Old Loan NO</th>
                                        <th>New Loan NO</th>
                                        <th>Fund</th>
                                        <th>Loanee Name</th>
                                        <th>House Name</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="tblBody">
                                </tbody>
                            </table>
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->


    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables  & Plugins -->
    <%--  <script src="assets/plugins/datatables/jquery.dataTables.min.js"></script>
    <script src="assets/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="assets/plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
    <script src="assets/plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
    <script src="assets/plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
    <script src="assets/plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>--%>

    <script src="assets/plugins/jszip/jszip.min.js"></script>
    <script src="assets/plugins/pdfmake/pdfmake.min.js"></script>
    <script src="assets/plugins/pdfmake/vfs_fonts.js"></script>

    <script src="assets/plugins/datatables/jquery.dataTables.min.js"></script>
    <script src="assets/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="assets/plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
    <script src="assets/plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
    <script src="assets/plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
    <script src="assets/plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <!-- Page specific script -->
    <script>

        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }


        $(function () {
            if (<%=Session["User_Id"] %>== "6") {
                $("#DIV_District").css("display", "block");

            }
            else {
                $("#DIV_District").css("display", "none");
            }
            Load_All_District_Offices();

           

            var table =$('#AgencyList').DataTable({
                "responsive": true, "lengthChange": false, "autoWidth": false,
                "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"],
                "processing": true,
                "serverSide": true,
                "ajax": {
                    "url": "WebService.asmx/Select_All_Personal_Ledger", // Replace with your server-side API URL
                    "type": "POST",
                    "data": function (data) {
                        //  var jsonObject = JSON.parse(jsonData);

                        // Add the orderColumn parameter to the data being sent to the server
                        // Add the required parameters to the DataTables AJAX request


                        data.draw = data.draw || 1; // Example: Default value for draw parameter
                        data.start = data.start || 0;
                        data.length = data.length || 10;
                        data.orderColumn = 'SerialNumber';
                        data.orderDirection = 'asc';
                        data.Old_Loan_No = getParameterByName("Old_Loan_No");
                        data.Loan_No = getParameterByName("Loan_No");
                        data.Loanee_Name = getParameterByName("Loanee_Name");
                        data.House_Name = getParameterByName("House_Name");
                        data.Mobile_No = getParameterByName("Mobile_No");

                        data.search = $('#AgencyList_filter label input').val();//{ value: $('#AgencyList_filter label input').val() }; // Send the search value to the server
                        //     alert($('#AgencyList_filter label input').val());

                        // Log the modified data object to the console
                        console.log(data);
                        // var jsonData = JSON.stringify(data);

                        // Return the modified data object
                        return data;
                    }
                },
                "columns": [
                    { "data": "SerialNumber" },
                    { "data": "vchr_oldno" },
                    { "data": "int_loanno" },
                    { "data": "vchr_fund" },
                    { "data": "vchr_applname" },
                    { "data": "vchr_hsename" },
                  
                    { "data": "chr_status" },
                    {
                        data: null,
                        title: 'Actions',
                        render: function (data, type, row) {
                            return '<button data-name="' + data.vchr_applname + '" data-loan-no="' + data.int_loanno + '" onclick="View(this);" type="button" class="btn btn-block btn-success">View</button>';
                            // Replace "editData" with your function to handle the button click event
                            // "data.ID" is the ID of the row, adjust this based on your data structure
                        }
                    }
                ]
            });


            $("#dropOffices").change(function () {
                var Office_Code = $('option:selected', this).val();
                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/Set_Office_Id_Session",
                    data: JSON.stringify({ Office_Code: Office_Code}), // If you have parameters
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        

                    },
                    error: function (error) {


                        // alert("error" + error.responseText);
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Something went wrong!',

                        })
                    }
                }).done(function () {

                  //  location.reload();

                    table.destroy();
                    table=  $('#AgencyList').DataTable({
                        "responsive": true, "lengthChange": false, "autoWidth": false,
                        "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"],
                        "processing": true,
                        "serverSide": true,
                        "ajax": {
                            "url": "WebService.asmx/Select_All_Personal_Ledger", // Replace with your server-side API URL
                            "type": "POST",
                            "data": function (data) {
                                //  var jsonObject = JSON.parse(jsonData);

                                // Add the orderColumn parameter to the data being sent to the server
                                // Add the required parameters to the DataTables AJAX request


                                data.draw = data.draw || 1; // Example: Default value for draw parameter
                                data.start = data.start || 0;
                                data.length = data.length || 10;
                                data.orderColumn = 'SerialNumber';
                                data.orderDirection = 'asc';
                                data.Old_Loan_No = getParameterByName("Old_Loan_No");
                                data.Loan_No = getParameterByName("Loan_No");
                                data.Loanee_Name = getParameterByName("Loanee_Name");
                                data.House_Name = getParameterByName("House_Name");
                                data.Mobile_No = getParameterByName("Mobile_No");

                                data.search = $('#AgencyList_filter label input').val();//{ value: $('#AgencyList_filter label input').val() }; // Send the search value to the server
                                //     alert($('#AgencyList_filter label input').val());

                                // Log the modified data object to the console
                                console.log(data);
                                // var jsonData = JSON.stringify(data);

                                // Return the modified data object
                                return data;
                            }
                        },
                        "columns": [
                            { "data": "SerialNumber" },
                            { "data": "int_loanno" },
                            { "data": "vchr_fund" },
                            { "data": "vchr_applname" },
                            { "data": "vchr_hsename" },
                            { "data": "vchr_oldno" },
                            { "data": "chr_status" },
                            {
                                data: null,
                                title: 'Actions',
                                render: function (data, type, row) {
                                    return '<button data-name="' + data.vchr_applname + '" data-loan-no="' + data.int_loanno + '" onclick="View(this);" type="button" class="btn btn-block btn-success">View</button>';
                                    // Replace "editData" with your function to handle the button click event
                                    // "data.ID" is the ID of the row, adjust this based on your data structure
                                }
                            }
                        ]
                    });

                });
            });

        });

      
        function Load_Personal_Ledger() {

        }
        function Load_All_District_Offices() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Offices",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropOffices').empty();
                    $('#dropOffices').append('<option value="17">All</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Office_Name = value.Office_Name;
                        var District_Id = value.District_Id;
                        var ShortCode = value.ShortCode;
                        var Office_Code = value.Office_Code;

                        var html = '<option value="' + Office_Code +'">' + Office_Name + '</option>';
                        $('#dropOffices').append(html);
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });

        }


        function Load_Personal_Ledger() {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Personal_Ledger",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {

                        var int_loanappid = value.int_loanappid;
                        var SerialNumber = value.SerialNumber;
                        var int_loanno = value.int_loanno;
                        var vchr_fund = value.vchr_fund;
                        var vchr_applname = value.vchr_applname;
                        var vchr_hsename = value.vchr_hsename;
                        var vchr_oldno = value.vchr_oldno;
                        var chr_status = value.chr_status;

                        var html = '<tr>' +
                            '    <td>' + SerialNumber + '</td>' +
                            '    <td>' + int_loanno + '</td>' +
                            '    <td>' + vchr_fund + '</td>' +
                            '    <td>' + vchr_applname + '</td>' +
                            '    <td>' + vchr_hsename + '</td>' +
                            '    <td>' + vchr_oldno + '</td>' +
                            '    <td>' + chr_status + '</td>' +
                            '    <td>' +
                            '       <button data-loan-no="' + int_loanno + '" onclick="View(this);" type="button" class="btn btn-block btn-success">View</button>' +

                            '    </td>' +
                            '</tr>';
                        $('#tblBody').append(html);
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                $("#AgencyList").DataTable({
                    "responsive": true, "lengthChange": false, "autoWidth": false,
                    "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"]
                }).buttons().container().appendTo('#AgencyList .col-md-6:eq(0)');


            });
        }

        function View(This_Val) {
            // alert($(This_Val).attr("data-loan-no"));
            location.href = 'Personal_Ledger_Manager_View.aspx?loan_no=' + $(This_Val).attr("data-loan-no") + '&name=' + $(This_Val).attr("data-name");
        }

        function Delete(This_Val) {
            $.ajax({
                type: "POST", // or "GET" depending on your web method
                url: "WebService.asmx/Agency_Delete",
                data: JSON.stringify({ Id: $(This_Val).attr("data-Id") }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (response) {

                    // Handle the successful response from the web method
                    console.log(response.d); // "d" is the default property name for the response
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Agency Successfully Deleted !',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked

                            location.href = 'Agency_List.aspx';
                            // Your code here
                        }
                    });

                },
                error: function (xhr, status, error) {
                    // Handle the error response

                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                },
                done: function (response) {
                    // Handle the error response
                    alert(response);

                }
            });
        }
    </script>
</asp:Content>

