﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{8900DB7C-2285-48D7-B155-77DD86242862}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>KSDCSCST_Portal</RootNamespace>
    <AssemblyName>KSDCSCST_Portal</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ClosedXML, Version=*********, Culture=neutral, PublicKeyToken=fd1eb21b62ae805b, processorArchitecture=MSIL">
      <HintPath>..\packages\ClosedXML.0.102.3\lib\netstandard2.0\ClosedXML.dll</HintPath>
    </Reference>
    <!-- Crystal Reports assembly references commented out due to missing assemblies -->
    <!--
    <Reference Include="CrystalDecisions.CrystalReports.Design, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL">
      <HintPath>..\packages\CrystalReports.Design.13.0.4003\lib\net40\CrystalDecisions.CrystalReports.Design.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.CrystalReports.Engine">
      <HintPath>..\..\..\..\program files (x86)\sap businessobjects\crystal reports for .net framework 4.0\common\sap businessobjects enterprise xi 4.0\win32_x86\dotnet\CrystalDecisions.CrystalReports.Engine.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.ReportAppServer.ClientDoc">
      <HintPath>..\..\..\..\program files (x86)\sap businessobjects\crystal reports for .net framework 4.0\common\sap businessobjects enterprise xi 4.0\win32_x86\dotnet\CrystalDecisions.ReportAppServer.ClientDoc.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.ReportAppServer.CommLayer">
      <HintPath>..\..\..\..\program files (x86)\sap businessobjects\crystal reports for .net framework 4.0\common\sap businessobjects enterprise xi 4.0\win32_x86\dotnet\CrystalDecisions.ReportAppServer.CommLayer.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.ReportAppServer.CommonControls">
      <HintPath>..\..\..\..\program files (x86)\sap businessobjects\crystal reports for .net framework 4.0\common\sap businessobjects enterprise xi 4.0\win32_x86\dotnet\CrystalDecisions.ReportAppServer.CommonControls.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.ReportAppServer.CommonObjectModel">
      <HintPath>..\..\..\..\program files (x86)\sap businessobjects\crystal reports for .net framework 4.0\common\sap businessobjects enterprise xi 4.0\win32_x86\dotnet\CrystalDecisions.ReportAppServer.CommonObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.ReportAppServer.Controllers">
      <HintPath>..\..\..\..\program files (x86)\sap businessobjects\crystal reports for .net framework 4.0\common\sap businessobjects enterprise xi 4.0\win32_x86\dotnet\CrystalDecisions.ReportAppServer.Controllers.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.ReportAppServer.CubeDefModel">
      <HintPath>..\..\..\..\program files (x86)\sap businessobjects\crystal reports for .net framework 4.0\common\sap businessobjects enterprise xi 4.0\win32_x86\dotnet\CrystalDecisions.ReportAppServer.CubeDefModel.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.ReportAppServer.DataDefModel">
      <HintPath>..\..\..\..\program files (x86)\sap businessobjects\crystal reports for .net framework 4.0\common\sap businessobjects enterprise xi 4.0\win32_x86\dotnet\CrystalDecisions.ReportAppServer.DataDefModel.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.ReportAppServer.DataSetConversion">
      <HintPath>..\..\..\..\program files (x86)\sap businessobjects\crystal reports for .net framework 4.0\common\sap businessobjects enterprise xi 4.0\win32_x86\dotnet\CrystalDecisions.ReportAppServer.DataSetConversion.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.ReportAppServer.ObjectFactory, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL">
      <HintPath>..\packages\CrystalReports.ReportAppServer.ObjectFactory.13.0.4003\lib\net40\CrystalDecisions.ReportAppServer.ObjectFactory.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="CrystalDecisions.ReportAppServer.Prompting, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL">
      <HintPath>..\packages\CrystalReports.ReportAppServer.Prompting.13.0.4003\lib\net40\CrystalDecisions.ReportAppServer.Prompting.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="CrystalDecisions.ReportAppServer.ReportDefModel">
      <HintPath>..\..\..\..\program files (x86)\sap businessobjects\crystal reports for .net framework 4.0\common\sap businessobjects enterprise xi 4.0\win32_x86\dotnet\CrystalDecisions.ReportAppServer.ReportDefModel.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.ReportAppServer.XmlSerialize">
      <HintPath>..\..\..\..\program files (x86)\sap businessobjects\crystal reports for .net framework 4.0\common\sap businessobjects enterprise xi 4.0\win32_x86\dotnet\CrystalDecisions.ReportAppServer.XmlSerialize.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.ReportSource">
      <HintPath>..\..\..\..\program files (x86)\sap businessobjects\crystal reports for .net framework 4.0\common\sap businessobjects enterprise xi 4.0\win32_x86\dotnet\CrystalDecisions.ReportSource.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.Shared">
      <HintPath>..\..\..\..\program files (x86)\sap businessobjects\crystal reports for .net framework 4.0\common\sap businessobjects enterprise xi 4.0\win32_x86\dotnet\CrystalDecisions.Shared.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.Web">
      <HintPath>..\..\..\..\program files (x86)\sap businessobjects\crystal reports for .net framework 4.0\common\sap businessobjects enterprise xi 4.0\win32_x86\dotnet\CrystalDecisions.Web.dll</HintPath>
    </Reference>
    -->
    <Reference Include="DocumentFormat.OpenXml, Version=2.16.0.0, Culture=neutral, PublicKeyToken=8fb06cb64d019a17, processorArchitecture=MSIL">
      <HintPath>..\packages\DocumentFormat.OpenXml.2.16.0\lib\net46\DocumentFormat.OpenXml.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus, Version=7.0.5.0, Culture=neutral, PublicKeyToken=ea159fdaa78159a1, processorArchitecture=MSIL">
      <HintPath>..\packages\EPPlus.7.0.5\lib\net462\EPPlus.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus.Interfaces, Version=6.1.1.0, Culture=neutral, PublicKeyToken=a694d7f3b0907a61, processorArchitecture=MSIL">
      <HintPath>..\packages\EPPlus.Interfaces.6.1.1\lib\net462\EPPlus.Interfaces.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus.System.Drawing, Version=6.1.1.0, Culture=neutral, PublicKeyToken=2308d35469c9bac0, processorArchitecture=MSIL">
      <HintPath>..\packages\EPPlus.System.Drawing.6.1.1\lib\net462\EPPlus.System.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="ExcelNumberFormat, Version=*******, Culture=neutral, PublicKeyToken=23c6f5d73be07eca, processorArchitecture=MSIL">
      <HintPath>..\packages\ExcelNumberFormat.1.1.0\lib\net20\ExcelNumberFormat.dll</HintPath>
    </Reference>
    <Reference Include="Irony, Version=1.0.11.0, Culture=neutral, PublicKeyToken=ca48ace7223ead47, processorArchitecture=MSIL">
      <HintPath>..\packages\Irony.NetCore.1.0.11\lib\net461\Irony.dll</HintPath>
    </Reference>
    <Reference Include="KSDC, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\KSDC\bin\Debug\KSDC.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=2.0.12.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.12\lib\net45\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ApplicationBlocks.Data">
      <HintPath>..\KSDC\Microsoft.ApplicationBlocks.Data.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.IO.RecyclableMemoryStream, Version=1.4.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IO.RecyclableMemoryStream.1.4.1\lib\net46\Microsoft.IO.RecyclableMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="SixLabors.Fonts, Version=*******, Culture=neutral, PublicKeyToken=d998eea7b14cab13, processorArchitecture=MSIL">
      <HintPath>..\packages\SixLabors.Fonts.1.0.0\lib\netstandard2.0\SixLabors.Fonts.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Annotations.5.0.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.IO, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Packaging, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Packaging.6.0.0\lib\net461\System.IO.Packaging.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.4\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.0\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.4.7.0\lib\netstandard2.0\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.1\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Cng, Version=5.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Cng.5.0.0\lib\net47\System.Security.Cryptography.Cng.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.RegularExpressions.4.3.1\lib\net463\System.Text.RegularExpressions.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="AspNet.ScriptManager.bootstrap">
      <HintPath>..\packages\AspNet.ScriptManager.bootstrap.3.4.1\lib\net45\AspNet.ScriptManager.bootstrap.dll</HintPath>
    </Reference>
    <Reference Include="AspNet.ScriptManager.jQuery">
      <HintPath>..\packages\AspNet.ScriptManager.jQuery.3.4.1\lib\net45\AspNet.ScriptManager.jQuery.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ScriptManager.MSAjax">
      <HintPath>..\packages\Microsoft.AspNet.ScriptManager.MSAjax.5.0.0\lib\net45\Microsoft.ScriptManager.MSAjax.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ScriptManager.WebForms">
      <HintPath>..\packages\Microsoft.AspNet.ScriptManager.WebForms.5.0.0\lib\net45\Microsoft.ScriptManager.WebForms.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Optimization, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>..\packages\Newtonsoft.Json.12.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime">
      <Private>True</Private>
      <HintPath>..\packages\Antlr.*******\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Web.Optimization.WebForms">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.WebForms.1.1.3\lib\net45\Microsoft.AspNet.Web.Optimization.WebForms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.FriendlyUrls">
      <HintPath>..\packages\Microsoft.AspNet.FriendlyUrls.Core.1.0.2\lib\net45\Microsoft.AspNet.FriendlyUrls.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="XLParser, Version=1.5.2.0, Culture=neutral, PublicKeyToken=63397e1e46bb91b4, processorArchitecture=MSIL">
      <HintPath>..\packages\XLParser.1.5.2\lib\net461\XLParser.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Agency_Add.aspx" />
    <Content Include="Agency_Edit.aspx" />
    <Content Include="Agency_List.aspx" />
    <Content Include="ApplicationDataEntry_List.aspx" />
    <Content Include="ApplicationDataEntry_Modification_List.aspx" />
    <Content Include="ApplicationDataEntry_Modification_View.aspx" />
    <Content Include="ApplicationDataEntry_Approval_List.aspx" />
    <Content Include="ApplicationDataEntry_Approval_Rejected_List.aspx" />
    <Content Include="ApplicationDataEntry_Approval_Rejected_View.aspx" />
    <Content Include="ApplicationDataEntry_Approval_View.aspx" />
    <Content Include="ApplicationDataEntry_Verification_List.aspx" />
    <Content Include="ApplicationDataEntry_Verification_View.aspx" />
    <Content Include="ApplicationDataEntry_View.aspx" />
    <Content Include="ApplicationHold_List.aspx" />
    <Content Include="ApplicationIssue_Add.aspx" />
    <Content Include="ApplicationIssue_Edit.aspx" />
    <Content Include="ApplicationIssue_List.aspx" />
    <Content Include="ApplicationIssue_Print.aspx" />
    <Content Include="ApplicationIssue_View.aspx" />
    <Content Include="ApplicationReceipt_List.aspx" />
    <Content Include="ApplicationReceipt_View.aspx" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_cs.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_da.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_de.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_en.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_es.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_fi.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_fr.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_hu.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_it.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_ja.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_ko.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_nb.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_nl.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_pl.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_pt.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_ru.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_sk.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_sv.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_th.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_tr.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_zh_CN.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\ActiveXControls\PrintControl_res_zh_TW.cab" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allInOne.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_cs.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_da.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_de.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_en.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_es.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_fi.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_fr.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_hu.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_it.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_ja.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_ko.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_nb.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_nl.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_pl.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_pt.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_ru.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_sk.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_sv.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_th.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_tr.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_zh_CN.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\allStrings_zh_TW.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\css\cedefault.css" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\css\default.css" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\css\exception.css" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\css\prompting.css" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\html\calendar.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\html\calendarbottom.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\html\calendartop.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\buttonl.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\buttonm.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\buttonr.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortActive100.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortActive150.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortActive200.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortActive250.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortActive300.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortActive350.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortActive400.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortActive50.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortHover100.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortHover150.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortHover200.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortHover250.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortHover300.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortHover350.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortHover400.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortHover50.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortInactive100.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortInactive150.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortInactive200.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortInactive250.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortInactive300.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortInactive400.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\DownArrowSortInactive50.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortActive100.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortActive150.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortActive200.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortActive250.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortActive300.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortActive350.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortActive400.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortActive50.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortHover100.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortHover150.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortHover200.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortHover250.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortHover300.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortHover350.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortHover400.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortHover50.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortInactive100.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortInactive150.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortInactive200.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortInactive250.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortInactive300.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortInactive400.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\images\isort\UpArrowSortInactive50.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\calendar.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\calendar_param.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewerinclude.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\api.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\ArgumentNormalizer.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\bobjcallback.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\ButtonList.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\Calendar.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\Colors.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\common.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\crv.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\Dialogs.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\dom.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\encoding.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\event.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\GroupTree.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\GroupTreeListener.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\GroupTreeNode.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\html.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\ImageSprites.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\allInOne.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\allInOneBG.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\arrow_button_depressed.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\arrow_button_hover.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\breadcrumbSep.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\calendar.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\catalyst.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\clearValues.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\clear_x.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\closePanel.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\delete.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\drill_cursor.cur" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\export.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\filledCircle.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\first.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\grouptree_toggle.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\hollowCircle.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\last.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\leftTriangle.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\line.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\logo.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\magnify.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\min.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\next.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\open_parameter_arrow.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\panelHeaderBG.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\panelNavigatorItemHL.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\panelNavigatorItemSelectedBG.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\panel_toggle.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\param_datafetching.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\param_dirty.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\param_info.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\param_run.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\param_warning.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\plus.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\prev.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\print.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\refresh.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\rightTriangle.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\search.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\separator.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\StackedTab.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\style.css" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\toolbar_background.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\toolbar_buttongroup_background.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\toolbar_buttongroup_left.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\toolbar_buttongroup_right.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\toolbar_button_depressed.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\toolbar_button_hover.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\toolbar_menuarrow_depressed.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\toolbar_menuarrow_hover.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\toolbar_pagenav_buttons.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\toolbar_separator_icon.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\undo.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\up.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\images\WarningPopupTriangle.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\initDhtmlLib.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\IOAdapters.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\LeftPanel.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\OptionalParameterUI.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\OptionalParameterValueRow.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\PanelHeader.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\PanelNavigator.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\PanelNavigatorItem.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\Parameter.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\ParameterController.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\ParameterDialog.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\ParameterInfoRow.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\ParameterPanel.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\ParameterPanelToolbar.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\ParameterUI.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\ParameterValueRow.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\PromptPage.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\RangeField.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\ReportAlbum.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\ReportPage.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\ReportView.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\Separator.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\SharedWidgetHolder.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\SignalDisposer.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\StackedPanel.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\StackedTab.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\StateManager.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\Statusbar.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_cs.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_da.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_de.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_en.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_es.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_fi.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_fr.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_hu.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_it.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_ja.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_ko.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_nb.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_nl.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_pl.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_pt.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_ru.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_sk.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_sv.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_th.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_tr.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_zh_CN.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\strings_zh_TW.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\TextCombo.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\TextField.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\Toolbar.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\ToolPanel.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\Viewer.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\ViewerFlexParameterAdapter.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\ViewerListener.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\crviewer\WarningPopup.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\bolist.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\calendar.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\dialog.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\dom.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\empty.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\borders.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\help.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\loading.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\lov.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\min.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\plus.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\print.css" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\print_fe.css" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\prompt.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\resizepattern.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\bolist.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\button.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\buttonIcons.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\critical_icon.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\dialogelements.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\dialogframe.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\dialogframeleftright.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\dialogframetopbottom.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\dialogtitle.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\down.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\iconsep.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\information_icon.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\left.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\menus.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\node.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\progress.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\resize.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\right.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\scroll_icon.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\sep.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\sep_solid.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\style.css" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\style.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\style_fe.css" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\tabs.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\up.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\wait01.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\wait02.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\warning_icon.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\bolist.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\button.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\buttonIcons.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\critical_icon.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\dialogelements.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\dialogframe.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\dialogframeleftright.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\dialogframetopbottom.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\dialogtitle.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\down.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\iconsep.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\information_icon.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\left.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\menus.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\node.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\progress.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\resize.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\right.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\scroll_icon.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\sep.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\sep_solid.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\style.css" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\style.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\style_fe.css" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\tabs.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\up.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\wait01.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\wait02.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_corporate\warning_icon.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\bolist.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\button.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\buttonIcons.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\critical_icon.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\dialogelements.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\dialogframe.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\dialogframeleftright.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\dialogframetopbottom.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\dialogtitle.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\down.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\iconsep.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\information_icon.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\left.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\menus.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\node.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\progress.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\resize.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\right.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\scroll_icon.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\sep.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\sep_solid.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\style.css" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\style.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\style_fe.css" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\tabs.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\up.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\wait01.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\wait02.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_default\warning_icon.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\background_field.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\background_menusel.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\bolist.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\button.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\buttonIcons.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\critical_icon.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\critical_icon.png" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\dialogelements.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\dialogframe.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\dialogframeleftright.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\dialogframetopbottom.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\dialogtitle.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\down.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\hgrab.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\horiz_tabs.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\iconsep.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\information_icon.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\left.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\menuitem_check.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\menus.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\node.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\panemenu.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\panetitle.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\progress.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\resize.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\right.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\scroll_icon.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\sep.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\sep_solid.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\style.css" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\style.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\style_fe.css" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\tabs.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\toolbar_elements.png" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\up.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\vgrab.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\wait01.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\wait02.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\skin_standard\warning_icon.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\swap.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\transp.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\images\tree.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\cs\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\da\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\de\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\en\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\es\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\fi\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\fr\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\hu\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\it\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\ja\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\ko\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\nb\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\nl\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\pl\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\pt\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\ru\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\sk\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\sv\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\th\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\tr\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\zh_CN\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\language\zh_TW\labels.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\lov.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\menu.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\palette.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\prompttree.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\psheet.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\andorwidget.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\basicDialog.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\blockWhileWait.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\bolist.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\borderswidget.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\buttons.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\calendar.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\checktree.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\default.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\dim.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\dim_close.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\dim_o.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\dim_open.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\empty.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\eventsDialog.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\fieldswidget.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\format.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\framezone.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\grabber.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\iconlistpopupwidget.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\iconscrollmenuwidget.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\imgtabs.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\inconwidget.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\infowidget.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\lov.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\measure.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\measure_o.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\menu.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\navigation.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\palettes.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\pane.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\parent_dialog.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\progressBar.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\prompttree.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\qualif.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\scrolltabs.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\simplelist.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\skinIntl.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\tabs.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\textcombowidget.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\tooltipwidget.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\treeicons.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\treeview.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\unv_close.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\unv_o.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\unv_open.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\samples\waitDialog.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\treeview.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\validator\default.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\validator\validator_res.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dhtmllib\waitdialog.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\dt_param.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\external\date.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\FlexParameterBridge.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\KeyDownEvent.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\log4javascript\log4javascript.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\log4javascript\log4javascript_lite.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\log4javascript\log4javascript_lite_uncompressed.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\log4javascript\log4javascript_production.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\log4javascript\log4javascript_production_uncompressed.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\log4javascript\log4javascript_uncompressed.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\3rdParty.txt" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\Async.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\Base.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\Color.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\Controls.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\DateTime.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\DOM.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\DragAndDrop.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\Format.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\Iter.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\License.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\Logging.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\LoggingPane.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\MochiKit.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\MockDOM.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\New.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\Signal.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\Sortable.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\Style.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\Test.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\Visual.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\MochiKit\__package__.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\previewerror.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\prompts.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\prompts_param.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\js\swfobject\swfobject.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\parameterUIController-compressed.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\processindicator.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\promptengine-compressed.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\css\promptengine2.css" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\css\promptengine_default.css" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\html\calendar.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\html\calendarbottom.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\html\calendartop.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\addallfield.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\addallfield_over.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\addfield.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\addfield_over.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\back.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\background_field.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\back_over.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\button.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\button_left_normal.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\button_middle_normal.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\button_right_normal.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\calendar.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\cfilter.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\cfilter_over.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\dialogelements.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\dialogtitle.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\error.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\errormsg.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\filter.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\filter_over.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\forward.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\forward_over.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\info.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\infomsg.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\minus.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\next.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\next_over.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\plus.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\prev.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\prev_over.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\resize.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\transp.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\warning.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\images\warningmsg.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\initDhtmlLib.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_calendar.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_calendar2.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_prompts.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_prompts2.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_cs.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_da.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_de.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_en.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_es.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_fi.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_fr.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_hu.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_it.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_ja.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_ko.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_nb.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_nl.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_pl.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_pt.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_ru.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_sk.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_sv.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_th.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_tr.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_zh_CN.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\prompting\js\promptengine_strings_zh_TW.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\urlreporting\activexviewer.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\crystalreportviewers13\urlreporting\appletviewer.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\css\default.css" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\html\calendar.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\html\calendarbottom.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\html\calendartop.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\html\crystalexportdialog.htm" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\html\crystalprinthost.html" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\calendar.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\crlogo.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\export.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\exportd.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\export_over.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\First.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\Firstd.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\first_over.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\gotopage.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\gotopaged.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\gotopage_over.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\grouptree.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\grouptreed.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\grouptreepressed.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\grouptree_over.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\Last.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\Lastd.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\last_over.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\Next.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\Nextd.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\next_over.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\Prev.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\Prevd.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\prev_over.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\print.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\printd.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\print_over.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\Refresh.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\refreshd.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\refresh_over.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\Search.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\searchd.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\search_over.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\up.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\upd.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\toolbar\up_over.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\tree\begindots.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\tree\beginminus.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\tree\beginplus.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\tree\blank.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\tree\blankdots.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\tree\dots.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\tree\lastdots.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\tree\lastminus.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\tree\lastplus.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\tree\Magnify.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\tree\minus.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\tree\minusbox.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\tree\plus.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\tree\plusbox.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\tree\singleminus.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\images\tree\singleplus.gif" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\js\calendar_param.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\js\dt_param.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\js\export.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\js\KeyDownEvent.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\js\print.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\js\prompts_param.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\js\strings_chs.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\js\strings_cht.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\js\strings_de.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\js\strings_en.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\js\strings_es.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\js\strings_fr.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\js\strings_it.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\js\strings_ja.js" />
    <Content Include="aspnet_client\system_web\2_0_50727\CrystalReportWebFormViewer4\js\strings_ko.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_cs.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_da.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_de.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_en.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_es.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_fi.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_fr.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_hu.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_it.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_ja.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_ko.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_nb.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_nl.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_pl.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_pt.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_ru.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_sk.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_sv.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_th.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_tr.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_zh_CN.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\ActiveXControls\PrintControl_res_zh_TW.cab" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allInOne.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_cs.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_da.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_de.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_en.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_es.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_fi.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_fr.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_hu.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_it.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_ja.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_ko.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_nb.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_nl.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_pl.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_pt.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_ru.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_sk.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_sv.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_th.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_tr.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_zh_CN.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\allStrings_zh_TW.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\css\cedefault.css" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\css\default.css" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\css\exception.css" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\css\prompting.css" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\html\calendar.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\html\calendarbottom.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\html\calendartop.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\buttonl.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\buttonm.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\buttonr.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortActive100.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortActive150.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortActive200.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortActive250.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortActive300.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortActive350.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortActive400.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortActive50.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortHover100.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortHover150.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortHover200.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortHover250.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortHover300.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortHover350.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortHover400.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortHover50.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortInactive100.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortInactive150.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortInactive200.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortInactive250.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortInactive300.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortInactive400.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\DownArrowSortInactive50.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortActive100.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortActive150.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortActive200.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortActive250.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortActive300.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortActive350.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortActive400.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortActive50.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortHover100.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortHover150.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortHover200.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortHover250.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortHover300.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortHover350.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortHover400.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortHover50.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortInactive100.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortInactive150.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortInactive200.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortInactive250.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortInactive300.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortInactive400.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\images\isort\UpArrowSortInactive50.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\calendar.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\calendar_param.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewerinclude.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\api.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\ArgumentNormalizer.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\bobjcallback.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\ButtonList.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\Calendar.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\Colors.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\common.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\crv.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\Dialogs.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\dom.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\encoding.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\event.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\GroupTree.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\GroupTreeListener.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\GroupTreeNode.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\html.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\ImageSprites.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\allInOne.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\allInOneBG.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\arrow_button_depressed.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\arrow_button_hover.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\breadcrumbSep.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\calendar.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\catalyst.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\clearValues.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\clear_x.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\closePanel.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\delete.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\drill_cursor.cur" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\export.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\filledCircle.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\first.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\grouptree_toggle.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\hollowCircle.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\last.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\leftTriangle.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\line.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\logo.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\magnify.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\min.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\next.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\open_parameter_arrow.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\panelHeaderBG.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\panelNavigatorItemHL.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\panelNavigatorItemSelectedBG.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\panel_toggle.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\param_datafetching.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\param_dirty.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\param_info.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\param_run.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\param_warning.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\plus.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\prev.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\print.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\refresh.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\rightTriangle.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\search.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\separator.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\StackedTab.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\style.css" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\toolbar_background.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\toolbar_buttongroup_background.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\toolbar_buttongroup_left.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\toolbar_buttongroup_right.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\toolbar_button_depressed.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\toolbar_button_hover.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\toolbar_menuarrow_depressed.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\toolbar_menuarrow_hover.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\toolbar_pagenav_buttons.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\toolbar_separator_icon.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\undo.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\up.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\images\WarningPopupTriangle.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\initDhtmlLib.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\IOAdapters.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\LeftPanel.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\OptionalParameterUI.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\OptionalParameterValueRow.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\PanelHeader.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\PanelNavigator.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\PanelNavigatorItem.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\Parameter.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\ParameterController.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\ParameterDialog.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\ParameterInfoRow.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\ParameterPanel.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\ParameterPanelToolbar.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\ParameterUI.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\ParameterValueRow.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\PromptPage.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\RangeField.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\ReportAlbum.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\ReportPage.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\ReportView.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\Separator.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\SharedWidgetHolder.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\SignalDisposer.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\StackedPanel.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\StackedTab.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\StateManager.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\Statusbar.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_cs.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_da.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_de.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_en.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_es.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_fi.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_fr.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_hu.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_it.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_ja.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_ko.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_nb.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_nl.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_pl.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_pt.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_ru.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_sk.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_sv.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_th.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_tr.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_zh_CN.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\strings_zh_TW.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\TextCombo.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\TextField.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\Toolbar.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\ToolPanel.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\Viewer.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\ViewerFlexParameterAdapter.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\ViewerListener.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\crviewer\WarningPopup.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\bolist.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\calendar.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\dialog.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\dom.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\empty.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\borders.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\help.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\loading.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\lov.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\min.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\plus.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\print.css" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\print_fe.css" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\prompt.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\resizepattern.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\bolist.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\button.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\buttonIcons.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\critical_icon.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\dialogelements.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\dialogframe.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\dialogframeleftright.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\dialogframetopbottom.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\dialogtitle.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\down.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\iconsep.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\information_icon.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\left.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\menus.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\node.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\progress.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\resize.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\right.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\scroll_icon.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\sep.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\sep_solid.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\style.css" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\style.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\style_fe.css" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\tabs.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\up.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\wait01.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\wait02.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_coloredline\warning_icon.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\bolist.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\button.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\buttonIcons.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\critical_icon.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\dialogelements.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\dialogframe.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\dialogframeleftright.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\dialogframetopbottom.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\dialogtitle.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\down.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\iconsep.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\information_icon.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\left.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\menus.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\node.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\progress.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\resize.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\right.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\scroll_icon.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\sep.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\sep_solid.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\style.css" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\style.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\style_fe.css" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\tabs.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\up.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\wait01.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\wait02.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_corporate\warning_icon.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\bolist.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\button.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\buttonIcons.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\critical_icon.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\dialogelements.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\dialogframe.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\dialogframeleftright.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\dialogframetopbottom.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\dialogtitle.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\down.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\iconsep.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\information_icon.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\left.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\menus.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\node.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\progress.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\resize.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\right.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\scroll_icon.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\sep.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\sep_solid.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\style.css" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\style.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\style_fe.css" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\tabs.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\up.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\wait01.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\wait02.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_default\warning_icon.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\background_field.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\background_menusel.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\bolist.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\button.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\buttonIcons.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\critical_icon.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\critical_icon.png" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\dialogelements.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\dialogframe.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\dialogframeleftright.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\dialogframetopbottom.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\dialogtitle.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\down.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\hgrab.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\horiz_tabs.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\iconsep.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\information_icon.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\left.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\menuitem_check.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\menus.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\node.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\panemenu.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\panetitle.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\progress.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\resize.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\right.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\scroll_icon.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\sep.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\sep_solid.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\style.css" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\style.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\style_fe.css" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\tabs.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\toolbar_elements.png" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\up.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\vgrab.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\wait01.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\wait02.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\skin_standard\warning_icon.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\swap.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\transp.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\images\tree.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\cs\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\da\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\de\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\en\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\es\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\fi\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\fr\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\hu\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\it\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\ja\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\ko\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\nb\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\nl\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\pl\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\pt\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\ru\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\sk\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\sv\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\th\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\tr\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\zh_CN\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\language\zh_TW\labels.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\lov.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\menu.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\palette.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\prompttree.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\psheet.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\andorwidget.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\basicDialog.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\blockWhileWait.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\bolist.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\borderswidget.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\buttons.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\calendar.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\checktree.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\default.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\dim.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\dim_close.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\dim_o.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\dim_open.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\empty.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\eventsDialog.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\fieldswidget.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\format.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\framezone.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\grabber.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\iconlistpopupwidget.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\iconscrollmenuwidget.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\imgtabs.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\inconwidget.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\infowidget.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\lov.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\measure.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\measure_o.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\menu.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\navigation.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\palettes.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\pane.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\parent_dialog.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\progressBar.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\prompttree.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\qualif.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\scrolltabs.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\simplelist.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\skinIntl.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\tabs.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\textcombowidget.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\tooltipwidget.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\treeicons.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\treeview.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\unv_close.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\unv_o.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\unv_open.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\samples\waitDialog.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\treeview.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\validator\default.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\validator\validator_res.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dhtmllib\waitdialog.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\dt_param.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\external\date.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\FlexParameterBridge.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\KeyDownEvent.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\log4javascript\log4javascript.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\log4javascript\log4javascript_lite.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\log4javascript\log4javascript_lite_uncompressed.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\log4javascript\log4javascript_production.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\log4javascript\log4javascript_production_uncompressed.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\log4javascript\log4javascript_uncompressed.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\3rdParty.txt" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\Async.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\Base.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\Color.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\Controls.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\DateTime.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\DOM.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\DragAndDrop.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\Format.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\Iter.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\License.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\Logging.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\LoggingPane.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\MochiKit.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\MockDOM.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\New.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\Signal.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\Sortable.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\Style.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\Test.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\Visual.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\MochiKit\__package__.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\previewerror.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\prompts.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\prompts_param.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\js\swfobject\swfobject.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\parameterUIController-compressed.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\processindicator.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\promptengine-compressed.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\css\promptengine2.css" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\css\promptengine_default.css" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\html\calendar.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\html\calendarbottom.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\html\calendartop.html" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\addallfield.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\addallfield_over.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\addfield.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\addfield_over.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\back.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\background_field.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\back_over.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\button.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\button_left_normal.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\button_middle_normal.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\button_right_normal.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\calendar.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\cfilter.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\cfilter_over.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\dialogelements.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\dialogtitle.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\error.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\errormsg.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\filter.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\filter_over.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\forward.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\forward_over.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\info.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\infomsg.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\minus.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\next.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\next_over.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\plus.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\prev.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\prev_over.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\resize.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\transp.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\warning.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\images\warningmsg.gif" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\initDhtmlLib.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_calendar.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_calendar2.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_prompts.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_prompts2.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_cs.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_da.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_de.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_en.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_es.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_fi.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_fr.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_hu.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_it.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_ja.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_ko.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_nb.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_nl.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_pl.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_pt.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_ru.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_sk.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_sv.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_th.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_tr.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_zh_CN.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\prompting\js\promptengine_strings_zh_TW.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\urlreporting\activexviewer.js" />
    <Content Include="aspnet_client\system_web\4_0_30319\crystalreportviewers13\urlreporting\appletviewer.js" />
    <Content Include="assets\css\main.css" />
    <Content Include="assets\dist\css\adminlte.css" />
    <Content Include="assets\dist\css\adminlte.min.css" />
    <Content Include="assets\dist\css\alt\adminlte.components.css" />
    <Content Include="assets\dist\css\alt\adminlte.components.min.css" />
    <Content Include="assets\dist\css\alt\adminlte.core.css" />
    <Content Include="assets\dist\css\alt\adminlte.core.min.css" />
    <Content Include="assets\dist\css\alt\adminlte.extra-components.css" />
    <Content Include="assets\dist\css\alt\adminlte.extra-components.min.css" />
    <Content Include="assets\dist\css\alt\adminlte.light.css" />
    <Content Include="assets\dist\css\alt\adminlte.light.min.css" />
    <Content Include="assets\dist\css\alt\adminlte.pages.css" />
    <Content Include="assets\dist\css\alt\adminlte.pages.min.css" />
    <Content Include="assets\dist\css\alt\adminlte.plugins.css" />
    <Content Include="assets\dist\css\alt\adminlte.plugins.min.css" />
    <Content Include="assets\dist\img\AdminLTELogo.png" />
    <Content Include="assets\dist\img\avatar.png" />
    <Content Include="assets\dist\img\avatar2.png" />
    <Content Include="assets\dist\img\avatar3.png" />
    <Content Include="assets\dist\img\avatar4.png" />
    <Content Include="assets\dist\img\avatar5.png" />
    <Content Include="assets\dist\img\credit\american-express.png" />
    <Content Include="assets\dist\img\credit\cirrus.png" />
    <Content Include="assets\dist\img\credit\mastercard.png" />
    <Content Include="assets\dist\img\credit\paypal.png" />
    <Content Include="assets\dist\img\credit\paypal2.png" />
    <Content Include="assets\dist\img\credit\visa.png" />
    <Content Include="assets\dist\img\default-150x150.png" />
    <Content Include="assets\dist\img\icons.png" />
    <Content Include="assets\dist\img\photo1.png" />
    <Content Include="assets\dist\img\photo2.png" />
    <Content Include="assets\dist\img\photo3.jpg" />
    <Content Include="assets\dist\img\photo4.jpg" />
    <Content Include="assets\dist\img\prod-1.jpg" />
    <Content Include="assets\dist\img\prod-2.jpg" />
    <Content Include="assets\dist\img\prod-3.jpg" />
    <Content Include="assets\dist\img\prod-4.jpg" />
    <Content Include="assets\dist\img\prod-5.jpg" />
    <Content Include="assets\dist\img\user1-128x128.jpg" />
    <Content Include="assets\dist\img\user2-160x160.jpg" />
    <Content Include="assets\dist\img\user3-128x128.jpg" />
    <Content Include="assets\dist\img\user4-128x128.jpg" />
    <Content Include="assets\dist\img\user5-128x128.jpg" />
    <Content Include="assets\dist\img\user6-128x128.jpg" />
    <Content Include="assets\dist\img\user7-128x128.jpg" />
    <Content Include="assets\dist\img\user8-128x128.jpg" />
    <Content Include="assets\dist\js\adminlte.js" />
    <Content Include="assets\dist\js\adminlte.min.js" />
    <Content Include="assets\dist\js\demo.js" />
    <Content Include="assets\dist\js\pages\dashboard.js" />
    <Content Include="assets\dist\js\pages\dashboard2.js" />
    <Content Include="assets\dist\js\pages\dashboard3.js" />
    <Content Include="assets\img\check.svg" />
    <Content Include="assets\img\down.svg" />
    <Content Include="assets\img\Excel.jpg" />
    <Content Include="assets\img\fav_192.png" />
    <Content Include="assets\img\fav_32.png" />
    <Content Include="assets\img\Image.jpg" />
    <Content Include="assets\img\logo.png" />
    <Content Include="assets\img\Logo_Black_And_White.jpg" />
    <Content Include="assets\img\PDF.jpg" />
    <Content Include="assets\img\right.svg" />
    <Content Include="assets\img\square-minus.svg" />
    <Content Include="assets\img\square.svg" />
    <Content Include="assets\img\welcome_bg.png" />
    <Content Include="assets\img\Word.jpg" />
    <Content Include="assets\js\jquery.checktree.js" />
    <Content Include="assets\js\jquery.dateandtime.js" />
    <Content Include="assets\plugins\bootstrap-colorpicker\css\bootstrap-colorpicker.css" />
    <Content Include="assets\plugins\bootstrap-colorpicker\css\bootstrap-colorpicker.min.css" />
    <Content Include="assets\plugins\bootstrap-colorpicker\js\bootstrap-colorpicker.js" />
    <Content Include="assets\plugins\bootstrap-colorpicker\js\bootstrap-colorpicker.min.js" />
    <Content Include="assets\plugins\bootstrap-slider\bootstrap-slider.js" />
    <Content Include="assets\plugins\bootstrap-slider\bootstrap-slider.min.js" />
    <Content Include="assets\plugins\bootstrap-slider\css\bootstrap-slider.css" />
    <Content Include="assets\plugins\bootstrap-slider\css\bootstrap-slider.min.css" />
    <Content Include="assets\plugins\bootstrap4-duallistbox\bootstrap-duallistbox.css" />
    <Content Include="assets\plugins\bootstrap4-duallistbox\bootstrap-duallistbox.min.css" />
    <Content Include="assets\plugins\bootstrap4-duallistbox\jquery.bootstrap-duallistbox.js" />
    <Content Include="assets\plugins\bootstrap4-duallistbox\jquery.bootstrap-duallistbox.min.js" />
    <Content Include="assets\plugins\bootstrap\js\bootstrap.bundle.js" />
    <Content Include="assets\plugins\bootstrap\js\bootstrap.bundle.min.js" />
    <Content Include="assets\plugins\bootstrap\js\bootstrap.js" />
    <Content Include="assets\plugins\bootstrap\js\bootstrap.min.js" />
    <Content Include="assets\plugins\bs-custom-file-input\bs-custom-file-input.js" />
    <Content Include="assets\plugins\bs-custom-file-input\bs-custom-file-input.min.js" />
    <Content Include="assets\plugins\bs-stepper\css\bs-stepper.css" />
    <Content Include="assets\plugins\bs-stepper\css\bs-stepper.min.css" />
    <Content Include="assets\plugins\bs-stepper\js\bs-stepper.js" />
    <Content Include="assets\plugins\bs-stepper\js\bs-stepper.min.js" />
    <Content Include="assets\plugins\chart.js\Chart.bundle.js" />
    <Content Include="assets\plugins\chart.js\Chart.bundle.min.js" />
    <Content Include="assets\plugins\chart.js\Chart.css" />
    <Content Include="assets\plugins\chart.js\Chart.js" />
    <Content Include="assets\plugins\chart.js\Chart.min.css" />
    <Content Include="assets\plugins\chart.js\Chart.min.js" />
    <Content Include="assets\plugins\codemirror\addon\comment\comment.js" />
    <Content Include="assets\plugins\codemirror\addon\comment\continuecomment.js" />
    <Content Include="assets\plugins\codemirror\addon\dialog\dialog.css" />
    <Content Include="assets\plugins\codemirror\addon\dialog\dialog.js" />
    <Content Include="assets\plugins\codemirror\addon\display\autorefresh.js" />
    <Content Include="assets\plugins\codemirror\addon\display\fullscreen.css" />
    <Content Include="assets\plugins\codemirror\addon\display\fullscreen.js" />
    <Content Include="assets\plugins\codemirror\addon\display\panel.js" />
    <Content Include="assets\plugins\codemirror\addon\display\placeholder.js" />
    <Content Include="assets\plugins\codemirror\addon\display\rulers.js" />
    <Content Include="assets\plugins\codemirror\addon\edit\closebrackets.js" />
    <Content Include="assets\plugins\codemirror\addon\edit\closetag.js" />
    <Content Include="assets\plugins\codemirror\addon\edit\continuelist.js" />
    <Content Include="assets\plugins\codemirror\addon\edit\matchbrackets.js" />
    <Content Include="assets\plugins\codemirror\addon\edit\matchtags.js" />
    <Content Include="assets\plugins\codemirror\addon\edit\trailingspace.js" />
    <Content Include="assets\plugins\codemirror\addon\fold\brace-fold.js" />
    <Content Include="assets\plugins\codemirror\addon\fold\comment-fold.js" />
    <Content Include="assets\plugins\codemirror\addon\fold\foldcode.js" />
    <Content Include="assets\plugins\codemirror\addon\fold\foldgutter.css" />
    <Content Include="assets\plugins\codemirror\addon\fold\foldgutter.js" />
    <Content Include="assets\plugins\codemirror\addon\fold\indent-fold.js" />
    <Content Include="assets\plugins\codemirror\addon\fold\markdown-fold.js" />
    <Content Include="assets\plugins\codemirror\addon\fold\xml-fold.js" />
    <Content Include="assets\plugins\codemirror\addon\hint\anyword-hint.js" />
    <Content Include="assets\plugins\codemirror\addon\hint\css-hint.js" />
    <Content Include="assets\plugins\codemirror\addon\hint\html-hint.js" />
    <Content Include="assets\plugins\codemirror\addon\hint\javascript-hint.js" />
    <Content Include="assets\plugins\codemirror\addon\hint\show-hint.css" />
    <Content Include="assets\plugins\codemirror\addon\hint\show-hint.js" />
    <Content Include="assets\plugins\codemirror\addon\hint\sql-hint.js" />
    <Content Include="assets\plugins\codemirror\addon\hint\xml-hint.js" />
    <Content Include="assets\plugins\codemirror\addon\lint\coffeescript-lint.js" />
    <Content Include="assets\plugins\codemirror\addon\lint\css-lint.js" />
    <Content Include="assets\plugins\codemirror\addon\lint\html-lint.js" />
    <Content Include="assets\plugins\codemirror\addon\lint\javascript-lint.js" />
    <Content Include="assets\plugins\codemirror\addon\lint\json-lint.js" />
    <Content Include="assets\plugins\codemirror\addon\lint\lint.css" />
    <Content Include="assets\plugins\codemirror\addon\lint\lint.js" />
    <Content Include="assets\plugins\codemirror\addon\lint\yaml-lint.js" />
    <Content Include="assets\plugins\codemirror\addon\merge\merge.css" />
    <Content Include="assets\plugins\codemirror\addon\merge\merge.js" />
    <Content Include="assets\plugins\codemirror\addon\mode\loadmode.js" />
    <Content Include="assets\plugins\codemirror\addon\mode\multiplex.js" />
    <Content Include="assets\plugins\codemirror\addon\mode\multiplex_test.js" />
    <Content Include="assets\plugins\codemirror\addon\mode\overlay.js" />
    <Content Include="assets\plugins\codemirror\addon\mode\simple.js" />
    <Content Include="assets\plugins\codemirror\addon\runmode\colorize.js" />
    <Content Include="assets\plugins\codemirror\addon\runmode\runmode-standalone.js" />
    <Content Include="assets\plugins\codemirror\addon\runmode\runmode.js" />
    <Content Include="assets\plugins\codemirror\addon\runmode\runmode.node.js" />
    <Content Include="assets\plugins\codemirror\addon\scroll\annotatescrollbar.js" />
    <Content Include="assets\plugins\codemirror\addon\scroll\scrollpastend.js" />
    <Content Include="assets\plugins\codemirror\addon\scroll\simplescrollbars.css" />
    <Content Include="assets\plugins\codemirror\addon\scroll\simplescrollbars.js" />
    <Content Include="assets\plugins\codemirror\addon\search\jump-to-line.js" />
    <Content Include="assets\plugins\codemirror\addon\search\match-highlighter.js" />
    <Content Include="assets\plugins\codemirror\addon\search\matchesonscrollbar.css" />
    <Content Include="assets\plugins\codemirror\addon\search\matchesonscrollbar.js" />
    <Content Include="assets\plugins\codemirror\addon\search\search.js" />
    <Content Include="assets\plugins\codemirror\addon\search\searchcursor.js" />
    <Content Include="assets\plugins\codemirror\addon\selection\active-line.js" />
    <Content Include="assets\plugins\codemirror\addon\selection\mark-selection.js" />
    <Content Include="assets\plugins\codemirror\addon\selection\selection-pointer.js" />
    <Content Include="assets\plugins\codemirror\addon\tern\tern.css" />
    <Content Include="assets\plugins\codemirror\addon\tern\tern.js" />
    <Content Include="assets\plugins\codemirror\addon\tern\worker.js" />
    <Content Include="assets\plugins\codemirror\addon\wrap\hardwrap.js" />
    <Content Include="assets\plugins\codemirror\codemirror.css" />
    <Content Include="assets\plugins\codemirror\codemirror.js" />
    <Content Include="assets\plugins\codemirror\keymap\emacs.js" />
    <Content Include="assets\plugins\codemirror\keymap\sublime.js" />
    <Content Include="assets\plugins\codemirror\keymap\vim.js" />
    <Content Include="assets\plugins\codemirror\mode\apl\apl.js" />
    <Content Include="assets\plugins\codemirror\mode\asciiarmor\asciiarmor.js" />
    <Content Include="assets\plugins\codemirror\mode\asn.1\asn.1.js" />
    <Content Include="assets\plugins\codemirror\mode\asterisk\asterisk.js" />
    <Content Include="assets\plugins\codemirror\mode\brainfuck\brainfuck.js" />
    <Content Include="assets\plugins\codemirror\mode\clike\clike.js" />
    <Content Include="assets\plugins\codemirror\mode\clojure\clojure.js" />
    <Content Include="assets\plugins\codemirror\mode\cmake\cmake.js" />
    <Content Include="assets\plugins\codemirror\mode\cobol\cobol.js" />
    <Content Include="assets\plugins\codemirror\mode\coffeescript\coffeescript.js" />
    <Content Include="assets\plugins\codemirror\mode\commonlisp\commonlisp.js" />
    <Content Include="assets\plugins\codemirror\mode\crystal\crystal.js" />
    <Content Include="assets\plugins\codemirror\mode\css\css.js" />
    <Content Include="assets\plugins\codemirror\mode\cypher\cypher.js" />
    <Content Include="assets\plugins\codemirror\mode\dart\dart.js" />
    <Content Include="assets\plugins\codemirror\mode\diff\diff.js" />
    <Content Include="assets\plugins\codemirror\mode\django\django.js" />
    <Content Include="assets\plugins\codemirror\mode\dockerfile\dockerfile.js" />
    <Content Include="assets\plugins\codemirror\mode\dtd\dtd.js" />
    <Content Include="assets\plugins\codemirror\mode\dylan\dylan.js" />
    <Content Include="assets\plugins\codemirror\mode\d\d.js" />
    <Content Include="assets\plugins\codemirror\mode\ebnf\ebnf.js" />
    <Content Include="assets\plugins\codemirror\mode\ecl\ecl.js" />
    <Content Include="assets\plugins\codemirror\mode\eiffel\eiffel.js" />
    <Content Include="assets\plugins\codemirror\mode\elm\elm.js" />
    <Content Include="assets\plugins\codemirror\mode\erlang\erlang.js" />
    <Content Include="assets\plugins\codemirror\mode\factor\factor.js" />
    <Content Include="assets\plugins\codemirror\mode\fcl\fcl.js" />
    <Content Include="assets\plugins\codemirror\mode\forth\forth.js" />
    <Content Include="assets\plugins\codemirror\mode\fortran\fortran.js" />
    <Content Include="assets\plugins\codemirror\mode\gas\gas.js" />
    <Content Include="assets\plugins\codemirror\mode\gfm\gfm.js" />
    <Content Include="assets\plugins\codemirror\mode\gherkin\gherkin.js" />
    <Content Include="assets\plugins\codemirror\mode\go\go.js" />
    <Content Include="assets\plugins\codemirror\mode\groovy\groovy.js" />
    <Content Include="assets\plugins\codemirror\mode\haml\haml.js" />
    <Content Include="assets\plugins\codemirror\mode\handlebars\handlebars.js" />
    <Content Include="assets\plugins\codemirror\mode\haskell-literate\haskell-literate.js" />
    <Content Include="assets\plugins\codemirror\mode\haskell\haskell.js" />
    <Content Include="assets\plugins\codemirror\mode\haxe\haxe.js" />
    <Content Include="assets\plugins\codemirror\mode\htmlembedded\htmlembedded.js" />
    <Content Include="assets\plugins\codemirror\mode\htmlmixed\htmlmixed.js" />
    <Content Include="assets\plugins\codemirror\mode\http\http.js" />
    <Content Include="assets\plugins\codemirror\mode\idl\idl.js" />
    <Content Include="assets\plugins\codemirror\mode\javascript\javascript.js" />
    <Content Include="assets\plugins\codemirror\mode\jinja2\jinja2.js" />
    <Content Include="assets\plugins\codemirror\mode\jsx\jsx.js" />
    <Content Include="assets\plugins\codemirror\mode\julia\julia.js" />
    <Content Include="assets\plugins\codemirror\mode\livescript\livescript.js" />
    <Content Include="assets\plugins\codemirror\mode\lua\lua.js" />
    <Content Include="assets\plugins\codemirror\mode\markdown\markdown.js" />
    <Content Include="assets\plugins\codemirror\mode\mathematica\mathematica.js" />
    <Content Include="assets\plugins\codemirror\mode\mbox\mbox.js" />
    <Content Include="assets\plugins\codemirror\mode\meta.js" />
    <Content Include="assets\plugins\codemirror\mode\mirc\mirc.js" />
    <Content Include="assets\plugins\codemirror\mode\mllike\mllike.js" />
    <Content Include="assets\plugins\codemirror\mode\modelica\modelica.js" />
    <Content Include="assets\plugins\codemirror\mode\mscgen\mscgen.js" />
    <Content Include="assets\plugins\codemirror\mode\mumps\mumps.js" />
    <Content Include="assets\plugins\codemirror\mode\nginx\nginx.js" />
    <Content Include="assets\plugins\codemirror\mode\nsis\nsis.js" />
    <Content Include="assets\plugins\codemirror\mode\ntriples\ntriples.js" />
    <Content Include="assets\plugins\codemirror\mode\octave\octave.js" />
    <Content Include="assets\plugins\codemirror\mode\oz\oz.js" />
    <Content Include="assets\plugins\codemirror\mode\pascal\pascal.js" />
    <Content Include="assets\plugins\codemirror\mode\pegjs\pegjs.js" />
    <Content Include="assets\plugins\codemirror\mode\perl\perl.js" />
    <Content Include="assets\plugins\codemirror\mode\php\php.js" />
    <Content Include="assets\plugins\codemirror\mode\pig\pig.js" />
    <Content Include="assets\plugins\codemirror\mode\powershell\powershell.js" />
    <Content Include="assets\plugins\codemirror\mode\properties\properties.js" />
    <Content Include="assets\plugins\codemirror\mode\protobuf\protobuf.js" />
    <Content Include="assets\plugins\codemirror\mode\pug\pug.js" />
    <Content Include="assets\plugins\codemirror\mode\puppet\puppet.js" />
    <Content Include="assets\plugins\codemirror\mode\python\python.js" />
    <Content Include="assets\plugins\codemirror\mode\q\q.js" />
    <Content Include="assets\plugins\codemirror\mode\rpm\rpm.js" />
    <Content Include="assets\plugins\codemirror\mode\rst\rst.js" />
    <Content Include="assets\plugins\codemirror\mode\ruby\ruby.js" />
    <Content Include="assets\plugins\codemirror\mode\rust\rust.js" />
    <Content Include="assets\plugins\codemirror\mode\r\r.js" />
    <Content Include="assets\plugins\codemirror\mode\sass\sass.js" />
    <Content Include="assets\plugins\codemirror\mode\sas\sas.js" />
    <Content Include="assets\plugins\codemirror\mode\scheme\scheme.js" />
    <Content Include="assets\plugins\codemirror\mode\shell\shell.js" />
    <Content Include="assets\plugins\codemirror\mode\sieve\sieve.js" />
    <Content Include="assets\plugins\codemirror\mode\slim\slim.js" />
    <Content Include="assets\plugins\codemirror\mode\smalltalk\smalltalk.js" />
    <Content Include="assets\plugins\codemirror\mode\smarty\smarty.js" />
    <Content Include="assets\plugins\codemirror\mode\solr\solr.js" />
    <Content Include="assets\plugins\codemirror\mode\soy\soy.js" />
    <Content Include="assets\plugins\codemirror\mode\sparql\sparql.js" />
    <Content Include="assets\plugins\codemirror\mode\spreadsheet\spreadsheet.js" />
    <Content Include="assets\plugins\codemirror\mode\sql\sql.js" />
    <Content Include="assets\plugins\codemirror\mode\stex\stex.js" />
    <Content Include="assets\plugins\codemirror\mode\stylus\stylus.js" />
    <Content Include="assets\plugins\codemirror\mode\swift\swift.js" />
    <Content Include="assets\plugins\codemirror\mode\tcl\tcl.js" />
    <Content Include="assets\plugins\codemirror\mode\textile\textile.js" />
    <Content Include="assets\plugins\codemirror\mode\tiddlywiki\tiddlywiki.css" />
    <Content Include="assets\plugins\codemirror\mode\tiddlywiki\tiddlywiki.js" />
    <Content Include="assets\plugins\codemirror\mode\tiki\tiki.css" />
    <Content Include="assets\plugins\codemirror\mode\tiki\tiki.js" />
    <Content Include="assets\plugins\codemirror\mode\toml\toml.js" />
    <Content Include="assets\plugins\codemirror\mode\tornado\tornado.js" />
    <Content Include="assets\plugins\codemirror\mode\troff\troff.js" />
    <Content Include="assets\plugins\codemirror\mode\ttcn-cfg\ttcn-cfg.js" />
    <Content Include="assets\plugins\codemirror\mode\ttcn\ttcn.js" />
    <Content Include="assets\plugins\codemirror\mode\turtle\turtle.js" />
    <Content Include="assets\plugins\codemirror\mode\twig\twig.js" />
    <Content Include="assets\plugins\codemirror\mode\vbscript\vbscript.js" />
    <Content Include="assets\plugins\codemirror\mode\vb\vb.js" />
    <Content Include="assets\plugins\codemirror\mode\velocity\velocity.js" />
    <Content Include="assets\plugins\codemirror\mode\verilog\verilog.js" />
    <Content Include="assets\plugins\codemirror\mode\vhdl\vhdl.js" />
    <Content Include="assets\plugins\codemirror\mode\vue\vue.js" />
    <Content Include="assets\plugins\codemirror\mode\wast\wast.js" />
    <Content Include="assets\plugins\codemirror\mode\webidl\webidl.js" />
    <Content Include="assets\plugins\codemirror\mode\xml\xml.js" />
    <Content Include="assets\plugins\codemirror\mode\xquery\xquery.js" />
    <Content Include="assets\plugins\codemirror\mode\yacas\yacas.js" />
    <Content Include="assets\plugins\codemirror\mode\yaml-frontmatter\yaml-frontmatter.js" />
    <Content Include="assets\plugins\codemirror\mode\yaml\yaml.js" />
    <Content Include="assets\plugins\codemirror\mode\z80\z80.js" />
    <Content Include="assets\plugins\codemirror\theme\3024-day.css" />
    <Content Include="assets\plugins\codemirror\theme\3024-night.css" />
    <Content Include="assets\plugins\codemirror\theme\abbott.css" />
    <Content Include="assets\plugins\codemirror\theme\abcdef.css" />
    <Content Include="assets\plugins\codemirror\theme\ambiance-mobile.css" />
    <Content Include="assets\plugins\codemirror\theme\ambiance.css" />
    <Content Include="assets\plugins\codemirror\theme\ayu-dark.css" />
    <Content Include="assets\plugins\codemirror\theme\ayu-mirage.css" />
    <Content Include="assets\plugins\codemirror\theme\base16-dark.css" />
    <Content Include="assets\plugins\codemirror\theme\base16-light.css" />
    <Content Include="assets\plugins\codemirror\theme\bespin.css" />
    <Content Include="assets\plugins\codemirror\theme\blackboard.css" />
    <Content Include="assets\plugins\codemirror\theme\cobalt.css" />
    <Content Include="assets\plugins\codemirror\theme\colorforth.css" />
    <Content Include="assets\plugins\codemirror\theme\darcula.css" />
    <Content Include="assets\plugins\codemirror\theme\dracula.css" />
    <Content Include="assets\plugins\codemirror\theme\duotone-dark.css" />
    <Content Include="assets\plugins\codemirror\theme\duotone-light.css" />
    <Content Include="assets\plugins\codemirror\theme\eclipse.css" />
    <Content Include="assets\plugins\codemirror\theme\elegant.css" />
    <Content Include="assets\plugins\codemirror\theme\erlang-dark.css" />
    <Content Include="assets\plugins\codemirror\theme\gruvbox-dark.css" />
    <Content Include="assets\plugins\codemirror\theme\hopscotch.css" />
    <Content Include="assets\plugins\codemirror\theme\icecoder.css" />
    <Content Include="assets\plugins\codemirror\theme\idea.css" />
    <Content Include="assets\plugins\codemirror\theme\isotope.css" />
    <Content Include="assets\plugins\codemirror\theme\juejin.css" />
    <Content Include="assets\plugins\codemirror\theme\lesser-dark.css" />
    <Content Include="assets\plugins\codemirror\theme\liquibyte.css" />
    <Content Include="assets\plugins\codemirror\theme\lucario.css" />
    <Content Include="assets\plugins\codemirror\theme\material-darker.css" />
    <Content Include="assets\plugins\codemirror\theme\material-ocean.css" />
    <Content Include="assets\plugins\codemirror\theme\material-palenight.css" />
    <Content Include="assets\plugins\codemirror\theme\material.css" />
    <Content Include="assets\plugins\codemirror\theme\mbo.css" />
    <Content Include="assets\plugins\codemirror\theme\mdn-like.css" />
    <Content Include="assets\plugins\codemirror\theme\midnight.css" />
    <Content Include="assets\plugins\codemirror\theme\monokai.css" />
    <Content Include="assets\plugins\codemirror\theme\moxer.css" />
    <Content Include="assets\plugins\codemirror\theme\neat.css" />
    <Content Include="assets\plugins\codemirror\theme\neo.css" />
    <Content Include="assets\plugins\codemirror\theme\night.css" />
    <Content Include="assets\plugins\codemirror\theme\nord.css" />
    <Content Include="assets\plugins\codemirror\theme\oceanic-next.css" />
    <Content Include="assets\plugins\codemirror\theme\panda-syntax.css" />
    <Content Include="assets\plugins\codemirror\theme\paraiso-dark.css" />
    <Content Include="assets\plugins\codemirror\theme\paraiso-light.css" />
    <Content Include="assets\plugins\codemirror\theme\pastel-on-dark.css" />
    <Content Include="assets\plugins\codemirror\theme\railscasts.css" />
    <Content Include="assets\plugins\codemirror\theme\rubyblue.css" />
    <Content Include="assets\plugins\codemirror\theme\seti.css" />
    <Content Include="assets\plugins\codemirror\theme\shadowfox.css" />
    <Content Include="assets\plugins\codemirror\theme\solarized.css" />
    <Content Include="assets\plugins\codemirror\theme\ssms.css" />
    <Content Include="assets\plugins\codemirror\theme\the-matrix.css" />
    <Content Include="assets\plugins\codemirror\theme\tomorrow-night-bright.css" />
    <Content Include="assets\plugins\codemirror\theme\tomorrow-night-eighties.css" />
    <Content Include="assets\plugins\codemirror\theme\ttcn.css" />
    <Content Include="assets\plugins\codemirror\theme\twilight.css" />
    <Content Include="assets\plugins\codemirror\theme\vibrant-ink.css" />
    <Content Include="assets\plugins\codemirror\theme\xq-dark.css" />
    <Content Include="assets\plugins\codemirror\theme\xq-light.css" />
    <Content Include="assets\plugins\codemirror\theme\yeti.css" />
    <Content Include="assets\plugins\codemirror\theme\yonce.css" />
    <Content Include="assets\plugins\codemirror\theme\zenburn.css" />
    <Content Include="assets\plugins\datatables-autofill\css\autoFill.bootstrap4.css" />
    <Content Include="assets\plugins\datatables-autofill\css\autoFill.bootstrap4.min.css" />
    <Content Include="assets\plugins\datatables-autofill\js\autoFill.bootstrap4.js" />
    <Content Include="assets\plugins\datatables-autofill\js\autoFill.bootstrap4.min.js" />
    <Content Include="assets\plugins\datatables-autofill\js\dataTables.autoFill.js" />
    <Content Include="assets\plugins\datatables-autofill\js\dataTables.autoFill.min.js" />
    <Content Include="assets\plugins\datatables-bs4\css\dataTables.bootstrap4.css" />
    <Content Include="assets\plugins\datatables-bs4\css\dataTables.bootstrap4.min.css" />
    <Content Include="assets\plugins\datatables-bs4\js\dataTables.bootstrap4.js" />
    <Content Include="assets\plugins\datatables-bs4\js\dataTables.bootstrap4.min.js" />
    <Content Include="assets\plugins\datatables-buttons\css\buttons.bootstrap4.css" />
    <Content Include="assets\plugins\datatables-buttons\css\buttons.bootstrap4.min.css" />
    <Content Include="assets\plugins\datatables-buttons\js\buttons.bootstrap4.js" />
    <Content Include="assets\plugins\datatables-buttons\js\buttons.bootstrap4.min.js" />
    <Content Include="assets\plugins\datatables-buttons\js\buttons.colVis.js" />
    <Content Include="assets\plugins\datatables-buttons\js\buttons.colVis.min.js" />
    <Content Include="assets\plugins\datatables-buttons\js\buttons.flash.js" />
    <Content Include="assets\plugins\datatables-buttons\js\buttons.flash.min.js" />
    <Content Include="assets\plugins\datatables-buttons\js\buttons.html5.js" />
    <Content Include="assets\plugins\datatables-buttons\js\buttons.html5.min.js" />
    <Content Include="assets\plugins\datatables-buttons\js\buttons.print.js" />
    <Content Include="assets\plugins\datatables-buttons\js\buttons.print.min.js" />
    <Content Include="assets\plugins\datatables-buttons\js\dataTables.buttons.js" />
    <Content Include="assets\plugins\datatables-buttons\js\dataTables.buttons.min.js" />
    <Content Include="assets\plugins\datatables-colreorder\css\colReorder.bootstrap4.css" />
    <Content Include="assets\plugins\datatables-colreorder\css\colReorder.bootstrap4.min.css" />
    <Content Include="assets\plugins\datatables-colreorder\js\colReorder.bootstrap4.js" />
    <Content Include="assets\plugins\datatables-colreorder\js\colReorder.bootstrap4.min.js" />
    <Content Include="assets\plugins\datatables-colreorder\js\dataTables.colReorder.js" />
    <Content Include="assets\plugins\datatables-colreorder\js\dataTables.colReorder.min.js" />
    <Content Include="assets\plugins\datatables-fixedcolumns\css\fixedColumns.bootstrap4.css" />
    <Content Include="assets\plugins\datatables-fixedcolumns\css\fixedColumns.bootstrap4.min.css" />
    <Content Include="assets\plugins\datatables-fixedcolumns\js\dataTables.fixedColumns.js" />
    <Content Include="assets\plugins\datatables-fixedcolumns\js\dataTables.fixedColumns.min.js" />
    <Content Include="assets\plugins\datatables-fixedcolumns\js\fixedColumns.bootstrap4.js" />
    <Content Include="assets\plugins\datatables-fixedcolumns\js\fixedColumns.bootstrap4.min.js" />
    <Content Include="assets\plugins\datatables-fixedheader\css\fixedHeader.bootstrap4.css" />
    <Content Include="assets\plugins\datatables-fixedheader\css\fixedHeader.bootstrap4.min.css" />
    <Content Include="assets\plugins\datatables-fixedheader\js\dataTables.fixedHeader.js" />
    <Content Include="assets\plugins\datatables-fixedheader\js\dataTables.fixedHeader.min.js" />
    <Content Include="assets\plugins\datatables-fixedheader\js\fixedHeader.bootstrap4.js" />
    <Content Include="assets\plugins\datatables-fixedheader\js\fixedHeader.bootstrap4.min.js" />
    <Content Include="assets\plugins\datatables-keytable\css\keyTable.bootstrap4.css" />
    <Content Include="assets\plugins\datatables-keytable\css\keyTable.bootstrap4.min.css" />
    <Content Include="assets\plugins\datatables-keytable\js\dataTables.keyTable.js" />
    <Content Include="assets\plugins\datatables-keytable\js\dataTables.keyTable.min.js" />
    <Content Include="assets\plugins\datatables-keytable\js\keyTable.bootstrap4.js" />
    <Content Include="assets\plugins\datatables-keytable\js\keyTable.bootstrap4.min.js" />
    <Content Include="assets\plugins\datatables-responsive\css\responsive.bootstrap4.css" />
    <Content Include="assets\plugins\datatables-responsive\css\responsive.bootstrap4.min.css" />
    <Content Include="assets\plugins\datatables-responsive\js\dataTables.responsive.js" />
    <Content Include="assets\plugins\datatables-responsive\js\dataTables.responsive.min.js" />
    <Content Include="assets\plugins\datatables-responsive\js\responsive.bootstrap4.js" />
    <Content Include="assets\plugins\datatables-responsive\js\responsive.bootstrap4.min.js" />
    <Content Include="assets\plugins\datatables-rowgroup\css\rowGroup.bootstrap4.css" />
    <Content Include="assets\plugins\datatables-rowgroup\css\rowGroup.bootstrap4.min.css" />
    <Content Include="assets\plugins\datatables-rowgroup\js\dataTables.rowGroup.js" />
    <Content Include="assets\plugins\datatables-rowgroup\js\dataTables.rowGroup.min.js" />
    <Content Include="assets\plugins\datatables-rowgroup\js\rowGroup.bootstrap4.js" />
    <Content Include="assets\plugins\datatables-rowgroup\js\rowGroup.bootstrap4.min.js" />
    <Content Include="assets\plugins\datatables-rowreorder\css\rowReorder.bootstrap4.css" />
    <Content Include="assets\plugins\datatables-rowreorder\css\rowReorder.bootstrap4.min.css" />
    <Content Include="assets\plugins\datatables-rowreorder\js\dataTables.rowReorder.js" />
    <Content Include="assets\plugins\datatables-rowreorder\js\dataTables.rowReorder.min.js" />
    <Content Include="assets\plugins\datatables-rowreorder\js\rowReorder.bootstrap4.js" />
    <Content Include="assets\plugins\datatables-rowreorder\js\rowReorder.bootstrap4.min.js" />
    <Content Include="assets\plugins\datatables-scroller\css\scroller.bootstrap4.css" />
    <Content Include="assets\plugins\datatables-scroller\css\scroller.bootstrap4.min.css" />
    <Content Include="assets\plugins\datatables-scroller\js\dataTables.scroller.js" />
    <Content Include="assets\plugins\datatables-scroller\js\dataTables.scroller.min.js" />
    <Content Include="assets\plugins\datatables-scroller\js\scroller.bootstrap4.js" />
    <Content Include="assets\plugins\datatables-scroller\js\scroller.bootstrap4.min.js" />
    <Content Include="assets\plugins\datatables-searchbuilder\css\searchBuilder.bootstrap4.css" />
    <Content Include="assets\plugins\datatables-searchbuilder\css\searchBuilder.bootstrap4.min.css" />
    <Content Include="assets\plugins\datatables-searchbuilder\js\dataTables.searchBuilder.js" />
    <Content Include="assets\plugins\datatables-searchbuilder\js\dataTables.searchBuilder.min.js" />
    <Content Include="assets\plugins\datatables-searchbuilder\js\searchBuilder.bootstrap4.js" />
    <Content Include="assets\plugins\datatables-searchbuilder\js\searchBuilder.bootstrap4.min.js" />
    <Content Include="assets\plugins\datatables-searchpanes\css\searchPanes.bootstrap4.css" />
    <Content Include="assets\plugins\datatables-searchpanes\css\searchPanes.bootstrap4.min.css" />
    <Content Include="assets\plugins\datatables-searchpanes\js\dataTables.searchPanes.js" />
    <Content Include="assets\plugins\datatables-searchpanes\js\dataTables.searchPanes.min.js" />
    <Content Include="assets\plugins\datatables-searchpanes\js\searchPanes.bootstrap4.js" />
    <Content Include="assets\plugins\datatables-searchpanes\js\searchPanes.bootstrap4.min.js" />
    <Content Include="assets\plugins\datatables-select\css\select.bootstrap4.css" />
    <Content Include="assets\plugins\datatables-select\css\select.bootstrap4.min.css" />
    <Content Include="assets\plugins\datatables-select\js\dataTables.select.js" />
    <Content Include="assets\plugins\datatables-select\js\dataTables.select.min.js" />
    <Content Include="assets\plugins\datatables-select\js\select.bootstrap4.js" />
    <Content Include="assets\plugins\datatables-select\js\select.bootstrap4.min.js" />
    <Content Include="assets\plugins\datatables\jquery.dataTables.js" />
    <Content Include="assets\plugins\datatables\jquery.dataTables.min.js" />
    <Content Include="assets\plugins\daterangepicker\daterangepicker.css" />
    <Content Include="assets\plugins\daterangepicker\daterangepicker.js" />
    <Content Include="assets\plugins\dropzone\basic.css" />
    <Content Include="assets\plugins\dropzone\dropzone-amd-module.js" />
    <Content Include="assets\plugins\dropzone\dropzone.css" />
    <Content Include="assets\plugins\dropzone\dropzone.js" />
    <Content Include="assets\plugins\dropzone\min\basic.css" />
    <Content Include="assets\plugins\dropzone\min\basic.min.css" />
    <Content Include="assets\plugins\dropzone\min\dropzone-amd-module.min.js" />
    <Content Include="assets\plugins\dropzone\min\dropzone.css" />
    <Content Include="assets\plugins\dropzone\min\dropzone.min.css" />
    <Content Include="assets\plugins\dropzone\min\dropzone.min.js" />
    <Content Include="assets\plugins\ekko-lightbox\ekko-lightbox.css" />
    <Content Include="assets\plugins\ekko-lightbox\ekko-lightbox.js" />
    <Content Include="assets\plugins\ekko-lightbox\ekko-lightbox.min.js" />
    <Content Include="assets\plugins\fastclick\fastclick.js" />
    <Content Include="assets\plugins\filterizr\filterizr.min.js" />
    <Content Include="assets\plugins\filterizr\jquery.filterizr.min.js" />
    <Content Include="assets\plugins\filterizr\vanilla.filterizr.min.js" />
    <Content Include="assets\plugins\flag-icon-css\css\flag-icon.css" />
    <Content Include="assets\plugins\flag-icon-css\css\flag-icon.min.css" />
    <Content Include="assets\plugins\flag-icon-css\css\flag-icons.css" />
    <Content Include="assets\plugins\flag-icon-css\css\flag-icons.min.css" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ac.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ad.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ae.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\af.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ag.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ai.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\al.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\am.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ao.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\aq.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ar.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\as.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\at.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\au.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\aw.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ax.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\az.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ba.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\bb.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\bd.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\be.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\bf.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\bg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\bh.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\bi.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\bj.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\bl.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\bm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\bn.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\bo.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\bq.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\br.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\bs.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\bt.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\bv.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\bw.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\by.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\bz.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ca.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\cc.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\cd.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\cf.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\cg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ch.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ci.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ck.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\cl.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\cm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\cn.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\co.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\cp.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\cr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\cu.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\cv.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\cw.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\cx.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\cy.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\cz.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\de.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\dg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\dj.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\dk.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\dm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\do.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\dz.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ea.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ec.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ee.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\eg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\eh.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\er.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\es-ca.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\es-ct.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\es-ga.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\es.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\et.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\eu.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\fi.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\fj.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\fk.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\fm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\fo.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\fr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ga.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\gb-eng.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\gb-nir.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\gb-sct.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\gb-wls.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\gb.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\gd.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ge.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\gf.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\gg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\gh.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\gi.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\gl.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\gm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\gn.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\gp.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\gq.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\gr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\gs.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\gt.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\gu.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\gw.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\gy.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\hk.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\hm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\hn.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\hr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ht.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\hu.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ic.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\id.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ie.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\il.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\im.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\in.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\io.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\iq.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ir.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\is.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\it.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\je.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\jm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\jo.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\jp.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ke.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\kg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\kh.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ki.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\km.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\kn.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\kp.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\kr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\kw.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ky.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\kz.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\la.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\lb.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\lc.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\li.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\lk.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\lr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ls.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\lt.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\lu.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\lv.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ly.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ma.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\mc.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\md.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\me.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\mf.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\mg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\mh.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\mk.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ml.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\mm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\mn.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\mo.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\mp.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\mq.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\mr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ms.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\mt.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\mu.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\mv.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\mw.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\mx.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\my.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\mz.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\na.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\nc.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ne.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\nf.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ng.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ni.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\nl.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\no.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\np.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\nr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\nu.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\nz.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\om.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\pa.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\pe.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\pf.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\pg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ph.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\pk.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\pl.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\pm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\pn.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\pr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ps.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\pt.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\pw.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\py.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\qa.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\re.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ro.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\rs.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ru.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\rw.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\sa.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\sb.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\sc.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\sd.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\se.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\sg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\sh.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\si.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\sj.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\sk.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\sl.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\sm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\sn.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\so.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\sr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ss.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\st.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\sv.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\sx.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\sy.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\sz.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ta.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\tc.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\td.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\tf.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\tg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\th.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\tj.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\tk.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\tl.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\tm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\tn.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\to.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\tr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\tt.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\tv.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\tw.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\tz.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ua.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ug.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\um.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\un.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\us.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\uy.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\uz.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\va.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\vc.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ve.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\vg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\vi.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\vn.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\vu.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\wf.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ws.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\xk.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\xx.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\ye.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\yt.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\za.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\zm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\1x1\zw.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ac.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ad.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ae.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\af.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ag.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ai.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\al.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\am.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ao.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\aq.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ar.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\as.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\at.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\au.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\aw.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ax.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\az.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ba.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\bb.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\bd.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\be.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\bf.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\bg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\bh.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\bi.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\bj.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\bl.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\bm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\bn.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\bo.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\bq.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\br.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\bs.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\bt.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\bv.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\bw.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\by.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\bz.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ca.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\cc.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\cd.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\cf.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\cg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ch.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ci.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ck.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\cl.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\cm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\cn.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\co.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\cp.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\cr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\cu.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\cv.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\cw.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\cx.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\cy.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\cz.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\de.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\dg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\dj.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\dk.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\dm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\do.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\dz.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ea.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ec.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ee.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\eg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\eh.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\er.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\es-ca.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\es-ct.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\es-ga.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\es.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\et.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\eu.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\fi.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\fj.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\fk.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\fm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\fo.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\fr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ga.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\gb-eng.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\gb-nir.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\gb-sct.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\gb-wls.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\gb.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\gd.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ge.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\gf.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\gg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\gh.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\gi.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\gl.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\gm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\gn.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\gp.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\gq.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\gr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\gs.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\gt.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\gu.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\gw.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\gy.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\hk.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\hm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\hn.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\hr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ht.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\hu.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ic.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\id.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ie.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\il.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\im.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\in.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\io.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\iq.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ir.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\is.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\it.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\je.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\jm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\jo.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\jp.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ke.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\kg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\kh.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ki.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\km.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\kn.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\kp.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\kr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\kw.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ky.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\kz.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\la.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\lb.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\lc.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\li.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\lk.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\lr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ls.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\lt.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\lu.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\lv.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ly.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ma.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\mc.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\md.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\me.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\mf.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\mg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\mh.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\mk.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ml.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\mm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\mn.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\mo.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\mp.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\mq.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\mr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ms.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\mt.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\mu.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\mv.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\mw.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\mx.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\my.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\mz.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\na.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\nc.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ne.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\nf.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ng.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ni.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\nl.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\no.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\np.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\nr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\nu.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\nz.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\om.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\pa.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\pe.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\pf.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\pg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ph.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\pk.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\pl.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\pm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\pn.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\pr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ps.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\pt.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\pw.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\py.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\qa.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\re.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ro.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\rs.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ru.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\rw.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\sa.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\sb.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\sc.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\sd.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\se.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\sg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\sh.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\si.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\sj.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\sk.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\sl.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\sm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\sn.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\so.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\sr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ss.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\st.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\sv.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\sx.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\sy.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\sz.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ta.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\tc.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\td.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\tf.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\tg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\th.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\tj.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\tk.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\tl.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\tm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\tn.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\to.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\tr.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\tt.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\tv.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\tw.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\tz.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ua.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ug.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\um.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\un.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\us.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\uy.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\uz.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\va.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\vc.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ve.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\vg.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\vi.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\vn.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\vu.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\wf.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ws.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\xk.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\xx.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\ye.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\yt.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\za.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\zm.svg" />
    <Content Include="assets\plugins\flag-icon-css\flags\4x3\zw.svg" />
    <Content Include="assets\plugins\flot\jquery.flot.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.axislabels.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.browser.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.categories.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.composeImages.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.crosshair.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.drawSeries.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.errorbars.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.fillbetween.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.flatdata.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.hover.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.image.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.legend.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.logaxis.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.navigate.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.pie.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.resize.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.saturated.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.selection.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.stack.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.symbol.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.threshold.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.time.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.touch.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.touchNavigate.js" />
    <Content Include="assets\plugins\flot\plugins\jquery.flot.uiConstants.js" />
    <Content Include="assets\plugins\fontawesome-free\css\all.css" />
    <Content Include="assets\plugins\fontawesome-free\css\all.min.css" />
    <Content Include="assets\plugins\fontawesome-free\css\brands.css" />
    <Content Include="assets\plugins\fontawesome-free\css\brands.min.css" />
    <Content Include="assets\plugins\fontawesome-free\css\fontawesome.css" />
    <Content Include="assets\plugins\fontawesome-free\css\fontawesome.min.css" />
    <Content Include="assets\plugins\fontawesome-free\css\regular.css" />
    <Content Include="assets\plugins\fontawesome-free\css\regular.min.css" />
    <Content Include="assets\plugins\fontawesome-free\css\solid.css" />
    <Content Include="assets\plugins\fontawesome-free\css\solid.min.css" />
    <Content Include="assets\plugins\fontawesome-free\css\svg-with-js.css" />
    <Content Include="assets\plugins\fontawesome-free\css\svg-with-js.min.css" />
    <Content Include="assets\plugins\fontawesome-free\css\v4-shims.css" />
    <Content Include="assets\plugins\fontawesome-free\css\v4-shims.min.css" />
    <Content Include="assets\plugins\fontawesome-free\webfonts\fa-brands-400.svg" />
    <Content Include="assets\plugins\fontawesome-free\webfonts\fa-regular-400.svg" />
    <Content Include="assets\plugins\fontawesome-free\webfonts\fa-solid-900.svg" />
    <Content Include="assets\plugins\fullcalendar\LICENSE.txt" />
    <Content Include="assets\plugins\fullcalendar\locales-all.js" />
    <Content Include="assets\plugins\fullcalendar\locales-all.min.js" />
    <Content Include="assets\plugins\fullcalendar\locales\af.js" />
    <Content Include="assets\plugins\fullcalendar\locales\ar-dz.js" />
    <Content Include="assets\plugins\fullcalendar\locales\ar-kw.js" />
    <Content Include="assets\plugins\fullcalendar\locales\ar-ly.js" />
    <Content Include="assets\plugins\fullcalendar\locales\ar-ma.js" />
    <Content Include="assets\plugins\fullcalendar\locales\ar-sa.js" />
    <Content Include="assets\plugins\fullcalendar\locales\ar-tn.js" />
    <Content Include="assets\plugins\fullcalendar\locales\ar.js" />
    <Content Include="assets\plugins\fullcalendar\locales\az.js" />
    <Content Include="assets\plugins\fullcalendar\locales\bg.js" />
    <Content Include="assets\plugins\fullcalendar\locales\bn.js" />
    <Content Include="assets\plugins\fullcalendar\locales\bs.js" />
    <Content Include="assets\plugins\fullcalendar\locales\ca.js" />
    <Content Include="assets\plugins\fullcalendar\locales\cs.js" />
    <Content Include="assets\plugins\fullcalendar\locales\cy.js" />
    <Content Include="assets\plugins\fullcalendar\locales\da.js" />
    <Content Include="assets\plugins\fullcalendar\locales\de-at.js" />
    <Content Include="assets\plugins\fullcalendar\locales\de.js" />
    <Content Include="assets\plugins\fullcalendar\locales\el.js" />
    <Content Include="assets\plugins\fullcalendar\locales\en-au.js" />
    <Content Include="assets\plugins\fullcalendar\locales\en-gb.js" />
    <Content Include="assets\plugins\fullcalendar\locales\en-nz.js" />
    <Content Include="assets\plugins\fullcalendar\locales\eo.js" />
    <Content Include="assets\plugins\fullcalendar\locales\es-us.js" />
    <Content Include="assets\plugins\fullcalendar\locales\es.js" />
    <Content Include="assets\plugins\fullcalendar\locales\et.js" />
    <Content Include="assets\plugins\fullcalendar\locales\eu.js" />
    <Content Include="assets\plugins\fullcalendar\locales\fa.js" />
    <Content Include="assets\plugins\fullcalendar\locales\fi.js" />
    <Content Include="assets\plugins\fullcalendar\locales\fr-ca.js" />
    <Content Include="assets\plugins\fullcalendar\locales\fr-ch.js" />
    <Content Include="assets\plugins\fullcalendar\locales\fr.js" />
    <Content Include="assets\plugins\fullcalendar\locales\gl.js" />
    <Content Include="assets\plugins\fullcalendar\locales\he.js" />
    <Content Include="assets\plugins\fullcalendar\locales\hi.js" />
    <Content Include="assets\plugins\fullcalendar\locales\hr.js" />
    <Content Include="assets\plugins\fullcalendar\locales\hu.js" />
    <Content Include="assets\plugins\fullcalendar\locales\hy-am.js" />
    <Content Include="assets\plugins\fullcalendar\locales\id.js" />
    <Content Include="assets\plugins\fullcalendar\locales\is.js" />
    <Content Include="assets\plugins\fullcalendar\locales\it.js" />
    <Content Include="assets\plugins\fullcalendar\locales\ja.js" />
    <Content Include="assets\plugins\fullcalendar\locales\ka.js" />
    <Content Include="assets\plugins\fullcalendar\locales\kk.js" />
    <Content Include="assets\plugins\fullcalendar\locales\km.js" />
    <Content Include="assets\plugins\fullcalendar\locales\ko.js" />
    <Content Include="assets\plugins\fullcalendar\locales\ku.js" />
    <Content Include="assets\plugins\fullcalendar\locales\lb.js" />
    <Content Include="assets\plugins\fullcalendar\locales\lt.js" />
    <Content Include="assets\plugins\fullcalendar\locales\lv.js" />
    <Content Include="assets\plugins\fullcalendar\locales\mk.js" />
    <Content Include="assets\plugins\fullcalendar\locales\ms.js" />
    <Content Include="assets\plugins\fullcalendar\locales\nb.js" />
    <Content Include="assets\plugins\fullcalendar\locales\ne.js" />
    <Content Include="assets\plugins\fullcalendar\locales\nl.js" />
    <Content Include="assets\plugins\fullcalendar\locales\nn.js" />
    <Content Include="assets\plugins\fullcalendar\locales\pl.js" />
    <Content Include="assets\plugins\fullcalendar\locales\pt-br.js" />
    <Content Include="assets\plugins\fullcalendar\locales\pt.js" />
    <Content Include="assets\plugins\fullcalendar\locales\ro.js" />
    <Content Include="assets\plugins\fullcalendar\locales\ru.js" />
    <Content Include="assets\plugins\fullcalendar\locales\si-lk.js" />
    <Content Include="assets\plugins\fullcalendar\locales\sk.js" />
    <Content Include="assets\plugins\fullcalendar\locales\sl.js" />
    <Content Include="assets\plugins\fullcalendar\locales\sm.js" />
    <Content Include="assets\plugins\fullcalendar\locales\sq.js" />
    <Content Include="assets\plugins\fullcalendar\locales\sr-cyrl.js" />
    <Content Include="assets\plugins\fullcalendar\locales\sr.js" />
    <Content Include="assets\plugins\fullcalendar\locales\sv.js" />
    <Content Include="assets\plugins\fullcalendar\locales\ta-in.js" />
    <Content Include="assets\plugins\fullcalendar\locales\th.js" />
    <Content Include="assets\plugins\fullcalendar\locales\tr.js" />
    <Content Include="assets\plugins\fullcalendar\locales\ug.js" />
    <Content Include="assets\plugins\fullcalendar\locales\uk.js" />
    <Content Include="assets\plugins\fullcalendar\locales\uz.js" />
    <Content Include="assets\plugins\fullcalendar\locales\vi.js" />
    <Content Include="assets\plugins\fullcalendar\locales\zh-cn.js" />
    <Content Include="assets\plugins\fullcalendar\locales\zh-tw.js" />
    <Content Include="assets\plugins\fullcalendar\main.css" />
    <Content Include="assets\plugins\fullcalendar\main.js" />
    <Content Include="assets\plugins\fullcalendar\main.min.css" />
    <Content Include="assets\plugins\fullcalendar\main.min.js" />
    <Content Include="assets\plugins\icheck-bootstrap\icheck-bootstrap.css" />
    <Content Include="assets\plugins\icheck-bootstrap\icheck-bootstrap.min.css" />
    <Content Include="assets\plugins\inputmask\inputmask.es6.js" />
    <Content Include="assets\plugins\inputmask\inputmask.js" />
    <Content Include="assets\plugins\inputmask\inputmask.min.js" />
    <Content Include="assets\plugins\inputmask\jquery.inputmask.js" />
    <Content Include="assets\plugins\inputmask\jquery.inputmask.min.js" />
    <Content Include="assets\plugins\ion-rangeslider\css\ion.rangeSlider.css" />
    <Content Include="assets\plugins\ion-rangeslider\css\ion.rangeSlider.min.css" />
    <Content Include="assets\plugins\ion-rangeslider\js\ion.rangeSlider.js" />
    <Content Include="assets\plugins\ion-rangeslider\js\ion.rangeSlider.min.js" />
    <Content Include="assets\plugins\jquery-knob\jquery.knob.min.js" />
    <Content Include="assets\plugins\jquery-mapael\jquery.mapael.js" />
    <Content Include="assets\plugins\jquery-mapael\jquery.mapael.min.js" />
    <Content Include="assets\plugins\jquery-mapael\maps\france_departments.js" />
    <Content Include="assets\plugins\jquery-mapael\maps\france_departments.min.js" />
    <Content Include="assets\plugins\jquery-mapael\maps\README.txt" />
    <Content Include="assets\plugins\jquery-mapael\maps\usa_states.js" />
    <Content Include="assets\plugins\jquery-mapael\maps\usa_states.min.js" />
    <Content Include="assets\plugins\jquery-mapael\maps\world_countries.js" />
    <Content Include="assets\plugins\jquery-mapael\maps\world_countries.min.js" />
    <Content Include="assets\plugins\jquery-mapael\maps\world_countries_mercator.js" />
    <Content Include="assets\plugins\jquery-mapael\maps\world_countries_mercator.min.js" />
    <Content Include="assets\plugins\jquery-mapael\maps\world_countries_miller.js" />
    <Content Include="assets\plugins\jquery-mapael\maps\world_countries_miller.min.js" />
    <Content Include="assets\plugins\jquery-mousewheel\jquery.mousewheel.js" />
    <Content Include="assets\plugins\jquery-mousewheel\LICENSE.txt" />
    <Content Include="assets\plugins\jquery-ui\images\ui-icons_444444_256x240.png" />
    <Content Include="assets\plugins\jquery-ui\images\ui-icons_555555_256x240.png" />
    <Content Include="assets\plugins\jquery-ui\images\ui-icons_777620_256x240.png" />
    <Content Include="assets\plugins\jquery-ui\images\ui-icons_777777_256x240.png" />
    <Content Include="assets\plugins\jquery-ui\images\ui-icons_cc0000_256x240.png" />
    <Content Include="assets\plugins\jquery-ui\images\ui-icons_ffffff_256x240.png" />
    <Content Include="assets\plugins\jquery-ui\jquery-ui.css" />
    <Content Include="assets\plugins\jquery-ui\jquery-ui.js" />
    <Content Include="assets\plugins\jquery-ui\jquery-ui.min.css" />
    <Content Include="assets\plugins\jquery-ui\jquery-ui.min.js" />
    <Content Include="assets\plugins\jquery-ui\jquery-ui.structure.css" />
    <Content Include="assets\plugins\jquery-ui\jquery-ui.structure.min.css" />
    <Content Include="assets\plugins\jquery-ui\jquery-ui.theme.css" />
    <Content Include="assets\plugins\jquery-ui\jquery-ui.theme.min.css" />
    <Content Include="assets\plugins\jquery-ui\LICENSE.txt" />
    <Content Include="assets\plugins\jquery-validation\additional-methods.js" />
    <Content Include="assets\plugins\jquery-validation\additional-methods.min.js" />
    <Content Include="assets\plugins\jquery-validation\jquery.validate.js" />
    <Content Include="assets\plugins\jquery-validation\jquery.validate.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_ar.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_ar.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_az.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_az.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_bg.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_bg.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_bn_BD.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_bn_BD.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_ca.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_ca.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_cs.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_cs.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_da.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_da.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_de.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_de.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_el.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_el.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_es.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_es.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_es_AR.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_es_AR.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_es_PE.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_es_PE.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_et.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_et.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_eu.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_eu.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_fa.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_fa.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_fi.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_fi.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_fr.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_fr.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_ge.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_ge.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_gl.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_gl.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_he.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_he.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_hr.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_hr.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_hu.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_hu.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_hy_AM.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_hy_AM.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_id.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_id.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_is.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_is.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_it.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_it.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_ja.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_ja.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_ka.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_ka.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_kk.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_kk.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_ko.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_ko.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_lt.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_lt.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_lv.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_lv.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_mk.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_mk.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_my.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_my.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_nl.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_nl.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_no.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_no.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_pl.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_pl.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_pt_BR.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_pt_BR.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_pt_PT.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_pt_PT.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_ro.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_ro.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_ru.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_ru.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_sd.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_sd.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_si.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_si.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_sk.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_sk.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_sl.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_sl.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_sr.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_sr.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_sr_lat.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_sr_lat.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_sv.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_sv.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_th.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_th.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_tj.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_tj.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_tr.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_tr.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_uk.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_uk.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_ur.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_ur.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_vi.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_vi.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_zh.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_zh.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_zh_TW.js" />
    <Content Include="assets\plugins\jquery-validation\localization\messages_zh_TW.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\methods_de.js" />
    <Content Include="assets\plugins\jquery-validation\localization\methods_de.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\methods_es_CL.js" />
    <Content Include="assets\plugins\jquery-validation\localization\methods_es_CL.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\methods_fi.js" />
    <Content Include="assets\plugins\jquery-validation\localization\methods_fi.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\methods_it.js" />
    <Content Include="assets\plugins\jquery-validation\localization\methods_it.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\methods_nl.js" />
    <Content Include="assets\plugins\jquery-validation\localization\methods_nl.min.js" />
    <Content Include="assets\plugins\jquery-validation\localization\methods_pt.js" />
    <Content Include="assets\plugins\jquery-validation\localization\methods_pt.min.js" />
    <Content Include="assets\plugins\jquery\jquery.js" />
    <Content Include="assets\plugins\jquery\jquery.min.js" />
    <Content Include="assets\plugins\jquery\jquery.slim.js" />
    <Content Include="assets\plugins\jquery\jquery.slim.min.js" />
    <Content Include="assets\plugins\jqvmap\jquery.vmap.js" />
    <Content Include="assets\plugins\jqvmap\jquery.vmap.min.js" />
    <Content Include="assets\plugins\jqvmap\jqvmap.css" />
    <Content Include="assets\plugins\jqvmap\jqvmap.min.css" />
    <Content Include="assets\plugins\jqvmap\maps\continents\jquery.vmap.africa.js" />
    <Content Include="assets\plugins\jqvmap\maps\continents\jquery.vmap.asia.js" />
    <Content Include="assets\plugins\jqvmap\maps\continents\jquery.vmap.australia.js" />
    <Content Include="assets\plugins\jqvmap\maps\continents\jquery.vmap.europe.js" />
    <Content Include="assets\plugins\jqvmap\maps\continents\jquery.vmap.north-america.js" />
    <Content Include="assets\plugins\jqvmap\maps\continents\jquery.vmap.south-america.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.algeria.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.argentina.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.brazil.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.canada.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.croatia.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.europe.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.france.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.germany.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.greece.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.indonesia.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.iran.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.iraq.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.new_regions_france.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.russia.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.serbia.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.tunisia.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.turkey.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.ukraine.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.usa.counties.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.usa.districts.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.usa.js" />
    <Content Include="assets\plugins\jqvmap\maps\jquery.vmap.world.js" />
    <Content Include="assets\plugins\jsgrid\demos\db.js" />
    <Content Include="assets\plugins\jsgrid\i18n\jsgrid-de.js" />
    <Content Include="assets\plugins\jsgrid\i18n\jsgrid-es.js" />
    <Content Include="assets\plugins\jsgrid\i18n\jsgrid-fr.js" />
    <Content Include="assets\plugins\jsgrid\i18n\jsgrid-he.js" />
    <Content Include="assets\plugins\jsgrid\i18n\jsgrid-ja.js" />
    <Content Include="assets\plugins\jsgrid\i18n\jsgrid-ka.js" />
    <Content Include="assets\plugins\jsgrid\i18n\jsgrid-pl.js" />
    <Content Include="assets\plugins\jsgrid\i18n\jsgrid-pt-br.js" />
    <Content Include="assets\plugins\jsgrid\i18n\jsgrid-pt.js" />
    <Content Include="assets\plugins\jsgrid\i18n\jsgrid-ru.js" />
    <Content Include="assets\plugins\jsgrid\i18n\jsgrid-tr.js" />
    <Content Include="assets\plugins\jsgrid\i18n\jsgrid-zh-cn.js" />
    <Content Include="assets\plugins\jsgrid\i18n\jsgrid-zh-tw.js" />
    <Content Include="assets\plugins\jsgrid\jsgrid-theme.css" />
    <Content Include="assets\plugins\jsgrid\jsgrid-theme.min.css" />
    <Content Include="assets\plugins\jsgrid\jsgrid.css" />
    <Content Include="assets\plugins\jsgrid\jsgrid.js" />
    <Content Include="assets\plugins\jsgrid\jsgrid.min.css" />
    <Content Include="assets\plugins\jsgrid\jsgrid.min.js" />
    <Content Include="assets\plugins\jszip\jszip.js" />
    <Content Include="assets\plugins\jszip\jszip.min.js" />
    <Content Include="assets\plugins\moment\locales.js" />
    <Content Include="assets\plugins\moment\locales.min.js" />
    <Content Include="assets\plugins\moment\locale\af.js" />
    <Content Include="assets\plugins\moment\locale\ar-dz.js" />
    <Content Include="assets\plugins\moment\locale\ar-kw.js" />
    <Content Include="assets\plugins\moment\locale\ar-ly.js" />
    <Content Include="assets\plugins\moment\locale\ar-ma.js" />
    <Content Include="assets\plugins\moment\locale\ar-sa.js" />
    <Content Include="assets\plugins\moment\locale\ar-tn.js" />
    <Content Include="assets\plugins\moment\locale\ar.js" />
    <Content Include="assets\plugins\moment\locale\az.js" />
    <Content Include="assets\plugins\moment\locale\be.js" />
    <Content Include="assets\plugins\moment\locale\bg.js" />
    <Content Include="assets\plugins\moment\locale\bm.js" />
    <Content Include="assets\plugins\moment\locale\bn-bd.js" />
    <Content Include="assets\plugins\moment\locale\bn.js" />
    <Content Include="assets\plugins\moment\locale\bo.js" />
    <Content Include="assets\plugins\moment\locale\br.js" />
    <Content Include="assets\plugins\moment\locale\bs.js" />
    <Content Include="assets\plugins\moment\locale\ca.js" />
    <Content Include="assets\plugins\moment\locale\cs.js" />
    <Content Include="assets\plugins\moment\locale\cv.js" />
    <Content Include="assets\plugins\moment\locale\cy.js" />
    <Content Include="assets\plugins\moment\locale\da.js" />
    <Content Include="assets\plugins\moment\locale\de-at.js" />
    <Content Include="assets\plugins\moment\locale\de-ch.js" />
    <Content Include="assets\plugins\moment\locale\de.js" />
    <Content Include="assets\plugins\moment\locale\dv.js" />
    <Content Include="assets\plugins\moment\locale\el.js" />
    <Content Include="assets\plugins\moment\locale\en-au.js" />
    <Content Include="assets\plugins\moment\locale\en-ca.js" />
    <Content Include="assets\plugins\moment\locale\en-gb.js" />
    <Content Include="assets\plugins\moment\locale\en-ie.js" />
    <Content Include="assets\plugins\moment\locale\en-il.js" />
    <Content Include="assets\plugins\moment\locale\en-in.js" />
    <Content Include="assets\plugins\moment\locale\en-nz.js" />
    <Content Include="assets\plugins\moment\locale\en-SG.js" />
    <Content Include="assets\plugins\moment\locale\eo.js" />
    <Content Include="assets\plugins\moment\locale\es-do.js" />
    <Content Include="assets\plugins\moment\locale\es-mx.js" />
    <Content Include="assets\plugins\moment\locale\es-us.js" />
    <Content Include="assets\plugins\moment\locale\es.js" />
    <Content Include="assets\plugins\moment\locale\et.js" />
    <Content Include="assets\plugins\moment\locale\eu.js" />
    <Content Include="assets\plugins\moment\locale\fa.js" />
    <Content Include="assets\plugins\moment\locale\fi.js" />
    <Content Include="assets\plugins\moment\locale\fil.js" />
    <Content Include="assets\plugins\moment\locale\fo.js" />
    <Content Include="assets\plugins\moment\locale\fr-ca.js" />
    <Content Include="assets\plugins\moment\locale\fr-ch.js" />
    <Content Include="assets\plugins\moment\locale\fr.js" />
    <Content Include="assets\plugins\moment\locale\fy.js" />
    <Content Include="assets\plugins\moment\locale\ga.js" />
    <Content Include="assets\plugins\moment\locale\gd.js" />
    <Content Include="assets\plugins\moment\locale\gl.js" />
    <Content Include="assets\plugins\moment\locale\gom-deva.js" />
    <Content Include="assets\plugins\moment\locale\gom-latn.js" />
    <Content Include="assets\plugins\moment\locale\gu.js" />
    <Content Include="assets\plugins\moment\locale\he.js" />
    <Content Include="assets\plugins\moment\locale\hi.js" />
    <Content Include="assets\plugins\moment\locale\hr.js" />
    <Content Include="assets\plugins\moment\locale\hu.js" />
    <Content Include="assets\plugins\moment\locale\hy-am.js" />
    <Content Include="assets\plugins\moment\locale\id.js" />
    <Content Include="assets\plugins\moment\locale\is.js" />
    <Content Include="assets\plugins\moment\locale\it-ch.js" />
    <Content Include="assets\plugins\moment\locale\it.js" />
    <Content Include="assets\plugins\moment\locale\ja.js" />
    <Content Include="assets\plugins\moment\locale\jv.js" />
    <Content Include="assets\plugins\moment\locale\ka.js" />
    <Content Include="assets\plugins\moment\locale\kk.js" />
    <Content Include="assets\plugins\moment\locale\km.js" />
    <Content Include="assets\plugins\moment\locale\kn.js" />
    <Content Include="assets\plugins\moment\locale\ko.js" />
    <Content Include="assets\plugins\moment\locale\ku.js" />
    <Content Include="assets\plugins\moment\locale\ky.js" />
    <Content Include="assets\plugins\moment\locale\lb.js" />
    <Content Include="assets\plugins\moment\locale\lo.js" />
    <Content Include="assets\plugins\moment\locale\lt.js" />
    <Content Include="assets\plugins\moment\locale\lv.js" />
    <Content Include="assets\plugins\moment\locale\me.js" />
    <Content Include="assets\plugins\moment\locale\mi.js" />
    <Content Include="assets\plugins\moment\locale\mk.js" />
    <Content Include="assets\plugins\moment\locale\ml.js" />
    <Content Include="assets\plugins\moment\locale\mn.js" />
    <Content Include="assets\plugins\moment\locale\mr.js" />
    <Content Include="assets\plugins\moment\locale\ms-my.js" />
    <Content Include="assets\plugins\moment\locale\ms.js" />
    <Content Include="assets\plugins\moment\locale\mt.js" />
    <Content Include="assets\plugins\moment\locale\my.js" />
    <Content Include="assets\plugins\moment\locale\nb.js" />
    <Content Include="assets\plugins\moment\locale\ne.js" />
    <Content Include="assets\plugins\moment\locale\nl-be.js" />
    <Content Include="assets\plugins\moment\locale\nl.js" />
    <Content Include="assets\plugins\moment\locale\nn.js" />
    <Content Include="assets\plugins\moment\locale\oc-lnc.js" />
    <Content Include="assets\plugins\moment\locale\pa-in.js" />
    <Content Include="assets\plugins\moment\locale\pl.js" />
    <Content Include="assets\plugins\moment\locale\pt-br.js" />
    <Content Include="assets\plugins\moment\locale\pt.js" />
    <Content Include="assets\plugins\moment\locale\ro.js" />
    <Content Include="assets\plugins\moment\locale\ru.js" />
    <Content Include="assets\plugins\moment\locale\sd.js" />
    <Content Include="assets\plugins\moment\locale\se.js" />
    <Content Include="assets\plugins\moment\locale\si.js" />
    <Content Include="assets\plugins\moment\locale\sk.js" />
    <Content Include="assets\plugins\moment\locale\sl.js" />
    <Content Include="assets\plugins\moment\locale\sq.js" />
    <Content Include="assets\plugins\moment\locale\sr-cyrl.js" />
    <Content Include="assets\plugins\moment\locale\sr.js" />
    <Content Include="assets\plugins\moment\locale\ss.js" />
    <Content Include="assets\plugins\moment\locale\sv.js" />
    <Content Include="assets\plugins\moment\locale\sw.js" />
    <Content Include="assets\plugins\moment\locale\ta.js" />
    <Content Include="assets\plugins\moment\locale\te.js" />
    <Content Include="assets\plugins\moment\locale\tet.js" />
    <Content Include="assets\plugins\moment\locale\tg.js" />
    <Content Include="assets\plugins\moment\locale\th.js" />
    <Content Include="assets\plugins\moment\locale\tk.js" />
    <Content Include="assets\plugins\moment\locale\tl-ph.js" />
    <Content Include="assets\plugins\moment\locale\tlh.js" />
    <Content Include="assets\plugins\moment\locale\tr.js" />
    <Content Include="assets\plugins\moment\locale\tzl.js" />
    <Content Include="assets\plugins\moment\locale\tzm-latn.js" />
    <Content Include="assets\plugins\moment\locale\tzm.js" />
    <Content Include="assets\plugins\moment\locale\ug-cn.js" />
    <Content Include="assets\plugins\moment\locale\uk.js" />
    <Content Include="assets\plugins\moment\locale\ur.js" />
    <Content Include="assets\plugins\moment\locale\uz-latn.js" />
    <Content Include="assets\plugins\moment\locale\uz.js" />
    <Content Include="assets\plugins\moment\locale\vi.js" />
    <Content Include="assets\plugins\moment\locale\x-pseudo.js" />
    <Content Include="assets\plugins\moment\locale\yo.js" />
    <Content Include="assets\plugins\moment\locale\zh-cn.js" />
    <Content Include="assets\plugins\moment\locale\zh-hk.js" />
    <Content Include="assets\plugins\moment\locale\zh-mo.js" />
    <Content Include="assets\plugins\moment\locale\zh-tw.js" />
    <Content Include="assets\plugins\moment\moment-with-locales.js" />
    <Content Include="assets\plugins\moment\moment-with-locales.min.js" />
    <Content Include="assets\plugins\moment\moment.min.js" />
    <Content Include="assets\plugins\overlayScrollbars\css\OverlayScrollbars.css" />
    <Content Include="assets\plugins\overlayScrollbars\css\OverlayScrollbars.min.css" />
    <Content Include="assets\plugins\overlayScrollbars\js\jquery.overlayScrollbars.js" />
    <Content Include="assets\plugins\overlayScrollbars\js\jquery.overlayScrollbars.min.js" />
    <Content Include="assets\plugins\overlayScrollbars\js\OverlayScrollbars.js" />
    <Content Include="assets\plugins\overlayScrollbars\js\OverlayScrollbars.min.js" />
    <Content Include="assets\plugins\pace-progress\pace.js" />
    <Content Include="assets\plugins\pace-progress\pace.min.js" />
    <Content Include="assets\plugins\pace-progress\themes\black\pace-theme-barber-shop.css" />
    <Content Include="assets\plugins\pace-progress\themes\black\pace-theme-big-counter.css" />
    <Content Include="assets\plugins\pace-progress\themes\black\pace-theme-bounce.css" />
    <Content Include="assets\plugins\pace-progress\themes\black\pace-theme-center-atom.css" />
    <Content Include="assets\plugins\pace-progress\themes\black\pace-theme-center-circle.css" />
    <Content Include="assets\plugins\pace-progress\themes\black\pace-theme-center-radar.css" />
    <Content Include="assets\plugins\pace-progress\themes\black\pace-theme-center-simple.css" />
    <Content Include="assets\plugins\pace-progress\themes\black\pace-theme-corner-indicator.css" />
    <Content Include="assets\plugins\pace-progress\themes\black\pace-theme-fill-left.css" />
    <Content Include="assets\plugins\pace-progress\themes\black\pace-theme-flash.css" />
    <Content Include="assets\plugins\pace-progress\themes\black\pace-theme-flat-top.css" />
    <Content Include="assets\plugins\pace-progress\themes\black\pace-theme-loading-bar.css" />
    <Content Include="assets\plugins\pace-progress\themes\black\pace-theme-mac-osx.css" />
    <Content Include="assets\plugins\pace-progress\themes\black\pace-theme-material.css" />
    <Content Include="assets\plugins\pace-progress\themes\black\pace-theme-minimal.css" />
    <Content Include="assets\plugins\pace-progress\themes\blue\pace-theme-barber-shop.css" />
    <Content Include="assets\plugins\pace-progress\themes\blue\pace-theme-big-counter.css" />
    <Content Include="assets\plugins\pace-progress\themes\blue\pace-theme-bounce.css" />
    <Content Include="assets\plugins\pace-progress\themes\blue\pace-theme-center-atom.css" />
    <Content Include="assets\plugins\pace-progress\themes\blue\pace-theme-center-circle.css" />
    <Content Include="assets\plugins\pace-progress\themes\blue\pace-theme-center-radar.css" />
    <Content Include="assets\plugins\pace-progress\themes\blue\pace-theme-center-simple.css" />
    <Content Include="assets\plugins\pace-progress\themes\blue\pace-theme-corner-indicator.css" />
    <Content Include="assets\plugins\pace-progress\themes\blue\pace-theme-fill-left.css" />
    <Content Include="assets\plugins\pace-progress\themes\blue\pace-theme-flash.css" />
    <Content Include="assets\plugins\pace-progress\themes\blue\pace-theme-flat-top.css" />
    <Content Include="assets\plugins\pace-progress\themes\blue\pace-theme-loading-bar.css" />
    <Content Include="assets\plugins\pace-progress\themes\blue\pace-theme-mac-osx.css" />
    <Content Include="assets\plugins\pace-progress\themes\blue\pace-theme-material.css" />
    <Content Include="assets\plugins\pace-progress\themes\blue\pace-theme-minimal.css" />
    <Content Include="assets\plugins\pace-progress\themes\green\pace-theme-barber-shop.css" />
    <Content Include="assets\plugins\pace-progress\themes\green\pace-theme-big-counter.css" />
    <Content Include="assets\plugins\pace-progress\themes\green\pace-theme-bounce.css" />
    <Content Include="assets\plugins\pace-progress\themes\green\pace-theme-center-atom.css" />
    <Content Include="assets\plugins\pace-progress\themes\green\pace-theme-center-circle.css" />
    <Content Include="assets\plugins\pace-progress\themes\green\pace-theme-center-radar.css" />
    <Content Include="assets\plugins\pace-progress\themes\green\pace-theme-center-simple.css" />
    <Content Include="assets\plugins\pace-progress\themes\green\pace-theme-corner-indicator.css" />
    <Content Include="assets\plugins\pace-progress\themes\green\pace-theme-fill-left.css" />
    <Content Include="assets\plugins\pace-progress\themes\green\pace-theme-flash.css" />
    <Content Include="assets\plugins\pace-progress\themes\green\pace-theme-flat-top.css" />
    <Content Include="assets\plugins\pace-progress\themes\green\pace-theme-loading-bar.css" />
    <Content Include="assets\plugins\pace-progress\themes\green\pace-theme-mac-osx.css" />
    <Content Include="assets\plugins\pace-progress\themes\green\pace-theme-material.css" />
    <Content Include="assets\plugins\pace-progress\themes\green\pace-theme-minimal.css" />
    <Content Include="assets\plugins\pace-progress\themes\orange\pace-theme-barber-shop.css" />
    <Content Include="assets\plugins\pace-progress\themes\orange\pace-theme-big-counter.css" />
    <Content Include="assets\plugins\pace-progress\themes\orange\pace-theme-bounce.css" />
    <Content Include="assets\plugins\pace-progress\themes\orange\pace-theme-center-atom.css" />
    <Content Include="assets\plugins\pace-progress\themes\orange\pace-theme-center-circle.css" />
    <Content Include="assets\plugins\pace-progress\themes\orange\pace-theme-center-radar.css" />
    <Content Include="assets\plugins\pace-progress\themes\orange\pace-theme-center-simple.css" />
    <Content Include="assets\plugins\pace-progress\themes\orange\pace-theme-corner-indicator.css" />
    <Content Include="assets\plugins\pace-progress\themes\orange\pace-theme-fill-left.css" />
    <Content Include="assets\plugins\pace-progress\themes\orange\pace-theme-flash.css" />
    <Content Include="assets\plugins\pace-progress\themes\orange\pace-theme-flat-top.css" />
    <Content Include="assets\plugins\pace-progress\themes\orange\pace-theme-loading-bar.css" />
    <Content Include="assets\plugins\pace-progress\themes\orange\pace-theme-mac-osx.css" />
    <Content Include="assets\plugins\pace-progress\themes\orange\pace-theme-material.css" />
    <Content Include="assets\plugins\pace-progress\themes\orange\pace-theme-minimal.css" />
    <Content Include="assets\plugins\pace-progress\themes\pink\pace-theme-barber-shop.css" />
    <Content Include="assets\plugins\pace-progress\themes\pink\pace-theme-big-counter.css" />
    <Content Include="assets\plugins\pace-progress\themes\pink\pace-theme-bounce.css" />
    <Content Include="assets\plugins\pace-progress\themes\pink\pace-theme-center-atom.css" />
    <Content Include="assets\plugins\pace-progress\themes\pink\pace-theme-center-circle.css" />
    <Content Include="assets\plugins\pace-progress\themes\pink\pace-theme-center-radar.css" />
    <Content Include="assets\plugins\pace-progress\themes\pink\pace-theme-center-simple.css" />
    <Content Include="assets\plugins\pace-progress\themes\pink\pace-theme-corner-indicator.css" />
    <Content Include="assets\plugins\pace-progress\themes\pink\pace-theme-fill-left.css" />
    <Content Include="assets\plugins\pace-progress\themes\pink\pace-theme-flash.css" />
    <Content Include="assets\plugins\pace-progress\themes\pink\pace-theme-flat-top.css" />
    <Content Include="assets\plugins\pace-progress\themes\pink\pace-theme-loading-bar.css" />
    <Content Include="assets\plugins\pace-progress\themes\pink\pace-theme-mac-osx.css" />
    <Content Include="assets\plugins\pace-progress\themes\pink\pace-theme-material.css" />
    <Content Include="assets\plugins\pace-progress\themes\pink\pace-theme-minimal.css" />
    <Content Include="assets\plugins\pace-progress\themes\purple\pace-theme-barber-shop.css" />
    <Content Include="assets\plugins\pace-progress\themes\purple\pace-theme-big-counter.css" />
    <Content Include="assets\plugins\pace-progress\themes\purple\pace-theme-bounce.css" />
    <Content Include="assets\plugins\pace-progress\themes\purple\pace-theme-center-atom.css" />
    <Content Include="assets\plugins\pace-progress\themes\purple\pace-theme-center-circle.css" />
    <Content Include="assets\plugins\pace-progress\themes\purple\pace-theme-center-radar.css" />
    <Content Include="assets\plugins\pace-progress\themes\purple\pace-theme-center-simple.css" />
    <Content Include="assets\plugins\pace-progress\themes\purple\pace-theme-corner-indicator.css" />
    <Content Include="assets\plugins\pace-progress\themes\purple\pace-theme-fill-left.css" />
    <Content Include="assets\plugins\pace-progress\themes\purple\pace-theme-flash.css" />
    <Content Include="assets\plugins\pace-progress\themes\purple\pace-theme-flat-top.css" />
    <Content Include="assets\plugins\pace-progress\themes\purple\pace-theme-loading-bar.css" />
    <Content Include="assets\plugins\pace-progress\themes\purple\pace-theme-mac-osx.css" />
    <Content Include="assets\plugins\pace-progress\themes\purple\pace-theme-material.css" />
    <Content Include="assets\plugins\pace-progress\themes\purple\pace-theme-minimal.css" />
    <Content Include="assets\plugins\pace-progress\themes\red\pace-theme-barber-shop.css" />
    <Content Include="assets\plugins\pace-progress\themes\red\pace-theme-big-counter.css" />
    <Content Include="assets\plugins\pace-progress\themes\red\pace-theme-bounce.css" />
    <Content Include="assets\plugins\pace-progress\themes\red\pace-theme-center-atom.css" />
    <Content Include="assets\plugins\pace-progress\themes\red\pace-theme-center-circle.css" />
    <Content Include="assets\plugins\pace-progress\themes\red\pace-theme-center-radar.css" />
    <Content Include="assets\plugins\pace-progress\themes\red\pace-theme-center-simple.css" />
    <Content Include="assets\plugins\pace-progress\themes\red\pace-theme-corner-indicator.css" />
    <Content Include="assets\plugins\pace-progress\themes\red\pace-theme-fill-left.css" />
    <Content Include="assets\plugins\pace-progress\themes\red\pace-theme-flash.css" />
    <Content Include="assets\plugins\pace-progress\themes\red\pace-theme-flat-top.css" />
    <Content Include="assets\plugins\pace-progress\themes\red\pace-theme-loading-bar.css" />
    <Content Include="assets\plugins\pace-progress\themes\red\pace-theme-mac-osx.css" />
    <Content Include="assets\plugins\pace-progress\themes\red\pace-theme-material.css" />
    <Content Include="assets\plugins\pace-progress\themes\red\pace-theme-minimal.css" />
    <Content Include="assets\plugins\pace-progress\themes\silver\pace-theme-barber-shop.css" />
    <Content Include="assets\plugins\pace-progress\themes\silver\pace-theme-big-counter.css" />
    <Content Include="assets\plugins\pace-progress\themes\silver\pace-theme-bounce.css" />
    <Content Include="assets\plugins\pace-progress\themes\silver\pace-theme-center-atom.css" />
    <Content Include="assets\plugins\pace-progress\themes\silver\pace-theme-center-circle.css" />
    <Content Include="assets\plugins\pace-progress\themes\silver\pace-theme-center-radar.css" />
    <Content Include="assets\plugins\pace-progress\themes\silver\pace-theme-center-simple.css" />
    <Content Include="assets\plugins\pace-progress\themes\silver\pace-theme-corner-indicator.css" />
    <Content Include="assets\plugins\pace-progress\themes\silver\pace-theme-fill-left.css" />
    <Content Include="assets\plugins\pace-progress\themes\silver\pace-theme-flash.css" />
    <Content Include="assets\plugins\pace-progress\themes\silver\pace-theme-flat-top.css" />
    <Content Include="assets\plugins\pace-progress\themes\silver\pace-theme-loading-bar.css" />
    <Content Include="assets\plugins\pace-progress\themes\silver\pace-theme-mac-osx.css" />
    <Content Include="assets\plugins\pace-progress\themes\silver\pace-theme-material.css" />
    <Content Include="assets\plugins\pace-progress\themes\silver\pace-theme-minimal.css" />
    <Content Include="assets\plugins\pace-progress\themes\white\pace-theme-barber-shop.css" />
    <Content Include="assets\plugins\pace-progress\themes\white\pace-theme-big-counter.css" />
    <Content Include="assets\plugins\pace-progress\themes\white\pace-theme-bounce.css" />
    <Content Include="assets\plugins\pace-progress\themes\white\pace-theme-center-atom.css" />
    <Content Include="assets\plugins\pace-progress\themes\white\pace-theme-center-circle.css" />
    <Content Include="assets\plugins\pace-progress\themes\white\pace-theme-center-radar.css" />
    <Content Include="assets\plugins\pace-progress\themes\white\pace-theme-center-simple.css" />
    <Content Include="assets\plugins\pace-progress\themes\white\pace-theme-corner-indicator.css" />
    <Content Include="assets\plugins\pace-progress\themes\white\pace-theme-fill-left.css" />
    <Content Include="assets\plugins\pace-progress\themes\white\pace-theme-flash.css" />
    <Content Include="assets\plugins\pace-progress\themes\white\pace-theme-flat-top.css" />
    <Content Include="assets\plugins\pace-progress\themes\white\pace-theme-loading-bar.css" />
    <Content Include="assets\plugins\pace-progress\themes\white\pace-theme-mac-osx.css" />
    <Content Include="assets\plugins\pace-progress\themes\white\pace-theme-material.css" />
    <Content Include="assets\plugins\pace-progress\themes\white\pace-theme-minimal.css" />
    <Content Include="assets\plugins\pace-progress\themes\yellow\pace-theme-barber-shop.css" />
    <Content Include="assets\plugins\pace-progress\themes\yellow\pace-theme-big-counter.css" />
    <Content Include="assets\plugins\pace-progress\themes\yellow\pace-theme-bounce.css" />
    <Content Include="assets\plugins\pace-progress\themes\yellow\pace-theme-center-atom.css" />
    <Content Include="assets\plugins\pace-progress\themes\yellow\pace-theme-center-circle.css" />
    <Content Include="assets\plugins\pace-progress\themes\yellow\pace-theme-center-radar.css" />
    <Content Include="assets\plugins\pace-progress\themes\yellow\pace-theme-center-simple.css" />
    <Content Include="assets\plugins\pace-progress\themes\yellow\pace-theme-corner-indicator.css" />
    <Content Include="assets\plugins\pace-progress\themes\yellow\pace-theme-fill-left.css" />
    <Content Include="assets\plugins\pace-progress\themes\yellow\pace-theme-flash.css" />
    <Content Include="assets\plugins\pace-progress\themes\yellow\pace-theme-flat-top.css" />
    <Content Include="assets\plugins\pace-progress\themes\yellow\pace-theme-loading-bar.css" />
    <Content Include="assets\plugins\pace-progress\themes\yellow\pace-theme-mac-osx.css" />
    <Content Include="assets\plugins\pace-progress\themes\yellow\pace-theme-material.css" />
    <Content Include="assets\plugins\pace-progress\themes\yellow\pace-theme-minimal.css" />
    <Content Include="assets\plugins\pdfmake\pdfmake.js" />
    <Content Include="assets\plugins\pdfmake\pdfmake.min.js" />
    <Content Include="assets\plugins\pdfmake\vfs_fonts.js" />
    <Content Include="assets\plugins\popper\esm\popper-utils.js" />
    <Content Include="assets\plugins\popper\esm\popper-utils.min.js" />
    <Content Include="assets\plugins\popper\esm\popper.js" />
    <Content Include="assets\plugins\popper\esm\popper.min.js" />
    <Content Include="assets\plugins\popper\popper-utils.js" />
    <Content Include="assets\plugins\popper\popper-utils.min.js" />
    <Content Include="assets\plugins\popper\popper.js" />
    <Content Include="assets\plugins\popper\popper.min.js" />
    <Content Include="assets\plugins\popper\umd\popper-utils.js" />
    <Content Include="assets\plugins\popper\umd\popper-utils.min.js" />
    <Content Include="assets\plugins\popper\umd\popper.js" />
    <Content Include="assets\plugins\popper\umd\popper.min.js" />
    <Content Include="assets\plugins\raphael\Gruntfile.js" />
    <Content Include="assets\plugins\raphael\license.txt" />
    <Content Include="assets\plugins\raphael\raphael.js" />
    <Content Include="assets\plugins\raphael\raphael.min.js" />
    <Content Include="assets\plugins\raphael\raphael.no-deps.js" />
    <Content Include="assets\plugins\raphael\raphael.no-deps.min.js" />
    <Content Include="assets\plugins\select2-bootstrap4-theme\select2-bootstrap4.css" />
    <Content Include="assets\plugins\select2-bootstrap4-theme\select2-bootstrap4.min.css" />
    <Content Include="assets\plugins\select2\css\select2.css" />
    <Content Include="assets\plugins\select2\css\select2.min.css" />
    <Content Include="assets\plugins\select2\js\i18n\af.js" />
    <Content Include="assets\plugins\select2\js\i18n\ar.js" />
    <Content Include="assets\plugins\select2\js\i18n\az.js" />
    <Content Include="assets\plugins\select2\js\i18n\bg.js" />
    <Content Include="assets\plugins\select2\js\i18n\bn.js" />
    <Content Include="assets\plugins\select2\js\i18n\bs.js" />
    <Content Include="assets\plugins\select2\js\i18n\build.txt" />
    <Content Include="assets\plugins\select2\js\i18n\ca.js" />
    <Content Include="assets\plugins\select2\js\i18n\cs.js" />
    <Content Include="assets\plugins\select2\js\i18n\da.js" />
    <Content Include="assets\plugins\select2\js\i18n\de.js" />
    <Content Include="assets\plugins\select2\js\i18n\dsb.js" />
    <Content Include="assets\plugins\select2\js\i18n\el.js" />
    <Content Include="assets\plugins\select2\js\i18n\en.js" />
    <Content Include="assets\plugins\select2\js\i18n\es.js" />
    <Content Include="assets\plugins\select2\js\i18n\et.js" />
    <Content Include="assets\plugins\select2\js\i18n\eu.js" />
    <Content Include="assets\plugins\select2\js\i18n\fa.js" />
    <Content Include="assets\plugins\select2\js\i18n\fi.js" />
    <Content Include="assets\plugins\select2\js\i18n\fr.js" />
    <Content Include="assets\plugins\select2\js\i18n\gl.js" />
    <Content Include="assets\plugins\select2\js\i18n\he.js" />
    <Content Include="assets\plugins\select2\js\i18n\hi.js" />
    <Content Include="assets\plugins\select2\js\i18n\hr.js" />
    <Content Include="assets\plugins\select2\js\i18n\hsb.js" />
    <Content Include="assets\plugins\select2\js\i18n\hu.js" />
    <Content Include="assets\plugins\select2\js\i18n\hy.js" />
    <Content Include="assets\plugins\select2\js\i18n\id.js" />
    <Content Include="assets\plugins\select2\js\i18n\is.js" />
    <Content Include="assets\plugins\select2\js\i18n\it.js" />
    <Content Include="assets\plugins\select2\js\i18n\ja.js" />
    <Content Include="assets\plugins\select2\js\i18n\ka.js" />
    <Content Include="assets\plugins\select2\js\i18n\km.js" />
    <Content Include="assets\plugins\select2\js\i18n\ko.js" />
    <Content Include="assets\plugins\select2\js\i18n\lt.js" />
    <Content Include="assets\plugins\select2\js\i18n\lv.js" />
    <Content Include="assets\plugins\select2\js\i18n\mk.js" />
    <Content Include="assets\plugins\select2\js\i18n\ms.js" />
    <Content Include="assets\plugins\select2\js\i18n\nb.js" />
    <Content Include="assets\plugins\select2\js\i18n\ne.js" />
    <Content Include="assets\plugins\select2\js\i18n\nl.js" />
    <Content Include="assets\plugins\select2\js\i18n\pl.js" />
    <Content Include="assets\plugins\select2\js\i18n\ps.js" />
    <Content Include="assets\plugins\select2\js\i18n\pt-BR.js" />
    <Content Include="assets\plugins\select2\js\i18n\pt.js" />
    <Content Include="assets\plugins\select2\js\i18n\ro.js" />
    <Content Include="assets\plugins\select2\js\i18n\ru.js" />
    <Content Include="assets\plugins\select2\js\i18n\sk.js" />
    <Content Include="assets\plugins\select2\js\i18n\sl.js" />
    <Content Include="assets\plugins\select2\js\i18n\sq.js" />
    <Content Include="assets\plugins\select2\js\i18n\sr-Cyrl.js" />
    <Content Include="assets\plugins\select2\js\i18n\sr.js" />
    <Content Include="assets\plugins\select2\js\i18n\sv.js" />
    <Content Include="assets\plugins\select2\js\i18n\th.js" />
    <Content Include="assets\plugins\select2\js\i18n\tk.js" />
    <Content Include="assets\plugins\select2\js\i18n\tr.js" />
    <Content Include="assets\plugins\select2\js\i18n\uk.js" />
    <Content Include="assets\plugins\select2\js\i18n\vi.js" />
    <Content Include="assets\plugins\select2\js\i18n\zh-CN.js" />
    <Content Include="assets\plugins\select2\js\i18n\zh-TW.js" />
    <Content Include="assets\plugins\select2\js\select2.full.js" />
    <Content Include="assets\plugins\select2\js\select2.full.min.js" />
    <Content Include="assets\plugins\select2\js\select2.js" />
    <Content Include="assets\plugins\select2\js\select2.min.js" />
    <Content Include="assets\plugins\sparklines\sparkline.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-ar-AR.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-ar-AR.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-ar-AR.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-az-AZ.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-az-AZ.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-az-AZ.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-bg-BG.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-bg-BG.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-bg-BG.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-bn-BD.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-bn-BD.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-ca-ES.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-ca-ES.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-ca-ES.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-cs-CZ.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-cs-CZ.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-cs-CZ.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-da-DK.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-da-DK.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-da-DK.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-de-CH.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-de-CH.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-de-DE.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-de-DE.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-de-DE.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-el-GR.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-el-GR.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-el-GR.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-en-US.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-en-US.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-es-ES.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-es-ES.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-es-ES.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-es-EU.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-es-EU.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-es-EU.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-fa-IR.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-fa-IR.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-fa-IR.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-fi-FI.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-fi-FI.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-fi-FI.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-fr-FR.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-fr-FR.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-fr-FR.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-gl-ES.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-gl-ES.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-gl-ES.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-he-IL.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-he-IL.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-he-IL.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-hr-HR.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-hr-HR.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-hr-HR.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-hu-HU.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-hu-HU.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-hu-HU.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-id-ID.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-id-ID.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-id-ID.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-it-IT.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-it-IT.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-it-IT.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-ja-JP.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-ja-JP.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-ja-JP.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-ko-KR.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-ko-KR.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-ko-KR.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-lt-LT.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-lt-LT.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-lt-LT.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-lt-LV.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-lt-LV.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-lt-LV.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-mn-MN.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-mn-MN.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-mn-MN.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-nb-NO.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-nb-NO.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-nb-NO.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-nl-NL.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-nl-NL.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-nl-NL.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-pl-PL.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-pl-PL.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-pl-PL.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-pt-BR.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-pt-BR.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-pt-BR.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-pt-PT.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-pt-PT.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-pt-PT.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-ro-RO.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-ro-RO.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-ro-RO.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-ru-RU.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-ru-RU.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-ru-RU.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-sk-SK.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-sk-SK.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-sk-SK.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-sl-SI.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-sl-SI.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-sl-SI.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-sr-RS-Latin.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-sr-RS-Latin.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-sr-RS-Latin.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-sr-RS.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-sr-RS.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-sr-RS.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-sv-SE.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-sv-SE.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-sv-SE.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-ta-IN.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-ta-IN.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-ta-IN.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-th-TH.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-th-TH.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-th-TH.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-tr-TR.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-tr-TR.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-tr-TR.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-uk-UA.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-uk-UA.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-uk-UA.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-uz-UZ.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-uz-UZ.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-uz-UZ.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-vi-VN.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-vi-VN.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-vi-VN.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-zh-CN.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-zh-CN.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-zh-CN.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\lang\summernote-zh-TW.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-zh-TW.min.js" />
    <Content Include="assets\plugins\summernote\lang\summernote-zh-TW.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\plugin\databasic\summernote-ext-databasic.css" />
    <Content Include="assets\plugins\summernote\plugin\databasic\summernote-ext-databasic.js" />
    <Content Include="assets\plugins\summernote\plugin\hello\summernote-ext-hello.js" />
    <Content Include="assets\plugins\summernote\plugin\specialchars\summernote-ext-specialchars.js" />
    <Content Include="assets\plugins\summernote\summernote-bs4.css" />
    <Content Include="assets\plugins\summernote\summernote-bs4.js" />
    <Content Include="assets\plugins\summernote\summernote-bs4.min.css" />
    <Content Include="assets\plugins\summernote\summernote-bs4.min.js" />
    <Content Include="assets\plugins\summernote\summernote-bs4.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\summernote-bs5.css" />
    <Content Include="assets\plugins\summernote\summernote-bs5.js" />
    <Content Include="assets\plugins\summernote\summernote-bs5.min.css" />
    <Content Include="assets\plugins\summernote\summernote-bs5.min.js" />
    <Content Include="assets\plugins\summernote\summernote-lite.css" />
    <Content Include="assets\plugins\summernote\summernote-lite.js" />
    <Content Include="assets\plugins\summernote\summernote-lite.min.css" />
    <Content Include="assets\plugins\summernote\summernote-lite.min.js" />
    <Content Include="assets\plugins\summernote\summernote-lite.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\summernote\summernote.css" />
    <Content Include="assets\plugins\summernote\summernote.js" />
    <Content Include="assets\plugins\summernote\summernote.min.css" />
    <Content Include="assets\plugins\summernote\summernote.min.js" />
    <Content Include="assets\plugins\summernote\summernote.min.js.LICENSE.txt" />
    <Content Include="assets\plugins\sweetalert2-theme-bootstrap-4\bootstrap-4.css" />
    <Content Include="assets\plugins\sweetalert2-theme-bootstrap-4\bootstrap-4.min.css" />
    <Content Include="assets\plugins\sweetalert2\sweetalert2.all.js" />
    <Content Include="assets\plugins\sweetalert2\sweetalert2.all.min.js" />
    <Content Include="assets\plugins\sweetalert2\sweetalert2.css" />
    <Content Include="assets\plugins\sweetalert2\sweetalert2.js" />
    <Content Include="assets\plugins\sweetalert2\sweetalert2.min.css" />
    <Content Include="assets\plugins\sweetalert2\sweetalert2.min.js" />
    <Content Include="assets\plugins\tempusdominus-bootstrap-4\css\tempusdominus-bootstrap-4.css" />
    <Content Include="assets\plugins\tempusdominus-bootstrap-4\css\tempusdominus-bootstrap-4.min.css" />
    <Content Include="assets\plugins\tempusdominus-bootstrap-4\js\tempusdominus-bootstrap-4.js" />
    <Content Include="assets\plugins\tempusdominus-bootstrap-4\js\tempusdominus-bootstrap-4.min.js" />
    <Content Include="assets\plugins\toastr\toastr.css" />
    <Content Include="assets\plugins\toastr\toastr.min.css" />
    <Content Include="assets\plugins\toastr\toastr.min.js" />
    <Content Include="assets\plugins\uplot\uPlot.cjs.js" />
    <Content Include="assets\plugins\uplot\uPlot.esm.js" />
    <Content Include="assets\plugins\uplot\uPlot.iife.js" />
    <Content Include="assets\plugins\uplot\uPlot.iife.min.js" />
    <Content Include="assets\plugins\uplot\uPlot.min.css" />
    <Content Include="assets\videos\KSDC_Intro_Video.mp4" />
    <Content Include="assets\videos\Launch_Btn.png" />
    <Content Include="assets\videos\SMART_Intro_Video.mp4" />
    <Content Include="assets\videos\Start_Btn.png" />
    <Content Include="Content\bootstrap-theme.css" />
    <Content Include="Content\bootstrap-theme.min.css" />
    <Content Include="Content\bootstrap.css" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="Content\Site.css" />
    <Content Include="DailyTransactionViewer.aspx" />
    <Content Include="Data_Migration.aspx" />
    <Content Include="Default.aspx" />
    <Content Include="DE_Agriculture_Loan_Edit.aspx" />
    <Content Include="DE_Agriculture_Loan_View.aspx" />
    <Content Include="DE_Computer_Loan_For_Student_Edit.aspx" />
    <Content Include="DE_Computer_Loan_For_Student_View.aspx" />
    <Content Include="DE_Education_Loan_Edit.aspx" />
    <Content Include="DE_Education_Loan_View.aspx" />
    <Content Include="DE_Foreign_Loan_Edit.aspx" />
    <Content Include="DE_Foreign_Loan_View.aspx" />
    <Content Include="DE_Housing_Loan_Edit.aspx" />
    <Content Include="DE_Housing_Loan_View.aspx" />
    <Content Include="DE_Housing_Maintenance_Loan_Edit.aspx" />
    <Content Include="DE_Housing_Maintenance_Loan_View.aspx" />
    <Content Include="DE_Marriage_Loan_Edit.aspx" />
    <Content Include="DE_Marriage_Loan_View.aspx" />
    <Content Include="DE_Personal_Loan_Edit.aspx" />
    <Content Include="DE_Personal_Loan_View.aspx" />
    <Content Include="DE_Self_Employement_Loan_Edit.aspx" />
    <Content Include="DE_Self_Employement_Loan_View.aspx" />
    <Content Include="DE_Vehicle_Or_Home_Appliance_Loan_Edit.aspx" />
    <Content Include="DE_Vehicle_Or_Home_Appliance_Loan_View.aspx" />
    <Content Include="DE_Working_Capital_Or_Business_Development_Loan_Edit.aspx" />
    <Content Include="DE_Working_Capital_Or_Business_Development_Loan_View.aspx" />
    <Content Include="Disbursement_Agreement_CheckList_List.aspx" />
    <Content Include="Disbursement_Agreement_CheckList_View.aspx" />
    <Content Include="Disbursement_Agreement_DataEntry_List.aspx" />
    <Content Include="Disbursement_Agreement_DataEntry_Edit.aspx" />
    <Content Include="Disbursement_Agreement_CheckList_Approve_View.aspx" />
    <Content Include="Disbursement_Agreement_CheckList_Approve_List.aspx" />
    <Content Include="Disbursement_Agreement_DataEntry_Modification_List.aspx" />
    <Content Include="Disbursement_Agreement_DataEntry_Modification_View.aspx" />
    <Content Include="Disbursement_List.aspx" />
    <Content Include="Disbursement_Order_List.aspx" />
    <Content Include="Disbursement_Order_View.aspx" />
    <Content Include="Disbursement_View.aspx" />
    <Content Include="Employee_Confirmation_Letter_List.aspx" />
    <Content Include="Employee_Confirmation_Letter_Print.aspx" />
    <Content Include="eProtal.aspx" />
    <Content Include="eProtal_Ledger.aspx" />
    <Content Include="eProtal_View.aspx" />
    <Content Include="favicon.ico" />
    <Content Include="fonts\glyphicons-halflings-regular.svg" />
    <Content Include="Global.asax" />
    <Content Include="fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="fonts\glyphicons-halflings-regular.woff" />
    <Content Include="fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="fonts\glyphicons-halflings-regular.eot" />
    <Content Include="Content\bootstrap.min.css.map" />
    <Content Include="Content\bootstrap.css.map" />
    <Content Include="Content\bootstrap-theme.min.css.map" />
    <Content Include="Content\bootstrap-theme.css.map" />
    <Content Include="assets\dist\css\adminlte.css.map" />
    <Content Include="assets\dist\css\adminlte.min.css.map" />
    <Content Include="assets\dist\css\alt\adminlte.components.css.map" />
    <Content Include="assets\dist\css\alt\adminlte.components.min.css.map" />
    <Content Include="assets\dist\css\alt\adminlte.core.css.map" />
    <Content Include="assets\dist\css\alt\adminlte.core.min.css.map" />
    <Content Include="assets\dist\css\alt\adminlte.extra-components.css.map" />
    <Content Include="assets\dist\css\alt\adminlte.extra-components.min.css.map" />
    <Content Include="assets\dist\css\alt\adminlte.light.css.map" />
    <Content Include="assets\dist\css\alt\adminlte.light.min.css.map" />
    <Content Include="assets\dist\css\alt\adminlte.pages.css.map" />
    <Content Include="assets\dist\css\alt\adminlte.pages.min.css.map" />
    <Content Include="assets\dist\css\alt\adminlte.plugins.css.map" />
    <Content Include="assets\dist\css\alt\adminlte.plugins.min.css.map" />
    <Content Include="assets\dist\js\.eslintrc.json" />
    <Content Include="assets\dist\js\adminlte.js.map" />
    <Content Include="assets\dist\js\adminlte.min.js.map" />
    <Content Include="assets\plugins\bootstrap-colorpicker\css\bootstrap-colorpicker.css.map" />
    <Content Include="assets\plugins\bootstrap-colorpicker\css\bootstrap-colorpicker.min.css.map" />
    <Content Include="assets\plugins\bootstrap-colorpicker\js\bootstrap-colorpicker.js.map" />
    <Content Include="assets\plugins\bootstrap-colorpicker\js\bootstrap-colorpicker.min.js.map" />
    <Content Include="assets\plugins\bootstrap\js\bootstrap.bundle.js.map" />
    <Content Include="assets\plugins\bootstrap\js\bootstrap.bundle.min.js.map" />
    <Content Include="assets\plugins\bootstrap\js\bootstrap.js.map" />
    <Content Include="assets\plugins\bootstrap\js\bootstrap.min.js.map" />
    <Content Include="assets\plugins\bs-custom-file-input\bs-custom-file-input.js.map" />
    <Content Include="assets\plugins\bs-custom-file-input\bs-custom-file-input.min.js.map" />
    <Content Include="assets\plugins\bs-stepper\css\bs-stepper.css.map" />
    <Content Include="assets\plugins\bs-stepper\css\bs-stepper.min.css.map" />
    <Content Include="assets\plugins\bs-stepper\js\bs-stepper.js.map" />
    <Content Include="assets\plugins\bs-stepper\js\bs-stepper.min.js.map" />
    <Content Include="assets\plugins\dropzone\dropzone.js.map" />
    <Content Include="assets\plugins\ekko-lightbox\ekko-lightbox.js.map" />
    <Content Include="assets\plugins\ekko-lightbox\ekko-lightbox.min.js.map" />
    <Content Include="assets\plugins\fontawesome-free\webfonts\fa-brands-400.eot" />
    <Content Include="assets\plugins\fontawesome-free\webfonts\fa-brands-400.ttf" />
    <Content Include="assets\plugins\fontawesome-free\webfonts\fa-brands-400.woff" />
    <Content Include="assets\plugins\fontawesome-free\webfonts\fa-brands-400.woff2" />
    <Content Include="assets\plugins\fontawesome-free\webfonts\fa-regular-400.eot" />
    <Content Include="assets\plugins\fontawesome-free\webfonts\fa-regular-400.ttf" />
    <Content Include="assets\plugins\fontawesome-free\webfonts\fa-regular-400.woff" />
    <Content Include="assets\plugins\fontawesome-free\webfonts\fa-regular-400.woff2" />
    <Content Include="assets\plugins\fontawesome-free\webfonts\fa-solid-900.eot" />
    <Content Include="assets\plugins\fontawesome-free\webfonts\fa-solid-900.ttf" />
    <Content Include="assets\plugins\fontawesome-free\webfonts\fa-solid-900.woff" />
    <Content Include="assets\plugins\fontawesome-free\webfonts\fa-solid-900.woff2" />
    <Content Include="assets\plugins\icheck-bootstrap\LICENSE" />
    <Content Include="assets\plugins\ion-rangeslider\License.md" />
    <Content Include="assets\plugins\jquery\jquery.min.map" />
    <Content Include="assets\plugins\jquery\jquery.slim.min.map" />
    <Content Include="assets\plugins\moment\locales.min.js.map" />
    <Content Include="assets\plugins\moment\moment-with-locales.min.js.map" />
    <Content Include="assets\plugins\moment\moment.min.js.map" />
    <Content Include="assets\plugins\pdfmake\pdfmake.js.map" />
    <Content Include="assets\plugins\pdfmake\pdfmake.min.js.map" />
    <Content Include="assets\plugins\popper\esm\popper-utils.js.map" />
    <Content Include="assets\plugins\popper\esm\popper-utils.min.js.map" />
    <Content Include="assets\plugins\popper\esm\popper.js.map" />
    <Content Include="assets\plugins\popper\esm\popper.min.js.map" />
    <Content Include="assets\plugins\popper\popper-utils.js.map" />
    <Content Include="assets\plugins\popper\popper-utils.min.js.map" />
    <Content Include="assets\plugins\popper\popper.js.map" />
    <Content Include="assets\plugins\popper\popper.min.js.map" />
    <Content Include="assets\plugins\popper\umd\popper-utils.js.map" />
    <Content Include="assets\plugins\popper\umd\popper-utils.min.js.map" />
    <Content Include="assets\plugins\popper\umd\popper.js.flow" />
    <Content Include="assets\plugins\popper\umd\popper.js.map" />
    <Content Include="assets\plugins\popper\umd\popper.min.js.map" />
    <Content Include="assets\plugins\sparklines\sparkline.mjs" />
    <Content Include="assets\plugins\summernote\font\summernote.eot" />
    <Content Include="assets\plugins\summernote\font\summernote.hash" />
    <Content Include="assets\plugins\summernote\font\summernote.ttf" />
    <Content Include="assets\plugins\summernote\font\summernote.woff" />
    <Content Include="assets\plugins\summernote\font\summernote.woff2" />
    <Content Include="assets\plugins\summernote\lang\summernote-ar-AR.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-az-AZ.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-bg-BG.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-bn-BD.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-ca-ES.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-cs-CZ.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-da-DK.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-de-CH.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-de-DE.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-el-GR.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-en-US.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-es-ES.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-es-EU.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-fa-IR.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-fi-FI.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-fr-FR.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-gl-ES.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-he-IL.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-hr-HR.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-hu-HU.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-id-ID.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-it-IT.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-ja-JP.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-ko-KR.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-lt-LT.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-lt-LV.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-mn-MN.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-nb-NO.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-nl-NL.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-pl-PL.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-pt-BR.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-pt-PT.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-ro-RO.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-ru-RU.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-sk-SK.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-sl-SI.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-sr-RS-Latin.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-sr-RS.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-sv-SE.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-ta-IN.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-th-TH.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-tr-TR.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-uk-UA.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-uz-UZ.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-vi-VN.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-zh-CN.js.map" />
    <Content Include="assets\plugins\summernote\lang\summernote-zh-TW.js.map" />
    <Content Include="assets\plugins\summernote\summernote-bs4.css.map" />
    <Content Include="assets\plugins\summernote\summernote-bs4.js.map" />
    <Content Include="assets\plugins\summernote\summernote-bs4.min.js.map" />
    <Content Include="assets\plugins\summernote\summernote-bs5.css.map" />
    <Content Include="assets\plugins\summernote\summernote-bs5.js.map" />
    <Content Include="assets\plugins\summernote\summernote-lite.css.map" />
    <Content Include="assets\plugins\summernote\summernote-lite.js.map" />
    <Content Include="assets\plugins\summernote\summernote-lite.min.js.map" />
    <Content Include="assets\plugins\summernote\summernote.css.map" />
    <Content Include="assets\plugins\summernote\summernote.js.map" />
    <Content Include="assets\plugins\summernote\summernote.min.js.map" />
    <Content Include="assets\plugins\toastr\toastr.js.map" />
    <Content Include="Inauguration.aspx" />
    <Content Include="Inauguration_KSDC.aspx" />
    <Content Include="KSDC_LIVE_Entities.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>KSDC_LIVE_Entities.edmx</DependentUpon>
      <LastGenOutput>KSDC_LIVE_Entities.Context.cs</LastGenOutput>
    </Content>
    <Content Include="KSDC_LIVE_Entities.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>KSDC_LIVE_Entities.edmx</DependentUpon>
      <LastGenOutput>KSDC_LIVE_Entities1.cs</LastGenOutput>
    </Content>
    <Content Include="KSDC_SMART_Issues_Add.aspx" />
    <Content Include="KSDC_SMART_Issues_Add_Attachment.aspx" />
    <Content Include="KSDC_SMART_Issues_All_List.aspx" />
    <Content Include="KSDC_SMART_Issues_Edit.aspx" />
    <Content Include="KSDC_SMART_Issues_List.aspx" />
    <Content Include="Leads.aspx" />
    <Content Include="Lead_List.aspx" />
    <Content Include="Letter_Ordinary_Print.aspx" />
    <Content Include="Letter_Register_Print.aspx" />
    <Content Include="Live_Loanee_Report.aspx" />
    <Content Include="Loanee_Master.aspx" />
    <Content Include="Loan_Agreement.aspx" />
    <Content Include="Disbursement_Agreement_DataEntry_Add.aspx" />
    <Content Include="Loan_Agreement_Print.aspx" />
    <Content Include="Loan_Disbursement.aspx" />
    <Content Include="Loan_Ledger_Abstarct.aspx" />
    <Content Include="Loan_Master_View.aspx" />
    <Content Include="Loan_Remittance_Prathyasa_Print.aspx" />
    <Content Include="Loan_Remittance_Print.aspx" />
    <Content Include="Loan_Remittane_List.aspx" />
    <Content Include="Loan_Remittane_View.aspx" />
    <Content Include="Loan_Sanction.aspx" />
    <Content Include="Loan_Verification.aspx" />
    <Content Include="Log_List.aspx" />
    <Content Include="Log_View.aspx" />
    <Content Include="Notice_Ordinary_View.aspx" />
    <Content Include="Notice_Registered_Bfore_RRSR.aspx" />
    <Content Include="Notice_Registered_View.aspx" />
    <Content Include="Notice_Solvency_Letter_View.aspx" />
    <Content Include="Notice_SRNotice_View.aspx" />
    <Content Include="Notice_SRRemnder_View.aspx" />
    <Content Include="Permissions.aspx" />
    <Content Include="Personal_Ledger_Manager_List.aspx" />
    <Content Include="Personal_Ledger_Manager_View.aspx" />
    <Content Include="Personal_Ledger_Search.aspx" />
    <Content Include="Personal_Ledger_List.aspx" />
    <Content Include="Personal_Ledger_View.aspx" />
    <Content Include="Print_BC.aspx" />
    <Content Include="Print_Land_Fee.aspx" />
    <Content Include="Print_Legal_Fee.aspx" />
    <Content Include="Print_Processing_Fee.aspx" />
    <Content Include="Print_Receipt.aspx" />
    <Content Include="Proceedings_Print.aspx" />
    <Content Include="Project_Add.aspx" />
    <Content Include="Project_Edit.aspx" />
    <Content Include="Project_List.aspx" />
    <Content Include="Receipt_Application_Isuue.aspx" />
    <Content Include="Receipt_Beneficiary_Contribution.aspx" />
    <Content Include="Receipt_Processing_Fee_View.aspx" />
    <Content Include="Reciept_Processing_Fee.aspx" />
    <Content Include="Receipt_Employee_Confirmation_Letter_List.aspx" />
    <Content Include="Receipt_Loan_Remittane_List.aspx" />
    <Content Include="Receipt_Miscellaneous_List.aspx" />
    <Content Include="Receipt_Miscellaneous_View.aspx" />
    <Content Include="Receipt_Others_List.aspx" />
    <Content Include="Receipt_Others_View.aspx" />
    <Content Include="Report_Application_Issue_Search.aspx" />
    <Content Include="Report_Application_Issue_View.aspx" />
    <!-- Crystal Reports .cs files commented out due to missing assemblies -->
    <!--
    <Compile Include="Crystal_Reports\ApplicationReceivedSanctionedReport.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ApplicationReceivedSanctionedReport.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Crystal_Reports\DisburseReport.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DisburseReport.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Crystal_Reports\LoanAgreementRpt.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>LoanAgreementRpt.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    -->
    <Compile Include="eProtal.aspx.cs">
      <DependentUpon>eProtal.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="eProtal.aspx.designer.cs">
      <DependentUpon>eProtal.aspx</DependentUpon>
    </Compile>
    <Compile Include="eProtal_Ledger.aspx.cs">
      <DependentUpon>eProtal_Ledger.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="eProtal_Ledger.aspx.designer.cs">
      <DependentUpon>eProtal_Ledger.aspx</DependentUpon>
    </Compile>
    <Compile Include="eProtal_View.aspx.cs">
      <DependentUpon>eProtal_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="eProtal_View.aspx.designer.cs">
      <DependentUpon>eProtal_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="GetTransactionScroll_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Get_All_Sub_Caste_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Inauguration.aspx.cs">
      <DependentUpon>Inauguration.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Inauguration.aspx.designer.cs">
      <DependentUpon>Inauguration.aspx</DependentUpon>
    </Compile>
    <Compile Include="Inauguration_KSDC.aspx.cs">
      <DependentUpon>Inauguration_KSDC.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Inauguration_KSDC.aspx.designer.cs">
      <DependentUpon>Inauguration_KSDC.aspx</DependentUpon>
    </Compile>
    <Compile Include="KSDC_LIVE_Entities1.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Leads.aspx.cs">
      <DependentUpon>Leads.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Leads.aspx.designer.cs">
      <DependentUpon>Leads.aspx</DependentUpon>
    </Compile>
    <Compile Include="Lead_List.aspx.cs">
      <DependentUpon>Lead_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Lead_List.aspx.designer.cs">
      <DependentUpon>Lead_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Live_Loanee_Report.aspx.cs">
      <DependentUpon>Live_Loanee_Report.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Live_Loanee_Report.aspx.designer.cs">
      <DependentUpon>Live_Loanee_Report.aspx</DependentUpon>
    </Compile>
    <Compile Include="Notice_Ordinary_View.aspx.cs">
      <DependentUpon>Notice_Ordinary_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Notice_Ordinary_View.aspx.designer.cs">
      <DependentUpon>Notice_Ordinary_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Notice_Registered_Bfore_RRSR.aspx.cs">
      <DependentUpon>Notice_Registered_Bfore_RRSR.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Notice_Registered_Bfore_RRSR.aspx.designer.cs">
      <DependentUpon>Notice_Registered_Bfore_RRSR.aspx</DependentUpon>
    </Compile>
    <Compile Include="Notice_Registered_View.aspx.cs">
      <DependentUpon>Notice_Registered_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Notice_Registered_View.aspx.designer.cs">
      <DependentUpon>Notice_Registered_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Notice_Solvency_Letter_View.aspx.cs">
      <DependentUpon>Notice_Solvency_Letter_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Notice_Solvency_Letter_View.aspx.designer.cs">
      <DependentUpon>Notice_Solvency_Letter_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Notice_SRNotice_View.aspx.cs">
      <DependentUpon>Notice_SRNotice_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Notice_SRNotice_View.aspx.designer.cs">
      <DependentUpon>Notice_SRNotice_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Notice_SRRemnder_View.aspx.cs">
      <DependentUpon>Notice_SRRemnder_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Notice_SRRemnder_View.aspx.designer.cs">
      <DependentUpon>Notice_SRRemnder_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Report_Application_Issue_View.aspx.designer.cs" />
    <Content Include="Report_Application_Report_Search.aspx" />
    <Content Include="Report_Application_Report_View.aspx" />
    <Content Include="Report_Loan_Agreement_Search.aspx" />
    <Content Include="Report_Loan_Agreement_View.aspx" />
    <Content Include="Report_Loan_Disbursement_Search.aspx" />
    <Content Include="Report_Loan_Disbursement_View.aspx" />
    <Content Include="Report_Loan_Status.aspx" />
    <Content Include="Report_Loan_Status_Search.aspx" />
    <Content Include="Report_Transaction_Scroll_View.aspx" />
    <Content Include="Report_Transaction_Scroll_Search.aspx" />
    <Content Include="Role_Add.aspx" />
    <Content Include="Role_Edit.aspx" />
    <Content Include="Role_List.aspx" />
    <Content Include="Role_Permission.aspx" />
    <Content Include="Schemes_Edit.aspx" />
    <Content Include="Schemes_List.aspx" />
    <Content Include="Sector_Add.aspx" />
    <Content Include="Sector_Edit.aspx" />
    <Content Include="Sector_List.aspx" />
    <Content Include="Settings.aspx" />
    <Content Include="Smart_Report_Transaction_Scroll_Search.aspx" />
    <Content Include="Smart_Report_Transaction_View.aspx" />
    <Content Include="Surety_Confirmation.aspx" />
    <Content Include="Surety_Confirmation_List.aspx" />
    <Content Include="Surety_Confirmation_MGR.aspx" />
    <Content Include="Surety_Confirmation_MGR_List.aspx" />
    <Content Include="Surety_Employee_Edit.aspx" />
    <Content Include="Surety_Employee_View.aspx" />
    <Content Include="Surety_Employee_List.aspx" />
    <Content Include="Surety_FD_LIC_Edit.aspx" />
    <Content Include="Surety_FD_LIC_List.aspx" />
    <Content Include="Surety_FD_LIC_View.aspx" />
    <Content Include="Surety_Land_Edit.aspx" />
    <Content Include="Surety_Land_List.aspx" />
    <Content Include="Surety_Land_View.aspx" />
    <Content Include="Surety_Modification_List.aspx" />
    <Content Include="Surety_Modification_List_JS.aspx" />
    <Content Include="Surety_Modification_List_MGR.aspx" />
    <Content Include="Surety_personal_Edit.aspx" />
    <Content Include="Surety_Personal_List.aspx" />
    <Content Include="Surety_Personal_View.aspx" />
    <Content Include="Surety_Verification.aspx" />
    <Content Include="Surety_Verification_List.aspx" />
    <Content Include="User_Add.aspx" />
    <Content Include="User_Edit.aspx" />
    <Content Include="User_List.aspx" />
    <Content Include="WebService.asmx" />
    <Content Include="Welcome.aspx" />
    <Content Include="Crystal_Reports\DayBook.rpt" />
    <Content Include="Crystal_Reports\DailyTrans.rpt" />
    <EntityDeploy Include="KSDC_LIVE_Entities.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>KSDC_LIVE_Entities.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <Content Include="KSDC_LIVE_Entities.edmx.diagram">
      <DependentUpon>KSDC_LIVE_Entities.edmx</DependentUpon>
    </Content>
    <None Include="Scripts\jquery-3.4.1.intellisense.js" />
    <Content Include="Dashboard.aspx" />
    <Content Include="Profile.aspx" />
    <Content Include="Schemes_Add.aspx" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <Content Include="Scripts\jquery-3.4.1.js" />
    <Content Include="Scripts\jquery-3.4.1.min.js" />
    <Content Include="Scripts\jquery-3.4.1.slim.js" />
    <Content Include="Scripts\jquery-3.4.1.slim.min.js" />
    <Content Include="Scripts\modernizr-2.8.3.js" />
    <Content Include="Scripts\WebForms\DetailsView.js" />
    <Content Include="Scripts\WebForms\Focus.js" />
    <Content Include="Scripts\WebForms\GridView.js" />
    <Content Include="Scripts\WebForms\Menu.js" />
    <Content Include="Scripts\WebForms\MenuStandards.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjax.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxApplicationServices.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxComponentModel.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxCore.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxGlobalization.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxHistory.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxNetwork.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxSerialization.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxTimer.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxWebForms.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxWebServices.js" />
    <Content Include="Scripts\WebForms\SmartNav.js" />
    <Content Include="Scripts\WebForms\TreeView.js" />
    <Content Include="Scripts\WebForms\WebForms.js" />
    <Content Include="Scripts\WebForms\WebParts.js" />
    <Content Include="Scripts\WebForms\WebUIValidation.js" />
    <Content Include="Main.Master" />
    <Content Include="ViewSwitcher.ascx" />
    <Content Include="Web.config" />
    <Content Include="Bundle.config" />
    <Content Include="Site.Mobile.Master" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Agency_Add.aspx.cs">
      <DependentUpon>Agency_Add.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Agency_Add.aspx.designer.cs">
      <DependentUpon>Agency_Add.aspx</DependentUpon>
    </Compile>
    <Compile Include="Agency_Edit.aspx.cs">
      <DependentUpon>Agency_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Agency_Edit.aspx.designer.cs">
      <DependentUpon>Agency_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="Agency_List.aspx.cs">
      <DependentUpon>Agency_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Agency_List.aspx.designer.cs">
      <DependentUpon>Agency_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="ApplicationDataEntry_List.aspx.cs">
      <DependentUpon>ApplicationDataEntry_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ApplicationDataEntry_List.aspx.designer.cs">
      <DependentUpon>ApplicationDataEntry_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="ApplicationDataEntry_Modification_List.aspx.cs">
      <DependentUpon>ApplicationDataEntry_Modification_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ApplicationDataEntry_Modification_List.aspx.designer.cs">
      <DependentUpon>ApplicationDataEntry_Modification_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="ApplicationDataEntry_Modification_View.aspx.cs">
      <DependentUpon>ApplicationDataEntry_Modification_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ApplicationDataEntry_Modification_View.aspx.designer.cs">
      <DependentUpon>ApplicationDataEntry_Modification_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="ApplicationDataEntry_Approval_List.aspx.cs">
      <DependentUpon>ApplicationDataEntry_Approval_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ApplicationDataEntry_Approval_List.aspx.designer.cs">
      <DependentUpon>ApplicationDataEntry_Approval_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="ApplicationDataEntry_Approval_Rejected_List.aspx.cs">
      <DependentUpon>ApplicationDataEntry_Approval_Rejected_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ApplicationDataEntry_Approval_Rejected_List.aspx.designer.cs">
      <DependentUpon>ApplicationDataEntry_Approval_Rejected_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="ApplicationDataEntry_Approval_Rejected_View.aspx.cs">
      <DependentUpon>ApplicationDataEntry_Approval_Rejected_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ApplicationDataEntry_Approval_Rejected_View.aspx.designer.cs">
      <DependentUpon>ApplicationDataEntry_Approval_Rejected_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="ApplicationDataEntry_Approval_View.aspx.cs">
      <DependentUpon>ApplicationDataEntry_Approval_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ApplicationDataEntry_Approval_View.aspx.designer.cs">
      <DependentUpon>ApplicationDataEntry_Approval_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="ApplicationDataEntry_Verification_List.aspx.cs">
      <DependentUpon>ApplicationDataEntry_Verification_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ApplicationDataEntry_Verification_List.aspx.designer.cs">
      <DependentUpon>ApplicationDataEntry_Verification_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="ApplicationDataEntry_Verification_View.aspx.cs">
      <DependentUpon>ApplicationDataEntry_Verification_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ApplicationDataEntry_Verification_View.aspx.designer.cs">
      <DependentUpon>ApplicationDataEntry_Verification_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="ApplicationDataEntry_View.aspx.cs">
      <DependentUpon>ApplicationDataEntry_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ApplicationDataEntry_View.aspx.designer.cs">
      <DependentUpon>ApplicationDataEntry_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="ApplicationHold_List.aspx.cs">
      <DependentUpon>ApplicationHold_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ApplicationHold_List.aspx.designer.cs">
      <DependentUpon>ApplicationHold_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="ApplicationIssue_Add.aspx.cs">
      <DependentUpon>ApplicationIssue_Add.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ApplicationIssue_Add.aspx.designer.cs">
      <DependentUpon>ApplicationIssue_Add.aspx</DependentUpon>
    </Compile>
    <Compile Include="ApplicationIssue_Edit.aspx.cs">
      <DependentUpon>ApplicationIssue_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ApplicationIssue_Edit.aspx.designer.cs">
      <DependentUpon>ApplicationIssue_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="ApplicationIssue_List.aspx.cs">
      <DependentUpon>ApplicationIssue_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ApplicationIssue_List.aspx.designer.cs">
      <DependentUpon>ApplicationIssue_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="ApplicationIssue_Print.aspx.cs">
      <DependentUpon>ApplicationIssue_Print.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ApplicationIssue_Print.aspx.designer.cs">
      <DependentUpon>ApplicationIssue_Print.aspx</DependentUpon>
    </Compile>
    <Compile Include="ApplicationIssue_View.aspx.cs">
      <DependentUpon>ApplicationIssue_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ApplicationIssue_View.aspx.designer.cs">
      <DependentUpon>ApplicationIssue_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="ApplicationReceipt_List.aspx.cs">
      <DependentUpon>ApplicationReceipt_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ApplicationReceipt_List.aspx.designer.cs">
      <DependentUpon>ApplicationReceipt_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="ApplicationReceipt_View.aspx.cs">
      <DependentUpon>ApplicationReceipt_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ApplicationReceipt_View.aspx.designer.cs">
      <DependentUpon>ApplicationReceipt_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="Check_Year_End_Record_Exist_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <!-- Crystal Reports AppIssueRpt.cs file commented out due to missing assemblies -->
    <!--
    <Compile Include="Crystal_Reports\AppIssueRpt.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>AppIssueRpt.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    -->
    <Compile Include="DailyTransactionViewer.aspx.cs">
      <DependentUpon>DailyTransactionViewer.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DailyTransactionViewer.aspx.designer.cs">
      <DependentUpon>DailyTransactionViewer.aspx</DependentUpon>
    </Compile>
    <Compile Include="Data_Migration.aspx.cs">
      <DependentUpon>Data_Migration.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Data_Migration.aspx.designer.cs">
      <DependentUpon>Data_Migration.aspx</DependentUpon>
    </Compile>
    <Compile Include="Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Agriculture_Loan_Edit.aspx.cs">
      <DependentUpon>DE_Agriculture_Loan_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Agriculture_Loan_Edit.aspx.designer.cs">
      <DependentUpon>DE_Agriculture_Loan_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Agriculture_Loan_View.aspx.cs">
      <DependentUpon>DE_Agriculture_Loan_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Agriculture_Loan_View.aspx.designer.cs">
      <DependentUpon>DE_Agriculture_Loan_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Computer_Loan_For_Student_Edit.aspx.cs">
      <DependentUpon>DE_Computer_Loan_For_Student_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Computer_Loan_For_Student_Edit.aspx.designer.cs">
      <DependentUpon>DE_Computer_Loan_For_Student_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Computer_Loan_For_Student_View.aspx.cs">
      <DependentUpon>DE_Computer_Loan_For_Student_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Computer_Loan_For_Student_View.aspx.designer.cs">
      <DependentUpon>DE_Computer_Loan_For_Student_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Education_Loan_Edit.aspx.cs">
      <DependentUpon>DE_Education_Loan_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Education_Loan_Edit.aspx.designer.cs">
      <DependentUpon>DE_Education_Loan_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Education_Loan_View.aspx.cs">
      <DependentUpon>DE_Education_Loan_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Education_Loan_View.aspx.designer.cs">
      <DependentUpon>DE_Education_Loan_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Foreign_Loan_Edit.aspx.cs">
      <DependentUpon>DE_Foreign_Loan_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Foreign_Loan_Edit.aspx.designer.cs">
      <DependentUpon>DE_Foreign_Loan_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Foreign_Loan_View.aspx.cs">
      <DependentUpon>DE_Foreign_Loan_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Foreign_Loan_View.aspx.designer.cs">
      <DependentUpon>DE_Foreign_Loan_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Housing_Loan_Edit.aspx.cs">
      <DependentUpon>DE_Housing_Loan_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Housing_Loan_Edit.aspx.designer.cs">
      <DependentUpon>DE_Housing_Loan_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Housing_Loan_View.aspx.cs">
      <DependentUpon>DE_Housing_Loan_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Housing_Loan_View.aspx.designer.cs">
      <DependentUpon>DE_Housing_Loan_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Housing_Maintenance_Loan_Edit.aspx.cs">
      <DependentUpon>DE_Housing_Maintenance_Loan_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Housing_Maintenance_Loan_Edit.aspx.designer.cs">
      <DependentUpon>DE_Housing_Maintenance_Loan_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Housing_Maintenance_Loan_View.aspx.cs">
      <DependentUpon>DE_Housing_Maintenance_Loan_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Housing_Maintenance_Loan_View.aspx.designer.cs">
      <DependentUpon>DE_Housing_Maintenance_Loan_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Marriage_Loan_Edit.aspx.cs">
      <DependentUpon>DE_Marriage_Loan_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Marriage_Loan_Edit.aspx.designer.cs">
      <DependentUpon>DE_Marriage_Loan_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Marriage_Loan_View.aspx.cs">
      <DependentUpon>DE_Marriage_Loan_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Marriage_Loan_View.aspx.designer.cs">
      <DependentUpon>DE_Marriage_Loan_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Personal_Loan_Edit.aspx.cs">
      <DependentUpon>DE_Personal_Loan_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Personal_Loan_Edit.aspx.designer.cs">
      <DependentUpon>DE_Personal_Loan_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Personal_Loan_View.aspx.cs">
      <DependentUpon>DE_Personal_Loan_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Personal_Loan_View.aspx.designer.cs">
      <DependentUpon>DE_Personal_Loan_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Self_Employement_Loan_Edit.aspx.cs">
      <DependentUpon>DE_Self_Employement_Loan_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Self_Employement_Loan_Edit.aspx.designer.cs">
      <DependentUpon>DE_Self_Employement_Loan_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Self_Employement_Loan_View.aspx.cs">
      <DependentUpon>DE_Self_Employement_Loan_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Self_Employement_Loan_View.aspx.designer.cs">
      <DependentUpon>DE_Self_Employement_Loan_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Vehicle_Or_Home_Appliance_Loan_Edit.aspx.cs">
      <DependentUpon>DE_Vehicle_Or_Home_Appliance_Loan_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Vehicle_Or_Home_Appliance_Loan_Edit.aspx.designer.cs">
      <DependentUpon>DE_Vehicle_Or_Home_Appliance_Loan_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Vehicle_Or_Home_Appliance_Loan_View.aspx.cs">
      <DependentUpon>DE_Vehicle_Or_Home_Appliance_Loan_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Vehicle_Or_Home_Appliance_Loan_View.aspx.designer.cs">
      <DependentUpon>DE_Vehicle_Or_Home_Appliance_Loan_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Working_Capital_Or_Business_Development_Loan_Edit.aspx.cs">
      <DependentUpon>DE_Working_Capital_Or_Business_Development_Loan_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Working_Capital_Or_Business_Development_Loan_Edit.aspx.designer.cs">
      <DependentUpon>DE_Working_Capital_Or_Business_Development_Loan_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="DE_Working_Capital_Or_Business_Development_Loan_View.aspx.cs">
      <DependentUpon>DE_Working_Capital_Or_Business_Development_Loan_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DE_Working_Capital_Or_Business_Development_Loan_View.aspx.designer.cs">
      <DependentUpon>DE_Working_Capital_Or_Business_Development_Loan_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Disbursement_Agreement_CheckList_List.aspx.cs">
      <DependentUpon>Disbursement_Agreement_CheckList_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Disbursement_Agreement_CheckList_List.aspx.designer.cs">
      <DependentUpon>Disbursement_Agreement_CheckList_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Disbursement_Agreement_CheckList_View.aspx.cs">
      <DependentUpon>Disbursement_Agreement_CheckList_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Disbursement_Agreement_CheckList_View.aspx.designer.cs">
      <DependentUpon>Disbursement_Agreement_CheckList_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Disbursement_Agreement_DataEntry_List.aspx.cs">
      <DependentUpon>Disbursement_Agreement_DataEntry_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Disbursement_Agreement_DataEntry_List.aspx.designer.cs">
      <DependentUpon>Disbursement_Agreement_DataEntry_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Disbursement_Agreement_DataEntry_Edit.aspx.cs">
      <DependentUpon>Disbursement_Agreement_DataEntry_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Disbursement_Agreement_DataEntry_Edit.aspx.designer.cs">
      <DependentUpon>Disbursement_Agreement_DataEntry_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="Disbursement_Agreement_CheckList_Approve_View.aspx.cs">
      <DependentUpon>Disbursement_Agreement_CheckList_Approve_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Disbursement_Agreement_CheckList_Approve_View.aspx.designer.cs">
      <DependentUpon>Disbursement_Agreement_CheckList_Approve_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Disbursement_Agreement_CheckList_Approve_List.aspx.cs">
      <DependentUpon>Disbursement_Agreement_CheckList_Approve_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Disbursement_Agreement_CheckList_Approve_List.aspx.designer.cs">
      <DependentUpon>Disbursement_Agreement_CheckList_Approve_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Disbursement_Agreement_DataEntry_Modification_List.aspx.cs">
      <DependentUpon>Disbursement_Agreement_DataEntry_Modification_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Disbursement_Agreement_DataEntry_Modification_List.aspx.designer.cs">
      <DependentUpon>Disbursement_Agreement_DataEntry_Modification_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Disbursement_Agreement_DataEntry_Modification_View.aspx.cs">
      <DependentUpon>Disbursement_Agreement_DataEntry_Modification_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Disbursement_Agreement_DataEntry_Modification_View.aspx.designer.cs">
      <DependentUpon>Disbursement_Agreement_DataEntry_Modification_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Disbursement_List.aspx.cs">
      <DependentUpon>Disbursement_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Disbursement_List.aspx.designer.cs">
      <DependentUpon>Disbursement_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Disbursement_Order_List.aspx.cs">
      <DependentUpon>Disbursement_Order_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Disbursement_Order_List.aspx.designer.cs">
      <DependentUpon>Disbursement_Order_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Disbursement_Order_View.aspx.cs">
      <DependentUpon>Disbursement_Order_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Disbursement_Order_View.aspx.designer.cs">
      <DependentUpon>Disbursement_Order_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Disbursement_View.aspx.cs">
      <DependentUpon>Disbursement_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Disbursement_View.aspx.designer.cs">
      <DependentUpon>Disbursement_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Employee_Confirmation_Letter_List.aspx.cs">
      <DependentUpon>Employee_Confirmation_Letter_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Employee_Confirmation_Letter_List.aspx.designer.cs">
      <DependentUpon>Employee_Confirmation_Letter_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Employee_Confirmation_Letter_Print.aspx.cs">
      <DependentUpon>Employee_Confirmation_Letter_Print.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Employee_Confirmation_Letter_Print.aspx.designer.cs">
      <DependentUpon>Employee_Confirmation_Letter_Print.aspx</DependentUpon>
    </Compile>
    <Compile Include="Employee_Confirmation_Letter_Print_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="GetMenuItems_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Get_All_Remittance_By_Reg_No_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Get_BC_Amount_By_Reg_No_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Get_BC_Fee_Rec_No_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Get_Land_Fee_Rec_No_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Get_Legal_Fee_Rec_No_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Get_Processing_Fee_By_Reg_No_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Get_Processing_Fee_Rec_No_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Get_Recovered_Amount_Details_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Get_Remittance_By_Reg_No_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Get_User_Details_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Get_Waiver_Details_By_Loan_No_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Get_Wavier_By_No_Default_Installment_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Dashboard.aspx.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Dashboard.aspx.designer.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
    </Compile>
    <Compile Include="KSDC_LIVE_Entities.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>KSDC_LIVE_Entities.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="KSDC_LIVE_Entities.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="KSDC_LIVE_Entities.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>KSDC_LIVE_Entities.edmx</DependentUpon>
    </Compile>
    <Compile Include="KSDC_SMART_Issues_Add.aspx.cs">
      <DependentUpon>KSDC_SMART_Issues_Add.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="KSDC_SMART_Issues_Add.aspx.designer.cs">
      <DependentUpon>KSDC_SMART_Issues_Add.aspx</DependentUpon>
    </Compile>
    <Compile Include="KSDC_SMART_Issues_Add_Attachment.aspx.cs">
      <DependentUpon>KSDC_SMART_Issues_Add_Attachment.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="KSDC_SMART_Issues_Add_Attachment.aspx.designer.cs">
      <DependentUpon>KSDC_SMART_Issues_Add_Attachment.aspx</DependentUpon>
    </Compile>
    <Compile Include="KSDC_SMART_Issues_All_List.aspx.cs">
      <DependentUpon>KSDC_SMART_Issues_All_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="KSDC_SMART_Issues_All_List.aspx.designer.cs">
      <DependentUpon>KSDC_SMART_Issues_All_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="KSDC_SMART_Issues_Edit.aspx.cs">
      <DependentUpon>KSDC_SMART_Issues_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="KSDC_SMART_Issues_Edit.aspx.designer.cs">
      <DependentUpon>KSDC_SMART_Issues_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="KSDC_SMART_Issues_List.aspx.cs">
      <DependentUpon>KSDC_SMART_Issues_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="KSDC_SMART_Issues_List.aspx.designer.cs">
      <DependentUpon>KSDC_SMART_Issues_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Letter_Ordinary_Print.aspx.cs">
      <DependentUpon>Letter_Ordinary_Print.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Letter_Ordinary_Print.aspx.designer.cs">
      <DependentUpon>Letter_Ordinary_Print.aspx</DependentUpon>
    </Compile>
    <Compile Include="Letter_Register_Print.aspx.cs">
      <DependentUpon>Letter_Register_Print.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Letter_Register_Print.aspx.designer.cs">
      <DependentUpon>Letter_Register_Print.aspx</DependentUpon>
    </Compile>
    <Compile Include="Loanee_Master.aspx.cs">
      <DependentUpon>Loanee_Master.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Loanee_Master.aspx.designer.cs">
      <DependentUpon>Loanee_Master.aspx</DependentUpon>
    </Compile>
    <Compile Include="Loan_Agreement.aspx.cs">
      <DependentUpon>Loan_Agreement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Loan_Agreement.aspx.designer.cs">
      <DependentUpon>Loan_Agreement.aspx</DependentUpon>
    </Compile>
    <Compile Include="Disbursement_Agreement_DataEntry_Add.aspx.cs">
      <DependentUpon>Disbursement_Agreement_DataEntry_Add.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Disbursement_Agreement_DataEntry_Add.aspx.designer.cs">
      <DependentUpon>Disbursement_Agreement_DataEntry_Add.aspx</DependentUpon>
    </Compile>
    <Compile Include="Loan_Agreement_Print.aspx.cs">
      <DependentUpon>Loan_Agreement_Print.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Loan_Agreement_Print.aspx.designer.cs">
      <DependentUpon>Loan_Agreement_Print.aspx</DependentUpon>
    </Compile>
    <Compile Include="Loan_Credit_Balance_Details_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Loan_Disbursement.aspx.cs">
      <DependentUpon>Loan_Disbursement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Loan_Disbursement.aspx.designer.cs">
      <DependentUpon>Loan_Disbursement.aspx</DependentUpon>
    </Compile>
    <Compile Include="Loan_Ledger_Abstarct.aspx.cs">
      <DependentUpon>Loan_Ledger_Abstarct.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Loan_Ledger_Abstarct.aspx.designer.cs">
      <DependentUpon>Loan_Ledger_Abstarct.aspx</DependentUpon>
    </Compile>
    <Compile Include="Loan_Master_View.aspx.cs">
      <DependentUpon>Loan_Master_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Loan_Master_View.aspx.designer.cs">
      <DependentUpon>Loan_Master_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Loan_Remittance_Prathyasa_Print.aspx.cs">
      <DependentUpon>Loan_Remittance_Prathyasa_Print.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Loan_Remittance_Prathyasa_Print.aspx.designer.cs">
      <DependentUpon>Loan_Remittance_Prathyasa_Print.aspx</DependentUpon>
    </Compile>
    <Compile Include="Loan_Remittance_Print.aspx.cs">
      <DependentUpon>Loan_Remittance_Print.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Loan_Remittance_Print.aspx.designer.cs">
      <DependentUpon>Loan_Remittance_Print.aspx</DependentUpon>
    </Compile>
    <Compile Include="Loan_Remittane_List.aspx.cs">
      <DependentUpon>Loan_Remittane_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Loan_Remittane_List.aspx.designer.cs">
      <DependentUpon>Loan_Remittane_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Loan_Remittane_View.aspx.cs">
      <DependentUpon>Loan_Remittane_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Loan_Remittane_View.aspx.designer.cs">
      <DependentUpon>Loan_Remittane_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Loan_Sanction.aspx.cs">
      <DependentUpon>Loan_Sanction.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Loan_Sanction.aspx.designer.cs">
      <DependentUpon>Loan_Sanction.aspx</DependentUpon>
    </Compile>
    <Compile Include="Loan_Verification.aspx.cs">
      <DependentUpon>Loan_Verification.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Loan_Verification.aspx.designer.cs">
      <DependentUpon>Loan_Verification.aspx</DependentUpon>
    </Compile>
    <Compile Include="Login_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Log_List.aspx.cs">
      <DependentUpon>Log_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Log_List.aspx.designer.cs">
      <DependentUpon>Log_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Log_View.aspx.cs">
      <DependentUpon>Log_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Log_View.aspx.designer.cs">
      <DependentUpon>Log_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="MASTER_VIEW_Select_Loan_Details_By_Loan_No_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Migration_Select_All_Transaction_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="New_Insert_To_tbl_loanapp_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Permissions.aspx.cs">
      <DependentUpon>Permissions.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Permissions.aspx.designer.cs">
      <DependentUpon>Permissions.aspx</DependentUpon>
    </Compile>
    <Compile Include="Personal_Ledger_Manager_List.aspx.cs">
      <DependentUpon>Personal_Ledger_Manager_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Personal_Ledger_Manager_List.aspx.designer.cs">
      <DependentUpon>Personal_Ledger_Manager_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Personal_Ledger_Manager_View.aspx.cs">
      <DependentUpon>Personal_Ledger_Manager_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Personal_Ledger_Manager_View.aspx.designer.cs">
      <DependentUpon>Personal_Ledger_Manager_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Personal_Ledger_Search.aspx.cs">
      <DependentUpon>Personal_Ledger_Search.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Personal_Ledger_Search.aspx.designer.cs">
      <DependentUpon>Personal_Ledger_Search.aspx</DependentUpon>
    </Compile>
    <Compile Include="Personal_Ledger_List.aspx.cs">
      <DependentUpon>Personal_Ledger_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Personal_Ledger_List.aspx.designer.cs">
      <DependentUpon>Personal_Ledger_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Personal_Ledger_View.aspx.cs">
      <DependentUpon>Personal_Ledger_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Personal_Ledger_View.aspx.designer.cs">
      <DependentUpon>Personal_Ledger_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Print_BC.aspx.cs">
      <DependentUpon>Print_BC.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Print_BC.aspx.designer.cs">
      <DependentUpon>Print_BC.aspx</DependentUpon>
    </Compile>
    <Compile Include="Print_Land_Fee.aspx.cs">
      <DependentUpon>Print_Land_Fee.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Print_Land_Fee.aspx.designer.cs">
      <DependentUpon>Print_Land_Fee.aspx</DependentUpon>
    </Compile>
    <Compile Include="Print_Legal_Fee.aspx.cs">
      <DependentUpon>Print_Legal_Fee.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Print_Legal_Fee.aspx.designer.cs">
      <DependentUpon>Print_Legal_Fee.aspx</DependentUpon>
    </Compile>
    <Compile Include="Print_Processing_Fee.aspx.cs">
      <DependentUpon>Print_Processing_Fee.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Print_Processing_Fee.aspx.designer.cs">
      <DependentUpon>Print_Processing_Fee.aspx</DependentUpon>
    </Compile>
    <Compile Include="Print_Receipt.aspx.cs">
      <DependentUpon>Print_Receipt.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Print_Receipt.aspx.designer.cs">
      <DependentUpon>Print_Receipt.aspx</DependentUpon>
    </Compile>
    <Compile Include="Proceedings_Print.aspx.cs">
      <DependentUpon>Proceedings_Print.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Proceedings_Print.aspx.designer.cs">
      <DependentUpon>Proceedings_Print.aspx</DependentUpon>
    </Compile>
    <Compile Include="Profile.aspx.cs">
      <DependentUpon>Profile.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Profile.aspx.designer.cs">
      <DependentUpon>Profile.aspx</DependentUpon>
    </Compile>
    <Compile Include="Project_Add.aspx.cs">
      <DependentUpon>Project_Add.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Project_Add.aspx.designer.cs">
      <DependentUpon>Project_Add.aspx</DependentUpon>
    </Compile>
    <Compile Include="Project_Edit.aspx.cs">
      <DependentUpon>Project_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Project_Edit.aspx.designer.cs">
      <DependentUpon>Project_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="Project_List.aspx.cs">
      <DependentUpon>Project_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Project_List.aspx.designer.cs">
      <DependentUpon>Project_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Main.Master.cs">
      <DependentUpon>Main.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Main.Master.designer.cs">
      <DependentUpon>Main.Master</DependentUpon>
    </Compile>
    <Compile Include="Receipt_Application_Isuue.aspx.cs">
      <DependentUpon>Receipt_Application_Isuue.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Receipt_Application_Isuue.aspx.designer.cs">
      <DependentUpon>Receipt_Application_Isuue.aspx</DependentUpon>
    </Compile>
    <Compile Include="Receipt_BC_Amount_By_Reg_No_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Receipt_Beneficiary_Contribution.aspx.cs">
      <DependentUpon>Receipt_Beneficiary_Contribution.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Receipt_Beneficiary_Contribution.aspx.designer.cs">
      <DependentUpon>Receipt_Beneficiary_Contribution.aspx</DependentUpon>
    </Compile>
    <Compile Include="Receipt_Processing_Fee_View.aspx.cs">
      <DependentUpon>Receipt_Processing_Fee_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Receipt_Processing_Fee_View.aspx.designer.cs">
      <DependentUpon>Receipt_Processing_Fee_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reciept_Processing_Fee.aspx.cs">
      <DependentUpon>Reciept_Processing_Fee.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reciept_Processing_Fee.aspx.designer.cs">
      <DependentUpon>Reciept_Processing_Fee.aspx</DependentUpon>
    </Compile>
    <Compile Include="Receipt_Employee_Confirmation_Letter_List.aspx.cs">
      <DependentUpon>Receipt_Employee_Confirmation_Letter_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Receipt_Employee_Confirmation_Letter_List.aspx.designer.cs">
      <DependentUpon>Receipt_Employee_Confirmation_Letter_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Receipt_Loan_Remittane_List.aspx.cs">
      <DependentUpon>Receipt_Loan_Remittane_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Receipt_Loan_Remittane_List.aspx.designer.cs">
      <DependentUpon>Receipt_Loan_Remittane_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Receipt_Miscellaneous_List.aspx.cs">
      <DependentUpon>Receipt_Miscellaneous_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Receipt_Miscellaneous_List.aspx.designer.cs">
      <DependentUpon>Receipt_Miscellaneous_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Receipt_Miscellaneous_View.aspx.cs">
      <DependentUpon>Receipt_Miscellaneous_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Receipt_Miscellaneous_View.aspx.designer.cs">
      <DependentUpon>Receipt_Miscellaneous_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Receipt_Others_List.aspx.cs">
      <DependentUpon>Receipt_Others_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Receipt_Others_List.aspx.designer.cs">
      <DependentUpon>Receipt_Others_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Receipt_Others_View.aspx.cs">
      <DependentUpon>Receipt_Others_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Receipt_Others_View.aspx.designer.cs">
      <DependentUpon>Receipt_Others_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Report_ApplicationStatus_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Report_Application_Issue_Search.aspx.cs">
      <DependentUpon>Report_Application_Issue_Search.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Report_Application_Issue_Search.aspx.designer.cs">
      <DependentUpon>Report_Application_Issue_Search.aspx</DependentUpon>
    </Compile>
    <Compile Include="Report_Application_Issue_View.aspx.cs">
      <DependentUpon>Report_Application_Issue_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Report_Application_Report_Search.aspx.cs">
      <DependentUpon>Report_Application_Report_Search.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Report_Application_Report_Search.aspx.designer.cs">
      <DependentUpon>Report_Application_Report_Search.aspx</DependentUpon>
    </Compile>
    <Compile Include="Report_Application_Report_View.aspx.cs">
      <DependentUpon>Report_Application_Report_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Report_Application_Report_View.aspx.designer.cs">
      <DependentUpon>Report_Application_Report_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Report_Loan_Agreement_Search.aspx.cs">
      <DependentUpon>Report_Loan_Agreement_Search.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Report_Loan_Agreement_Search.aspx.designer.cs">
      <DependentUpon>Report_Loan_Agreement_Search.aspx</DependentUpon>
    </Compile>
    <Compile Include="Report_Loan_Agreement_View.aspx.cs">
      <DependentUpon>Report_Loan_Agreement_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Report_Loan_Agreement_View.aspx.designer.cs">
      <DependentUpon>Report_Loan_Agreement_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Report_Loan_Disbursement_Search.aspx.cs">
      <DependentUpon>Report_Loan_Disbursement_Search.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Report_Loan_Disbursement_Search.aspx.designer.cs">
      <DependentUpon>Report_Loan_Disbursement_Search.aspx</DependentUpon>
    </Compile>
    <Compile Include="Report_Loan_Disbursement_View.aspx.cs">
      <DependentUpon>Report_Loan_Disbursement_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Report_Loan_Disbursement_View.aspx.designer.cs">
      <DependentUpon>Report_Loan_Disbursement_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Report_Loan_Status.aspx.cs">
      <DependentUpon>Report_Loan_Status.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Report_Loan_Status.aspx.designer.cs">
      <DependentUpon>Report_Loan_Status.aspx</DependentUpon>
    </Compile>
    <Compile Include="Report_Loan_Status_Search.aspx.cs">
      <DependentUpon>Report_Loan_Status_Search.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Report_Loan_Status_Search.aspx.designer.cs">
      <DependentUpon>Report_Loan_Status_Search.aspx</DependentUpon>
    </Compile>
    <Compile Include="Report_Transaction_Scroll_View.aspx.cs">
      <DependentUpon>Report_Transaction_Scroll_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Report_Transaction_Scroll_View.aspx.designer.cs">
      <DependentUpon>Report_Transaction_Scroll_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Report_Transaction_Scroll_Search.aspx.cs">
      <DependentUpon>Report_Transaction_Scroll_Search.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Report_Transaction_Scroll_Search.aspx.designer.cs">
      <DependentUpon>Report_Transaction_Scroll_Search.aspx</DependentUpon>
    </Compile>
    <Compile Include="Role_Add.aspx.cs">
      <DependentUpon>Role_Add.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Role_Add.aspx.designer.cs">
      <DependentUpon>Role_Add.aspx</DependentUpon>
    </Compile>
    <Compile Include="Role_Edit.aspx.cs">
      <DependentUpon>Role_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Role_Edit.aspx.designer.cs">
      <DependentUpon>Role_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="Role_List.aspx.cs">
      <DependentUpon>Role_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Role_List.aspx.designer.cs">
      <DependentUpon>Role_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Role_Permission.aspx.cs">
      <DependentUpon>Role_Permission.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Role_Permission.aspx.designer.cs">
      <DependentUpon>Role_Permission.aspx</DependentUpon>
    </Compile>
    <Compile Include="Schemes_Add.aspx.cs">
      <DependentUpon>Schemes_Add.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Schemes_Add.aspx.designer.cs">
      <DependentUpon>Schemes_Add.aspx</DependentUpon>
    </Compile>
    <Compile Include="Schemes_Edit.aspx.cs">
      <DependentUpon>Schemes_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Schemes_Edit.aspx.designer.cs">
      <DependentUpon>Schemes_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="Schemes_List.aspx.cs">
      <DependentUpon>Schemes_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Schemes_List.aspx.designer.cs">
      <DependentUpon>Schemes_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Sector_Add.aspx.cs">
      <DependentUpon>Sector_Add.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Sector_Add.aspx.designer.cs">
      <DependentUpon>Sector_Add.aspx</DependentUpon>
    </Compile>
    <Compile Include="Sector_Edit.aspx.cs">
      <DependentUpon>Sector_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Sector_Edit.aspx.designer.cs">
      <DependentUpon>Sector_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="Sector_List.aspx.cs">
      <DependentUpon>Sector_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Sector_List.aspx.designer.cs">
      <DependentUpon>Sector_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Agency_By_Cast_And_Scheme_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Agency_By_ID_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Agency_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Agency_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_ApplicationStatus_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Bank_Details_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Block_By_District_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Cast_By_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Cast_By_Scheme_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Cast_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Constituency_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Corporation_By_District_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Disbursement_Details_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Districts_By_Village_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Districts_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_EmpSurety_By_Lnaccid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_IFSC_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Lnaaccdtls_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_LoanDisburment_By_Lnaccid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Loanee_Details_By_Lnaccid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_LoanRepayment_Lnaccid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_loanTrans_LoanNo_Migration_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_loanTrans_LoanNo_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Lok_Sabha_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Municipality_By_District_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Offices_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_OtherRcpt_By_Lnaccid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Panchayath_By_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Panjayath_By_Block_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Personal_Ledger_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_PostOffices_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Processing_Fee_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Projects_ACTIVE_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Projects_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Project_By_ID_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Roles_By_ID_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Roles_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Schemes_By_ID_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Schemes_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Sectors_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Sector_By_ID_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Sub_Cast_By_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Sub_Cast_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Sub_Districts_By_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Sub_Districts_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Surety_Confirmation_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Surety_Type_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Taluk_By_Office_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_course_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Department_int_deptid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Department_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Designations_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_EmpSurety_By_LoanAppId_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_FDLICSurety_By_LoanAppId_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_KSDC_SMART_Issues_All_Branches_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_KSDC_SMART_Issues_Attachments_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_KSDC_SMART_Issues_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_LandSurety_By_LoanAppId_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Leads_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_LoanApp_By_Status_BenifContribution_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_LoanApp_By_Status_Disbursement_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_LoanApp_By_Status_Employee_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_LoanApp_By_Status_Remittance_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_LoanApp_By_Status_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_LoanApp_By_Status_Surety_Confirm_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_LoanApp_By_Status_Surety_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_loanreg_By_LoanNo_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Loan_App_Issue_By_ID_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Loan_App_Issue_Print_List_For_Branches_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Loan_App_Issue_Print_List_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Loan_App_Issue_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Logs_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Projects_By_Sector_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Sectors_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_State_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Taluk_By_District_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_tbl_Taluk_By_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Users_By_ID_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Users_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Villages_By_Taluk_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Villages_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Village_By_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_All_Village_By_Office_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Interest_Penal_Due_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Interest_Rates_By_LoanNo_And_Date_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Last_Loan_Trans_Details_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Last_Principle_Amount_By_Loan_No_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Last_Reg_Details_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_LoanAppId_By_LoanNo_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_LoanApp_By_IssueId_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Loan_Closure_Approval_By_Loan_No_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Lonees_By_Reg_No_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Lonee_Details_And_Account_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Lonee_Details_By_Loan_No_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Offices_By_Office_Code_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_OTS_Waiver_Details_By_Loan_No_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Period_Over_Check_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Pricipale_Due_New_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Pricipale_Due_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_RR_By_LoanNo_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_RR_Details_By_Loan_No_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Scheme_By_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_SR_By_LoanNo_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_SR_Details_By_Loan_No_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Sub_Caste_Details_By_Sub_Caste_Name_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_agriloan_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_BankDetails_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_disbursement_By_RegNo_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_eduloanapp_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_edu_amtreqest_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_empsurety_By_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_FDLICsurety_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_foreignloan_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_House_Loan_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_hsemaintanenceloan_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_KSDC_SMART_Issues_By_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_KSDC_SMART_Issues_Messages_By_Issue_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_landsurety_By_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_LoanApp_By_LoanAppId_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_loanapp_By_Reg_No_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_loanreg_By_RegNo_Or_LoanNo_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_Logs_By_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_marriageloan_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_personal_loan_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_selfloan_By_loanappid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_Sub_Reg_Office_By_District_Id_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_tbl_workcapital_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_To_tbl_veh_hme_loan_By_int_loanappid_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Select_Year_End_Record_Result.cs">
      <DependentUpon>KSDC_LIVE_Entities.tt</DependentUpon>
    </Compile>
    <Compile Include="Settings.aspx.cs">
      <DependentUpon>Settings.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Settings.aspx.designer.cs">
      <DependentUpon>Settings.aspx</DependentUpon>
    </Compile>
    <Compile Include="Site.Mobile.Master.cs">
      <DependentUpon>Site.Mobile.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Site.Mobile.Master.designer.cs">
      <DependentUpon>Site.Mobile.Master</DependentUpon>
    </Compile>
    <Compile Include="Smart_Report_Transaction_Scroll_Search.aspx.cs">
      <DependentUpon>Smart_Report_Transaction_Scroll_Search.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Smart_Report_Transaction_Scroll_Search.aspx.designer.cs">
      <DependentUpon>Smart_Report_Transaction_Scroll_Search.aspx</DependentUpon>
    </Compile>
    <Compile Include="Smart_Report_Transaction_View.aspx.cs">
      <DependentUpon>Smart_Report_Transaction_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Smart_Report_Transaction_View.aspx.designer.cs">
      <DependentUpon>Smart_Report_Transaction_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surety_Confirmation.aspx.cs">
      <DependentUpon>Surety_Confirmation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surety_Confirmation.aspx.designer.cs">
      <DependentUpon>Surety_Confirmation.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surety_Confirmation_List.aspx.cs">
      <DependentUpon>Surety_Confirmation_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surety_Confirmation_List.aspx.designer.cs">
      <DependentUpon>Surety_Confirmation_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surety_Confirmation_MGR.aspx.cs">
      <DependentUpon>Surety_Confirmation_MGR.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surety_Confirmation_MGR.aspx.designer.cs">
      <DependentUpon>Surety_Confirmation_MGR.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surety_Confirmation_MGR_List.aspx.cs">
      <DependentUpon>Surety_Confirmation_MGR_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surety_Confirmation_MGR_List.aspx.designer.cs">
      <DependentUpon>Surety_Confirmation_MGR_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surety_Employee_Edit.aspx.cs">
      <DependentUpon>Surety_Employee_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surety_Employee_Edit.aspx.designer.cs">
      <DependentUpon>Surety_Employee_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surety_Employee_View.aspx.cs">
      <DependentUpon>Surety_Employee_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surety_Employee_View.aspx.designer.cs">
      <DependentUpon>Surety_Employee_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surety_Employee_List.aspx.cs">
      <DependentUpon>Surety_Employee_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surety_Employee_List.aspx.designer.cs">
      <DependentUpon>Surety_Employee_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surety_FD_LIC_Edit.aspx.cs">
      <DependentUpon>Surety_FD_LIC_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surety_FD_LIC_Edit.aspx.designer.cs">
      <DependentUpon>Surety_FD_LIC_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surety_FD_LIC_List.aspx.cs">
      <DependentUpon>Surety_FD_LIC_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surety_FD_LIC_List.aspx.designer.cs">
      <DependentUpon>Surety_FD_LIC_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surety_FD_LIC_View.aspx.cs">
      <DependentUpon>Surety_FD_LIC_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surety_FD_LIC_View.aspx.designer.cs">
      <DependentUpon>Surety_FD_LIC_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surety_Land_Edit.aspx.cs">
      <DependentUpon>Surety_Land_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surety_Land_Edit.aspx.designer.cs">
      <DependentUpon>Surety_Land_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surety_Land_List.aspx.cs">
      <DependentUpon>Surety_Land_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surety_Land_List.aspx.designer.cs">
      <DependentUpon>Surety_Land_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surety_Land_View.aspx.cs">
      <DependentUpon>Surety_Land_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surety_Land_View.aspx.designer.cs">
      <DependentUpon>Surety_Land_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surety_Modification_List.aspx.cs">
      <DependentUpon>Surety_Modification_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surety_Modification_List.aspx.designer.cs">
      <DependentUpon>Surety_Modification_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surety_Modification_List_JS.aspx.cs">
      <DependentUpon>Surety_Modification_List_JS.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surety_Modification_List_JS.aspx.designer.cs">
      <DependentUpon>Surety_Modification_List_JS.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surety_Modification_List_MGR.aspx.cs">
      <DependentUpon>Surety_Modification_List_MGR.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surety_Modification_List_MGR.aspx.designer.cs">
      <DependentUpon>Surety_Modification_List_MGR.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surety_personal_Edit.aspx.cs">
      <DependentUpon>Surety_personal_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surety_personal_Edit.aspx.designer.cs">
      <DependentUpon>Surety_personal_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surety_Personal_List.aspx.cs">
      <DependentUpon>Surety_Personal_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surety_Personal_List.aspx.designer.cs">
      <DependentUpon>Surety_Personal_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surety_Personal_View.aspx.cs">
      <DependentUpon>Surety_Personal_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surety_Personal_View.aspx.designer.cs">
      <DependentUpon>Surety_Personal_View.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surety_Verification.aspx.cs">
      <DependentUpon>Surety_Verification.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surety_Verification.aspx.designer.cs">
      <DependentUpon>Surety_Verification.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surety_Verification_List.aspx.cs">
      <DependentUpon>Surety_Verification_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surety_Verification_List.aspx.designer.cs">
      <DependentUpon>Surety_Verification_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="UrlEncryptor.cs" />
    <Compile Include="User_Add.aspx.cs">
      <DependentUpon>User_Add.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="User_Add.aspx.designer.cs">
      <DependentUpon>User_Add.aspx</DependentUpon>
    </Compile>
    <Compile Include="User_Edit.aspx.cs">
      <DependentUpon>User_Edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="User_Edit.aspx.designer.cs">
      <DependentUpon>User_Edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="User_List.aspx.cs">
      <DependentUpon>User_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="User_List.aspx.designer.cs">
      <DependentUpon>User_List.aspx</DependentUpon>
    </Compile>
    <Compile Include="ViewSwitcher.ascx.cs">
      <DependentUpon>ViewSwitcher.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ViewSwitcher.ascx.designer.cs">
      <DependentUpon>ViewSwitcher.ascx</DependentUpon>
    </Compile>
    <Compile Include="WebService.asmx.cs">
      <DependentUpon>WebService.asmx</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Welcome.aspx.cs">
      <DependentUpon>Welcome.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Welcome.aspx.designer.cs">
      <DependentUpon>Welcome.aspx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="Reports\" />
    <Folder Include="Uploads\SMART_Issues\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
    <Content Include="Scripts\jquery-3.4.1.slim.min.map" />
    <Content Include="Scripts\jquery-3.4.1.min.map" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
    <Service Include="{C0C07587-41A7-46C8-8FBD-3F9C8EBE2DDC}" />
  </ItemGroup>
  <!-- Crystal Reports .rpt files commented out due to missing assemblies -->
  <!--
  <ItemGroup>
    <EmbeddedResource Include="Crystal_Reports\AppIssueRpt.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>AppIssueRpt.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Crystal_Reports\ApplicationReceivedSanctionedReport.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>ApplicationReceivedSanctionedReport.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Crystal_Reports\DisburseReport.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>DisburseReport.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Crystal_Reports\LoanAgreementRpt.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>LoanAgreementRpt.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  -->
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>54043</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:54043/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>