﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Report_Loan_Disbursement_Search.aspx.cs" Inherits="KSDCSCST_Portal.Report_Loan_Disbursement" %>
<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- DataTables -->
    <link rel="stylesheet" href="assets/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">

    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">




    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Loan Disbursement Report</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="Dashboard.aspx">Home</a></li>
                        <li class="breadcrumb-item active">Enhanced Search</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        -
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6 mx-auto">


                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Filter</h3>

                            <div style="float: right; display: none;"><a onclick="GeneratePDF()" style="background: #0f6caa; border-color: #0f6caa;" class="btn btn-block btn-success">Generate PDF</a></div>
                        </div>
                        <!-- /.card-header -->


                        <div class="card-body">
                            <div class="tab-content">
                                <div class="active tab-pane" id="settings">
 
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Select Office*</label>
                                        <div class="col-sm-9">
                                             <select id="dropOffice" class="form-control">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">From Date*</label>
                                        <div class="col-sm-9">
                                            <input type="date" class="dateandtime form-control" id="txt_From_Date">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">To Date*</label>
                                        <div class="col-sm-9">
                                            <input type="date" class="dateandtime form-control" id="txt_To_Date">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Fund*</label>
                                        <div class="col-sm-9">
                                            <select id="dropFund" class="form-control">
                                                <option selected>Select</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Loan Scheme*</label>
                                        <div class="col-sm-9">
                                            <select id="dropScheme" class="form-control">
                                                <option selected>Select</option>

                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Caste*</label>
                                        <div class="col-sm-9">
                                            <select id="dropCast" class="form-control">
                                               
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Sub Caste*</label>
                                        <div class="col-sm-9">
                                            <select id="dropSubCast" class="form-control">
                                                <option selected>Select</option>

                                            </select>
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Sex*</label>
                                        <div class="col-sm-9">
                                            <select id="dropSex" class="form-control">
                                                <option value="0">Select</option>
                                                <option value="All">All</option>
                                                <option value="Male">Male</option>
                                                <option value="Female">Female</option>
                                                <option value="Other">Other</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Taluk*</label>
                                        <div class="col-sm-9">
                                            <select id="dropTaluk" class="form-control">
                                                <option selected>Select</option>

                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Village*</label>
                                        <div class="col-sm-9">
                                            <select id="dropVillage" class="form-control">
                                                <option selected>Select</option>

                                            </select>
                                        </div>
                                    </div>


                                    
<%--


                                    <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Local Body *</label>
                                        <div class="col-sm-9">
                                            <select id="dropLocalBody" class="form-control">
                                                <option value="0">Select</option>
                                                <option value="All">All</option>
                                                <option value="1">Panchayat</option>
                                                <option value="2">Municipality</option>
                                                <option value="3">Corporation</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row" id="DIV_Block" style="display: none;">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Block *</label>
                                        <div class="col-sm-9">
                                            <select id="dropBlock" class="form-control">
                                                <option value="0">Select Block</option>


                                            </select>
                                        </div>
                                    </div>
                                    <div id="DIV_Panchayat" style="display: none;" class="form-group row">
                                        <label class="col-sm-3 col-form-label">Panchayat *</label>
                                        <div class="col-sm-9">
                                            <select id="dropPanchayat" class="form-control">
                                                <option value="0">Select</option>


                                            </select>
                                        </div>
                                    </div>
                                    <div id="DIV_Municipality" style="display: none;" class="form-group row">
                                        <label class="col-sm-3 col-form-label">Municipality *</label>
                                        <div class="col-sm-9">
                                            <select id="dropMunicipality" class="form-control">
                                                <option value="0">Select</option>


                                            </select>
                                        </div>
                                    </div>
                                    <div id="DIV_Corporation" style="display: none;" class="form-group row">
                                        <label class="col-sm-3 col-form-label">Corporation *</label>
                                        <div class="col-sm-9">
                                            <select id="dropCorporation" class="form-control">
                                                <option value="0">Select</option>


                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Lok-Sabha *</label>
                                        <div class="col-sm-9">
                                            <select id="dropLokSabha" class="form-control">
                                                <option value="0">Select</option>


                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Assembly *</label>
                                        <div class="col-sm-9">
                                            <select id="dropConstituency" class="form-control">
                                                <option value="0">Select</option>
                                            </select>
                                        </div>
                                    </div>


                                    <div class="form-group row" id="DIV_Local_Body_Input" style="display: none;">
                                        <label id="lblLocal_Body" class="col-sm-3 col-form-label"></label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtLocal_Body" value="0" placeholder="">
                                        </div>
                                    </div>

--%>

                                    <section class="content">
                                        <div class="container-fluid">

                                            <form action="enhanced-results.html">
                                                <div class="row">
                                                    <div class="col-md-10 offset-md-3">
                                                        <div class="row">

                                                            <div class="col-3">
                                                                <div class="form-group">
                                                                    <label>Sort Order:</label>
                                                                     <select id="dropSortOrder" class="form-control">
                                                                                    <option value="ASC" selected>Ascending</option>
                                                                        <option value="DESC">Descending</option>
                                            </select>
                                                                </div>
                                                            </div>
                                                            <div class="col-3">
                                                                <div class="form-group">
                                                                    <label>Order By:</label>
                                                                    <select id="dropOrderBy" class="form-control">
                                                                                    <option selected value="Loan No">Loan No</option>
                                                                                    <option value="Applicant Name">Applicant Name</option>
                                                                                    <option value="Fund">Fund</option>
                                                                                    <option value="Loan Scheme">Loan Scheme</option>
                                                                                    <option value="Disburse Date">Disburse Date</option>
                                                                                    <option value="Taluk">Taluk</option>
                                                                                    <option value="Village">Village</option>
                                                                                    <option value="Caste">Caste</option>

                                            </select>
                                                                </div>
                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </section>













                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">

                                            <a onclick="Save();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>





























                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->










    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 -->
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <!-- Bootstrap4 Duallistbox -->
    <script src="assets/plugins/bootstrap4-duallistbox/jquery.bootstrap-duallistbox.min.js"></script>
    <!-- InputMask -->
    <script src="assets/plugins/moment/moment.min.js"></script>
    <script src="assets/plugins/inputmask/jquery.inputmask.min.js"></script>
    <!-- date-range-picker -->
    <script src="assets/plugins/daterangepicker/daterangepicker.js"></script>
    <!-- bootstrap color picker -->
    <script src="assets/plugins/bootstrap-colorpicker/js/bootstrap-colorpicker.min.js"></script>
    <!-- Tempusdominus Bootstrap 4 -->
    <script src="assets/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js"></script>
    <!-- BS-Stepper -->
    <script src="assets/plugins/bs-stepper/js/bs-stepper.min.js"></script>
    <!-- dropzonejs -->
    <script src="assets/plugins/dropzone/min/dropzone.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script src="assets/js/jquery.dateandtime.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>


    <script>

        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;
        const $searchInput = $('#txtPincode');
        const $searchInput_IFSC = $('#txtIFSC');

        var Rural_Max = "";
        var Urban_Max = "";
        var Min_Age = "";
        var Max_Age = "";
        var Loan_Amount = "";
        var Loan_Period = "";




        // Results container element
        const $searchResults = $('#search-results');
        const $searchResults_IFSC = $('#search-results-ifsc');
        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }
        $(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtApplication_Date').val(formattedDate);


            //Date picker
            //$('#txtDOB').datetimepicker({
            //    format: 'DD/MM/YYYY',
            //    allowInputToggle: false
            //});

            //$('#txtRetirement').datetimepicker({
            //    format: 'DD/MM/YYYY',
            //    allowInputToggle: false
            //});
            $('#txtDOB').dateAndTime();
            $('#txtRetirement').dateAndTime();




            // Load_All_Districts();



        //    Load_All_Schemes();
            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            


            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }



            //$(document).on("input", ".form-control", function () {
            //    // Get the current value of the input
            //    var currentValue = $(this).val();

            //    // Convert the value to propcase
            //    var propcaseValue = currentValue.replace(/\w\S*/g, function (txt) {
            //        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
            //    });

            //    // Update the input with the propcase value
            //    $(this).val(propcaseValue);
            //});


            $searchInput.on('input', function () {
                const query = $searchInput.val();
                fetchAutocompleteResults(query);
            });

            $searchInput_IFSC.on('input', function () {
                const query = $searchInput_IFSC.val();
                fetchAutocompleteResults_IFSC(query);
            });



            // Event handler for selecting a result
            $searchResults.on('click', 'li', function () {
                $("#txtPincode").val($(this).attr("data-pincode"));
                $("#txtPostOffices").val($(this).html());
                //  const selectedResult = $(this).text();
                //  $searchInput.val(selectedResult);
                $searchResults.hide();
            });

            $searchResults_IFSC.on('click', 'li', function () {
                $("#txtIFSC").val($(this).attr("data-vchr_IFSC"));
                $("#txtBank_Name").val($(this).attr("data-vchr_Bank"));
                $("#txtBranch").val($(this).attr("data-vchr_Branch"));
                //  const selectedResult = $(this).text();
                //  $searchInput.val(selectedResult);
                $searchResults_IFSC.hide();
            });


            $("#dropScheme").on("change", function () {
               

            });

        })

        $(document).ready(function () {


            Load_Offices_By_Office_Code();


            $("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
            $("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");


            $("#dropLocalBody").on("change", function () {
                var selectedValue = $(this).val();

                $("#dropPanchayat").val("0");
                $("#dropMunicipality").val("0");
                $("#dropCorporation").val("0");

                if (selectedValue == "0" || selectedValue == "All") {
                    $("#DIV_Panchayat").css("display", "none");
                    $("#DIV_Municipality").css("display", "none");
                    $("#DIV_Corporation").css("display", "none");
                    $("#DIV_Block").css("display", "none");

                }
                else if (selectedValue == "1") {
                    $("#DIV_Panchayat").css("display", "flex");
                    $("#DIV_Municipality").css("display", "none");
                    $("#DIV_Corporation").css("display", "none");
                    $("#DIV_Block").css("display", "flex");
                    Load_All_Block(parseInt($("#dropOffice").val().toString().substring(0, 2), 10));
                }
                else if (selectedValue == "2") {
                    $("#DIV_Panchayat").css("display", "none");
                    $("#DIV_Municipality").css("display", "flex");
                    $("#DIV_Corporation").css("display", "none");
                    $("#DIV_Block").css("display", "none");
                    Load_All_Municipality(parseInt($("#dropOffice").val().toString().substring(0, 2), 10));
                }
                else if (selectedValue == "3") {
                    $("#DIV_Panchayat").css("display", "none");
                    $("#DIV_Municipality").css("display", "none");
                    $("#DIV_Corporation").css("display", "flex");
                    $("#DIV_Block").css("display", "none");
                    Load_All_Corporation(parseInt($("#dropOffice").val().toString().substring(0, 2), 10));
                }

            });



        });


        function Load_Offices_By_Office_Code() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_Offices_By_Office_Code",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropOffice').empty();
                    // $('#dropOffice').append('<option value="0">Select</option>');
                    $.each(data.d, function (key, value) {
                        var OfficeCode = value.Office_Code;
                        var Office = value.Office_Name;
                        var html = '<option value="' + OfficeCode + '">' + Office + '</option>';
                        $('#dropOffice').append(html);
                    });
                },
                error: function (error) {
                    //alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Agency();
            });

        }

        function Load_All_Agency() {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Agency",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropFund').empty();
                    $('#dropFund').append('<option value="0">Select</option>');
                    $('#dropFund').append('<option value="All">All</option>');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Name = value.Name;
                        if (Name.length > 0) {
                            var html = '<option value="' + Name + '">' + Name + '</option>';
                            $('#dropFund').append(html);
                        }
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_All_Schemes();
            });
        }
         
        function Load_All_Sub_Districts(District_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Districts",
                data: JSON.stringify({ District_Id: District_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubDistrict').empty();
                    $('#dropSubDistrict').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Sub_District_Name = value.Sub_District_Name;
                        var html = '<option value="' + Id + '">' + Sub_District_Name + '</option>';

                        $('#dropSubDistrict').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });

        }
         

        function Load_All_Schemes() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Schemes",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropScheme').empty();
                    $('#dropScheme').append('<option value="0">Select</option>');
                    $('#dropScheme').append('<option value="All">All</option>');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var IsActive = value.IsActive;
                        var Scheme = value.Scheme;
                        var html = '<option value="' + Id + '">' + Scheme + '</option>';
                        if (IsActive == 1) {
                            $('#dropScheme').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_All_Cast();

            });

        }

        function Load_All_Cast() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Cast",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropCast').empty();
                    $('#dropCast').append('<option value="0">Select</option>');
                    $('#dropCast').append('<option value="All">All</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var Cast = value.Cast;
                        var html = '<option value="' + Id + '">' + Cast + '</option>';

                        $('#dropCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                

                $('#dropCast').change(function () {

                    if ($(this).val() == "All") {
                        Get_All_Sub_Caste();
                    }
                    else {
                    Load_All_SubCast($(this).val());
                    }
                });

                Load_All_Taluk();
            });

        }

        function Get_All_Sub_Caste() {


            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Get_All_Sub_Caste",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubCast').empty();
                    $('#dropSubCast').append('<option value="0">Select</option>');
                    $('#dropSubCast').append('<option value="All">All</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var SubCast = value.SubCast;
                        var html = '<option value="' + Id + '">' + SubCast + '</option>';

                        $('#dropSubCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {


            });

        }

        function Load_All_SubCast(Cast_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Cast",
                data: JSON.stringify({ Cast_Id: Cast_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubCast').empty();
                    $('#dropSubCast').append('<option value="0">Select</option>');
                    $('#dropSubCast').append('<option value="All">All</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var SubCast = value.SubCast;
                        var html = '<option value="' + Id + '">' + SubCast + '</option>';

                        $('#dropSubCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {


            });

        }

        var District_Id;
        var SubDistrict_id;
        var Cast_Id;
        var Sub_Cast_Id;

        function Load_All_Village_By_Office_Id(office_Id)
        {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Villages_By_Office_Id",
                data: JSON.stringify({ office_Id: office_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropVillage').empty();
                    $('#dropVillage').append('<option value="0">Select</option>');
                    $('#dropVillage').append('<option value="All">All</option>');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Village = value.Village;
                        var html = '<option value="' + Id + '">' + Village + '</option>';
                        if (Id != 15) {
                            $('#dropVillage').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                
            });

        }
        function Load_All_Village_By_Taluk_Id(Taluk_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Villages_By_Taluk_Id",
                data: JSON.stringify({ Taluk_Id: Taluk_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropVillage').empty();
                    $('#dropVillage').append('<option value="0">Select</option>');
                    $('#dropVillage').append('<option value="All">All</option>');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Village = value.Village;
                        var html = '<option value="' + Id + '">' + Village + '</option>';
                        if (Id != 15) {
                            $('#dropVillage').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });

        }
        function Load_All_Lok_Sabha() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Lok_Sabha_By_District_Id",
                // data: JSON.stringify({ block_Id: block_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropLokSabha').empty();
                    $('#dropLokSabha').append('<option value="0">Select</option>');
                    $('#dropLokSabha').append('<option value="All">All</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Lok_Sabha = value.Lok_Sabha;
                        var html = '<option value="' + Id + '">' + Lok_Sabha + '</option>';

                        $('#dropLokSabha').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                
                $('#dropLokSabha').change(function () {

                    if ($(this).val() == "All") {
                        $('#dropConstituency').empty();
                        $('#dropConstituency').append('<option value="0">Select</option>');
                        $('#dropConstituency').append('<option value="All">All</option>');
                    }
                    else {
                        Load_All_Constituency($(this).val());
                    }
                });

            });

        }


        function Load_All_SubCast_And_Set(Cast_Id, Sub_Cast_Id) {


            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Cast",
                data: JSON.stringify({ Cast_Id: Cast_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubCast').empty();
                    $('#dropSubCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var SubCast = value.SubCast;
                        var html = '<option value="' + Id + '">' + SubCast + '</option>';

                        $('#dropSubCast').append(html);

                    });



                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
             

                $("#dropSubCast").val(Sub_Cast_Id);
                //   Load_All_Taluk($("#dropSubCast").val());

            });

        }
        function Load_All_Taluk() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Taluk_By_Office_Id",
                data: JSON.stringify({ office_Id: $("#dropOffice").val() }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropTaluk').empty();
                     $('#dropTaluk').append('<option value="0">Select</option>');
                    $('#dropTaluk').append('<option value="All">All</option>');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Taluk = value.Taluk;
                        var html = '<option value="' + Id + '">' + Taluk + '</option>';
                        if (Id != 15) {
                            $('#dropTaluk').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $('#dropTaluk').change(function () {

                    if ($(this).val() == "All") {
                        Load_All_Village_By_Office_Id($("#dropOffice").val());
                    }
                    else {
                        Load_All_Village_By_Taluk_Id($(this).val());
                    }
                });
                Load_All_Lok_Sabha();
            });

        }

        //  dropPanjayath

        



        function Load_All_Block(district_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Block_By_District_Id",
                data: JSON.stringify({ district_Id: district_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropBlock').empty();
                    $('#dropBlock').append('<option value="0">Select</option>');
                    $('#dropBlock').append('<option value="All">All</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Block = value.Block;
                        var html = '<option value="' + Id + '">' + Block + '</option>';

                        $('#dropBlock').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
               // Load_All_Municipality(district_Id);

                $('#dropBlock').change(function () {

                    if ($(this).val() == "All") {
                        $('#dropPanchayat').empty();
                        $('#dropPanchayat').append('<option value="0">Select</option>');
                        $('#dropPanchayat').append('<option value="All">All</option>');
                    }
                    else {
                        Load_All_Panjayath($(this).val());
                    }

                });

                
            });

        }


        


        function Load_All_Constituency(Lok_Sabha_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Constituency_By_Lok_Sabha_Id",
                data: JSON.stringify({ lok_Sabha_Id: Lok_Sabha_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropConstituency').empty();
                    $('#dropConstituency').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Constituency = value.Constituency;
                        var html = '<option value="' + Id + '">' + Constituency + '</option>';

                        $('#dropConstituency').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                
            });

        }


        function Load_All_Panjayath(block_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Panjayath_By_Block_Id",
                data: JSON.stringify({ block_Id: block_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropPanchayat').empty();
                    $('#dropPanchayat').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Panjayath = value.Panjayath;
                        var html = '<option value="' + Id + '">' + Panjayath + '</option>';

                        $('#dropPanchayat').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });

        }

        function Load_All_Municipality(District_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Municipality_By_District_Id",
                data: JSON.stringify({ District_Id: District_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropMunicipality').empty();
                    $('#dropMunicipality').append('<option value="0">Select</option>');
                    $('#dropMunicipality').append('<option value="All">All</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Municipality = value.Municipality;
                        var html = '<option value="' + Id + '">' + Municipality + '</option>';

                        $('#dropMunicipality').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
               // Load_All_Corporation(District_Id);
            });

        }


        function Load_All_Corporation(District_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Corporation_By_District_Id",
                data: JSON.stringify({ District_Id: District_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropCorporation').empty();
                    $('#dropCorporation').append('<option value="0">Select</option>');
                    $('#dropCorporation').append('<option value="All">All</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Corporation = value.Corporation;
                        var html = '<option value="' + Id + '">' + Corporation + '</option>';

                        $('#dropCorporation').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });

        }


       
         


        function Save() {

            var Scheme = $("#dropScheme");
            var Cast = $("#dropCast");
            var SubCast = $("#dropSubCast");
            var Taluk = $("#dropTaluk");
            var Village = $("#dropVillage");
            var Block = $("#dropBlock");
            var LocalBody = $("#dropLocalBody");
            var Panchayat = $("#dropPanchayat");
            var Municipality = $("#dropMunicipality");
            var Corporation = $("#dropCorporation");
            var LokSabha = $("#dropLokSabha");
            var Constituency = $("#dropConstituency");

            var Office = $("#dropOffice");
            var dtFrom = $("#txt_From_Date");
            var dtTo = $("#txt_To_Date");
            var Fund = $("#dropFund");
            var Sex = $("#dropSex");

            if (Office.val().trim() == "0") {
                Focus_Error(Office);
                Toast.fire({
                    icon: 'error',
                    title: 'Office is required !'
                })
            }
            else if (dtFrom.val().trim() == "") {
                Focus_Error(dtFrom);
                Toast.fire({
                    icon: 'error',
                    title: 'From Date is required !'
                })
            }
            else if (dtTo.val().trim() == "") {
                Focus_Error(dtTo);
                Toast.fire({
                    icon: 'error',
                    title: 'To Date is required !'
                })
            }
            else if (Fund.val().trim() == "0") {
                Focus_Error(Fund);
                Toast.fire({
                    icon: 'error',
                    title: 'Fund is required !'
                })
            }
            else if (Scheme.val().trim() == "0") {
                Focus_Error(Scheme);
                Toast.fire({
                    icon: 'error',
                    title: 'Scheme is required !'
                })
            }
            else if (Cast.val().trim() == "0") {
                Focus_Error(Cast);
                Toast.fire({
                    icon: 'error',
                    title: 'Caste is required !'
                })
            }
            else if (SubCast.val().trim() == "0") {
                Focus_Error(SubCast);
                Toast.fire({
                    icon: 'error',
                    title: 'Sub Caste is required !'
                })
            }
            else if (Sex.val().trim() == "0") {
                Focus_Error(Sex);
                Toast.fire({
                    icon: 'error',
                    title: 'Sex is required !'
                })
            }
            else if (Taluk.val().trim() == "0") {
                Focus_Error(Taluk);
                Toast.fire({
                    icon: 'error',
                    title: 'Taluk is required !'
                })
            }
            else if (Village.val().trim() == "0") {
                Focus_Error(Village);
                Toast.fire({
                    icon: 'error',
                    title: 'Village is required !'
                })
            }
            //else if (LocalBody.val().trim() == "0") {
            //    Focus_Error(LocalBody);
            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Local Body is required !'
            //    })
            //}
            //else if ((LocalBody.val().trim() == "1") && (Block.val().trim() == "0"))  {
            //    Focus_Error(Block);
            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Block is required !'
            //    })
            //}
            //else if ((LocalBody.val().trim() == "1") && (Block.val().trim() != "0") && (Panchayat.val().trim() == "0")) {
            //    Focus_Error(Panchayat);
            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Panchayat is required !'
            //    })
            //}
            //else if ((LocalBody.val().trim() == "2") && (Municipality.val().trim() == "0")) {
            //    Focus_Error(Municipality);
            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Municipality is required !'
            //    })
            //}
            //else if ((LocalBody.val().trim() == "3") && (Corporation.val().trim() == "0")) {
            //    Focus_Error(Corporation);
            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Corporation is required !'
            //    })
            //}
            //else if (LokSabha.val().trim() == "0") {
            //    Focus_Error(LokSabha);
            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Lok Sabha is required !'
            //    })
            //}
            //else if (Constituency.val().trim() == "0") {
            //    Focus_Error(Constituency);
            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Constituency is required !'
            //    })
            //}
            else {

                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/Report_Loan_Disbursement",
                    data: JSON.stringify({ office_id: Office.val(), fromDate: dtFrom.val(), toDate: dtTo.val(), fund: Fund.val(), scheme: Scheme.val(), caste: Cast.val(), subcaste: SubCast.val(), sex: Sex.val(), taluk: Taluk.val(), village: Village.val(), localbody: 'All', block: 'All', panchayat: 'All', muncipality: 'All', corporation: 'All', loksabha: 'All', assembly: 'All', sortorder: $("#dropSortOrder").val(), orderby: $("#dropOrderBy").val()}),
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        var url = 'Report_Loan_Disbursement_View.aspx';
                        window.open(url, '_blank');
                    },
                    error: function (error) {


                        // alert("error" + error.responseText);
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                        })
                    }
                }).done(function () {

                });

            }
        }



        function Save_Bank_Details() {

            var Bank_Account_No = $("#txtBank_Account_No").val();
            var IFSC = $("#txtIFSC").val();
            var Bank_Name = $("#txtBank_Name").val();
            var Branch = $("#txtBranch").val();


            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Insert_To_tbl_BankDetails",
                data: JSON.stringify({ int_loanappid: getParameterByName("Id"), int_BankAccNo: Bank_Account_No, vchr_IFSC: IFSC, vchr_Bank: Bank_Name, vchr_Branch: Branch }),

                contentType: "application/json; charset=utf-8",
                success: function (data) {

                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                    })
                }
            }).done(function () {
                Next();


            });

        }

        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }
        function Is_Number(inputText) {


            if (isNaN(inputText)) {
                return false;
            } else {
                return true;
            }


        }

        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }

        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }



        function Check_Age_Limit(Age) {
            //var Age = $("#txtAge").val();
            var Min_Age = $("#dropScheme option:selected").attr("data-min_age");
            var Max_Age = $("#dropScheme option:selected").attr("data-max_age");

            Age = parseInt(Age);
            Min_Age = parseInt(Min_Age);
            Max_Age = parseInt(Max_Age);

            if (Min_Age <= Age && Max_Age >= Age) {
                return true;
            }
            else {
                return false;
            }

        }

        function Check_Annual_Income_Limit(Annual_Income) {
            //  var Annual_Income = $("#txtAnnualIncome").val();
            //    var Anninc_Rural_Max = $("#dropScheme option:selected").attr("data-anninc_rural_max");
            //    var Anninc_Urban_Max = $("#dropScheme option:selected").attr("data-anninc_urban_max");

            Annual_Income = parseInt(Annual_Income);
            Rural_Max = parseInt(Rural_Max);
            Urban_Max = parseInt(Urban_Max);

            if (Rural_Max >= Annual_Income && Urban_Max >= Annual_Income) {
                return true;
            }
            else {
                return false;
            }

        }


        function Check_Loan_Amount_Limit(Loan_Amount) {
            //   var Loan_Amount = $("#txtLoanAmount").val();
            var Req_Loan_Amount = $("#dropScheme option:selected").attr("data-loan_amount");


            Loan_Amount = parseInt(Loan_Amount);
            Req_Loan_Amount = parseInt(Req_Loan_Amount);

            if (Loan_Amount <= Req_Loan_Amount) {
                return true;
            }
            else {
                return false;
            }

        }

        function Is_Valid_Aadhar_No(Aadhar_No) {
            var aadharPattern = /^\d{12}$/;

            if (aadharPattern.test(Aadhar_No)) {
                return true;
            } else {
                return false;
            }
        }


        function Is_Valid_RationCard_No(Ration_Card_No) {
            var rationPattern = /^\d{10}$/;

            if (rationPattern.test(Ration_Card_No)) {
                return true;
            } else {
                return false;
            }
        }



    </script>
</asp:Content>