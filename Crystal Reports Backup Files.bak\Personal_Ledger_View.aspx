﻿<%@ Page Title="Personal Ledger View" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Personal_Ledger_View.aspx.cs" Inherits="KSDCSCST_Portal.Personal_Ledger_View" %>




<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <style>
        thead tr th {
            position: sticky !important;
            top: 0;
            background: #f9f9f9;
            z-index: 1;
            border-bottom: 2px solid #ddd;
        }

        table.dataTable > thead .sorting:before, table.dataTable > thead .sorting_asc:before, table.dataTable > thead .sorting_desc:before, table.dataTable > thead .sorting_asc_disabled:before, table.dataTable > thead .sorting_desc_disabled:before {
            right: 1em;
            content: "↑";
            display: none !important;
        }

        table.dataTable > thead .sorting:after, table.dataTable > thead .sorting_asc:after, table.dataTable > thead .sorting_desc:after, table.dataTable > thead .sorting_asc_disabled:after, table.dataTable > thead .sorting_desc_disabled:after {
            display: none !important;
        }
    </style>
    <div id="DIV_Full_Page">
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
        <!-- Font Awesome -->
        <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
        <!-- DataTables -->
        <link rel="stylesheet" href="assets/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
        <link rel="stylesheet" href="assets/plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
        <link rel="stylesheet" href="assets/plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
        <!-- Theme style -->
        <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">

        <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/css/datepicker.css" rel="stylesheet" type="text/css" />


        <script src="assets/plugins/toastr/toastr.min.js"></script>
        <link rel="stylesheet" href="assets/css/main.css">
        <div id="DIV_All_Section">
            <!-- Content Header (Page header) -->
            <section class="content-header">
                <div class="container-fluid">
                    <div class="row mb-2">
                        <div class="col-sm-6">
                            <h1>Personal Ledger Details</h1>
                        </div>
                        <div class="col-sm-6">
                            <ol class="breadcrumb float-sm-right">
                                <li class="breadcrumb-item"><a href="Dashboard.aspx">Home</a></li>
                                <li class="breadcrumb-item active">Personal Ledger Details</li>
                            </ol>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->
            </section>
            <section class="content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="active tab-pane" id="Ledger_Abstract">

                                        <div class="Sub_Header" style="text-align: center;">LOAN LEDGER</div>



                                        <button class="accordion">Loan Details</button>
                                        <div class="panel" style="border: 1px solid #d0c7c7;">
                                            <div class="row">
                                                <div class="col-sm-12">
                                                    <div class="row" style="margin: 1%;">
                                                        <div class="col-sm-12">
                                                            <div class="Sub_Header_Abstract">LOAN DETAILS</div>


                                                            <table class="table_Abstract" style="width: 100%;">
                                                                <tbody>
                                                                    <tr>
                                                                        <td>Loanee Name</td>

                                                                        <td style="">
                                                                            <label id="txtLoanee_Name"></label>
                                                                        </td>



                                                                        <td>Address</td>

                                                                        <td>
                                                                            <label id="txtAddress"></label>
                                                                        </td>


                                                                        <td>Scheme Name</td>

                                                                        <td>
                                                                            <label id="txtScheme_Name"></label>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Old Agr.No</td>

                                                                        <td>
                                                                            <label id="txtOld_Agr_No"></label>
                                                                        </td>

                                                                        <td>Contact Number</td>

                                                                        <td>
                                                                            <label id="txtContact_Number"></label>
                                                                        </td>

                                                                        <td>Due Date</td>

                                                                        <td>
                                                                            <label id="txtDue_Date"></label>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>EMI</td>

                                                                        <td>
                                                                            <label id="txtEMI"></label>
                                                                        </td>

                                                                        <td>Loan Duration</td>

                                                                        <td>
                                                                            <label id="txt_Loan_Duration"></label>
                                                                        </td>
                                                                        <td>New Loan No</td>

                                                                        <td>
                                                                            <label id="txt_New_Loan_No"></label>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Interest Rate (%)</td>

                                                                        <td>
                                                                            <label id="lbl_Interest_Rate"></label>
                                                                        </td>

                                                                        <td>Penal Rate (%)</td>

                                                                        <td>
                                                                            <label id="lbl_Penal_Rate"></label>
                                                                        </td>
                                                                        <td>Action Taken
                                                                        </td>
                                                                        <td>
                                                                            <label id="txtAction_Taken"></label>
                                                                        </td>

                                                                    </tr>

                                                                </tbody>
                                                            </table>


                                                        </div>
                                                    </div>
                                                    <div class="row" style="margin: 1%;">


                                                        <div class="col-sm-6" style="display: none;">

                                                            <div class="Sub_Header_Abstract">ABSTRACT</div>


                                                            <table class="table_Abstract" style="width: 100%; height: 212px !important;">
                                                                <tbody>
                                                                    <tr>
                                                                        <td>Principal</td>

                                                                        <td>
                                                                            <label id="txtTotal_Principal">500000</label>
                                                                        </td>

                                                                    </tr>
                                                                    <tr>
                                                                        <td>Interest</td>

                                                                        <td>
                                                                            <label id="txtTotal_Interest">138281.04</label>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Penal</td>

                                                                        <td>
                                                                            <label id="txtTotal_Penal">9652.91</label>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Remittance</td>

                                                                        <td>
                                                                            <label id="txtTotal_Remittance">304000</label>
                                                                        </td>
                                                                    </tr>
                                                                    <tr style="position: relative; height: 35px;">
                                                                        <td></td>

                                                                        <td></td>

                                                                    </tr>
                                                                </tbody>
                                                            </table>

                                                            <div class="Sub_Header_Abstract">CHARGES/WAIVER/WRITE OFF</div>

                                                            <%-- <table class="table_Abstract" style="width: 100%;">
                                                                <tbody>
                                                                    <tr>
                                                                        <td>Principal Adj.</td>

                                                                        <td>
                                                                            <label id="txtPrincipal_Adj">-15000</label>
                                                                        </td>

                                                                    </tr>
                                                                    <tr>
                                                                        <td>Interest Adj.</td>

                                                                        <td>
                                                                            <label id="txtInterest_Adj">0.00</label>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Penal Adj.</td>

                                                                        <td>
                                                                            <label id="txtPenal_Adj">0.00</label>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>C/B Adj.</td>

                                                                        <td>
                                                                            <label id="txtC/B_Adj">0.00</label>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Action Taken</td>

                                                                        <td>
                                                                            <label id="txtAction_Taken">No Action</label>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Referance Date</td>

                                                                        <td>
                                                                            <label id="txtReferance_Date"></label>
                                                                        </td>
                                                                    </tr>

                                                                </tbody>
                                                            </table>--%>
                                                        </div>


                                                        <div class="col-sm-6">
                                                            <div class="Sub_Header_Abstract">RECOVERED AMOUNT DETAILS</div>


                                                            <table class="table_Abstract" style="width: 100%;">
                                                                <tbody>
                                                                    <tr>
                                                                        <td>Principal</td>

                                                                        <td>
                                                                            <label id="txtRecovered_Principal"></label>
                                                                        </td>

                                                                    </tr>
                                                                    <tr>
                                                                        <td>Interest</td>

                                                                        <td>
                                                                            <label id="txtRecovered_Interest"></label>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Penal</td>

                                                                        <td>
                                                                            <label id="txtRecovered_Penal"></label>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>C/B Adj.</td>

                                                                        <td>
                                                                            <label id="txtRecovered_CB_Adj"></label>
                                                                        </td>
                                                                    </tr>

                                                                    <tr>
                                                                        <td>Total</td>

                                                                        <td>
                                                                            <label id="txtTotal_Recovered_Amount"></label>
                                                                        </td>
                                                                    </tr>
                                                                    <tr id="tr_Closure">
                                                                        <td colspan="2">
                                                                            <input type="hidden" id="Stage" name="Stage" value="">
                                                                            <input type="hidden" id="ForClosing" name="ForClosing" value="0">
                                                                            <span id="SPAN_Closure" style="color: red; font-weight: bold;"></span>
                                                                        </td>
                                                                    </tr>

                                                                </tbody>
                                                            </table>


                                                        </div>

                                                        <div class="col-sm-6">
                                                            <div class="Sub_Header_Abstract">AMOUNT OUTSTANDING</div>

                                                            <table class="table_Abstract" style="width: 100%;">
                                                                <tbody>
                                                                    <tr>
                                                                        <td>Principal</td>

                                                                        <td>
                                                                            <label id="txtPrincipal_Outstanding"></label>
                                                                        </td>

                                                                    </tr>
                                                                    <tr>
                                                                        <td>Interest</td>

                                                                        <td>
                                                                            <label id="txtInterest_Outstanding"></label>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Penal</td>

                                                                        <td>
                                                                            <label id="txtPenal_Outstanding"></label>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>C/B Adj.</td>

                                                                        <td>
                                                                            <label id="txt_CB_Adj">0.00</label>
                                                                        </td>
                                                                    </tr>

                                                                    <tr>
                                                                        <td>Total</td>

                                                                        <td>
                                                                            <label id="txtTotal_Outatanding"></label>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="color: red; font-weight: bold;">Default Amount(Def.EMI+Penal)</td>

                                                                        <td>
                                                                            <label style="color: red; font-weight: bold; font-size: 14px !important;" id="lbl_Def_Amount"></label>
                                                                        </td>
                                                                    </tr>

                                                                    <tr>
                                                                        <td style="color: red; font-weight: bold;">Default Installement</td>

                                                                        <td>
                                                                            <label style="color: red; font-weight: bold; font-size: 14px !important;" id="lbl_Def_Installement"></label>
                                                                        </td>
                                                                    </tr>

                                                                </tbody>
                                                            </table>

                                                        </div>



                                                    </div>
                                                </div>
                                            </div>


                                        </div>






                                    </div>
                                    <div class="row" style="display: none;">
                                        <div class="col-6">
                                            <table border="1" class="Lonee_Details">
                                                <tr>
                                                    <td colspan="2" style="text-align: center;">
                                                        <b>Lonee Details</b>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <b>Name</b>
                                                    </td>
                                                    <td><span id="SPAN_Name"></span></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <b>Address</b>
                                                    </td>
                                                    <td><span id="SPAN_Address"></span></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <b>Old Agr.No</b>
                                                    </td>
                                                    <td><span id="SPAN_OldAgrNo"></span></td>
                                                </tr>

                                                <tr>
                                                    <td>
                                                        <b>Loan No</b>
                                                    </td>
                                                    <td><span id="SPAN_Loan_No"></span></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <b>Due Date</b>
                                                    </td>
                                                    <td><span id="SPAN_DueDate"></span></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <b>EMI</b>
                                                    </td>
                                                    <td><span id="SPAN_EMI"></span></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <b>Loan Duration</b>
                                                    </td>
                                                    <td><span id="SPAN_LoanDuration"></span>Monthly</td>
                                                </tr>

                                            </table>
                                        </div>
                                        <div class="col-6">
                                            <table border="1" class="Lonee_Details">
                                                <tr>
                                                    <td colspan="2" style="text-align: center;">
                                                        <b>Interest Rate(s)</b>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <b>Interest</b>
                                                    </td>
                                                    <td><span id="SPAN_Interest_Rate"></span></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <b>Penal Interest</b>
                                                    </td>
                                                    <td><span id="SPAN_Penal_Rate"></span></td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2" style="text-align: center;">
                                                        <b>Abstract</b>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <b>Principal</b>
                                                    </td>
                                                    <td><span id="SPAN_Principal"></span></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <b>Interst</b>
                                                    </td>
                                                    <td><span id="SPAN_Interest"></span></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <b>Penal</b>
                                                    </td>
                                                    <td><span id="SPAN_Penal"></span></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <b>Remitance</b>
                                                    </td>
                                                    <td><span id="SPAN_Remitance"></span></td>
                                                </tr>

                                            </table>
                                        </div>
                                        <div class="col-4" style="display: none;">
                                            <table border="1" class="Lonee_Details">
                                                <tr>
                                                    <td colspan="2" style="text-align: center;">
                                                        <b>Lonee Details</b>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <b>Name</b>
                                                    </td>
                                                    <td></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <b>Loan No :</b>
                                                    </td>
                                                    <td></td>
                                                </tr>
                                            </table>


                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

            </section>
            <!-- Main content -->
            <section class="content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">


                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">List Personal Ledger Details</h3>
                                    <div class="Display_None">
                                        <div style="float: right; margin-left: 10px;"><a href="Personal_Ledger_List.aspx" class="btn btn-dark">Back to Search</a></div>
                                        <div style="float: right; margin-left: 10px;"><a onclick="Print_Ledger();" class="btn btn-success">Print Ledger</a></div>
                                        <div style="float: right; margin-left: 10px;"><a data-target="#modal_Debit" id="Loan_Debits" data-toggle="modal" class="btn btn-danger">Debits</a></div>

                                        <div style="float: right; margin-left: 10px;"><a data-target="#modal_SR_Update" id="Loan_SR_Update" data-toggle="modal" class="btn btn-danger">SR Update</a></div>
                                        <div style="float: right; margin-left: 10px;"><a data-target="#modal_RR_Update" id="Loan_RR_Update" data-toggle="modal" class="btn btn-danger">RR Update</a></div>
                                        <%--  <div style="float: right; margin-left: 10px;"><a href="#" style="color: #fff;" class="btn btn-warning">Loanee Master</a></div>--%>

                                        <%-- <div style="float: right; margin-left: 10px;"><a data-target="#modal_Loanee_Details" data-toggle="modal" class="btn btn-primary">Loanee Details</a></div>--%>
                                    </div>
                                </div>
                                <!-- /.card-header -->
                                <div class="card-body">
                                    <div id="DIV_Tansaction_List">
                                        <table id="Tansaction_List" class="table table-bordered table-striped" style="font-size: 14px;">
                                            <thead>
                                                <tr>
                                                    <th>Date</th>
                                                    <th>RecNo</th>
                                                    <th>Amount</th>
                                                    <th>Debit</th>
                                                    <th>Tot. Int.</th>
                                                    <th>Tot. Penal</th>
                                                    <th>Prin. Remt</th>
                                                    <th>Int. Remt</th>
                                                    <th>Penl. Remt</th>
                                                    <th>Credit</th>
                                                    <th>Int. Bal</th>
                                                    <th>Penl. Bal</th>
                                                    <th>Prin. Out</th>
                                                    <th>Remarks</th>
                                                </tr>
                                            </thead>
                                            <tbody id="tblBody">
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <!-- /.card-body -->
                            </div>
                            <!-- /.card -->
                        </div>
                        <!-- /.col -->
                    </div>
                    <!-- /.row -->
                </div>
                <!-- /.container-fluid -->
            </section>
            <!-- /.content -->
        </div>

        <div class="modal fade" id="modal_Loanee_Details" style="display: none;" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title">Loanee Details</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label">NO.OF INSTALMENTS</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="txtNoInstalments" disabled="disabled">
                            </div>
                            <label class="col-sm-2 col-form-label">OLD LOAN NO.</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="txtOldLoanNumber" disabled="disabled">
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label">SCHEME</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="txtScheme" disabled="disabled">
                            </div>
                            <label class="col-sm-2 col-form-label">RELIGION/ CASTE</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="txtReligion" disabled="disabled">
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label">LOAN AMOUNT</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="txtLoanAmount" disabled="disabled">
                            </div>
                            <label class="col-sm-2 col-form-label">TOAL PRE-FIXED INT.</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="txtTotalPreFixedInt" disabled="disabled">
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label">EMI AMOUNT</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="txtEMI" disabled="disabled">
                            </div>
                            <label class="col-sm-2 col-form-label">PRE-FIXED INT INSTAL</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="txtPreFixedIntInstal" disabled="disabled">
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label">INTEREST RATE</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="txtInterestRate" disabled="disabled">
                            </div>
                            <label class="col-sm-2 col-form-label">PENAL INT. RATE</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="txtPenalIntRate" disabled="disabled">
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label">DISBURSEMENT DATE</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="txtDisbursementDate" disabled="disabled">
                            </div>
                            <label class="col-sm-2 col-form-label">FIRST DUE DATE</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="txtFirstDueDate" disabled="disabled">
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label">RRC NO.</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="txtRRCNo" disabled="disabled">
                            </div>
                            <label class="col-sm-2 col-form-label">RR DATE</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="txtRRDate" disabled="disabled">
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="inputName2" class="col-sm-2 col-form-label">ADDRESS</label>
                            <div class="col-sm-10">
                                <textarea class="form-control" id="txtAddress" disabled="disabled"></textarea>
                            </div>
                        </div>

                    </div>
                    <div class="modal-footer justify-content-end">
                        <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>

                    </div>
                </div>
                <!-- /.modal-content -->
            </div>
            <!-- /.modal-dialog -->
        </div>


        <div class="modal fade" id="modal_SR_Update" style="display: none;" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title">Salary Recovery</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <section class="content">
                            <div class="container-fluid">
                                <div class="row">
                                    <!-- /.col -->
                                    <div class="col-md-12 mx-auto">
                                        <div class="card">

                                            <div class="card-body">
                                                <div class="tab-content">

                                                    <div class="active tab-pane" id="settings">
                                                        <div class="Sub_Header" style="text-align: center;">SR Details Update</div>


                                                        <div class="form-group row">
                                                            <label for="inputName2" class="col-sm-3 col-form-label">Status</label>
                                                            <div class="col-sm-9">
                                                                <input type="text" disabled="disabled" max="12" class="form-control" id="txt_SR_Status" placeholder=" ">
                                                            </div>
                                                        </div>



                                                        <div class="form-group row">
                                                            <label for="inputName2" class="col-sm-3 col-form-label">SR Date*</label>
                                                            <div class="col-sm-9">
                                                                <input class="dateandtime form-control" id="txtSR_Date" type="text" name="machin-b" value="">
                                                            </div>
                                                        </div>






                                                    </div>
                                                    <!-- /.tab-pane -->
                                                </div>
                                                <!-- /.tab-content -->
                                            </div>
                                            <!-- /.card-body -->
                                        </div>
                                        <!-- /.card -->
                                    </div>
                                    <!-- /.col -->
                                </div>
                                <!-- /.row -->
                            </div>
                            <!-- /.container-fluid -->
                        </section>



                    </div>
                    <div class="modal-footer justify-content-end">
                        <a onclick="Save_SR();" id="btn_SR_Save" class="btn btn-success">Save</a>
                        <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>

                    </div>

                </div>
                <!-- /.modal-content -->
            </div>
            <!-- /.modal-dialog -->
        </div>

        <div class="modal fade" id="modal_RR_Update" style="display: none;" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title">RR Details Update</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <section class="content">
                            <div class="container-fluid">
                                <div class="row">
                                    <!-- /.col -->
                                    <div class="col-md-12 mx-auto">
                                        <div class="card">

                                            <div class="card-body">
                                                <div class="tab-content">

                                                    <div class="active tab-pane" id="settings">
                                                        <div class="Sub_Header" style="text-align: center;">RR Details Update</div>


                                                        <div class="form-group row">
                                                            <label for="inputName2" class="col-sm-3 col-form-label">Status</label>
                                                            <div class="col-sm-9">
                                                                <input type="text" disabled="disabled" max="12" class="form-control" id="txt_RR_Status" placeholder=" ">
                                                            </div>
                                                        </div>

                                                        <div class="form-group row">
                                                            <label for="inputName2" class="col-sm-3 col-form-label">RRC No.</label>
                                                            <div class="col-sm-9">
                                                                <input type="text" max="12" class="form-control" id="txtRRC_No" placeholder=" ">
                                                            </div>
                                                        </div>

                                                        <div class="form-group row">
                                                            <label for="inputName2" class="col-sm-3 col-form-label">RRC Date*</label>
                                                            <div class="col-sm-9">
                                                                <input class="dateandtime form-control" id="txtRRC_Date" type="text" name="machin-b" value="">
                                                            </div>
                                                        </div>


                                                        <div class="form-group row">
                                                            <label for="inputName2" class="col-sm-3 col-form-label">RR Demand</label>
                                                            <div class="col-sm-9">
                                                                <input type="text" max="12" class="form-control" id="txtRR_Demand" placeholder=" ">
                                                            </div>
                                                        </div>



                                                        <div class="form-group row">
                                                            <label for="inputName2" class="col-sm-3 col-form-label">Taluk Ref No</label>
                                                            <div class="col-sm-9">
                                                                <input type="text" max="12" class="form-control" id="txtTaluk_Ref_No" placeholder=" ">
                                                            </div>
                                                        </div>
                                                        <div class="form-group row">
                                                            <label for="inputName2" class="col-sm-3 col-form-label">Remarks</label>
                                                            <div class="col-sm-9">
                                                                <textarea class="form-control" maxlength="200" id="txtRemark_RR" placeholder="Remarks"></textarea>
                                                                <span class="validation_message">Maximum text length is 200</span>
                                                            </div>
                                                        </div>





                                                    </div>
                                                    <!-- /.tab-pane -->
                                                </div>
                                                <!-- /.tab-content -->
                                            </div>
                                            <!-- /.card-body -->
                                        </div>
                                        <!-- /.card -->
                                    </div>
                                    <!-- /.col -->
                                </div>
                                <!-- /.row -->
                            </div>
                            <!-- /.container-fluid -->
                        </section>



                    </div>
                    <div class="modal-footer justify-content-end">
                        <a onclick="Save_RR();" id="btn_RR_Save" class="btn btn-success">Save</a>
                        <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>

                    </div>
                </div>
                <!-- /.modal-content -->
            </div>
            <!-- /.modal-dialog -->
        </div>

        <div class="modal fade" id="modal_Debit" style="display: none;" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title">Debits</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <section class="content">
                            <div class="container-fluid">
                                <div class="row">
                                    <!-- /.col -->
                                    <div class="col-md-12 mx-auto">
                                        <div class="card">

                                            <div class="card-body">
                                                <div class="tab-content">

                                                    <div class="active tab-pane" id="settings">
                                                        <div class="Sub_Header" style="text-align: center;">Debits Details Update</div>


                                                        <div class="form-group row">
                                                            <label for="inputName2" class="col-sm-3 col-form-label">Type</label>
                                                            <div class="col-sm-9">
                                                                <select class="form-control" id="dropDebits">
                                                                    <option value="4">Notice Charges</option>
                                                                    <option value="12">Other Charges</option>
                                                                    <option value="4">Bank Charges</option>
                                                                    <option value="40">Field Visit</option>
                                                                </select>
                                                            </div>
                                                        </div>

                                                        <div class="form-group row">
                                                            <label for="inputName2" class="col-sm-3 col-form-label">Date*</label>
                                                            <div class="col-sm-9">

                                                                <div id="datepicker" class="input-group date" data-date-format="dd-mm-yyyy">
                                                                    <input readonly class="dateandtime form-control" id="txtDebit_Date" type="text" name="machin-b" value="">
                                                                    <span class="input-group-addon"><i class="glyphicon glyphicon-calendar"></i></span>
                                                                </div>

                                                            </div>
                                                        </div>
                                                        <div class="form-group row">
                                                            <label for="inputName2" class="col-sm-3 col-form-label">Amount</label>
                                                            <div class="col-sm-9">
                                                                <input type="number" max="12" class="form-control" id="txt_Debit_Amount" placeholder=" ">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <!-- /.tab-pane -->
                                                </div>
                                                <!-- /.tab-content -->
                                            </div>
                                            <!-- /.card-body -->
                                        </div>
                                        <!-- /.card -->
                                    </div>
                                    <!-- /.col -->
                                </div>
                                <!-- /.row -->
                            </div>
                            <!-- /.container-fluid -->
                        </section>



                    </div>
                    <div class="modal-footer justify-content-end">
                        <a onclick="SaveDebits();" id="btn_Debits_Save" class="btn btn-success">Save</a>
                        <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>

                    </div>
                </div>
                <!-- /.modal-content -->
            </div>
            <!-- /.modal-dialog -->
        </div>


        <div class="modal fade" id="modal_LC_Update" style="display: none;" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title">Loan Closure</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <section class="content">
                            <div class="container-fluid">
                                <div class="row">
                                    <!-- /.col -->
                                    <div class="col-md-12 mx-auto">
                                        <div class="card">

                                            <div class="card-body">
                                                <div class="tab-content">

                                                    <div class="active tab-pane" id="settingsLoanClosure">
                                                        <div class="Sub_Header" style="text-align: center;">Loan Closure</div>


                                                        <div class="form-group row">
                                                            <label for="inputName2" class="col-sm-3 col-form-label">Approve For Loan Closure</label>
                                                            <div class="col-sm-9">
                                                                <input type="checkbox" style="width: auto;" class="form-control" id="chk_Close">
                                                            </div>
                                                        </div>





                                                    </div>
                                                    <!-- /.tab-pane -->
                                                </div>
                                                <!-- /.tab-content -->
                                            </div>
                                            <!-- /.card-body -->
                                        </div>
                                        <!-- /.card -->
                                    </div>
                                    <!-- /.col -->
                                </div>
                                <!-- /.row -->
                            </div>
                            <!-- /.container-fluid -->
                        </section>



                    </div>
                    <div class="modal-footer justify-content-end">
                        <a onclick="SaveClosure();" id="btn_Closure_Save" class="btn btn-success">Save</a>
                        <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>

                    </div>
                </div>
                <!-- /.modal-content -->
            </div>
            <!-- /.modal-dialog -->
        </div>



    </div>
    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables  & Plugins -->
    <script src="assets/plugins/datatables/jquery.dataTables.min.js"></script>
    <script src="assets/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="assets/plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
    <script src="assets/plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
    <script src="assets/plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
    <script src="assets/plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
    <script src="assets/plugins/jszip/jszip.min.js"></script>
    <script src="assets/plugins/pdfmake/pdfmake.min.js"></script>
    <script src="assets/plugins/pdfmake/vfs_fonts.js"></script>
    <script src="assets/plugins/datatables-buttons/js/buttons.html5.min.js"></script>
    <script src="assets/plugins/datatables-buttons/js/buttons.print.min.js"></script>
    <script src="assets/plugins/datatables-buttons/js/buttons.colVis.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>


    <script src="assets/js/jquery.dateandtime.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.2.0/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/js/bootstrap-datepicker.js"></script>

    <script>

        var acc = document.getElementsByClassName("accordion");
        var i;

        for (i = 0; i < acc.length; i++) {
            acc[i].addEventListener("click", function () {
                this.classList.toggle("active");
                var panel = this.nextElementSibling;
                if (panel.style.display === "block") {
                    panel.style.display = "none";
                } else {
                    panel.style.display = "block";
                }
            });
        }

        var Toast;

        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }
        var mytable;
        $(function () {
            var startDate = new Date();
            startDate.setDate(startDate.getDate() - 2);

            var endDate = new Date();
            endDate.setDate(endDate.getDate());

            var today = new Date();

            // Format today's date as 'dd/mm/yyyy'
            var formattedDate = (today.getDate() < 10 ? '0' : '') + today.getDate() + '/' +
                ((today.getMonth() + 1) < 10 ? '0' : '') + (today.getMonth() + 1) + '/' +
                today.getFullYear();

            // Initialize the datepicker with start and end dates
            $("#datepicker").datepicker({
                startDate: startDate,
                endDate: endDate,
                autoclose: true,
                todayHighlight: true,
                format: 'dd-mm-yyyy' // Set the date format
            });

            // Set today's date as the default date and update the datepicker
            $("#datepicker").datepicker("setDate", formattedDate);




            $('#txtRRC_Date').dateAndTime();
            $('#txtSR_Date').dateAndTime();
            //   $('#txtDebit_Date').dateAndTime();

            $("#chk_Close").change(function () {
                // Check if the checkbox is checked
                if ($(this).is(":checked")) {
                    $("#txt_Waiver_Amount").removeAttr("disabled");
                } else {
                    $("#txt_Waiver_Amount").attr("disabled", "disabled");
                    $("#txt_Waiver_Amount").val('0');
                }
            });

            $("#tr_Closure").hide();

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });


            Load_RR_Details();
            Select_Lonee_Details_And_Account();
            Load_Lonee_Details();


            function Print_Ledger() {
                var printWindow = window.open('', '_blank');
                printWindow.document.open();
                printWindow.document.write('<html><head><title>Personal Ledger - ' + getParameterByName("name") + '(' + getParameterByName("loan_no") + ')</title>');
                printWindow.document.write('<style>.Display_None{display: none;} div#DIV_All_Section {width: 1500px;} .dt-buttons.btn-group.flex-wrap {display: none;} div#Tansaction_List_filter {display: none;} div#Tansaction_List_info {display: none;} div#Tansaction_List_paginate {display: none;}</style>');

                printWindow.document.write('</head><body style="width: 210mm;height: 297mm;margin: 0;  ">');
                printWindow.document.write($("#DIV_Full_Page").html());
                printWindow.document.write('</body></html>');
                printWindow.document.close();
                setTimeout(function () { printWindow.print(); }, 300);

                // if (confirm('Print dialog initiated. Please close the window after printing.')) {
                //  printWindow.close();
                //  Update_Payemnt();
                //}
            }




            // Load_Personal_Ledger();
            $('#Tansaction_List').DataTable({
                "searching": false,
                "responsive": true, "lengthChange": false, "autoWidth": false,
                dom: 'Blfrtip',
                "buttons": ["excel",

                    {
                        extend: 'pdfHtml5',
                        exportOptions: {
                            columns: ':visible'
                        },
                        text: 'Download Pdf',
                        title: 'Personal Ledger Details',
                        orientation: 'landscape',
                        customize: function (doc) {
                            // Splice the image in after the header, but before the table


                        }
                    }, "colvis"],
                "processing": true,
                "serverSide": true,
                "ajax": {
                    "url": "WebService.asmx/Select_All_loanTrans_LoanNo", // Replace with your server-side API URL
                    "type": "POST",
                    "data": function (data) {
                        //  var jsonObject = JSON.parse(jsonData);

                        // Add the orderColumn parameter to the data being sent to the server
                        // Add the required parameters to the DataTables AJAX request


                        data.draw = data.draw || 1; // Example: Default value for draw parameter
                        data.start = data.start || 0;
                        data.length = data.length;
                        data.orderColumn = 'dt_transaction';
                        data.orderDirection = 'asc';

                        //   data.search = $('#Tansaction_List_filter label input').val();//{ value: $('#AgencyList_filter label input').val() }; // Send the search value to the server
                        //     alert($('#AgencyList_filter label input').val());
                        data.loan_no = getParameterByName("loan_no");
                        // Log the modified data object to the console
                        console.log(data);
                        // var jsonData = JSON.stringify(data);

                        // Return the modified data object
                        return data;
                    }
                },


                "initComplete": function (settings, json) {
                    Get_Current_Date_Entry();
                    // call your function here
                },
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, 'All']], // Length menu options
                "pageLength": 100000,
                "columns": [
                    { "data": "formated_transaction" },
                    { "data": "chr_rec_no" },
                    {
                        "data": "int_amt",
                        render: formatDecimal
                    },
                    {
                        "data": "int_debit",
                        render: formatDecimal
                    },

                    {
                        "data": "int_int_amt",
                        render: formatDecimal
                    },
                    {
                        "data": "int_penal_amt",
                        render: formatDecimal
                    },
                    {
                        "data": "int_prin_amt",
                        render: formatDecimal
                    },
                    {
                        "data": "int_int_r",
                        render: formatDecimal
                    },
                    {
                        "data": "int_penal_r",
                        render: formatDecimal
                    },
                    {
                        "data": "int_bank_charge_r",
                        render: formatDecimal
                    },
                    {
                        "data": "int_int_dues",
                        render: formatDecimal
                    },
                    {
                        "data": "int_penal_dues",
                        render: formatDecimal
                    },
                    {
                        "data": "int_prin_dues",
                        render: formatDecimal
                    },
                    { "data": "chr_remark" }

                ]
            });




        });

        function Print_Ledger() {
            var printWindow = window.open('', '_blank');
            printWindow.document.open();
            printWindow.document.write('<html><head><title>Personal Ledger - ' + getParameterByName("name") + '(' + getParameterByName("loan_no") + ')</title>');
            printWindow.document.write('<style>.Display_None{display: none;} div#DIV_All_Section {width: 1500px;} .dt-buttons.btn-group.flex-wrap {display: none;} div#Tansaction_List_filter {display: none;} div#Tansaction_List_info {display: none;} div#Tansaction_List_paginate {display: none;}</style>');

            printWindow.document.write('</head><body style="width: 210mm;height: 297mm;margin: 0;  ">');
            printWindow.document.write($("#DIV_Full_Page").html());
            printWindow.document.write('</body></html>');
            printWindow.document.close();
            setTimeout(function () { printWindow.print(); }, 100);

            // if (confirm('Print dialog initiated. Please close the window after printing.')) {
            //  printWindow.close();
            //  Update_Payemnt();
            //}
        }




        function Get_Current_Date_Entry() {
            //txtNoInstalments
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ LoanNumber: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Get_Current_Date_Entry",
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    // Get the current date
                    var currentDate = new Date();
                    var day = currentDate.getDate();
                    var month = (currentDate.getMonth() + 1);
                    if (day < 10) {
                        day = '0' + day;
                    }
                    if (month < 10) {
                        month = '0' + month;
                    }
                    // Format the date as desired (e.g., "MM/DD/YYYY")
                    var formattedDate = day + '/' + month + '/' + currentDate.getFullYear();
                    // alert(currentDate.getMonth() );



                    $.each(data.d, function (key, value) {
                        $("#txtPrincipal_Outstanding").text(value.int_prin_dues.toFixed(2));
                        $("#txtInterest_Outstanding").text(value.int_int_dues.toFixed(2));
                        $("#txtPenal_Outstanding").text(value.int_penal_dues.toFixed(2));
                        $("#txtCB_Adj_Outstanding").text(parseFloat(value.int_debit.toFixed(2)));

                        var Total = parseFloat(value.int_prin_dues.toFixed(2)) + parseFloat(value.int_int_dues.toFixed(2)) + parseFloat(value.int_penal_dues.toFixed(2)) + parseFloat(value.int_debit.toFixed(2));
                        $("#txtTotal_Outatanding").text(parseFloat(Total.toFixed(2)));


                        $("#txt_Total_Amount").val($("#txtTotal_Outatanding").text());

                        $("#txt_CB_Adj").text(parseFloat(value.int_debit.toFixed(2)));

                        var formated_transaction = '';
                        //Current Date --- 28/03/2024
                        //$("#Tansaction_List tbody").append('<tr class="odd">'
                        //    + '	<td class="sorting_1 dtr-control">' + formattedDate + '</td>'
                        //    + '	<td></td>'
                        //    + '	<td>' + value.int_amt.toFixed(2) + '</td>'
                        //    + '	<td>' + value.int_debit.toFixed(2) + '</td>'
                        //    + '	<td>' + value.int_int_amt.toFixed(2) + '</td>'
                        //    + '	<td>' + value.int_penal_amt.toFixed(2) + '</td>'
                        //    + '	<td>' + value.int_prin_amt.toFixed(2) + '</td>'
                        //    + '	<td>' + value.int_int_r.toFixed(2) + '</td>'
                        //    + '	<td>' + value.int_penal_r.toFixed(2) + '</td>'
                        //    + '	<td>' + value.int_bank_charge_r.toFixed(2) + '</td>'
                        //    + '	<td>' + value.int_int_dues.toFixed(2) + '</td>'
                        //    + '	<td>' + value.int_penal_dues.toFixed(2) + '</td>'
                        //    + '	<td>' + value.int_prin_dues.toFixed(2) + '</td>'
                        //    + '	<td>CURRENT DATE</td>'
                        //    + '</tr>');

                        //mytable.row.add([value.dt_transaction
                        //    , value.int_amt
                        //    , value.int_debit
                        //    , value.int_int_amt
                        //    , value.int_penal_amt
                        //    , value.int_prin_amt
                        //    , value.int_int_r
                        //    , value.int_penal_r
                        //    , value.int_bank_charge_r
                        //    , value.int_int_dues
                        //    , value.int_penal_dues
                        //    , value.int_prin_dues]);
                        //mytable.draw();






                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {


                Get_Recovered_Amount_Details();
            });
        }

        function Get_Recovered_Amount_Details() {
            //txtNoInstalments
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Int_Loanno: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Get_Recovered_Amount_Details",
                contentType: "application/json; charset=utf-8",
                success: function (data) {



                    $.each(data.d, function (key, value) {
                        $("#txtRecovered_Principal").text(value.Princple_Amount.toFixed(2));
                        $("#txtRecovered_Interest").text(value.Interest_Remited.toFixed(2));
                        $("#txtRecovered_Penal").text(value.Penal_Remitted.toFixed(2));
                        $("#txtRecovered_CB_Adj").text(value.Bank_Charges.toFixed(2));
                        var Recovered_Amount = parseFloat(value.Princple_Amount.toFixed(2)) +
                            parseFloat(value.Interest_Remited.toFixed(2)) +
                            parseFloat(value.Penal_Remitted.toFixed(2)) +
                            parseFloat(value.Bank_Charges.toFixed(2))

                        $("#txtTotal_Recovered_Amount").text(parseFloat(Recovered_Amount).toFixed(2));







                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Check_Period_Over()


            });
        }

        function Check_Period_Over() {
            //txtNoInstalments
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Int_Loanno: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Select_Period_Over_Check",
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    if (data.d.length != 0) {
                        var Principal_Outstanding = $("#txtPrincipal_Outstanding").text();
                        if (parseInt(Principal_Outstanding) > 0) {
                            $("#SPAN_PeriodOver").text('      (PERIOD OVER)      ');
                            $("#txtAction_Taken").text($("#txtAction_Taken").text() + "," + " PERIOD OVER");
                        }
                    }
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Get_Default_Amount();
            });
        }


        function Get_Default_Amount() {
            //txtNoInstalments
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Reg_No: '', Int_Loanno: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Get_Default_Amount",
                contentType: "application/json; charset=utf-8",
                success: function (data) {



                    $.each(data.d, function (key, value) {


                        // $("#lbl_Def_Amount").text(parseFloat(value.defamt) + parseFloat($("#txtPenal_Outstanding").text()));

                        var Def_Amount = parseFloat(value.defamt) + parseFloat($("#txtPenal_Outstanding").text());

                        $("#lbl_Def_Amount").text(parseFloat(Def_Amount).toFixed(2));

                        var Total_Outatanding = $("#txtTotal_Outatanding").text();

                        console.log(parseFloat(Def_Amount).toFixed(2));
                        console.log(parseFloat(Total_Outatanding));

                        if (parseFloat(Def_Amount).toFixed(2) > parseFloat(Total_Outatanding)) {
                            $("#lbl_Def_Amount").text(parseFloat(Total_Outatanding));
                        }

                        $("#lbl_Def_Installement").text(value.definst);
                        var Principal_Outstanding = $("#txtPrincipal_Outstanding").text();
                        if (parseFloat(Principal_Outstanding) <= 0) {
                            $("#lbl_Def_Installement").text(0.00);
                            $("#SPAN_Closure").text('LOAN CLOSED');
                            $("#tr_Closure").show();
                            $("#LoanClosureApproval").hide();
                            $("#Loan_Debits").hide();
                            $("#Loan_SR_Update").hide();
                            $("#Loan_RR_Update").hide();


                            Select_OTS_Waiver_Details_By_Loan_No();
                        }
                        else {
                            $("#Loan_Credit_Payment").hide();
                            $("#Credit_Details").hide();
                        }
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                if (($("#ForClosing").val() == "1") && ($("#Stage").val() != "CLOSED")) {
                    Load_Loan_Wavier_Amount();
                }

                Get_Wavier_By_No_Default_Installment();

            });
        }



        function Select_OTS_Waiver_Details_By_Loan_No() {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Int_Loanno: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Select_OTS_Waiver_Details_By_Loan_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $.each(data.d, function (key, value) {
                        var Waiver = value.int_Interest + value.int_Penal;
                        $("#SPAN_Closure").text('LOAN CLOSED With Waiver Rs. ' + Waiver);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }


        var RR_Wavier_Amount = 0;



        function Get_Wavier_By_No_Default_Installment() {
            //txtNoInstalments
            var Type = '';

            if ($("#txtAction_Taken").text().indexOf("RR") !== -1) {
                Type = 'RR';
            }
            else if ($("#txtAction_Taken").text().indexOf("PERIOD OVER") !== -1) {
                Type = 'RR';
            }
            else {
                Type = 'NRR';
            }




            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ default_Installment: $("#lbl_Def_Installement").text(), vchr_Type: Type }), // If you have parameters
                url: "WebService.asmx/Get_Wavier_By_No_Default_Installment",
                contentType: "application/json; charset=utf-8",
                success: function (data) {



                    $.each(data.d, function (key, value) {

                        RR_Wavier_Amount = Math.round((parseFloat($("#txtTotal_Outatanding").text() * parseFloat(value.int_per))) / 100);

                        var IntPernal = Math.round(parseFloat($("#txtInterest_Outstanding").text()) + parseFloat($("#txtPenal_Outstanding").text()));

                        if (RR_Wavier_Amount > IntPernal) { RR_Wavier_Amount = IntPernal }

                        $("#lbl_Wavier_Amount").text("Eligible Wavier Amount(" + RR_Wavier_Amount.toFixed(2) + ") - Percentage (" + value.int_per + ")");






                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                $("#txt_Waiver_Amount").on("input", function () {
                    var inputValue = $(this).val();
                    if (parseInt(RR_Wavier_Amount) < parseInt(inputValue)) {
                        Toast.fire({
                            icon: 'error',
                            title: 'Enterd Amount is higher then Waiver Amount  !'
                        })

                        $("#btn_Closure_Save").css("display", "none");
                    }
                    else {

                        $("#btn_Closure_Save").css("display", "block");
                    }
                });
            });
        }




        function formatDecimal(data, type, row) {
            if (type === "display" && typeof data === "number") {
                return data.toFixed(2);
            }
            return data;
        }

        function Load_Loan_Closure_Approval() {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ int_Loanno: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Select_Loan_Closure_Approval_By_Loan_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#chk_Close').prop('checked', false);
                    $.each(data.d, function (key, value) {
                        var ClosureApproval = value.int_ForClosing
                        if (ClosureApproval == 0) { $('#chk_Close').prop('checked', false); }
                        else if (ClosureApproval == 1) {
                            $('#chk_Close').prop('checked', true);
                            Load_Loan_Wavier_Amount();
                        }
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }

        function Load_Loan_Wavier_Amount() {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Int_Loanno: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Get_Waiver_Details_By_Loan_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $.each(data.d, function (key, value) {
                        $("#txt_Waiver_Amount").val(value.int_wavier_amt);
                        $("#txt_Waiver_Amount").removeAttr("disabled");

                        if (($("#ForClosing").val() == "1") && ($("#Stage").val() != "CLOSED")) {
                            $("#SPAN_Closure").text('Approved For Closure With Waiver Rs. ' + value.int_wavier_amt);
                            $("#tr_Closure").show();
                        }

                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }

        function SaveClosure() {

            var chk_ClosureApproval = $('#chk_Close');
            var ClosureApproval = 0;
            if (chk_ClosureApproval.is(':checked')) {
                ClosureApproval = 1;
            }

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Loan_Closure_Approval",
                data: JSON.stringify({ int_loanno: getParameterByName("loan_no"), int_ForClosing: ClosureApproval, userName: '', WavierAmount: $("#txt_Waiver_Amount").val() }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                },
                error: function (jqXHR, textStatus, errorThrown) {
                    // Handle AJAX error
                    alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                    })
                }

            }).done(function () {

                //  Update_Status();
                $("#btn_Closure_Save").css("background-color", "#c8328b");
                $("#btn_Closure_Save").removeAttr("disabled");
                $("#btn_Closure_Save").html("Submit");
                Swal.fire({
                    icon: 'success',
                    title: 'Message',
                    text: 'Loan Closure Approval Successfully Updated !',
                }).then((result) => {
                    if (result.isConfirmed) {
                        // OK button clicked
                        location.reload();
                        // Your code here
                    }
                });
            });

        }

        function SaveDebits() {

            var Debit_Date = $("#txtDebit_Date");
            var Debit_Amount = $("#txt_Debit_Amount");
            var DebitType = $("#dropDebits option:selected");

            if (Debit_Date.val().trim() == "") {
                Focus_Error(Debit_Date);
                Toast.fire({
                    icon: 'error',
                    title: 'Debit Date is required !'
                })
            }
            else if (Debit_Amount.val().trim() == "") {
                Focus_Error(Debit_Amount);
                Toast.fire({
                    icon: 'error',
                    title: 'Debit Amount is required !'
                })
            }
            else if (Debit_Amount.val().trim() == "0") {
                Focus_Error(Debit_Amount);
                Toast.fire({
                    icon: 'error',
                    title: 'Debit Amount is required !'
                })
            }

            else if (($("#dropDebits").val() == "40") && (Debit_Amount.val().trim() > "250")) {
                Focus_Error(Debit_Amount);
                Toast.fire({
                    icon: 'error',
                    title: 'Maximum Amount : 250 !'
                })
            }

            else {
                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/Loan_Debit_Charges",
                    data: JSON.stringify({ int_loanno: getParameterByName("loan_no"), debitDate: Debit_Date.val(), debitCharge: Debit_Amount.val().trim(), debitType: DebitType.text().trim() }), // If you have parameters
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {

                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        // Handle AJAX error
                        alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                        })
                    }

                }).done(function () {

                    //  Update_Status();
                    $("#btn_Closure_Save").css("background-color", "#c8328b");
                    $("#btn_Closure_Save").removeAttr("disabled");
                    $("#btn_Closure_Save").html("Submit");
                    //Swal.fire({
                    //    icon: 'success',
                    //    title: 'Message',
                    //    text: 'Debit Charges Successfully Updated !',
                    //    allowOutsideClick: false,
                    //})

                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Debit Charges Successfully Updated !',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked
                            location.reload();

                            // Your code here
                        }
                    });
                });
            }
        }


        function Load_RR_Details() {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ int_Loanno: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Select_RR_Details_By_Loan_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $.each(data.d, function (key, value) {

                        const inputDate = value.dt_RR_Date;
                        const parts = inputDate.split('/');
                        if (parts.length === 3) {
                            const yyyy = parts[2];
                            const mm = parts[1];
                            const dd = parts[0];

                            const formattedDate = `${yyyy}-${mm}-${dd}`;

                            // Set the value of the date input
                            $('#txtRRC_Date').val(formattedDate);
                        }

                        $("#txt_RR_Status").val("RR");
                        $("#txtRRC_No").val(value.vchr_RRCno);
                        $("#txtRR_Demand").val(value.int_rrdemand);
                        $("#txtTaluk_Ref_No").val(value.vchr_TRRCno);
                        $("#txtRemark_RR").val(value.vchr_RRRemarks);



                        //$("#txt_RR_Status").attr("disabled", "disabled");
                        //$("#txtRRC_No").attr("disabled", "disabled");
                        //$("#txtRR_Demand").attr("disabled", "disabled");
                        //$("#txtTaluk_Ref_No").attr("disabled", "disabled");
                        //$("#txtRemark_RR").attr("disabled", "disabled");

                        //$("#btn_RR_Save").css("display", "none");

                        if ($("#txtAction_Taken").text() == "No Action") {
                            $("#txtAction_Taken").text("RR");
                        }
                        else {
                            $("#txtAction_Taken").text($("#txtAction_Taken").text() + "," + "RR");
                        }




                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_SR_Details();

            });
        }


        function Load_SR_Details() {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ int_Loanno: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Select_SR_Details_By_Loan_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $.each(data.d, function (key, value) {

                        const inputDate = value.dt_SR_Date;
                        const parts = inputDate.split('/');
                        if (parts.length === 3) {
                            const yyyy = parts[2];
                            const mm = parts[1];
                            const dd = parts[0];

                            const formattedDate = `${yyyy}-${mm}-${dd}`;

                            // Set the value of the date input
                            $('#txtSR_Date').val(formattedDate);
                        }

                        $("#txt_SR_Status").val("SR");



                        $("#txt_SR_Status").attr("disabled", "disabled");


                        $("#btn_SR_Save").css("display", "none");

                        if ($("#txtAction_Taken").text() == "No Action") {
                            $("#txtAction_Taken").text("SR");
                        }
                        else {
                            $("#txtAction_Taken").text($("#txtAction_Taken").text());
                        }





                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });
        }




        function Save_SR() {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ int_Loanno: getParameterByName("loan_no"), int_SR: '1', dt_SRDate: $("#txtSR_Date").val() }), // If you have parameters
                url: "WebService.asmx/Update_SR_Details_By_Loan_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {


                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'SR Successfully Saved !',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked
                            $('#modal_SR_Update').modal('toggle');
                            //  location.href = 'Agency_List.aspx';
                            // Your code here
                        }
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_SR_Details();

            });
        }

        function Save_RR() {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ DueDate: $("#txtDue_Date").text(), int_Loanno: getParameterByName("loan_no"), vchr_RRCno: $("#txtRRC_No").val(), dt_RR_Date: $("#txtRRC_Date").val(), int_RR: '10', vchr_TRRCno: $("#txtTaluk_Ref_No").val(), vchr_RRRemarks: $("#txtRemark_RR").val(), int_rrdemand: $("#txtRR_Demand").val() }), // If you have parameters
                url: "WebService.asmx/Update_RR_Details_By_Loan_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {


                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'RR Successfully Saved !',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked
                            $('#modal_RR_Update').modal('toggle');
                            //  location.href = 'Agency_List.aspx';
                            // Your code here
                        }
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_RR_Details();
                location.reload();

            });
        }





        function Select_Lonee_Details_And_Account() {
            //txtNoInstalments
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ int_loanno: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Select_Lonee_Details_And_Account",
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $.each(data.d, function (key, value) {
                        $("#txt_New_Loan_No").text(value.int_loanno);
                        $("#txtLoanee_Name").text(value.vchr_applname);
                        $("#txtAddress").text(value.vchr_hsename);
                        $("#txtOld_Agr_No").text(value.vchr_remark);
                        $("#txtDue_Date").text(value.dt_first_due_date);
                        $("#txtEMI").text(value.mny_repayamt);
                        $("#txt_Loan_Duration").text(value.int_repay_inst + " Monthly");
                        $("#lbl_Interest_Rate").text(value.int_rate_int);
                        $("#lbl_Penal_Rate").text(value.int_rate_penal);
                        $("#SPAN_Principal").text(value.mny_loanbal);
                        $("#SPAN_Interest").text(value.Total_Interest);
                        $("#SPAN_Penal").text(value.Total_penal);
                        $("#SPAN_Remitance").text(value.Total_Remitance);
                        $("#txtScheme_Name").text(value.Scheme_Name);
                        $("#txtContact_Number").text(value.Contact_Number);
                        $("#Stage").val(value.vchr_stage);
                        $("#ForClosing").val(value.int_ForClosing);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                    })
                }
            }).done(function () {
                if (($("#ForClosing").val() == "1") && ($("#Stage").val() != "CLOSED")) {
                    $("#SPAN_Closure").text('Approved For Closure');
                    $("#tr_Closure").show();
                }
            });
        }

        function Load_Lonee_Details_And_Account() {
            //txtNoInstalments
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ int_loanno: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Select_Lonee_Details_And_Account",
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $.each(data.d, function (key, value) {



                        $("#txtNoInstalments").val(value.int_repay_inst);





                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });
        }

        function Load_Lonee_Details() {
            //txtNoInstalments
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ int_loanno: getParameterByName("loan_no") }), // If you have parameters
                url: "WebService.asmx/Select_Lonee_Details_By_Loan_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $.each(data.d, function (key, value) {



                        $("#txtNoInstalments").val(value.int_repay_inst);
                        $("#txtOldLoanNumber").val(value.vchr_oldno);
                        $("#txtScheme").val(value.vchr_fund);
                        $("#txtReligion").val(value.vchr_caste);
                        $("#txtLoanAmount").val(value.mny_disbamt);
                        $("#txtTotalPreFixedInt").val('0');
                        $("#txtEMI").val(value.mny_repayamt);
                        $("#txtPreFixedIntInstal").val('0');
                        $("#txtInterestRate").val(value.int_rate_int);
                        $("#txtPenalIntRate").val(value.int_rate_penal);
                        $("#txtDisbursementDate").val(value.dt_disbdate);
                        $("#txtFirstDueDate").val(value.dt_first_due_date);
                        $("#txtRRCNo").val('');
                        $("#txtRRDate").val('');
                        $("#ADDRESS").val(value.vchr_hsename);




                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });
        }

        function Load_Personal_Ledger() {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Personal_Ledger",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {

                        var int_loanappid = value.int_loanappid;
                        var SerialNumber = value.SerialNumber;
                        var int_loanno = value.int_loanno;
                        var vchr_fund = value.vchr_fund;
                        var vchr_applname = value.vchr_applname;
                        var vchr_hsename = value.vchr_hsename;
                        var vchr_oldno = value.vchr_oldno;
                        var chr_status = value.chr_status;

                        var html = '<tr>' +
                            '    <td>' + SerialNumber + '</td>' +
                            '    <td>' + int_loanno + '</td>' +
                            '    <td>' + vchr_fund + '</td>' +
                            '    <td>' + vchr_applname + '</td>' +
                            '    <td>' + vchr_hsename + '</td>' +
                            '    <td>' + vchr_oldno + '</td>' +
                            '    <td>' + chr_status + '</td>' +
                            '    <td>' +
                            '       <button data-loan-no="' + int_loanno + '" onclick="View(this);" type="button" class="btn btn-block btn-success">View</button>' +

                            '    </td>' +
                            '</tr>';
                        $('#tblBody').append(html);
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                $("#AgencyList").DataTable({
                    "responsive": true, "lengthChange": false, "autoWidth": false,
                    "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"]
                }).buttons().container().appendTo('#example1_wrapper .col-md-6:eq(0)');


            });
        }

        function Delete(This_Val) {
            $.ajax({
                type: "POST", // or "GET" depending on your web method
                url: "WebService.asmx/Agency_Delete",
                data: JSON.stringify({ Id: $(This_Val).attr("data-Id") }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (response) {

                    // Handle the successful response from the web method
                    console.log(response.d); // "d" is the default property name for the response
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Agency Successfully Deleted !',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked

                            location.href = 'Agency_List.aspx';
                            // Your code here
                        }
                    });

                },
                error: function (xhr, status, error) {
                    // Handle the error response

                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                },
                done: function (response) {
                    // Handle the error response
                    alert(response);

                }
            });
        }


        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }


    </script>
</asp:Content>
