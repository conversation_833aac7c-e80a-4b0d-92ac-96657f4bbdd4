﻿<Window x:Class="CorpNet_To_KSDC_SMART.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CorpNet_To_KSDC_SMART"
        mc:Ignorable="d"
        Title="MainWindow" Height="450" Width="800">
    <Grid>
        <Button Content="Start" HorizontalAlignment="Left" Margin="21,65,0,0" VerticalAlignment="Top" Width="162" Height="63" FontWeight="Bold" FontSize="20" Click="Button_Click"/>
        <Label x:Name="StatusLabel" Content="" HorizontalAlignment="Center" Margin="206,79,438.6,297" VerticalAlignment="Center" Height="44" Width="149" FontSize="20" FontWeight="Bold"/>
        <Label Content="Corpnet To MSSQL" HorizontalAlignment="Left" Margin="10,10,0,0" VerticalAlignment="Top" FontWeight="Bold" FontSize="20" Height="52" Width="199"/>
        <Rectangle Fill="#FF303130" HorizontalAlignment="Left" Height="3" Margin="0,159,-0.4,0" VerticalAlignment="Top" Width="794"/>
        <Label Content="Data Migration" HorizontalAlignment="Left" Margin="10,179,0,0" VerticalAlignment="Top" FontWeight="Bold" FontSize="20" Height="52" Width="199"/>
        <Button x:Name="btn_Migration_Start" Content="Start" HorizontalAlignment="Left" Margin="21,308,0,0" VerticalAlignment="Top" Width="162" Height="63" FontWeight="Bold" FontSize="20" Click="btn_Migration_Start_Click"/>
        <Label x:Name="StatusLabel_Migration" Content="" HorizontalAlignment="Center" Margin="206,322,365.6,54" VerticalAlignment="Center" Height="44" Width="222" FontSize="20" FontWeight="Bold"/>
        <ComboBox x:Name="dropDistrict" HorizontalAlignment="Left" SelectionChanged="OnComboBoxSelectionChanged" Margin="21,251,0,0" VerticalAlignment="Top" Width="375" FontSize="20"/>

    </Grid>
</Window>
