/***

MochiKit.Base 1.4

See <http://mochikit.com/> for documentation, downloads, license, etc.

(c) 2005 <PERSON>.  All rights Reserved.

***/
if(typeof(dojo)!="undefined"){dojo.provide("MochiKit.Base")}if(typeof(MochiKit)=="undefined"){MochiKit={}}if(typeof(MochiKit.Base)=="undefined"){MochiKit.Base={}}MochiKit.Base.VERSION="1.4";MochiKit.Base.NAME="MochiKit.Base";MochiKit.Base.update=function(b,f){if(b===null){b={}}for(var c=1;c<arguments.length;c++){var g=arguments[c];if(typeof(g)!="undefined"&&g!==null){for(var a in g){b[a]=g[a]}}}return b};MochiKit.Base.update(MochiKit.Base,{__repr__:function(){return"["+this.NAME+" "+this.VERSION+"]"},toString:function(){return this.__repr__()},camelize:function(b){var a=b.split("-");var f=a[0];for(var c=1;c<a.length;c++){f+=a[c].charAt(0).toUpperCase()+a[c].substring(1)}return f},counter:function(a){if(arguments.length===0){a=1}return function(){return a++}},clone:function(b){var a=arguments.callee;if(arguments.length==1){a.prototype=b;return new a()}},extend:function(b,g,f){if(!f){f=0}if(g){var a=g.length;if(typeof(a)!="number"){if(typeof(MochiKit.Iter)!="undefined"){g=MochiKit.Iter.list(g);a=g.length}else{throw new TypeError("Argument not an array-like and MochiKit.Iter not present")}}if(!b){b=[]}for(var c=f;c<a;c++){b.push(g[c])}}return b},updatetree:function(c,g){if(c===null){c={}}for(var f=1;f<arguments.length;f++){var h=arguments[f];if(typeof(h)!="undefined"&&h!==null){for(var b in h){var a=h[b];if(typeof(c[b])=="object"&&typeof(a)=="object"){arguments.callee(c[b],a)}else{c[b]=a}}}}return c},setdefault:function(b,f){if(b===null){b={}}for(var c=1;c<arguments.length;c++){var g=arguments[c];for(var a in g){if(!(a in b)){b[a]=g[a]}}}return b},_newNamedError:function(b,a,c){c.prototype=new MochiKit.Base.NamedError(b.NAME+"."+a);b[a]=c},operator:{identity:function(b){return b}},forwardCall:function(a){return function(){return this[a].apply(this,arguments)}},typeMatcher:function(){var b={};for(var a=0;a<arguments.length;a++){var c=arguments[a];b[c]=c}return function(){for(var f=0;f<arguments.length;f++){if(!(typeof(arguments[f]) in b)){return false}}return true}},isNull:function(){for(var a=0;a<arguments.length;a++){if(arguments[a]!==null){return false}}return true},isUndefinedOrNull:function(){for(var a=0;a<arguments.length;a++){var b=arguments[a];if(!(typeof(b)=="undefined"||b===null)){return false}}return true},isEmpty:function(a){return !MochiKit.Base.isNotEmpty.apply(this,arguments)},isNotEmpty:function(b){for(var a=0;a<arguments.length;a++){var c=arguments[a];if(!(c&&c.length)){return false}}return true},isArrayLike:function(){for(var a=0;a<arguments.length;a++){var c=arguments[a];var b=typeof(c);if((b!="object"&&!(b=="function"&&typeof(c.item)=="function"))||c===null||typeof(c.length)!="number"||c.nodeType===3){return false}}return true},isDateLike:function(){for(var a=0;a<arguments.length;a++){var b=arguments[a];if(typeof(b)!="object"||b===null||typeof(b.getTime)!="function"){return false}}return true},xmap:function(b){if(b===null){return MochiKit.Base.extend(null,arguments,1)}var c=[];for(var a=1;a<arguments.length;a++){c.push(b(arguments[a]))}return c},map:function(q,h){var b=MochiKit.Base;var n=MochiKit.Iter;var r=b.isArrayLike;if(arguments.length<=2){if(!r(h)){if(n){h=n.list(h);if(q===null){return h}}else{throw new TypeError("Argument not an array-like and MochiKit.Iter not present")}}if(q===null){return b.extend(null,h)}var k=[];for(var g=0;g<h.length;g++){k.push(q(h[g]))}return k}else{if(q===null){q=Array}var a=null;for(g=1;g<arguments.length;g++){if(!r(arguments[g])){if(n){return n.list(n.imap.apply(null,arguments))}else{throw new TypeError("Argument not an array-like and MochiKit.Iter not present")}}var c=arguments[g].length;if(a===null||a>c){a=c}}k=[];for(g=0;g<a;g++){var o=[];for(var f=1;f<arguments.length;f++){o.push(arguments[f][g])}k.push(q.apply(this,o))}return k}},xfilter:function(b){var c=[];if(b===null){b=MochiKit.Base.operator.truth}for(var a=1;a<arguments.length;a++){var f=arguments[a];if(b(f)){c.push(f)}}return c},filter:function(g,b,c){var h=[];var a=MochiKit.Base;if(!a.isArrayLike(b)){if(MochiKit.Iter){b=MochiKit.Iter.list(b)}else{throw new TypeError("Argument not an array-like and MochiKit.Iter not present")}}if(g===null){g=a.operator.truth}if(typeof(Array.prototype.filter)=="function"){return Array.prototype.filter.call(b,g,c)}else{if(typeof(c)=="undefined"||c===null){for(var f=0;f<b.length;f++){var j=b[f];if(g(j)){h.push(j)}}}else{for(f=0;f<b.length;f++){j=b[f];if(g.call(c,j)){h.push(j)}}}}return h},_wrapDumbFunction:function(func){return function(){switch(arguments.length){case 0:return func();case 1:return func(arguments[0]);case 2:return func(arguments[0],arguments[1]);case 3:return func(arguments[0],arguments[1],arguments[2])}var args=[];for(var i=0;i<arguments.length;i++){args.push("arguments["+i+"]")}return eval("(func("+args.join(",")+"))")}},methodcaller:function(b){var a=MochiKit.Base.extend(null,arguments,1);if(typeof(b)=="function"){return function(c){return b.apply(c,a)}}else{return function(c){return c[b].apply(c,a)}}},method:function(b,c){var a=MochiKit.Base;return a.bind.apply(this,a.extend([c,b],arguments,2))},compose:function(b,h){var g=[];var a=MochiKit.Base;if(arguments.length===0){throw new TypeError("compose() requires at least one argument")}for(var c=0;c<arguments.length;c++){var f=arguments[c];if(typeof(f)!="function"){throw new TypeError(repr(f)+" is not a function")}g.push(f)}return function(){var j=arguments;for(var k=g.length-1;k>=0;k--){j=[g[k].apply(this,j)]}return j[0]}},bind:function(g,c){if(typeof(g)=="string"){g=c[g]}var f=g.im_func;var j=g.im_preargs;var b=g.im_self;var a=MochiKit.Base;if(typeof(g)=="function"&&typeof(g.apply)=="undefined"){g=a._wrapDumbFunction(g)}if(typeof(f)!="function"){f=g}if(typeof(c)!="undefined"){b=c}if(typeof(j)=="undefined"){j=[]}else{j=j.slice()}a.extend(j,arguments,2);var h=function(){var l=arguments;var m=arguments.callee;if(m.im_preargs.length>0){l=a.concat(m.im_preargs,l)}var k=m.im_self;if(!k){k=this}return m.im_func.apply(k,l)};h.im_self=b;h.im_func=f;h.im_preargs=j;return h},bindMethods:function(b){var f=MochiKit.Base.bind;for(var a in b){var c=b[a];if(typeof(c)=="function"){b[a]=f(c,b)}}},registerComparator:function(c,b,a,f){MochiKit.Base.comparatorRegistry.register(c,b,a,f)},_primitives:{"boolean":true,string:true,number:true},compare:function(k,f){if(k==f){return 0}var j=(typeof(k)=="undefined"||k===null);var l=(typeof(f)=="undefined"||f===null);if(j&&l){return 0}else{if(j){return -1}else{if(l){return 1}}}var c=MochiKit.Base;var h=c._primitives;if(!(typeof(k) in h&&typeof(f) in h)){try{return c.comparatorRegistry.match(k,f)}catch(n){if(n!=c.NotFound){throw n}}}if(k<f){return -1}else{if(k>f){return 1}}var g=c.repr;throw new TypeError(g(k)+" and "+g(f)+" can not be compared")},compareDateLike:function(f,c){return MochiKit.Base.compare(f.getTime(),c.getTime())},compareArrayLike:function(f,c){var k=MochiKit.Base.compare;var j=f.length;var l=0;if(j>c.length){l=1;j=c.length}else{if(j<c.length){l=-1}}for(var g=0;g<j;g++){var h=k(f[g],c[g]);if(h){return h}}return l},registerRepr:function(b,a,f,c){MochiKit.Base.reprRegistry.register(b,a,f,c)},repr:function(f){if(typeof(f)=="undefined"){return"undefined"}else{if(f===null){return"null"}}try{if(typeof(f.__repr__)=="function"){return f.__repr__()}else{if(typeof(f.repr)=="function"&&f.repr!=arguments.callee){return f.repr()}}return MochiKit.Base.reprRegistry.match(f)}catch(b){if(typeof(f.NAME)=="string"&&(f.toString==Function.prototype.toString||f.toString==Object.prototype.toString)){return f.NAME}}try{var c=(f+"")}catch(b){return"["+typeof(f)+"]"}if(typeof(f)=="function"){f=c.replace(/^\s+/,"");var a=f.indexOf("{");if(a!=-1){f=f.substr(0,a)+"{...}"}}return c},reprArrayLike:function(b){var a=MochiKit.Base;return"["+a.map(a.repr,b).join(", ")+"]"},reprString:function(a){return('"'+a.replace(/(["\\])/g,"\\$1")+'"').replace(/[\f]/g,"\\f").replace(/[\b]/g,"\\b").replace(/[\n]/g,"\\n").replace(/[\t]/g,"\\t").replace(/[\r]/g,"\\r")},reprNumber:function(a){return a+""},registerJSON:function(b,a,f,c){MochiKit.Base.jsonRegistry.register(b,a,f,c)},evalJSON:function(){return eval("("+arguments[0]+")")},serializeJSON:function(a){var r=typeof(a);if(r=="undefined"){return"undefined"}else{if(r=="number"||r=="boolean"){return a+""}else{if(a===null){return"null"}}}var c=MochiKit.Base;var s=c.reprString;if(r=="string"){return s(a)}var q=arguments.callee;var g;if(typeof(a.__json__)=="function"){g=a.__json__();if(a!==g){return q(g)}}if(typeof(a.json)=="function"){g=a.json();if(a!==g){return q(g)}}if(r!="function"&&typeof(a.length)=="number"){var n=[];for(var j=0;j<a.length;j++){var b=q(a[j]);if(typeof(b)!="string"){b="undefined"}n.push(b)}return"["+n.join(", ")+"]"}try{g=c.jsonRegistry.match(a);if(a!==g){return q(g)}}catch(l){if(l!=c.NotFound){throw l}}if(r=="function"){return null}n=[];for(var f in a){var h;if(typeof(f)=="number"){h='"'+f+'"'}else{if(typeof(f)=="string"){h=s(f)}else{continue}}b=q(a[f]);if(typeof(b)!="string"){continue}n.push(h+":"+b)}return"{"+n.join(", ")+"}"},objEqual:function(f,c){return(MochiKit.Base.compare(f,c)===0)},arrayEqual:function(b,a){if(b.length!=a.length){return false}return(MochiKit.Base.compare(b,a)===0)},concat:function(){var b=[];var c=MochiKit.Base.extend;for(var a=0;a<arguments.length;a++){c(b,arguments[a])}return b},keyComparator:function(b){var a=MochiKit.Base;var f=a.compare;if(arguments.length==1){return function(h,g){return f(h[b],g[b])}}var c=a.extend(null,arguments);return function(h,g){var l=0;for(var k=0;(l===0)&&(k<c.length);k++){var j=c[k];l=f(h[j],g[j])}return l}},reverseKeyComparator:function(b){var a=MochiKit.Base.keyComparator.apply(this,arguments);return function(f,c){return a(c,f)}},partial:function(b){var a=MochiKit.Base;return a.bind.apply(this,a.extend([b,undefined],arguments,1))},listMinMax:function(h,a){if(a.length===0){return null}var g=a[0];var c=MochiKit.Base.compare;for(var b=1;b<a.length;b++){var f=a[b];if(c(f,g)==h){g=f}}return g},objMax:function(){return MochiKit.Base.listMinMax(1,arguments)},objMin:function(){return MochiKit.Base.listMinMax(-1,arguments)},findIdentical:function(a,f,g,b){if(typeof(b)=="undefined"||b===null){b=a.length}if(typeof(g)=="undefined"||g===null){g=0}for(var c=g;c<b;c++){if(a[c]===f){return c}}return -1},mean:function(){var f=0;var a=MochiKit.Base;var b=a.extend(null,arguments);var g=b.length;while(b.length){var h=b.shift();if(h&&typeof(h)=="object"&&typeof(h.length)=="number"){g+=h.length-1;for(var c=h.length-1;c>=0;c--){f+=h[c]}}else{f+=h}}if(g<=0){throw new TypeError("mean() requires at least one argument")}return f/g},median:function(){var b=MochiKit.Base.flattenArguments(arguments);if(b.length===0){throw new TypeError("median() requires at least one argument")}b.sort(compare);if(b.length%2==0){var a=b.length/2;return(b[a]+b[a-1])/2}else{return b[(b.length-1)/2]}},findValue:function(a,g,h,b){if(typeof(b)=="undefined"||b===null){b=a.length}if(typeof(h)=="undefined"||h===null){h=0}var f=MochiKit.Base.compare;for(var c=h;c<b;c++){if(f(a[c],g)===0){return c}}return -1},nodeWalk:function(c,f){var a=[c];var g=MochiKit.Base.extend;while(a.length){var b=f(a.shift());if(b){g(a,b)}}},nameFunctions:function(b){var c=b.NAME;if(typeof(c)=="undefined"){c=""}else{c=c+"."}for(var a in b){var g=b[a];if(typeof(g)=="function"&&typeof(g.NAME)=="undefined"){try{g.NAME=c+a}catch(f){}}}},queryString:function(l,m){if(typeof(MochiKit.DOM)!="undefined"&&arguments.length==1&&(typeof(l)=="string"||(typeof(l.nodeType)!="undefined"&&l.nodeType>0))){var j=MochiKit.DOM.formContents(l);l=j[0];m=j[1]}else{if(arguments.length==1){var a=l;l=[];m=[];for(var b in a){var n=a[b];if(typeof(n)!="function"){l.push(b);m.push(n)}}}}var g=[];var h=Math.min(l.length,m.length);var c=MochiKit.Base.urlEncode;for(var f=0;f<h;f++){n=m[f];if(typeof(n)!="undefined"&&n!==null){g.push(c(l[f])+"="+c(n))}}return g.join("&")},parseQueryString:function(k,l){var c=k.replace(/\+/g,"%20").split("&");var f={};var a;if(typeof(decodeURIComponent)!="undefined"){a=decodeURIComponent}else{a=unescape}if(l){for(var h=0;h<c.length;h++){var g=c[h].split("=");var b=a(g[0]);var j=f[b];if(!(j instanceof Array)){j=[];f[b]=j}j.push(a(g[1]))}}else{for(h=0;h<c.length;h++){g=c[h].split("=");f[a(g[0])]=a(g[1])}}return f}});MochiKit.Base.AdapterRegistry=function(){this.pairs=[]};MochiKit.Base.AdapterRegistry.prototype={register:function(b,a,f,c){if(c){this.pairs.unshift([b,a,f])}else{this.pairs.push([b,a,f])}},match:function(){for(var a=0;a<this.pairs.length;a++){var b=this.pairs[a];if(b[1].apply(this,arguments)){return b[2].apply(this,arguments)}}throw MochiKit.Base.NotFound},unregister:function(a){for(var b=0;b<this.pairs.length;b++){var c=this.pairs[b];if(c[0]==a){this.pairs.splice(b,1);return true}}return false}};MochiKit.Base.EXPORT=["flattenArray","noop","camelize","counter","clone","extend","update","updatetree","setdefault","keys","items","NamedError","operator","forwardCall","itemgetter","typeMatcher","isCallable","isUndefined","isUndefinedOrNull","isNull","isEmpty","isNotEmpty","isArrayLike","isDateLike","xmap","map","xfilter","filter","methodcaller","compose","bind","bindMethods","NotFound","AdapterRegistry","registerComparator","compare","registerRepr","repr","objEqual","arrayEqual","concat","keyComparator","reverseKeyComparator","partial","merge","listMinMax","listMax","listMin","objMax","objMin","nodeWalk","zip","urlEncode","queryString","serializeJSON","registerJSON","evalJSON","parseQueryString","findValue","findIdentical","flattenArguments","method","average","mean","median"];MochiKit.Base.EXPORT_OK=["nameFunctions","comparatorRegistry","reprRegistry","jsonRegistry","compareDateLike","compareArrayLike","reprArrayLike","reprString","reprNumber"];MochiKit.Base._exportSymbols=function(f,b){if(typeof(MochiKit.__export__)=="undefined"){MochiKit.__export__=(MochiKit.__compat__||(typeof(JSAN)=="undefined"&&typeof(dojo)=="undefined"))}if(!MochiKit.__export__){return}var c=b.EXPORT_TAGS[":all"];for(var a=0;a<c.length;a++){f[c[a]]=b[c[a]]}};MochiKit.Base.__new__=function(){var a=this;a.noop=a.operator.identity;a.forward=a.forwardCall;a.find=a.findValue;if(typeof(encodeURIComponent)!="undefined"){a.urlEncode=function(c){return encodeURIComponent(c).replace(/\'/g,"%27")}}else{a.urlEncode=function(c){return escape(c).replace(/\+/g,"%2B").replace(/\"/g,"%22").rval.replace(/\'/g,"%27")}}a.NamedError=function(c){this.message=c;this.name=c};a.NamedError.prototype=new Error();a.update(a.NamedError.prototype,{repr:function(){if(this.message&&this.message!=this.name){return this.name+"("+a.repr(this.message)+")"}else{return this.name+"()"}},toString:a.forwardCall("repr")});a.NotFound=new a.NamedError("MochiKit.Base.NotFound");a.listMax=a.partial(a.listMinMax,1);a.listMin=a.partial(a.listMinMax,-1);a.isCallable=a.typeMatcher("function");a.isUndefined=a.typeMatcher("undefined");a.merge=a.partial(a.update,null);a.zip=a.partial(a.map,null);a.average=a.mean;a.comparatorRegistry=new a.AdapterRegistry();a.registerComparator("dateLike",a.isDateLike,a.compareDateLike);a.registerComparator("arrayLike",a.isArrayLike,a.compareArrayLike);a.reprRegistry=new a.AdapterRegistry();a.registerRepr("arrayLike",a.isArrayLike,a.reprArrayLike);a.registerRepr("string",a.typeMatcher("string"),a.reprString);a.registerRepr("numbers",a.typeMatcher("number","boolean"),a.reprNumber);a.jsonRegistry=new a.AdapterRegistry();var b=a.concat(a.EXPORT,a.EXPORT_OK);a.EXPORT_TAGS={":common":a.concat(a.EXPORT_OK),":all":b};a.nameFunctions(this)};MochiKit.Base.__new__();if(MochiKit.__export__){compare=MochiKit.Base.compare}MochiKit.Base._exportSymbols(this,MochiKit.Base);if(typeof(dojo)!="undefined"){dojo.provide("MochiKit.Async");dojo.require("MochiKit.Base")}if(typeof(JSAN)!="undefined"){JSAN.use("MochiKit.Base",[])}try{if(typeof(MochiKit.Base)=="undefined"){throw""}}catch(e){throw"MochiKit.Async depends on MochiKit.Base!"}if(typeof(MochiKit.Async)=="undefined"){MochiKit.Async={}}MochiKit.Async.NAME="MochiKit.Async";MochiKit.Async.VERSION="1.4";MochiKit.Async.__repr__=function(){return"["+this.NAME+" "+this.VERSION+"]"};MochiKit.Async.toString=function(){return this.__repr__()};MochiKit.Async.Deferred=function(a){this.chain=[];this.id=this._nextId();this.fired=-1;this.paused=0;this.results=[null,null];this.canceller=a;this.silentlyCancelled=false;this.chained=false};MochiKit.Async.Deferred.prototype={repr:function(){var a;if(this.fired==-1){a="unfired"}else{if(this.fired===0){a="success"}else{a="error"}}return"Deferred("+this.id+", "+a+")"},toString:MochiKit.Base.forwardCall("repr"),_nextId:MochiKit.Base.counter(),cancel:function(){var a=MochiKit.Async;if(this.fired==-1){if(this.canceller){this.canceller(this)}else{this.silentlyCancelled=true}if(this.fired==-1){this.errback(new a.CancelledError(this))}}else{if((this.fired===0)&&(this.results[0] instanceof a.Deferred)){this.results[0].cancel()}}},_resback:function(a){this.fired=((a instanceof Error)?1:0);this.results[this.fired]=a;this._fire()},_check:function(){if(this.fired!=-1){if(!this.silentlyCancelled){throw new MochiKit.Async.AlreadyCalledError(this)}this.silentlyCancelled=false;return}},callback:function(a){this._check();if(a instanceof MochiKit.Async.Deferred){throw new Error("Deferred instances can only be chained if they are the result of a callback")}this._resback(a)},errback:function(b){this._check();var a=MochiKit.Async;if(b instanceof a.Deferred){throw new Error("Deferred instances can only be chained if they are the result of a callback")}if(!(b instanceof Error)){b=new a.GenericError(b)}this._resback(b)},addBoth:function(a){if(arguments.length>1){a=MochiKit.Base.partial.apply(null,arguments)}return this.addCallbacks(a,a)},addCallback:function(a){if(arguments.length>1){a=MochiKit.Base.partial.apply(null,arguments)}return this.addCallbacks(a,null)},addErrback:function(a){if(arguments.length>1){a=MochiKit.Base.partial.apply(null,arguments)}return this.addCallbacks(null,a)},addCallbacks:function(a,b){if(this.chained){throw new Error("Chained Deferreds can not be re-used")}this.chain.push([a,b]);if(this.fired>=0){this._fire()}return this},_fire:function(){var g=this.chain;var k=this.fired;var c=this.results[k];var b=this;var a=null;while(g.length>0&&this.paused===0){var l=g.shift();var j=l[k];if(j===null){continue}try{c=j(c);k=((c instanceof Error)?1:0);if(c instanceof MochiKit.Async.Deferred){a=function(f){b._resback(f);b.paused--;if((b.paused===0)&&(b.fired>=0)){b._fire()}};this.paused++}}catch(h){k=1;if(!(h instanceof Error)){h=new MochiKit.Async.GenericError(h)}c=h}}this.fired=k;this.results[k]=c;if(a&&this.paused){c.addBoth(a);c.chained=true}}};MochiKit.Base.update(MochiKit.Async,{evalJSONRequest:function(){return eval("("+arguments[0].responseText+")")},succeed:function(a){var b=new MochiKit.Async.Deferred();b.callback.apply(b,arguments);return b},fail:function(a){var b=new MochiKit.Async.Deferred();b.errback.apply(b,arguments);return b},getXMLHttpRequest:function(){var a=arguments.callee;if(!a.XMLHttpRequest){var g=[function(){return new XMLHttpRequest()},function(){return new ActiveXObject("Msxml2.XMLHTTP")},function(){return new ActiveXObject("Microsoft.XMLHTTP")},function(){return new ActiveXObject("Msxml2.XMLHTTP.4.0")},function(){throw new MochiKit.Async.BrowserComplianceError("Browser does not support XMLHttpRequest")}];for(var b=0;b<g.length;b++){var c=g[b];try{a.XMLHttpRequest=c;return c()}catch(f){}}}return a.XMLHttpRequest()},_xhr_onreadystatechange:function(g){var a=MochiKit.Base;if(this.readyState==4){try{this.onreadystatechange=null}catch(f){try{this.onreadystatechange=a.noop}catch(f){}}var b=null;try{b=this.status;if(!b&&a.isNotEmpty(this.responseText)){b=304}}catch(f){}if(b==200||b==304){g.callback(this)}else{var c=new MochiKit.Async.XMLHttpRequestError(this,"Request failed");if(c.number){g.errback(c)}else{g.errback(c)}}}},_xhr_canceller:function(a){try{a.onreadystatechange=null}catch(b){try{a.onreadystatechange=MochiKit.Base.noop}catch(b){}}a.abort()},sendXMLHttpRequest:function(f,c){if(typeof(c)=="undefined"||c===null){c=""}var a=MochiKit.Base;var b=MochiKit.Async;var h=new b.Deferred(a.partial(b._xhr_canceller,f));try{f.onreadystatechange=a.bind(b._xhr_onreadystatechange,f,h);f.send(c)}catch(g){try{f.onreadystatechange=null}catch(j){}h.errback(g)}return h},doSimpleXMLHttpRequest:function(f){var c=MochiKit.Async;var g=c.getXMLHttpRequest();if(arguments.length>1){var b=MochiKit.Base;var a=b.queryString.apply(null,b.extend(null,arguments,1));if(a){f+="?"+a}}g.open("GET",f,true);return c.sendXMLHttpRequest(g)},loadJSONDoc:function(b){var a=MochiKit.Async;var c=a.doSimpleXMLHttpRequest.apply(a,arguments);c=c.addCallback(a.evalJSONRequest);return c},wait:function(g,c){var f=new MochiKit.Async.Deferred();var a=MochiKit.Base;if(typeof(c)!="undefined"){f.addCallback(function(){return c})}var b=setTimeout(a.bind("callback",f),Math.floor(g*1000));f.canceller=function(){try{clearTimeout(b)}catch(h){}};return f},callLater:function(f,b){var a=MochiKit.Base;var c=a.partial.apply(a,a.extend(null,arguments,1));return MochiKit.Async.wait(f).addCallback(function(g){return c()})}});MochiKit.Async.DeferredLock=function(){this.waiting=[];this.locked=false;this.id=this._nextId()};MochiKit.Async.DeferredLock.prototype={__class__:MochiKit.Async.DeferredLock,acquire:function(){d=new MochiKit.Async.Deferred();if(this.locked){this.waiting.push(d)}else{this.locked=true;d.callback(this)}return d},release:function(){if(!this.locked){throw TypeError("Tried to release an unlocked DeferredLock")}this.locked=false;if(this.waiting.length>0){this.locked=true;this.waiting.shift().callback(this)}},_nextId:MochiKit.Base.counter(),repr:function(){var a;if(this.locked){a="locked, "+this.waiting.length+" waiting"}else{a="unlocked"}return"DeferredLock("+this.id+", "+a+")"},toString:MochiKit.Base.forwardCall("repr")};MochiKit.Async.DeferredList=function(j,h,b,f,l){MochiKit.Async.Deferred.apply(this,[l]);this.list=j;var a=[];this.resultList=a;this.finishedCount=0;this.fireOnOneCallback=h;this.fireOnOneErrback=b;this.consumeErrors=f;var c=MochiKit.Base.bind(this._cbDeferred,this);for(var g=0;g<j.length;g++){var k=j[g];a.push(undefined);k.addCallback(c,g,true);k.addErrback(c,g,false)}if(j.length===0&&!h){this.callback(this.resultList)}};MochiKit.Async.DeferredList.prototype=new MochiKit.Async.Deferred();MochiKit.Async.DeferredList.prototype._cbDeferred=function(b,c,a){this.resultList[b]=[c,a];this.finishedCount+=1;if(this.fired!==0){if(c&&this.fireOnOneCallback){this.callback([b,a])}else{if(!c&&this.fireOnOneErrback){this.errback(a)}else{if(this.finishedCount==this.list.length){this.callback(this.resultList)}}}}if(!c&&this.consumeErrors){a=null}return a};MochiKit.Async.gatherResults=function(a){var b=new MochiKit.Async.DeferredList(a,false,true,false);b.addCallback(function(g){var c=[];for(var f=0;f<g.length;f++){c.push(g[f][1])}return c});return b};MochiKit.Async.maybeDeferred=function(f){var b=MochiKit.Async;var a;try{var c=f.apply(null,MochiKit.Base.extend([],arguments,1));if(c instanceof b.Deferred){a=c}else{if(c instanceof Error){a=b.fail(c)}else{a=b.succeed(c)}}}catch(g){a=b.fail(g)}return a};MochiKit.Async.EXPORT=["AlreadyCalledError","CancelledError","BrowserComplianceError","GenericError","XMLHttpRequestError","Deferred","succeed","fail","getXMLHttpRequest","doSimpleXMLHttpRequest","loadJSONDoc","wait","callLater","sendXMLHttpRequest","DeferredLock","DeferredList","gatherResults","maybeDeferred"];MochiKit.Async.EXPORT_OK=["evalJSONRequest"];MochiKit.Async.__new__=function(){var a=MochiKit.Base;var b=a.partial(a._newNamedError,this);b("AlreadyCalledError",function(c){this.deferred=c});b("CancelledError",function(c){this.deferred=c});b("BrowserComplianceError",function(c){this.message=c});b("GenericError",function(c){this.message=c});b("XMLHttpRequestError",function(c,g){this.req=c;this.message=g;try{this.number=c.status}catch(f){}});this.EXPORT_TAGS={":common":this.EXPORT,":all":a.concat(this.EXPORT,this.EXPORT_OK)};a.nameFunctions(this)};MochiKit.Async.__new__();MochiKit.Base._exportSymbols(this,MochiKit.Async);if(typeof(dojo)!="undefined"){dojo.provide("MochiKit.DOM");dojo.require("MochiKit.Base")}if(typeof(JSAN)!="undefined"){JSAN.use("MochiKit.Base",[])}try{if(typeof(MochiKit.Base)=="undefined"){throw""}}catch(e){throw"MochiKit.DOM depends on MochiKit.Base!"}if(typeof(MochiKit.DOM)=="undefined"){MochiKit.DOM={}}MochiKit.DOM.NAME="MochiKit.DOM";MochiKit.DOM.VERSION="1.4";MochiKit.DOM.__repr__=function(){return"["+this.NAME+" "+this.VERSION+"]"};MochiKit.DOM.toString=function(){return this.__repr__()};MochiKit.DOM.EXPORT=["removeEmptyTextNodes","formContents","currentWindow","currentDocument","withWindow","withDocument","registerDOMConverter","coerceToDOM","createDOM","createDOMFunc","getNodeAttribute","setNodeAttribute","updateNodeAttributes","appendChildNodes","replaceChildNodes","removeElement","swapDOM","BUTTON","TT","PRE","H1","H2","H3","BR","CANVAS","HR","LABEL","TEXTAREA","FORM","STRONG","SELECT","OPTION","OPTGROUP","LEGEND","FIELDSET","P","UL","OL","LI","TD","TR","THEAD","TBODY","TFOOT","TABLE","TH","INPUT","SPAN","A","DIV","IMG","getElement","$","getElementsByTagAndClassName","addToCallStack","addLoadEvent","focusOnLoad","setElementClass","toggleElementClass","addElementClass","removeElementClass","swapElementClass","hasElementClass","escapeHTML","toHTML","emitHTML","scrapeText"];MochiKit.DOM.EXPORT_OK=["domConverters"];MochiKit.DOM.DEPRECATED=[["computedStyle","MochiKit.Style.computedStyle","1.4"],["elementDimensions","MochiKit.Style.getElementDimensions","1.4"],["elementPosition","MochiKit.Style.getElementPosition","1.4"],["hideElement","MochiKit.Style.hideElement","1.4"],["setElementDimensions","MochiKit.Style.setElementDimensions","1.4"],["setElementPosition","MochiKit.Style.setElementPosition","1.4"],["setDisplayForElement","MochiKit.Style.setDisplayForElement","1.4"],["setOpacity","MochiKit.Style.setOpacity","1.4"],["showElement","MochiKit.Style.showElement","1.4"],["Coordinates","MochiKit.Style.Coordinates","1.4"],["Dimensions","MochiKit.Style.Dimensions","1.4"]];MochiKit.DOM.getViewportDimensions=new Function('if (!MochiKit["Style"]) {    throw new Error("This function has been deprecated and depends on MochiKit.Style.");}return MochiKit.Style.getViewportDimensions.apply(this, arguments);');MochiKit.Base.update(MochiKit.DOM,{currentWindow:function(){return MochiKit.DOM._window},currentDocument:function(){return MochiKit.DOM._document},withWindow:function(j,f){var c=MochiKit.DOM;var b=c._document;var a=c._win;var h;try{c._window=j;c._document=j.document;h=f()}catch(g){c._window=a;c._document=b;throw g}c._window=a;c._document=b;return h},formContents:function(f){var g=[];var c=[];var a=MochiKit.Base;var b=MochiKit.DOM;if(typeof(f)=="undefined"||f===null){f=b._document}else{f=b.getElement(f)}a.nodeWalk(f,function(n){var h=n.name;if(a.isNotEmpty(h)){var l=n.nodeName;if(l=="INPUT"&&(n.type=="radio"||n.type=="checkbox")&&!n.checked){return null}if(l=="SELECT"){if(n.type=="select-one"){if(n.selectedIndex>=0){var k=n.options[n.selectedIndex];g.push(h);c.push((k.value)?k.value:k.text);return null}g.push(h);c.push("");return null}else{var m=n.options;if(!m.length){g.push(h);c.push("");return null}for(var j=0;j<m.length;j++){var k=m[j];if(!k.selected){continue}g.push(h);c.push((k.value)?k.value:k.text)}return null}}if(l=="FORM"||l=="P"||l=="SPAN"||l=="DIV"){return n.childNodes}g.push(h);c.push(n.value||"");return null}return n.childNodes});return[g,c]},withDocument:function(h,c){var b=MochiKit.DOM;var a=b._document;var g;try{b._document=h;g=c()}catch(f){b._document=a;throw f}b._document=a;return g},registerDOMConverter:function(b,a,f,c){MochiKit.DOM.domConverters.register(b,a,f,c)},coerceToDOM:function(f,r){var g=MochiKit.Base;var l=MochiKit.Iter;var q=MochiKit.DOM;if(l){var n=l.iter;var b=l.repeat;var a=g.map}var o=q.domConverters;var c=arguments.callee;var j=g.NotFound;while(true){if(typeof(f)=="undefined"||f===null){return null}if(typeof(f.nodeType)!="undefined"&&f.nodeType>0){return f}if(typeof(f)=="number"||typeof(f)=="boolean"){f=f.toString()}if(typeof(f)=="string"){return q._document.createTextNode(f)}if(typeof(f.__dom__)=="function"){f=f.__dom__(r);continue}if(typeof(f.dom)=="function"){f=f.dom(r);continue}if(typeof(f)=="function"){f=f.apply(r,[r]);continue}if(l){var h=null;try{h=n(f)}catch(k){}if(h){return a(c,h,b(r))}}try{f=o.match(f,r);continue}catch(k){if(k!=j){throw k}}return q._document.createTextNode(f.toString())}return undefined},setNodeAttribute:function(b,a,c){var g={};g[a]=c;try{return MochiKit.DOM.updateNodeAttributes(b,g)}catch(f){}return null},getNodeAttribute:function(f,a){var c=MochiKit.DOM;var b=c.attributeArray.renames[a];f=c.getElement(f);try{if(b){return f[b]}return f.getAttribute(a)}catch(g){}return null},updateNodeAttributes:function(b,h){var c=b;var m=MochiKit.DOM;if(typeof(b)=="string"){c=m.getElement(b)}if(h){var l=MochiKit.Base.updatetree;if(m.attributeArray.compliant){for(var f in h){var j=h[f];if(typeof(j)=="object"&&typeof(c[f])=="object"){l(c[f],j)}else{if(f.substring(0,2)=="on"){if(typeof(j)=="string"){j=new Function(j)}c[f]=j}else{c.setAttribute(f,j)}}}}else{var g=m.attributeArray.renames;for(f in h){j=h[f];var a=g[f];if(f=="style"&&typeof(j)=="string"){c.style.cssText=j}else{if(typeof(a)=="string"){c[a]=j}else{if(typeof(c[f])=="object"&&typeof(j)=="object"){l(c[f],j)}else{if(f.substring(0,2)=="on"){if(typeof(j)=="string"){j=new Function(j)}c[f]=j}else{c.setAttribute(f,j)}}}}}}}return c},appendChildNodes:function(c){var b=c;var a=MochiKit.DOM;if(typeof(c)=="string"){b=a.getElement(c)}var f=[a.coerceToDOM(MochiKit.Base.extend(null,arguments,1),b)];var g=MochiKit.Base.concat;while(f.length){var h=f.shift();if(typeof(h)=="undefined"||h===null){}else{if(typeof(h.nodeType)=="number"){b.appendChild(h)}else{f=g(h,f)}}}return b},replaceChildNodes:function(c){var b=c;var a=MochiKit.DOM;if(typeof(c)=="string"){b=a.getElement(c);arguments[0]=b}var f;while((f=b.firstChild)){b.removeChild(f)}if(arguments.length<2){return b}else{return a.appendChildNodes.apply(this,arguments)}},createDOM:function(g,f){var j;var b=MochiKit.DOM;var a=MochiKit.Base;if(typeof(f)=="string"||typeof(f)=="number"){var c=a.extend([g,null],arguments,1);return arguments.callee.apply(this,c)}if(typeof(g)=="string"){if(f&&!b.attributeArray.compliant){var h="";if("name" in f){h+=' name="'+b.escapeHTML(f.name)+'"'}if(g=="input"&&"type" in f){h+=' type="'+b.escapeHTML(f.type)+'"'}if(h){g="<"+g+h+">"}}j=b._document.createElement(g)}else{j=g}if(f){b.updateNodeAttributes(j,f)}if(arguments.length<=2){return j}else{var c=a.extend([j],arguments,2);return b.appendChildNodes.apply(this,c)}},createDOMFunc:function(){var a=MochiKit.Base;return a.partial.apply(this,a.extend([MochiKit.DOM.createDOM],arguments))},removeElement:function(a){var b=MochiKit.DOM.getElement(a);b.parentNode.removeChild(b);return b},swapDOM:function(b,f){var a=MochiKit.DOM;b=a.getElement(b);var c=b.parentNode;if(f){f=a.getElement(f);c.replaceChild(f,b)}else{c.removeChild(b)}return f},getElement:function(b){var a=MochiKit.DOM;if(arguments.length==1){return((typeof(b)=="string")?a._document.getElementById(b):b)}else{return MochiKit.Base.map(a.getElement,arguments)}},getElementsByTagAndClassName:function(g,l,m){var n=MochiKit.DOM;if(typeof(g)=="undefined"||g===null){g="*"}if(typeof(m)=="undefined"||m===null){m=n._document}m=n.getElement(m);var f=(m.getElementsByTagName(g)||n._document.all);if(typeof(l)=="undefined"||l===null){return MochiKit.Base.extend(null,f)}var b=[];for(var k=0;k<f.length;k++){var c=f[k];var a=c.className.split(" ");for(var h=0;h<a.length;h++){if(a[h]==l){b.push(c);break}}}return b},_newCallStack:function(c,a){var b=function(){var f=arguments.callee.callStack;for(var g=0;g<f.length;g++){if(f[g].apply(this,arguments)===false){break}}if(a){try{this[c]=null}catch(h){}}};b.callStack=[];return b},addToCallStack:function(j,h,g,c){var b=MochiKit.DOM;var f=j[h];var a=f;if(!(typeof(f)=="function"&&typeof(f.callStack)=="object"&&f.callStack!==null)){a=b._newCallStack(h,c);if(typeof(f)=="function"){a.callStack.push(f)}j[h]=a}a.callStack.push(g)},addLoadEvent:function(b){var a=MochiKit.DOM;a.addToCallStack(a._window,"onload",b,true)},focusOnLoad:function(b){var a=MochiKit.DOM;a.addLoadEvent(function(){b=a.getElement(b);if(b){b.focus()}})},setElementClass:function(b,c){var a=MochiKit.DOM;var f=a.getElement(b);if(a.attributeArray.compliant){f.setAttribute("class",c)}else{f.setAttribute("className",c)}},toggleElementClass:function(c){var a=MochiKit.DOM;for(var b=1;b<arguments.length;b++){var f=a.getElement(arguments[b]);if(!a.addElementClass(f,c)){a.removeElementClass(f,c)}}},addElementClass:function(g,h){var b=MochiKit.DOM;var j=b.getElement(g);var a=j.className;if(a.length===0){b.setElementClass(j,h);return true}if(a==h){return false}var f=j.className.split(" ");for(var c=0;c<f.length;c++){if(f[c]==h){return false}}b.setElementClass(j,a+" "+h);return true},removeElementClass:function(g,h){var b=MochiKit.DOM;var j=b.getElement(g);var a=j.className;if(a.length===0){return false}if(a==h){b.setElementClass(j,"");return true}var f=j.className.split(" ");for(var c=0;c<f.length;c++){if(f[c]==h){f.splice(c,1);b.setElementClass(j,f.join(" "));return true}}return false},swapElementClass:function(c,g,a){var f=MochiKit.DOM.getElement(c);var b=MochiKit.DOM.removeElementClass(f,g);if(b){MochiKit.DOM.addElementClass(f,a)}return b},hasElementClass:function(f,g){var k=MochiKit.DOM.getElement(f);var c=k.className.split(" ");for(var b=1;b<arguments.length;b++){var h=false;for(var a=0;a<c.length;a++){if(c[a]==arguments[b]){h=true;break}}if(!h){return false}}return true},escapeHTML:function(a){return a.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/</g,"&lt;").replace(/>/g,"&gt;")},toHTML:function(a){return MochiKit.DOM.emitHTML(a).join("")},emitHTML:function(f,k){if(typeof(k)=="undefined"||k===null){k=[]}var m=[f];var s=MochiKit.DOM;var r=s.escapeHTML;var b=s.attributeArray;while(m.length){f=m.pop();if(typeof(f)=="string"){k.push(f)}else{if(f.nodeType==1){k.push("<"+f.nodeName.toLowerCase());var g=[];var n=b(f);for(var h=0;h<n.length;h++){var o=n[h];g.push([" ",o.name,'="',r(o.value),'"'])}g.sort();for(h=0;h<g.length;h++){var q=g[h];for(var c=0;c<q.length;c++){k.push(q[c])}}if(f.hasChildNodes()){k.push(">");m.push("</"+f.nodeName.toLowerCase()+">");var l=f.childNodes;for(h=l.length-1;h>=0;h--){m.push(l[h])}}else{k.push("/>")}}else{if(f.nodeType==3){k.push(r(f.nodeValue))}}}}return k},scrapeText:function(b,a){var c=[];(function(h){var j=h.childNodes;if(j){for(var g=0;g<j.length;g++){arguments.callee.call(this,j[g])}}var f=h.nodeValue;if(typeof(f)=="string"){c.push(f)}})(MochiKit.DOM.getElement(b));if(a){return c}else{return c.join("")}},removeEmptyTextNodes:function(b){b=MochiKit.DOM.getElement(b);for(var a=0;a<b.childNodes.length;a++){var c=b.childNodes[a];if(c.nodeType==3&&!/\S/.test(c.nodeValue)){c.parentNode.removeChild(c)}}},__new__:function(l){var g=MochiKit.Base;if(typeof(document)!="undefined"){this._document=document}else{if(MochiKit.MockDOM){this._document=MochiKit.MockDOM.document}}this._window=l;this.domConverters=new g.AdapterRegistry();var o=this._document.createElement("span");var b;if(o&&o.attributes&&o.attributes.length>0){var c=g.filter;b=function(a){return c(b.ignoreAttrFilter,a.attributes)};b.ignoreAttr={};var q=o.attributes;var h=b.ignoreAttr;for(var k=0;k<q.length;k++){var n=q[k];h[n.name]=n.value}b.ignoreAttrFilter=function(m){return(b.ignoreAttr[m.name]!=m.value)};b.compliant=false;b.renames={"class":"className",checked:"defaultChecked",usemap:"useMap","for":"htmlFor",readonly:"readOnly"}}else{b=function(a){return a.attributes};b.compliant=true;b.renames={}}this.attributeArray=b;var j=function(t,a){var m=a[1].split(".");var s="";var r={};s+="if (!MochiKit."+m[1]+') { throw new Error("';s+="This function has been deprecated and depends on MochiKit.";s+=m[1]+'.");}';s+="return MochiKit."+m[1]+"."+a[0];s+=".apply(this, arguments);";r[m[2]]=new Function(s);MochiKit.Base.update(MochiKit[t],r)};for(var k;k<MochiKit.DOM.DEPRECATED.length;k++){j("DOM",MochiKit.DOM.DEPRECATED[k])}var f=this.createDOMFunc;this.UL=f("ul");this.OL=f("ol");this.LI=f("li");this.TD=f("td");this.TR=f("tr");this.TBODY=f("tbody");this.THEAD=f("thead");this.TFOOT=f("tfoot");this.TABLE=f("table");this.TH=f("th");this.INPUT=f("input");this.SPAN=f("span");this.A=f("a");this.DIV=f("div");this.IMG=f("img");this.BUTTON=f("button");this.TT=f("tt");this.PRE=f("pre");this.H1=f("h1");this.H2=f("h2");this.H3=f("h3");this.BR=f("br");this.HR=f("hr");this.LABEL=f("label");this.TEXTAREA=f("textarea");this.FORM=f("form");this.P=f("p");this.SELECT=f("select");this.OPTION=f("option");this.OPTGROUP=f("optgroup");this.LEGEND=f("legend");this.FIELDSET=f("fieldset");this.STRONG=f("strong");this.CANVAS=f("canvas");this.$=this.getElement;this.EXPORT_TAGS={":common":this.EXPORT,":all":g.concat(this.EXPORT,this.EXPORT_OK)};g.nameFunctions(this)}});MochiKit.DOM.__new__(((typeof(window)=="undefined")?this:window));if(MochiKit.__export__){withWindow=MochiKit.DOM.withWindow;withDocument=MochiKit.DOM.withDocument}MochiKit.Base._exportSymbols(this,MochiKit.DOM);if(typeof(dojo)!="undefined"){dojo.provide("MochiKit.Style");dojo.require("MochiKit.Base");dojo.require("MochiKit.DOM")}if(typeof(JSAN)!="undefined"){JSAN.use("MochiKit.Base",[])}try{if(typeof(MochiKit.Base)=="undefined"){throw""}}catch(e){throw"MochiKit.Style depends on MochiKit.Base!"}try{if(typeof(MochiKit.DOM)=="undefined"){throw""}}catch(e){throw"MochiKit.Style depends on MochiKit.DOM!"}if(typeof(MochiKit.Style)=="undefined"){MochiKit.Style={}}MochiKit.Style.NAME="MochiKit.Style";MochiKit.Style.VERSION="1.4";MochiKit.Style.__repr__=function(){return"["+this.NAME+" "+this.VERSION+"]"};MochiKit.Style.toString=function(){return this.__repr__()};MochiKit.Style.EXPORT_OK=[];MochiKit.Style.EXPORT=["setOpacity","computedStyle","getElementDimensions","elementDimensions","setElementDimensions","getElementPosition","elementPosition","setElementPosition","setDisplayForElement","hideElement","showElement","getViewportDimensions","Dimensions","Coordinates"];MochiKit.Style.Dimensions=function(a,b){this.w=a;this.h=b};MochiKit.Style.Dimensions.prototype.__repr__=function(){var a=MochiKit.Base.repr;return"{w: "+a(this.w)+", h: "+a(this.h)+"}"};MochiKit.Style.Dimensions.prototype.toString=function(){return this.__repr__()};MochiKit.Style.Coordinates=function(a,b){this.x=a;this.y=b};MochiKit.Style.Coordinates.prototype.__repr__=function(){var a=MochiKit.Base.repr;return"{x: "+a(this.x)+", y: "+a(this.y)+"}"};MochiKit.Style.Coordinates.prototype.toString=function(){return this.__repr__()};MochiKit.Base.update(MochiKit.Style,{computedStyle:function(f,a){var j=MochiKit.DOM;var h=j._document;f=j.getElement(f);a=MochiKit.Base.camelize(a);if(!f||f==h){return undefined}if(a=="opacity"&&f.filters){try{return f.filters.item("DXImageTransform.Microsoft.Alpha").opacity/100}catch(g){try{return f.filters.item("alpha").opacity/100}catch(g){}}}if(f.currentStyle){return f.currentStyle[a]}if(typeof(h.defaultView)=="undefined"){return undefined}if(h.defaultView===null){return undefined}var c=h.defaultView.getComputedStyle(f,null);if(typeof(c)=="undefined"||c===null){return undefined}var b=a.replace(/([A-Z])/g,"-$1").toLowerCase();return c.getPropertyValue(b)},setOpacity:function(a,b){a=MochiKit.DOM.getElement(a);MochiKit.DOM.updateNodeAttributes(a,{style:{opacity:b,"-moz-opacity":b,"-khtml-opacity":b,filter:" alpha(opacity="+(b*100)+")"}})},getElementPosition:function(f,g,o){var r=MochiKit.Style;var h=MochiKit.DOM;f=h.getElement(f);if(!f||(!(f.x&&f.y)&&(!f.parentNode==null||r.computedStyle(f,"display")=="none"))){return undefined}var l=new r.Coordinates(0,0);var j=null;var q=null;var k=o?o:MochiKit.DOM._document;var n=k.documentElement;var m=k.body;if(!f.parentNode&&f.x&&f.y){l.x+=f.x||0;l.y+=f.y||0}else{if(f.getBoundingClientRect){j=f.getBoundingClientRect();l.x+=j.left+(n.scrollLeft||m.scrollLeft)-(n.clientLeft||0);l.y+=j.top+(n.scrollTop||m.scrollTop)-(n.clientTop||0)}else{if(f.offsetParent){l.x+=f.offsetLeft;l.y+=f.offsetTop;q=f.offsetParent;if(q!=f){while(q){l.x+=q.offsetLeft;l.y+=q.offsetTop;q=q.offsetParent}}var a=navigator.userAgent.toLowerCase();if((typeof(opera)!="undefined"&&parseFloat(opera.version())<9)||(a.indexOf("safari")!=-1&&r.computedStyle(f,"position")=="absolute")){l.x-=m.offsetLeft;l.y-=m.offsetTop}}}}if(typeof(g)!="undefined"){g=arguments.callee(g);if(g){l.x-=(g.x||0);l.y-=(g.y||0)}}if(f.parentNode){q=f.parentNode}else{q=null}while(q&&q.tagName!="BODY"&&q.tagName!="HTML"){l.x-=q.scrollLeft;l.y-=q.scrollTop;if(q.parentNode){q=q.parentNode}else{q=null}}return l},setElementPosition:function(c,b,a){c=MochiKit.DOM.getElement(c);if(typeof(a)=="undefined"){a="px"}MochiKit.DOM.updateNodeAttributes(c,{style:{left:b.x+a,top:b.y+a}})},getElementDimensions:function(f){var a=MochiKit.Style;var j=MochiKit.DOM;if(typeof(f.w)=="number"||typeof(f.h)=="number"){return new a.Dimensions(f.w||0,f.h||0)}f=j.getElement(f);if(!f){return undefined}if(a.computedStyle(f,"display")!="none"){return new a.Dimensions(f.offsetWidth||0,f.offsetHeight||0)}var c=f.style;var h=c.visibility;var b=c.position;c.visibility="hidden";c.position="absolute";c.display="";var k=f.offsetWidth;var g=f.offsetHeight;c.display="none";c.position=b;c.visibility=h;return new a.Dimensions(k,g)},setElementDimensions:function(c,b,a){c=MochiKit.DOM.getElement(c);if(typeof(a)=="undefined"){a="px"}MochiKit.DOM.updateNodeAttributes(c,{style:{width:b.w+a,height:b.h+a}})},setDisplayForElement:function(g,b){var f=MochiKit.Base.extend(null,arguments,1);var c=MochiKit.DOM.getElement;for(var a=0;a<f.length;a++){var b=c(f[a]);if(b){b.style.display=g}}},getViewportDimensions:function(){var f=new MochiKit.Style.Dimensions();var c=MochiKit.DOM._window;var a=MochiKit.DOM._document.body;if(c.innerWidth){f.w=c.innerWidth;f.h=c.innerHeight}else{if(a.parentElement.clientWidth){f.w=a.parentElement.clientWidth;f.h=a.parentElement.clientHeight}else{if(a&&a.clientWidth){f.w=a.clientWidth;f.h=a.clientHeight}}}return f},__new__:function(){var a=MochiKit.Base;this.elementPosition=this.getElementPosition;this.elementDimensions=this.getElementDimensions;this.hideElement=a.partial(this.setDisplayForElement,"none");this.showElement=a.partial(this.setDisplayForElement,"block");this.EXPORT_TAGS={":common":this.EXPORT,":all":a.concat(this.EXPORT,this.EXPORT_OK)};a.nameFunctions(this)}});MochiKit.Style.__new__();MochiKit.Base._exportSymbols(this,MochiKit.Style);if(typeof(dojo)!="undefined"){dojo.provide("MochiKit.Signal");dojo.require("MochiKit.Base");dojo.require("MochiKit.DOM");dojo.require("MochiKit.Style")}if(typeof(JSAN)!="undefined"){JSAN.use("MochiKit.Base",[]);JSAN.use("MochiKit.DOM",[])}try{if(typeof(MochiKit.Base)=="undefined"){throw""}}catch(e){throw"MochiKit.Signal depends on MochiKit.Base!"}try{if(typeof(MochiKit.DOM)=="undefined"){throw""}}catch(e){throw"MochiKit.Signal depends on MochiKit.DOM!"}try{if(typeof(MochiKit.Style)=="undefined"){throw""}}catch(e){throw"MochiKit.Signal depends on MochiKit.Style!"}if(typeof(MochiKit.Signal)=="undefined"){MochiKit.Signal={}}MochiKit.Signal.NAME="MochiKit.Signal";MochiKit.Signal.VERSION="1.4";MochiKit.Signal._observers=[];MochiKit.Signal.Event=function(b,a){this._event=a||window.event;this._src=b};MochiKit.Base.update(MochiKit.Signal.Event.prototype,{__repr__:function(){var a=MochiKit.Base.repr;var b="{event(): "+a(this.event())+", src(): "+a(this.src())+", type(): "+a(this.type())+", target(): "+a(this.target())+", modifier(): {alt: "+a(this.modifier().alt)+", ctrl: "+a(this.modifier().ctrl)+", meta: "+a(this.modifier().meta)+", shift: "+a(this.modifier().shift)+", any: "+a(this.modifier().any)+"}";if(this.type()&&this.type().indexOf("key")===0){b+=", key(): {code: "+a(this.key().code)+", string: "+a(this.key().string)+"}"}if(this.type()&&(this.type().indexOf("mouse")===0||this.type().indexOf("click")!=-1||this.type()=="contextmenu")){b+=", mouse(): {page: "+a(this.mouse().page)+", client: "+a(this.mouse().client);if(this.type()!="mousemove"){b+=", button: {left: "+a(this.mouse().button.left)+", middle: "+a(this.mouse().button.middle)+", right: "+a(this.mouse().button.right)+"}}"}else{b+="}"}}if(this.type()=="mouseover"||this.type()=="mouseout"){b+=", relatedTarget(): "+a(this.relatedTarget())}b+="}";return b},toString:function(){return this.__repr__()},src:function(){return this._src},event:function(){return this._event},type:function(){return this._event.type||undefined},target:function(){return this._event.target||this._event.srcElement},_relatedTarget:null,relatedTarget:function(){if(this._relatedTarget!==null){return this._relatedTarget}var a=null;if(this.type()=="mouseover"){a=(this._event.relatedTarget||this._event.fromElement)}else{if(this.type()=="mouseout"){a=(this._event.relatedTarget||this._event.toElement)}}if(a!==null){this._relatedTarget=a;return a}return undefined},_modifier:null,modifier:function(){if(this._modifier!==null){return this._modifier}var a={};a.alt=this._event.altKey;a.ctrl=this._event.ctrlKey;a.meta=this._event.metaKey||false;a.shift=this._event.shiftKey;a.any=a.alt||a.ctrl||a.shift||a.meta;this._modifier=a;return a},_key:null,key:function(){if(this._key!==null){return this._key}var a={};if(this.type()&&this.type().indexOf("key")===0){if(this.type()=="keydown"||this.type()=="keyup"){a.code=this._event.keyCode;a.string=(MochiKit.Signal._specialKeys[a.code]||"KEY_UNKNOWN");this._key=a;return a}else{if(this.type()=="keypress"){a.code=0;a.string="";if(typeof(this._event.charCode)!="undefined"&&this._event.charCode!==0&&!MochiKit.Signal._specialMacKeys[this._event.charCode]){a.code=this._event.charCode;a.string=String.fromCharCode(a.code)}else{if(this._event.keyCode&&typeof(this._event.charCode)=="undefined"){a.code=this._event.keyCode;a.string=String.fromCharCode(a.code)}}this._key=a;return a}}}return undefined},_mouse:null,mouse:function(){if(this._mouse!==null){return this._mouse}var c={};var f=this._event;if(this.type()&&(this.type().indexOf("mouse")===0||this.type().indexOf("click")!=-1||this.type()=="contextmenu")){c.client=new MochiKit.Style.Coordinates(0,0);if(f.clientX||f.clientY){c.client.x=(!f.clientX||f.clientX<0)?0:f.clientX;c.client.y=(!f.clientY||f.clientY<0)?0:f.clientY}c.page=new MochiKit.Style.Coordinates(0,0);if(f.pageX||f.pageY){c.page.x=(!f.pageX||f.pageX<0)?0:f.pageX;c.page.y=(!f.pageY||f.pageY<0)?0:f.pageY}else{var g=MochiKit.DOM._document.documentElement;var a=MochiKit.DOM._document.body;c.page.x=f.clientX+(g.scrollLeft||a.scrollLeft)-(g.clientLeft||0);c.page.y=f.clientY+(g.scrollTop||a.scrollTop)-(g.clientTop||0)}if(this.type()!="mousemove"){c.button={};c.button.left=false;c.button.right=false;c.button.middle=false;if(f.which){c.button.left=(f.which==1);c.button.middle=(f.which==2);c.button.right=(f.which==3)}else{c.button.left=!!(f.button&1);c.button.right=!!(f.button&2);c.button.middle=!!(f.button&4)}}this._mouse=c;return c}return undefined},stop:function(){this.stopPropagation();this.preventDefault()},stopPropagation:function(){if(this._event.stopPropagation){this._event.stopPropagation()}else{this._event.cancelBubble=true}},preventDefault:function(){if(this._event.preventDefault){this._event.preventDefault()}else{if(this._confirmUnload===null){this._event.returnValue=false}}},_confirmUnload:null,confirmUnload:function(a){if(this.type()=="beforeunload"){this._confirmUnload=a;this._event.returnValue=a}}});MochiKit.Signal._specialMacKeys={3:"KEY_ENTER",63289:"KEY_NUM_PAD_CLEAR",63276:"KEY_PAGE_UP",63277:"KEY_PAGE_DOWN",63275:"KEY_END",63273:"KEY_HOME",63234:"KEY_ARROW_LEFT",63232:"KEY_ARROW_UP",63235:"KEY_ARROW_RIGHT",63233:"KEY_ARROW_DOWN",63302:"KEY_INSERT",63272:"KEY_DELETE"};for(i=63236;i<=63242;i++){MochiKit.Signal._specialMacKeys[i]="KEY_F"+(i-63236+1)}MochiKit.Signal._specialKeys={8:"KEY_BACKSPACE",9:"KEY_TAB",12:"KEY_NUM_PAD_CLEAR",13:"KEY_ENTER",16:"KEY_SHIFT",17:"KEY_CTRL",18:"KEY_ALT",19:"KEY_PAUSE",20:"KEY_CAPS_LOCK",27:"KEY_ESCAPE",32:"KEY_SPACEBAR",33:"KEY_PAGE_UP",34:"KEY_PAGE_DOWN",35:"KEY_END",36:"KEY_HOME",37:"KEY_ARROW_LEFT",38:"KEY_ARROW_UP",39:"KEY_ARROW_RIGHT",40:"KEY_ARROW_DOWN",44:"KEY_PRINT_SCREEN",45:"KEY_INSERT",46:"KEY_DELETE",59:"KEY_SEMICOLON",91:"KEY_WINDOWS_LEFT",92:"KEY_WINDOWS_RIGHT",93:"KEY_SELECT",106:"KEY_NUM_PAD_ASTERISK",107:"KEY_NUM_PAD_PLUS_SIGN",109:"KEY_NUM_PAD_HYPHEN-MINUS",110:"KEY_NUM_PAD_FULL_STOP",111:"KEY_NUM_PAD_SOLIDUS",144:"KEY_NUM_LOCK",145:"KEY_SCROLL_LOCK",186:"KEY_SEMICOLON",187:"KEY_EQUALS_SIGN",188:"KEY_COMMA",189:"KEY_HYPHEN-MINUS",190:"KEY_FULL_STOP",191:"KEY_SOLIDUS",192:"KEY_GRAVE_ACCENT",219:"KEY_LEFT_SQUARE_BRACKET",220:"KEY_REVERSE_SOLIDUS",221:"KEY_RIGHT_SQUARE_BRACKET",222:"KEY_APOSTROPHE"};for(var i=48;i<=57;i++){MochiKit.Signal._specialKeys[i]="KEY_"+(i-48)}for(i=65;i<=90;i++){MochiKit.Signal._specialKeys[i]="KEY_"+String.fromCharCode(i)}for(i=96;i<=105;i++){MochiKit.Signal._specialKeys[i]="KEY_NUM_PAD_"+(i-96)}for(i=112;i<=123;i++){MochiKit.Signal._specialKeys[i]="KEY_F"+(i-112+1)}MochiKit.Base.update(MochiKit.Signal,{__repr__:function(){return"["+this.NAME+" "+this.VERSION+"]"},toString:function(){return this.__repr__()},_unloadCache:function(){var a=MochiKit.Signal;var f=a._observers;for(var b=0;b<f.length;b++){a._disconnect(f[b])}delete a._observers;try{window.onload=undefined}catch(c){}try{window.onunload=undefined}catch(c){}},_listener:function(g,b,f,a){var c=MochiKit.Signal.Event;if(!a){return MochiKit.Base.bind(b,f)}f=f||g;if(typeof(b)=="string"){return function(h){f[b].apply(f,[new c(g,h)])}}else{return function(h){b.apply(f,[new c(g,h)])}}},connect:function(a,m,k,b){a=MochiKit.DOM.getElement(a);var l=MochiKit.Signal;if(typeof(m)!="string"){throw new Error("'sig' must be a string")}var h=null;var f=null;if(typeof(b)!="undefined"){h=k;f=b;if(typeof(b)=="string"){if(typeof(k[b])!="function"){throw new Error("'funcOrStr' must be a function on 'objOrFunc'")}}else{if(typeof(b)!="function"){throw new Error("'funcOrStr' must be a function or string")}}}else{if(typeof(k)!="function"){throw new Error("'objOrFunc' must be a function if 'funcOrStr' is not given")}else{f=k}}if(typeof(h)=="undefined"||h===null){h=a}var j=!!(a.addEventListener||a.attachEvent);var c=l._listener(a,f,h,j);if(a.addEventListener){a.addEventListener(m.substr(2),c,false)}else{if(a.attachEvent){a.attachEvent(m,c)}}var g=[a,m,c,j,k,b];l._observers.push(g);return g},_disconnect:function(b){if(!b[3]){return}var f=b[0];var c=b[1];var a=b[2];if(f.removeEventListener){f.removeEventListener(c.substr(2),a,false)}else{if(f.detachEvent){f.detachEvent(c,a)}else{throw new Error("'src' must be a DOM element")}}},disconnect:function(j){var n=MochiKit.Signal;var k=n._observers;var f=MochiKit.Base;if(arguments.length>1){var a=MochiKit.DOM.getElement(arguments[0]);var q=arguments[1];var h=arguments[2];var c=arguments[3];for(var g=k.length-1;g>=0;g--){var b=k[g];if(b[0]===a&&b[1]===q&&b[4]===h&&b[5]===c){n._disconnect(b);k.splice(g,1);return true}}}else{var l=f.findIdentical(k,j);if(l>=0){n._disconnect(j);k.splice(l,1);return true}}return false},disconnectAll:function(a,n){a=MochiKit.DOM.getElement(a);var b=MochiKit.Base;var c=b.flattenArguments(b.extend(null,arguments,1));var k=MochiKit.Signal;var j=k._disconnect;var h=k._observers;if(c.length===0){for(var g=h.length-1;g>=0;g--){var f=h[g];if(f[0]===a){j(f);h.splice(g,1)}}}else{var l={};for(var g=0;g<c.length;g++){l[c[g]]=true}for(var g=h.length-1;g>=0;g--){var f=h[g];if(f[0]===a&&f[1] in l){j(f);h.splice(g,1)}}}},signal:function(j,h){var g=MochiKit.Signal._observers;j=MochiKit.DOM.getElement(j);var a=MochiKit.Base.extend(null,arguments,2);var k=[];for(var b=0;b<g.length;b++){var c=g[b];if(c[0]===j&&c[1]===h){try{c[2].apply(j,a)}catch(f){k.push(f)}}}if(k.length==1){throw k[0]}else{if(k.length>1){var f=new Error("Multiple errors thrown in handling 'sig', see errors property");f.errors=k;throw f}}}});MochiKit.Signal.EXPORT_OK=[];MochiKit.Signal.EXPORT=["connect","disconnect","signal","disconnectAll"];MochiKit.Signal.__new__=function(c){var a=MochiKit.Base;this._document=document;this._window=c;try{this.connect(window,"onunload",this._unloadCache)}catch(b){}this.EXPORT_TAGS={":common":this.EXPORT,":all":a.concat(this.EXPORT,this.EXPORT_OK)};a.nameFunctions(this)};MochiKit.Signal.__new__(this);if(MochiKit.__export__){connect=MochiKit.Signal.connect;disconnect=MochiKit.Signal.disconnect;disconnectAll=MochiKit.Signal.disconnectAll;signal=MochiKit.Signal.signal}MochiKit.Base._exportSymbols(this,MochiKit.Signal);MochiKit.Base.update(MochiKit.Base,{isIE:function(){return/MSIE/.test(navigator.userAgent)},isGecko:function(){return/Gecko/.test(navigator.userAgent)},isKHTML:function(){return/Konqueror|Safari|KHTML/.test(navigator.userAgent)},isSafari:function(){return/AppleWebKit'/.test(navigator.appVersion)},isOpera:function(){return/Opera/.test(navigator.userAgent)}});MochiKit.Base.update(MochiKit.DOM,{getStyle:function(b,c){b=MochiKit.DOM.getElement(b);var f=b.style[MochiKit.Base.camelize(c)];if(!f){if(document.defaultView&&document.defaultView.getComputedStyle){var a=document.defaultView.getComputedStyle(b,null);f=a?a.getPropertyValue(c):null}else{if(b.currentStyle){f=b.currentStyle[MochiKit.Base.camelize(c)]}}}if(MochiKit.Base.isOpera()&&(MochiKit.Base.find(["left","top","right","bottom"],c))){if(MochiKit.DOM.getStyle(b,"position")=="static"){f="auto"}}return f=="auto"?null:f},setStyle:function(b,c){b=MochiKit.DOM.getElement(b);for(var a in c){b.style[MochiKit.Base.camelize(a)]=c[a]}},getOpacity:function(b){var a;if(a=MochiKit.DOM.getStyle(b,"opacity")){return parseFloat(a)}if(a=(MochiKit.DOM.getStyle(b,"filter")||"").match(/alpha\(opacity=(.*)\)/)){if(a[1]){return parseFloat(a[1])/100}}return 1},getInlineOpacity:function(a){return MochiKit.DOM.getElement(a).style.opacity||""},setOpacity:function(a,c){a=MochiKit.DOM.getElement(a);if(c==1){var b=a.style.cssText;if(MochiKit.Base.isIE()){b=b.replace(/filter: ?alpha\([^\)]*\);?/gi,"")}a.style.cssText=b.replace(/opacity: ?\d\.?\d*/gi,"")}else{if(c<0.00001){c=0}MochiKit.DOM.setStyle(a,{opacity:c});if(MochiKit.Base.isIE()){MochiKit.DOM.setStyle(a,{filter:MochiKit.DOM.getStyle(a,"filter").replace(/alpha\([^\)]*\)/gi,"")+"alpha(opacity="+c*100+")"})}}},isVisible:function(a){return MochiKit.DOM.getElement(a).style.display!="none"},makeClipping:function(a){a=MochiKit.DOM.getElement(a);if(a._overflow){return}a._overflow=a.style.overflow;if((MochiKit.DOM.getStyle(a,"overflow")||"visible")!="hidden"){a.style.overflow="hidden"}},undoClipping:function(a){a=MochiKit.DOM.getElement(a);if(!a._overflow){return}a.style.overflow=a._overflow;a._overflow=undefined},makePositioned:function(a){a=MochiKit.DOM.getElement(a);var b=MochiKit.DOM.getStyle(a,"position");if((b=="static"||!b)&&!a._madePositioned){a._madePositioned=true;a.style.position="relative";if(MochiKit.Base.isOpera()){a.style.top=0;a.style.left=0}}},undoPositioned:function(a){a=MochiKit.DOM.getElement(a);if(a._madePositioned){a._madePositioned=undefined;a.style.position=a.style.top=a.style.left=a.style.bottom=a.style.right=""}},getFirstElementByTagAndClassName:function(f,k,l){var m=MochiKit.DOM;if(typeof(f)=="undefined"||f===null){f="*"}if(typeof(l)=="undefined"||l===null){l=m._document}l=m.getElement(l);var c=(l.getElementsByTagName(f)||m._document.all);if(typeof(k)=="undefined"||k===null){return MochiKit.Base.extend(null,c)}for(var h=0;h<c.length;h++){var b=c[h];var a=b.className.split(" ");for(var g=0;g<a.length;g++){if(a[g]==k){return b}}}},isParent:function(b,a){if(!b.parentNode||b==a){return false}if(b.parentNode==a){return true}return MochiKit.DOM.isParent(b.parentNode,a)}});MochiKit.Position={includeScrollOffsets:false,prepare:function(){var b=window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0;var a=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;this.windowOffset=new MochiKit.Style.Coordinates(b,a)},cumulativeOffset:function(b){var a=0;var c=0;do{a+=b.offsetTop||0;c+=b.offsetLeft||0;b=b.offsetParent}while(b);return new MochiKit.Style.Coordinates(c,a)},realOffset:function(b){var a=0;var c=0;do{a+=b.scrollTop||0;c+=b.scrollLeft||0;b=b.parentNode}while(b);return new MochiKit.Style.Coordinates(c,a)},within:function(b,a,c){if(this.includeScrollOffsets){return this.withinIncludingScrolloffsets(b,a,c)}this.xcomp=a;this.ycomp=c;this.offset=this.cumulativeOffset(b);if(b.style.position=="fixed"){this.offset.x+=this.windowOffset.x;this.offset.y+=this.windowOffset.y}return(c>=this.offset.y&&c<this.offset.y+b.offsetHeight&&a>=this.offset.x&&a<this.offset.x+b.offsetWidth)},withinIncludingScrolloffsets:function(b,a,f){var c=this.realOffset(b);this.xcomp=a+c.x-this.windowOffset.x;this.ycomp=f+c.y-this.windowOffset.y;this.offset=this.cumulativeOffset(b);return(this.ycomp>=this.offset.y&&this.ycomp<this.offset.y+b.offsetHeight&&this.xcomp>=this.offset.x&&this.xcomp<this.offset.x+b.offsetWidth)},overlap:function(b,a){if(!b){return 0}if(b=="vertical"){return((this.offset.y+a.offsetHeight)-this.ycomp)/a.offsetHeight}if(b=="horizontal"){return((this.offset.x+a.offsetWidth)-this.xcomp)/a.offsetWidth}},absolutize:function(c){c=MochiKit.DOM.getElement(c);if(c.style.position=="absolute"){return}MochiKit.Position.prepare();var g=MochiKit.Position.positionedOffset(c);var f=c.clientWidth;var a=c.clientHeight;var b={position:c.style.position,left:g.x-parseFloat(c.style.left||0),top:g.y-parseFloat(c.style.top||0),width:c.style.width,height:c.style.height};c.style.position="absolute";c.style.top=g.y+"px";c.style.left=g.x+"px";c.style.width=f+"px";c.style.height=a+"px";return b},positionedOffset:function(b){var a=0,c=0;do{a+=b.offsetTop||0;c+=b.offsetLeft||0;b=b.offsetParent;if(b){p=MochiKit.DOM.getStyle(b,"position");if(p=="relative"||p=="absolute"){break}}}while(b);return new MochiKit.Style.Coordinates(c,a)},relativize:function(b,a){b=MochiKit.DOM.getElement(b);if(b.style.position=="relative"){return}MochiKit.Position.prepare();var f=parseFloat(b.style.top||0)-(a.top||0);var c=parseFloat(b.style.left||0)-(a.left||0);b.style.position=a.position;b.style.top=f+"px";b.style.left=c+"px";b.style.width=a.width;b.style.height=a.height},clone:function(b,c){b=MochiKit.DOM.getElement(b);c=MochiKit.DOM.getElement(c);c.style.position="absolute";var a=this.cumulativeOffset(b);c.style.top=a.y+"px";c.style.left=a.x+"px";c.style.width=b.offsetWidth+"px";c.style.height=b.offsetHeight+"px"},page:function(f){var a=0;var c=0;var b=f;do{a+=b.offsetTop||0;c+=b.offsetLeft||0;if(b.offsetParent==document.body&&MochiKit.DOM.getStyle(b,"position")=="absolute"){break}}while(b=b.offsetParent);b=f;do{a-=b.scrollTop||0;c-=b.scrollLeft||0}while(b=b.parentNode);return new MochiKit.Style.Coordinates(c,a)}};if(typeof(dojo)!="undefined"){dojo.provide("MochiKit.Color");dojo.require("MochiKit.Base");dojo.require("MochiKit.DOM");dojo.require("MochiKit.Style")}if(typeof(JSAN)!="undefined"){JSAN.use("MochiKit.Base",[]);JSAN.use("MochiKit.DOM",[]);JSAN.use("MochiKit.Style",[])}try{if(typeof(MochiKit.Base)=="undefined"){throw""}}catch(e){throw"MochiKit.Color depends on MochiKit.Base"}try{if(typeof(MochiKit.Base)=="undefined"){throw""}}catch(e){throw"MochiKit.Color depends on MochiKit.DOM"}try{if(typeof(MochiKit.Base)=="undefined"){throw""}}catch(e){throw"MochiKit.Color depends on MochiKit.Style"}if(typeof(MochiKit.Color)=="undefined"){MochiKit.Color={}}MochiKit.Color.NAME="MochiKit.Color";MochiKit.Color.VERSION="1.4";MochiKit.Color.__repr__=function(){return"["+this.NAME+" "+this.VERSION+"]"};MochiKit.Color.toString=function(){return this.__repr__()};MochiKit.Color.Color=function(f,b,a,c){if(typeof(c)=="undefined"||c===null){c=1}this.rgb={r:f,g:b,b:a,a:c}};MochiKit.Color.Color.prototype={__class__:MochiKit.Color.Color,toString:function(){return this.toRGBString()},repr:function(){var b=this.rgb;var a=[b.r,b.g,b.b,b.a];return this.__class__.NAME+"("+a.join(", ")+")"}};MochiKit.Base.update(MochiKit.Color.Color,{fromComputedStyle:function(g,f){var h=MochiKit.DOM;var a=MochiKit.Color.Color;for(g=h.getElement(g);g;g=g.parentNode){var c=MochiKit.Style.computedStyle.apply(h,arguments);if(!c){continue}var b=a.fromString(c);if(!b){break}if(b.asRGB().a>0){return b}}return null},fromBackground:function(b){var a=MochiKit.Color.Color;return a.fromComputedStyle(b,"backgroundColor","background-color")||a.whiteColor()},fromText:function(b){var a=MochiKit.Color.Color;return a.fromComputedStyle(b,"color","color")||a.blackColor()},namedColors:function(){return MochiKit.Base.clone(MochiKit.Color.Color._namedColors)}});MochiKit.Base.update(MochiKit.Color,{__new__:function(){var a=MochiKit.Base;a.nameFunctions(this);this.EXPORT_TAGS={":common":this.EXPORT,":all":a.concat(this.EXPORT,this.EXPORT_OK)}}});MochiKit.Color.EXPORT=["Color"];MochiKit.Color.EXPORT_OK=["clampColorComponent","rgbToHSL","hslToRGB","rgbToHSV","hsvToRGB","toColorPart"];MochiKit.Color.__new__();MochiKit.Base._exportSymbols(this,MochiKit.Color);if(typeof(dojo)!="undefined"){dojo.provide("MochiKit.Iter");dojo.require("MochiKit.Base")}if(typeof(JSAN)!="undefined"){JSAN.use("MochiKit.Base",[])}try{if(typeof(MochiKit.Base)=="undefined"){throw""}}catch(e){throw"MochiKit.Iter depends on MochiKit.Base!"}if(typeof(MochiKit.Iter)=="undefined"){MochiKit.Iter={}}MochiKit.Iter.NAME="MochiKit.Iter";MochiKit.Iter.VERSION="1.4";MochiKit.Base.update(MochiKit.Iter,{__repr__:function(){return"["+this.NAME+" "+this.VERSION+"]"},toString:function(){return this.__repr__()},registerIteratorFactory:function(b,a,f,c){MochiKit.Iter.iteratorRegistry.register(b,a,f,c)},iter:function(f,c){var b=MochiKit.Iter;if(arguments.length==2){return b.takewhile(function(h){return h!=c},f)}if(typeof(f.next)=="function"){return f}else{if(typeof(f.iter)=="function"){return f.iter()}}try{return b.iteratorRegistry.match(f)}catch(g){var a=MochiKit.Base;if(g==a.NotFound){g=new TypeError(typeof(f)+": "+a.repr(f)+" is not iterable")}throw g}},repeat:function(b,c){var a=MochiKit.Base;if(typeof(c)=="undefined"){return{repr:function(){return"repeat("+a.repr(b)+")"},toString:a.forwardCall("repr"),next:function(){return b}}}return{repr:function(){return"repeat("+a.repr(b)+", "+c+")"},toString:a.forwardCall("repr"),next:function(){if(c<=0){throw MochiKit.Iter.StopIteration}c-=1;return b}}},next:function(a){return a.next()},ifilter:function(c,b){var a=MochiKit.Base;b=MochiKit.Iter.iter(b);if(c===null){c=a.operator.truth}return{repr:function(){return"ifilter(...)"},toString:a.forwardCall("repr"),next:function(){while(true){var f=b.next();if(c(f)){return f}}return undefined}}},ifilterfalse:function(c,b){var a=MochiKit.Base;b=MochiKit.Iter.iter(b);if(c===null){c=a.operator.truth}return{repr:function(){return"ifilterfalse(...)"},toString:a.forwardCall("repr"),next:function(){while(true){var f=b.next();if(!c(f)){return f}}return undefined}}},imap:function(c,k,h){var a=MochiKit.Base;var f=MochiKit.Iter;var b=a.map(f.iter,a.extend(null,arguments,1));var j=a.map;var g=f.next;return{repr:function(){return"imap(...)"},toString:a.forwardCall("repr"),next:function(){return c.apply(this,j(g,b))}}},list:function(c){var a=MochiKit.Base;if(typeof(c.slice)=="function"){return c.slice()}else{if(a.isArrayLike(c)){return a.concat(c)}}var b=MochiKit.Iter;c=b.iter(c);var g=[];try{while(true){g.push(c.next())}}catch(f){if(f!=b.StopIteration){throw f}return g}return undefined},forEach:function(g,f,b){var a=MochiKit.Base;if(arguments.length>2){f=a.bind(f,b)}if(a.isArrayLike(g)){try{for(var c=0;c<g.length;c++){f(g[c])}}catch(h){if(h!=MochiKit.Iter.StopIteration){throw h}}}else{b=MochiKit.Iter;b.exhaust(b.imap(f,g))}},every:function(c,b){var a=MochiKit.Iter;try{a.ifilterfalse(b,c).next();return false}catch(f){if(f!=a.StopIteration){throw f}return true}},arrayLikeIter:function(b){var a=0;return{repr:function(){return"arrayLikeIter(...)"},toString:MochiKit.Base.forwardCall("repr"),next:function(){if(a>=b.length){throw MochiKit.Iter.StopIteration}return b[a++]}}},hasIterateNext:function(a){return(a&&typeof(a.iterateNext)=="function")},iterateNextIter:function(a){return{repr:function(){return"iterateNextIter(...)"},toString:MochiKit.Base.forwardCall("repr"),next:function(){var b=a.iterateNext();if(b===null||b===undefined){throw MochiKit.Iter.StopIteration}return b}}}});MochiKit.Iter.EXPORT_OK=["iteratorRegistry","arrayLikeIter","hasIterateNext","iterateNextIter",];MochiKit.Iter.EXPORT=["StopIteration","registerIteratorFactory","iter","count","cycle","repeat","next","izip","ifilter","ifilterfalse","islice","imap","applymap","chain","takewhile","dropwhile","tee","list","reduce","range","sum","exhaust","forEach","every","sorted","reversed","some","iextend","groupby","groupby_as_array"];MochiKit.Iter.__new__=function(){var a=MochiKit.Base;if(typeof(StopIteration)!="undefined"){this.StopIteration=StopIteration}else{this.StopIteration=new a.NamedError("StopIteration")}this.iteratorRegistry=new a.AdapterRegistry();this.registerIteratorFactory("arrayLike",a.isArrayLike,this.arrayLikeIter);this.registerIteratorFactory("iterateNext",this.hasIterateNext,this.iterateNextIter);this.EXPORT_TAGS={":common":this.EXPORT,":all":a.concat(this.EXPORT,this.EXPORT_OK)};a.nameFunctions(this)};MochiKit.Iter.__new__();if(MochiKit.__export__){reduce=MochiKit.Iter.reduce}MochiKit.Base._exportSymbols(this,MochiKit.Iter);if(typeof(dojo)!="undefined"){dojo.provide("MochiKit.Visual");dojo.require("MochiKit.Base");dojo.require("MochiKit.DOM");dojo.require("MochiKit.Style");dojo.require("MochiKit.Color");dojo.require("MochiKit.Iter")}if(typeof(JSAN)!="undefined"){JSAN.use("MochiKit.Base",[]);JSAN.use("MochiKit.DOM",[]);JSAN.use("MochiKit.Style",[]);JSAN.use("MochiKit.Color",[]);JSAN.use("MochiKit.Iter",[])}try{if(typeof(MochiKit.Base)==="undefined"||typeof(MochiKit.DOM)==="undefined"||typeof(MochiKit.Style)==="undefined"||typeof(MochiKit.Color)==="undefined"||typeof(MochiKit.Iter)==="undefined"){throw""}}catch(e){throw"MochiKit.Visual depends on MochiKit.Base, MochiKit.DOM, MochiKit.Style, MochiKit.Color and MochiKit.Iter!"}if(typeof(MochiKit.Visual)=="undefined"){MochiKit.Visual={}}MochiKit.Visual.NAME="MochiKit.Visual";MochiKit.Visual.VERSION="1.4";MochiKit.Visual.__repr__=function(){return"["+this.NAME+" "+this.VERSION+"]"};MochiKit.Visual.toString=function(){return this.__repr__()};MochiKit.Visual.forceRerendering=function(a){try{a=MochiKit.DOM.getElement(a);var c=document.createTextNode(" ");a.appendChild(c);a.removeChild(c)}catch(b){}};MochiKit.Visual.PAIRS={slide:["slideDown","slideUp"],blind:["blindDown","blindUp"],appear:["appear","fade"],size:["grow","shrink"]};MochiKit.Visual.Transitions={};MochiKit.Visual.Transitions.linear=function(a){return a};MochiKit.Visual.Transitions.sinoidal=function(a){return(-Math.cos(a*Math.PI)/2)+0.5};MochiKit.Visual.Transitions.reverse=function(a){return 1-a};MochiKit.Visual.Transitions.flicker=function(a){return((-Math.cos(a*Math.PI)/4)+0.75)+Math.random()/4};MochiKit.Visual.Transitions.wobble=function(a){return(-Math.cos(a*Math.PI*(9*a))/2)+0.5};MochiKit.Visual.Transitions.pulse=function(a){return(Math.floor(a*10)%2==0?(a*10-Math.floor(a*10)):1-(a*10-Math.floor(a*10)))};MochiKit.Visual.Transitions.none=function(a){return 0};MochiKit.Visual.Transitions.full=function(a){return 1};MochiKit.Visual.ScopedQueue=function(){this.__init__()};MochiKit.Base.update(MochiKit.Visual.ScopedQueue.prototype,{__init__:function(){this.effects=[];this.interval=null},add:function(f){var g=new Date().getTime();var a=(typeof(f.options.queue)=="string")?f.options.queue:f.options.queue.position;var b=MochiKit.Iter.forEach;switch(a){case"front":b(this.effects,function(h){if(h.state=="idle"){h.startOn+=f.finishOn;h.finishOn+=f.finishOn}});break;case"end":var c;b(this.effects,function(j){var h=j.finishOn;if(h>=(c||h)){c=h}});g=c||g;break}f.startOn+=g;f.finishOn+=g;if(!f.options.queue.limit||this.effects.length<f.options.queue.limit){this.effects.push(f)}if(!this.interval){this.interval=setInterval(MochiKit.Base.bind(this.loop,this),40)}},remove:function(a){this.effects=MochiKit.Base.filter(function(b){return b!=a},this.effects);if(this.effects.length==0){clearInterval(this.interval);this.interval=null}},loop:function(){var a=new Date().getTime();MochiKit.Iter.forEach(this.effects,function(b){b.loop(a)})}});MochiKit.Visual.Queues={instances:{},get:function(a){if(typeof(a)!="string"){return a}if(!this.instances[a]){this.instances[a]=new MochiKit.Visual.ScopedQueue()}return this.instances[a]}};MochiKit.Visual.Queue=MochiKit.Visual.Queues.get("global");MochiKit.Visual.DefaultOptions={transition:MochiKit.Visual.Transitions.sinoidal,duration:1,fps:25,sync:false,from:0,to:1,delay:0,queue:"parallel"};MochiKit.Visual.Base=function(){};MochiKit.Visual.Base.prototype={__class__:MochiKit.Visual.Base,start:function(b){var a=MochiKit.Visual;this.options=MochiKit.Base.setdefault(b||{},a.DefaultOptions);this.currentFrame=0;this.state="idle";this.startOn=this.options.delay*1000;this.finishOn=this.startOn+(this.options.duration*1000);this.event("beforeStart");if(!this.options.sync){a.Queues.get(typeof(this.options.queue)=="string"?"global":this.options.queue.scope).add(this)}},loop:function(c){if(c>=this.startOn){if(c>=this.finishOn){this.render(1);this.cancel();this.event("beforeFinish");this.finish();this.event("afterFinish");return}var b=(c-this.startOn)/(this.finishOn-this.startOn);var a=Math.round(b*this.options.fps*this.options.duration);if(a>this.currentFrame){this.render(b);this.currentFrame=a}}},render:function(a){if(this.state=="idle"){this.state="running";this.event("beforeSetup");this.setup();this.event("afterSetup")}if(this.state=="running"){if(this.options.transition){a=this.options.transition(a)}a*=(this.options.to-this.options.from);a+=this.options.from;this.event("beforeUpdate");this.update(a);this.event("afterUpdate")}},cancel:function(){if(!this.options.sync){MochiKit.Visual.Queues.get(typeof(this.options.queue)=="string"?"global":this.options.queue.scope).remove(this)}this.state="finished"},setup:function(){},finish:function(){},update:function(a){},event:function(a){if(this.options[a+"Internal"]){this.options[a+"Internal"](this)}if(this.options[a]){this.options[a](this)}},repr:function(){return"["+this.__class__.NAME+", options:"+MochiKit.Base.repr(this.options)+"]"}};MochiKit.Visual.Opacity=function(b,a){this.__init__(b,a)};MochiKit.Visual.Opacity.prototype=new MochiKit.Visual.Base();MochiKit.Base.update(MochiKit.Visual.Opacity.prototype,{__init__:function(f,c){var a=MochiKit.Base;var g=MochiKit.DOM;this.element=g.getElement(f);if(a.isIE()&&(!this.element.currentStyle.hasLayout)){g.setStyle(this.element,{zoom:1})}c=a.update({from:g.getOpacity(this.element)||0,to:1},c||{});this.start(c)},update:function(a){MochiKit.DOM.setOpacity(this.element,a)}});MochiKit.Visual.Move=function(b,a){this.__init__(b,a)};MochiKit.Visual.Move.prototype=new MochiKit.Visual.Base();MochiKit.Base.update(MochiKit.Visual.Move.prototype,{__init__:function(b,a){this.element=MochiKit.DOM.getElement(b);a=MochiKit.Base.update({x:0,y:0,mode:"relative"},a||{});this.start(a)},setup:function(){var f=MochiKit.DOM;f.makePositioned(this.element);var b=this.element.style;var c=b.visibility;var a=b.display;if(a=="none"){b.visibility="hidden";b.display=""}this.originalLeft=parseFloat(f.getStyle(this.element,"left")||"0");this.originalTop=parseFloat(f.getStyle(this.element,"top")||"0");if(this.options.mode=="absolute"){this.options.x-=this.originalLeft;this.options.y-=this.originalTop}if(a=="none"){b.visibility=c;b.display=a}},update:function(a){MochiKit.DOM.setStyle(this.element,{left:Math.round(this.options.x*a+this.originalLeft)+"px",top:Math.round(this.options.y*a+this.originalTop)+"px"})}});MochiKit.Visual.Scale=function(b,c,a){this.__init__(b,c,a)};MochiKit.Visual.Scale.prototype=new MochiKit.Visual.Base();MochiKit.Base.update(MochiKit.Visual.Scale.prototype,{__init__:function(b,c,a){this.element=MochiKit.DOM.getElement(b);a=MochiKit.Base.update({scaleX:true,scaleY:true,scaleContent:true,scaleFromCenter:false,scaleMode:"box",scaleFrom:100,scaleTo:c},a||{});this.start(a)},setup:function(){this.restoreAfterFinish=this.options.restoreAfterFinish||false;this.elementPositioning=MochiKit.DOM.getStyle(this.element,"position");var c=MochiKit.Iter.forEach;var a=MochiKit.Base.bind;this.originalStyle={};c(["top","left","width","height","fontSize"],a(function(b){this.originalStyle[b]=this.element.style[b]},this));this.originalTop=this.element.offsetTop;this.originalLeft=this.element.offsetLeft;var f=MochiKit.DOM.getStyle(this.element,"font-size")||"100%";c(["em","px","%"],a(function(b){if(f.indexOf(b)>0){this.fontSize=parseFloat(f);this.fontSizeType=b}},this));this.factor=(this.options.scaleTo-this.options.scaleFrom)/100;if(/^content/.test(this.options.scaleMode)){this.dims=[this.element.scrollHeight,this.element.scrollWidth]}else{if(this.options.scaleMode=="box"){this.dims=[this.element.offsetHeight,this.element.offsetWidth]}else{this.dims=[this.options.scaleMode.originalHeight,this.options.scaleMode.originalWidth]}}},update:function(a){var b=(this.options.scaleFrom/100)+(this.factor*a);if(this.options.scaleContent&&this.fontSize){MochiKit.DOM.setStyle(this.element,{fontSize:this.fontSize*b+this.fontSizeType})}this.setDimensions(this.dims[0]*b,this.dims[1]*b)},finish:function(){if(this.restoreAfterFinish){MochiKit.DOM.setStyle(this.element,this.originalStyle)}},setDimensions:function(a,f){var g={};if(this.options.scaleX){g.width=Math.round(f)+"px"}if(this.options.scaleY){g.height=Math.round(a)+"px"}if(this.options.scaleFromCenter){var c=(a-this.dims[0])/2;var b=(f-this.dims[1])/2;if(this.elementPositioning=="absolute"){if(this.options.scaleY){g.top=this.originalTop-c+"px"}if(this.options.scaleX){g.left=this.originalLeft-b+"px"}}else{if(this.options.scaleY){g.top=-c+"px"}if(this.options.scaleX){g.left=-b+"px"}}}MochiKit.DOM.setStyle(this.element,g)}});MochiKit.Visual.fade=function(c,b){var f=MochiKit.DOM;var a=f.getInlineOpacity(c);b=MochiKit.Base.update({from:f.getOpacity(c)||1,to:0,afterFinishInternal:function(g){if(g.options.to!==0){return}MochiKit.Style.hideElement(g.element);f.setStyle(g.element,{opacity:a})}},b||{});return new MochiKit.Visual.Opacity(c,b)};MochiKit.Visual.appear=function(c,b){var f=MochiKit.DOM;var a=MochiKit.Visual;b=MochiKit.Base.update({from:(f.getStyle(c,"display")=="none"?0:f.getOpacity(c)||0),to:1,afterFinishInternal:function(g){a.forceRerendering(g.element)},beforeSetupInternal:function(g){f.setOpacity(g.element,g.options.from);MochiKit.Style.showElement(g.element)}},b||{});return new a.Opacity(c,b)};MochiKit.Visual.blindUp=function(b,a){var c=MochiKit.DOM;b=c.getElement(b);c.makeClipping(b);a=MochiKit.Base.update({scaleContent:false,scaleX:false,restoreAfterFinish:true,afterFinishInternal:function(f){MochiKit.Style.hideElement(f.element);c.undoClipping(f.element)}},a||{});return new MochiKit.Visual.Scale(b,0,a)};MochiKit.Visual.blindDown=function(c,b){var f=MochiKit.DOM;c=f.getElement(c);var a=MochiKit.Style.getElementDimensions(c);b=MochiKit.Base.update({scaleContent:false,scaleX:false,scaleFrom:0,scaleMode:{originalHeight:a.h,originalWidth:a.w},restoreAfterFinish:true,afterSetupInternal:function(g){f.makeClipping(g.element);f.setStyle(g.element,{height:"0px"});MochiKit.Style.showElement(g.element)},afterFinishInternal:function(g){f.undoClipping(g.element)}},b||{});return new MochiKit.Visual.Scale(c,100,b)};MochiKit.Visual.Color=MochiKit.Color.Color;MochiKit.Visual.getElementsComputedStyle=MochiKit.DOM.computedStyle;MochiKit.Visual.__new__=function(){var a=MochiKit.Base;a.nameFunctions(this);this.EXPORT_TAGS={":common":this.EXPORT,":all":a.concat(this.EXPORT,this.EXPORT_OK)}};MochiKit.Visual.EXPORT=["roundElement","roundClass","tagifyText","multiple","toggle","Base","Parallel","Opacity","Move","Scale","Highlight","ScrollTo","fade","appear","puff","blindUp","blindDown","switchOff","dropOut","shake","slideDown","slideUp","squish","grow","shrink","pulsate","fold"];MochiKit.Visual.EXPORT_OK=["PAIRS"];MochiKit.Visual.__new__();MochiKit.Base._exportSymbols(this,MochiKit.Visual);/**
 * Copyright 2009 Tim Down.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


var log4javascript_stub=(function(){var log4javascript;function ff(){return function(){};}
function copy(obj,props){for(var i in props){obj[i]=props[i];}}
var f=ff();var Logger=ff();copy(Logger.prototype,{addChild:f,getEffectiveAppenders:f,invalidateAppenderCache:f,getAdditivity:f,setAdditivity:f,addAppender:f,removeAppender:f,removeAllAppenders:f,log:f,setLevel:f,getLevel:f,getEffectiveLevel:f,trace:f,debug:f,info:f,warn:f,error:f,fatal:f,isEnabledFor:f,isTraceEnabled:f,isDebugEnabled:f,isInfoEnabled:f,isWarnEnabled:f,isErrorEnabled:f,isFatalEnabled:f,callAppenders:f,group:f,groupEnd:f,time:f,timeEnd:f,assert:f,parent:new Logger()});var getLogger=function(){return new Logger();};function EventSupport(){}
copy(EventSupport.prototype,{setEventTypes:f,addEventListener:f,removeEventListener:f,dispatchEvent:f,eventTypes:[],eventListeners:{}});function Log4JavaScript(){}
Log4JavaScript.prototype=new EventSupport();log4javascript=new Log4JavaScript();log4javascript={isStub:true,version:"1.4.2",edition:"log4javascript",setDocumentReady:f,setEventTypes:f,addEventListener:f,removeEventListener:f,dispatchEvent:f,eventTypes:[],eventListeners:{},logLog:{setQuietMode:f,setAlertAllErrors:f,debug:f,displayDebug:f,warn:f,error:f},handleError:f,setEnabled:f,isEnabled:f,setTimeStampsInMilliseconds:f,isTimeStampsInMilliseconds:f,evalInScope:f,setShowStackTraces:f,getLogger:getLogger,getDefaultLogger:getLogger,getNullLogger:getLogger,getRootLogger:getLogger,resetConfiguration:f,Level:ff(),LoggingEvent:ff(),Layout:ff(),Appender:ff()};log4javascript.LoggingEvent.prototype={getThrowableStrRep:f,getCombinedMessages:f};log4javascript.Level.prototype={toString:f,equals:f,isGreaterOrEqual:f};var level=new log4javascript.Level();copy(log4javascript.Level,{ALL:level,TRACE:level,DEBUG:level,INFO:level,WARN:level,ERROR:level,FATAL:level,OFF:level});log4javascript.Layout.prototype={defaults:{},format:f,ignoresThrowable:f,getContentType:f,allowBatching:f,getDataValues:f,setKeys:f,setCustomField:f,hasCustomFields:f,setTimeStampsInMilliseconds:f,isTimeStampsInMilliseconds:f,getTimeStampValue:f,toString:f};log4javascript.SimpleDateFormat=ff();log4javascript.SimpleDateFormat.prototype={setMinimalDaysInFirstWeek:f,getMinimalDaysInFirstWeek:f,format:f};log4javascript.PatternLayout=ff();log4javascript.PatternLayout.prototype=new log4javascript.Layout();log4javascript.Appender=ff();log4javascript.Appender.prototype=new EventSupport();copy(log4javascript.Appender.prototype,{layout:new log4javascript.PatternLayout(),threshold:log4javascript.Level.ALL,loggers:[],doAppend:f,append:f,setLayout:f,getLayout:f,setThreshold:f,getThreshold:f,setAddedToLogger:f,setRemovedFromLogger:f,group:f,groupEnd:f,toString:f});log4javascript.SimpleLayout=ff();log4javascript.SimpleLayout.prototype=new log4javascript.Layout();log4javascript.NullLayout=ff();log4javascript.NullLayout.prototype=new log4javascript.Layout();log4javascript.XmlLayout=ff();log4javascript.XmlLayout.prototype=new log4javascript.Layout();copy(log4javascript.XmlLayout.prototype,{escapeCdata:f,isCombinedMessages:f});log4javascript.JsonLayout=ff();log4javascript.JsonLayout.prototype=new log4javascript.Layout();copy(log4javascript.JsonLayout.prototype,{isReadable:f,isCombinedMessages:f});log4javascript.HttpPostDataLayout=ff();log4javascript.HttpPostDataLayout.prototype=new log4javascript.Layout();log4javascript.PatternLayout=ff();log4javascript.PatternLayout.prototype=new log4javascript.Layout();log4javascript.AlertAppender=ff();log4javascript.AlertAppender.prototype=new log4javascript.Appender();log4javascript.BrowserConsoleAppender=ff();log4javascript.BrowserConsoleAppender.prototype=new log4javascript.Appender();log4javascript.AjaxAppender=ff();log4javascript.AjaxAppender.prototype=new log4javascript.Appender();copy(log4javascript.AjaxAppender.prototype,{getSessionId:f,setSessionId:f,isTimed:f,setTimed:f,getTimerInterval:f,setTimerInterval:f,isWaitForResponse:f,setWaitForResponse:f,getBatchSize:f,setBatchSize:f,isSendAllOnUnload:f,setSendAllOnUnload:f,setRequestSuccessCallback:f,setFailCallback:f,getPostVarName:f,setPostVarName:f,sendAll:f,defaults:{requestSuccessCallback:null,failCallback:null}});function ConsoleAppender(){}
ConsoleAppender.prototype=new log4javascript.Appender();copy(ConsoleAppender.prototype,{create:f,isNewestMessageAtTop:f,setNewestMessageAtTop:f,isScrollToLatestMessage:f,setScrollToLatestMessage:f,getWidth:f,setWidth:f,getHeight:f,setHeight:f,getMaxMessages:f,setMaxMessages:f,isShowCommandLine:f,setShowCommandLine:f,isShowHideButton:f,setShowHideButton:f,isShowCloseButton:f,setShowCloseButton:f,getCommandLineObjectExpansionDepth:f,setCommandLineObjectExpansionDepth:f,isInitiallyMinimized:f,setInitiallyMinimized:f,isUseDocumentWrite:f,setUseDocumentWrite:f,group:f,groupEnd:f,clear:f,focus:f,focusCommandLine:f,focusSearch:f,getCommandWindow:f,setCommandWindow:f,executeLastCommand:f,getCommandLayout:f,setCommandLayout:f,evalCommandAndAppend:f,addCommandLineFunction:f,storeCommandHistory:f,unload:f});ConsoleAppender.addGlobalCommandLineFunction=f;log4javascript.InPageAppender=ff();log4javascript.InPageAppender.prototype=new ConsoleAppender();copy(log4javascript.InPageAppender.prototype,{addCssProperty:f,hide:f,show:f,isVisible:f,close:f,defaults:{layout:new log4javascript.PatternLayout(),maxMessages:null}});log4javascript.InlineAppender=log4javascript.InPageAppender;log4javascript.PopUpAppender=ff();log4javascript.PopUpAppender.prototype=new ConsoleAppender();copy(log4javascript.PopUpAppender.prototype,{isUseOldPopUp:f,setUseOldPopUp:f,isComplainAboutPopUpBlocking:f,setComplainAboutPopUpBlocking:f,isFocusPopUp:f,setFocusPopUp:f,isReopenWhenClosed:f,setReopenWhenClosed:f,close:f,hide:f,show:f,defaults:{layout:new log4javascript.PatternLayout(),maxMessages:null}});return log4javascript;})();if(typeof window.log4javascript=="undefined"){var log4javascript=log4javascript_stub;}if(typeof bobj=="undefined"){bobj={}}if(typeof bobj.external=="undefined"){bobj.external={}}if(typeof bobj.external.date=="undefined"){bobj.external.date={}}bobj.external.date.MONTH_NAMES=new Array("January","February","March","April","May","June","July","August","September","October","November","December","Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec");bobj.external.date.DAY_NAMES=new Array("Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sun","Mon","Tue","Wed","Thu","Fri","Sat");bobj.external.date.LZ=function(a){return(a<0||a>9?"":"0")+a};bobj.external.date.isDate=function(c,b){var a=bobj.external.date.getDateFromFormat(c,b);if(!a){return false}return true};bobj.external.date.compareDates=function(f,g,c,e){var b=bobj.external.date.getDateFromFormat(f,g);var a=bobj.external.date.getDateFromFormat(c,e);if(!b||!a){return -1}else{if(b.getTime()>a.getTime()){return 1}}return 0};bobj.external.date.formatDate=function(O,J){J=J+"";var p="";var A=0;var N="";var f="";var n=O.getFullYear()+"";var g=O.getMonth()+1;var L=O.getDate();var r=O.getDay();var q=O.getHours();var C=O.getMinutes();var u=O.getSeconds();var x,z,b,v,P,e,I,G,D,t,R,q,Q,l,a,F;var B=new Object();while(n.length<4){n="0"+n}B.y=""+n;B.yyyy=n;B.yy=n.substring(2,4);B.Y=B.y;B.YY=B.yy;B.YYYY=B.yyyy;B.M=g;B.MM=bobj.external.date.LZ(g);B.MMM=bobj.external.date.MONTH_NAMES[g-1];B.NNN=bobj.external.date.MONTH_NAMES[g+11];B.d=L;B.dd=bobj.external.date.LZ(L);B.D=B.d;B.DD=B.dd;B.E=bobj.external.date.DAY_NAMES[r+7];B.EE=bobj.external.date.DAY_NAMES[r];B.H=q;B.HH=bobj.external.date.LZ(q);if(q==0){B.h=12}else{if(q>12){B.h=q-12}else{B.h=q}}B.hh=bobj.external.date.LZ(B.h);if(q>11){B.K=q-12}else{B.K=q}B.k=q+1;B.KK=bobj.external.date.LZ(B.K);B.kk=bobj.external.date.LZ(B.k);if(q>11){B.a="PM"}else{B.a="AM"}B.m=C;B.mm=bobj.external.date.LZ(C);B.s=u;B.ss=bobj.external.date.LZ(u);while(A<J.length){N=J.charAt(A);f="";while((J.charAt(A)==N)&&(A<J.length)){f+=J.charAt(A++)}if(B[f]!=null){p=p+B[f]}else{p=p+f}}return p};bobj.external.date._isInteger=function(c){var b="1234567890";for(var a=0;a<c.length;a++){if(b.indexOf(c.charAt(a))==-1){return false}}return true};bobj.external.date._getInt=function(g,e,f,c){for(var a=c;a>=f;a--){var b=g.substring(e,e+a);if(b.length<f){return null}if(bobj.external.date._isInteger(b)){return b}}return null};bobj.external.date.getDateFromFormat=function(C,s){C=C+"";s=s+"";var B=0;var n=0;var u="";var f="";var A="";var k,g;var l=null;var z=null;var v=null;var a=null;var t=null;var q=null;var m="";while(n<s.length){u=s.charAt(n);f="";while((s.charAt(n)==u)&&(n<s.length)){f+=s.charAt(n++)}if(f=="yyyy"||f=="YYYY"||f=="yy"||f=="YY"||f=="y"||f=="Y"){if(f=="yyyy"||f=="YYYY"){k=4;g=4}if(f=="yy"||f=="YY"){k=2;g=2}if(f=="y"||f=="Y"){k=2;g=4}l=bobj.external.date._getInt(C,B,k,g);if(l==null){return null}B+=l.length;if(l.length==2){if(l>70){l=1900+(l-0)}else{l=2000+(l-0)}}}else{if(f=="MMM"||f=="NNN"){z=0;for(var r=0;r<bobj.external.date.MONTH_NAMES.length;r++){var b=bobj.external.date.MONTH_NAMES[r];if(C.substring(B,B+b.length).toLowerCase()==b.toLowerCase()){if(f=="MMM"||(f=="NNN"&&r>11)){z=r+1;if(z>12){z-=12}B+=b.length;break}}}if((z<1)||(z>12)){return null}}else{if(f=="EE"||f=="E"){for(var r=0;r<bobj.external.date.DAY_NAMES.length;r++){var p=bobj.external.date.DAY_NAMES[r];if(C.substring(B,B+p.length).toLowerCase()==p.toLowerCase()){B+=p.length;break}}}else{if(f=="MM"||f=="M"){z=bobj.external.date._getInt(C,B,f.length,2);if(z==null||(z<1)||(z>12)){return null}B+=z.length}else{if(f=="dd"||f=="DD"||f=="d"||f=="D"){v=bobj.external.date._getInt(C,B,f.length,2);if(v==null||(v<1)||(v>31)){return null}B+=v.length}else{if(f=="hh"||f=="h"){a=bobj.external.date._getInt(C,B,f.length,2);if(a==null||(a<1)||(a>12)){return null}B+=a.length}else{if(f=="HH"||f=="H"){a=bobj.external.date._getInt(C,B,f.length,2);if(a==null||(a<0)||(a>23)){return null}B+=a.length}else{if(f=="KK"||f=="K"){a=bobj.external.date._getInt(C,B,f.length,2);if(a==null||(a<0)||(a>11)){return null}B+=a.length}else{if(f=="kk"||f=="k"){a=bobj.external.date._getInt(C,B,f.length,2);if(a==null||(a<1)||(a>24)){return null}B+=a.length;a--}else{if(f=="mm"||f=="m"){t=bobj.external.date._getInt(C,B,f.length,2);if(t==null||(t<0)||(t>59)){return null}B+=t.length}else{if(f=="ss"||f=="s"){q=bobj.external.date._getInt(C,B,f.length,2);if(q==null||(q<0)||(q>59)){return null}B+=q.length}else{if(f=="a"){if(C.substring(B,B+2).toLowerCase()=="am"){m="AM"}else{if(C.substring(B,B+2).toLowerCase()=="pm"){m="PM"}else{return null}}B+=2}else{if(C.substring(B,B+f.length)!=f){return null}else{B+=f.length}}}}}}}}}}}}}}if(B!=C.length){return null}if(z==2){if(((l%4==0)&&(l%100!=0))||(l%400==0)){if(v>29){return null}}else{if(v>28){return null}}}if((z==4)||(z==6)||(z==9)||(z==11)){if(v>30){return null}}if(a!==null){if(a<12&&m=="PM"){a=a-0+12}else{if(a>11&&m=="AM"){a-=12}}}var e=new Date(l,z-1,v,a,t,q);e.setFullYear(l);return e};_userAgent=navigator.userAgent?navigator.userAgent.toLowerCase():null;_ie=(document.all!=null)?true:false;_dom=(document.getElementById!=null)?true:false;_isQuirksMode=(document.compatMode!="CSS1Compat");_dtd4=!_ie||(document.compatMode!="BackCompat");_show="visible";_hide="hidden";_hand=_ie?"hand":"pointer";_appVer=navigator.appVersion.toLowerCase();_webKit=(_userAgent.indexOf("safari")>=0)||(_userAgent.indexOf("applewebkit")>=0);_mac=(_appVer.indexOf("macintosh")>=0)||(_appVer.indexOf("macos")>=0);_opera=(_userAgent.indexOf("opera")!=-1);_userAgent=navigator.userAgent?navigator.userAgent.toLowerCase():null;_ctrl=0;_shift=1;_alt=2;var docMode=document.documentMode;var _ie6Up=(docMode>=6);var _ie8Up=(docMode>=8);var _ie10Up=(docMode>=10);var _ie11Up=(docMode>=11);_moz=_dom&&!_ie&&!_ie11Up;_saf=_moz&&(_userAgent.indexOf("safari")>=0);_small=(screen.height<=600);_curDoc=document;_curWin=self;_tooltipWin=self;_tooltipDx=0;_tooltipDy=0;_codeWinName="_CW";_leftBtn=(_ie||_saf)?1:0;_preloadArr=new Array;_widgets=new Array;_resizeW=_ie6Up?"col-resize":"E-resize";_resizeH=_ie6Up?"row-resize":"S-resize";_ddData=new Array;_dontNeedEncoding=null;_thex=null;_defaultButtonWidth=60;function initDom(e,c,f,b,a){_skin=e;_lang=f;_style=c;if(b){_curWin=b;_curDoc=b.document}_tooltipWin=_curWin;if(a){_codeWinName="_CW"+a}_curWin[_codeWinName]=self}function styleSheet(){includeCSS("style")}function isLayerDisplayed(a){var b=a?a.style:null;if(b){if(b.display=="none"||b.visibility=="hidden"){return false}else{var c=a.parentNode;if(c!=null){return isLayerDisplayed(c)}else{return true}}}else{return true}}function safeSetFocus(a){if(a&&a.focus&&isLayerDisplayed(a)){a.focus()}}function newWidget(b){var a=new Object;a.id=b;a.layer=null;a.css=null;a.getHTML=Widget_getHTML;a.beginHTML=Widget_getHTML;a.endHTML=Widget_getHTML;a.write=Widget_write;a.begin=Widget_begin;a.end=Widget_end;a.init=Widget_init;a.move=Widget_move;a.resize=Widget_resize;a.setBgColor=Widget_setBgColor;a.show=Widget_show;a.getWidth=Widget_getWidth;a.getHeight=Widget_getHeight;a.setHTML=Widget_setHTML;a.setDisabled=Widget_setDisabled;a.focus=Widget_focus;a.setDisplay=Widget_setDisplay;a.isDisplayed=Widget_isDisplayed;a.appendHTML=Widget_appendHTML;a.setTooltip=Widget_setTooltip;a.initialized=Widget_initialized;a.widx=_widgets.length;_widgets[a.widx]=a;return a}function new_Widget(a){return newWidget(a.id)}function getEvent(b,a){if(_ie&&(b==null)){b=a?a.event:_curWin.event}return b}function Widget_param(c,b,a){var e=c?c[b]:null;return e==null?a:e}function Widget_appendHTML(){append(_curDoc.body,this.getHTML())}function Widget_getHTML(){return""}function Widget_write(a){_curDoc.write(this.getHTML(a))}function Widget_begin(){_curDoc.write(this.beginHTML())}function Widget_end(){_curDoc.write(this.endHTML())}function Widget_init(){var a=this;a.layer=getLayer(a.id);a.css=a.layer.style;a.layer._widget=a.widx;if(a.initialHTML){a.setHTML(a.initialHTML)}}function Widget_move(a,e){var b=this.css;if(a!=null){if(_moz){b.left=""+a+"px"}else{b.pixelLeft=a}}if(e!=null){if(_moz){b.top=""+e+"px"}else{b.pixelTop=e}}}function Widget_focus(){safeSetFocus(this.layer)}function Widget_setBgColor(a){this.css.backgroundColor=a}function Widget_show(a){this.css.visibility=a?_show:_hide}function Widget_getWidth(){return this.layer.offsetWidth}function Widget_getHeight(){return this.layer.offsetHeight}function Widget_setHTML(a){var b=this;if(b.layer){b.layer.innerHTML=a}else{b.initialHTML=a}}function Widget_setDisplay(a){if(this.css){this.css.display=a?"":"none"}}function Widget_isDisplayed(){if(this.css.display=="none"){return false}else{return true}}function Widget_setDisabled(a){if(this.layer){this.layer.disabled=a}}function Widget_resize(a,b){if(a!=null){this.css.width=""+(Math.max(0,a))+"px"}if(b!=null){this.css.height=""+(Math.max(0,b))+"px"}}function Widget_setTooltip(a){this.layer.title=a}function Widget_initialized(){return this.layer!=null}function newGrabberWidget(a,e,k,g,l,f,c,b,m){o=newWidget(a);o.resizeCB=e;o.x=k;o.y=g;o.w=l;o.h=f;o.dx=0;o.dy=0;o.min=null;o.max=null;o.isHori=c;o.preloaded=new Image;o.preloaded.src=_skin+"../resizepattern.gif";o.buttonCB=b;o.allowGrab=true;o.collapsed=false;o.isFromButton=false;o.showGrab=GrabberWidget_showGrab;o.setCollapsed=GrabberWidget_setCollapsed;o.tooltipButton=m;o.getHTML=GrabberWidget_getHTML;o.enableGrab=GrabberWidget_enableGrab;o.setMinMax=GrabberWidget_setMinMax;if(window._allGrabbers==null){window._allGrabbers=new Array}o.index=_allGrabbers.length;_allGrabbers[o.index]=o;o.buttonLyr=null;o.setButtonImage=GrabberWidget_setButtonImage;o.getImgOffset=GrabberWidget_getImgOffset;return o}function GrabberWidget_setCollapsed(b,a){this.collapsed=b;this.setButtonImage(false,a)}function GrabberWidget_getImgOffset(a){var b=this;if(b.isHori){b.dx=(b.collapsed?12:0)+(a?6:0);b.dy=0}else{b.dy=(b.collapsed?12:0)+(a?6:0);b.dx=0}}function GrabberWidget_setButtonImage(a,b){var c=this;c.getImgOffset(a);c.tooltipButton=b;if(c.layer){if(c.buttonLyr==null){c.buttonLyr=getLayer("grabImg_"+c.id)}if(c.buttonLyr){changeSimpleOffset(c.buttonLyr,c.dx,c.dy,null,b)}}}function GrabberWidget_enableGrab(a){var b=this;b.allowGrab=a;if(b.css){b.css.cursor=b.allowGrab?(b.isHori?_resizeW:_resizeH):"default"}}function GrabberWidget_getHTML(){var b=this;var a=b.isHori?_resizeW:_resizeH;var e='onselectstart="return false" ondragstart="return false" onmousedown="'+_codeWinName+".GrabberWidget_down(event,'"+b.index+"',this);return false;\"";var c=_ie?('<img onselectstart="return false" ondragstart="return false" onmousedown="'+_codeWinName+'.eventCancelBubble(event)" border="0" hspace="0" vspace="0" src="'+_skin+'../transp.gif" id="modal_'+b.id+'" style="z-index:10000;display:none;position:absolute;top:0px;left:0px;width:1px;height:1px;cursor:'+a+'">'):('<div onselectstart="return false" ondragstart="return false" onmousedown="'+_codeWinName+'.eventCancelBubble(event)" border="0" hspace="0" vspace="0" id="modal_'+b.id+'" style="z-index:10000;display:none;position:absolute;top:0px;left:0px;width:1px;height:1px;cursor:'+a+'"></div>');return getBGIframe("grabIframe_"+b.id)+c+'<table cellpadding="0" cellspacing="0" border="0" '+e+' id="'+b.id+'" style="overflow:hidden;position:absolute;left:'+b.x+"px;top:"+b.y+"px;width:"+b.w+"px;height:"+b.h+"px;cursor:"+a+'"><tr><td></td></tr></table>'}function GrabberWidget_setMinMax(b,a){this.min=b;this.max=a}function GrabberWidget_button(e,index,lyr){var o=_allGrabbers[index];o.isFromButton=true;lyr.onmouseup=eval("_curWin."+_codeWinName+".GrabberWidget_buttonup")}function GrabberWidget_buttonover(c,b,a){var f=_allGrabbers[b];f.setButtonImage(true)}function GrabberWidget_buttonout(c,b,a){var f=_allGrabbers[b];f.setButtonImage(false)}function GrabberWidget_buttonup(a){GrabberWidget_up(a)}function GrabberWidget_showGrab(){var e=this,c=e.mod,f=e.iframe,b=e.layer.style,a=c.style;f.setDisplay(true)}function GrabberWidget_down(e,index,lyr){var o=_allGrabbers[index];window._theGrabber=o;if(o.mod==null){o.mod=getLayer("modal_"+o.id);o.iframe=newWidget("grabIframe_"+o.id);o.iframe.init()}o.mod.onmousemove=eval("_curWin."+_codeWinName+".GrabberWidget_move");o.mod.onmouseup=eval("_curWin."+_codeWinName+".GrabberWidget_up");o.grabStartPosx=parseInt(lyr.style.left);o.grabStartPosy=parseInt(lyr.style.top);o.grabStartx=eventGetX(e);o.grabStarty=eventGetY(e);var mod=o.mod,ifr=o.iframe,stl=o.layer.style,st=mod.style;stl.backgroundImage="url('"+_skin+"../resizepattern.gif')";o.prevZ=stl.zIndex;stl.zIndex=9999;ifr.css.zIndex=9998;st.width="100%";st.height="100%";mod.style.display="block";var p=getPos(o.layer);ifr.move(p.x,p.y);ifr.resize(o.getWidth(),o.getHeight());if(!o.isFromButton){o.showGrab()}return false}function GrabberWidget_move(k){var c=_theGrabber,g=c.layer,m=c.mod;if(c.isFromButton){if(c.isHori){var n=eventGetX(k),f=c.grabStartx;if((n<f-3)||(n>f+3)){c.isFromButton=false}}else{var a=eventGetY(k),b=c.grabStarty;if((l<b-3)||(l>b+3)){c.isFromButton=false}}if(!c.isFromButton){c.showGrab()}}if(!c.isFromButton){if(c.allowGrab){var n=c.isHori?Math.max(0,c.grabStartPosx-c.grabStartx+eventGetX(k)):null;var l=c.isHori?null:Math.max(0,c.grabStartPosy-c.grabStarty+eventGetY(k));if(c.isHori){if(c.min!=null){n=Math.max(n,c.min)}if(c.max!=null){n=Math.min(n,c.max)}}else{if(c.min!=null){l=Math.max(l,c.min)}if(c.max!=null){l=Math.min(l,c.max)}}eventCancelBubble(k);c.move(n,l);getPos(c.layer);if(c.buttonCB){var p=c.buttonLyr.style;if(p.display!="none"){p.display="none"}}c.iframe.move(n,l)}}}function GrabberWidget_up(g){var k=_theGrabber,b=k.layer,f=k.mod,c=b.style;c.backgroundImage="";c.zIndex=k.prevZ;var l=k.iframe;l.move(-100,-100);l.resize(1,1);l.setDisplay(false);eventCancelBubble(g);var a=f.style;a.display="none";a.width="0px";a.height="0px";if(k.buttonCB){k.buttonLyr.style.display=""}if(k&&(k.isFromButton)){if(k.buttonCB){k.buttonCB()}k.isFromButton=false}if(k.allowGrab&&(!k.isFromButton)){if(k.resizeCB){k.resizeCB(parseInt(b.style.left),parseInt(b.style.top))}}}function newButtonWidget(c,q,l,e,g,v,k,m,b,r,p,u,s,n,a,t){var f=newWidget(c);f.label=q;f.cb=l;f.width=e;f.hlp=g;f.tooltip=v;f.tabIndex=k;f.isGray=false;f.isDefault=false;f.txt=null;f.icn=null;f.margin=m?m:0;f.extraStyle="";f.isDelayCallback=true;if(b){f.url=b;f.w=r;f.h=p;f.dx=u;f.dy=s;f.disDx=(a!=null)?a:u;f.disDy=(t!=null)?t:s;f.imgRight=n?true:false}f.getHTML=ButtonWidget_getHTML;f.setDisabled=ButtonWidget_setDisabled;f.setText=ButtonWidget_setText;f.changeImg=ButtonWidget_changeImg;f.oldInit=f.init;f.init=ButtonWidget_init;f.isDisabled=ButtonWidget_isDisabled;f.setDefaultButton=ButtonWidget_setDefaultButton;f.executeCB=ButtonWidget_executeCB;f.setTooltip=ButtonWidget_setTooltip;f.setDelayCallback=ButtonWidget_setDelayCallback;f.instIndex=ButtonWidget_currInst;ButtonWidget_inst[ButtonWidget_currInst++]=f;return f}ButtonWidget_inst=new Array;ButtonWidget_currInst=0;function ButtonWidget_getHTML(){with(this){var clk=_codeWinName+".ButtonWidget_clickCB("+this.instIndex+');return false;"';var clcbs='onclick="'+clk+'" ';if(_ie){clcbs+='ondblclick="'+clk+'" '}var isDefaultSty=(this.isDefault&&!this.isGray);clcbs+='onkeydown=" return '+_codeWinName+".ButtonWidget_keydownCB(event,"+this.instIndex+');" ';var url1=_skin+"button.gif",addPar=' style="'+extraStyle+"cursor:"+_hand+";margin-left:"+margin+"px; margin-right:"+margin+'px; "'+clcbs+" ",tip=attr("title",tooltip),idText="theBttn"+id,idIcon="theBttnIcon"+id;var bg=backImgOffset(url1,0,isDefaultSty?105:42);var lnkB="<a "+attr("id",idText)+" "+tip+" "+attr("tabindex",tabIndex)+' href="javascript:void(0)" class="wizbutton" role="button">';var l=(label!=null);var im=(this.url?('<td align="'+(l?(this.imgRight?"right":"left"):"center")+'" style="'+bg+'" width="'+(!l&&(width!=null)?width+6:w+6)+'">'+(l?"":lnkB)+simpleImgOffset(url,w,h,this.isGray?disDs:dx,this.isGray?disDy:dy,idIcon,null,(l?"":tooltip),"cursor:"+_hand)+(l?"":"</a>")+"</td>"):"");return'<table onmouseover="return true" '+attr("id",id)+" "+addPar+' border="0" cellspacing="0" cellpadding="0"><tr valign="middle"><td height="21" width="5" style="'+backImgOffset(url1,0,isDefaultSty?63:0)+'"></td>'+(this.imgRight?"":im)+(l?("<td "+attr("width",width)+attr("id","theBttnCenterImg"+id)+' align="center" class="'+(this.isGray?"wizbuttongray":"wizbutton")+'" style="padding-left:3px;padding-right:3px;'+bg+'"><nobr>'+lnkB+label+"</a></nobr></td>"):"")+(this.imgRight?im:"")+'<td height="21" width="5" style="'+backImgOffset(url1,0,isDefaultSty?84:21)+'"></td></tr></table>'}}function ButtonWidget_setDelayCallback(a){this.isDelayCallback=(a==true)}function ButtonWidget_setDisabled(g){var f=this,e=g?"default":_hand;f.isGray=g;if(f.layer){var b=g?"wizbuttongray":"wizbutton";if(f.txt.className!=b){f.txt.className=b;f.txt.style.cursor=e;f.css.cursor=e;if(f.icn){changeSimpleOffset(f.icn,f.isGray?f.disDx:f.dx,f.isGray?f.disDy:f.dy);f.icn.style.cursor=e}if(f.isDefault){var a=!g,c=_skin+"button.gif";changeSimpleOffset(f.leftImg,0,a?63:0,c);changeOffset(f.centerImg,0,a?105:42,c);changeSimpleOffset(f.rightImg,0,a?84:21,c)}}}}function ButtonWidget_setDefaultButton(){var c=this;if(c.layer){var a=!c.isGray,b=_skin+"button.gif";changeSimpleOffset(c.leftImg,0,a?63:0,b);changeOffset(c.centerImg,0,a?105:42,b);changeSimpleOffset(c.rightImg,0,a?84:21,b)}c.isDefault=true}function ButtonWidget_isDisabled(){return this.isGray}function ButtonWidget_setText(a){this.txt.innerHTML=convStr(a)}function ButtonWidget_setTooltip(a){var b=this;b.tooltip=a;b.layer.title=a;if(b.txt){b.txt.title=a}if(b.icn){b.icn.title=a}}function ButtonWidget_init(){var b=this;b.oldInit();b.txt=getLayer("theBttn"+this.id);b.icn=getLayer("theBttnIcon"+this.id);b.leftImg=getLayer("theBttnLeftImg"+this.id);b.centerImg=getLayer("theBttnCenterImg"+this.id);b.rightImg=getLayer("theBttnRightImg"+this.id);var a=b.isGray?"wizbuttongray":"wizbutton";if(b.txt.className!=a){b.setDisabled(b.isGray)}}function ButtonWidget_changeImg(b,a,f,e,c,g){var k=this;if(c){k.url=c}if(b!=null){k.dx=b}if(a!=null){k.dy=a}if(f!=null){k.disDx=f}if(e!=null){k.disDy=e}if(g!=null){k.tooltip=g}if(k.icn){changeSimpleOffset(k.icn,k.isGray?k.disDx:k.dx,k.isGray?k.disDy:k.dy,k.url,k.tooltip)}}function ButtonWidget_clickCB(a){var b=ButtonWidget_inst[a];if(b&&!b.isGray){if(b.isDelayCallback){setTimeout("ButtonWidget_delayClickCB("+a+")",1)}else{ButtonWidget_delayClickCB(a)}}}function ButtonWidget_delayClickCB(a){var b=ButtonWidget_inst[a];b.executeCB()}function ButtonWidget_executeCB(){var o=this;if(o.cb){if(typeof o.cb!="string"){o.cb()}else{eval(o.cb)}}}function ButtonWidget_keydownCB(f,b){var a=eventGetKey(f);var c=ButtonWidget_inst[b];if(a==13&&c.cb){eventCancelBubble(f)}return true}function newScrolledZoneWidget(k,c,f,b,e,a){var g=newWidget(k);g.borderW=c;g.padding=f;g.w=b;g.h=e;g.oldResize=g.resize;g.beginHTML=ScrolledZoneWidget_beginHTML;g.endHTML=ScrolledZoneWidget_endHTML;g.resize=ScrolledZoneWidget_resize;g.bgClass=(a)?a:"insetBorder";return g}function ScrolledZoneWidget_beginHTML(){var a=this.w,b=this.h;var c=_moz?2*(this.borderW+this.padding):0;if(typeof(a)=="number"){if(_moz){a=Math.max(0,a-c)}a=""+a+"px"}if(typeof(b)=="number"){if(_moz){b=Math.max(0,b-c)}b=""+b+"px"}return'<div tabindex=-1 align="left" class="'+this.bgClass+'" id="'+this.id+'" style="border-width:'+this.borderW+"px;padding:"+this.padding+"px;"+sty("width",a)+sty("height",b)+'overflow:auto">'}function ScrolledZoneWidget_endHTML(){return"</div>"}function ScrolledZoneWidget_resize(a,b){if(_moz){var c=2*(this.borderW+this.padding);if(a!=null){a=Math.max(0,a-c)}if(b!=null){b=Math.max(0,b-c)}}this.oldResize(a,b)}function newComboWidget(g,f,a,b,c){var e=newWidget(g);e.tooltip=c;e.size=1;e.getHTML=ComboWidget_getHTML;e.beginHTML=ComboWidget_beginHTML;e.endHTML=ComboWidget_endHTML;e.changeCB=f;e.noMargin=a;e.width=b==null?null:""+b+"px";e.add=ComboWidget_add;e.del=ComboWidget_del;e.getSelection=ComboWidget_getSelection;e.select=ComboWidget_select;e.valueSelect=ComboWidget_valueSelect;e.getCount=ComboWidget_getCount;e.oldSetDisabled=e.setDisabled;e.setDisabled=ComboWidget_setDisabled;e.setUndefined=ComboWidget_setUndefined;e.delByID=ComboWidget_delByID;e.findByValue=ComboWidget_findByValue;e.findByText=ComboWidget_findByText;e.getValue=ComboWidget_getValue;e.isGrayed=ComboWidget_isGrayed;e.clearSelection=ComboWidget_clearSelection;e.isDisabled=false;e.multi=false;e.undef=false;e.isCombo=true;e.undefId=e.id+"__undef";e.disabledId=e.id+"__disabled";return e}_extrCmbS=(_moz?"font-size:12px;":"");function ComboWidget_beginHTML(){var b=this,a=((_moz&&!b.isCombo)?"font-size:12px;":"");return"<select "+(b.multi?"multiple":"")+" "+(b.noMargin?'style="'+sty("width",b.width)+a+'"':'style="'+sty("width",b.width)+"margin-left:10px;"+a+'"')+' class="listinputs" '+attr("onchange",_codeWinName+".ComboWidget_changeCB(event,this)")+attr("onclick",_codeWinName+".ComboWidget_clickCB(event,this)")+attr("ondblclick",_codeWinName+".ComboWidget_dblClickCB(event,this)")+attr("onkeyup",_codeWinName+".ComboWidget_keyUpCB(event,this)")+attr("onkeydown",_codeWinName+".ComboWidget_keyDownCB(event,this)")+attr("id",b.id)+attr("name",b.id)+attr("title",b.tooltip)+'size="'+b.size+'">'}function ComboWidget_clearSelection(){var a=this;if(a.layer){a.layer.selectedIndex=-1}}function ComboWidget_endHTML(){return"</select>"}function ComboWidget_getHTML(a){return this.beginHTML()+(a?a:"")+this.endHTML()}function ComboWidget_add(b,k,c,l,g){var f=this.layer,a=_curDoc.createElement("option");if(_ie){f.options.add(a)}else{f.appendChild(a)}if(a.innerText!=null){a.innerText=b}else{a.innerHTML=convStr(b)}a.value=k;if(l!=null){a.id=l}if(c){a.selected=true}if(g){a.style.color="gray"}return a}function ComboWidget_getSelection(){var c=this.layer,b=c.selectedIndex;if(b<0){return null}var a=new Object;a.index=b;a.value=c.options[b].value;a.text=c.options[b].text;return a}function ComboWidget_select(b){var f=this,c=f.layer,a=c.options.length;if(b==null){c.selectedIndex=-1}if((b<0)||(b>=a)){b=a-1}if(b>=0){c.selectedIndex=b}f.setUndefined(false)}function ComboWidget_valueSelect(b){var k=this,g=k.layer,f=g.options,a=f.length;for(var c=0;c<a;c++){if(f[c].value==b){f[c].selected=true;k.setUndefined(false);break}}}function ComboWidget_del(a){var b=this.layer;if(a==null){b.options.length=0}else{if(_ie){b.remove(a)}else{b.options[a]=null}this.select(a)}}function ComboWidget_changeCB(b,a){var c=getWidget(a);if(c.changeCB){c.changeCB(b)}}function ComboWidget_clickCB(b,a){var c=getWidget(a);if(c.clickCB){c.clickCB(b)}}function ComboWidget_dblClickCB(b,a){var c=getWidget(a);if(c.dblClickCB){c.dblClickCB(b)}}function ComboWidget_keyUpCB(b,a){var c=getWidget(a);if(c.keyUpCB){c.keyUpCB(b)}}function ComboWidget_keyDownCB(c,a){var b=eventGetKey(c);var f=getWidget(a);if(f.isCombo&&(b==27||b==13)){eventCancelBubble(c)}else{if(b==13&&f.keyUpCB){eventCancelBubble(c)}}}function ComboWidget_getCount(){return this.layer.options.length}function ComboWidget_delByID(b){var a=getLayer(b);if(a!=null){this.del(a.index)}a=null}function ComboWidget_setDisabled(e,b){var c=this;c.oldSetDisabled(e);c.isDisabled=e;if(e==true){var a=getLayer(c.disabledId);if(a==null){c.add("","",true,c.disabledId)}else{c.layer.selectedIndex=a.index}}else{c.delByID(c.disabledId)}}function ComboWidget_setUndefined(b){var c=this;c.undef=b;if(b==true){var a=getLayer(c.undefId);if(a==null){c.add("","",true,c.undefId)}else{c.layer.selectedIndex=a.index}}else{c.delByID(c.undefId)}}function ComboWidget_findByValue(l){var k=this,g=k.layer,f=g.options,a=f.length;for(var c=0;c<a;c++){if(f[c].value==l){var b=new Object;b.index=c;b.value=g.options[c].value;b.text=g.options[c].text;return b}}return null}function ComboWidget_findByText(b){var l=this,k=l.layer,g=k.options,a=g.length;for(var f=0;f<a;f++){if(g[f].text==b){var c=new Object;c.index=f;c.value=k.options[f].value;c.text=k.options[f].text;return c}}return null}function ComboWidget_getValue(c){var k=this,g=k.layer,f=g.options,a=f.length;if(c==null||c<0||c>a){return null}var b=new Object;b.index=c;b.value=g.options[c].value;return b}function ComboWidget_isGrayed(b){var g=this,f=g.layer,c=f.options,a=c.length;if(b==null||b<0||b>a){return false}return(f.options[b].style.color=="gray")}function newListWidget(a,g,f,b,n,m,k,e,l){var c=newComboWidget(a,g,true,b,m);c.clickCB=l;c.dblClickCB=k;c.keyUpCB=e;c.size=n;c.multi=f;c.getMultiSelection=ListWidget_getMultiSelection;c.setUndefined=ListWidget_setUndefined;c.isUndefined=ListWidget_isUndefined;c.change=ListWidget_change;c.isCombo=false;return c}function ListWidget_setUndefined(a){var b=this;b.undef=a;if(a==true){b.layer.selectedIndex=-1}}function ListWidget_isUndefined(){return(this.layer.selectedIndex==-1)}function ListWidget_getMultiSelection(){var k=this.layer,g=new Array,a=k.options.length;for(var f=0;f<a;f++){var c=k.options[f];if(c.selected){var b=new Object;b.index=f;b.value=c.value;b.text=c.text;g[g.length]=b}}return g}function ListWidget_change(b,a){var c=this;if(b!=null){c.multi=b;c.layer.multiple=b}if(a!=null){c.size=a;c.layer.size=a}}function newInfoWidget(g,f,b,e,a){var c=newWidget(g);c.title=f?f:"";c.boldTitle=b?b:"";c.text=e?e:"";c.height=(a!=null)?a:55;c.getHTML=InfoWidget_getHTML;c.setText=InfoWidget_setText;c.setTitle=InfoWidget_setTitle;c.setTitleBold=InfoWidget_setTitleBold;c.oldResize=c.resize;c.resize=InfoWidget_resize;c.textLayer=null;return c}function InfoWidget_setText(e,b){var c=this;e=e?e:"";c.text=e;if(c.layer){var a=c.textLayer;if(a==null){a=c.textLayer=getLayer("infozone_"+c.id)}if(a){a.innerHTML=b?e:convStr(e,false,true)}}}function InfoWidget_setTitle(c){var b=this;c=c?c:"";b.title=c;if(b.layer){var a=b.titleLayer;if(a==null){a=b.titleLayer=getLayer("infotitle_"+b.id)}if(a){a.innerHTML=convStr(c)}}}function InfoWidget_setTitleBold(c){var b=this;c=c?c:"";b.boldTitle=c;if(b.layer){var a=b.titleLayerBold;if(a==null){a=b.titleLayerBold=getLayer("infotitlebold_"+b.id)}if(a){a.innerHTML=convStr(c)}}}function InfoWidget_getHTML(){var a=this;return'<div class="dialogzone" align="left" style="overflow:hidden;'+sty("width",a.width)+sty("height",""+a.height+"px")+'" id="'+a.id+'"><nobr>'+img(_skin+"../help.gif",16,16,"top",null,_helpLab)+'<span class="dialogzone" style="padding-left:5px" id="infotitle_'+a.id+'">'+convStr(a.title)+'</span><span style="padding-left:5px" class="dialogzonebold" id="infotitlebold_'+a.id+'">'+convStr(a.boldTitle)+"</span></nobr><br>"+getSpace(1,2)+'<div class="infozone" align="left" id="infozone_'+a.id+'" style="height:'+(a.height-18-(_moz?10:0))+"px;overflow"+(_ie?"-y":"")+':auto">'+convStr(a.text,false,true)+"</div></div>"}function InfoWidget_resize(b,c){var e=this;if(b!=null){e.w=b}if(c!=null){e.h=c}e.oldResize(b,c);if(e.layer){var a=e.textLayer;if(a==null){a=e.textLayer=getLayer("infozone_"+e.id)}if(a){if(e.h!=null){a.style.height=""+Math.max(0,e.h-(_ie?18:28))+"px"}}}}function newCheckWidget(a,l,g,f,m,k,e,c){var b=newWidget(a);b.text=l;b.convText=c;b.changeCB=g;b.idCheckbox="check_"+a;b.checkbox=null;b.kind="checkbox";b.name=b.idCheckbox;b.bold=f;b.imgUrl=m;b.imgW=k;b.imgH=e;b.getHTML=CheckWidget_getHTML;b.setText=CheckWidget_setText;b.parentInit=Widget_init;b.init=CheckWidget_init;b.check=CheckWidget_check;b.isChecked=CheckWidget_isChecked;b.setDisabled=CheckWidget_setDisabled;b.isDisabled=CheckWidget_isDisabled;b.uncheckOthers=CheckWidget_uncheckOthers;b.isIndeterminate=CheckWidget_isIndeterminate;b.setIndeterminate=CheckWidget_setIndeterminate;b.layerClass=("dialogzone"+(b.bold?"bold":""));b.nobr=true;return b}function CheckWidget_getHTML(){var b=this,a=b.layerClass;return'<table border="0" onselectstart="return false" cellspacing="0" cellpadding="0" class="'+a+'"'+attr("id",b.id)+'><tr valign="middle"><td style="height:20px;width:21px"><input style="margin:'+(_moz?3:0)+'px" onclick="'+_codeWinName+'.CheckWidget_changeCB(event,this)" type="'+b.kind+'"'+attr("id",b.idCheckbox)+attr("name",b.name)+"></td>"+(b.imgUrl?'<td><label style="padding-left:2px" for="'+b.idCheckbox+'">'+img(b.imgUrl,b.imgW,b.imgH)+"</label></td>":"")+"<td>"+(b.nobr?"<nobr>":"")+'<label style="padding-left:'+(b.imgUrl?4:2)+'px" id="label_'+b.id+'" for="'+b.idCheckbox+'">'+(b.convText?convStr(b.text):b.text)+"</label>"+(b.nobr?"</nobr>":"")+"</td></tr></table>"}function CheckWidget_setText(a){var b=this;b.text=a;if(b.layer){if(b.labelLyr==null){b.labelLyr=getLayer("label_"+b.id)}b.labelLyr.innerHTML=b.convText?convStr(a):a}}function CheckWidget_init(){this.parentInit();this.checkbox=getLayer(this.idCheckbox)}function CheckWidget_check(a){this.checkbox.checked=a;if(a){this.uncheckOthers()}}function CheckWidget_isChecked(){return this.checkbox.checked}function CheckWidget_changeCB(b,a){var c=getWidget(a);c.uncheckOthers();if(c.changeCB){c.changeCB(b)}}function CheckWidget_setDisabled(a){this.checkbox.disabled=a;if(_moz){this.checkbox.className=(a?"dialogzone":"")}}function CheckWidget_isDisabled(){return this.checkbox.disabled}function CheckWidget_uncheckOthers(){}function CheckWidget_isIndeterminate(){return this.checkbox.indeterminate}function CheckWidget_setIndeterminate(a){this.checkbox.indeterminate=a}function newRadioWidget(a,n,p,l,k,q,m,e,c){var b=newCheckWidget(a,p,l,k,q,m,e,c);b.kind="radio";b.name=n;if(_RadioWidget_groups[n]==null){_RadioWidget_groups[n]=new Array}b.groupInstance=_RadioWidget_groups[n];var f=b.groupInstance;b.groupIdx=f.length;f[f.length]=b;b.uncheckOthers=RadioWidget_uncheckOthers;return b}var _RadioWidget_groups=new Array;function RadioWidget_uncheckOthers(){var f=this.groupInstance,b=this.groupIdx,a=f.length;for(var e=0;e<a;e++){if(e!=b){var k=f[e].checkbox;if(k){k.checked=false}}}}function newTextFieldWidget(c,k,n,g,m,b,p,e,a,l){var f=newWidget(c);f.tooltip=p;f.changeCB=k;f.maxChar=n;f.keyUpCB=g;f.enterCB=m;f.noMargin=b;f.width=e==null?null:""+e+"px";f.focusCB=a;f.blurCB=l;f.disabled=false;f.getHTML=TextFieldWidget_getHTML;f.getValue=TextFieldWidget_getValue;f.setValue=TextFieldWidget_setValue;f.intValue=TextFieldWidget_intValue;f.intPosValue=TextFieldWidget_intPosValue;f.select=TextFieldWidget_select;f.setDisabled=TextFieldWidget_setDisabled;f.beforeChange=null;f.wInit=f.init;f.init=TextFieldWidget_init;f.oldValue="";f.helpTxt="";f.isHelpTxt=false;f.setHelpTxt=TextFieldWidget_setHelpTxt;f.eraseHelpTxt=TextFieldWidget_eraseHelpTxt;f.enterCancelBubble=true;return f}function TextFieldWidget_setDisabled(b){var a=this;a.disabled=b;if(a.layer){a.layer.disabled=b}}function TextFieldWidget_init(){var a=this;a.wInit();a.layer.value=""+(a.oldValue!="")?a.oldValue:"";if(a.helpTxt&&!a.oldValue){a.setHelpTxt(a.helpTxt)}}function TextFieldWidget_getHTML(){var a=this;return"<input"+(a.disabled?" disabled":"")+' oncontextmenu="event.cancelBubble=true;return true" style="'+sty("width",this.width)+(_moz?"margin-top:1px;margin-bottom:1px;padding-left:5px;padding-right:2px;":"")+(_isQuirksMode?"height:20px;":"height:16px;")+"margin-left:"+(this.noMargin?0:10)+'px" onfocus="'+_codeWinName+'.TextFieldWidget_focus(this)" onblur="'+_codeWinName+'.TextFieldWidget_blur(this)" onchange="'+_codeWinName+'.TextFieldWidget_changeCB(event,this)" onkeydown=" return '+_codeWinName+'.TextFieldWidget_keyDownCB(event,this);" onkeyup=" return '+_codeWinName+'.TextFieldWidget_keyUpCB(event,this);" onkeypress=" return '+_codeWinName+'.TextFieldWidget_keyPressCB(event,this);" type="text" '+attr("maxLength",this.maxChar)+' ondragstart="event.cancelBubble=true;return true" onselectstart="event.cancelBubble=true;return true" class="textinputs" id="'+this.id+'" name="'+this.id+'"'+attr("title",this.tooltip)+' value="">'}function TextFieldWidget_getValue(){var a=this;if(a.isHelpTxt){return""}else{return a.layer?a.layer.value:a.oldValue}}function TextFieldWidget_setValue(a){var b=this;if(b.layer){b.eraseHelpTxt();b.layer.value=""+a}else{b.oldValue=a}}function TextFieldWidget_changeCB(b,a){var c=getWidget(a);c.eraseHelpTxt();if(c.beforeChange){c.beforeChange()}if(c.changeCB){c.changeCB(b)}}function TextFieldWidget_keyPressCB(b,a){var c=getWidget(a);if(eventGetKey(b)==13){c.enterKeyPressed=true;return false}else{c.enterKeyPressed=false}return true}function TextFieldWidget_keyUpCB(b,a){var c=getWidget(a);c.eraseHelpTxt();if(eventGetKey(b)==13&&c.enterKeyPressed){if(c.beforeChange){c.beforeChange()}if(c.enterCB){if(c.enterCancelBubble){eventCancelBubble(b)}c.enterCB(b)}return false}else{if(c.keyUpCB){c.keyUpCB(b)}}c.enterKeyPressed=false;return true}function TextFieldWidget_keyDownCB(b,a){var c=getWidget(a);c.eraseHelpTxt();c.enterKeyPressed=false;if(eventGetKey(b)==13){return true}else{if(eventGetKey(b)==8){eventCancelBubble(b)}}return true}function TextFieldWidget_eraseHelpTxt(){var a=this;if(a.isHelpTxt){a.layer.value=""}a.isHelpTxt=false;a.layer.style.color="black"}function TextFieldWidget_focus(a){var b=getWidget(a);b.eraseHelpTxt();if(b.focusCB){b.focusCB()}}function TextFieldWidget_blur(a){var b=getWidget(a);if(b.beforeChange){b.beforeChange()}if(b.blurCB){b.blurCB()}}function TextFieldWidget_intValue(a){var b=parseInt(this.getValue());return isNaN(b)?a:b}function TextFieldWidget_intPosValue(a){var b=this.intValue(a);return(b<0)?a:b}function TextFieldWidget_select(){this.layer.select()}function TextFieldWidget_setHelpTxt(a){var b=this;b.helpTxt=a;if(b.layer&&(b.layer.value=="")){b.isHelpTxt=true;b.layer.value=a;b.layer.style.color="#808080"}}function newIntFieldWidget(c,k,m,g,l,b,n,e,a){var f=newTextFieldWidget(c,k,m,g,l,b,n,e);f.min=-Number.MAX_VALUE;f.max=Number.MAX_VALUE;f.customCheckCB=a;f.setMin=IntFieldWidget_setMin;f.setMax=IntFieldWidget_setMax;f.setValue=IntFieldWidget_setValue;f.beforeChange=IntFieldWidget_checkChangeCB;f.value="";return f}function IntFieldWidget_setMin(a){if(!isNaN(a)){this.min=a}}function IntFieldWidget_setMax(a){if(!isNaN(a)){this.max=a}}function IntFieldWidget_setValue(b){var c=this,a=c.layer;b=""+b;if(b==""){if(a){a.value=""}c.oldValue="";return}var e=parseInt(b);value="";if(!isNaN(e)&&(e>=c.min)&&(e<=c.max)&&((c.customCheckCB==null)||c.customCheckCB(e))){value=e;c.oldValue=value}else{if(c.oldValue){value=c.oldValue}}if(a){a.value=""+value}}function IntFieldWidget_checkChangeCB(){var a=this;a.setValue(a.layer.value)}function newFrameZoneWidget(f,a,c,b){var e=newWidget(f);e.w=(a!=null)?""+Math.max(0,a-10)+"px":null;e.h=(c!=null)?""+Math.max(0,c-10)+"px":null;e.reverse=(b!=null)?b:false;e.cont=null;e.beginHTML=FrameZoneWidget_beginHTML;e.endHTML=FrameZoneWidget_endHTML;e.oldResize=e.resize;e.resize=FrameZoneWidget_resize;return e}function FrameZoneWidget_resize(a,b){var e=this;var c=e.layer.display!="none";if(c&_moz&&!_saf){e.setDisplay(false)}e.oldResize(a,b);if(c&_moz&&!_saf){e.setDisplay(true)}}function FrameZoneWidget_beginHTML(){var a=this;return'<table width="100%" style="'+sty("width",a.w)+sty("height",a.h)+'" id="'+a.id+'" cellspacing="0" cellpadding="4" border="0"><tbody><tr><td valign="top" class="dlgFrame" id="frame_cont_'+a.id+'" style="padding:5px">'}function FrameZoneWidget_endHTML(){var a=this;return"</td></tr></tbody></table>"}function arrayAdd(f,k,e,b){var g=f[k],a=g.length;if((b==null)||(typeof b!="number")){b=-1}if((b<0)||(b>a)){b=a}if(b!=a){var c=g.slice(b);g.length=b+1;g[b]=e;g=g.concat(c)}else{g[b]=e}f[k]=g;return b}function arrayRemove(e,g,a){var f=e[g],c=f.length-1;if(a==null){f.length=0;e[g]=f;return -1}if((a<0)||(a>c)){return -1}if(a==c){f.length=c}else{var b=f.slice(a+1);f.length=a;f=f.concat(b)}e[g]=f;return a}function getFrame(name,par){if(par==null){par=self}var frames=par.frames,w=eval("frames."+name);if(w==null){return w}var l=frames.length;for(var i=0;i<l;i++){w=frames[i];try{if(w.name==name){return w}}catch(exc){}}return null}function frameGetUrl(a){return a.location.href}function frameReload(a){var b=a.location;b.replace(b.href)}function setTopFrameset(){_curWin._topfs="topfs"}function getTopFrameset(a){if(a==null){a=self}if(a._topfs=="topfs"){return a}else{if(a!=top){return getTopFrameset(a.parent)}else{return null}}}function convStr(e,a,c){e=""+e;var b=e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;");if(a){b=b.replace(/ /g,"&nbsp;")}if(c){b=b.replace(/\n/g,"<br>")}return b}function escapeCR(b){b=""+b;var a=b.replace(/\r/g,"").replace(/\n/g,"\\n");return a}function addDblClickCB(b,a){if(b.addEventListener&&!_saf){b.addEventListener("dblclick",a,false)}else{b.ondblclick=a}}function img(f,a,c,g,b,e){b=(b?b:"");if(e==null){e=""}return"<img"+attr("width",a)+attr("height",c)+attr("src",f)+attr("alt",e)+attr("align",g)+' border="0" hspace="0" vspace="0" '+(b?b:"")+">"}function imgOffset(a,k,e,n,m,b,g,c,l,f){return img(_skin+"../transp.gif",k,e,f,(g?g:"")+" "+attr("id",b)+' style="float:left;'+backImgOffset(a,n,m)+(l?l:"")+'"',c)}function simpleImgOffset(a,k,e,n,m,b,g,c,l,f){if(_ie){if(n==null){n=0}if(m==null){m=0}return"<div "+(g?g:"")+" "+attr("id",b)+' style="position:relative;padding:0px;width:'+k+"px;height:"+e+"px;overflow:hidden;"+(l?l:"")+'">'+img(a,null,null,(f?f:"top"),'style="margin:0px;position:relative;top:'+(-m)+"px;left:"+(-n)+'px" tabIndex="-1"',c)+"</div>"}else{return imgOffset(a,k,e,n,m,b,g,c,l,f)}}function changeSimpleOffset(f,b,a,e,g){if(_ie){f=f.childNodes[0];var c=f.style;if((e!=null)&&(e!=f.src)){f.src=e}if(b!=null){c.left=""+(-b)+"px"}if(a!=null){c.top=""+(-a)+"px"}if(g!=null){f.title=g;f.alt=g}}else{changeOffset(f,b,a,e,g)}}function backImgOffset(c,b,a){return"background-image:url('"+c+"');background-position:"+(-b)+"px "+(-a)+"px;"}function changeOffset(f,b,a,e,g){var c=f.style;if(c){if((b!=null)&&(a!=null)){c.backgroundPosition=""+(-b)+"px "+(-a)+"px"}if(e){c.backgroundImage="url('"+e+"')"}}if(g){f.title=g}}function includeCSS(b,c){if(typeof(_skin)=="string"&&_skin!=""){var a="";if(c){a=_skin+"../"+b}else{a=_skin+b}a+=".css";_curDoc.write('<link rel="stylesheet" type="text/css" href="'+a+'">')}}function getLayer(b){var a=null;if(typeof b=="object"){a=_curDoc.getElementById(b.id)}else{a=_curDoc.getElementById(b)}return a}function setLayerTransp(a,b){if(_ie){a.style.filter=(b==null)?"":"progid:DXImageTransform.Microsoft.Alpha( style=0,opacity="+b+")"}else{a.style.MozOpacity=(b==null)?1:b/100}}function getPos(b,a){a=a?a:null;for(var e=0,c=0;(b!=null)&&(b!=a);e+=b.offsetLeft,c+=b.offsetTop,b=b.offsetParent){}return{x:e,y:c}}function getPos2(b,a){var a=a?a:null;var e=0;var c=0;while(b.parentNode||b.offsetParent){if(b.offsetParent){e+=b.offsetLeft;c+=b.offsetTop;b=b.offsetParent}else{if(b.parentNode){if(b.style){if(b.style.left){e+=b.style.left}if(b.style.top){c+=b.style.top}}b=b.parentNode}else{break}}}if(a){relToCord=getPos2(a);e-=relToCord.x;c-=relToCord.y}return{x:e,y:c}}function getPosScrolled(c,b){b=b?b:null;if(_ie){for(var f=0,e=0;(c!=null)&&(c!=b);f+=c.offsetLeft-c.scrollLeft,e+=c.offsetTop-c.scrollTop,c=c.offsetParent){}}else{var a=c;for(var f=0,e=0;(c!=null)&&(c!=b);f+=c.offsetLeft,e+=c.offsetTop,c=c.offsetParent){}for(c=a;(c!=null)&&(c!=b);c=c.parentNode){if(c.scrollLeft!=null){f-=c.scrollLeft;e-=c.scrollTop}}}f+=getScrollX();e+=getScrollY();return{x:f,y:e}}function getWidget(b){if(b==null){return null}var a=b._widget;if(a!=null){return _widgets[a]}else{return getWidget(b.parentNode)}}function getWidgetFromID(b){if(b==null){return null}var a=getLayer(b);return getWidget(a)}function attr(a,b){return(b!=null?" "+a+'="'+b+'" ':"")}function sty(a,b){return(b!=null?a+":"+b+";":"")}function getSep(b,a){if(b==null){b=0}var c=b>0?'<td width="'+b+'">'+getSpace(b,1)+"</td>":"";return'<table style="margin-top:5px;margin-bottom:5px;" width="100%" cellspacing="0" cellpadding="0"><tr>'+c+'<td background="'+_skin+"sep"+(a?"_solid":"")+'.gif" class="smalltxt"><img alt="" src="'+_skin+'../transp.gif" width="10" height="2"></td>'+c+"</tr></table>"}function writeSep(b,a){_curDoc.write(getSep(b,a))}function getSpace(a,b){return'<table height="'+b+'" border="0" cellspacing="0" cellpadding="0"><tr><td>'+img(_skin+"../transp.gif",a,b)+"</td></tr></table>"}function writeSpace(a,b){_curDoc.write(getSpace(a,b))}function documentWidth(b){var b=b?b:_curWin;var a=Math.max(document.body.clientWidth,document.documentElement.clientWidth);a=Math.max(a,document.body.scrollWidth);return a}function documentHeight(b){var b=b?b:_curWin;var a=Math.max(document.body.clientHeight,document.documentElement.clientHeight);a=Math.max(a,document.body.scrollHeight);return a}function winWidth(b){var a;var b=b?b:_curWin;if(_ie){if(_isQuirksMode){a=b.document.body.clientWidth}else{a=b.document.documentElement.clientWidth}}else{a=b.innerWidth}return a}function winHeight(b){var b=b?b:_curWin;var a;if(_ie){if(_isQuirksMode){a=document.body.clientHeight}else{a=document.documentElement.clientHeight}}else{a=b.innerHeight}return a}function getScrollX(a){var b=0;var a=a?a:_curWin;if(typeof(a.scrollX)=="number"){b=a.scrollX}else{b=Math.max(a.document.body.scrollLeft,a.document.documentElement.scrollLeft)}return b}function getScrollY(b){var a=0;var b=b?b:_curWin;if(typeof(b.scrollY)=="number"){a=window.scrollY}else{a=Math.max(b.document.body.scrollTop,b.document.documentElement.scrollTop)}return a}function winScrollTo(a,c,b){b=b?b:_curWin;b.scrollTo(a,c)}function eventGetKey(b,a){a=a?a:_curWin;return _ie?a.event.keyCode:b.keyCode}function eventGetX(a){return _ie?_curWin.event.clientX:a.clientX?a.clientX:a.pageX}function eventGetY(a){return _ie?_curWin.event.clientY:a.clientY?a.clientY:a.pageY}function xpos(f,c,b,a){if((a==null)||(!_ie)){a=1}return((c.clientX/a)-getPos(f).x)+getScrollX()}function ypos(f,c,b,a){if((a==null)||(!_ie)){a=1}return((c.clientY/a)-getPos(f).y)+(_ie?b.body.scrollTop:0)}function absxpos(b,a){if((a==null)||(!_ie)){return b.clientX}else{return b.clientX/a}}function absypos(b,a){if((a==null)||(!_ie)){return b.clientY}else{return b.clientY/a}}function eventCancelBubble(c,b){b=b?b:_curWin;var a=_ie?b.event:c;if(a){a.cancelBubble=true;if(a.stopPropagation){a.stopPropagation()}}}function isHidden(b){if((b==null)||(b.tagName=="BODY")){return false}var a=b.style;if((a==null)||(a.visibility==_hide)||(a.display=="none")){return true}return isHidden(b.parentNode)}function opt(c,a,b){return'<option value="'+c+'" '+(b?"selected":"")+">"+convStr(""+a)+"</option>"}function lnk(c,e,a,g,b,f){if(e==null){e="return false"}b=b?b:"";return"<a"+attr("class",a)+attr("id",g)+attr("href","javascript:void(0)")+attr("onclick",e)+attr("ondblclick",f)+b+">"+c+"</a>"}_oldErrHandler=null;function localErrHandler(){return true}function canScanFrames(a){var b=true,e=null;if(_moz){_oldErrHandler=window.onerror;window.onerror=localErrHandler}try{e=a.document;b=false}catch(c){}if(_moz){window.onerror=_oldErrHandler}return(!b&&(e!=null))}function getBGIframe(a){return'<iframe id="'+a+'" name="'+a+'" style="display:none;left:0px;position:absolute;top:0px" src="'+_skin+'../../empty.html" frameBorder="0" scrolling="no"></iframe>'}function getDynamicBGIFrameLayer(){var a=false;if(_curWin.BGIFramePool){BGIFrames=_curWin.BGIFramePool.split(",");BGIFCount=BGIFrames.length;for(var b=0;b<BGIFCount;b++){if(BGIFrames[b]!="1"){a=true;break}}}else{b=0;BGIFrames=new Array}BGIFrames[b]="1";_curWin.BGIFramePool=BGIFrames.join(",");if(!a){targetApp(getBGIframe("BGIFramePool_"+b))}return getLayer("BGIFramePool_"+b)}function holdBGIFrame(b){var a=getLayer(b);if(a){a.style.display=""}id=parseInt(b.split("_")[1]);BGIFrames=_curWin.BGIFramePool.split(",");BGIFrames[id]=1;_curWin.BGIFramePool=BGIFrames.join(",")}function releaseBGIFrame(b){var a=getLayer(b);if(a){a.style.display="none"}id=parseInt(b.split("_")[1]);BGIFrames=_curWin.BGIFramePool.split(",");BGIFrames[id]=0;_curWin.BGIFramePool=BGIFrames.join(",")}function append(g,b,l){if(_ie){g.insertAdjacentHTML("BeforeEnd",b)}else{var a=l?l:_curDoc;var f=a.createRange();f.setStartBefore(g);var k=f.createContextualFragment(b);g.appendChild(k)}}function append2(g,b,l){if(_ie){g.insertAdjacentHTML("afterBegin",b)}else{var a=l?l:_curDoc;var f=a.createRange();f.setStartBefore(g);var k=f.createContextualFragment(b);g.appendChild(k)}}function insBefore(g,b,l){if(_ie){g.insertAdjacentHTML("BeforeBegin",b)}else{var a=l?l:_curDoc;var f=_curDoc.createRange();f.setEndBefore(g);var k=f.createContextualFragment(b);g.parentNode.insertBefore(k,g)}}function insBefore2(g,b,l){if(_ie){g.insertAdjacentHTML("BeforeBegin",b)}else{var a=l?l:_curDoc;var f=_curDoc.createRange();f.setStartBefore(g);var k=f.createContextualFragment(b);g.parentNode.insertBefore(k,g)}}function targetApp(a){append(_curDoc.body,a)}function preloadImg(b){var a=_preloadArr[_preloadArr.length]=new Image;a.src=b}_staticUnicBlockWhileWaitWidgetID="staticUnicBlockWhileWaitWidgetID";function hideBlockWhileWaitWidget(){var a=getLayer(_staticUnicBlockWhileWaitWidgetID);if(a){a.style.display="none"}}function newBlockWhileWaitWidget(a){if(window._BlockWhileWaitWidget!=null){return window._BlockWhileWaitWidget}var b=newWidget(_staticUnicBlockWhileWaitWidgetID);b.getPrivateHTML=BlockWhileWaitWidget_getPrivateHTML;b.init=BlockWhileWaitWidget_init;b.show=BlockWhileWaitWidget_show;window._BlockWhileWaitWidget=b;return b}function BlockWhileWaitWidget_init(){}function BlockWhileWaitWidget_getPrivateHTML(){return'<div id="'+this.id+'" onselectstart="return false" ondragstart="return false" onmousedown="'+_codeWinName+'.eventCancelBubble(event)" border="0" hspace="0" vspace="0"  style="background-image:url('+_skin+'../transp.gif);z-index:5000;cursor:wait;position:absolute;top:0px;left:0px;width:100%;height:100%"></div>'}function BlockWhileWaitWidget_show(a){var b=this;if(b.layer==null){b.layer=getLayer(b.id);if(b.layer==null){targetApp(b.getPrivateHTML());b.layer=getLayer(b.id);b.css=b.layer.style}else{b.css=b.layer.style}}b.setDisplay(a)}function isTextInput(a){var b=_ie?a.srcElement:a.target;var c=false;if(b.tagName=="TEXTAREA"){c=true}if((b.tagName=="INPUT")&&((b.type.toLowerCase()=="text")||(b.type.toLowerCase()=="password"))){c=true}return c}function isTextArea(a){var b=_ie?a.srcElement:a.target;if(b.tagName=="TEXTAREA"){return true}else{return false}}function LZ(a){return(a<0||a>9?"":"0")+a}if(bobj.crv.config.isDebug){localErrHandler=null}initDom(bobj.crvUri("../dhtmllib/images/")+bobj.crv.config.skin+"/","",bobj.crv.config.lang);styleSheet();_allBOIcons=new Array;_allBOIconsMenus=new Array;_menuType_simple=0;_menuType_color=1;_menuType_border=2;function NewLabelWidget(e,c,a){var b=newWidget(e);b.text=c;b.convBlanks=a;b.getHTML=LabelWidget_getHTML;b.setDisabled=LabelWidget_setDisabled;b.dis=false;return b}function LabelWidget_setDisabled(a){var b=this;if(b.dis!=a){b.dis=a;if(b.layer){b.layer.className="iconText"+(a?"Dis":"")}}}function LabelWidget_getHTML(){var a=this;return'<div id="'+a.id+'" class="iconText'+(a.dis?"Dis":"")+'" style="white-space:nowrap;margin-right:4px;margin-left:4px;cursor:default">'+convStr(a.text,a.convBlanks)+"</div>"}function newIconWidget(c,b,r,m,g,l,k,s,p,a,q,n,f){var e=newWidget(c);e.src=b;e.clickCB=r;e.text=m;e.alt=g;e.isTabEnabled=n;e.ariaHasPopUp=f;e.width=null;e.txtAlign="left";e.border=4;e.txtNoPadding=false;e.allowDblClick=false;if(b){e.w=(l!=null)?l:16;e.h=(k!=null)?k:16;e.dx=(s!=null)?s:0;e.dy=(p!=null)?p:0;e.disDx=(a!=null)?a:0;e.disDy=(q!=null)?q:0}else{e.w=1;e.h=16}e.dis=false;e.disp=true;e.margin=1;e.extraHTML="";e.imgLayer=null;e.txtLayer=null;e.overCB="IconWidget_overCB";e.outCB="IconWidget_outCB";e.isDisplayed=IconWidget_isDisplayed;e.setDisplay=IconWidget_setDisplay;e.getHTML=IconWidget_getHTML;e.getTxtWidth=IconWidget_getTxtWidth;e.index=_allBOIcons.length++;e.nocheckClass="iconnocheck";e.hoverClass="iconhover";e.checkClass="iconcheck";e.checkhoverClass="iconcheckhover";e.currentClass=e.nocheckClass;e.currentHoverClass=e.hoverClass;e.setClasses=IconWidget_setClasses;e.internalUpCB=null;e.internalDownCB=IconWidget_internalDownCB;e.internalUpCB=IconWidget_internalUpCB;e.isHover=false;e.changeTooltip=IconWidget_changeTooltip;e.changeText=IconWidget_changeText;e.changeImg=IconWidget_changeImg;e.setDisabled=IconWidget_setDisabled;e.isDisabled=IconWidget_isDisabled;e.acceptClick=IconWidget_acceptClick;_allBOIcons[e.index]=e;e.outEnable=true;e.setCrs=IconWidget_setCrs;e.oldRes=e.resize;e.resize=IconWidget_resize;e.iconOldInit=e.init;e.init=IconWidget_init;return e}function newIconMenuWidget(c,b,t,p,g,n,k,u,q,a,s,f,m,l){var e=newWidget(c);if(typeof(l)=="undefined"){l=f?_menuType_color:_menuType_simple}e.menuItemType=f?_isColor:_isNotColor;var r=_openMenu.replace("{0}",(p?p:(g?g:"")));e.icon=newIconWidget("iconMenu_icon_"+c,b,IconMenuWidget_iconClickCB,p,g,n,k,u,q,a,s,false);e.arrow=newIconWidget("iconMenu_arrow_"+c,_skin+"menus.gif",IconMenuWidget_arrowClickCB,null,r,7,16,0,81,0,97,true);switch(l){case _menuType_color:e.menu=newMenuColorWidget("iconMenu_menu_"+c,IconMenuWidget_hideCB);break;case _menuType_border:e.menu=newMenuBordersWidget("iconMenu_menu_"+c,IconMenuWidget_hideCB,m,IconBordersMenuWidget_internalClickCB);break;default:case _menuType_simple:e.menu=newMenuWidget("iconMenu_menu_"+c,IconMenuWidget_hideCB,m);break}e.icon.par=e;e.arrow.par=e;e.menu.parIcon=e;e.icon.margin=0;e.arrow.margin=0;e.icon.overCB="IconWidget_none";e.icon.outCB="IconWidget_none";e.arrow.overCB="IconWidget_none";e.arrow.outCB="IconWidget_none";e.margin=1;e.spc=0;e.getHTML=IconMenuWidget_getHTML;e.clickCB=t;e.getMenu=IconMenuWidget_getMenu;e.menIcnOldInit=e.init;e.init=IconMenuWidget_init;e.removeAllMenuItems=IconMenuWidget_removeAllMenuItems;e.index=_allBOIconsMenus.length++;_allBOIconsMenus[e.index]=e;e.setDisabled=IconMenuWidget_setDisabled;e.isDisabled=IconMenuWidget_isDisabled;e.disableMenu=IconMenuWidget_disableMenu;e.changeText=IconMenuWidget_changeText;e.imwpResize=e.resize;e.resize=IconMenuWidget_resize;e.focus=IconMenuWidget_focus;e.changeArrowTooltip=IconMenuWidget_changeArrowTooltip;e.disp=true;e.isDisplayed=IconWidget_isDisplayed;e.setDisplay=IconWidget_setDisplay;return e}function IconMenuWidget_removeAllMenuItems(){this.menu.removeAll();this.menu.resetItemCount()}function IconMenuWidget_changeText(a){this.icon.changeText(a)}function IconMenuWidget_changeArrowTooltip(a){this.arrow.changeTooltip(a,false)}function IconMenuWidget_resize(a,b){var e=this;if(a!=null){a=Math.max(0,a-2*e.margin)}var c=e.layer.display!="none";if(c&_moz&&!_saf){e.setDisplay(false)}e.imwpResize(a,b);if(a!=null){e.icon.resize(Math.max(0,a-13-e.spc))}if(c&_moz&&!_saf){e.setDisplay(true)}}function IconMenuWidget_setDisabled(a){var b=this;if(a){if(b.menu.isShown()){b.menu.show(false)}IconMenuWidgetOutCB(b.index)}b.icon.setDisabled(a);b.arrow.setDisabled(a)}function IconMenuWidget_isDisabled(){return(this.icon.dis==true)}function IconMenuWidget_internalCB(){var c=this,a=null;if(c.id!=null){a=(c.menuItemType!=_isLastUsedColor)?c.id.slice(6):c.color}var b=c.par.parIcon;b.oldColor=b.curColor;b.curColor=a;if(b.curColor!=null){b.showSample()}if(b.clickColor){b.clickColor()}}function IconMenuWidget_focus(){var a=this;a.arrow.focus()}function IconMenuWidget_disableMenu(a){var c=this;c.arrow.setDisabled(a);c.menu.setDisabled(a)}function IconMenuWidget_getMenu(){return this.menu}function IconWidget_none(){}function IconMenuWidget_init(){var b=this;b.menIcnOldInit();b.icon.init();b.arrow.init();b.menu.init();var a=b.layer;a.onmouseover=IconMenuWidget_OverCB;a.onmouseout=IconMenuWidget_OutCB}function IconMenuWidget_getHTML(){var b=this,a=b.disp?"":"display:none;";return'<table id="'+b.id+'" cellspacing="0" cellpadding="0" border="0" style="'+a+"margin:"+b.margin+'px"><tr><td>'+b.icon.getHTML()+'</td><td style="padding-left:'+b.spc+'px" width="'+(13+b.spc)+'">'+b.arrow.getHTML()+"</td></table>"}function IconMenuWidget_OverCB(){IconMenuWidgetOverCB(getWidget(this).index);return true}function IconMenuWidget_OutCB(){IconMenuWidgetOutCB(getWidget(this).index)}function IconMenuWidgetOverCB(a){o=_allBOIconsMenus[a];IconWidget_overCB(o.icon.index);IconWidget_overCB(o.arrow.index)}function IconMenuWidgetOutCB(a){o=_allBOIconsMenus[a];if(!o.menu.isShown()){IconWidget_outCB(o.icon.index);IconWidget_outCB(o.arrow.index)}else{IconWidget_overCB(o.icon.index);IconWidget_overCB(o.arrow.index)}}function IconMenuWidget_iconClickCB(){var c=this.par;if(c.clickCB==null){var b=c.layer;var a=getPos2(b);c.menu.show(!c.menu.isShown(),a.x,a.y+c.getHeight()+1,null,null,c);IconMenuWidgetOverCB(c.index)}else{c.clickCB()}}function IconMenuWidget_arrowClickCB(){var c=this.par,b=c.layer;var a=getPos2(b);c.menu.show(!c.menu.isShown(),a.x,a.y+c.getHeight()+1,null,null,c);IconMenuWidgetOverCB(c.index)}function IconMenuWidget_hideCB(){var a=this.parIcon;if(a.arrow){a.arrow.focus()}IconMenuWidgetOutCB(a.index)}function newSingleIconMenuWidget(c,b,s,n,g,m,k,t,p,a,r,f,l){var q=_openMenu.replace("{0}",(n?n:(g?g:"")));var e=newIconWidget(c,b,SingleIconMenuWidget_clickCB,null,q,m,k,t,p,a,r,true);e.icon=newIconWidget("singleIconMenu_icon_"+c,b,null,n,null,m,k,t,p,a,r,false);e.arrow=newIconWidget("singleIcon_arrow_"+c,_skin+"menus.gif",SingleIconMenuWidget_iconClickCB,null,q,7,16,0,81,0,97,false);e.menu=newMenuWidget("singleIconMenu_menu_"+c,SingleIconMenuWidget_hideCB,l);e.icon.par=e;e.arrow.par=e;e.menu.parIcon=e;e.icon.margin=0;e.arrow.margin=0;e.icon.overCB="IconWidget_none";e.icon.outCB="IconWidget_none";e.arrow.overCB="IconWidget_none";e.arrow.outCB="IconWidget_none";e.margin=1;e.spc=0;e.getHTML=SingleIconMenuWidget_getHTML;e.getMenu=IconMenuWidget_getMenu;e.menIcnOldInit=e.init;e.init=SingleIconMenuWidget_init;e.removeAllMenuItems=IconMenuWidget_removeAllMenuItems;e.index=_allBOIconsMenus.length++;_allBOIconsMenus[e.index]=e;e.setDisabled=IconMenuWidget_setDisabled;e.isDisabled=IconMenuWidget_isDisabled;e.disableMenu=IconMenuWidget_disableMenu;e.changeText=IconMenuWidget_changeText;e.imwpResize=e.resize;e.resize=IconMenuWidget_resize;e.changeArrowTooltip=IconMenuWidget_changeArrowTooltip;e.disp=true;e.isDisplayed=IconWidget_isDisplayed;e.setDisplay=IconWidget_setDisplay;return e}function SingleIconMenuWidget_init(){var b=this;b.menIcnOldInit();b.menu.init();var a=b.layer;a.onmouseover=SingleIconMenuWidget_OverCB;a.onmouseout=SingleIconMenuWidget_OutCB}function SingleIconMenuWidget_getHTML(){var b=this,a=b.disp?"":"display:none;";return'<table id="'+b.id+'" cellspacing="0" cellpadding="0" border="0" style="'+a+"margin:"+b.margin+'px" role="button"><tr><td class="singleIconMenuL"></td><td class="singleIconMenuM">'+b.icon.getHTML()+'</td><td class="singleIconMenuM" style="padding-left:'+b.spc+'px" width="'+(13+b.spc)+'">'+b.arrow.getHTML()+'</td><td class="singleIconMenuR"></td></tr></table>'}function SingleIconMenuWidget_OverCB(){SingleIconMenuWidgetOverCB(getWidget(this).index);return true}function SingleIconMenuWidget_OutCB(){SingleIconMenuWidgetOutCB(getWidget(this).index)}function SingleIconMenuWidgetOverCB(a){o=_allBOIconsMenus[a];IconWidget_overCB(o.index)}function SingleIconMenuWidgetOutCB(a){o=_allBOIconsMenus[a];if(!o.menu.isShown()){IconWidget_outCB(o.index)}else{IconWidget_overCB(o.index)}}function SingleIconMenuWidget_clickCB(){var c=this,b=c.layer;var a=getPos2(b);c.menu.show(!c.menu.isShown(),a.x,a.y+c.getHeight()+1,null,null,c);SingleIconMenuWidgetOverCB(c.index)}function SingleIconMenuWidget_iconClickCB(){}function SingleIconMenuWidget_hideCB(){var a=this.parIcon;a.focus();SingleIconMenuWidgetOutCB(a.index)}function newIconCheckWidget(c,b,p,l,f,k,g,q,m,a,n){var e=newIconWidget(c,b,p,l,f,k,g,q,m,a,n);e.checked=false;e.internalUpCB=IconCheckWidget_internalUpCB;e.internalDownCB=IconCheckWidget_internalDownCB;e.check=IconCheckWidget_check;e.isChecked=IconCheckWidget_isChecked;e.oldInit=e.init;e.init=IconCheckWidget_init;e.isRadio=false;return e}function newPaletteContainerWidget(e,a,b){var c=newWidget(e);c.beginHTML=PaletteContainerWidget_beginHTML;c.endHTML=PaletteContainerWidget_endHTML;c.add=PaletteContainerWidget_add;c.palettes=new Array;c.contextMenu=a;c.margin=(b!=null)?b:4;return c}function newPaletteWidget(c,a){var b=newWidget(c);b.getHTML=PaletteWidget_getHTML;b.add=PaletteWidget_add;b.disableChildren=PaletteWidget_disableChildren;b.items=new Array;b.oldInit=b.init;b.init=PaletteWidget_init;b.beginRightZone=PaletteWidget_beginRightZone;b.height=a;b.rightZoneIndex=-1;b.sepCount=0;b.vertPadding=4;b.isLeftTableFixed=false;return b}function newPaletteVerticalSepWidget(b){var a=newWidget(b);a.getHTML=PaletteVerticalSepWidget_getHTML;a.isSeparator=true;return a}function PaletteVerticalSepWidget_getHTML(){return img(_skin+"iconsep.gif",6,22,null,' id="'+this.id+'" ')}function getPaletteSep(){return img(_skin+"iconsep.gif",6,22)}function IconRadioWidget_uncheckOthers(){var f=this.groupInstance,b=this.groupIdx,a=f.length;for(var e=0;e<a;e++){if(e!=b){var k=f[e];if(k){k.check(false)}}}}function PaletteWidget_beginRightZone(){this.rightZoneIndex=this.items.length}function PaletteSepWidget_getHTML(){return'<div style="background-image:url('+_skin+'sep.gif);height:2px;padding:0px;margin-top:0px;margin-bottom:0px;margin-left:4px;margin-right:4px">'+getSpace(1,2)+"</div>"}function PaletteContainerWidget_beginHTML(){var b=this;var a=b.contextMenu?('oncontextmenu="'+_codeWinName+'.PaletteContainerWidget_contextMenu(this,event);return false"'):"";return"<div "+a+'class="palette" style="overflow:hidden;margin:'+b.margin+'px;" id="'+b.id+'">'}_delayedMenu=null;function PaletteContainerWidget_contextMenu(b,a){if(_ie){a=_curWin.event}_delayedMenu=getWidget(b).contextMenu;setTimeout("_delayedMenu.par=null;_delayedMenu.show(true,"+absxpos(a)+","+absypos(a)+")",1)}function PaletteContainerWidget_endHTML(){return"</div>"}function PaletteContainerWidget_add(a){this.palettes[this.palettes.length]=a;return a}function PaletteWidget_getHTML(){var k=this,c=k.items,b=c.length,a=new Array;j=0;a[j++]='<table style="position:relative;overflow:hidden" id="'+k.id+'" '+attr("height",k.height)+' cellspacing="0" cellpadding="0" width="100%"><tbody><tr valign="middle">';a[j++]='<td width="100%" align="left" style="padding-left:'+k.vertPadding+'px;padding-right:4px"><table cellspacing="0" cellpadding="0"'+(k.isLeftTableFixed?'style="table-layout:fixed;width:100%"':"")+'><tbody><tr valign="middle">';var g=false;for(var e=0;e<b;e++){if(e==k.rightZoneIndex){a[j++]='</tr></tbody></table></td><td align="right" style="padding-right:'+k.vertPadding+'px"><table cellspacing="0" cellpadding="0"><tbody><tr valign="middle">';g=true}var f=c[e];a[j++]="<td>"+f.getHTML()+"</td>"}if(!g){a[j++]='</tr></tbody></table></td><td align="right" style="padding-right:4px"><table cellspacing="0" cellpadding="0"><tbody><tr valign="middle"><td></td>'}a[j++]="</tr></tbody></table></td></tr></tbody></table>";return a.join("")}function PaletteWidget_add(a){if(a==null){a=newPaletteVerticalSepWidget(this.id+"_palettesep_"+(this.sepCount++))}this.items[this.items.length]=a;return a}function PaletteWidget_disableChildren(b){var a=this.items;for(var c in a){var e=a[c];if(e&&(e.isSep!=true)){e.setDisabled(b)}}}function PaletteWidget_init(){this.oldInit();var a=this.items;for(var b in a){var c=a[b];if(c){c.init()}}}function IconWidget_isDisplayed(){return this.disp}function IconWidget_setDisplay(c){var b=this;if(b.css){var a=c?"block":"none";if(b.css.display!=a){b.css.display=a}}b.disp=c}function IconWidget_getTxtWidth(){var b=this,a=b.width;if(a!=null){a=a-(b.margin*2);a=a-(b.src?b.w+b.border:1);a=a-(b.txtNoPadding?0:((b.src?4:2)+2));if(_ie){a-=2}else{a-=2}return Math.max(0,a)}else{return -1}}function IconWidget_init(){var e=this,b=false;e.iconOldInit();var a=e.layer;a.tabIndex=e.dis?-1:0;a.title=(e.alt?e.alt:(e.text?e.text:""));if(e.clickCB){a.onclick=IconWidget_upCB;a.onmousedown=IconWidget_downCB;if(e.allowDblClick&&(_ie||_saf)){b=true;addDblClickCB(a,IconWidget_upCB)}a.onkeydown=IconWidget_keydownCB;a.onmouseover=IconWidget_realOverCB;a.onmouseout=IconWidget_realOutCB}if(!b){addDblClickCB(a,IconWidget_retFalse)}a.onselectstart=IconWidget_retFalse;var c=e.disp?"block":"none";if(e.css.display!=c){e.css.display=c}}function IconWidget_getHTML(){var f=this,b=f.src?'<div style="overflow:hidden;height:'+(f.h+f.border)+"px;width:"+(f.w+f.border)+"px;cursor:"+(f.clickCB?(!f.acceptClick()?"default":_hand):"default")+'">'+simpleImgOffset(f.src,f.w,f.h,f.dis?f.disDx:f.dx,f.dis?f.disDy:f.dy,"IconImg_"+f.id,null,f.alt,"margin:2px;")+f.extraHTML+"</div>":'<div class="iconText" style="width:1px;height:'+(f.h+f.border)+'px"></div>';var a='style="white-space:nowrap;',e=f.getTxtWidth();if(e>=0){a+="text-overflow:ellipsis;overflow:hidden;width:"+e+"px"}a+='"';var c=f.disp?"":"display:none;";return'<table style="'+c+"height:"+(f.h+f.border+(_moz?2:0))+"px;"+(f.width!=null?"width:"+f.width+"px;":"")+"margin:"+f.margin+'px" id="'+f.id+'"  class="'+f.nocheckClass+'" cellspacing="0" cellpadding="0" border="0" role="button" '+(f.ariaHasPopUp?'aria-haspopup="true"':"")+'><tr valign="middle"><td>'+((f.clickCB&&_ie)?lnk(b,null,null,null,' tabIndex="-1"'):b)+"</td>"+(f.text?'<td align="'+f.txtAlign+'" style="padding-left:'+(f.txtNoPadding?0:(f.src?4:2))+"px;padding-right:"+(f.txtNoPadding?0:2)+'px"><div id="IconImg_Txt_'+f.id+'" class="iconText'+(f.dis?"Dis":"")+'" '+a+">"+convStr(f.text)+"</div></td>":"")+"</tr></table>"}function IconWidget_realOutCB(){var o=getWidget(this);eval(o.outCB+"("+o.index+")")}function IconWidget_realOverCB(){var o=getWidget(this);eval(o.overCB+"("+o.index+")");return true}function IconWidget_retFalse(){return false}function IconWidget_resize(a,b){var e=this;if(e.layer){e.oldRes(a,b)}if(e.txtLayer==null){e.txtLayer=getLayer("IconImg_Txt_"+e.id)}if(a!=null){e.width=a;var c=e.getTxtWidth();if(e.txtLayer&&(c>=0)){e.txtLayer.style.width=""+c+"px"}}if(b!=null){e.h=b?(b-e.border):e.h;if(e.txtLayer&&(e.h>=0)){e.txtLayer.style.height=""+e.h+"px"}}}function IconWidget_changeTooltip(b,a){var c=this;if(b==null){return}if(!a){c.alt=b}if(c.layer){c.layer.title=b}if(c.imgLayer==null){c.imgLayer=getLayer("IconImg_"+this.id)}if(c.imgLayer){changeSimpleOffset(c.imgLayer,null,null,null,b)}}function IconWidget_changeText(a){var b=this;b.text=a;if(b.layer){if(b.txtLayer==null){b.txtLayer=getLayer("IconImg_Txt_"+b.id)}b.txtLayer.innerHTML=convStr(a)}}function IconWidget_changeImg(b,a,e){var c=this;if(e){c.src=e}if(b!=null){c.dx=b}if(a!=null){c.dy=a}if(c.layer&&(c.imgLayer==null)){c.imgLayer=getLayer("IconImg_"+this.id)}if(c.imgLayer){changeSimpleOffset(c.imgLayer,b,a,c.src)}}function IconWidget_internalDownCB(){if(!this.dis){this.currentHoverClass=this.checkhoverClass}}function IconWidget_internalUpCB(){if(!this.dis){this.currentHoverClass=this.hoverClass}}function IconWidget_setCrs(){var b=this,a=(b.clickCB?(!b.acceptClick()?"default":_hand):"default");b.css.cursor=a;if(b.src){if(b.imgLayer==null){b.imgLayer=getLayer("IconImg_"+b.id)}if(b.imgLayer){b.imgLayer.style.cursor=a}}}function IconWidget_downCB(){var a=getWidget(this);if((a.layer)&&(a.acceptClick())){a.internalDownCB();a.layer.className=a.currentHoverClass;if((a.par!=null&&a.par.menu==_globMenuCaptured)||(a!=null&&a.menu&&a.menu==_globMenuCaptured)){MenuWidget_releaseGlobMenuCaptured()}}if(_ie||_saf){return false}}function IconWidget_upCB(){var a=getWidget(this);if((a.layer)&&(a.acceptClick())){a.internalUpCB();a.layer.className=a.isHover?a.currentHoverClass:a.currentClass;a.setCrs();delayedClickCB(a.index)}}function IconWidget_keydownCB(a){if(eventGetKey(a)==13||eventGetKey(a)==32){var b=getWidget(this);if((b.layer)&&(b.acceptClick())){b.internalUpCB();b.layer.className=b.isHover?b.currentHoverClass:b.currentClass;b.setCrs();setTimeout("delayedClickCB("+b.index+")",1)}eventCancelBubble(a)}}function delayedClickCB(a){var b=_allBOIcons[a];if(b.beforeClickCB){b.beforeClickCB()}if(b.clickCB){b.clickCB()}}function IconWidget_overCB(a){var b=_allBOIcons[a];b.setCrs();if((b.layer)&&(!b.dis)&&!(b.par&&b.par.checked)){b.isHover=true;b.layer.className=b.currentHoverClass}}function IconWidget_outCB(a){var b=_allBOIcons[a];if((b.layer)&&(b.outEnable)&&!(b.par&&b.par.checked)){b.isHover=false;b.layer.className=b.currentClass}}function IconCheckWidget_init(){var a=this;a.oldInit();a.check(a.checked,true)}function IconCheckWidget_internalDownCB(){var a=this;if(a.acceptClick()){a.currentHoverClass=a.checked?a.hoverClass:a.checkhoverClass}}function IconCheckWidget_internalUpCB(){var a=this;if(a.acceptClick()){a.checked=a.isRadio?true:!a.checked;a.currentClass=a.checked?this.checkClass:this.nocheckClass;a.currentHoverClass=a.checked?this.checkhoverClass:this.hoverClass}}function IconCheckWidget_check(a,b){var c=this;if((c.checked!=a)||b){c.checked=a;if(c.layer){c.layer.className=c.currentClass=c.checked?this.checkClass:this.nocheckClass;c.currentHoverClass=c.checked?this.checkhoverClass:this.hoverClass}}if(c.checked&&c.beforeClickCB){if(c.layer){c.beforeClickCB()}}}function IconCheckWidget_isChecked(){return this.checked}function IconWidget_setClasses(b,a,c,e){var f=this;f.nocheckClass=b;f.checkClass=a;f.hoverClass=c;f.checkhoverClass=e;f.currentClass=f.nocheckClass;f.currentHoverClass=f.hoverClass}function IconWidget_setDisabled(a){var b=this;if(b.dis!=a){b.dis=a;if(b.layer){b.setCrs();if(b.src){if(b.imgLayer==null){b.imgLayer=getLayer("IconImg_"+this.id)}changeSimpleOffset(b.imgLayer,a?b.disDx:b.dx,a?b.disDy:b.dy)}if(b.text){if(b.txtLayer==null){b.txtLayer=getLayer("IconImg_Txt_"+b.id)}b.txtLayer.className="iconText"+(a?"Dis":"");if(a){b.layer.className=b.currentClass}}if(b.isTabEnabled){b.layer.tabIndex=b.dis?-1:0}}}}function IconWidget_isDisabled(){return this.dis?this.dis:false}function IconWidget_acceptClick(){var a=this;if(a.isDisabled()){return false}if(a.isRadio&&a.checked){return false}return true}function newCustomCombo(e,l,b,f,r,c,m,k,q,n,a,p){var g=newIconMenuWidget(e,c,null," ",r,m,k,q,n,a,p);g.icon.width=f!=null?Math.max(0,f-13):50-(2*g.margin);g.icon.setClasses("combonocheck","combocheck","combohover","combocheck");g.icon.clip;g.arrow.setClasses("iconnocheck","combobtnhover","combobtnhover","combobtnhover");g.spc=0;g.margin=2;if(c==null){g.icon.h=12;g.arrow.h=12;g.arrow.dy+=2;g.arrow.disDy+=2}g.counter=0;g.changeCB=l;g.selectedItem=null;g.setOldDid=g.setDisabled;g.disabled=false;g.ccomboOldInit=g.init;g.init=CustomCombo_init;g.add=CustomCombo_add;g.addSeparator=CustomCombo_addSeparator;g.addMenuItem=CustomCombo_addMenuItem;g.select=CustomCombo_select;g.getSelection=CustomCombo_getSelection;g.valueShow=CustomCombo_valueShow;g.valueSelect=CustomCombo_valueSelect;g.setUndefined=CustomCombo_setUndefined;g.setDisabled=CustomCombo_setDisabled;g.getVisibleItemsCount=CustomCombo_getVisibleItemsCount;g.selectItem=CustomCombo_selectItem;g.getItemByIndex=CustomCombo_getItemByIndex;g.getItemIndex=CustomCombo_getItemIndex;g.setItemDisabled=CustomCombo_setItemDisabled;return g}function CustomCombo_init(){var b=this;b.ccomboOldInit();if(b.disabled){b.icon.changeTooltip(b.icon.alt?b.icon.alt:"",true)}var a=_openMenu.replace("{0}",(b.icon.alt?b.icon.alt:""));b.arrow.changeTooltip(a)}function CustomCombo_add(b,f,a){var e=this;var c=e.menu.addCheck(e.id+"_it_"+(e.counter++),b,CustomCombo_internalCB);c.val=""+f;c.parCombo=e;c.isComboVal=true;if((e.selectedItem==null)||a){e.selectItem(c)}}function CustomCombo_addSeparator(){this.menu.addSeparator()}function CustomCombo_addMenuItem(b,g,c,f,m,k,e,a,l){this.menu.add(b,g,c,f,m,k,e,a,l)}function CustomCombo_internalCB(){var a=this,b=a.parCombo;b.selectItem(a);if(b.changeCB){b.changeCB()}}function CustomCombo_getItemByIndex(a){var b=this.menu.items;return((a>=0)&&(a<b.length))?b[a]:null}function CustomCombo_getItemIndex(g){var b=this.menu.items,a=b.length,c=0;for(var e=0;e<a;e++){var f=b[e];if(f.isComboVal){if(f.id==g.id){return c}c++}}return -1}function CustomCombo_selectItem(a){var b=this;if(b.selectedItem){b.selectedItem.check(false)}if(a){b.val=a.val;b.icon.changeText(b.disabled?"":a.text);b.selectedItem=a;a.check(true);if(b.disabled){b.icon.changeTooltip(b.icon.alt?b.icon.alt:"",true)}else{b.icon.changeTooltip(b.icon.alt?(b.icon.alt+" ("+a.text)+")":(a.text),true)}}else{b.val=null;b.icon.changeText("");b.icon.changeTooltip(b.icon.alt?b.icon.alt:"",true);b.selectedItem=null}}function CustomCombo_setDisabled(b){var a=this;if(a.selectedItem){a.icon.changeText(b?"":a.selectedItem.text)}a.disabled=b;a.setOldDid(b);if(b){a.icon.changeTooltip(a.icon.alt?a.icon.alt:"",true)}}function CustomCombo_select(a){var c=this,b=c.getItemByIndex(a);if(b){c.selectItem(b)}}function CustomCombo_setItemDisabled(a,b){var e=this,c=e.getItemByIndex(a);if(c){c.setDisabled(b)}}function CustomCombo_getSelection(){var b=this,a=b.selectedItem;if(a){return{index:b.getItemIndex(a),value:a.val}}else{return null}}function CustomCombo_valueSelect(c){c=""+c;var g=this,b=g.menu.items,a=b.length;for(var e=0;e<a;e++){var f=b[e];if((f.isComboVal)&&(f.val==c)&&(f.isShown)){g.selectItem(f);return true}}return false}function CustomCombo_valueShow(e,b){e=""+e;var k=this,c=k.menu.items,a=c.length;for(var f=0;f<a;f++){var g=c[f];if((g.isComboVal)&&(g.val==e)){g.show(b);return}}}function CustomCombo_setUndefined(a){var b=this;if(a){b.selectItem(null)}}function CustomCombo_getVisibleItemsCount(){var f=this,b=f.menu.items,a=b.length,g=0;for(var c=0;c<a;c++){var e=b[c];if((e.isComboVal)&&(e.isShown)){g++}}return g}function newComboTextFieldWidget(c,k,n,g,m,b,p,e,a,l){var f=newTextFieldWidget(c,k,n,g,m,b,p,e,a,l);f.par=null;f.oldInit=f.init;f.init=ComboTextFieldWidget_init;f.setContentEditable=ComboTextFieldWidget_setContentEditable;f.isContentEditable=ComboTextFieldWidget_isContentEditable;f.getHTML=ComboTextFieldWidget_getHTML;f.oldSetDisabled=f.setDisabled;f.setDisabled=ComboTextFieldWidget_setDisabled;return f}function ComboTextFieldWidget_init(){var b=this;b.oldInit();var a=b.layer;if(a!=null){b.setContentEditable(true);a.onclick=ComboTextFieldWidget_onClick}}function ComboTextFieldWidget_setContentEditable(c){var b=this,a=b.layer;b.contentEditable=c;if(a){if(_moz){a.readOnly=!c}else{a.contentEditable=c}a.style.cursor=c?"text":_hand;a.className=c?"comboEditable":"combo"}}function ComboTextFieldWidget_isContentEditable(){var a=this;return a.contentEditable}function ComboTextFieldWidget_onClick(){var a=getWidget(this);if(a.contentEditable){return}if(a.par!=null){a.par.clickCB()}}function ComboTextFieldWidget_getHTML(){var a=this;return"<input"+(a.disabled?" disabled":"")+' oncontextmenu="event.cancelBubble=true;return true" style="'+sty("width",this.width)+(_moz?"margin-top:1px;margin-bottom:1px;padding-left:5px;padding-right:2px;":"")+(_isQuirksMode?"height:20px;":"height:16px;")+"margin-left:"+(this.noMargin?0:10)+'px" onfocus="'+_codeWinName+'.TextFieldWidget_focus(this)" onblur="'+_codeWinName+'.TextFieldWidget_blur(this)" onchange="'+_codeWinName+'.TextFieldWidget_changeCB(event,this)" onkeydown=" return '+_codeWinName+'.TextFieldWidget_keyDownCB(event,this);" onkeyup=" return '+_codeWinName+'.TextFieldWidget_keyUpCB(event,this);" onkeypress=" return '+_codeWinName+'.TextFieldWidget_keyPressCB(event,this);" type="text" '+attr("maxLength",this.maxChar)+' ondragstart="event.cancelBubble=true;return true" onselectstart="event.cancelBubble=true;return true" class="combo" id="'+this.id+'" name="'+this.id+'"'+attr("title",this.tooltip)+' value="">'}function ComboTextFieldWidget_setDisabled(b){var a=this;a.oldSetDisabled(b)}function newTextComboWidget(a,l,n,k,f,b,g,m){var c=newWidget(a);c.text=newComboTextFieldWidget((m?m:"text_"+a),TextComboWidget_checkCB,l,null,TextComboWidget_enterCB,true,n,k-13);var e=_openMenu.replace("{0}",(n?n:""));c.arrow=newIconWidget("arrow_"+a,_skin+"menus.gif",TextComboWidget_arrowClickCB,null,e,7,16,0,81,0,97,true,true);c.menu=newMenuWidget("menu_"+a,TextComboWidget_hideCB,g);c.arrow.setClasses("iconnocheck","combobtnhover","combobtnhover","combobtnhover");c.text.par=c;c.arrow.par=c;c.menu.parIcon=c;c.arrow.margin=0;c.arrow.overCB="IconWidget_none";c.arrow.outCB="IconWidget_none";c.margin=0;c.spc=0;c.counter=0;c.arrow.h=12;c.arrow.dy+=2;c.arrow.disDy+=2;c.index=_allBOIconsMenus.length++;_allBOIconsMenus[c.index]=c;c.menIcnOldInit=c.init;c.init=TextComboWidget_init;c.imwpResize=c.resize;c.resize=TextComboWidget_resize;c.getHTML=TextComboWidget_getHTML;c.setDisabled=TextComboWidget_setDisabled;c.isDisabled=TextComboWidget_isDisabled;c.add=TextComboWidget_add;c.addSeparator=TextComboWidget_addSeparator;c.addMenuItem=TextComboWidget_addMenuItem;c.select=TextComboWidget_select;c.getSelection=TextComboWidget_getSelection;c.valueShow=TextComboWidget_valueShow;c.valueSelect=TextComboWidget_valueSelect;c.setUndefined=TextComboWidget_setUndefined;c.setContentEditable=TextComboWidget_setContentEditable;c.isContentEditable=TextComboWidget_isContentEditable;c.changeCB=f;c.checkCB=b;c.clickCB=TextComboWidget_clickCB;c.selectItem=TextComboWidget_selectItem;c.getItemByIndex=TextComboWidget_getItemByIndex;c.getItemIndex=TextComboWidget_getItemIndex;c.setItemDisabled=TextComboWidget_setItemDisabled;c.text.enterCancelBubble=false;return c}function TextComboWidget_init(){var b=this;b.menIcnOldInit();b.text.init();b.arrow.init();b.menu.init();var a=b.layer;a.onmouseover=TextCombo_OverCB;a.onmouseout=TextCombo_OutCB}function TextComboWidget_getHTML(){var b=this,a="";a+='<table id="'+b.id+'" cellspacing="0" cellpadding="0" border="0" style="cursor:default;margin:'+b.margin+'px"><tbody><tr>';a+="<td>"+b.text.getHTML()+"</td>";a+='<td style="padding-left:'+b.spc+'px" width="'+(13+b.spc)+'">'+b.arrow.getHTML()+"</td>";a+="</tr></tbody></table>";return a}function TextComboWidget_resize(a,b){var e=this;if(a!=null){a=Math.max(0,a-2*e.margin)}var c=e.layer.display!="none";if(c&_moz&&!_saf){e.setDisplay(false)}e.imwpResize(a,b);if(c&_moz&&!_saf){e.setDisplay(true)}}function TextComboWidget_add(b,f,a){var e=this;var c=e.menu.addCheck(e.id+"_it_"+(e.counter++),b,TextComboWidget_internalCB);c.val=""+f;c.parCombo=e;c.isComboVal=true;if((e.selectedItem==null)||a){e.selectItem(c)}}function TextComboWidget_addSeparator(){this.menu.addSeparator()}function TextComboWidget_addMenuItem(b,g,c,f,m,k,e,a,l){this.menu.add(b,g,c,f,m,k,e,a,l)}function TextComboWidget_setDisabled(b){var a=this;a.text.setDisabled(b);a.arrow.setDisabled(b);a.menu.setDisabled(b);a.disabled=b}function TextComboWidget_isDisabled(){var a=this;return a.disabled}function TextComboWidget_select(a){var c=this,b=c.getItemByIndex(a);if(b){c.selectItem(b)}}function TextComboWidget_setItemDisabled(a,b){var e=this,c=e.getItemByIndex(a);if(c){c.setDisabled(b)}}function TextComboWidget_getSelection(){var c=this,b=c.selectedItem;var a=c.text.getValue();if(b){return{index:c.getItemIndex(b),value:b.val}}else{return{index:-1,value:a}}}function TextComboWidget_valueSelect(c){c=""+c;var g=this,b=g.menu.items,a=b.length;for(var e=0;e<a;e++){var f=b[e];if((f.isComboVal)&&(f.val==c)){g.selectItem(f);return}}g.text.setValue(c)}function TextComboWidget_valueShow(e,b){e=""+e;var k=this,c=k.menu.items,a=c.length;for(var f=0;f<a;f++){var g=c[f];if((g.isComboVal)&&(g.val==e)){g.show(b);return}}k.text.setValue(e);k.text.show(b)}function TextComboWidget_setUndefined(a){var b=this;if(a){b.selectItem(null)}}function TextComboWidget_setContentEditable(b){var a=this;a.text.setContentEditable(b)}function TextComboWidget_isContentEditable(){var a=this;return a.text.isContentEditable()}function TextComboWidget_selectItem(a){var b=this;if(b.selectedItem){b.selectedItem.check(false)}if(a){b.val=a.val;b.text.setValue(a.text);b.selectedItem=a;a.check(true)}else{b.val=null;b.text.setValue("");b.selectedItem=null}}function TextComboWidget_getItemByIndex(a){var b=this.menu.items;return((a>=0)&&(a<b.length))?b[a]:null}function TextComboWidget_getItemIndex(g){var b=this.menu.items,a=b.length,c=0;for(var e=0;e<a;e++){var f=b[e];if(f.isComboVal){if(f.id==g.id){return c}c++}}return -1}function TextComboWidget_changeCB(){var c=this.par;var a=true;if(c.checkCB){a=c.checkCB()}if(!a){return}if(c.changeCB){c.changeCB()}}function TextComboWidget_enterCB(){var c=this.par;if(c.selectedItem){c.selectedItem.check(false);c.selectedItem=null}var a=true;if(c.checkCB){a=c.checkCB()}if(!a){return}if(c.changeCB){c.changeCB()}}function TextComboWidget_checkCB(){var a=this.par;if(a.checkCB){a.checkCB()}}function TextComboWidget_hideCB(){var a=this.parIcon;if(a.arrow){a.arrow.focus()}TextComboOutCB(a.index)}function TextComboWidget_arrowClickCB(){this.par.clickCB()}function TextComboWidget_clickCB(){var b=this,a=b.layer;b.menu.show(!b.menu.isShown(),getPosScrolled(a).x,getPosScrolled(a).y+b.getHeight()+1,null,null,b);TextComboOverCB(b.index)}function TextCombo_OverCB(){TextComboOverCB(getWidget(this).index);return true}function TextComboOverCB(a){var b=_allBOIconsMenus[a];IconWidget_overCB(b.arrow.index)}function TextCombo_OutCB(a){TextComboOutCB(getWidget(this).index)}function TextComboOutCB(a){var b=_allBOIconsMenus[a];if(!b.menu.isShown()){IconWidget_outCB(b.arrow.index)}else{IconWidget_overCB(b.arrow.index)}}function TextComboWidget_internalCB(){var a=this,b=a.parCombo;b.selectItem(a);if(b.changeCB){b.changeCB()}}function TextComboWidget_keyUpCB(){}_menusZIndex=2000;_menusItems=new Array;_globMenuCaptured=null;_isColor=0;_isLastUsedColor=1;_isNotColor=2;_currentFocus=null;_mitemH=22;function newMenuWidget(e,a,c){var b=newWidget(e);b.items=new Array;b.par=null;b.container=null;b.currentSub=-1;b.nextSub=-1;b.zIndex=_menusZIndex;b.hideCB=a;b.beforeShowCB=c;b.accelEnabled=true;b.init=MenuWidget_init;b.justInTimeInit=MenuWidget_justInTimeInit;b.getHTML=MenuWidget_getHTML;b.show=MenuWidget_show;b.setAccelEnabled=MenuWidget_setAccelEnabled;b.isAccelEnabled=MenuWidget_isAccelEnabled;b.internalAdd=b.add=MenuWidget_add;b.addCheck=MenuWidget_addCheck;b.addSeparator=MenuWidget_addSeparator;b.insert=MenuWidget_insert;b.insertCheck=MenuWidget_insertCheck;b.insertSeparator=MenuWidget_insertSeparator;b.getItem=MenuWidget_getItem;b.getItemByID=MenuWidget_getItemByID;b.isShown=MenuWidget_isShown;b.remove=MenuWidget_remove;b.removeAll=MenuWidget_removeAll;b.removeByID=MenuWidget_removeByID;b.resetItemCount=MenuWidget_resetItemCount;b.resetTooltips=MenuWidget_resetTooltips;b.showSub=MenuWidget_showSub;b.captureClicks=MenuWidget_captureClicks;b.releaseClicks=MenuWidget_releaseClicks;b.focus=MenuWidget_focus;b.restoreFocus=MenuWidget_restoreFocus;b.hasVisibleItem=MenuWidget_hasVisibleItem;b.updateIndex=MenuWidget_updateIndex;b.getTotalNumItems=MenuWidget_getTotalNumItems;b.clickCB=new Array;b.clickCBDocs=new Array;b.write=MenuWidget_write;b.alignLeft=false;b.sepCount=0;b.itemCount=0;return b}function MenuWidget_captureClicks(c){var g=this;if(g.par==null){if(c==null){_globMenuCaptured=g;g.clickCB.length=0;g.clickCBDocs.length=0;c=_curWin}if(canScanFrames(c)){if(_moz){_oldErrHandler=window.onerror;window.onerror=localErrHandler}try{d=c.document;g.clickCB[g.clickCB.length]=d.onmousedown;g.clickCBDocs[g.clickCBDocs.length]=d;d.onmousedown=MenuWidget_globalClick;var b=c.frames,a=b.length;for(var e=0;e<a;e++){g.captureClicks(b[e])}}catch(f){}if(_moz){window.onerror=_oldErrHandler}}}}function MenuWidget_releaseClicks(){var e=this;if(e.par==null){var a=e.clickCB.length;for(var b=0;b<a;b++){try{e.clickCBDocs[b].onmousedown=e.clickCB[b]}catch(c){}e.clickCB[b]=null;e.clickCBDocs[b]=null}e.clickCB.length=0;e.clickCBDocs.length=0}}_menuItem=null;function MenuWidget_focus(){var e=this,b=e.items,a=b.length;for(var c=0;c<a;c++){if(b[c].isShown&&!b[c].isSeparator){_menuItem=b[c];setTimeout("_menuItem.focus()",1);if(e.endLink){e.endLink.show(true)}if(e.startLink){e.startLink.show(true)}break}}}function MenuWidget_keepFocus(b){var a=getWidget(getLayer(b));if(a){a.focus()}}function MenuWidget_restoreFocus(){var a=this;if(a.endLink){a.endLink.show(false)}if(a.startLink){a.startLink.show(false)}if(a.parIcon){a.parIcon.focus()}else{if(a.par){a.par.focus()}else{if(a.parCalendar){a.parCalendar.focus()}}}}function MenuWidget_keyDown(f,b){var c=getWidget(getLayer(f));var a=eventGetKey(b);if(a==27&&c){c.restoreFocus();c.show(false);if(c.par&&c.par.par){c.par.par.currentSub=-1}c.currentSub=-1;eventCancelBubble(b)}else{if(c&&(a==109||a==37)){if(c.par&&c.par.par){c.restoreFocus();c.show(false);c.par.par.currentSub=-1;c.currentSub=-1}}else{if(a==13){eventCancelBubble(b)}}}}function MenuWidget_releaseGlobMenuCaptured(){var a=_globMenuCaptured;if(a!=null){a.releaseClicks();_globMenuCaptured=null}}function MenuWidget_globalClick(){var a=_globMenuCaptured;if(a!=null){MenuWidget_releaseGlobMenuCaptured();a.show(false)}}function MenuWidget_add(b,p,e,n,s,q,f,a,r,k){var c=this,g=c.items.length,l=null;if(b.substr(0,9)!="_menusep_"){c.itemCount++;l=c.itemCount}var m=c.items[g]=newMenuItem(c,b,p,e,l,n,s,q,f,a,r,false,k);m.menuIndex=g;m.dynHTML();return m}function MenuWidget_addCheck(b,p,e,n,s,q,f,a,r,k){var c=this,g=c.items.length,l=null;if(b.substr(0,9)!="_menusep_"){c.itemCount++;l=c.itemCount}var m=c.items[g]=newMenuItem(c,b,p,e,l,n,s,q,f,a,r,true,k);m.menuIndex=g;m.dynHTML();return m}function MenuWidget_addSeparator(){var a=this.internalAdd("_menusep_"+(this.sepCount++));a.isSeparator=true;return a}function MenuWidget_insert(m,b,p,e,n,t,r,f,a,s,k){var c=this,g=c.items.length,l=null;if(b.substr(0,9)!="_menusep_"){c.itemCount++;l=c.itemCount}var q=newMenuItem(c,b,p,e,l,n,t,r,f,a,s,false,k);arrayAdd(c,"items",q,m);c.updateIndex();q.dynHTML();return q}function MenuWidget_insertCheck(m,b,p,e,n,t,r,f,a,s,k){var c=this,g=c.items.length,l=null;if(b.substr(0,9)!="_menusep_"){c.itemCount++;l=c.itemCount}var q=newMenuItem(c,b,p,e,l,n,t,r,f,a,s,true,k);arrayAdd(c,"items",q,m);c.updateIndex();q.dynHTML();return q}function MenuWidget_insertSeparator(a){var b=newMenuItem(this,"_menusep_"+(this.sepCount++));b.isSeparator=true;arrayAdd(this,"items",b,a);this.updateIndex();b.dynHTML();return b}function MenuWidget_init(){}function MenuWidget_getItem(b){var c=this,a=c.items;if((b>=0)&&(b<a.length)){return a[b]}return null}function MenuWidget_getItemByID(e){var c=this,a=c.items;for(var b in a){if(a[b].id==e){return a[b]}}return null}function MenuWidget_removeByID(e){var c=this;var b=c.getItemByID(e);if(b){arrayRemove(c,"items",b.menuIndex);c.updateIndex();if(c.layer==null){return}var a=c.layer.childNodes[0];a.deleteRow(b.menuIndex)}}function MenuWidget_removeAll(){this.remove()}function MenuWidget_remove(b){var c=this;if(b!=null){arrayRemove(c,"items",b);c.updateIndex()}else{c.items.length=0}if(c.layer==null){return}var a=c.layer.childNodes[0];if(b!=null){a.deleteRow(b)}else{while(a.firstChild){a.removeChild(a.firstChild)}}}function MenuWidget_updateIndex(){var b=this.items,a=b.length;for(var c=0;c<a;c++){b[c].menuIndex=c}}function MenuWidget_showSub(){var b=this;if(b.nextSub!=-1){if(b.nextSub!=b.currentSub){var l=b.items[b.currentSub];if(l&&l.sub){l.sub.show(false);b.currentSub=-1}var a=b.items[b.nextSub];if(a&&a.sub){var e=a.layer;var g=parseInt(b.css.left);var f=parseInt(b.css.top);for(var c=0;c<b.nextSub;c++){var m=b.items[c];if(m.isShown){if((m.icon!=null)||(m.text!=null)){f+=_mitemH}else{f+=3}}}var k=b.getWidth();g=g+k-4;a.attachSubMenu(a.sub);a.sub.show(true,g,f,false,k);b.currentSub=b.nextSub}}}else{if(b.currentSub!=-1){var l=b.items[b.currentSub];if(l&&l.sub){l.sub.show(false);b.currentSub=-1}}}}function MenuWidget_write(){}function MenuWidget_justInTimeInit(){var c=this;c.layer=getLayer(c.id);if(c.layer==null){targetApp(c.getHTML());c.layer=getLayer(c.id)}c.layer._widget=c.widx;c.css=c.layer.style;c.endLink=newWidget("endLink_"+c.id);c.endLink.init();c.startLink=newWidget("startLink_"+c.id);c.startLink.init();var a=c.items;for(var b in a){a[b].init()}}function MenuWidget_getHTML(){var g=this,c=g.items;var a=' onkeydown="'+_codeWinName+".MenuWidget_keyDown('"+g.id+"',event);return true\" ";var f='<a style="position:absolute;left:-30px;top:-30px; visibility:hidden" id="startLink_'+g.id+'" href="javascript:void(0)" onfocus="'+_codeWinName+".MenuWidget_keepFocus('"+g.id+'\');return false;" ></a><table style="display:none;" class="menuFrame" id="'+g.id+'" cellspacing="0" cellpadding="0" border="0" '+a+' dir="ltr" role="menu"><tbody><tr><td><table cellspacing="0" cellpadding="0" border="0"><tbody>';for(var e=0,b=c.length;e<b;e++){c[e].needsRightPart=g.accelEnabled;f+=c[e].getHTML()}f+='</tbody></td></tr></tbody></table><a style="position:absolute;left:-30px;top:-30px; visibility:hidden" id="endLink_'+g.id+'" href="javascript:void(0)" onfocus="'+_codeWinName+".MenuWidget_keepFocus('"+g.id+"');return false;\" ></a>";return f}function MenuWidget_show(q,n,l,f,c,m){var b=this;if(b.layer==null){b.justInTimeInit()}var g=b.css;if(q){b.iframeLyr=getDynamicBGIFrameLayer();b.iframeCss=b.iframeLyr.style;if(b.beforeShowCB){b.beforeShowCB()}if(!b.hasVisibleItem()){return}b.captureClicks();g.display="block";g.zIndex=(b.zIndex+1);g.visibility="hidden";g.left="-1000px";g.top="-1000px";var p=b.getWidth();var e=b.getHeight();if(b.alignLeft){n-=p}if(m){var r=m.getWidth();if(r>p){n=n+r-p}}var a=n+p+4,k=l+e+4;if(a-getScrollX()>winWidth()){if(m){n=Math.max(0,winWidth()-p)}else{n=Math.max(0,n-4-(p+((c!=null)?c-12:0)))}}if(k-getScrollY()>winHeight()){l=Math.max(0,l-4-e+(c!=null?30:0))}g.left=""+n+"px";g.top=""+l+"px";g.visibility="visible";iCss=b.iframeCss;iCss.left=""+n+"px";iCss.top=""+l+"px";iCss.width=""+p+"px";iCss.height=""+e+"px";iCss.zIndex=b.zIndex-1;iCss.display="block";if(_ie){l-=2;n-=2}b.nextSub=-1;b.showSub();b.focus()}else{if(f&&b.par&&b.par.par){b.par.par.show(q,n,l,f)}if(b.iframeLyr){releaseBGIFrame(b.iframeLyr.id)}g.display="none";if(b.iframeCss){b.iframeCss.display="none"}b.nextSub=-1;b.showSub();if(b.hideCB){b.hideCB()}b.releaseClicks()}}function MenuWidget_setAccelEnabled(a){var b=this;b.accelEnabled=a}function MenuWidget_isAccelEnabled(a){var b=this;return b.accelEnabled}function MenuWidget_isShown(){var a=this;if(a.layer==null){return false}else{return(a.css.display=="block")}}function MenuWidget_hasVisibleItem(){var e=this;if(e.isMenuColor||e.isCalendar){return true}var a=e.items;for(var b in a){var c=a[b];if(c&&!(c.isSeparator==true)&&c.isShown){return true}}return false}function MenuWidget_getTotalNumItems(){var b=this,a=b.items;return a.length-b.sepCount}function MenuWidget_resetItemCount(){var a=this;a.itemCount=0}function MenuWidget_resetTooltips(){var a=this;a.resetItemCount();len=a.items.length;for(i=0;i<len;i++){a.items[i].updateTooltip()}}function newMenuItem(m,c,p,f,l,n,s,q,g,b,r,a,k){var e=new Object;e.par=m;e.id=c;e.text=p;e.cb=f;e.itemNo=l;e.icon=n;e.dx=(s==null)?0:s;e.dy=(q==null)?0:q;e.disDx=(b==null)?e.dx:b;e.disDy=(r==null)?e.dy:r;e.sub=null;e.layer=null;e.iconTDLayer=null;e.iconLayer=null;e.textLayer=null;e.textOnlyLayer=null;e.accel=null;e.accelLayer=null;e.hasNoLayer=false;e.isSeparator=false;e.disabled=(g!=null)?g:false;e.isShown=true;e.alt=k;e.needsRightPart=true;e.index=_menusItems.length;_menusItems[e.index]=e;e.menuIndex=-1;e.isCheck=a;e.checked=false;e.menuItemType=_isNotColor;e.init=MenuItem_init;e.leftZoneClass="menuLeftPart";e.leftZoneSelClass="menuLeftPartSel";e.totalNumItems=null;e.attachSubMenu=MenuItem_attachSubMenu;e.getHTML=MenuItem_getHTML;e.getHTMLPart=MenuItem_getHTMLPart;e.dynHTML=MenuItem_dynHTML;e.setDisabled=MenuItem_setDisabled;e.check=MenuItem_check;e.isChecked=MenuItem_isChecked;e.show=MenuItem_show;e.setText=MenuItem_setText;e.setIcon=MenuItem_setIcon;e.setAccelerator=MenuItem_setAccelerator;e.focus=MenuItem_focus;e.setTextClass=MenuItem_setTextClass;e.updateTooltip=MenuItem_updateTooltip;return e}function MenuItem_setTextClass(a){var b=this;if(b.textOnlyLayer){b.textOnlyLayer.className=a}}function MenuItem_init(){if(!this.hasNoLayer){var a=this,b=a.par.id;a.layer=getLayer(b+"_item_"+a.id);a.layer._boIndex=a.index;if(!a.isSeparator){if((a.icon!=null)||(a.isCheck)){a.iconLayer=getLayer(b+"_item_icon_"+a.id);a.iconTDLayer=getLayer(b+"_item_td_"+a.id)}a.textLayer=getLayer(b+"_text_"+a.id);a.textOnlyLayer=getLayer(b+"_span_text_"+a.id);a.hiddenLabelLayer=getLayer(b+"_hiddenLabel_"+a.id);a.accelLayer=getLayer(b+"_accel_"+a.id);if(!a.isCheck){a.updateTooltip()}}if(a.isCheck){a.check(a.checked,true)}}}function MenuItem_attachSubMenu(c){var b=this;b.sub=c;c.par=b;c.zIndex=b.par.zIndex+2;if(b.layer){if(b.arrowLayer==null){b.arrowLayer=getLayer(b.par.id+"_item_arrow_"+b.id)}var a=b.disabled;changeSimpleOffset(b.arrowLayer,a?7:0,a?81:64)}return c}function MenuItem_check(a,c){var e=this;if((e.checked!=a)||c){e.checked=a;if(e.par.layer){var b=e.layer;if(b){if(e.icon==null){changeSimpleOffset(e.iconLayer,0,(e.checked?48:0),null,(e.checked?_menuCheckLab:""))}changeOffset(e.iconTDLayer,0,(e.checked?0:0));if(e.checkFrame==null){e.checkFrame=getLayer(e.par.id+"_item_check_"+e.id)}e.checkFrame.className="menuIcon"+(e.checked?"Check":"");e.updateTooltip()}}}}function MenuItem_setDisabled(a){var c=this;if(c.disabled!=a){c.disabled=a;if(c.par.layer){var b=c.layer;if(b){b.style.cursor=a?"default":_hand;if(c.icon){changeSimpleOffset(c.iconLayer,a?c.disDx:c.dx,a?c.disDy:c.dy)}var e="menuTextPart"+(c.disabled?"Disabled":"");if(e!=c.textLayer.className){c.textLayer.className=e}if(c.accel&&(e!=c.accelLayer.className)){c.accelLayer.className=e}if(c.sub){if(c.arrowLayer==null){c.arrowLayer=getLayer(c.par.id+"_item_arrow_"+c.id)}changeSimpleOffset(c.arrowLayer,a?7:0,a?81:64)}c.updateTooltip()}}}}function _mii(g,k){var m=g.childNodes,n=0,l=m.length,p=g._boIndex;var b=_menusItems[p];if(b.disabled){k=0}else{if(k){b.par.nextSub=b.menuIndex;MenuItem_callShowSub(p,true);if(b.par.par){if(b.par.par.par){b.par.par.par.nextSub=b.par.par.menuIndex}}}}var e=0;for(var f=0;f<l;f++){var a=m[f];if(a.tagName!=null){if(e==0){a.className=k?b.leftZoneSelClass:b.leftZoneClass}else{if(e==1){a.className="menuTextPart"+(k?"Sel":"")+(b.disabled?"Disabled":"")}else{if(b.accel&&(e==2)){a.className="menuTextPart"+(k?"Sel":"")+(b.disabled?"Disabled":"");break}else{a.className="menuRightPart"+(k?"Sel":"")}}}e++}}}function MenuItem_getHTMLPart(c){var k=this;switch(c){case 0:var b=null,f=' class="menuIcon'+(k.checked?"Check":"")+'"';if(k.isCheck&&(k.icon==null)){b=simpleImgOffset(_skin+"menus.gif",16,16,0,k.checked?48:0,(k.par.id+"_item_icon_"+k.id),null,(k.checked?_menuCheckLab:""))}else{b=k.icon?simpleImgOffset(k.icon,16,16,k.disabled?k.disDx:k.dx,k.disabled?k.disDy:k.dy,(k.par.id+"_item_icon_"+k.id),null,k.alt?k.alt:""):(getSpace(16,16))}if(k.isCheck){b='<div id="'+k.par.id+"_item_check_"+k.id+'" class="menuIcon'+(k.checked?"Check":"")+'" style="width:16px;height:16px;padding:2px">'+b+"</div>"}return b;case 1:var e=(k.par.id+"_span_text_"+k.id);var a=' onkeydown="'+_codeWinName+'._mikd(this, event);return true" ';var g='<label for="'+e+'" id="'+(k.par.id+"_hiddenLabel_"+k.id)+'" style="display:none;" ></label>';return'<span id="'+e+'" '+a+' tabIndex="0" role="menuitem">'+convStr(k.text)+"</span>"+g;case 2:return simpleImgOffset(_skin+"menus.gif",16,16,k.sub?(k.disabled?7:0):0,k.sub?(k.disabled?81:64):0,k.par.id+"_item_arrow_"+k.id,null,null,null,"right");case 3:return'<table width="100%" height="3" cellpadding="0" cellspacing="0" border="0" style="'+backImgOffset(_skin+"menus.gif",0,80)+';"><tbody><tr><td></td></tr></tbody></table>';case 4:return convStr(k.accel)}}function MenuItem_getHTML(){var f=this;if((f.icon!=null)||(f.text!=null)){var e=' onclick="'+_codeWinName+'._micl(this,event);return true" oncontextmenu="'+_codeWinName+'._micl(this,event);return false" onmouseover="'+_codeWinName+'._mii(this,1)" onmouseout="'+_codeWinName+'._mii(this,0);" ';var a=' onkeydown="'+_codeWinName+'._mikd(this,event);return true" ';var b=new Array(),c=0;b[c++]='<tr onmousedown="'+_codeWinName+'._minb(event)" onmouseup="'+_codeWinName+'._minb(event)" id="'+(f.par.id+"_item_"+f.id)+'" style="'+(!f.isShown?"display:none;":"")+"height:"+_mitemH+"px;width:24px;cursor:"+(f.disabled?"default":_hand)+'" '+e+a+' valign="middle">';b[c++]='<td id="'+(f.par.id+"_item_td_"+f.id)+'" style="width:23px;height:'+_mitemH+'px;" align="center" class="'+f.leftZoneClass+'">';b[c++]=f.getHTMLPart(0);b[c++]="</td>";b[c++]="<td "+(f.centered?' align="center" ':"")+' style="height:'+_mitemH+'px" id="'+(f.par.id+"_text_"+f.id)+'" class="menuTextPart'+(f.disabled?"Disabled":"")+'">';b[c++]=f.getHTMLPart(1);b[c++]="</td>";if(f.needsRightPart){if(f.accel!=null){b[c++]='<td class="menuTextPart'+(f.disabled?"Disabled":"")+'" id="'+(f.par.id+"_accel_"+f.id)+'" align="right" style="height:'+_mitemH+'px"  tabIndex="-1">';b[c++]=f.getHTMLPart(4);b[c++]="</td>"}else{b[c++]='<td class="menuRightPart" align="right" style="width:40px;height:'+_mitemH+'px;" >';b[c++]=f.getHTMLPart(2);b[c++]="</td>"}}else{b[c++]='<td class="menuRightPart" align="right" style="width:2px;height:'+_mitemH+'px;" >';b[c++]=img(_skin+"../transp.gif",1,1,null,null,null);b[c++]="</td>"}b[c++]="</tr>";return b.join("")}else{return'<tr onmousedown="'+_codeWinName+'._minb(event)" onclick="'+_codeWinName+'._minb(event)" id="'+(f.par.id+"_item_"+f.id)+'" onmouseup="'+_codeWinName+'._minb(event)" style="height:3px"><td class="'+f.leftZoneClass+'" style="width:24px;height:3px;border:0px"></td><td colspan="2" style="padding-left:5px;padding-right:5px;border:0px">'+f.getHTMLPart(3)+"</td></tr>"}}function MenuItem_dynHTML(){var a=this;if(a.par.layer==null){return}var g=a.par.layer.childNodes[0],m=g.insertRow(a.menuIndex),n=m.style;m.onmousedown=_minb;m.onmouseup=_minb;m.id=(a.par.id+"_item_"+a.id);if((a.icon!=null)||(a.text!=null)){var e=m.insertCell(0),c=m.insertCell(1),b=m.insertCell(2),l=e.style,k=c.style,f=b.style;m.onclick=MenuItem_clickCallTrue;m.oncontextmenu=MenuItem_clickCallFalse;m.onmouseover=MenuItem_invertCall1;m.onmouseout=MenuItem_invertCall0;n.height=""+_mitemH+"px";n.width="24px";n.cursor=(a.disabled?"default":_hand);e.id=(a.par.id+"_item_td_"+a.id);l.width="23px";l.height=""+_mitemH+"px";e.innerHTML=a.getHTMLPart(0);e.align="center";e.className=a.leftZoneClass;if(a.centered){c.align="center"}k.height=""+_mitemH+"px";c.id=(a.par.id+"_text_"+a.id);c.className="menuTextPart"+(a.disabled?"Disabled":"");c.innerHTML=a.getHTMLPart(1);if(a.accel){b.className="menuTextPart"+(a.disabled?"Disabled":"");b.align="right";f.height=""+_mitemH+"px";b.innerHTML=a.getHTMLPart(4)}else{b.className="menuRightPart";b.align="right";f.width="40px";f.height=""+_mitemH+"px";b.innerHTML=a.getHTMLPart(2)}a.init()}else{m.onclick=_minb;m.style.height="3px";var e=m.insertCell(0),c=m.insertCell(1),l=e.style,k=c.style;e.className=a.leftZoneClass;l.width="24px";l.height="3px";l.border="0px";c.colSpan="2";k.paddingLeft="5px";k.paddingRight="5px";c.innerHTML=a.getHTMLPart(3)}}function MenuItem_isChecked(){return this.checked}function MenuItem_setText(a){var b=this,c=b.par.id;b.text=a;if(b.textLayer){b.textLayer.innerHTML=b.getHTMLPart(1);b.textOnlyLayer=getLayer(c+"_span_text_"+b.id)}}function MenuItem_setAccelerator(b,a){var c=this,e=c.par.id;c.accel=((a!=null)?_modifiers[a]:"")+b;if(c.accelLayer){c.accelLayer.innerHTML=c.getHTMLPart(4)}}function MenuItem_setIcon(b,a,f,e,c){var g=this;g.url=c?c:g.url;g.dx=(b!=null)?b:g.dx;g.dy=(a!=null)?a:g.dy;g.disDx=(f!=null)?f:g.disDx;g.disDy=(e!=null)?e:g.disDy;if(g.icon&&g.iconLayer){changeSimpleOffset(g.iconLayer,g.disabled?g.disDx:g.dx,g.disabled?g.disDy:g.dy,g.url)}}function MenuItem_show(a){var b=this;b.isShown=a;if(b.layer!=null){b.layer.style.display=a?"":"none"}}function _micl(b,c){eventCancelBubble(c);var a=b._boIndex,f=_menusItems[a];f.layer=b;if(!f.disabled){if(f.sub){f.par.nextSub=f.menuIndex;MenuItem_callShowSub(a)}else{f.par.show(false,0,0,true);if(f.isCheck){if(f.par.uncheckAll){f.par.uncheckAll()}f.check(!f.checked)}if(f.par.container&&f.par.container.updateButton){f.par.container.updateButton(a)}_mii(b,0,a);f.par.nextSub=-1;if(f.cb){setTimeout("MenuItem_delayedClick("+a+")",1)}}}}function _mikd(g,m){while(g&&!g._boIndex){g=g.parentNode}if(!g||!g._boIndex){return}var b=g._boIndex,n=_menusItems[b];n.layer=g;var f=eventGetKey(m);switch(f){case 32:case 13:_micl(g,m);break;case 107:case 39:if(!n.disabled&&n.sub){_micl(g,m)}break;case 109:case 37:break;case 40:var c=n.par.items,a=c.length;for(var l=n.menuIndex+1;l<a;l++){if(c[l].isShown&&!c[l].isSeparator){c[l].focus();break}}break;case 38:var c=n.par.items,a=c.length;for(var l=n.menuIndex-1;l>=0;l--){if(c[l].isShown&&!c[l].isSeparator){c[l].focus();break}}break}}function MenuItem_callShowSub(a,b){var c=_menusItems[a];if(b){setTimeout("MenuItem_delayedShowSub("+a+")",500)}else{MenuItem_delayedShowSub(a)}}function MenuItem_delayedShowSub(a){var b=_menusItems[a];b.par.showSub()}function _minb(a){eventCancelBubble(a)}function MenuItem_delayedClick(a){var b=_menusItems[a];if(b.cb){b.cb()}}function MenuItem_clickCallTrue(a){_micl(this,a);return true}function MenuItem_clickCallFalse(a){_micl(this,a);return false}function MenuItem_invertCall0(a){_mii(this,0)}function MenuItem_invertCall1(a){_mii(this,1)}function MenuItem_focus(){var a=this;if(isLayerDisplayed(a.layer)&&a.textOnlyLayer&&a.textOnlyLayer.focus){a.textOnlyLayer.focus()}}function MenuItem_updateTooltip(){var b=this;if(b.textOnlyLayer&&!b.isSeparator){if(b.textOnlyLayer.innerHTML){b.textOnlyLayer.title=b.textOnlyLayer.innerHTML}b.textOnlyLayer.title+=b.checked?" "+_menuCheckLab:"";if(b.disabled){b.textOnlyLayer.title=(b.textOnlyLayer.title!=null?b.textOnlyLayer.title:"")+" "+_menuDisableLab}if(b.hiddenLabelLayer){var a=((_moz&&b.textOnlyLayer.title)?b.textOnlyLayer.title:"")+b.itemNo+_of+b.par.getTotalNumItems();b.hiddenLabelLayer.innerHTML=a}}}function newScrollMenuWidget(a,m,l,b,s,r,p,k,g,n,e,q,f){var c=newWidget(a);c.list=newListWidget("list_"+a,ScrollMenuWidget_changeCB,l,b,s,r,ScrollMenuWidget_dblClickCB,ScrollMenuWidget_keyUpCB,ScrollMenuWidget_clickCB);c.list.par=c;c.label=NewLabelWidget("label_"+a,n,e);c.showLabel=g;c.changeCB=m;c.menuClickCB=f;c.dblClickCB=p;c.keyUpCB=k;c.beforeShowCB=q;c.zIndex=_menusZIndex;c.init=ScrollMenuWidget_init;c.justInTimeInit=ScrollMenuWidget_justInTimeInit;c.setDisabled=ScrollMenuWidget_setDisabled;c.write=ScrollMenuWidget_write;c.getHTML=ScrollMenuWidget_getHTML;c.show=ScrollMenuWidget_show;c.add=ScrollMenuWidget_add;c.del=ScrollMenuWidget_del;c.getSelection=ScrollMenuWidget_getSelection;c.select=ScrollMenuWidget_select;c.clearSelection=ScrollMenuWidget_clearSelection;c.valueSelect=ScrollMenuWidget_valueSelect;c.getCount=ScrollMenuWidget_getCount;c.isShown=MenuWidget_isShown;c.captureClicks=MenuWidget_captureClicks;c.releaseClicks=MenuWidget_releaseClicks;c.clickCB=new Array;c.clickCBDocs=new Array;return c}function ScrollMenuWidget_init(){}function ScrollMenuWidget_clearSelection(){var a=this;if(a.list){a.list.clearSelection()}}function ScrollMenuWidget_justInTimeInit(){var a=this;a.layer=getLayer(a.id);if(a.layer==null){append2(_curDoc.body,a.getHTML());a.layer=getLayer(a.id)}a.layer._widget=a.widx;a.css=a.layer.style;a.css.visibility="hidden";a.list.init();a.label.init()}function ScrollMenuWidget_setDisabled(){}function ScrollMenuWidget_write(){}function ScrollMenuWidget_getHTML(){var b=this;var a="";a+='<table dir="ltr" onmousedown="event.cancelBubble=true" id="'+b.id+'" style="display:none;" class="menuFrame" cellspacing="0" cellpadding="0" border="0"><tbody>';a+='<tr><td align="center">'+b.list.getHTML()+"</td></tr>";a+='<tr><td align="center">'+b.label.getHTML()+"</td></tr>";a+="</tbody></table>";return a}function ScrollMenuWidget_show(l,k,g){var b=this;if(b.layer==null){b.justInTimeInit()}var e=b.css;if(l){if(b.beforeShowCB){b.beforeShowCB()}b.captureClicks();e.display="block";e.zIndex=(b.zIndex+1);e.visibility="hidden";e.left="-1000px";e.top="-1000px";var m=b.getWidth();var c=b.getHeight();if(b.alignLeft){k-=m}var a=k+m+4,f=g+c+4;if(a>winWidth()){k=Math.max(0,k-4-m)}if(f>winHeight()){g=Math.max(0,g-4-c)}e.left=""+k+"px";e.top=""+g+"px";e.visibility="visible";b.iframeLyr=getDynamicBGIFrameLayer();b.iframeCss=b.iframeLyr.style;iCss=b.iframeCss;iCss.left=""+k+"px";iCss.top=""+g+"px";iCss.width=""+m+"px";iCss.height=""+c+"px";iCss.zIndex=b.zIndex-1;iCss.display="block";if(_ie){g-=2;k-=2}}else{releaseBGIFrame(b.iframeLyr.id);e.display="none";iCss.display="none";b.releaseClicks()}}function ScrollMenuWidget_add(a,e,b,f){var c=this;if(c.layer==null){c.justInTimeInit()}c.list.add(a,e,b,f)}function ScrollMenuWidget_del(a){var b=this;if(b.layer==null){b.justInTimeInit()}b.list.del(a)}function ScrollMenuWidget_getSelection(){var a=this;if(a.layer==null){a.justInTimeInit()}return a.list.getSelection()}function ScrollMenuWidget_select(a){var b=this;if(b.layer==null){b.justInTimeInit()}b.list.select(a)}function ScrollMenuWidget_valueSelect(a){var b=this;if(b.layer==null){b.justInTimeInit()}b.list.valueSelect(a)}function ScrollMenuWidget_getCount(){var a=this;if(a.layer==null){a.justInTimeInit()}return a.list.getCount()}function ScrollMenuWidget_changeCB(){var a=this;if(a.par.changeCB){a.par.changeCB()}}function ScrollMenuWidget_clickCB(){var a=this;a.par.show(false);if(a.par.menuClickCB){a.par.menuClickCB()}}function ScrollMenuWidget_dblClickCB(){var a=this;a.par.show(false);if(a.par.dblClickCB){a.par.dblClickCB()}}function ScrollMenuWidget_keyUpCB(c){var g=13,b=27;var f=this;var a=eventGetKey(c);if(a==g||a==b){f.par.show(false)}if(f.par.keyUpCB){f.par.keyUpCB()}}if(window._DHTML_LIB_PSHEET_JS_LOADED==null){_DHTML_LIB_PSHEET_JS_LOADED=true;_boAllTabs=new Array;_vertTabImgW=3;_vertTabLBorderToTxt=20-_vertTabImgW-1;_vertTabLBorderToIcon=12-_vertTabImgW-1;_vertTabIconToTxt=8;_vertTabIconSize=16;_tabImgLeft=0;_tabImgMid=1;_tabImgRight=2;_tabTxt=3;_tabScrollBar=4;_tabList=5;_VertTab=2;_VertTabWithIcon=3;_HorizTabTop=4;_HorizTabBottom=5;_HorizTabTopWithClose=6;_menuBarTab=7;_vertTabHover=0;_vertTabSelected=1;_vertTabPressed=2;_vertTabNormal=3;_vertTabCSS=[["menuLeftMostSel","naviVTabLSelected","naviVTabLPressed","naviVTabNormal"],["menuLeftPartSel","naviVTabMSelected","naviVTabMPressed","naviVTabNormal"],["menuRightMostSel","naviVTabRSelected","naviVTabRPressed","naviVTabNormal"]];_vertNaviPanelMinW=120;_vertNaviPanelMaxW=180;_vertNaviPanelWithIconW=150;_vertNaviPanelH=23*8;_naviHorzTabH=24;_horizTabSelected=0;_horizTabNormal=1;_horizTabHover=2;_horizTabSelHover=3;_horizTabPressed=4;_tabListNormal=0;_tabListHover=1;_tabListPressed=2;_horizTabCSS=[["naviHTabLSelected","naviHTabLNormal","naviHTabLHover","naviHTabLSelHover","naviHTabLNormal"],["naviHTabMSelected","naviHTabMNormal","naviHTabMHover","naviHTabMSelHover","naviHTabMNormal"],["naviHTabRSelected","naviHTabRNormal","naviHTabRHover","naviHTabRSelHover","naviHTabRNormal"],["naviHTabTextSel","naviHTabText","naviHTabTextHover","naviHTabTextSelHover","naviHTabText"],["naviHScrollBarL","naviHScrollBarM","naviHScrollBarR","naviHScrlBarFirstArrow naviHScrlBarArrowPos","naviHScrlBarPrevArrow naviHScrlBarArrowPos","naviHScrlBarNextArrow naviHScrlBarArrowPos","naviHScrlBarLastArrow naviHScrlBarArrowPos","naviHScrlBarHover","naviHScrlBarPressed","naviHScrlBarDisabled","naviHScrlBarFirstDis naviHScrlBarArrowPos","naviHScrlBarPrevDis naviHScrlBarArrowPos","naviHScrlBarNextDis naviHScrlBarArrowPos","naviHScrlBarLastDis naviHScrlBarArrowPos"],["tabListTop","tabListTopHover","tabListTopPressed"]];_horizTabWithCloseCSS=[["naviHTabLSelected","naviHTabLNormal","naviHTabLHover","naviHTabLSelHover","naviHTabLNormal"],["naviHTabMSelected","naviHTabMNormal","naviHTabMHover","naviHTabMSelHover","naviHTabMNormal"],["naviHTabWithCloseRSel","naviHTabRWithCloseNormal","naviHTabWithCloseRHover","naviHTabWithCloseRSelHover","naviHTabRWithCloseNormal"],["naviHTabTextSel","naviHTabText","naviHTabTextHover","naviHTabTextSelHover","naviHTabText"],["naviHScrollBarL","naviHScrollBarM","naviHScrollBarR","naviHScrlBarFirstArrow naviHScrlBarArrowPos","naviHScrlBarPrevArrow naviHScrlBarArrowPos","naviHScrlBarNextArrow naviHScrlBarArrowPos","naviHScrlBarLastArrow naviHScrlBarArrowPos","naviHScrlBarHover","naviHScrlBarPressed","naviHScrlBarDisabled","naviHScrlBarFirstDis naviHScrlBarArrowPos","naviHScrlBarPrevDis naviHScrlBarArrowPos","naviHScrlBarNextDis naviHScrlBarArrowPos","naviHScrlBarLastDis naviHScrlBarArrowPos"]];_horizTabImgL=4;_horizTabImgR=23;_horizTabTxtPaddingL=15;_horizTabTxtPaddingR=3;_horizTabTxtPaddingB=5;_horizTabImgPadL=8;_horizTabImgToTxt=6;_horizBottomTabTxtPadB=7;_horizTabTxtToClose=5;_horizTabClosePadR=1;_noScrollBar=0;_ScrollBarAtBegin=1;_ScrollBarAtEnd=2;_horizBarWidth=100;_defaultMenuBarWidth=200;_menuBarTabHeight=22;_tabListIconWidth=19}function newTabbedZone(g,c,a,b,e){var f=newFrameZoneWidget(g,b,e);f.w=b;f.h=e;f.cb=a;f.oldIndex=-1;f.tzOldInit=f.init;f.add=TabbedZoneWidget_add;f.select=TabbedZoneWidget_select;f.getTabCSS=TabbedZoneWidget_getTabCSS;f.init=TabbedZoneWidget_init;f.beginHTML=TabbedZoneWidget_beginHTML;f.oldFrameZoneEndHTML=f.endHTML;f.endHTML=TabbedZoneWidget_endHTML;if(!c){f.tabs=newTabBarWidget("tzone_tabs_"+g,true,TabbedZone_itemClick);f.tabs.parentTabbedZone=f}else{f.tabs=c}f.beginTabHTML=TabbedZoneWidget_beginTabHTML;f.endTabHTML=TabbedZoneWidget_endTabHTML;f.beginTab=TabbedZoneWidget_beginTab;f.endTab=TabbedZoneWidget_endTab;f.showTab=TabbedZoneWidget_showTab;f.tzOldResize=f.resize;f.resize=TabbedZoneWidget_resize;return f}function TabbedZone_itemClick(){var b=this.parentTabbedZone,a=this.getSelection().index;b.select(a);if(b.cb){b.cb(a)}}function TabbedZoneWidget_add(b,f,c,l,a,g,e){var k=this;k.tabs.add(b,f,-1,c,l,a,g,e)}function TabbedZoneWidget_init(){var a=this;a.tzOldInit();a.tabs.init();a.select(0)}function TabbedZoneWidget_getTabCSS(a){if(a!=null){if(!a.zoneLayer){a.zoneLayer=getLayer(a.zoneId)}if(a.zoneLayer){return a.zoneLayer.style}}return null}function TabbedZoneWidget_showTab(b,a){var c=this.tabs.items[b];if(c){c.setDisplay(a)}}function TabbedZoneWidget_resize(b,e){var g=this;if(b!=null){g.w=b}if(e!=null){g.h=e}g.tzOldResize(b,e);var a=getLayer(g.id+"_container");if(a){if(g.w){a.style.width=g.w+"px"}if(g.h){a.style.height=g.h+"px"}}var f=g.tabs.items[g.oldIndex];if(f){var c=getLayer(f.zoneId);if(c){if(g.w){c.style.width=g.w+"px"}if(g.h){c.style.height=g.h+"px"}}}}function TabbedZoneWidget_select(f){var a=this,l=a.tabs,b=l.getSelection(),m=a.oldIndex,k;var n=l.items[a.oldIndex];a.tabs.select(f);if(n){k=a.getTabCSS(n);if(k){k.display="none"}}else{var g=l.items.length;for(var e=0;e<g;e++){k=a.getTabCSS(l.items[e]);if(k){k.display="none"}}}a.oldIndex=f;k=a.getTabCSS(l.items[f]);if(k){k.display="";a.resize(a.w,a.h)}}function TabbedZoneWidget_beginHTML(){var a=this;return'<table id="'+this.id+'" cellspacing="0" cellpadding="0" border="0" style="position:absolute;"><tbody><tr class="hideableFrame" valign="bottom" height="28"><td>'+imgOffset(_skin+"dialogframe.gif",5,5,0,0)+'</td><td valign="top" align="left" style="'+backImgOffset(_skin+"tabs.gif",0,288)+'">'+a.tabs.getHTML()+"</td><td>"+imgOffset(_skin+"dialogframe.gif",5,5,5,0)+'</td></tr><tr><td class="hideableFrame" style="'+backImgOffset(_skin+"dialogframeleftright.gif",0,0)+'"></td><td class="dialogzone"><div id="'+a.id+'_container" style="'+sty("width",a.w)+sty("height",a.h)+'">'}function TabbedZoneWidget_endHTML(){return'</div><td class="hideableFrame" style="'+backImgOffset(_skin+"dialogframeleftright.gif",5,0)+'"></td></tr><tr class="hideableFrame"><td>'+imgOffset(_skin+"dialogframe.gif",5,5,0,5)+'</td><td style="'+backImgOffset(_skin+"tabs.gif",0,187)+'"></td><td>'+imgOffset(_skin+"dialogframe.gif",5,5,5,5)+"</td></tr></tr></tbody></table>"}function TabbedZoneWidget_beginTabHTML(a){var b=this;return'<div id="'+b.zoneId+'" style="display:none;'+sty("width",b.w)+sty("height",b.h)+'">'}function TabbedZoneWidget_endTabHTML(){return"</div>"}function TabbedZoneWidget_beginTab(a){_curDoc.write(this.beginTabHTML(a))}function TabbedZoneWidget_endTab(){_curDoc.write(this.endTabHTML())}function newNaviTabWidget(c,a,q,f,g,e,r,n,b,p,m,l,k){return new_NaviTabWidget({id:c,name:a,value:q,tabType:f,clickCB:g,dblclickCB:e,tooltip:r,icon:n,iconW:b,iconH:p,iconOffX:m,iconOffY:l,closeTabCB:k})}function new_NaviTabWidget(a){var b=new_Widget(a);b.superInit=b.init;b.init=NaviTabWidget_init;b.getHTML=NaviTabWidget_getHTML;b.hasCloseButton=NaviTabWidget_hasCloseButton;b.select=NaviTabWidget_select;b.setUserData=NaviTabWidget_setUserData;b.getUserData=NaviTabWidget_getUserData;b.setMenu=NaviTabWidget_setMenu;b.getMenu=NaviTabWidget_getMenu;b.setHtml=NaviTabWidget_setHtml;b.getHtml=NaviTabWidget_getHtml;b.zoneId="tzone_tab_"+Math.round(Math.random()*12345)+new Date().getTime();b.tabType=Widget_param(a,"tabType",_HorizTabTop);b.name=Widget_param(a,"name","Tab");b.value=Widget_param(a,"value",0);b.cb=Widget_param(a,"clickCB",null);b.dblClick=Widget_param(a,"dblclickCB",null);b.icon=Widget_param(a,"icon",null);b.iconW=Widget_param(a,"iconW",(b.icon?_vertTabIconSize:1));b.iconH=Widget_param(a,"iconH",(b.icon?_vertTabIconSize:1));b.iconOffX=Widget_param(a,"iconOffX",0);b.iconOffY=Widget_param(a,"iconOffY",0);b.tooltip=Widget_param(a,"tooltip",null);b.closeTabCB=Widget_param(a,"closeTabCB",null);b.isSelected=false;b.leftimgid="naviTabL_"+b.id;b.midimgid="naviTabM_"+b.id;b.rightimgid="naviTabR_"+b.id;b.txtid="naviTabTxt_"+b.id;b.sepid="naviTabSep_"+b.id;b.closeid="naviTabClose_"+b.id;b.iconid="naviTabIcon_"+b.id;switch(b.tabType){case _VertTab:case _VertTabWithIcon:b.tabCSSTable=_vertTabCSS;break;case _HorizTabBottom:b.tabCSSTable=_horizBottomTabCSS;break;case _HorizTabTopWithClose:b.tabCSSTable=_horizTabWithCloseCSS;break;case _HorizTabTop:default:b.tabCSSTable=_horizTabCSS;break;case _menuBarTab:b.tabCSSTable=_menuBarTabCSS;break}b.mover=NaviTabWidget_mover;b.mdown=NaviTabWidget_mdown;b.keydownCB=NaviTabWidget_keydownCB;b.contextMenuCB=NaviTabWidget_contextMenuCB;b.changeState=NaviTabWidget_changeState;b.getVertHTML=NaviTabWidget_getVertHTML;b.getHorizHTML=NaviTabWidget_getHorizHTML;b.displaySep=NaviTabWidget_displaySep;b.isVert=NaviTabWidget_isVert;b.updateCloseIcon=NaviTabWidget_updateCloseIcon;b.mdown_closeIcon=NaviTabWidget_mdown_closeIcon;b.kdown_closeIcon=NaviTabWidget_kdown_closeIcon;b.clickCB=NaviTabWidget_clickCB;b.dblClickCB=NaviTabWidget_dblClickCB;b.leftimgLyr=null;b.midimgLyr=null;b.rightimgLyr=null;b.txtLyr=null;b.iconLyr=null;b.sepLyr=null;b.closeLyr=null;b.data=new Object;return b}function NaviTabWidget_init(){var a=this;a.superInit();if(a.layer){a.layer.onmouseover=a.mover;a.layer.onmouseout=a.mover;a.layer.onmousedown=a.mdown;a.layer.onmouseup=a.mdown;a.layer.onclick=a.clickCB;if(_ie){a.layer.ondblclick=a.dblClickCB}a.layer.onkeydown=a.keydownCB;a.layer.onselectstart=function(){return false};a.layer.ondragstart=function(){return false};a.layer.oncontextmenu=a.contextMenuCB}a.leftimgLyr=getLayer(a.leftimgid);a.midimgLyr=getLayer(a.midimgid);a.rightimgLyr=getLayer(a.rightimgid);a.txtLyr=getLayer(a.txtid);a.iconLyr=getLayer(a.iconid);a.sepLyr=getLayer(a.sepid);if(a.hasCloseButton()){a.closeLyr=getLayer(a.closeid);a.closeLyr.onmousedown=a.mdown_closeIcon;a.closeLyr.onkeypress=a.kdown_closeIcon;a.closeLyr.onmouseup=a.mdown_closeIcon;a.updateCloseIcon("naviHTabCloseSel",a.isSelected)}}function NaviTabWidget_getVertHTML(){var f=this;var b="";b='<table id="'+f.id+'" style="cursor:'+_hand+'" cellspacing="0" cellpadding="0" border="0">';b+='<tbody><tr height="'+_mitemH+'">';var c=f.isSelected?_vertTabSelected:_vertTabNormal;b+='<td id="'+f.leftimgid+'" class="'+f.tabCSSTable[_tabImgLeft][c]+'"><div style="width:'+_vertTabImgW+'px;"></div></td>';var a=f.par.w-2*_vertTabImgW;b+='<td id="'+f.midimgid+'" class="'+f.tabCSSTable[_tabImgMid][c]+'" width="'+a+'">';if(f.tabType==_VertTabWithIcon){b+='<table cellspacing="0" cellpadding="0" border="0" width="100%">';b+='<tbody><tr height="'+_mitemH+'">';b+='<td style="padding-left:'+_vertTabLBorderToIcon+'px;">'+imgOffset((f.icon?f.icon:(_skin+"../transp.gif")),_vertTabIconSize,f.iconH,f.iconOffX,f.iconOffY,f.iconid)+"</td>";var e=Math.max(40,a-_vertTabLBorderToIcon-_vertTabIconSize);b+='<td  style="padding-left:'+_vertTabIconToTxt+"px;width:"+e+'px;" >';b+='<div id="'+f.txtid+'" class="naviVTabText" style="width:'+(e-_vertTabIconToTxt-2)+'px;">'+convStr(f.name)+"</div></td>";b+="</tr></tbody></table>"}else{b+='<div id="'+f.txtid+'" class="naviVTabText" style="padding-left:'+_vertTabLBorderToTxt+"px;width:"+(a-_vertTabLBorderToTxt-2)+'px;">'+convStr(f.name)+"</div>"}b+="</td>";b+='<td id="'+f.rightimgid+'" class="'+f.tabCSSTable[_tabImgRight][c]+'"><div style="width:'+_vertTabImgW+'px"></div></td>';b+="</tr></tbody></table>";return b}function NaviTabWidget_hasCloseButton(){var a=this;return _HorizTabTopWithClose==a.tabType&&(!a.par.getTabIndexByName(a.name)==0||a.isFirstTabClosable)}function NaviTabWidget_getHorizHTML(){var g=this;var c='<table id="'+g.id+'" style="cursor:'+_hand+'" cellspacing="0" cellpadding="0" border="0">';c+='<tbody><tr height="'+_naviHorzTabH+'">';var f=g.isSelected?_horizTabSelected:_horizTabNormal;c+='<td id="'+g.leftimgid+'" class="'+g.tabCSSTable[_tabImgLeft][f]+'" valign="top"><div style="width:'+_horizTabImgL+'px;"></div></td>';c+='<td id="'+g.midimgid+'" valign="bottom" class="'+g.tabCSSTable[_tabImgMid][f]+'">';c+='<table cellspacing="0" cellpadding="0" border="0" width="100%">';c+='<tbody><tr height="'+_naviHorzTabH+'">';if(_menuBarTab!=g.tabType){var b=(_HorizTabTop==g.tabType||_HorizTabTopWithClose==g.tabType)?"margin-bottom:3px;":"margin-bottom:6px;";b=b+"margin-left:"+((g.iconW>1)?_horizTabImgPadL:0)+"px;";c+='<td  valign="bottom" >'+imgOffset((g.icon?g.icon:(_skin+"../transp.gif")),g.iconW,g.iconH,g.iconOffX,g.iconOffY,g.iconid,null,null,b)+"</td>"}var e=' style="padding-left:'+((g.iconW>1)?_horizTabImgToTxt:_horizTabTxtPaddingL)+"px;padding-right:"+((_HorizTabTopWithClose==g.tabType)?_horizTabTxtToClose:_horizTabTxtPaddingR)+"px;padding-bottom:"+((_HorizTabTop==g.tabType||_HorizTabTopWithClose==g.tabType)?_horizTabTxtPaddingB:_horizBottomTabTxtPadB)+'px;" ';var a='<div tabindex="0" role="tab" id="'+g.txtid+'" '+e+' class="'+g.tabCSSTable[_tabTxt][f]+'" >'+convStr(g.name)+"</div>";c+='<td valign="bottom">'+a+"</td>";if(g.hasCloseButton()){c+='<td valign="top" style="padding-right:'+_horizTabClosePadR+'px;">';c+='<div tabindex="0" class="naviHTabCloseSel" id="'+g.closeid+'" role="button" title="'+_closeTab+" "+convStr(g.name)+'"></div></td>'}c+="</tr></tbody></table>";c+="</td>";c+='<td id="'+g.rightimgid+'" class="'+g.tabCSSTable[_tabImgRight][f]+'" valign="top">';c+='<div style="width:'+((_HorizTabTopWithClose==g.tabType)?_horizTabImgL:_horizTabImgR)+'px"></div></td>';c+='<td  class="'+((g.tabType==_HorizTabBottom)?"naviHBottomTabMNormal":"naviHTabMNormal")+'">';c+='<div id="'+g.sepid+'" class="naviHTabSeparator"></div></td>';c+="</tr></tbody></table>";return c}function NaviTabWidget_isVert(){var a=this;return(a.tabType==_VertTabWithIcon||a.tabType==_VertTab)}function NaviTabWidget_getHTML(){var a=this;return(a.isVert())?a.getVertHTML():a.getHorizHTML()}function NaviTabWidget_select(a){var b=this;b.isSelected=a;b.changeState(b.isVert()?(a?_vertTabSelected:_vertTabNormal):(a?_horizTabSelected:_horizTabNormal));b.updateCloseIcon("naviHTabCloseSel",b.isSelected)}function NaviTabWidget_updateCloseIcon(b,a){var c=this;if(c.tabType==_HorizTabTopWithClose&&c.closeLyr){c.closeLyr.className=b;c.closeLyr.style.visibility=a?_show:_hide}}function NaviTabWidget_changeState(a){var b=this;if(b.layer){b.leftimgLyr.className=b.tabCSSTable[_tabImgLeft][a];b.midimgLyr.className=b.tabCSSTable[_tabImgMid][a];b.rightimgLyr.className=b.tabCSSTable[_tabImgRight][a]}if(b.txtLyr&&!b.isVert()){b.txtLyr.className=b.tabCSSTable[_tabTxt][a]}}function NaviTabWidget_mover(b){var k=getWidget(this);var b=getEvent(b);var f=(b&&b.type=="mouseover")?true:false;if(k.isVert()){k.changeState(f?_vertTabHover:(k.isSelected?_vertTabSelected:_vertTabNormal))}else{k.changeState(k.isSelected?(f?_horizTabSelHover:_horizTabSelected):(f?_horizTabHover:_horizTabNormal));var c=k.par.getPrevNextTabs(k.idx);if(!c){return}var e=c.prevTab;var a=c.nextTab;if(!k.isSelected){var g=false;if(!f){g=true;if(a){if(a.isSelected){g=false}}else{g=false}}k.displaySep(g,true);if(e){e.displaySep(f?false:((e.isSelected)?false:true),true)}}else{k.displaySep(false);if(e){e.displaySep(false)}}k.updateCloseIcon((f?"naviHTabCloseHover":"naviHTabCloseSel"),(k.isSelected||f))}}function NaviTabWidget_mdown(a){var b=getWidget(this);var a=getEvent(a);var c=(a&&a.type=="mousedown")?true:false;if(b.isVert()){b.changeState(c?_vertTabPressed:(b.isSelected?_vertTabSelected:_vertTabNormal))}}function NaviTabWidget_mdown_closeIcon(a){var b=getWidget(this);var a=getEvent(a);var c=(a&&a.type=="mousedown")?true:false;b.updateCloseIcon((c?"naviHTabClosePressed":"naviHTabCloseSel"),(c||b.isSelected));if(!c){if(b.par&&b.par.closeTab){b.par.closeTab(b.par.findTabIndex(b),b.closeTabCB)}}}function NaviTabWidget_kdown_closeIcon(b){var a=eventGetKey(b);if(a==32||a==13){eventCancelBubble(b);var c=getWidget(this);if(c.par&&c.par.closeTab){c.par.closeTab(c.par.findTabIndex(c),c.closeTabCB)}}}function NaviTabWidget_clickCB(a){var a=getEvent(a);var b=getWidget(this);if(b&&b.cb){b.cb()}eventCancelBubble(a);return false}function NaviTabWidget_dblClickCB(a){var a=getEvent(a);var b=getWidget(this);if(b&&b.dblClick){b.dblClick()}eventCancelBubble(a);return false}function NaviTabWidget_keydownCB(b){var a=eventGetKey(b);if(a==13){eventCancelBubble(b);var c=getWidget(this);if(c&&c.cb){c.cb()}}}function NaviTabWidget_displaySep(c,a){var b=this;if(!b.isVert()&&b.sepLyr){b.sepLyr.style.visibility=c?_show:_hide}}function NaviTabWidget_setUserData(a){this.data.userdata=a}function NaviTabWidget_getUserData(){return this.data.userdata}function NaviTabWidget_contextMenuCB(a){a=getEvent(a);var c=getWidget(this);var b=c.par;if(c&&c.cb){c.cb()}if(b&&b.showTabMenu){b.showTabMenu(a,c.idx)}eventCancelBubble(a);return false}function NaviTabWidget_setMenu(a){this.data.menu=a}function NaviTabWidget_getMenu(){return this.data.menu}function NaviTabWidget_setHtml(a){this.data.html=a}function NaviTabWidget_getHtml(){return this.data.html}function new_NaviBarWidget(e){var g=new_Widget(e);g.superInit=g.init;g.oldResize=g.resize;g.init=NaviBarWidget_init;g.getHTML=NaviBarWidget_getHTML;g.add=NaviBarWidget_add;g.addByPrms=NaviBarWidget_addByPrms;g.remove=NaviBarWidget_remove;g.removeAll=NaviBarWidget_removeAll;g.getCount=NaviBarWidget_getCount;g.select=NaviBarWidget_select;g.getSelection=NaviBarWidget_getSelection;g.getBarType=NaviBarWidget_getBarType;g.getMenu=NaviBarWidget_getMenu;g.showMenu=NaviBarWidget_showMenu;g.getTabMenu=NaviBarWidget_getTabMenu;g.showTabMenu=NaviBarWidget_showTabMenu;g.setShowContextMenuAllowed=NaviBarWidget_setShowContextMenuAllowed;g.getTab=NaviBarWidget_getTab;g.findTabIndex=NaviBarWidget_findTabIndex;g.getSelectedTab=NaviBarWidget_getSelectedTab;g.showTab=NaviBarWidget_showTab;g.resize=NaviBarWidget_resize;g.setTabHTML=NaviBarWidget_setTabHTML;g.getTabHTML=NaviBarWidget_getTabHTML;g.getTabIndexByName=NaviBarWidget_getTabIndexByName;g.getTabIndexByValue=NaviBarWidget_getTabIndexByValue;g.getTabID=NaviBarWidget_getTabID;var b=Widget_param(e,"w",null);var f=Widget_param(e,"h",null);g.cb=Widget_param(e,"cb",null);g.isFirstTabClosable=Widget_param(e,"isFirstTabClosable",true);g.dblclick=Widget_param(e,"dblclick",null);g.beforeShowTabMenu=Widget_param(e,"beforeShowTabMenu",null);g.type=Widget_param(e,"naviBarType",_VertTab);g.counter=0;g.items=new Array;g.selIndex=-1;g.leftLimit=0;g.showContextMenuAllowed=true;g.menu=newMenuWidget("naviBarMenu_"+g.id,null,Widget_param(e,"beforeShowMenu",null));g.tabList=null;g.showScrollBar=_noScrollBar;g.isVert=((g.type==_VertTab)||(g.type==_VertTabWithIcon))?true:false;if(g.isVert){if(b){b=Math.max(_vertNaviPanelMinW,b);b=Math.min(b,_vertNaviPanelMaxW)}g.w=(b?b:((g.type==_VertTab)?_vertNaviPanelMinW:_vertNaviPanelWithIconW))+2;if(f){var k=Math.ceil(f/_mitemH);f=(Math.max(1,k))*_mitemH}g.h=(f?f:_vertNaviPanelH)+2}else{var c=Widget_param(e,"showTabList",false);if(c){g.tabList=newMenuWidget("naviBarTabListMenu_"+g.id,null,NaviBarWidget_beforeShowTabListCB,NaviBarWidget_TabListonPositionCB);g.tabList.navibar=g}else{var a=Widget_param(e,"showScrollBar",false);g.showScrollBar=a?((_HorizTabBottom==g.type)?_ScrollBarAtBegin:_ScrollBarAtEnd):_noScrollBar}g.w=b?b:(_horizBarWidth+_scrollBarWidth);g.h=_naviHorzTabH}switch(g.type){case _VertTab:case _VertTabWithIcon:g.tabCSSTable=_vertTabCSS;break;case _HorizTabBottom:g.tabCSSTable=_horizBottomTabCSS;break;case _HorizTabTop:default:g.tabCSSTable=_horizTabCSS;break}g.trid="naviBarTR_"+g.id;g.trLyr=null;g.divid="naviBarDIV_"+g.id;g.divLyr=null;g.scrollbarid="scrlbar_"+g.id;g.firstid="f_"+g.id;g.previd="p_"+g.id;g.nextid="n_"+g.id;g.lastid="l_"+g.id;g.scrollbarLyr=null;g.firstLyr=null;g.prevLyr=null;g.nexttLyr=null;g.lastLyr=null;g.updateSepDisplay=NaviBarWidget_updateSepDisplay;g.getPrevNextTabs=NaviBarWidget_getPrevNextTabs;g.closeTab=NaviBarWidget_closeTab;g.getBarIndex=NaviBarWidget_getBarIndex;g.mover_scrollbar=NaviBarWidget_mover_scrollbar;g.mdown_scrollbar=NaviBarWidget_mdown_scrollbar;g.contextMenuCB=NaviBarWidget_contextMenuCB;g.dblclickCB=NaviBarWidget_dblclickCB;g.getItemXPos=NaviBarWidget_getItemXPos;g.par=null;g.tablistid="bartablist_"+g.id;g.tablistLyr=null;g.mover_tablist=NaviBarWidget_mover_tablist;g.mdown_tablist=NaviBarWidget_mdown_tablist;g.kdown_tablist=NaviBarWidget_kdown_tablist;g.onfocus_tablist=NaviBarWidget_onfocus_tablist;g.onblur_tablist=NaviBarWidget_onblur_tablist;g.click_tablist=NaviBarWidget_click_tablist;g.onChangeTabList=NaviBarWidget_onChangeTabList;g.showTabListIcon=NaviBarWidget_showTabListIcon;g.buildTabList=NaviBarWidget_buildTabList;return g}function newNaviBarWidget(b,n,c,e,m,f,p,l,k,a,g){return new_NaviBarWidget({id:b,naviBarType:n,cb:c,dblclick:e,w:m,h:f,beforeShowTabMenu:p,beforeShowMenu:l,showScrollBar:k,showTabList:a,isFirstTabClosable:g})}function NaviBarWidget_getBarType(){return this.type}function NaviBarWidget_init(){var g=this,c=g.items;g.superInit();g.trLyr=getLayer(g.trid);g.divLyr=getLayer(g.divid);var b=c.length;for(var e=0;e<b;e++){var f=c[e];f.init();f.select(e==g.selIndex)}g.updateSepDisplay();if(g.tabList){g.tablistLyr=getLayer(g.tablistid);g.tablistLyr.onmouseover=g.mover_tablist;g.tablistLyr.onmouseout=g.mover_tablist;g.tablistLyr.onmousedown=g.mdown_tablist;g.tablistLyr.onkeypress=g.kdown_tablist;g.tablistLyr.onmouseup=g.mdown_tablist;g.tablistLyr.onfocus=g.onfocus_tablist;g.tablistLyr.onblur=g.onblur_tablist;g.tablistLyr.onclick=g.click_tablist;g.tablistLyr.oncontextmenu=function(){return false};g.showTabListIcon()}else{if(g.showScrollBar!=_noScrollBar){g.scrollbarLyr=getLayer(g.scrollbarid);g.firstLyr=getLayer(g.firstid);g.prevLyr=getLayer(g.previd);g.nexttLyr=getLayer(g.nextid);g.lastLyr=getLayer(g.lastid);var a=[g.firstLyr,g.prevLyr,g.nexttLyr,g.lastLyr];for(var e=0;e<4;e++){a[e].onmouseover=g.mover_scrollbar;a[e].onmouseout=g.mover_scrollbar;a[e].onmousedown=g.mdown_scrollbar;a[e].onmouseup=g.mdown_scrollbar}g.firstLyr.onclick=g.firstCB;g.prevLyr.onclick=g.prevCB;g.nexttLyr.onclick=g.nextCB;g.lastLyr.onclick=g.lastCB;g.scrollbarLyr.oncontextmenu=function(){return false}}}if(g.isVert){g.layer.oncontextmenu=g.contextMenuCB}else{if(g.divLyr){g.divLyr.oncontextmenu=g.contextMenuCB}}if(g.cb){if(g.isVert){g.layer.onclick=function(){g.cb();return false}}else{if(g.divLyr){g.divLyr.onclick=function(){g.cb();return false}}}}if(g.dblclick){if(g.isVert){g.layer.ondblclick=g.dblclickCB}else{if(g.divLyr){g.divLyr.ondblclick=g.dblclickCB}}}g.layer.onselectstart=function(){return false};g.layer.ondragstart=function(){return false};g.resize(g.w,g.h)}function NaviBarWidget_dblclickCB(a){var b=getWidget(this);var a=getEvent(a);if(b.dblclick){b.dblclick()}eventCancelBubble(a);return false}function NaviBarWidget_mover_scrollbar(a){if(this.disabled){return}var c=getWidget(this);var a=getEvent(a);var b=(a&&a.type=="mouseover")?true:false;this.className=b?c.tabCSSTable[_tabScrollBar][_scrollbarHover]:c.tabCSSTable[_tabScrollBar][_scrollbarM]}function NaviBarWidget_mdown_scrollbar(a){if(this.disabled){return}var b=getWidget(this);var a=getEvent(a);var c=(a&&a.type=="mousedown")?true:false;this.className=c?b.tabCSSTable[_tabScrollBar][_scrollbarPressed]:b.tabCSSTable[_tabScrollBar][_scrollbarM]}function NaviBarWidget_getHTML(){var k=this,e=k.items,b=e.length;var g='<div id="'+this.id+'" class="'+(k.isVert?"dlgFrame":"")+'" align="left" style="overflow:hidden;width:'+k.w+"px;height:"+k.h+'px">';g+='<table cellspacing="0" cellpadding="0" border="0" '+(k.isVert?' class="naviVTabBackgnd"':"")+"><tbody>";if(k.isVert){for(var f=0;f<b;f++){g+="<tr><td>"+e[f].getHTML()+"</td></tr>"}}else{g+="<tr>";var a="";if(k.showScrollBar!=_noScrollBar){a="<td>";a+='<table id="'+k.scrollbarid+'" cellspacing="0" cellpadding="0" border="0" width="'+_scrollBarWidth+'"><tbody><tr style="width:'+_scrollBarWidth+'px;">';a+='<td class="'+k.tabCSSTable[_tabScrollBar][_scrollbarL]+'"><div></div></td>';a+='<td id="'+k.firstid+'" class="'+k.tabCSSTable[_tabScrollBar][_scrollbarM]+'" align="center" valign="bottom"><div class="'+k.tabCSSTable[_tabScrollBar][_scrollbarFirst]+'"></div></td>';a+='<td id="'+k.previd+'" class="'+k.tabCSSTable[_tabScrollBar][_scrollbarM]+'" align="center" valign="bottom"><div class="'+k.tabCSSTable[_tabScrollBar][_scrollbarPrev]+'"></div></td>';a+='<td id="'+k.nextid+'" class="'+k.tabCSSTable[_tabScrollBar][_scrollbarM]+'" align="center" valign="bottom"><div class="'+k.tabCSSTable[_tabScrollBar][_scrollbarNext]+'"></div></td>';a+='<td id="'+k.lastid+'" class="'+k.tabCSSTable[_tabScrollBar][_scrollbarM]+'" align="center" valign="bottom"><div class="'+k.tabCSSTable[_tabScrollBar][_scrollbarLast]+'"></div></td>';a+='<td class="'+k.tabCSSTable[_tabScrollBar][_scrollbarR]+'"><div></div></td>';a+="</tr></tbody></table>";a+="</td>"}if(k.showScrollBar==_ScrollBarAtBegin){g+=a}var c=k.w;if(k.showScrollBar!=_noScrollBar){c=Math.max(c-_scrollBarWidth,_horizBarWidth)}g+='<td><div style="overflow:hidden;width:'+c+'px;"  id="'+k.divid+'" class="'+k.tabCSSTable[_tabImgMid][_horizTabNormal]+'">';g+='<table cellspacing="0" cellpadding="0" border="0"><tbody>';g+='<tr id="'+k.trid+'">';for(var f=0;f<b;f++){g+="<td>"+e[f].getHTML()+"</td>"}g+="</tr></tbody></table></div></td>";if(k.tabList){g+='<td class="'+k.tabCSSTable[_tabImgMid][_horizTabNormal]+'"><table cellspacing="0" cellpadding="0" border="0"><tbody><tr>';g+='<td tabindex=0 id="'+k.tablistid+'" class="'+k.tabCSSTable[_tabList][_tabListNormal]+'" role="button" title="'+L_bobj_crv_TabList+'">';g+='<div class="tabListIcon"></div></td>';g+="</tr></tbody></table></td>"}if(k.showScrollBar==_ScrollBarAtEnd){g+=a}g+="</tr>"}g+="</tbody></table></div>";return g}function NaviBarWidget_add(a,l,m,g,b,k,f,e,n,c){return this.addByPrms({name:a,value:l,tooltip:n,icon:g,iconW:b,iconH:k,iconOffX:f,iconOffY:e,closeTabCB:c},m)}function NaviBarWidget_addByPrms(l,m){var b=this,a=b.counter++;l.id="naviTab_"+a+"_"+b.id;l.tabType=b.type;l.clickCB=NaviBarWidget_itemClick;l.dblclickCB=NaviBarWidget_itemDblClick;var f=new_NaviTabWidget(l);f.par=b;f.idx=a;arrayAdd(b,"items",f,m);var k=b.items.length;if(b.isVert&&b.layer!=null){var c=document.createElement("td");c.innerHTML=f.getHTML();var g=document.createElement("tr");g.appendChild(c);var e=b.layer.childNodes[0].childNodes[0];if((typeof(m)=="undefined")||(k==1)||(m==null)||(m==-1)||(m>=k)){e.appendChild(g);m=k-1}else{if(e.childNodes[parseInt(m)]){e.insertBefore(g,e.childNodes[parseInt(m)])}}f.init()}else{if(b.trLyr){var c=document.createElement("td");c.innerHTML=f.getHTML();if((typeof(m)=="undefined")||(k==1)||(m==null)||(m==-1)||(m>=k)){b.trLyr.appendChild(c);m=k-1}else{b.trLyr.insertBefore(c,b.trLyr.childNodes[parseInt(m)])}f.init();b.showTabListIcon()}}if((b.selIndex!=null)&&(b.selIndex>=0)){if(m<=b.selIndex){b.selIndex++}}b.updateSepDisplay();return f}function NaviBarWidget_getBarIndex(e){var f=this,b=f.items,a=b.length;for(var c=0;c<a;c++){if(b[c].idx==e){return c}}return null}function NaviBarWidget_itemClick(){var f=this.par,b=f.items,a=b.length,c=-1;for(var e=0;e<a;e++){if(b[e].idx==this.idx){f.select(e);c=e;break}}if(f.cb){f.cb(c)}}function NaviBarWidget_itemDblClick(){var f=this.par,b=f.items,a=b.length,c=-1;for(var e=0;e<a;e++){if(b[e].idx==this.idx){c=e;break}}if(f.dblclick){f.dblclick(c)}}function NaviBarWidget_select(c){if(c==null||typeof(c)=="undefined"){return}var e=this,b=e.items,a=b.length;if(c==-1){c=a-1}if((c>=0)&&(c<a)){if((e.selIndex!=null)&&(e.selIndex>=0)&&(e.selIndex!=c)&&(e.selIndex<a)){b[e.selIndex].select(false)}e.selIndex=c;b[c].select(true);e.updateSepDisplay()}}function NaviBarWidget_updateSepDisplay(){var f=this;if(f.isVert||(f.layer==null)){return}var b=f.items,a=b.length;var e=true;for(var c=0;c<a;c++){e=true;if(b[c].isSelected){e=false}else{if((c+1<a)&&b[c+1].isSelected){e=false}else{if(c==a-1){e=false}}}b[c].displaySep(e)}}function NaviBarWidget_closeTab(c,a){var e=this;var b=e.getBarIndex(c);if(a){a(b)}if(e.par&&e.par.closeTab){e.par.closeTab(b)}e.remove(b)}function NaviBarWidget_getPrevNextTabs(e){var g=this;var f=g.getBarIndex(e);var b=g.items,a=b.length;if(f!=null){var c=new Object;c.prevTab=(f==0)?null:b[f-1];c.nextTab=(f==a-1)?null:b[f+1];return c}return null}function NaviBarWidget_getItemXPos(b){var e=this;var a=0;for(var c=0;c<b;c++){a+=parseInt(e.items[c].getWidth())}return a}function NaviBarWidget_getCount(){return this.items.length}function NaviBarWidget_mover_tablist(a){var c=getWidget(this);var a=getEvent(a);var b=(a&&a.type=="mouseover")?true:false;this.className=b?c.tabCSSTable[_tabList][_tabListHover]:c.tabCSSTable[_tabList][_tabListNormal]}function NaviBarWidget_mdown_tablist(a){var b=getWidget(this);var a=getEvent(a);var c=(a&&a.type=="mousedown")?true:false;this.className=c?b.tabCSSTable[_tabList][_tabListPressed]:b.tabCSSTable[_tabList][_tabListNormal]}function NaviBarWidget_onfocus_tablist(a){var b=getWidget(this);this.className=b.tabCSSTable[_tabList][_tabListHover]}function NaviBarWidget_onblur_tablist(a){var b=getWidget(this);this.className=b.tabCSSTable[_tabList][_tabListNormal]}function NaviBarWidget_kdown_tablist(a){if(eventGetKey(a)==13){NaviBarWidget_click_tablist.apply(this)}}function NaviBarWidget_click_tablist(){var a=getWidget(this);a.buildTabList();var b=NaviBarWidget_TabListonPositionCB.apply(a);a.tabList.show(true,b.x,b.y);a.tabList.resetTooltips()}function NaviBarWidget_TabListonPositionCB(){var b=this;if(!b){return}if(!b.tabList.layer){b.tabList.justInTimeInit()}var a=getPosScrolled(b.tablistLyr);var c=new Object;c.x=Math.max(0,(a.x+23-b.tabList.getWidth()));c.y=(a.y+b.tablistLyr.offsetHeight);return c}function NaviBarWidget_beforeShowTabListCB(){var c=this.navibar;if(!c){return}var a=c.getSelectedTab();if(a){var b=this.getItemByID(a.idx);if(b){b.setTextClass("tabListMenuItem")}}}function NaviBarWidget_remove(n,m){var a=this,k=a.items,g=k.length;if((n>=0)&&(n<g)){var c=k[n];arrayRemove(a,"items",n);k=a.items;g=k.length;var e=c.layer;if(e!=null){if(a.isVert&&a.layer){var q=e.parentNode.parentNode;var b=a.layer.childNodes[0].childNodes[0];b.removeChild(q)}else{if(a.trLyr){a.trLyr.removeChild(e.parentNode)}}if(c.zoneId){var p=getLayer(c.zoneId);if(p){p.parentNode.removeChild(p)}}}for(var f=0;f<g;f++){if(k[f].isSelected){a.selIndex=f;break}}if(m){if(a.selIndex>n){a.cb(a.selIndex-1)}else{if((a.selIndex==n)&&(g>0)){a.cb(Math.min(n,g-1))}}}if(g==0){a.selIndex=null}a.showTabListIcon()}}function NaviBarWidget_removeAll(){var e=this,b=e.items,a=b.length;for(var c=a-1;c>=0;c--){e.remove(c)}}function NaviBarWidget_setTabHTML(a,b){var c=this;if(typeof(a)=="undefined"||a==null){a=c.items.length-1}if(a>=0&&a<=(c.items.length-1)){c.items[a].setHtml(b)}}function NaviBarWidget_getTabHTML(a){var b=this;if(a>=0&&a<=(b.items.length-1)){return b.items[a].getHtml()}return""}function NaviBarWidget_getSelection(){var c=this;if(c.getCount()==0){c.selIndex=-1;return null}var a=c.selIndex;if((a!=null)&&(a>=0)&&c.items[a]){var b=new Object;b.index=a;b.valueOf=c.items[a].value;b.name=c.items[a].name;return b}else{return null}}function NaviBarWidget_getMenu(){return this.menu}function NaviBarWidget_getTabMenu(c){var f=null;var e=this,b=e.items,a=b.length;if((c>=0)&&(c<a)){f=b[c].getMenu();if(!f){f=newMenuWidget(("naviTabMenu_"+e.id+"_"+e.getTabID(c)),null,e.beforeShowTabMenu);b[c].setMenu(f)}}return f}function NaviBarWidget_setShowContextMenuAllowed(a){this.showContextMenuAllowed=a}function NaviBarWidget_showMenu(a){if(this.showContextMenuAllowed==false){return}a=getEvent(a);this.menu.show(true,(eventGetX(a)+winScrollX()),(eventGetY(a)+winScrollY()))}function NaviBarWidget_showTabMenu(a,c){if(this.showContextMenuAllowed==false){return}a=getEvent(a);var b=this.getBarIndex(c);var e=this.items[b].getMenu();if(e){e.show(true,(eventGetX(a)+winScrollX()),(eventGetY(a)+winScrollY()))}}function NaviBarWidget_showTab(e,b){var f=this,c=f.items,a=c.length;if((e>=0)&&(e<a)){c[e].show(b)}}function NaviBarWidget_showTabListIcon(){var a=this;if(a.tablistLyr){a.tablistLyr.style.visibility=(a.items.length>1)?_show:_hide}}function NaviBarWidget_tabListMenuItemsCB(){var b=this.par.navibar;if(!b){return}var a=b.getBarIndex(parseInt(this.id));if((a>=0)&&(a<b.items.length)&&b.divLyr&&b.tabList){b.onChangeTabList(a);b.select(a);if(b.cb){b.cb(a)}}}function NaviBarWidget_onChangeTabList(c){if(c<0){c=0}var b=this;var l=b.items,g=l.length;if(!b.divLyr||!b.trLyr||!b.tabList||!g){return}var k=c;var f=c;var m=c;var a=b.divLyr.offsetWidth;var e=l[c].getWidth();if(b.trLyr.offsetWidth>b.divLyr.offsetWidth){if(e<a){while(true){k++;if(k<g){if((l[k].getWidth()+e)<a){e+=l[k].getWidth()}else{break}}f--;if(f>=0){if((l[f].getWidth()+e)<a){e+=l[f].getWidth();m=f}else{break}}}}}else{m=0}if(m>=0){b.divLyr.scrollLeft=b.getItemXPos(m)}}function NaviBarWidget_buildTabList(){var e=this;var b=e.items,a=b.length;e.tabList.removeAll();for(var c=0;c<a;c++){e.tabList.add((""+b[c].idx),b[c].name,NaviBarWidget_tabListMenuItemsCB)}}function NaviBarWidget_resize(a,b){var c=this;if(c.isVert){return}c.oldResize(a);if(a!=null){c.w=a;if(c.divLyr){if(c.tabList){a=Math.max((a-_tabListIconWidth),_horizBarWidth)}else{if(c.showScrollBar!=_noScrollBar){a=Math.max(c.w-_scrollBarWidth,_horizBarWidth)}}c.divLyr.style.width=""+a+"px"}c.onChangeTabList(c.selIndex)}}function NaviBarWidget_contextMenuCB(a){a=getEvent(a);var b=getWidget(this);if(b.cb){b.cb()}if(b.showMenu){b.showMenu(a)}return false}function NaviBarWidget_getTabIndexByName(c){var f=this,b=f.items,a=b.length;for(var e=0;e<a;e++){if(b[e].name==c){return e}}return -1}function NaviBarWidget_getTabIndexByValue(e){var f=this,b=f.items,a=b.length;for(var c=0;c<a;c++){if(b[c].value==e){return c}}return -1}function NaviBarWidget_getTabID(a){var c=this,b=c.items;if((a!=null)&&(a>=0)&&(a<b.length)){return b[a].idx}return null}function NaviBarWidget_getTab(b){var c=this,a=c.items;if((b!=null)&&(b>=0)&&(b<a.length)){return a[b]}return null}function NaviBarWidget_findTabIndex(e){var f=this,b=f.items,a=b.length;for(var c=0;c<a;c++){if(e==b[c]){return c}}return -1}function NaviBarWidget_getSelectedTab(){var b=this;var a=b.getSelection();if(a){return b.getTab(a.index)}return null}_trIndent=18;function newTreeWidget(a,m,e,p,r,c,k,q,l,g,n,f){var b=newScrolledZoneWidget(a,2,4,m,e,k);b.icns=p;b.sub=new Array;b.clickCB=r;b.doubleClickCB=c;b.expandCB=q;b.collapseCB=l;b.deleteCB=g;b.minIcon=n;b.plusIcon=f;b.mouseOverCB=null;b.rightClickMenuCB=null;b.mouseOverTooltip=false;b.dragDrop=null;b.oldInit=b.init;b.init=TreeWidget_init;b.getHTML=TreeWidget_getHTML;b.getSelections=TreeWidget_getSelections;b.getSelectedItem=TreeWidget_getSelectedItem;b.getSelectedItems=TreeWidget_getSelectedItems;b.getCheckedItems=TreeWidget_getCheckedItems;b.setDragDrop=TreeWidget_setDragDrop;b.setFocus=TreeWidget_setFocus;b.add=TreeWidget_add;b.setRightClickMenuCB=TreeWidget_setRightClickMenuCB;b.findByData=TreeWidget_findByData;b.findById=TreeWidget_findById;b.selectByData=TreeWidget_selectByData;b.selectById=TreeWidget_selectById;b.unselect=TreeWidget_unselect;b.treeLyr=null;b.elems=new Array;b.elemCount=0;b.selId=-1;b.selIds=new Array;b.multiSelection=false;b.hlPath=false;b.hlElems=new Array;b.iconOrientVertical=true;b.deleteAll=TreeWidget_deleteAll;b.rebuildHTML=TreeWidget_rebuildHTML;b.iconW=16;b.iconH=16;b.initialIndent=0;b.buildElems=TreeWidget_buildElems;b.getCount=TreeWidget_getCount;if(window._TreeWidgetElemInstances==null){window._TreeWidgetElemInstances=new Array}b.dispIcnFuncName="dispIcn";b.setTooltipOnMouseOver=TreeWidget_setTooltipOnMouseOver;b.setMouseOverCB=TreeWidget_setMouseOverCB;b.setMultiSelection=TreeWidget_setMultiSelection;b.setHighlightPath=TreeWidget_setHighlightPath;b.highlightPath=TreeWidget_highlightPath;b.unhlPath=TreeWidget_unhlPath;return b}function TreeWidget_unselect(){var e=this;if(e.selId>=0){var c=_TreeWidgetElemInstances[e.selId];c.unselect();e.selId=-1}if(e.multiSelection){var a=e.selIds.length,f;for(var b=a-1;b>=0;b--){var c=_TreeWidgetElemInstances[e.selIds[b]];if(c){c.unselect()}}e.selIds.length=0;e.layer._BOselIds=""}e.unhlPath()}function TreeWidget_selectByData(c,a){var e=this,b=e.findByData(c);if(b){b.select(a)}}function TreeWidget_selectById(e,a){var c=this,b=c.findById(e);if(b){b.select(a)}}function TreeWidget_findByData(e){var f=this,b=f.sub,c=null;for(var a in b){c=b[a].findByData(e);if(c){return c}}return null}function TreeWidget_findById(f){var e=this,b=e.sub,c=null;for(var a in b){c=b[a].findById(f);if(c){return c}}return null}function TreeWidget_add(e,b){var f=this,c=f.sub,a=c.length;e.treeView=f;c[a]=e;e.expanded=(a==0);if(b){e.extraIndent=b}return e}function TreeWidget_getHTML(){var k=this,g=k.sub,b=g.length,c=new Array(b+3),e=0;c[e++]=k.beginHTML()+'<span id="treeCont_'+k.id+'"  >';for(var f in g){c[e++]=g[f].getHTML(k.initialIndent,f==0)}c[e++]="</span>"+k.endHTML();return c.join("")}function TreeWidget_deleteAll(){var b=this.sub;for(var a in b){b[a].deleteAll();b[a]=null}b.length=0;if(this.elems){this.elems.length=0}}function TreeWidget_rebuildHTML(){var l=this,k=l.sub,b=k.length,c=new Array(b),e=0,g=l.initialIndent;for(var f in k){c[e++]=k[f].getHTML(g,f==0)}l.treeLyr.innerHTML=c.join("");l.selId=-1;l.layer._BOselId=-1;l.selIds.length=0;l.layer._BOselIds="";this.buildElems()}function TreeWidget_init(){this.oldInit();var b=this.treeLyr=getLayer("treeCont_"+this.id);if(this.dragDrop){this.dragDrop.attachCallbacks(this.layer)}var a=this.layer._BOselId;if(a!=null){this.selId=a}var e=this.layer._BOselIds;if(e!=null&&e!=""){this.selIds.length=0;this.selIds=e.split(";")}var c=this.sub}function TreeWidget_buildElems(elem){with(this){if(elem==null){elem=this}else{var pos=elems.length;elems[pos]=elem;elem.elemPos=pos}var subArr=elem.sub,len=subArr.length;for(var i=0;i<len;i++){buildElems(subArr[i])}}}function TreeWidget_getSelectedItem(){var a=this.selId;return(a>=0)?_TreeWidgetElemInstances[a]:null}function TreeWidget_getSelections(){var c=this;if(c.multiSelection){return c.getSelectedItems()}else{var a=c.getSelectedItem(),b=new Array;if(a!=null){b[0]=a}return b}}function TreeWidget_setFocus(a){var b=_TreeWidgetElemInstances[a];if(b!=null){b.init();safeSetFocus(b.domElem)}}function TreeWidget_setDragDrop(c,b,a,e){this.dragCB=c;this.acceptDropCB=b;this.dropCB=a;this.dragEndCB=e;this.dragDrop=newDragDropData(this,TreeWidget_dragStartCB,TreeWidget_dragCB,TreeWidget_dragEndCB,TreeWidget_acceptDropCB,TreeWidget_leaveDropCB,TreeWidget_dropCB)}function TreeWidget_dragStartCB(f){var c=f.getSelections(),b=f.iconOrientVertical;f.dragCB(f);if(c&&c.length==1){var e=c[0];var a=e.iconId;newTooltipWidget().show(true,e.getDragTooltip(),a>=0?f.icns:null,f.iconW,f.iconH,b?0:f.iconW*a,b?f.iconH*a:0)}}function TreeWidget_setRightClickMenuCB(a){this.rightClickMenuCB=a}function TreeWidget_getCount(){var a=this;if(a.sub!=null){return a.sub.length}else{return 0}}function TreeWidget_setTooltipOnMouseOver(a){this.mouseOverTooltip=a}function TreeWidget_setMouseOverCB(a){this.mouseOverCB=a}function TreeWidget_dragCB(a){newTooltipWidget().setPos()}function TreeWidget_dragEndCB(a){newTooltipWidget().show(false);if(a.dragEndCB){a.dragEndCB()}}function TreeWidget_dragOverEnterCB(a,f){var b=_TreeWidgetElemInstances[f];if(a.ondrop==null){b.treeView.dragDrop.attachCallbacks(a,true);a.domEltID=f}var c=_ddData[a._dragDropData],b=_curWin.event;b.dataTransfer.dropEffect=b.ctrlKey?"copy":"move";if(c.acceptDropCB(window._globalDDD,c.widget,b.ctrlKey,b.ctrlKey?false:b.shiftKey,a,false)){b.returnValue=false}b.cancelBubble=true}function TreeWidget_acceptDropCB(f,e,c,a,b){return e.acceptDropCB(f,e,c,a,b)}function TreeWidget_leaveDropCB(e,c,b,a){if(c.dropWidget&&c.dropWidget.layer){if(c.dropWidget.layer.className!=c.dropWidget.nonselectedClass){c.dropWidget.layer.className=c.dropWidget.nonselectedClass}}}function TreeWidget_dropCB(g,e,c,a,b,f){newTooltipWidget().show(false);e.dropCB(g,e,c,a)}function TreeWidget_setMultiSelection(a){if((!this.multiSelection&&a)||(this.multiSelection&&!a)){this.unselect()}this.multiSelection=a}function TreeWidget_getSelectedItems(){var e=new Array;var a=this.selIds.length,f,c=0;for(var b=0;b<a;b++){f=this.selIds[b];if(f>=0){e[c]=_TreeWidgetElemInstances[f];c++}}return e}function TreeWidget_getCheckedItems(){var c=new Array;var a=_TreeWidgetElemInstances.length,e=0;for(var b=0;b<a;b++){elem=_TreeWidgetElemInstances[b];if(elem.isChecked()){c[e]=elem;e++}}return c}function TreeWidget_setHighlightPath(a){this.hlPath=a;if(!a){this.unhlPath()}}function TreeWidget_unhlPath(){var e=this,a=e.hlElems.length;var c,f;if(a>0){for(var b=0;b<a;b++){c=e.hlElems[b];c.init();f=c.domElem;if(f==null){return}if(c.isSelected()){f.className=c.selectedClass}else{f.className=c.nonselectedClass}}e.hlElems.length=0}}function TreeWidget_highlightPath(e){var c=this;if(!c.hlPath){return}c.unhlPath();var a=_TreeWidgetElemInstances[e];c.hlElems[c.hlElems.length]=a;a.domElem.className=a.selectedClass;if(a.elemPos==-1){c.buildElems()}var b=a.par;while(b){b.init();b.domElem.className=b.hlClass;c.hlElems[c.hlElems.length]=b;b=b.par}if(a.isNode()){hlVisibleChildren(a,c.hlElems)}}function hlVisibleChildren(f,b){if(f.expanded&&!f.isIncomplete){var a=f.sub.length;for(var c=0;c<a;c++){var e=f.sub[c];b[b.length]=e;e.init();e.domElem.className=e.hlClass;if(e.isNode()){hlVisibleChildren(e,b)}}}}function newTreeWidgetElem(l,a,b,f,m,p,g,n,k,e){var c=new Object;c.elemPos=-1;if(window._TreeWidgetElemInstances==null){window._TreeWidgetElemInstances=new Array}c.enableDoubleClick=e;c.expanded=false;c.generated=false;c.iconId=l;c.iconSelId=m?m:l;c.tooltip=p;c.customTooltip=false;c.iconAlt=g;c.isHTML=false;c.isCheck=false;c.checked=false;c.check=TreeWidgetElem_check;c.isChecked=TreeWidgetElem_isChecked;c.checkCB=null;c.name=a;c.par=null;c.userData=b;c.sub=new Array;c.treeView=null;c.id=_TreeWidgetElemInstances.length;c.layer=null;c.plusLyr=null;c.icnTooltipLyr=null;c.icnLyr=null;c.checkElem=null;c.domElem=null;c.txtTooltipLyr=null;c.toggleLyr=null;c.blackTxt=(n)?n:"treeNormal";c.grayTxt="treeGray";c.selectedClass=(k)?k:"treeSelected";c.nonselectedClass=c.blackTxt;c.feedbackDDClass="treeFeedbackDD";c.hlClass="treeHL";c.cursorClass=null;c.help=f;_TreeWidgetElemInstances[c.id]=c;c.getHTML=TreeWidgetElem_getHTML;c.init=TreeWidgetElem_init;c.add=TreeWidgetElem_add;c.select=TreeWidgetElem_select;c.unselect=TreeWidgetElem_unselect;c.getNextPrev=TreeWidgetElem_getNextPrev;c.getHiddenParent=TreeWidgetElem_getHiddenParent;c.nodeIndent=0;c.getTooltip=TreeWidgetElem_getTooltip;c.getDragTooltip=TreeWidgetElem_getDragTooltip;c.deleteAll=TreeWidget_deleteAll;c.setGrayStyle=TreeWidgetElem_setGrayStyle;c.isGrayStyle=TreeWidgetElem_isGrayStyle;c.findByData=TreeWidgetElem_findByData;c.findById=TreeWidgetElem_findById;c.isIncomplete=false;c.querycompleteCB=null;c.setIncomplete=TreeWidgetElem_setIncomplete;c.finishComplete=TreeWidgetElem_finishComplete;c.setEditable=TreeWidgetElem_setEditable;c.isLeaf=TreeWidgetElem_isLeaf;c.isNode=TreeWidgetElem_isNode;c.isSelected=TreeWidgetElem_isSelected;c.htmlWritten=false;c.showCustomTooltipCB=null;c.hideCustomTooltipCB=null;c.setCursorClass=TreeWidgetElem_setCursorClass;return c}function TreeWidgetElem_checkCB(a,c){var b=_TreeWidgetElemInstances[c];b.checked=a.checked;if(b.checkCB){b.checkCB(b,c)}}function TreeWidgetElem_isChecked(){var a=this;return(a.isCheck?a.checked:false)}function TreeWidgetElem_check(a){var b=this;if(b.isCheck){b.checked=a;if(b.htmlWritten){b.init();b.checkElem.checked=a}}}function TreeWidgetElem_EditNormalBehaviour(a){eventCancelBubble(a);return true}function TreeWidgetElem_EditBlurCB(){setTimeout("TreeWidgetElem_EditKeyCancel("+this.widID+")",1)}_globTreeTxtvalue="";function TreeWidgetElem_EditKeyDown(b){eventCancelBubble(b);var a=eventGetKey(b),c=_TreeWidgetElemInstances[this.widID];if(a==27){setTimeout("TreeWidgetElem_EditKeyCancel("+this.widID+")",1)}else{if(a==13){_globTreeTxtvalue=this.value;setTimeout("TreeWidgetElem_EditKeyAccept("+this.widID+")",1)}}}function TreeWidgetElem_EditKeyCancel(b){var a=_TreeWidgetElemInstances[b];a.showEditInput(false)}function TreeWidgetElem_EditKeyAccept(b){var a=_TreeWidgetElemInstances[b];if(a.validChangeNameCB){if(a.validChangeNameCB(_globTreeTxtvalue)==false){return}}a.change(null,_globTreeTxtvalue);a.showEditInput(false);if(a.changeNameCB){a.changeNameCB()}}function TreeWidgetElem_setEditable(a,e,b){var c=this;if(a){c.changeNameCB=e;c.validChangeNameCB=b}c.isEditable=a}function TreeWidgetElem_triggerDD(){var f=_treeWClickedW,c=_curWin.event;if(f&&(f.clicked)&&(c.button==_leftBtn)){if(f.initialX!=null){var b=eventGetX(c),g=eventGetY(c),a=3;if((b<(f.initialX-a))||(b>(f.initialX+a))||(g<(f.initialY-a))||(g>(f.initialY+a))){this.dragDrop();f.clicked=false}}}}function TreeWidgetElem_mouseUp(){var b=_treeWClickedW,a=_curWin.event;b.select(null,a);b.domElem.onmouseup=null}function TreeWidgetElem_init(c){var k=this;if(k.layer==null){var e=k.sub,a=e.length,g=(a>0)||k.isIncomplete;k.layer=c?c:getLayer(_codeWinName+"TWe_"+k.id);if(k.layer==null){return}var l=k.layer.childNodes,f=l.length;k.plusLyr=g?l[0].childNodes[1]:null;k.icnTooltipLyr=g?l[0].childNodes[0]:null;k.icnLyr=(k.iconId>-1)?l[g?1:0]:null;k.checkElem=k.isCheck?l[f-2]:null;k.domElem=l[f-1];k.txtTooltipLyr=l[f-1].childNodes[1];if(k.layer.nextSibling&&k.layer.nextSibling.id==_codeWinName+"trTog"+k.id){k.toggleLyr=k.layer.nextSibling}if(k.treeView.mouseOverTooltip||k.treeView.mouseOverCB){k.domElem.onmouseout=TreeFuncMouseout}if(g){addDblClickCB(k.plusLyr,_tpdb)}if(g&&k.generated){for(var b in e){e[b].init()}}if(k.enableDoubleClick){addDblClickCB(k.domElem,_tpdb)}}}function TreeIdToIdx(b){if(b){var c=b.id;if(c){var a=c.lastIndexOf("TWe_");if(a>=0){return parseInt(c.slice(a+4))}else{return -1}}else{return TreeIdToIdx(b.parentNode)}}return -1}function TreeFuncMouseout(a){_tmoc(this,TreeIdToIdx(this),false,a)}function _tmvc(a,b){_tmoc(a,TreeIdToIdx(a),true,b)}function _tpl(a,b){TreeWidget_clickCB(TreeIdToIdx(a),true,b,true);return false}function _tkl(a,c){var b=eventGetKey(c);if(b==13){return _tpl(a,c)}}function _tkt(a,c){var b=eventGetKey(c);if(b==13){return _tpt(a,c)}}function _tpt(a,b){TreeWidget_clickCB(TreeIdToIdx(a),false,b,true);return false}function _tpdb(a){treeDblClickCB(TreeIdToIdx(this),_ie?event:a);return false}function _tfcc(a,b){treeFCCB(a,TreeIdToIdx(a),true,b);a.onblur=_tblc}function _tblc(a){treeFCCB(this,TreeIdToIdx(this),false,a)}function TreeWidgetElem_getHTML(indent,isFirst){var s="";with(this){htmlWritten=true,isRoot=(par==null);var len=sub.length,exp=(len>0)||isIncomplete,a=new Array,i=0;if(this.extraIndent){indent+=_trIndent*extraIndent}var keyCB='onkeydown=" return '+_codeWinName+'._tkt(this,event)" ';var mouseCB='onclick="return '+_codeWinName+'._tpt(this,event)" ';if(treeView.mouseOverTooltip||treeView.mouseOverCB){mouseCB+='onmouseover="'+_codeWinName+'._tmvc(this,event)" '}var contextMenu="";if(treeView.rightClickMenuCB!=null){contextMenu=' oncontextmenu="'+_codeWinName+".treeContextMenuCB('"+id+"', event);return false\" "}var acceptDD="";if((treeView.acceptDropCB!=null)&&(_ie)){acceptDD=' ondragenter="'+_codeWinName+".TreeWidget_dragOverEnterCB(this,'"+id+"');\" ";acceptDD+=' ondragover="'+_codeWinName+".TreeWidget_dragOverEnterCB(this,'"+id+"');\" "}a[i++]='<div id="'+_codeWinName+"TWe_"+id+'"'+contextMenu+" class=trElt>";var onkeydown='onkeydown="return '+_codeWinName+'._tkl(this,event)" ';var onclick='onclick="return '+_codeWinName+'._tpl(this,event)" ';if(exp){var expIcon;var iconTooltip;if(expanded){if(treeView.minIcon!=null){expIcon=treeView.minIcon}else{expIcon=_skin+"../min.gif"}iconTooltip=_collapseNode.replace("%1",name)}else{if(treeView.plusIcon!=null){expIcon=treeView.plusIcon}else{expIcon=_skin+"../plus.gif"}iconTooltip=_expandNode.replace("%1",name)}a[i++]="<a tabindex=0 "+onclick+" "+onkeydown+' href="javascript:doNothing();" role="link"><span style="display:none">'+iconTooltip+'</span><img class=trPlus src="'+expIcon+'"/></a>'}if(iconId>-1){var iconClass="trIcn"+(exp||isRoot?"Plus":"");if(this.cursorClass){iconClass+=" "+this.cursorClass}a[i++]="<img tabindex=0 "+mouseCB+" "+keyCB+'class="'+iconClass+'" '+attr("src",_skin+"../transp.gif")+attr("alt",iconAlt)+' align=top style="'+backImgOffset(treeView.icns,(treeView.iconOrientVertical?0:treeView.iconW*(expanded?iconSelId:iconId)),(treeView.iconOrientVertical?treeView.iconH*(expanded?iconSelId:iconId):0))+'" >'}else{if(!exp&&!isRoot){a[i++]="<img tabindex=-1 class=trSep "+attr("src",_skin+"../transp.gif")+">"}}if(isCheck){a[i++]='<input type=checkbox style="margin:0px;" onclick="'+_codeWinName+".TreeWidgetElem_checkCB(this,'"+id+"')\""+(this.checked?" checked":"")+">"}var textClass=nonselectedClass;if(this.cursorClass){textClass+=" "+this.cursorClass}var textTooltip=this.getTooltip();a[i++]='<a href="javascript:doNothing();" '+mouseCB+" "+keyCB+" tabindex=0 "+acceptDD+' class="'+textClass+'" role="link">';a[i++]=(isHTML?name:convStr(name));a[i++]='<span style="display:none">'+textTooltip+"</span>";a[i++]="</a>";a[i++]="</div>";if(exp){a[i++]='<div id="'+_codeWinName+"trTog"+id+'" style="margin-left:18px;display:'+(expanded?"":"none")+'">'}if(expanded){generated=true;for(var j=0;j<len;j++){a[i++]=sub[j].getHTML(0,j==0)}}if(exp){nodeIndent=indent;a[i++]="</div>"}}return a.join("")}function TreeWidgetElem_setGrayStyle(b){var c=this,a=b?c.grayTxt:c.blackTxt;if(a!=c.nonselectedClass){c.nonselectedClass=a;c.init();if(c.domElem&&(c.domElem.className!=c.selectedClass)){c.domElem.className=a}}}function TreeWidgetElem_isGrayStyle(){return this.nonselectedClass==this.grayTxt}function TreeWidgetElem_setIncomplete(a){this.isIncomplete=true;this.querycompleteCB=a}function TreeWidgetElem_finishComplete(){this.isIncomplete=false;TreeWidget_toggleCB(this.id);this.treeView.buildElems()}function TreeWidgetElem_findByData(e){var f=this;if(f.userData==e){return f}var b=f.sub;for(var a in b){var c=b[a].findByData(e);if(c!=null){return c}}return null}function TreeWidgetElem_findById(f){var e=this;if(e.id==f){return e}var b=e.sub;for(var a in b){var c=b[a].findById(f);if(c!=null){return c}}return null}function treeInitDropFunc(a,c){var b=_TreeWidgetElemInstances[c];if(a.ondrop==null){b.treeView.dragDrop.attachCallbacks(a,true);a.domEltID=c}}function TreeWidget_toggleCB(e,b){var c=_TreeWidgetElemInstances[e];if(c.sub.length==0){c.plusLyr.style.visibility="hidden";return}c.expanded=!c.expanded;c.init();if(b){dispIcn(e)}else{setTimeout(c.treeView.dispIcnFuncName+"("+e+")",1)}var a=c.treeView;if(c.expanded&&a.expandCB){a.expandCB(c.userData)}if(!c.expanded&&a.collapseCB){a.collapseCB(c.userData)}}function dispIcn(eId){var e=_TreeWidgetElemInstances[eId];with(e){if(expanded&&!generated){generated=true;var a=new Array,i=0,len=sub.length,newInd=nodeIndent+_trIndent;for(var j=0;j<len;j++){a[i++]=sub[j].getHTML(newInd,j==0)}toggleLyr.innerHTML=a.join("")}toggleLyr.style.display=expanded?"block":"none";if(expanded){if(treeView.minIcon!=null){plusLyr.src=treeView.minIcon}else{plusLyr.src=_skin+"../min.gif"}}else{if(treeView.plusIcon!=null){plusLyr.src=treeView.plusIcon}else{plusLyr.src=_skin+"../plus.gif"}}var tooltip=expanded?_collapseNode:_expandNode;tooltip=tooltip.replace("%1",name);icnTooltipLyr.innerHTML=tooltip;txtTooltipLyr.innerHTML=getTooltip();if(icnLyr&&icnLyr.childNodes&&icnLyr.childNodes.length>1){var iconL=icnLyr.childNodes[1];changeOffset(iconL,treeView.iconOrientVertical?0:treeView.iconW*(expanded?iconSelId:iconId),treeView.iconOrientVertical?treeView.iconH*(expanded?iconSelId:iconId):0)}}}function TreeWidgetElem_add(elem){with(this){elem.treeView=treeView;elem.par=this;sub[sub.length]=elem}return elem}function TreeWidgetElem_getHiddenParent(){var a=this.par;if(a==null){return null}if(!a.expanded){return a}return;a.getHiddenParent()}function TreeWidgetElem_getNextPrev(delta){with(this){if(elemPos==-1){treeView.buildElems()}var newPos=elemPos+delta;if((newPos>=0)&&(newPos<treeView.elems.length)){var ret=treeView.elems[newPos];var hidden=ret.getHiddenParent();if(hidden!=null){return ret.getNextPrev(delta)}else{return ret}}else{return null}}}function TreeWidgetElem_scroll(f,a){var c=Math.max(0,a.offsetHeight-20),g=a.scrollTop;var b=getPos(f,a);var k=b.offsetTop,e=f.offsetHeight;if((k-g+e)>c){a.scrollTop=k+e-c}if((k-g)<0){a.scrollTop=k}}function TreeWidgetElem_unselect(){var o=this;with(o){init();if(domElem){domElem.className=o.nonselectedClass}treeView.selId=-1;if(treeView.multiSelection){var idx=arrayFind(treeView,"selIds",id);if(idx>-1){arrayRemove(treeView,"selIds",idx);treeView.layer._BOselIds="";var len=treeView.selIds.length;for(var i=0;i<len;i++){if(treeView.layer._BOselIds==""){treeView.layer._BOselIds=""+treeView.selIds[i]}else{treeView.layer._BOselIds+=";"+treeView.selIds[i]}}}}}}function TreeWidgetElem_select(setFocus,ev,noSendClickCB,isFromKeybArrow){var coll=new Array;var par=this.par;while(par){if(!par.expanded){coll[coll.length]=par}par=par.par}var cLen=coll.length;for(var i=cLen-1;i>=0;i--){TreeWidget_toggleCB(coll[i].id,true)}if(cLen>0){this.select(setFocus,ev,noSendClickCB,isFromKeybArrow)}if(this.treeView.multiSelection){TreeWidgetElem_multiSelect(this,setFocus,ev,noSendClickCB,isFromKeybArrow);return}if(noSendClickCB==null){noSendClickCB=false}with(this){if(treeView.selId!=id){if(treeView.selId>=0){var prev=_TreeWidgetElemInstances[treeView.selId];prev.init();if(prev.domElem){prev.domElem.className=prev.nonselectedClass}}treeView.selId=id;init();treeView.layer._BOselId=id;var de=domElem;if(de==null){return}if(treeView.hlPath){treeView.highlightPath(id)}else{de.className=selectedClass}if(setFocus){safeSetFocus(de)}TreeWidgetElem_scroll(de,treeView.layer)}if((treeView.clickCB)&&(!noSendClickCB)){treeView.clickCB(userData,isFromKeybArrow!=null?isFromKeybArrow:false)}}}_startShift=null;function TreeWidgetElem_multiSelectCtrl(){}function TreeWidget_clickCB(g,c,b,a){eventCancelBubble(b);var f=_TreeWidgetElemInstances[g];if(f==null){return}f.init();if(c&&(f.sub.length>0)){TreeWidget_toggleCB(g)}else{if(c&&f.isIncomplete&&f.querycompleteCB){f.querycompleteCB()}else{f.select(null,b)}}if(_curDoc.onmousedown){_curDoc.onmousedown(b)}if(a&&_ie){window._treeWClickedW=f;f.init();f.clicked=true;f.initialX=eventGetX(b);f.initialY=eventGetY(b);if(_ie&&f.domElem){f.domElem.onmousemove=TreeWidgetElem_triggerDD}}if(_moz&&f.domElem){setTimeout("_TreeWidgetElemInstances["+g+"].domElem.focus()",1)}return false}function treeDblClickCB(f,b){eventCancelBubble(b);var c=_TreeWidgetElemInstances[f],a=c.treeView;if(c.sub.length>0){TreeWidget_toggleCB(f)}else{if(c.isIncomplete&&c.querycompleteCB){c.querycompleteCB();return}}if(c.isEditable){c.showEditInput(true)}else{if(a.doubleClickCB){a.doubleClickCB(c.userData)}}}function TreeWidgetElem_UpdateTooltip(a,b){var c=_TreeWidgetElemInstances[a];if(c){c.init();if(c.domElem!=null){c.domElem.title=c.getTooltip(b)}}}function TreeWidgetElem_getDragTooltip(){var a=this;if(a.obj&&a.obj.getDragTooltip){return a.obj.getDragTooltip()}return a.name}function TreeWidgetElem_getTooltip(a){var c="",e=this;var b=false;if(e.treeView.multiSelection){b=(arrayFind(e.treeView,"selIds",e.id)>-1)}else{b=(e.treeView.selId==e.id)}if(a||b){c=_selectedLab+" "}if((e.sub.length>0)||(e.isIncomplete)){if(e.expanded){c+=" "+_expandedLab+" "}else{c+=" "+_collapsedLab+" "}}if(e.advTooltip){c+=" ("+e.advTooltip+")"}if(e.getLevel){c+=" ("+_level+" "+e.getLevel()+")"}return c}function treeFCCB(f,g,a,c){var b=_TreeWidgetElemInstances[g];if((b==null)||b.treeView.mouseOverTooltip){return}if(a){if(b.customTooltip&&b.showCustomTooltipCB){b.showCustomTooltipCB(b.userData,c);b.init()}else{f.title=b.getTooltip()}}else{if(b.customTooltip&&b.hideCustomTooltipCB){b.hideCustomTooltipCB()}else{f.title=""}}}function _tmoc(f,g,c,b){f.style.cursor=_hand;var a=_TreeWidgetElemInstances[g];if(a==null){return}if(a.treeView.mouseOverTooltip){if(c){if(a.customTooltip&&a.showCustomTooltipCB){a.showCustomTooltipCB(a.userData,b);a.init()}else{f.title=a.tooltip?a.tooltip:""}}else{if(a.customTooltip&&a.hideCustomTooltipCB){a.hideCustomTooltipCB()}else{f.title=""}}}if(a.treeView.mouseOverCB){a.treeView.mouseOverCB(a)}}function treeContextMenuCB(c,b){var a=_TreeWidgetElemInstances[c];if(a){a.treeView.rightClickMenuCB(c,_ie?_curWin.event:b)}}function TreeWidgetElem_isLeaf(){return(this.sub.length==0&&!this.isIncomplete)}function TreeWidgetElem_isNode(){return(!this.isLeaf())}function TreeWidgetElem_isSelected(){var b=this;if(b.treeView.multi){var a=arrayFind(b.treeView,"selIds",b.id);return(a>=0)}else{return(b.id==b.treeView.selId)}}function TreeWidgetElem_setCursorClass(a){this.cursorClass=a}if(window._DHTML_LIB_DIALOG_JS_LOADED==null){_DHTML_LIB_DIALOG_JS_LOADED=true;DialogBoxWidget_modals=new Array;DialogBoxWidget_instances=new Array;DialogBoxWidget_current=null;_promptDlgInfo=0;_promptDlgWarning=1;_promptDlgCritical=2;_dlgTitleLBorderToTxt=20;_dlgTitleHeight=25;_dlgTitleMarginBottom=4;_dlgTitleRBorderToClose=10;_dlgTitleCloseBtnImgFile="dialogtitle.gif";_dlgTitleCloseBtnW=11;_dlgTitleCloseBtnH=10;_dlgTitleCloseBtnDy=26;_dlgTitleCloseBtnHoverDy=37;_dlgBottomMargin=14}function newDialogBoxWidget(a,g,b,l,m,k,f,e){var c=newWidget(a);c.title=g;c.width=b;c.height=l;c.defaultCB=m;c.cancelCB=k;c.noCloseButton=f?f:false;c.isAlert=e;c.closeCB=null;c.resizeable=false;c.oldMouseDown=null;c.oldCurrent=null;c.modal=null;c.hiddenVis=new Array;c.lastLink=null;c.firstLink=null;c.titleLayer=null;c.defaultBtn=null;c.divLayer=null;c.oldInit=c.init;c.oldShow=c.show;c.init=DialogBoxWidget_init;c.setResize=DialogBoxWidget_setResize;c.beginHTML=DialogBoxWidget_beginHTML;c.endHTML=DialogBoxWidget_endHTML;c.show=DialogBoxWidget_Show;c.center=DialogBoxWidget_center;c.focus=DialogBoxWidget_focus;c.setTitle=DialogBoxWidget_setTitle;c.getContainerWidth=DialogBoxWidget_getContainerWidth;c.getContainerHeight=DialogBoxWidget_getContainerHeight;DialogBoxWidget_instances[a]=c;c.modal=newWidget("modal_"+a);c.placeIframe=DialogBoxWidget_placeIframe;c.oldResize=c.resize;c.resize=DialogBoxWidget_resize;c.attachDefaultButton=DialogBoxWidget_attachDefaultButton;c.unload=DialogBoxWidget_unload;c.close=DialogBoxWidget_close;c.setCloseCB=DialogBoxWidget_setCloseCB;c.setNoCloseButton=DialogBoxWidget_setNoCloseButton;if(!_ie){if(c.width!=null){c.width=Math.max(0,b+4)}if(c.height!=null){c.height=Math.max(0,l+4)}}return c}function DialogBoxWidget_setResize(a,c,e,b,g){var f=this;f.resizeable=true;f.resizeCB=a;f.minWidth=c?c:50;f.minHeight=e?e:50;f.noResizeW=b;f.noResizeH=g}function DialogBoxWidget_setTitle(b){var a=this;a.title=b;if(a.titleLayer==null){a.titleLayer=getLayer("titledialog_"+this.id)}a.titleLayer.innerHTML=convStr(b)}function DialogBoxWidget_setCloseIcon(a,b){changeOffset(a,0,(b==1?0:18))}function DialogBoxWidget_beginHTML(){with(this){var moveableCb=' onselectstart="return false" ondragstart="return false" onmousedown="'+_codeWinName+".DialogBoxWidget_down(event,'"+id+"',this,false);return false;\" ";var titleBG="background-image:url("+_skin+"dialogtitle.gif)";var mdl='<div onselectstart="return false" onmouseup="'+_codeWinName+".DialogBoxWidget_keepFocus('"+this.id+'\');" onmousedown="'+_codeWinName+'.eventCancelBubble(event)" border="0" hspace="0" vspace="0" src="'+_skin+'../transp.gif" id="modal_'+id+'" style="background-color:#888888;opacity:0.3;filter:alpha(opacity:30);position:absolute;top:0px;left:0px;width:1px;height:1px">'+(_ie?img(_skin+"../transp.gif","100%","100%",null):"")+"</div>";var btn="";if(_dtd4){btn='<td style="padding-right:'+_dlgTitleRBorderToClose+'px"><div id="dialogClose_'+id+'" class="dlgCloseBtn" title="'+_closeDialog+'"></div></td>'}else{btn='<td style="padding-right:'+_dlgTitleRBorderToClose+'px">'+simpleImgOffset(_skin+_dlgTitleCloseBtnImgFile,_dlgTitleCloseBtnW,_dlgTitleCloseBtnH,0,_dlgTitleCloseBtnDy,"dialogClose_"+id,null,_closeDialog)+"</td>"}var closeBtn='<td class="dlgCloseArea" align="left" valign="middle"><table border="0" cellspacing="0" cellpadding="0"><tbody><tr style="height:'+_dlgTitleHeight+'px">'+btn+"</tr></tbody></table></td>";var dlgtitle='<table style="height:'+_dlgTitleHeight+'" class="dlgTitle" width="100%"  border="0" cellspacing="0" cellpadding="0"><tr valign="top" style="height:'+_dlgTitleHeight+'px"><td '+moveableCb+' style="cursor:move;padding-left:'+_dlgTitleLBorderToTxt+'px;" width="100%" valign="middle" align="left"><nobr><span id="titledialog_'+id+'" tabIndex="0" class="titlezone">'+convStr(title)+"</span></nobr></td>"+closeBtn+"</tr></table>";var s="";s=mdl;var dims=sty("width",width?(""+width+"px"):null)+sty("height",height?(""+Math.max(0,height+(_moz?-2:0))+"px"):null);s+='<button style="position:absolute;left:-30px;top:-30px; visibility:hidden" id="firstLink_'+this.id+'" onfocus="'+_codeWinName+".DialogBoxWidget_keepFocus('"+this.id+"');return false;\" ></button>";s+='<table border="0" cellspacing="0" cellpadding="0" id="'+id+'" style="display:none;padding:0px;visibility:'+_hide+";position:absolute;top:-2000px;left:-2000px;"+dims+'" '+(isAlert?'role="alertdialog"':'role="dialog"')+">";s+="<tr><td "+(_moz?'style="'+dims+'" ':"")+'class="dialogbox" id="td_dialog_'+id+'" onresize="'+_codeWinName+".DialogBoxWidget_resizeIframeCB('"+id+'\',this)"  valign="top">';s+='<table class="dlgBox2" width="100%" border="0" cellspacing="0" cellpadding="0"><tbody>';s+='<tr><td height="'+_dlgTitleHeight+'" valign="top">'+dlgtitle+"</td></tr>";s+='<tr><td class="dlgBody" valign="top" id="div_dialog_'+id+'">';return s}}function DialogBoxWidget_endHTML(){var a="</td></tr>";a+='<tr><td style="height:'+_dlgBottomMargin+'px;"></td></tr>';a+="</tbody></table>";a+="</td></tr></table>";a+='<button style="position:absolute;left:-30px;top:-30px; visibility:hidden" id="lastLink_'+this.id+'" onfocus="'+_codeWinName+".DialogBoxWidget_keepFocus('"+this.id+"');return false;\" ></button>";return a}function DialogBoxWidget_getContainerWidth(){var a=this;return a.width-(2+2)}function DialogBoxWidget_getContainerHeight(){var a=this;return a.height-(2+18+2+2+2)}function DialogBoxWidget_close(b){var a=DialogBoxWidget_instances[b];if(a){a.show(false);if(a.cancelCB!=null){a.cancelCB()}}}function DialogBoxWidget_setCloseCB(a){this.closeCB=a}function DialogBoxWidget_setNoCloseButton(a){if(this.noCloseButton!==a){this.noCloseButton=a;if(this.initialized()){this.closeButton.style.visibility=this.noCloseButton?_hide:_show}}}function DialogBoxWidget_resizeIframeCB(b,a){DialogBoxWidget_instances[b].placeIframe()}function DialogBoxWidget_placeIframe(){var b=this;if(b.iframe){var a=b.td_lyr;if(a==null){b.td_lyr=a=getLayer("td_dialog_"+b.id)}b.iframe.resize(a.offsetWidth,a.offsetHeight);b.iframe.move(b.layer.offsetLeft,b.layer.offsetTop)}}function DialogBoxWidget_resize(a,b){var c=this;c.oldResize(a,b);if(c.iframe){c.iframe.resize(a,b)}}function DialogBoxWidget_init(){if(this.layer!=null){return}var a=this;a.oldInit();a.modal.init();a.lastLink=newWidget("lastLink_"+a.id);a.firstLink=newWidget("firstLink_"+a.id);a.lastLink.init();a.firstLink.init();if(_saf){a.webKitFocusElem=getLayer("webKitFocusElem"+a.id)}a.closeButton=getLayer("dialogClose_"+a.id);a.closeButton.style.visibility=a.noCloseButton?_hide:_show;a.closeButton.onmouseover=DialogBoxWidget_moverCloseBtn;a.closeButton.onmouseout=DialogBoxWidget_moverCloseBtn;a.closeButton.onclick=function(){a.close(a.id)}}function DialogBoxWidget_moverCloseBtn(a){var a=getEvent(a);var b=(a&&a.type=="mouseover")?true:false;if(_dtd4){this.className=b?"dlgCloseBtnHover":"dlgCloseBtn"}else{changeOffset(this,0,b?_dlgTitleCloseBtnHoverDy:_dlgTitleCloseBtnDy)}}function DialogBoxWidget_attachDefaultButton(a){this.defaultBtn=a;this.defaultBtn.setDefaultButton()}_theLYR=null;_dlgResize=null;function DialogBoxWidget_down(e,id,obj,isResize){_dlgResize=isResize;var o=DialogBoxWidget_instances[id],lyr=o.layer,mod=o.modal.layer;lyr.onmousemove=mod.onmousemove=eval("_curWin."+_codeWinName+".DialogBoxWidget_move");lyr.onmouseup=mod.onmouseup=eval("_curWin."+_codeWinName+".DialogBoxWidget_up");lyr.dlgStartPosx=mod.dlgStartPosx=parseInt(lyr.style.left);lyr.dlgStartPosy=mod.dlgStartPosy=parseInt(lyr.style.top);lyr.dlgStartx=mod.dlgStartx=eventGetX(e);lyr.dlgStarty=mod.dlgStarty=eventGetY(e);lyr.dlgStartw=mod.dlgStartw=o.getWidth();lyr.dlgStarth=mod.dlgStarth=o.getHeight();lyr._widget=mod._widget=o.widx;_theLYR=lyr;eventCancelBubble(e);if(lyr.setCapture){lyr.setCapture(true)}}function DialogBoxWidget_move(c){var g=_theLYR,f=getWidget(g);if(_dlgResize){var l=Math.max(f.minWidth,g.dlgStartw+eventGetX(c)-g.dlgStartx);var b=Math.max(f.minHeight,g.dlgStarth+eventGetY(c)-g.dlgStarty);f.resize(f.noResizeW?null:l,f.noResizeH?null:b);if(f.firstTR){if(!f.noResizeW){f.firstTR.style.width=l-4}if(!f.noResizeH){f.secondTR.style.height=b-44}}if(f.resizeCB){f.resizeCB(l,b)}}else{var a=Math.max(0,g.dlgStartPosx-g.dlgStartx+eventGetX(c));var k=Math.max(0,g.dlgStartPosy-g.dlgStarty+eventGetY(c));f.iframe.move(a,k);f.move(a,k)}eventCancelBubble(c);return false}function DialogBoxWidget_up(c){var f=getWidget(_theLYR),a=f.layer,b=f.modal.layer;a.onmousemove=b.onmousemove=null;a.onmouseup=b.onmouseup=null;if(a.releaseCapture){a.releaseCapture()}_theLYR=null}function DialogBoxWidget_keypress(a){eventCancelBubble(a);var b=DialogBoxWidget_current;if(b!=null){switch(eventGetKey(a)){case 13:if(b.yes&&!b.no){if(b.defaultCB){b.defaultCB()}return false}if(isTextArea(_ie?_curWin.event:a)){return true}if(b.defaultBtn!=null&&!b.defaultBtn.isDisabled()){b.defaultBtn.executeCB();return false}break;case 27:if(!b.noCloseButton){b.show(false);hideBlockWhileWaitWidget();if(b.cancelCB!=null){b.cancelCB()}}return false;break;case 8:return isTextInput(_ie?_curWin.event:a);break}}}function DialogBoxWidgetResizeModals(m){var l=DialogBoxWidget_current&&DialogBoxWidget_current.isDisplayed();if(l){DialogBoxWidget_current.setDisplay(false);DialogBoxWidget_current.iframe.setDisplay(false)}var g=[];for(var f=0,a=DialogBoxWidget_modals.length;f<a;f++){g[f]=DialogBoxWidget_modals[f].display;DialogBoxWidget_modals[f].display="none"}var c=documentWidth()+"px";var b=documentHeight()+"px";if(l){DialogBoxWidget_current.setDisplay(true);DialogBoxWidget_current.iframe.setDisplay(true)}for(var f=0,a=DialogBoxWidget_modals.length;f<a;f++){var k=DialogBoxWidget_modals[f];k.display=g[f];k.width=c;k.height=b}}function DialogBoxWidget_center(){var l=this;var g={modalDisplay:l.modal.css.display,layerDisplay:l.css.display};l.modal.css.display="none";l.css.display="none";var b=getScrollY(),c=getScrollX();l.modal.css.display=g.modalDisplay;l.css.display="block";var a=l.layer.offsetHeight,f=l.layer.offsetWidth;l.css.display=g.layerDisplay;var k=(winHeight()-a)/2;k=(k<0)?0:k;var e=(winWidth()-f)/2;e=(e<0)?0:e;l.move(Math.max(0,c+e),Math.max(0,b+k));l.placeIframe()}function DialogBoxWidget_Show(sh){with(this){m_sty=modal.css;l_sty=css;if(sh){if(!this.iframe){this.iframe=newWidget(getDynamicBGIFrameLayer().id);this.iframe.init()}oldCurrent=DialogBoxWidget_current;DialogBoxWidget_current=this;if(_ie){layer.onkeydown=eval("_curWin."+_codeWinName+".DialogBoxWidget_keypress");modal.layer.onkeydown=eval("_curWin."+_codeWinName+".DialogBoxWidget_keypress");window.attachEvent("onresize",eval("DialogBoxWidget_onWindowResize"))}else{_curDoc.addEventListener("keydown",eval("_curWin."+_codeWinName+".DialogBoxWidget_keypress"),false);window.addEventListener("resize",eval("DialogBoxWidget_onWindowResize"),false)}oldMouseDown=_curDoc.onmousedown;_curDoc.onmousedown=null;hideBlockWhileWaitWidget()}else{DialogBoxWidget_current=oldCurrent;oldCurrent=null;if(_ie){layer.onkeydown=null;modal.layer.onkeydown=null;window.detachEvent("onresize",eval("DialogBoxWidget_onWindowResize"))}else{_curDoc.removeEventListener("keydown",eval("_curWin."+_codeWinName+".DialogBoxWidget_keypress"),false);window.removeEventListener("resize",eval("DialogBoxWidget_onWindowResize"),false)}_curDoc.onmousedown=oldMouseDown}var sameState=(layer.isShown==sh);if(sameState){return}layer.isShown=sh;if(sh){if(_curWin.DialogBoxWidget_zindex==null){_curWin.DialogBoxWidget_zindex=1000}this.iframe.css.zIndex=_curWin.DialogBoxWidget_zindex++;m_sty.zIndex=_curWin.DialogBoxWidget_zindex++;l_sty.zIndex=_curWin.DialogBoxWidget_zindex++;DialogBoxWidget_modals[DialogBoxWidget_modals.length]=m_sty;m_sty.display="";l_sty.display="block";this.iframe.setDisplay(true);holdBGIFrame(this.iframe.id);DialogBoxWidgetResizeModals();this.height=layer.offsetHeight;this.width=layer.offsetWidth;if(_small&&height){if(divLayer==null){divLayer=getLayer("div_dialog_"+id)}if(divLayer){divLayer.style.overflow="auto";divLayer.style.height=(winHeight()<height)?(winHeight()-40):getContainerHeight();divLayer.style.width=(_moz?width+20:getContainerWidth())}resize(null,((winHeight()<height)?(winHeight()-10):null))}if(isHidden(layer)){this.center()}if(!_small&&this.resizeCB){this.resizeCB(width,height)}}else{var l=DialogBoxWidget_modals.length=Math.max(0,DialogBoxWidget_modals.length-1);m_sty.width="1px";m_sty.height="1px";m_sty.display="none";l_sty.display="none";if(this.iframe!=null){this.iframe.setDisplay(false);releaseBGIFrame(this.iframe.id)}}modal.show(sh);firstLink.show(sh);lastLink.show(sh);oldShow(sh);if(DialogBoxWidget_current!=null&&sh==true){DialogBoxWidget_current.focus()}if(!sh&&closeCB!=null){closeCB()}}}function DialogBoxWidget_onWindowResize(){DialogBoxWidgetResizeModals()}function DialogBoxWidget_unload(){if(this.iframe){releaseBGIFrame(this.iframe.id)}}function DialogBoxWidget_keepFocus(b){var a=DialogBoxWidget_instances[b];if(a){a.focus()}}function DialogBoxWidget_focus(){with(this){if(titleLayer==null){titleLayer=getLayer("titledialog_"+id)}if(_saf&&webKitFocusElem&&webKitFocusElem.focus){webKitFocusElem.focus()}else{if(titleLayer.focus){titleLayer.focus()}}}}function newPromptDialog(a,l,n,k,e,m,p,c,g,f){var b=newDialogBoxWidget(a,l,300,null,PromptDialog_defaultCB,PromptDialog_cancelCB,g,f);b.text=n;b.getHTML=PromptDialog_getHTML;b.yes=k?newButtonWidget(a+"_yesBtn",k,'PromptDialog_yesCB("'+b.id+'")',70):null;b.no=e?newButtonWidget(a+"_noBtn",e,'PromptDialog_noCB("'+b.id+'")',70):null;b.yesCB=p;b.noCB=c;b.promptType=m;b.txtLayer=null;b.imgLayer=null;b.setPromptType=PromptDialog_setPromptType;b.setText=PromptDialog_setText;if(b.yes){b.attachDefaultButton(b.yes)}else{if(b.no){b.attachDefaultButton(b.no)}}return b}function PromptDialog_getimgPath(b){var a=_skin;switch(b){case _promptDlgInfo:a+="information_icon.gif";break;case _promptDlgWarning:a+="warning_icon.gif";break;default:a+="critical_icon.gif";break}return a}function PromptDialog_getimgAlt(b){var a="";return a}function PromptDialog_setPromptType(b){var a=this;if(a.imgLayer==null){a.imgLayer=getLayer("dlg_img_"+a.id)}a.imgLayer.src=PromptDialog_getimgPath(b);a.imgLayer.alt=PromptDialog_getimgAlt(b)}function PromptDialog_setText(b){var a=this;a.text=b;if(a.txtLayer==null){a.txtLayer=getLayer("dlg_txt_"+a.id)}a.txtLayer.innerHTML='<div tabindex="0">'+convStr(b,false,true)+"</div>"}function PromptDialog_getHTML(){var c=this;var b=PromptDialog_getimgPath(c.promptType);var a=PromptDialog_getimgAlt(c.promptType);return c.beginHTML()+'<table class="dialogzone" width="290" cellpadding="0" cellspacing="5" border="0"><tr><td><table class="dialogzone" cellpadding="5" cellspacing="0" border="0"><tr><td align="right" width="32" >'+img(b,32,32,null,'id="dlg_img_'+c.id+'"',a)+'</td><td></td><td id="dlg_txt_'+c.id+'" align="left" tabindex="0">'+convStr(c.text,false,true)+"</td></tr></table></td></tr><tr><td>"+getSep()+'</td></tr><tr><td align="right"><table cellpadding="5" cellspacing="0" border="0"><tr>'+(c.yes?"<td>"+c.yes.getHTML()+"</td>":"")+(c.no?"<td>"+c.no.getHTML()+"</td>":"")+"</tr></table></td></tr></table>"+c.endHTML()}function PromptDialog_defaultCB(){var o=this;if(o.yesCB){if(typeof o.yesCB!="string"){o.yesCB()}else{eval(o.yesCB)}}this.show(false)}function PromptDialog_cancelCB(){var o=this;if(o.noCB){if(typeof o.noCB!="string"){o.noCB()}else{eval(o.noCB)}}this.show(false)}function PromptDialog_yesCB(a){DialogBoxWidget_instances[a].defaultCB()}function PromptDialog_noCB(a){DialogBoxWidget_instances[a].cancelCB()}function newWaitDialogBoxWidget(a,m,f,k,c,l,p,n,g){var e=250;var q=150;if(m<e){m=e}if(f<q){f=q}var b=newDialogBoxWidget(a,k,m,null,null,WaitDialogBoxWidget_cancelCB,g,true);b.pad=5;b.frZone=newFrameZoneWidget(a+"_frZone",null,null);b.showLabel=(p!=null)?p:false;b.showCancel=(c!=null)?c:false;b.label=newWidget(a+"_label");b.label.text=n;if(b.showCancel){b.cancelButton=newButtonWidget(a+"_cancelButton",_cancelButtonLab,CancelButton_cancelCB);b.cancelButton.par=b}else{b.cancelButton={};b.cancelButton.init=function(){};b.cancelButton.setDisplay=function(r){};b.cancelButton.setDisabled=function(r){};b.cancelButton.getHTML=function(){return""}}b.cancelCB=l;b.oldDialogBoxInit=b.init;b.init=WaitDialogBoxWidget_init;b.getHTML=WaitDialogBoxWidget_getHTML;b.setShowCancel=WaitDialogBoxWidget_setShowCancel;b.setShowLabel=WaitDialogBoxWidget_setShowLabel;return b}function WaitDialogBoxWidget_init(){var a=this;a.oldDialogBoxInit();a.frZone.init();a.label.init();a.label.setDisplay(a.showLabel);a.cancelButton.init();a.cancelButton.setDisplay(a.showCancel)}function WaitDialogBoxWidget_getHTML(){var b=this,a="";a+=b.beginHTML();a+='<table border="0" cellspacing="0" cellpadding="0" width="100%"><tbody>';a+='<tr><td align="center" valign="top">'+b.frZone.beginHTML();a+='<table border="0" cellspacing="0" cellpadding="0" width="100%"><tbody><tr><td align="center" style="padding-top:5px;">'+img(_skin+"wait01.gif",200,40)+'</td></tr><tr><td align="left" style="padding-left:2px;padding-right:2px;padding-top:5px;"><div id="'+b.label.id+'" class="iconText" style="wordWrap:break_word;text-align:center;">'+convStr(b.label.text,false,true)+"</div></td></tr></tbody></table>";a+=b.frZone.endHTML()+"</td></tr>";a+='<tr><td align="right" valign="middle" style="padding-top:5px;padding-right:9px">'+b.cancelButton.getHTML()+"</td></tr>";a+="</tbody></table>";a+=b.endHTML();return a}function WaitDialog_FrameZoneWidget_beginHTML(){var a=this;return'<table class="waitdialogzone" style="'+sty("width",a.w)+sty("height",a.h)+'" id="'+a.id+'" cellspacing="0" cellpadding="0" border="0"><tbody><tr><td valign="top" class="dialogzone" id="frame_cont_'+a.id+'">'}function WaitDialog_FrameZoneWidget_endHTML(){var a=this;return"</td></tr></tbody></table>"}function WaitDialogBoxWidget_setShowCancel(a,c){var b=this;b.showCancel=a;b.cancelButton.setDisabled(false);b.cancelButton.setDisplay(a);b.cancelCB=c}function WaitDialogBoxWidget_setShowLabel(a,c){var b=this;b.showLabel=a;b.label.text=c;b.label.setHTML(c);b.label.setDisplay(a)}function WaitDialogBoxWidget_cancelCB(){var a=this;if(a.cancelCB!=null){a.cancelCB();a.cancelButton.setDisabled(true)}}function CancelButton_cancelCB(){var a=this;if(a.par.cancelCB!=null){a.par.cancelCB();a.par.cancelButton.setDisabled(true)}}var swfobject=function(){var Z="undefined",P="object",B="Shockwave Flash",h="ShockwaveFlash.ShockwaveFlash",W="application/x-shockwave-flash",K="SWFObjectExprInst",G=window,g=document,N=navigator,f=[],H=[],Q=null,L=null,T=null,S=false,C=false;var a=function(){var l=typeof g.getElementById!=Z&&typeof g.getElementsByTagName!=Z&&typeof g.createElement!=Z&&typeof g.appendChild!=Z&&typeof g.replaceChild!=Z&&typeof g.removeChild!=Z&&typeof g.cloneNode!=Z,t=[0,0,0],n=null;if(typeof N.plugins!=Z&&typeof N.plugins[B]==P){n=N.plugins[B].description;if(n){n=n.replace(/^.*\s+(\S+\s+\S+$)/,"$1");t[0]=parseInt(n.replace(/^(.*)\..*$/,"$1"),10);t[1]=parseInt(n.replace(/^.*\.(.*)\s.*$/,"$1"),10);t[2]=/r/.test(n)?parseInt(n.replace(/^.*r(.*)$/,"$1"),10):0}}else{if(typeof G.ActiveXObject!=Z){var o=null,s=false;try{o=new ActiveXObject(h+".7")}catch(k){try{o=new ActiveXObject(h+".6");t=[6,0,21];o.AllowScriptAccess="always"}catch(k){if(t[0]==6){s=true}}if(!s){try{o=new ActiveXObject(h)}catch(k){}}}if(!s&&o){try{n=o.GetVariable("$version");if(n){n=n.split(" ")[1].split(",");t=[parseInt(n[0],10),parseInt(n[1],10),parseInt(n[2],10)]}}catch(k){}}}}var v=N.userAgent.toLowerCase(),j=N.platform.toLowerCase(),r=/webkit/.test(v)?parseFloat(v.replace(/^.*webkit\/(\d+(\.\d+)?).*$/,"$1")):false,i=false,q=j?/win/.test(j):/win/.test(v),m=j?/mac/.test(j):/mac/.test(v);
/*@cc_on i=true;@if(@_win32)q=true;@elif(@_mac)m=true;@end@*/
return{w3cdom:l,pv:t,webkit:r,ie:i,win:q,mac:m}}();var e=function(){if(!a.w3cdom){return}J(I);if(a.ie&&a.win){try{g.write("<script id=__ie_ondomload defer=true src=//:><\/script>");var i=c("__ie_ondomload");if(i){i.onreadystatechange=function(){if(this.readyState=="complete"){this.parentNode.removeChild(this);V()}}}}catch(j){}}if(a.webkit&&typeof g.readyState!=Z){Q=setInterval(function(){if(/loaded|complete/.test(g.readyState)){V()}},10)}if(typeof g.addEventListener!=Z){g.addEventListener("DOMContentLoaded",V,null)}M(V)}();function V(){if(S){return}if(a.ie&&a.win){var m=Y("span");try{var l=g.getElementsByTagName("body")[0].appendChild(m);l.parentNode.removeChild(l)}catch(n){return}}S=true;if(Q){clearInterval(Q);Q=null}var j=f.length;for(var k=0;k<j;k++){f[k]()}}function J(i){if(S){i()}else{f[f.length]=i}}function M(j){if(typeof G.addEventListener!=Z){G.addEventListener("load",j,false)}else{if(typeof g.addEventListener!=Z){g.addEventListener("load",j,false)}else{if(typeof G.attachEvent!=Z){G.attachEvent("onload",j)}else{if(typeof G.onload=="function"){var i=G.onload;G.onload=function(){i();j()}}else{G.onload=j}}}}}function I(){var l=H.length;for(var j=0;j<l;j++){var m=H[j].id;if(a.pv[0]>0){var k=c(m);if(k){H[j].width=k.getAttribute("width")?k.getAttribute("width"):"0";H[j].height=k.getAttribute("height")?k.getAttribute("height"):"0";if(O(H[j].swfVersion)){if(a.webkit&&a.webkit<312){U(k)}X(m,true)}else{if(H[j].expressInstall&&!C&&O("6.0.65")&&(a.win||a.mac)){D(H[j])}else{d(k)}}}}else{X(m,true)}}}function U(m){var k=m.getElementsByTagName(P)[0];if(k){var p=Y("embed"),r=k.attributes;if(r){var o=r.length;for(var n=0;n<o;n++){if(r[n].nodeName.toLowerCase()=="data"){p.setAttribute("src",r[n].nodeValue)}else{p.setAttribute(r[n].nodeName,r[n].nodeValue)}}}var q=k.childNodes;if(q){var s=q.length;for(var l=0;l<s;l++){if(q[l].nodeType==1&&q[l].nodeName.toLowerCase()=="param"){p.setAttribute(q[l].getAttribute("name"),q[l].getAttribute("value"))}}}m.parentNode.replaceChild(p,m)}}function F(i){if(a.ie&&a.win&&O("8.0.0")){G.attachEvent("onunload",function(){var k=c(i);if(k){for(var j in k){if(typeof k[j]=="function"){k[j]=function(){}}}k.parentNode.removeChild(k)}})}}function D(j){C=true;var o=c(j.id);if(o){if(j.altContentId){var l=c(j.altContentId);if(l){L=l;T=j.altContentId}}else{L=b(o)}if(!(/%$/.test(j.width))&&parseInt(j.width,10)<310){j.width="310"}if(!(/%$/.test(j.height))&&parseInt(j.height,10)<137){j.height="137"}g.title=g.title.slice(0,47)+" - Flash Player Installation";var n=a.ie&&a.win?"ActiveX":"PlugIn",k=g.title,m="MMredirectURL="+G.location+"&MMplayerType="+n+"&MMdoctitle="+k,p=j.id;if(a.ie&&a.win&&o.readyState!=4){var i=Y("div");p+="SWFObjectNew";i.setAttribute("id",p);o.parentNode.insertBefore(i,o);o.style.display="none";G.attachEvent("onload",function(){o.parentNode.removeChild(o)})}R({data:j.expressInstall,id:K,width:j.width,height:j.height},{flashvars:m},p)}}function d(j){if(a.ie&&a.win&&j.readyState!=4){var i=Y("div");j.parentNode.insertBefore(i,j);i.parentNode.replaceChild(b(j),i);j.style.display="none";G.attachEvent("onload",function(){j.parentNode.removeChild(j)})}else{j.parentNode.replaceChild(b(j),j)}}function b(n){var m=Y("div");if(a.win&&a.ie){m.innerHTML=n.innerHTML}else{var k=n.getElementsByTagName(P)[0];if(k){var o=k.childNodes;if(o){var j=o.length;for(var l=0;l<j;l++){if(!(o[l].nodeType==1&&o[l].nodeName.toLowerCase()=="param")&&!(o[l].nodeType==8)){m.appendChild(o[l].cloneNode(true))}}}}}return m}function R(AE,AC,q){var p,t=c(q);if(typeof AE.id==Z){AE.id=q}if(a.ie&&a.win){var AD="";for(var z in AE){if(AE[z]!=Object.prototype[z]){if(z=="data"){AC.movie=AE[z]}else{if(z.toLowerCase()=="styleclass"){AD+=' class="'+AE[z]+'"'}else{if(z!="classid"){AD+=" "+z+'="'+AE[z]+'"'}}}}}var AB="";for(var y in AC){if(AC[y]!=Object.prototype[y]){AB+='<param name="'+y+'" value="'+AC[y]+'" />'}}t.outerHTML='<object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"'+AD+">"+AB+"</object>";F(AE.id);p=c(AE.id)}else{if(a.webkit&&a.webkit<312){var AA=Y("embed");AA.setAttribute("type",W);for(var x in AE){if(AE[x]!=Object.prototype[x]){if(x=="data"){AA.setAttribute("src",AE[x])}else{if(x.toLowerCase()=="styleclass"){AA.setAttribute("class",AE[x])}else{if(x!="classid"){AA.setAttribute(x,AE[x])}}}}}for(var w in AC){if(AC[w]!=Object.prototype[w]){if(w!="movie"){AA.setAttribute(w,AC[w])}}}t.parentNode.replaceChild(AA,t);p=AA}else{var s=Y(P);s.setAttribute("type",W);for(var v in AE){if(AE[v]!=Object.prototype[v]){if(v.toLowerCase()=="styleclass"){s.setAttribute("class",AE[v])}else{if(v!="classid"){s.setAttribute(v,AE[v])}}}}for(var u in AC){if(AC[u]!=Object.prototype[u]&&u!="movie"){E(s,u,AC[u])}}t.parentNode.replaceChild(s,t);p=s}}return p}function E(k,i,j){var l=Y("param");l.setAttribute("name",i);l.setAttribute("value",j);k.appendChild(l)}function c(i){return g.getElementById(i)}function Y(i){return g.createElement(i)}function O(k){var j=a.pv,i=k.split(".");i[0]=parseInt(i[0],10);i[1]=parseInt(i[1],10);i[2]=parseInt(i[2],10);return(j[0]>i[0]||(j[0]==i[0]&&j[1]>i[1])||(j[0]==i[0]&&j[1]==i[1]&&j[2]>=i[2]))?true:false}function A(m,j){if(a.ie&&a.mac){return}var l=g.getElementsByTagName("head")[0],k=Y("style");k.setAttribute("type","text/css");k.setAttribute("media","screen");if(!(a.ie&&a.win)&&typeof g.createTextNode!=Z){k.appendChild(g.createTextNode(m+" {"+j+"}"))}l.appendChild(k);if(a.ie&&a.win&&typeof g.styleSheets!=Z&&g.styleSheets.length>0){var i=g.styleSheets[g.styleSheets.length-1];if(typeof i.addRule==P){i.addRule(m,j)}}}function X(k,i){var j=i?"visible":"hidden";if(S){c(k).style.visibility=j}else{A("#"+k,"visibility:"+j)}}return{registerObject:function(l,i,k){if(!a.w3cdom||!l||!i){return}var j={};j.id=l;j.swfVersion=i;j.expressInstall=k?k:false;H[H.length]=j;X(l,false)},getObjectById:function(l){var i=null;if(a.w3cdom&&S){var j=c(l);if(j){var k=j.getElementsByTagName(P)[0];if(!k||(k&&typeof j.SetVariable!=Z)){i=j}else{if(typeof k.SetVariable!=Z){i=k}}}}return i},embedSWF:function(n,u,r,t,j,m,k,p,s){if(!a.w3cdom||!n||!u||!r||!t||!j){return}r+="";t+="";if(O(j)){X(u,false);var q=(typeof s==P)?s:{};q.data=n;q.width=r;q.height=t;var o=(typeof p==P)?p:{};if(typeof k==P){for(var l in k){if(k[l]!=Object.prototype[l]){if(typeof o.flashvars!=Z){o.flashvars+="&"+l+"="+k[l]}else{o.flashvars=l+"="+k[l]}}}}J(function(){R(q,o,u);if(q.id==u){X(u,true)}})}else{if(m&&!C&&O("6.0.65")&&(a.win||a.mac)){X(u,false);J(function(){var i={};i.id=i.altContentId=u;i.width=r;i.height=t;i.expressInstall=m;D(i)})}}},getFlashPlayerVersion:function(){return{major:a.pv[0],minor:a.pv[1],release:a.pv[2]}},hasFlashPlayerVersion:O,createSWF:function(k,j,i){if(a.w3cdom&&S){return R(k,j,i)}else{return undefined}},createCSS:function(j,i){if(a.w3cdom){A(j,i)}},addDomLoadEvent:J,addLoadEvent:M,getQueryParamValue:function(m){var l=g.location.search||g.location.hash;if(m==null){return l}if(l){var k=l.substring(1).split("&");for(var j=0;j<k.length;j++){if(k[j].substring(0,k[j].indexOf("="))==m){return k[j].substring((k[j].indexOf("=")+1))}}}return""},expressInstallCallback:function(){if(C&&L){var i=c(K);if(i){i.parentNode.replaceChild(L,i);if(T){X(T,true);if(a.ie&&a.win){L.style.display="block"}}L=null;T=null;C=false}}}}}();if(typeof(bobj)=="undefined"){bobj={}}if(typeof(bobj.crv)=="undefined"){bobj.crv={}}if(typeof(bobj.crv.params)=="undefined"){bobj.crv.params={}}bobj.crv.params.FlexParameterBridge={_swfID:[],_swf:[],_cb:[],_promptData:[],setPromptData:function(b,a){this._promptData[b]=a},setMasterCallBack:function(a,b){this._cb[a]=b},getSWF:function(b){if(this._swf[b]){return this._swf[b]}else{var a=document.getElementById(this._swfID[b]);this._swf[b]=a;return a}},getInstallHTML:function(){return L_bobj_crv_FlashRequired.replace("{0}","<br><a href='http://www.adobe.com/go/getflash/' target='_blank' rel='noopener noreferrer'>")+"</a>"},checkFlashPlayer:function(){return swfobject.hasFlashPlayerVersion("9.0.0")},createSWF:function(g,a,p,n,t,c){var q=this._cb[g];if(!q){return}if(q.logger){q.logger("Create the SWF")}if(this.checkFlashPlayer()){var b=q.getSWFBaseURL();var A=b+"prompting.swf";var l=q.getSWFID();var z=q.getUseSavedData?q.getUseSavedData(g):false;var x=q.getUseOKCancelButtons?q.getUseOKCancelButtons(g):false;var e=q.getIsDialog?q.getIsDialog(g):false;var u=q.getAllowFullScreen?q.getAllowFullScreen(g):false;var s=q.getEnforceRequiredPrompt?q.getEnforceRequiredPrompt():true;var m=q.getShouldAutoResize?q.getShouldAutoResize(g):false;var r={eventTarget:g,locale:t,showMinUI:n,baseURL:b,servletURL:p,reportSourceKey:c,useSavedData:z,useOKCancelButtons:x,isDialog:e,allowFullScreen:u,enforceRequiredPrompt:s,shouldAutoResize:m};var y={menu:"false",wmode:"window",allowscriptaccess:"sameDomain"};var f={id:l,name:l,style:"z-index:"+q.getZIndex()};if(q.processingDelayedShow){q.processingDelayedShow("hidden",a)}var v=q.getSWFHeight?q.getSWFHeight(g)+"":"600";var k=q.getSWFWidth?q.getSWFWidth(g)+"":"800";swfobject.embedSWF(A,a,k,v,"9.0.0","",r,y,f);this._swfID[g]=l;if(q.processingDelayedShow){q.processingDelayedShow()}}else{document.getElementById(a).innerHTML="<p>"+q.getInstallHTML()+"</p>"}},init:function(c){if(!c){return}var a=this._cb[c];var b=this.getSWF(c);if(!b||!a){return}if(a.logger){a.logger("Init the SWF")}if(b.setShowMinUI&&a.getShowMinUI){b.setShowMinUI(a.getShowMinUI(c))}if(b.setUseSavedData&&a.getUseSavedData){b.setUseSavedData(a.getUseSavedData(c))}if(b.setUseOKCancelButtons&&a.getUseOKCancelButtons){b.setUseOKCancelButtons(a.getUseOKCancelButtons(c))}if(b.setAllowFullScreen&&a.getAllowFullScreen){b.setAllowFullScreen(a.getAllowFullScreen(c))}if(b.setReportStateInfo&&a.getReportStateInfo){b.setReportStateInfo(a.getReportStateInfo(c))}if(b.setPromptData){if(a.getPromptData&&a.getPromptData(c)){b.setPromptData(a.getPromptData(c))}else{b.setPromptData(this._promptData[c])}}if(a.getShouldAutoResize&&a.getShouldAutoResize(c)){this.resize(c,1,1,true)}else{if(a.getSWFHeight&&a.getSWFWidth){this.resize(c,a.getSWFHeight(c),a.getSWFWidth(c),true)}}},closeDialog:function(b){var a=this._cb[b];if(a&&a.closeDialog){a.closeDialog(b)}},resize:function(k,m,e,l){var f=this.getSWF(k);var g=this._cb[k];if(f&&g){g.logger("Resizing the SWF h:"+m+" w:"+e);if(g.getScreenHeight&&g.getScreenWidth){var c=g.getScreenHeight(k);var a=g.getScreenWidth(k);var b=MochiKit.Style.getElementPosition(f.parentNode);if((b.x>=0)&&((b.x+e)>=a)&&!l){e=a-b.x}else{if(e>a){e=a}}if((b.y>=0)&&((b.y+m)>=c)&&!l){m=c-b.y}else{if(m>c){m=c}}}if(f.setWidth&&f.setHeight){f.setWidth(e);f.setHeight(m)}f.style.width=e+"px";f.style.height=m+"px";if(l){this.move(k,((a-e)/2),((c-m)/2))}g.setVisibility(k);f._isMaximized=false}},fitScreen:function(f){var e=this.getSWF(f);var a=this._cb[f];if(e&&a&&a.getScreenHeight&&a.getScreenWidth&&e.setHeight&&e.setWidth){a.logger("Fitting SWF to the screen");var c=a.getScreenHeight(f);var b=a.getScreenWidth(f);this.move(f,0,0);this.resize(f,c,b,false);e._isMaximized=true}},startDrag:function(b){var a=this._cb[b];if(a&&a.startDrag){a.startDrag(b)}},stopDrag:function(b){var a=this._cb[b];if(a&&a.stopDrag){a.stopDrag(b)}},drag:function(c,b,e){var a=this._cb[c];if(a&&a.drag){a.drag(c,b,e)}},move:function(c,b,e){var a=this._cb[c];if(a&&a.move){a.move(c,b,e)}},setParamValues:function(c,b){var a=this._cb[c];if(a&&a.setParamValues){a.setParamValues(c,b)}},logon:function(c,b){var a=this._cb[c];if(a&&a.logon){a.logon(c,b)}},setReportStateInfo:function(c,b){var a=this._cb[c];if(a&&a.setReportStateInfo){a.setReportStateInfo(c,b)}},sendAsyncRequest:function(c,b){var a=this._cb[c];if(a&&a.sendAsyncRequest){a.sendAsyncRequest(c,b)}},handleAsyncResponse:function(c,a){var b=this.getSWF(c);if(b&&b.handleAsyncResponse){b.handleAsyncResponse(a)}},readyToShow:function(b){var a=this._cb[b];if(a&&a.readyToShow){a.readyToShow(b)}}};if(typeof bobj=="undefined"){bobj={}}if(typeof bobj.constants=="undefined"){bobj.constants={modalLayerIndex:1000}}bobj.uniqueId=function(){return"bobjid_"+(++bobj.uniqueId._count)};if(typeof bobj.uniqueId._count=="undefined"){bobj.uniqueId._count=new Date().getTime()}bobj.updateIf=function(l,c,f){if(c===null){c={}}for(var e=1,a=arguments.length;e<a;e++){var g=arguments[e];if(typeof(g)!="undefined"&&g!==null){for(var b in g){if(l(c,f,b)){c[b]=g[b]}}}}return c};bobj.fillIn=function(a,b){var c=function(f,g,e){return(typeof(f[e])=="undefined")};bobj.updateIf(c,a,b)};bobj.isObject=function(a){return(a&&typeof a=="object")};bobj.isArray=function(b){if(bobj.isObject(b)){try{return b.constructor==Array}catch(a){return false}}return false};bobj.isString=function(a){return(typeof(a)=="string")};bobj.isNumber=function(a){return typeof(a)=="number"&&isFinite(a)};bobj.isBoolean=function(a){return typeof a=="boolean"};bobj.isFunction=function(a){return typeof(a)=="function"};bobj.isBorderBoxModel=function(){if(typeof bobj.isBorderBoxModel._cachedValue=="undefined"){if(document.body){var a=document.createElement("div");a.style.width="10px";a.style.padding="1px";a.style.position="absolute";a.style.visibility="hidden";document.body.appendChild(a);bobj.isBorderBoxModel._cachedValue=(a.offsetWidth==10);document.body.removeChild(a)}else{return _ie&&bobj.isQuirksMode()}}return bobj.isBorderBoxModel._cachedValue};bobj.isQuirksMode=function(){return(document.compatMode!="CSS1Compat")};bobj.setVisualStyle=function(b,c){if(b===null||c===null){return}var a=b.style;if(c.className){b.className=c.className}MochiKit.Iter.forEach(["background","borderWidth","borderStyle","borderColor","fontFamily","fontStyle","fontSize","fontWeight","textDecoration","color","width","height","left","top"],function(e){if(c[e]){a[e]=c[e]}})};bobj.setOuterSize=function(f,b,e,c){var k=null;var a=f.style;if(a.display=="none"){k={visibility:a.visibility,position:a.position,display:"none"};a.visibility="hidden";a.position="absolute";a.display=""}function g(l){var m=MochiKit.DOM.getStyle(f,l);if(bobj.isString(m)&&m.substring(m.length-2=="px")){return(parseInt(m,10)||0)}return 0}if(bobj.isNumber(b)){if(!bobj.isBorderBoxModel()){b-=g("border-left-width");b-=g("border-right-width");b-=g("padding-left");b-=g("padding-right");if(c){b-=g("margin-left");b-=g("margin-right")}}a.width=Math.max(0,b)+"px"}if(bobj.isNumber(e)){if(!bobj.isBorderBoxModel()){if(c){e-=g("margin-top");e-=g("margin-bottom")}e-=g("border-top-width");e-=g("border-bottom-width");e-=g("padding-top");e-=g("padding-bottom")}a.height=Math.max(0,e)+"px"}if(k){a.display=k.display;a.position=k.position;a.visibility=k.visibility}};bobj.getContainer=function(a){if(a&&a.layer){return a.layer.parentNode}return null};bobj.checkParent=function(e,b){var a=false;if(e&&b){b=b.toUpperCase();var c=e.parentNode;while(c){if(c.tagName==b){a=true;break}c=c.parentNode}}return a};bobj.slice=function(a,g,c){if(bobj.isArray(a)){return a.slice(g,c)}else{if(MochiKit.Base.isArrayLike(a)){var b=[];var f=a.length;if(bobj.isNumber(c)&&c<f){f=c}g=Math.max(g,0);for(var e=g;e<f;++e){b.push(a[e])}return b}}return null};bobj.extractRange=function(e,k,a){if(e&&bobj.isNumber(k)){if(!bobj.isNumber(a)||a>e.length){a=e.length}k=Math.max(0,k);if(k<a){var c=0,g=k;var b=a,f=e.length;if(e.substring){return(e.substring(c,g)+e.substring(b,f))}else{return bobj.slice(e,c,g).concat(bobj.slice(e,b,f))}}}return e};bobj.unitValue=function(b,a){if(bobj.isNumber(b)){return b+(a||"px")}return b};bobj.evalInWindow=function(expression){if(window.execScript){return window.execScript(expression)}else{return MochiKit.Base.bind(eval,window,expression).call()}};bobj.loadJSResourceAndExecCallBack=function(e,f){if(!e||!f){return}if(!e.isLoaded){var b=function(k,l,g){k.isLoaded=true;bobj.evalInWindow(g.responseText);l.apply()};var c=MochiKit.Async.getXMLHttpRequest();c.open("GET",bobj.crvUri(e.path),true);c.setRequestHeader("Accept","application/x-javascript,application/javascript, text/javascript");var a=MochiKit.Async.sendXMLHttpRequest(c);a.addCallback(MochiKit.Base.bind(b,this,e,f))}else{setTimeout(function(){f.apply()},0)}};bobj.trimLeft=function(a){a=a||"";return a.replace(/^\s+/g,"")};bobj.trimRight=function(a){a=a||"";return a.replace(/\s+$/g,"")};bobj.trim=function(a){return bobj.trimLeft(bobj.trimRight(a))};bobj.equals=function(b,a){if(typeof(b)!=typeof(a)){return false}if(bobj.isObject(b)){var e=true;for(var c in b){e=e&&bobj.equals(b[c],a[c])}return e}else{return b==a}};bobj.includeLink=function(b){var c=document.getElementsByTagName("head")[0];var a=document.body;var e=document.createElement("link");e.setAttribute("rel","stylesheet");e.setAttribute("type","text/css");e.setAttribute("href",b);if(c){c.appendChild(e)}else{if(a){a.appendChild(e)}}};bobj.includeCSSLinksAndExecuteCallback=function(c,f){if(c==null||c.length<1){f.apply();return}var b=function(){var g=arguments.callee;var k=g.callback;g.hrefCount--;if(g.hrefCount==0){k.apply()}};b.hrefCount=c.length;b.callback=f;for(var e=0,a=c.length;e<a;e++){bobj.includeCSSLinkAndExecuteCallback(c[e],b)}};bobj.includeCSSLinkAndExecuteCallback=function(a,l){var e=encodeURIComponent(a);if(getLayer(e)){l.apply();return}var g=function(q,p,n){bobj.addStyleSheet(n.responseText,p);q.apply()};var c=function(n){n.apply()};var k=MochiKit.Async.getXMLHttpRequest();k.open("GET",a,true);k.setRequestHeader("Accept","text/css");var m=MochiKit.Async.sendXMLHttpRequest(k);var b=MochiKit.Base.bind(g,this,l,e);var f=MochiKit.Base.bind(c,this,l);m.addCallbacks(b,f)};bobj.addStyleSheet=function(c,f){var e=document.createElement("style");e.setAttribute("type","text/css");if(f){e.setAttribute("id",f)}if(e.styleSheet){e.styleSheet.cssText=c}else{e.appendChild(document.createTextNode(c))}var b=document.getElementsByTagName("head");var a=document.getElementsByTagName("body");if(b&&b[0]){b[0].appendChild(e)}else{if(a&&a[0]){a[0].appendChild(e)}}};bobj.removeAllChildElements=function(a){if(a){while(a.lastChild){a.removeChild(a.lastChild)}}};bobj.getValueHashCode=function(c,a){var b=bobj.crv.params.DataTypes;switch(c){case b.BOOLEAN:case b.CURRENCY:case b.NUMBER:case b.STRING:return""+a;case b.TIME:return""+a.h+","+a.min+","+a.s+","+a.ms;case b.DATE:return""+a.y+","+a.m+","+a.d;case b.DATE_TIME:return""+a.y+","+a.m+","+a.d+","+a.h+","+a.min+","+a.s+","+a.ms}};bobj.getElementByIdOrName=function(b){if(!b){return null}var c=document.getElementById(b);if(c){return c}var a=document.getElementsByName(b);if(a&&a.length>0){return a[0]}return null};bobj.getRect=function(e,b,a,c){return"rect("+e+"px, "+b+"px,"+a+"px,"+c+"px)"};bobj.getBodyScrollDimension=function(){var a=0;var c=0;var b=document.getElementsByTagName("Body");if(b&&b[0]){a=b[0].scrollWidth;c=b[0].scrollHeight}return{w:a,h:c}};bobj.disableTabbingKey=function(b,a){if(b){b.tabIndex=a?-1:0}};bobj.getStringWidth=function(b,a,f){if(document.body){var e=document.createElement("span");e.appendChild(document.createTextNode(b));e.style.position="absolute";e.style.visibility="hidden";if(a){e.style.fontFamily=a}if(f){e.style.fontSize=f}document.body.appendChild(e);var c=e.offsetWidth;document.body.removeChild(e);return c}return 0};bobj.deleteWidget=function(b){if(b&&b.widx){if(b.layer){b.layer.click=null;b.layer.onmouseup=null;b.layer.onmousedown=null;b.layer.onmouseover=null;b.layer.onmousemove=null;b.layer.onmouseout=null;b.layer.onchange=null;b.layer.onfocus=null;b.layer.onkeydown=null;b.layer.onkeyup=null;b.layer.onkeypress=null;var a=b.layer.parentNode;if(a){a.removeChild(b.layer)}delete b.layer}delete b.css;delete _widgets[b.widx];_widgets[b.widx]=null;delete b}};bobj.cloneArray=function(a){return a.slice()};bobj.bindFunctionToObject=function(a,b){return function(){return a.apply(b,arguments)}};bobj.extendClass=function(a,e,b){MochiKit.Base.update(a,e);a.superClass={};for(var c in b){a.superClass[c]=bobj.bindFunctionToObject(b[c],a)}};bobj.displayElementWithAnimation=function(a){if(a!=null){MochiKit.DOM.setOpacity(a,0);MochiKit.Style.setDisplayForElement("block",a);new MochiKit.Visual.appear(a,{duration:0.5})}};bobj.getHiddenElementDimensions=function(e){var c={w:0,h:0};if(e){var a=document.body;var f=e.cloneNode(true);var b=f.style;b.display="";b.visibility="hidden";b.width="";b.height="";b.position="absolute";b.left="-1000px";b.top="-1000px";a.appendChild(f);c={w:f.offsetWidth,h:f.offsetHeight};a.removeChild(f)}return c};bobj.hasPDFReaderWithJSFunctionality=function(){if(navigator.plugins){var b=navigator.plugins;for(var c=0,a=b.length;c<a;c++){if(b[c].description.indexOf("Adobe PDF Plug-In")!=-1){return true}}}try{var g=new ActiveXObject("AcroPDF.PDF.1");if(g){return true}}catch(f){}return false};if(typeof bobj=="undefined"){bobj={}}bobj.encodeUTF8=function(b){var a=[];var g=b.length;for(var f=0;f<g;f++){var k=b.charCodeAt(f);if(k<128){a.push(k)}else{if(k<2048){a.push((k>>6)|192);a.push(k&63|128)}else{if(k<55296||k>=57344){a.push((k>>12)|224);a.push((k>>6)&63|128);a.push(k&63|128)}else{if(k<56320){var e=b.charCodeAt(f+1);if(isNaN(e)||e<56320||e>=57344){a.push(239,191,189);continue}f++;val=((k&1023)<<10)|(e&1023);val+=65536;a.push((val>>18)|240);a.push((val>>12)&63|128);a.push((val>>6)&63|128);a.push(val&63|128)}else{a.push(239,191,189)}}}}}return a};bobj.encodeBASE64=function(a){var b="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";var k=[];var g,e,c,q,n,m,l;var f=0,p=a.length;while(f<p){g=a[f++];e=a[f++];c=a[f++];q=g>>2;n=((g&3)<<4)|(e>>4);m=((e&15)<<2)|(c>>6);l=c&63;if(isNaN(e)){m=l=64}else{if(isNaN(c)){l=64}}k.push(b.charAt(q));k.push(b.charAt(n));k.push(b.charAt(m));k.push(b.charAt(l))}return k.join("")};if(typeof(bobj.html)=="undefined"){bobj.html={}}bobj.html.openTag=function(b,l){var f="<"+b;for(var e in l){f+=" "+e+'="';var g=l[e];if(bobj.isArray(g)){g=g.join(" ")}else{if(bobj.isObject(g)){var a="";for(var c in g){a+=c+":"+g[c]+";"}g=a}}f+=g+'"'}return f+">"};bobj.html.closeTag=function(a){return"</"+a+">"};bobj.html.createHtml=function(a,f,b){var e=bobj.html.openTag(a,f);for(var c=2;c<arguments.length;++c){e+=arguments[c]}e+=bobj.html.closeTag(a);return e};bobj.html.TABLE=MochiKit.Base.partial(bobj.html.createHtml,"table");bobj.html.UL=MochiKit.Base.partial(bobj.html.createHtml,"ul");bobj.html.OL=MochiKit.Base.partial(bobj.html.createHtml,"ol");bobj.html.LI=MochiKit.Base.partial(bobj.html.createHtml,"li");bobj.html.TD=MochiKit.Base.partial(bobj.html.createHtml,"td");bobj.html.TR=MochiKit.Base.partial(bobj.html.createHtml,"tr");bobj.html.TBODY=MochiKit.Base.partial(bobj.html.createHtml,"tbody");bobj.html.THEAD=MochiKit.Base.partial(bobj.html.createHtml,"thead");bobj.html.TFOOT=MochiKit.Base.partial(bobj.html.createHtml,"tfoot");bobj.html.TABLE=MochiKit.Base.partial(bobj.html.createHtml,"table");bobj.html.TH=MochiKit.Base.partial(bobj.html.createHtml,"th");bobj.html.INPUT=MochiKit.Base.partial(bobj.html.createHtml,"input");bobj.html.SPAN=MochiKit.Base.partial(bobj.html.createHtml,"span");bobj.html.A=MochiKit.Base.partial(bobj.html.createHtml,"a");bobj.html.DIV=MochiKit.Base.partial(bobj.html.createHtml,"div");bobj.html.IMG=MochiKit.Base.partial(bobj.html.createHtml,"img");bobj.html.BUTTON=MochiKit.Base.partial(bobj.html.createHtml,"button");bobj.html.TT=MochiKit.Base.partial(bobj.html.createHtml,"tt");bobj.html.PRE=MochiKit.Base.partial(bobj.html.createHtml,"pre");bobj.html.H1=MochiKit.Base.partial(bobj.html.createHtml,"h1");bobj.html.H2=MochiKit.Base.partial(bobj.html.createHtml,"h2");bobj.html.H3=MochiKit.Base.partial(bobj.html.createHtml,"h3");bobj.html.BR=MochiKit.Base.partial(bobj.html.createHtml,"br");bobj.html.HR=MochiKit.Base.partial(bobj.html.createHtml,"hr");bobj.html.LABEL=MochiKit.Base.partial(bobj.html.createHtml,"label");bobj.html.TEXTAREA=MochiKit.Base.partial(bobj.html.createHtml,"textarea");bobj.html.FORM=MochiKit.Base.partial(bobj.html.createHtml,"form");bobj.html.P=MochiKit.Base.partial(bobj.html.createHtml,"p");bobj.html.SELECT=MochiKit.Base.partial(bobj.html.createHtml,"select");bobj.html.OPTION=MochiKit.Base.partial(bobj.html.createHtml,"option");bobj.html.OPTGROUP=MochiKit.Base.partial(bobj.html.createHtml,"optgroup");bobj.html.LEGEND=MochiKit.Base.partial(bobj.html.createHtml,"legend");bobj.html.FIELDSET=MochiKit.Base.partial(bobj.html.createHtml,"fieldset");bobj.html.STRONG=MochiKit.Base.partial(bobj.html.createHtml,"strong");bobj.html.CANVAS=MochiKit.Base.partial(bobj.html.createHtml,"canvas");bobj.html.IFRAME=MochiKit.Base.partial(bobj.html.createHtml,"iframe");bobj.html.SCRIPT=MochiKit.Base.partial(bobj.html.createHtml,"script");bobj.html.extractScripts=function(k){var g=/(?:<script([^>]*)\/>|<script([^>]*)>([\s\S]*?)<\/script>)/i;var e=/src=\"([^\"]*)\"/i;var a=[];var f=null;while(f=g.exec(k)){var c={src:null,text:null};var b=f[1]||f[2];if(b=e.exec(b)){c.src=b[1]}if(f[3]){c.text=f[3]}a.push(c);k=bobj.extractRange(k,f.index,f.index+f[0].length)}return{scripts:a,html:k}};bobj.html.extractHtml=function(a){var e=bobj.html.extractScripts(a);var c=bobj.html.extractLinks(e.html);var b=bobj.html.extractStyles(c.html);return{scripts:e.scripts,html:b.html,links:c.links,styles:b.styles}};bobj.html.extractLinks=function(f){var e=/<link([^>]*)>/i;var g=/href=\"([^\"]*)\"/i;var b=[];var c=null;while(c=e.exec(f)){var a=g.exec(c);if(a&&a.length>0){b.push(a[1])}f=bobj.extractRange(f,c.index,c.index+c[0].length)}return{links:b,html:f}};bobj.html.extractStyles=function(g){var c=/<style([^>]*)>([\s\S]*?)<\/style>/i;var m=/type=\"([^\"]*)\"/i;var f=/media=\"([^\"]*)\"/i;var l=[];var k=null;while(k=c.exec(g)){var b={media:null,type:null,text:k[2]};var a=m.exec(k[1]);if(a){b.type=a[1]}var e=f.exec(k[1]);if(e){b.media=e[1]}l.push(b);g=bobj.extractRange(g,k.index,k.index+k[0].length)}return{styles:l,html:g}};bobj.crv.allInOne=(function(){var b=new Object();b.uri=bobj.crvUri("images/allInOne.gif");var c=22;var a=0;a+=3;a=b.toolbarExportDy=a;a=b.toolbarPrintDy=a+c;a=b.toolbarRefreshDy=a+c;a=b.toolbarSearchDy=a+c;a=b.toolbarUpDy=a+c;a-=3;a=b.groupTreeToggleDy=a+c;a=b.paramPanelToggleDy=a+c;a=b.toolbarPrevPageDy=a+c;a=b.toolbarNextPageDy=a+20;a=b.paramRunDy=a+20;a=b.paramDataFetchingDy=a+22;a=b.closePanelDy=a+16;a=b.openParameterArrowDy=a+7;a=b.plusDy=a+15;a=b.minusDy=a+12;a=b.undoDy=a+12;return b})();bobj.crv.setAllClasses=function(b,a){if(b&&b.setClasses){if(a){b.setClasses(a+"_default",a+"_depressed",a+"_hover",a+"_depressed")}else{b.setClasses(null,null,null,null)}}};bobj.crv.newToolbar=function(a){var c=MochiKit.Base.update;a=c({id:bobj.uniqueId(),visualStyle:{className:null,backgroundColor:null,borderWidth:null,borderStyle:null,borderColor:null,fontFamily:null,fontWeight:null,textDecoration:null,color:null,width:null,height:null,fontStyle:null,fontSize:null}},a);var b=newPaletteContainerWidget(a.id);b.margin=0;bobj.fillIn(b,a);b._rightZoneWgts=[];b.widgetType="Toolbar";b.initOld=b.init;c(b,bobj.crv.Toolbar);b.palette=newPaletteWidget(b.id+"_palette");b.add(b.palette);return b};bobj.crv.Toolbar={addChild:function(c){if(!c){return}var b=MochiKit.Signal.signal;var e=MochiKit.Base.partial;var a=MochiKit.Base.bind;switch(c.widgetType){case"ToolbarMenu":this.menu=c;if(c.exportMenuItem){c.exportMenuItem.cb=e(b,this,"export")}if(c.printMenuItem){c.printMenuItem.cb=e(b,this,"print")}break;case"ToolbarButtonGroup":this.group=c;if(c.printButton){c.printButton.clickCB=e(b,this,"print",c.printButton.closeCB)}if(c.exportButton){c.exportButton.clickCB=e(b,this,"export",c.exportButton.closeCB)}if(c.refreshButton){c.refreshButton.clickCB=e(b,this,"refresh")}if(c.drillUpButton){c.drillUpButton.clickCB=e(b,this,"drillUp")}break;case"PrevPageButton":this.prevPageButton=c;c.clickCB=e(b,this,"prevPage");break;case"NextPageButton":this.nextPageButton=c;c.clickCB=e(b,this,"nextPage");break;case"ZoomControl":this.zoomControl=c;c.zoomCB=e(b,this,"zoom");break;case"SelectPageControl":this.selectPageControl=c;c.selectPageCB=e(b,this,"selectPage");if(c.firstPage&&c.lastPage){c.firstPage.cb=e(b,this,"firstPage");c.lastPage.cb=e(b,this,"lastPage")}break;case"SearchTextControl":this.searchTextControl=c;c.searchTextCB=e(b,this,"search");break;default:break}if(c.layoutAlign=="right"){this._rightZoneWgts.push(c)}else{this.palette.add(c)}},getSearchTextControl:function(){return this.searchTextControl},getZoomControl:function(){return this.zoomControl},getSelectPageControl:function(){return this.selectPageControl},getButtonGroup:function(){return this.group},getNextPageButton:function(){return this.nextPageButton},getPrevPageButton:function(){return this.prevPageButton},init:function(){this.initOld();bobj.setVisualStyle(this.layer,this.visualStyle);this.palette.init();this._updateNavButtons()},write:function(){this._addRightZone();this.begin();this.palette.write();this.end();document.write(bobj.crv.getInitHTML(this.widx))},beginHTML:function(){return bobj.html.openTag("div",{id:this.id,"class":"crtoolbar",style:{height:"28px",overflow:"hidden",margin:this.margin+"px"}})},getHTML:function(){this._addRightZone();return(this.beginHTML()+this.palette.getHTML()+this.endHTML())},getWidth:function(){var f;var e=0;var b=this.palette.items;for(var c=0,a=b.length;c<a;c++){f=b[c].layer;if(f.offsetWidth){e+=f.offsetWidth}if(f.offsetLeft){e+=(f.offsetLeft*2)}}return e},_addRightZone:function(){this.palette.beginRightZone();var a=null;while(a=this._rightZoneWgts.pop()){this.palette.add(a)}delete this._rightZoneWgts},_updateNavButtons:function(){if(this.selectPageControl){var e=this.selectPageControl.getCurrentPage();if(this.selectPageControl.firstPage){this.selectPageControl.firstPage.setDisabled(e==1)}if(this.prevPageButton){this.prevPageButton.setDisabled(e==1)}var b=this.selectPageControl.getNumPages();var a=b.indexOf("+")==-1;var c=parseInt(b,10);var f=a&&c==e;if(this.nextPageButton){this.nextPageButton.setDisabled(f)}if(this.selectPageControl.lastPage){this.selectPageControl.lastPage.setDisabled(f)}}},setPageNumber:function(b,a){if(this.selectPageControl){if(b){this.selectPageControl.setCurrentPage(b)}if(a){this.selectPageControl.setNumPages(a)}this._updateNavButtons()}},update:function(c){if(c){for(var a in c.children){var b=c.children[a];if(b){switch(b.cons){case"bobj.crv.newSelectPageControl":if(this.selectPageControl){this.selectPageControl.update(b);this._updateNavButtons()}break;case"bobj.crv.newSearchTextControl":if(this.searchTextControl){this.searchTextControl.update(b)}break;case"bobj.crv.newZoomControl":if(this.zoomControl){this.zoomControl.update(b)}break;case"bobj.crv.newToolbarButtonGroup":if(this.group){this.group.update(b)}break}}}}}};bobj.crv.newToolbarMenu=function(a){a=MochiKit.Base.update({id:bobj.uniqueId(),icon:null,text:L_bobj_crv_File,tooltip:L_bobj_crv_FileMenu},a);var b=newSingleIconMenuWidget(a.id,a.icon,null,a.text,a.tooltip);b.widgetType="ToolbarMenu";b._tbBtnOldInit=b.init;bobj.crv.setAllClasses(b.icon,null);bobj.crv.setAllClasses(b.arrow,null);bobj.crv.setAllClasses(b,"filemenu");b.arrow.resize(13,(_ie?20:18));MochiKit.Base.update(b,bobj.crv.ToolbarMenu);return b};bobj.crv.ToolbarMenu={init:function(){this._tbBtnOldInit();bobj.disableTabbingKey(this.icon.layer,true);if(this.getMenu().items.length==0){this.arrow.setDisplay(false);bobj.crv.setAllClasses(this.icon,null);this.icon.clickCB=null}},addChild:function(e){var f=this.getMenu();switch(e.widgetType){case"ExportMenuItem":var c=bobj.crv.allInOne.uri;var a=bobj.crv.allInOne.toolbarExportDy;var b=a;this.exportMenuItem=f.add(e.id,L_bobj_crv_submitBtnLbl,null,c,3,a,e.disabled,25,b,null);break;case"PrintMenuItem":var c=bobj.crv.allInOne.uri;var a=bobj.crv.allInOne.toolbarPrintDy;var b=a;this.printMenuItem=f.add(e.id,L_bobj_crv_ActiveXPrintDialogTitle,null,c,3,a,e.disabled,25,b,null);break;default:bobj.crv.ToolbarMenu.commonAddChild(f,e);break}},commonAddChild:function(e,b){switch(b.widgetType){case"Separator":e.addSeparator();break;case"ScriptMenuItem":var a=function(){bobj.crv.ToolbarMenuItem.scriptItemCB(b.callback,b.args)};var c=e.add(b.id,b.text,a,b.icon,b.dx,b.dy,b.disabled,b.disDx,b.disDy,b.alt);if(b.callback==null){c.setDisabled(true)}break;case"ToolbarSubMenu":var c=e.add(b.id,b.text,null,b.icon,b.dx,b.dy,b.disabled,b.disDx,b.disDy,b.alt);c.attachSubMenu(b.submenu);break}}};bobj.crv.newToolbarSubMenu=function(a){var b=MochiKit.Base.update({id:bobj.uniqueId(),text:null,icon:null,dx:0,dy:0,disabled:false,disDx:0,disDy:0,alt:null},a);b.submenu=newMenuWidget(b.id);b.widgetType="ToolbarSubMenu";MochiKit.Base.update(b,bobj.crv.ToolbarSubMenu);return b};bobj.crv.ToolbarSubMenu={addChild:function(a){bobj.crv.ToolbarMenu.commonAddChild(this.submenu,a)}};bobj.crv.newToolbarMenuItem=function(a){var b=MochiKit.Base.update({id:bobj.uniqueId(),widgetType:null,text:null,icon:null,dx:0,dy:0,disabled:false,disDx:0,disDy:0,alt:null},a);MochiKit.Base.update(b,bobj.crv.ToolbarMenuItem);return b};bobj.crv.ToolbarMenuItem={scriptItemCB:function(callback,args){if(callback){var f=eval(callback);if(typeof f=="function"){if(args){f.call(null,args)}else{f.call(null)}}}}};bobj.crv.newToolbarButton=function(a){a=MochiKit.Base.update({id:bobj.uniqueId(),icon:null,tooltip:null,text:null,isDisabled:false,isChecked:false,clickCB:null,width:16,height:16,dx:3,dy:3,disDx:25,disDy:3,isTabEnabled:true},a);var b=newIconWidget(a.id,a.icon,a.clickCB,a.text,a.tooltip,a.width,a.height,a.dx,a.dy,a.disDx,a.disDy,a.isTabEnabled);if(a.text){bobj.crv.setAllClasses(b,null)}else{bobj.crv.setAllClasses(b,"toolbar_button")}b._tbBtnOldInit=b.init;b._tbBtnKwArgs=a;MochiKit.Base.update(b,bobj.crv.ToolbarButton);return b};bobj.crv.ToolbarButton={init:function(){this._tbBtnOldInit();var a=this._tbBtnKwArgs;this.setDisabled(a.isDisabled)},update:function(a){if(a&&bobj.isBoolean(a.args.isDisabled)){this.setDisabled(a.args.isDisabled)}}};bobj.crv.newPrevPageButton=function(a){var b=bobj.crv.newToolbarButton(MochiKit.Base.update({icon:bobj.crv.allInOne.uri,tooltip:L_bobj_crv_PrevPage,dx:3,dy:bobj.crv.allInOne.toolbarPrevPageDy+3,disDx:25,disDy:bobj.crv.allInOne.toolbarPrevPageDy+3,width:16,height:16},a));b.widgetType="PrevPageButton";return b};bobj.crv.newNextPageButton=function(a){var b=bobj.crv.newToolbarButton(MochiKit.Base.update({icon:bobj.crv.allInOne.uri,tooltip:L_bobj_crv_NextPage,dx:3,dy:bobj.crv.allInOne.toolbarNextPageDy+3,disDx:25,disDy:bobj.crv.allInOne.toolbarNextPageDy+3,width:16,height:16},a));b.widgetType="NextPageButton";return b};bobj.crv.newDrillUpButton=function(a){var b=bobj.crv.newToolbarButton(MochiKit.Base.update({icon:bobj.crv.allInOne.uri,tooltip:L_bobj_crv_DrillUp,dx:0,dy:bobj.crv.allInOne.toolbarUpDy,disDx:22,disDy:bobj.crv.allInOne.toolbarUpDy},a));b.widgetType="DrillUpButton";return b};bobj.crv.newRefreshButton=function(a){var b=bobj.crv.newToolbarButton(MochiKit.Base.update({icon:bobj.crv.allInOne.uri,tooltip:L_bobj_crv_Refresh,dy:bobj.crv.allInOne.toolbarRefreshDy,disDy:bobj.crv.allInOne.toolbarRefreshDy},a));b.widgetType="RefreshButton";return b};bobj.crv.newExportButton=function(a){var b=bobj.crv.newToolbarButton(MochiKit.Base.update({icon:bobj.crv.allInOne.uri,tooltip:L_bobj_crv_Export,dy:bobj.crv.allInOne.toolbarExportDy,disDy:bobj.crv.allInOne.toolbarExportDy},a));b.widgetType="ExportButton";b.closeCB=MochiKit.Base.bind(function(){if(this.focus){this.focus()}},b);return b};bobj.crv.newPrintButton=function(a){var b=bobj.crv.newToolbarButton(MochiKit.Base.update({icon:bobj.crv.allInOne.uri,tooltip:L_bobj_crv_Print,dy:bobj.crv.allInOne.toolbarPrintDy,disDy:bobj.crv.allInOne.toolbarPrintDy},a));b.widgetType="PrintButton";b.closeCB=MochiKit.Base.bind(function(){if(this.focus){this.focus()}},b);return b};bobj.crv.newLogoIcon=function(a){a=MochiKit.Base.update({id:bobj.uniqueId(),icon:bobj.crvUri("images/logo.gif")},a);var b=newIconWidget(a.id,a.icon,function(){window.open("http://www.businessobjects.com/ipl/default.asp?destination=ViewerLogoLink&product=crystalreports&version=14%2E0")},null,"SAP Crystal Reports",120,20,0,0,0,0,true);bobj.crv.setAllClasses(b,null);b.layoutAlign="right";b.widgetType="LogoIcon";return b};bobj.crv.newCatalystIcon=function(a){var b=newIconWidget(a.id,bobj.crvUri("images/catalyst.gif"),function(){window.open("http://www.businessobjects.com/ipl/default.asp?destination=DHTMLViewerLandingPage&product=crystalreports&version=14%2E0")},null,L_bobj_crv_CatalystTip,22,22,0,0,0,0,true);bobj.crv.setAllClasses(b,null);b.layoutAlign="right";b.widgetType="CatalystIcon";return b};bobj.crv.newToolbarSeparator=function(){var a=newPaletteVerticalSepWidget(bobj.uniqueId());a.getHTML=function(){return bobj.html.DIV({id:this.id,style:{width:"6px"}},bobj.html.IMG({src:_skin+"../transp.gif","class":"toolbar_separator"}))};return a};bobj.crv.newZoomControl=function(e){var k=MochiKit.Base.update;e=k({initialZoom:"100%",id:bobj.uniqueId()},e);if(bobj.isNumber(e.initialZoom)){e.initialZoom=e.initialZoom+"%"}var g=newTextComboWidget(e.id,5,L_bobj_crv_Zoom,60,bobj.crv.ZoomControl._zoomChangeCB,null,null,null);g.arrow.resize(13,(_ie?20:18));bobj.crv.setAllClasses(g.arrow,"toolbar_menuarrow");g.menu.setAccelEnabled(false);var b=["400%","300%","200%","150%","125%","100%","75%","50%","25%"];for(var c=0,a=b.length;c<a;++c){var f=b[c];g.add(f,f,(f==e.initialZoom))}g.text.setValue(e.initialZoom);g.zoomCB=null;g.widgetType="ZoomControl";g.initOld=g.init;g._initZoom=e.initialZoom;k(g,bobj.crv.ZoomControl);return g};bobj.crv.ZoomControl={init:function(){this.initOld();this.setZoom(this._initZoom)},update:function(a){if(a.cons=="bobj.crv.newZoomControl"){this.setZoom(a.args.initialZoom)}},setZoom:function(b){var c=parseInt(b,10);if(bobj.isNumber(c)){c+="%";this.valueSelect(c);var a=this.selectedItem;if(a&&a.val!=c){a.check(false);this.selectedItem=null}this._lastValue=c;return true}return false},_zoomChangeCB:function(){var a=parseInt(this.text.getValue(),10);if(bobj.isNumber(a)){if(a<10){a=10}else{if(a>400){a=400}}}if(!this.setZoom(a)){this.setZoom(this._lastValue)}else{if(this.zoomCB){this.zoomCB(a)}}}};bobj.crv.newSelectPageControl=function(b){var e=MochiKit.Base.update;b=e({id:bobj.uniqueId(),showMenu:true},b);var a=bobj.crv.SelectPageControl.getTextAndWidth(b.curPage,b.numPages);var c=newTextComboWidget(b.id,75,L_bobj_crv_PageNav,a.width+13,null,null,null,null);c.curPage=b.curPage;c.numPages=b.numPages;if(b.showMenu){c.addMenuItem(bobj.uniqueId(),L_bobj_crv_FirstPage,null,null,0,0,false,0,0);c.addMenuItem(bobj.uniqueId(),L_bobj_crv_LastPage,null,null,0,0,false,0,0);c.firstPage=c.menu.items[0];c.lastPage=c.menu.items[1];c.arrow.resize(13,(_ie?20:18));bobj.crv.setAllClasses(c.arrow,"toolbar_menuarrow");c.menu.setAccelEnabled(false)}else{c.arrow.setDisplay(false)}c.text.oldSetValue=c.text.setValue;c.text.setValue=function(g,f){c.text.oldSetValue(g);if(f&&c.text.layer){c.text.resize(f,null)}};c.text.focusCB=function(){c.text.setValue(c.curPage);var f=c.text.layer;if(f){f.select()}};c.text.blurCB=function(){var f=c.getTextAndWidth(c.curPage,c.numPages);c.text.setValue(f.text)};c.text.enterCB=function(){if(c.selectPageCB){var f=c.text.getValue();c.selectPageCB(f)}};c.margin=1;c.space=0;c.fieldWidth=30;c.labelWidth=13+c.space;c.selectPageCB=null;e(c,bobj.crv.SelectPageControl);c.updateTextValue();c.widgetType="SelectPageControl";return c};bobj.crv.SelectPageControl={getTextAndWidth:function(c,a){var e=L_bobj_crv_of.replace("%1",c).replace("%2",a);var b=(e.length>9)?(e.length*7):60;return{text:e,width:b}},update:function(a){if(a&&a.cons=="bobj.crv.newSelectPageControl"){this.curPage=a.args.curPage;this.numPages=a.args.numPages;this.updateTextValue()}},updateTextValue:function(){var a=this.getTextAndWidth(this.curPage,this.numPages);this.text.setValue(a.text,a.width);var b=this.text.layer;if(b){b.blur()}},getCurrentPage:function(){return this.curPage},setCurrentPage:function(a){this.curPage=a;this.updateTextValue()},getNumPages:function(){return this.numPages},setNumPages:function(a){this.numPages=a;this.updateTextValue()},getFirstPageButton:function(){return this.firstPage},getLastPageButton:function(){return this.lastPage}};bobj.crv.newSearchTextControl=function(a){var c=MochiKit.Base.update;a=c({id:bobj.uniqueId(),icon:bobj.crv.allInOne.uri,dy:bobj.crv.allInOne.toolbarSearchDy,disDy:bobj.crv.allInOne.toolbarSearchDy},a);var b=newWidget(a.id);bobj.fillIn(b,a);b.textField=newTextFieldWidget(b.id+"_textField",null,null,null,MochiKit.Base.bind(bobj.crv.SearchTextControl._searchTextCB,b),true,L_bobj_crv_SearchText,100);b.empty=(b.searchText=="");b.textField.focusCB=function(){if(b.empty){b.textField.setValue("")}};b.textField.blurCB=function(){if(b.textField.getValue()==""){b.textField.setValue(L_bobj_crv_Find);b.empty=true}else{b.empty=false}};b.searchButton=newIconWidget(b.id+"_button",a.icon,MochiKit.Base.bind(bobj.crv.SearchTextControl._searchTextCB,b),null,L_bobj_crv_SearchText,16,16,3,a.dy,a.disDy,3,true);bobj.crv.setAllClasses(b.searchButton,"toolbar_button");b.initOld=b.init;b.searchTextCB=null;c(b,bobj.crv.SearchTextControl);b.widgetType="SearchTextControl";return b};bobj.crv.SearchTextControl={update:function(b){if(b&&b.cons=="bobj.crv.newSearchTextControl"){this.empty=(b.args.searchText=="");var a=(this.empty)?L_bobj_crv_Find:b.args.searchText;this.textField.setValue(a)}},init:function(){this.initOld();this.textField.init();var a=(this.empty)?L_bobj_crv_Find:this.searchText;this.textField.setValue(a);this.searchButton.init()},getHTML:function(){var b=bobj.html;var a={cursor:"default","padding-left":this.space+"px",width:this.labelWidth+"px"};return b.TABLE({id:this.id,cellspacing:0,cellpadding:0,border:0,style:{margin:this.margin+"px"}},b.TBODY(null,b.TR(null,b.TD(null,this.textField.getHTML()),b.TD(null,this.searchButton.getHTML()))))},_searchTextCB:function(){var a=this.textField.getValue();if((a!==""||this.searchText!=a)&&bobj.isFunction(this.searchTextCB)){this.searchTextCB(a)}}};bobj.crv.newToolbarButtonGroup=function(a){var c=MochiKit.Base.update;a=c({id:bobj.uniqueId(),visualStyle:{className:null,backgroundColor:null,borderWidth:null,borderStyle:null,borderColor:null,fontFamily:null,fontWeight:null,textDecoration:null,color:null,width:null,height:null,fontStyle:null,fontSize:null}},a);var b=newPaletteContainerWidget(a.id);b.margin=2;bobj.fillIn(b,a);b.widgetType="ToolbarButtonGroup";b.initOld=b.init;c(b,bobj.crv.ToolbarButtonGroup);b.palette=newPaletteWidget(b.id+"_palette");b.add(b.palette);return b};bobj.crv.ToolbarButtonGroup={addChild:function(a){switch(a.widgetType){case"ExportButton":this.exportButton=a;break;case"PrintButton":this.printButton=a;break;case"RefreshButton":this.refreshButton=a;break;case"DrillUpButton":this.drillUpButton=a}this.palette.add(a)},update:function(c){if(c!=null&&c.cons=="bobj.crv.newToolbarButtonGroup"){for(var a in c.children){var b=c.children[a];if(b){switch(b.cons){case"bobj.crv.newPrintButton":if(this.printButton){this.printButton.update(b)}break;case"bobj.crv.newExportButton":if(this.exportButton){this.exportButton.update(b)}break;case"bobj.crv.newRefreshButton":if(this.refreshButton){this.refreshButton.update(b)}break;case"bobj.crv.newDrillUpButton":if(this.drillUpButton){this.drillUpButton.update(b)}break}}}}},isAnyButtonDisplayed:function(){return(this.printButton!=null&&this.printButton.isDisplayed())||(this.exportButton!=null&&this.exportButton.isDisplayed())||(this.refreshButton!=null&&this.refreshButton.isDisplayed())||(this.drillUpButton!=null&&this.drillUpButton.isDisplayed())},autoDisplay:function(){if(this.isAnyButtonDisplayed()){this.setDisplay(true)}else{this.setDisplay(false)}},getExportButton:function(){return this.exportButton},getRefreshButton:function(){return this.refreshButton},getDrillUpButton:function(){return this.drillUpButton},getPrintButton:function(){return this.printButton},init:function(){this.initOld();bobj.setVisualStyle(this.layer,this.visualStyle);this.palette.init()},beginHTML:function(){var a=bobj.html;return a.openTag("table",{id:this.id,"class":"dialogzone toolbar",border:"0",cellspacing:"0",cellpadding:"0",style:{overflow:"hidden",margin:this.margin+"px"}})+a.openTag("tr",null)+a.TD({style:{width:"2px"}},a.IMG({src:_skin+"../transp.gif","class":"toolbar_buttongroup_left"}))+a.openTag("td",{"class":"toolbar_buttongroup"})},endHTML:function(){var a=bobj.html;return a.closeTag("td",null)+a.TD({style:{width:"2px"}},a.IMG({src:_skin+"../transp.gif","class":"toolbar_buttongroup_right"}))+a.closeTag("tr",null)+a.closeTag("table")},getHTML:function(){return this.beginHTML()+this.palette.getHTML()+this.endHTML()}};bobj.crv.newToolbarButtonGroupSeparator=function(){return newPaletteVerticalSepWidget(bobj.uniqueId())};bobj.crv.newStatusbar=function(a){var c=MochiKit.Base.update;a=c({id:bobj.uniqueId(),visualStyle:{className:null,backgroundColor:null,borderWidth:null,borderStyle:null,borderColor:null,fontFamily:null,fontWeight:null,textDecoration:null,color:null,width:null,height:null,fontStyle:null,fontSize:null}},a);var b=newPaletteContainerWidget(a.id);b.margin=0;bobj.fillIn(b,a);b._rightZoneWgts=[];b.widgetType="Statusbar";b.initOld=b.init;c(b,bobj.crv.Statusbar);b.palette=newPaletteWidget(b.id+"_palette");b.palette.isLeftTableFixed=true;b.add(b.palette);return b};bobj.crv.Statusbar={init:function(){this.initOld();bobj.setVisualStyle(this.layer,this.visualStyle);this.palette.init()},beginHTML:function(){return bobj.html.openTag("div",{id:this.id,"class":"dialogzone",style:{width:"100%",overflow:"hidden",margin:this.margin+"px",padding:"2px 0px",position:"absolute"}})},getHTML:function(){this._addRightZone();return(this.beginHTML()+this.palette.getHTML()+this.endHTML())},_addRightZone:function(){this.palette.beginRightZone();var a=null;while(a=this._rightZoneWgts.pop()){this.palette.add(a)}delete this._rightZoneWgts},write:function(){this._addRightZone();this.begin();this.palette.write();this.end();document.write(bobj.crv.getInitHTML(this.widx))},addChild:function(a){switch(a.widgetType){case"StatusbarBreadcrumb":this.breadcrumb=a;break;case"StatusbarVersionIndicator":this.versionIndicator=a;break}if(a.layoutAlign=="right"){this._rightZoneWgts.push(a)}else{this.palette.add(a)}},update:function(c){if(c){for(var a in c.children){var b=c.children[a];if(b){switch(b.cons){case"bobj.crv.newStatusbarBreadcrumb":if(this.breadcrumb){this.breadcrumb.update(b.args)}break;case"bobj.crv.newStatusbarVersionIndicator":if(this.versionIndicator){this.versionIndicator.update(b.args)}break}}}}},doLayout:function(){if(this.breadcrumb){this.breadcrumb._doLayout()}}};bobj.crv.newStatusbarBreadcrumb=function(a){var b=newWidget(bobj.uniqueId());b.widgetType="StatusbarBreadcrumb";b.values=a.values;b.layoutAlign="left";b._separatorImage=img(bobj.crvUri("images/breadcrumbSep.gif"),14,9);MochiKit.Base.update(b,bobj.crv.StatusbarBreadcrumb);return b};bobj.crv.StatusbarBreadcrumb={update:function(a){this.values=a.values;this.layer.innerHTML=this._render()},getHTML:function(){return bobj.html.DIV({"class":"statusbar_breadcrumb"},bobj.html.DIV({id:this.id},this._render()))},_render:function(){var b="";if(this.values&&this.values.length>0){var a="";for(i=0;i<this.values.length;i++){if(i>0){a+=bobj.html.TD(null,this._separatorImage)}a+=bobj.html.TD({style:{"white-space":"nowrap"}},this.values[i])}b=bobj.html.TABLE({"class":"iconText",cellspacing:"0",cellpadding:"0"},bobj.html.TR(null,a))}return b},_doLayout:function(){var a=(this.layer.parentNode.scrollWidth>this.layer.parentNode.offsetWidth)||(this.layer.offsetLeft<0);if(a){this.layer.style.position="absolute";this.layer.style.top="0px";this.layer.style.right="0px"}else{this.layer.style.position=""}}};bobj.crv.newStatusbarVersionIndicator=function(a){var c=(a&&a.value)?L_bobj_crv_LastRefreshed+": "+a.value:" ";var b=NewLabelWidget(bobj.uniqueId(),c,true);b.widgetType="StatusbarVersionIndicator";b.layoutAlign="right";MochiKit.Base.update(b,bobj.crv.StatusbarVersionIndicator);return b};bobj.crv.StatusbarVersionIndicator={update:function(a){var b=(a&&a.value)?L_bobj_crv_LastRefreshed+":&nbsp;"+a.value:"&nbsp;";this.layer.innerHTML=b}};bobj.crv.newGroupTreeNode=function(b){var f=MochiKit.Base.update;b=f({id:bobj.uniqueId()},b);var a=null;var c=-1;if(!b.isVisible){c=0;a=L_bobj_crv_Tree_Drilldown_Node.replace("%1",b.groupName)}var e=newTreeWidgetElem(c,b.groupName,b.groupPath,null,null,null,a,null,null,false);e._children=[];e._curSigs=[];bobj.fillIn(e,b);e.widgetType="GroupTreeNode";e.initOld=e.init;e.selectOld=e.select;e.select=bobj.crv.GroupTreeNode._drilldown;if(!b.isVisible){e.setCursorClass("drill_cursor")}f(e,bobj.crv.GroupTreeNode);return e};bobj.crv.GroupTreeNode={dispose:function(){while(this._curSigs.length>0){bobj.crv.SignalDisposer.dispose(this._curSigs.pop())}while(this._children.length>0){var a=this._children.pop();a.dispose();bobj.deleteWidget(a);delete a}this.sub=[]},init:function(b){this.initOld(b);this._setVisualStyle();if(this.isStatic){var a=MochiKit.DOM.getElementsByTagAndClassName("span","treeNormal",this.layer);if(a&&a.length>0){a[0].style.cursor="text"}}},isExpanded:function(){var a=TreeIdToIdx(this.layer);return _TreeWidgetElemInstances[a].expanded},expand:function(){var a=TreeIdToIdx(this.layer);_TreeWidgetElemInstances[a].expanded=false;TreeWidget_toggleCB(a)},collapse:function(){var a=TreeIdToIdx(this.layer);_TreeWidgetElemInstances[a].expanded=true;TreeWidget_toggleCB(a)},_setVisualStyle:function(){try{var f=this.layer.lastChild;var a=this.treeView}catch(c){return}var b=a.visualStyle;var e=f.style;if(b.fontFamily){e.fontFamily=b.fontFamily}if(b.fontWeight){e.fontWeight=b.fontWeight}if(b.textDecoration){e.textDecoration=b.textDecoration}if(b.color){e.color=b.color}if(b.fontStyle){e.fontStyle=b.fontStyle}if(b.fontSize){e.fontSize=b.fontSize}},delayedAddChild:function(m,l){var e=MochiKit.Signal.connect;var k=MochiKit.Signal.signal;var b=MochiKit.Base.partial;var f=this._children.length;if(f>0){this.expanded=true}else{this.expanded=false;if(!this.leaf){this.setIncomplete(bobj.crv.GroupTreeNode._getChildren)}}var c=this._children;for(var g=0;g<f;g++){var a=c[g];a.expandPath=this.expandPath+"-"+g;a._updateProperty(m,l);this.add(a);this._curSigs.push(e(a,"grpDrilldown",b(k,this,"grpDrilldown")));this._curSigs.push(e(a,"grpNodeRetrieveChildren",b(k,this,"grpNodeRetrieveChildren")));a.delayedAddChild(m,l)}},addChild:function(a){this._children.push(a)},getLevel:function(){return this.expandPath.split("-").length},_drilldown:function(){this.selectOld();MochiKit.Signal.signal(this,"grpDrilldown",this.groupName,this.groupPath,this.isVisible,this.groupNamePath)},_getChildren:function(){this.plusLyr.src=_skin+"../loading.gif";MochiKit.Signal.signal(this,"grpNodeRetrieveChildren",this.expandPath)},_updateProperty:function(b,c){var a=false;if(this.isVisible&&!c){a=true}else{if(!this.isVisible&&!b){a=true}}if(a){this.select=MochiKit.Base.noop}this.isStatic=a}};bobj.crv.newGroupTree=function(a){var c=MochiKit.Base.update;a=c({id:bobj.uniqueId(),visualStyle:{className:null,backgroundColor:null,borderWidth:null,borderStyle:null,borderColor:null,fontFamily:null,fontWeight:null,textDecoration:null,color:null,width:null,height:null,fontStyle:null,fontSize:null},icns:bobj.crvUri("images/magnify.gif"),minIcon:bobj.crvUri("images/min.gif"),plusIcon:bobj.crvUri("images/plus.gif")},a);var b=newTreeWidget(a.id+"_tree","100%","100%",a.icns,null,null,"groupTree",bobj.crv.GroupTree._expand,bobj.crv.GroupTree._collapse,null,a.minIcon,a.plusIcon);b._children=[];b._modalChildren=[];b._lastNodeIdInitialized=-1;b._lastNodeInitialized=null;b._curSigs=[];bobj.fillIn(b,a);b.widgetType="GroupTree";b.initOld=b.init;c(b,bobj.crv.GroupTree);return b};bobj.crv.GroupTree={dispose:function(){while(this._curSigs.length>0){bobj.crv.SignalDisposer.dispose(this._curSigs.pop())}while(this._children.length>0){var a=this._children.pop();a.dispose();bobj.deleteWidget(a);delete a}this._lastNodeIdInitialized=-1;this._lastNodeInitialized=null;this.sub=[];bobj.removeAllChildElements(this.treeLyr)},getModalChildren:function(){return this._modalChildren},addChild:function(e){var b=MochiKit.Base;var c=MochiKit.Signal;var a=c.connect;e.expandPath=this._children.length+"";this._children.push(e);e._updateProperty(this.enableDrilldown,this.enableNavigation);this.add(e);e.delayedAddChild(this.enableDrilldown,this.enableNavigation);this._curSigs.push(a(e,"grpDrilldown",b.partial(c.signal,this,"grpDrilldown")));this._curSigs.push(a(e,"grpNodeRetrieveChildren",b.partial(c.signal,this,"grpNodeRetrieveChildren")))},delayedBatchAdd:function(e){if(!e||e.length==0){return}this._modalChildren=e;var c="";var b=e.length>100?100:e.length;if(b>0){for(var a=0;a<b;a++){var f=bobj.crv.createWidget(this._modalChildren[a]);this.addChild(f);if(this.initialized()){c+=f.getHTML(0)}}}if(this.initialized()){this.appendChildrenHTML(c);this.initChildren()}},appendChildrenHTML:function(a){append(this.treeLyr,a)},init:function(){this.initOld();bobj.setVisualStyle(this.layer,this.visualStyle);this.css.verticalAlign="top";this.initChildren();this._groupTreeListener=new bobj.crv.GroupTreeListener(this)},update:function(c){if(c.cons=="bobj.crv.newGroupTree"){var a=c.args;var b=a.lastExpandedPath;if(b.length>0&&this._children.length>0){this.updateNode(b,c)}else{this.refreshChildNodes(c)}}},delayedAddChild:function(a){this.addChild(a);append(this.treeLyr,a.getHTML(this.initialIndent))},initChildren:function(){while(this._lastNodeIdInitialized<this._children.length-1){this.initNextChild()}},initNextChild:function(){var a=null;var b=-1;if(this._lastNodeIdInitialized==-1){var c=getLayer("treeCont_"+this.id);a=c.firstChild;b=0}else{a=this._lastNodeInitialized.nextSibling;while(!(a.id&&a.id.indexOf("TWe_")>-1)){a=a.nextSibling}b=this._lastNodeIdInitialized+1}if(b<this._children.length&&a!=null){this._children[b].init(a);this._lastNodeInitialized=a;this._lastNodeIdInitialized=b}},getBestFitHeight:function(){return bobj.getHiddenElementDimensions(this.layer).h},refreshChildNodes:function(a){this.dispose();this.delayedBatchAdd(a.children);MochiKit.Signal.signal(this,"refreshed")},updateNode:function(n,g){if(n&&n.length>0){var b=n.split("-");var c=this;var l=g;for(var f=0,k=b.length;f<k;f++){if(c&&l){var e=parseInt(b[f]);l=l.children[e];c=c._children[e]}else{break}}if(c&&l&&l.args.groupPath==c.groupPath&&c._children.length==0){for(var a in l.children){var m=bobj.crv.createWidget(l.children[a]);c.addChild(m)}c.delayedAddChild(this.enableDrilldown,this.enableNavigation);c.expand()}}},getChildrenCount:function(){return this.sub.length},_collapse:function(a){MochiKit.Signal.signal(this,"grpNodeCollapse",a)},_expand:function(a){MochiKit.Signal.signal(this,"grpNodeExpand",a)},resize:function(b,a){bobj.setOuterSize(this.layer,b,a);MochiKit.Signal.signal(this,"resized")}};bobj.crv.GroupTreeListener=function(a){this._groupTree=a;this._groupTreePrevState=this.getTreeState();this._lastNodeRendererd=this.getNumberOfNodesRendered()-1;this._nodeHeight=-1;this._futreNodesPlaceHolder=null;this.actionIDs=[];this.addFutureNodesPlaceHolder();MochiKit.Signal.connect(a.layer,"onscroll",bobj.bindFunctionToObject(this.detectTreeChanges,this));MochiKit.Signal.connect(a,"refreshed",this,this.reset);MochiKit.Signal.connect(a,"resized",this,this.detectTreeChanges)};bobj.crv.GroupTreeListener.prototype={getNumberOfNodesRendered:function(){return this._groupTree.getChildrenCount()},getNumberOfNodesMissing:function(){return this._groupTree.getModalChildren().length-this._lastNodeRendererd-1},getTreeState:function(){var a=this._groupTree;return{height:a.getHeight(),scrollTop:a.layer.scrollTop}},getNumberOfNodesToRender:function(){if(this.getNumberOfNodesMissing()==0){return 0}var a=this._groupTree.sub[this._lastNodeRendererd];var e=this.getTreeState();if(a!=null){var c=a.layer.offsetTop;if(c<e.scrollTop+e.height){var b=e.scrollTop+e.height-c;return Math.floor(b/this.getNodeHeight())}}return 0},getNodeHeight:function(){if(this._nodeHeight>-1){return this._nodeHeight}else{if(this.getNumberOfNodesRendered()>0){var a=this._groupTree.sub[0];this._nodeHeight=bobj.getHiddenElementDimensions(a.layer).h;return this._nodeHeight}}return 0},updateTreeChildren:function(){var a=this.getNumberOfNodesToRender();var g=this._groupTree.getModalChildren();if(a>0){var c="";for(var b=this._lastNodeRendererd+1,f=this._lastNodeRendererd+a;b<=f;b++){var e=g[b];if(e!=null){var k=bobj.crv.createWidget(e);this._groupTree.addChild(k);c+=k.getHTML(0);this._lastNodeRendererd=b}}this._groupTree.appendChildrenHTML(c);this._groupTree.initChildren()}this.updateFutureNodesPlaceHolderHeight()},detectTreeChanges:function(){if(this.isTreeStateChanged()){this.actionIDs.push(setTimeout(bobj.bindFunctionToObject(this.updateTreeChildren,this),200))}this._groupTreePrevState=this.getTreeState()},isTreeStateChanged:function(){var a=this.getTreeState();if(a.height!=this._groupTreePrevState.height){return true}if(a.scrollTop!=this._groupTreePrevState.scrollTop){return true}return false},reset:function(){this._groupTreePrevState=this.getTreeState();this._lastNodeRendererd=this.getNumberOfNodesRendered()-1;this.clearActions();this.updateFutureNodesPlaceHolderHeight()},clearActions:function(){while(this.actionIDs.length>0){clearTimeout(this.actionIDs.pop())}},updateFutureNodesPlaceHolderHeight:function(){var a=this.getFutureNodesPlaceHolderLayer();if(a!=null){a.style.height=(this.getNumberOfNodesMissing()*this.getNodeHeight())+"px"}},getFutureNodesPlaceHolderLayer:function(){return this._futreNodesPlaceHolder},addFutureNodesPlaceHolder:function(){this._futreNodesPlaceHolder=MochiKit.DOM.DIV({id:bobj.uniqueId()+"_futureHolder",style:{width:"0px",height:(this.getNumberOfNodesMissing()*this.getNodeHeight())+"px"}});this._groupTree.layer.appendChild(this._futreNodesPlaceHolder)}};bobj.crv.ToolPanelType={None:"None",GroupTree:"GroupTree",ParameterPanel:"ParameterPanel"};bobj.crv.ToolPanelTypeDetails={None:{title:null,img:null,alt:null},GroupTree:{title:L_bobj_crv_GroupTree,img:{uri:bobj.crv.allInOne.uri,dx:0,dy:bobj.crv.allInOne.groupTreeToggleDy},alt:L_bobj_crv_GroupTree},ParameterPanel:{title:L_bobj_crv_ParamPanel,img:{uri:bobj.crv.allInOne.uri,dx:0,dy:bobj.crv.allInOne.paramPanelToggleDy},alt:L_bobj_crv_ParamPanel}};bobj.crv.newToolPanel=function(a){a=MochiKit.Base.update({id:bobj.uniqueId()+"_toolPanel",width:"300px",height:"100%",initialViewType:bobj.crv.ToolPanelType.None},a);var b=newWidget(a.id);bobj.fillIn(b,a);b.widgetType="ToolPanel";b._children=[];b._selectedChild=null;b._groupTree=null;b._paramPanel=null;b.initOld=b.init;b.resizeOld=b.resize;MochiKit.Base.update(b,bobj.crv.ToolPanel);b.needLeftBorder=false;return b};bobj.crv.ToolPanel={hasGroupTree:function(){return this._groupTree!=null},addChild:function(f){if(!f){return}var c=MochiKit.Signal.connect;var b=MochiKit.Base.partial;var e=MochiKit.Signal.signal;var a=bobj.crv.ToolPanelType;if(f.widgetType=="GroupTree"){this._groupTree=f;MochiKit.Iter.forEach(["grpDrilldown","grpNodeRetrieveChildren","grpNodeCollapse","grpNodeExpand"],function(g){c(this._groupTree,g,b(e,this,g))},this);if(this.initialViewType==a.GroupTree){this._selectedChild=f}}else{if(f.widgetType=="ParameterPanel"){this._paramPanel=f;c(this._paramPanel,"resetParamPanel",b(e,this,"resetParamPanel"));if(this.initialViewType==a.ParameterPanel){this._selectedChild=f}}}this._children.push(f)},hasParameterPanel:function(){return this._paramPanel!=null},getParameterPanel:function(){return this._paramPanel},delayedAddChild:function(a){this.addChild(a);var b=a===this._selectedChild?"":"none";append2(this.layer,bobj.html.DIV({style:{display:b}},a.getHTML()));a.init()},setView:function(e){var b=this._selectedChild;this.updateSelectedChild(e);var a=this._selectedChild;if(b!=a){if(b){var c=bobj.getContainer(b);if(c){c.style.display="none"}}if(a){var c=bobj.getContainer(a);if(c){bobj.displayElementWithAnimation(c)}}}},updateSelectedChild:function(a){var b=bobj.crv.ToolPanelType;switch(a){case b.GroupTree:this._selectedChild=this._groupTree;break;case b.ParameterPanel:this._selectedChild=this._paramPanel;break;default:this._selectedChild=null}},getHTML:function(){var g=bobj.html;var m="";var c=this._children;for(var e=0,k=c.length;e<k;++e){var a=c[e];var n=a===this._selectedChild?"":"none";m+=g.DIV({style:{display:n}},a.getHTML())}var b=(bobj.crv.ToolPanelType.None!==this.initialViewType);var p="toolPanel";if(this.needLeftBorder){p+=" leftBorder"}var l={id:this.id,"class":p,style:{position:"absolute",margin:"0",width:this.width,height:this.height,overflow:"hidden",display:b?"":"none"}};var f=g.DIV(l,m);return f},init:function(){this.initOld();if(this._groupTree){this._groupTree.init()}if(this._paramPanel){this._paramPanel.init()}},update:function(c){if(c&&c.cons=="bobj.crv.newToolPanel"){for(var a in c.children){var b=c.children[a];if(b){switch(b.cons){case"bobj.crv.newGroupTree":if(this._groupTree){this._groupTree.update(b)}else{this.delayedAddChild(bobj.crv.createWidget(b))}break;case"bobj.crv.params.newParameterPanel":if(this._paramPanel){this._paramPanel.update(b)}else{this.delayedAddChild(bobj.crv.createWidget(b))}break}}}this.initialViewType=c.args.initialViewType;this.setView(this.initialViewType);this.css.width=c.args.width}},getBestFitHeight:function(){var a=0;if(this._selectedChild!=null){a=this._selectedChild.getBestFitHeight()}return a},hasPercentWidth:function(){return(this.width!=null)&&(this.width.length>0)&&(this.width.charAt(this.width.length-1)=="%")},getPercentWidth:function(){return parseInt(this.width)/100},_doLayout:function(){var a=this.layer.clientWidth;var b=this.layer.clientHeight;if(this._selectedChild){this._selectedChild.setDisplay(true);this._selectedChild.resize(a,b)}},resize:function(b,e){bobj.setOuterSize(this.layer,b,e);this._doLayout();var c=_ie&&_isQuirksMode?this.layer.offsetWidth:this.layer.clientWidth;var a=_ie&&_isQuirksMode?this.layer.offsetWidth:this.layer.clientWidth;MochiKit.Signal.signal(this,"resizeToolPanel",c,a);this.width=c},addLeftBorder:function(){this.needLeftBorder=true}};bobj.crv.PanelNavigator=function(){this._children=[];this.widgetType="PanelNavigator";this.id=bobj.uniqueId()+"_panelNav"};bobj.crv.PanelNavigator.prototype={getHTML:function(){var b="";for(var a=0;a<this._children.length;a++){b+=this._children[a].getHTML()}var e=bobj.html.DIV;var c={width:bobj.isBorderBoxModel()?"37px":"35px"};return e({"class":"panelNavigator",id:this.id,style:c},e({id:this.id+"_innerBorder","class":"panelNavigatorInnerBorder"},b))},init:function(){this.layer=getLayer(this.id);this._innerBorder=getLayer(this.id+"_innerBorder");this.css=this.layer.style;if(this._children.length==0){this.css.display="none"}else{for(var a=0;a<this._children.length;a++){this._children[a].init()}}},selectChild:function(a){for(var b=0;b<this._children.length;b++){var c=this._children[b];c.setSelected(c.getName()==a)}},getChild:function(a){for(var b=0;b<this._children.length;b++){var c=this._children[b];if(c.getName()==a){return c}}return null},hasChildren:function(){return(this._children.length>0)},getGroupTreeButton:function(){return this.getChild(bobj.crv.ToolPanelType.GroupTree)},getParamPanelButton:function(){return this.getChild(bobj.crv.ToolPanelType.ParameterPanel)},addChild:function(e){e=MochiKit.Base.update({name:"",title:"",img:{uri:"",dx:0,dy:0}},e);var c=MochiKit.Base.partial;var f=MochiKit.Signal.signal;var b=MochiKit.Signal.connect;var a=new bobj.crv.PanelNavigatorItem(e.name,e.img,e.title,35*this._children.length);b(a,"switchPanel",c(f,this,"switchPanel"));this._children.push(a)},resize:function(a,b){bobj.setOuterSize(this.layer,a,b);bobj.setOuterSize(this._innerBorder,a-2,b-2)},getBestFitHeight:function(){var a=0;for(var b=0;b<this._children.length;b++){a+=this._children[b].getHeight()}return a},move:Widget_move,getWidth:Widget_getWidth};bobj.crv.PanelNavigatorItem=function(b,a,e,c){this._name=b;this._img=a;this._isSelected=false;this._title=e;this.topOffset=c;this.widgetType="PanelNavigatorItem";this.id=bobj.uniqueId()+"_navItem_"+b};bobj.crv.PanelNavigatorItem.prototype={getHTML:function(){var b=bobj.html;var a=this._img;return b.DIV({id:this.id,"class":"panelNavigatorItem",tabindex:"0",style:{top:this.topOffset+"px"},title:this._title,role:"button"},imgOffset(a.uri,22,22,a.dx,a.dy,null,'class="panelNavigatorItemImage" title="'+this._title+'"'))},getName:function(){return this._name},init:function(){this.layer=getLayer(this.id);this.css=this.layer.style;var a=MochiKit.Signal.connect;a(this.layer,"onclick",this,this._onClick);a(this.layer,"onkeydown",this,this._onKeyDown);a(this.layer,"onmouseover",this,this._onMouseOver);a(this.layer,"onmouseout",this,this._onMouseOut);a(this.layer,"onfocus",this,this._onFocus);a(this.layer,"onblur",this,this._onBlur);this.setSelected(this._isSelected)},_onFocus:function(){MochiKit.DOM.addElementClass(this.layer,"highlighted")},_onBlur:function(){MochiKit.DOM.removeElementClass(this.layer,"highlighted")},_onMouseOver:function(){MochiKit.DOM.addElementClass(this.layer,"highlighted")},_onMouseOut:function(){MochiKit.DOM.removeElementClass(this.layer,"highlighted")},_onKeyDown:function(a){if(a&&a.key()&&(a.key().code==13||a.key().code==32)){this._signalSwitchPanel()}},_onClick:function(){this._signalSwitchPanel()},_signalSwitchPanel:function(){if(!this._isSelected){MochiKit.Signal.signal(this,"switchPanel",this._name)}},setSelected:function(a){this._isSelected=a;var b=MochiKit.DOM;if(this.layer){if(a){b.addElementClass(this.layer,"selected")}else{b.removeElementClass(this.layer,"selected")}}},getWidth:Widget_getWidth,getHeight:Widget_getHeight,setDisplay:Widget_setDisplay,isDisplayed:Widget_isDisplayed};bobj.crv.PanelHeader=function(){this.id=bobj.uniqueId()+"_panelHeader";this._closeButton=newIconWidget(this.id+"_close",bobj.crv.allInOne.uri,bobj.bindFunctionToObject(this._closeButtonOnClick,this),null,L_bobj_crv_Close,8,7,0,bobj.crv.allInOne.closePanelDy,null,null,true);this.normalCssClass="panelHeaderCloseButton";this.highlightedCssClass="panelHeaderCloseButtonHighlighted";this._closeButton.setClasses(this.normalCssClass,this.normalCssClass,this.highlightedCssClass,this.highlightedCssClass);this._title=""};bobj.crv.PanelHeader.prototype={getHTML:function(){var b=bobj.html.DIV;var a={height:bobj.isBorderBoxModel()?"21px":"20px"};return b({"class":"panelHeader",id:this.id,style:a},b({"class":"panelHeaderTitle",id:this.id+"_title"},this._title),b({"class":"panelHeaderButtonCtn"},this._closeButton.getHTML()))},init:function(){this.layer=getLayer(this.id);this.css=this.layer.style;this._closeButton.init();var a=this._closeButton.layer;if(a){MochiKit.Signal.connect(a,"onfocus",this,this._closeButtonOnFocus);MochiKit.Signal.connect(a,"onblur",this,this._closeButtonOnBlur)}},_getTitleLayer:function(){return getLayer(this.id+"_title")},setTitle:function(b){this._title=b;this._closeButton.changeTooltip(L_bobj_crv_Close+" "+b);var a=this._getTitleLayer();if(a){a.innerHTML=b}},_closeButtonOnFocus:function(){if(this._closeButton&&this._closeButton.layer){MochiKit.DOM.addElementClass(this._closeButton.layer,this.highlightedCssClass)}},_closeButtonOnBlur:function(){if(this._closeButton&&this._closeButton.layer){MochiKit.DOM.removeElementClass(this._closeButton.layer,this.highlightedCssClass)}},_closeButtonOnClick:function(){MochiKit.Signal.signal(this,"switchPanel",bobj.crv.ToolPanelType.None)},resize:function(a,b){if(this.layer){bobj.setOuterSize(this.layer,a,b)}var c=this._getTitleLayer();if(c){bobj.setOuterSize(c,a-30)}},hideCloseButton:function(){if(this._closeButton){this._closeButton.setDisplay(false)}},getWidth:Widget_getWidth,getHeight:Widget_getHeight,move:Widget_move,setDisplay:Widget_setDisplay};bobj.crv.newLeftPanel=function(a){a=MochiKit.Base.update({id:bobj.uniqueId()+"_leftPanel",hasToggleGroupTreeButton:true,hasToggleParameterPanelButton:true,paramIconImg:null,treeIconImg:null},a);return new bobj.crv.LeftPanel(a.id,a.hasToggleGroupTreeButton,a.hasToggleParameterPanelButton,a.paramIconImg,a.treeIconImg)};bobj.crv.LeftPanel=function(f,c,b,e,a){this._panelNavigator=null;this._panelHeader=null;this._toolPanel=null;this.id=f;this.widgetType="LeftPanel";this.hasToggleParameterPanelButton=b;this.hasToggleGroupTreeButton=c;this.paramIconImg=e;this.treeIconImg=a;this._lastViewedPanel=null};bobj.crv.LeftPanel.prototype={getHTML:function(){var c=this._toolPanel?this._toolPanel.getHTML():"";var b=this._panelHeader?this._panelHeader.getHTML():"";var a=this._panelNavigator?this._panelNavigator.getHTML():"";return bobj.html.DIV({"class":"leftPanel",id:this.id},a,b,c)},getBestFitWidth:function(){var a=0;if(this._panelNavigator){a+=this._panelNavigator.getWidth()}if(this._toolPanel&&this._toolPanel.isDisplayed()){a+=this._toolPanel.getWidth()}else{a+=5}return a},getBestFitHeight:function(){var a=0;var b=0;if(this._panelHeader){a+=this._panelHeader.getHeight()}if(this._toolPanel){a+=this._toolPanel.getBestFitHeight()}if(this._panelNavigator){b=this._panelNavigator.getBestFitHeight()}return Math.max(a,b)},update:function(c){if(!c||c.cons!="bobj.crv.newLeftPanel"){return}for(var a in c.children){var b=c.children[a];if(b&&b.cons=="bobj.crv.newToolPanel"){if(this._toolPanel){this._toolPanel.update(b)}break}}},init:function(){this.layer=getLayer(this.id);this.css=this.layer.style;if(this._toolPanel){this._toolPanel.init()}if(this._panelHeader){this._panelHeader.init();if(!this.isToolPanelDisplayed()){this._panelHeader.setDisplay(false)}}if(this._panelNavigator){this._panelNavigator.init()}},_initSignals:function(){var b=MochiKit.Base.partial;var c=MochiKit.Signal.signal;var a=MochiKit.Signal.connect;if(this._toolPanel){MochiKit.Iter.forEach(["grpDrilldown","grpNodeRetrieveChildren","grpNodeCollapse","grpNodeExpand","resetParamPanel","resizeToolPanel"],function(e){a(this._toolPanel,e,b(c,this,e))},this)}if(this._panelNavigator){a(this._panelNavigator,"switchPanel",this,"_switchPanel")}if(this._panelHeader){a(this._panelHeader,"switchPanel",this,"_switchPanel")}},isToolPanelDisplayed:function(){return this._toolPanel&&this._toolPanel.isDisplayed()},displayLastViewedPanel:function(){if(this._toolPanel){switch(this._lastViewedPanel){case bobj.crv.ToolPanelType.GroupTree:this._switchPanel(bobj.crv.ToolPanelType.GroupTree);break;case bobj.crv.ToolPanelType.ParameterPanel:this._switchPanel(bobj.crv.ToolPanelType.ParameterPanel);break;default:this._switchPanel(bobj.crv.ToolPanelType.GroupTree)}}},hideToolPanel:function(){this._switchPanel(bobj.crv.ToolPanelType.None)},_switchPanel:function(a){if(this._toolPanel){this._toolPanel.setView(a);if(a==bobj.crv.ToolPanelType.None){this._toolPanel.setDisplay(false);this._panelHeader.setDisplay(false)}else{this._toolPanel.setDisplay(true);this._panelHeader.setDisplay(true);this._lastViewedPanel=a}}if(this._panelHeader){var b=bobj.crv.ToolPanelTypeDetails[a].title}this._panelHeader.setTitle(b);if(this._panelNavigator){this._panelNavigator.selectChild(a)}MochiKit.Signal.signal(this,"switchPanel",a)},getPanelNavigator:function(){return this._panelNavigator},getToolPanel:function(){return this._toolPanel},addChild:function(a){if(a.widgetType=="ToolPanel"){this._toolPanel=a;this.updateChildren();this._initSignals()}},updateChildren:function(){if(this._toolPanel){this._panelNavigator=new bobj.crv.PanelNavigator();this._panelHeader=new bobj.crv.PanelHeader();var a=null;if(this._toolPanel.hasParameterPanel()&&this.hasToggleParameterPanelButton){a={name:bobj.crv.ToolPanelType.ParameterPanel,img:this.paramIconImg?this.paramIconImg:bobj.crv.ToolPanelTypeDetails.ParameterPanel.img,title:bobj.crv.ToolPanelTypeDetails.ParameterPanel.title};this._panelNavigator.addChild(a)}if(this._toolPanel.hasGroupTree()&&this.hasToggleGroupTreeButton){a={name:bobj.crv.ToolPanelType.GroupTree,img:this.treeIconImg?this.treeIconImg:bobj.crv.ToolPanelTypeDetails.GroupTree.img,title:bobj.crv.ToolPanelTypeDetails.GroupTree.title};this._panelNavigator.addChild(a)}this._lastViewedPanel=this._toolPanel.initialViewType;this._panelNavigator.selectChild(this._toolPanel.initialViewType);this._panelHeader.setTitle(bobj.crv.ToolPanelTypeDetails[this._toolPanel.initialViewType].title);if(!this._panelNavigator.hasChildren()){this._panelHeader.hideCloseButton();this._toolPanel.addLeftBorder()}}},resize:function(a,b){bobj.setOuterSize(this.layer,a,b);this._doLayout()},_doLayout:function(){if(!this._toolPanel||!this._panelNavigator||!this._panelHeader){return}var b=this.getWidth();var e=this.getHeight();var c=this._panelNavigator.getWidth();var a=b-c;var f=e-this._panelHeader.getHeight();if(this._toolPanel.isDisplayed()){this._toolPanel.resize(a,f);this._toolPanel.move(c,this._panelHeader.getHeight())}this._panelHeader.resize(a,null);this._panelHeader.move(c,0);this._panelNavigator.resize(c,e)},move:Widget_move,getWidth:Widget_getWidth,getHeight:Widget_getHeight};bobj.crv.newReportPage=function(a){a=MochiKit.Base.update({id:bobj.uniqueId(),bgColor:"#FFFFFF",width:720,height:984,extraCssFileUrl:"",documentView:bobj.crv.ReportPage.DocumentView.PRINT_LAYOUT},a);var b=newWidget(a.id);b.widgetType="ReportPage";bobj.fillIn(b,a);b.initOld=b.init;b.resizeOld=b.resize;MochiKit.Base.update(b,bobj.crv.ReportPage);return b};bobj.crv.ReportPage={DocumentView:{WEB_LAYOUT:"weblayout",PRINT_LAYOUT:"printlayout"},dispose:function(){MochiKit.DOM.removeElement(this.layer)},displayScrollBars:function(a){this.layer.style.overflow=a?"auto":"hidden"},isDisplayScrollBars:function(){this.layer.style.overflow=="auto"},update:function(a){if(a&&a.cons=="bobj.crv.newReportPage"){this.updateSize({width:a.args.width,height:a.args.height});this.layer.scrollLeft=0;this.layer.scrollTop=0;this.updateHTML(a.args.content,false)}},scrollToHighlighted:function(a){if(this._iframe){var g=_ie?this._iframe.contentWindow.document:this._iframe.contentDocument;var c=g.getElementById("CrystalHighLighted");if(c){var f=MochiKit.Style.getElementPosition(c,null,g);if(a){var b=MochiKit.Style.getElementPosition(this.layer);window.scrollTo(b.x+f.x,b.y+f.y)}else{this.layer.scrollLeft=f.x;this.layer.scrollTop=f.y}}}},updateHTML:function(a,c){if(a){if(!this._iframe){this._iframe=MochiKit.DOM.createDOM("IFRAME",{id:this.id+"_iframe",width:"100%",height:"100%",frameBorder:"0",margin:"0"});this._pageNode.appendChild(this._iframe)}if(c){this._iframe.style.display="none"}var b=_ie?this._iframe.contentWindow.document:this._iframe.contentDocument;b.open();b.write(this.getIFrameHTML(a));b.close();if(c){bobj.displayElementWithAnimation(this._iframe)}}},getIFrameHTML:function(b){var a="";if(this.extraCssFileUrl!=""){a='<link href="'+this.extraCssFileUrl+'" rel="stylesheet" type="text/css" />\r\n'}return'<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"><html>\r\n<head>\r\n'+a+"<style> body { overflow :hidden; margin : 0px;}</style>\r\n</head>\r\n<body>\r\n"+b+"</body>\r\n</html>"},updateSize:function(b){if(b){this.width=(b.width!=undefined)?b.width:this.width;this.height=(b.height!=undefined)?b.height:this.height}if(this._pageNode){var a=bobj.isBorderBoxModel();this._pageNode.style.width=(a?this.width+2:this.width)+"px";this._pageNode.style.height=(a?this.height+2:this.height)+"px"}if(this._shadowNode){this._shadowNode.style.width=(a?this.width+2:this.width)+"px";this._shadowNode.style.height=(a?this.height+2:this.height)+"px"}},getHTML:function(){var f=bobj.html;var k=bobj.isBorderBoxModel();var g={width:"100%",height:"100%",overflow:"auto",position:"absolute"};var b={position:"relative",width:(k?this.width+2:this.width)+"px",height:(k?this.height+2:this.height)+"px","z-index":1,"border-width":"1px","border-style":"solid","background-color":this.bgColor,overflow:"hidden","text-align":"left"};var a={position:"absolute","z-index":0,display:"none",width:(k?this.width+2:this.width)+"px",height:(k?this.height+2:this.height)+"px",top:"0px",left:"0px"};var c="";if(this.documentView.toLowerCase()==bobj.crv.ReportPage.DocumentView.PRINT_LAYOUT){g["background-color"]="#8E8E8E";b["border-color"]="#000000";a["background-color"]="#737373";c=f.DIV({id:this.id+"_shadow","class":"menuShadow",style:a});g["text-align"]="center";b.margin="0 auto";b.top="6px"}else{g["background-color"]="#FFFFFF";b["border-color"]="#FFFFFF";b.margin="0"}var e=f.DIV({id:this.id,style:g,"class":"insetBorder"},f.DIV({id:this.id+"_page",style:b}),c);return e},init:function(){this._pageNode=getLayer(this.id+"_page");this._shadowNode=getLayer(this.id+"_shadow");this.initOld();this.updateHTML(this.content,true)},updateShadowLocation:function(){var a=function(){if(this._shadowNode&&this._pageNode){this._shadowNode.style.display="none";var b={x:this._pageNode.offsetLeft,y:this._pageNode.offsetTop};this._shadowNode.style.display="block";this._shadowNode.style.top=b.y+(bobj.isBorderBoxModel()?4:6)+"px";this._shadowNode.style.left=b.x+(bobj.isBorderBoxModel()?4:6)+"px"}};setTimeout(bobj.bindFunctionToObject(a,this),0)},resize:function(a,b){bobj.setOuterSize(this.layer,a,b);if(_moz){this.css.clip=bobj.getRect(0,a,b,0)}this.updateShadowLocation()},getBestFitSize:function(){var a=this._pageNode;return{width:a.offsetWidth+30,height:a.offsetHeight+30}},hideFrame:function(){this.css.borderStyle="none";this._pageNode.style.border=""}};bobj.crv.newReportView=function(a){a=MochiKit.Base.update({id:bobj.uniqueId(),viewStateId:null,isMainReport:false},a);var b=newWidget(a.id);bobj.fillIn(b,a);b.widgetType="ReportView";b.reportPage=null;b._lastPanelWidth=null;b.initOld=b.init;b.isMainReportFlag=b.isMainReport;MochiKit.Base.update(b,bobj.crv.ReportView);return b};bobj.crv.ReportView={init:function(){this.initOld();if(this.reportPage){this.reportPage.init()}},addChild:function(a){if(a.widgetType=="ReportPage"){this.reportPage=a}},delayedAddChild:function(a){this.addChild(a);append2(this.layer,a.getHTML());a.init()},scrollToHighlighted:function(a){if(this.reportPage){this.reportPage.scrollToHighlighted(a)}},update:function(c){if(c&&c.cons=="bobj.crv.newReportView"){if(c.args){this.viewStateId=c.args.viewStateId}for(var a in c.children){var b=c.children[a];if(b&&b.cons=="bobj.crv.newReportPage"){if(!this.reportPage){this.delayedAddChild(bobj.crv.createWidget(b))}else{this.reportPage.update(b)}break}}}},getHTML:function(){var b=bobj.html;var c={width:"100%",height:"100%",overflow:"hidden",position:"relative"};var a=b.DIV({id:this.id,style:c},this.reportPage?this.reportPage.getHTML():"");return a},_doLayout:function(){if(this.reportPage){this.reportPage.resize(this.getWidth(),this.getHeight())}},isMainReport:function(){return this.isMainReportFlag},resize:function(){this._doLayout()},dispose:function(){if(this.reportPage){this.reportPage.dispose();bobj.deleteWidget(this.reportPage);delete this.reportPage}bobj.removeAllChildElements(this.layer)},getBestFitSize:function(){var b=0;var c=0;var a=this.reportPage?this.reportPage.getBestFitSize():null;if(a){b+=a.width;c+=a.height}return{width:b,height:c}},hasContent:function(){return this.reportPage!=null},hideFrame:function(){if(this.reportPage){this.reportPage.hideFrame()}}};bobj.crv.newButtonList=function(a){a=MochiKit.Base.update({id:bobj.uniqueId(),numLines:null,buttonWidth:24,buttonTooltip:L_bobj_crv_TabList,changeCB:null,label:null,tabIndex:0,multiSelect:false,menuWidth:null,menuTooltip:null},a);var b=newButtonWidget(a.id,a.label,bobj.crv.ButtonList._onClick,a.buttonWidth,null,a.buttonTooltip,a.tabIndex,0,_skin+"menus.gif",7,16,0,81,true,0,97);b._menu=newListWidget(a.id+"_menu",MochiKit.Base.bind(bobj.crv.ButtonList._onChange,b),a.multiSelect,a.menuWidth,a.numLines||2,a.menuTooltip,null,null);b._listItems=[];b._blOldInit=b.init;b._blOldGetHTML=b.getHTML;b._menuDiv=null;b._captureClicks=MenuWidget_captureClicks;b._releaseClicks=MenuWidget_releaseClicks;bobj.fillIn(b,a);b.widgetType="ButtonList";MochiKit.Base.update(b,bobj.crv.ButtonList);return b};bobj.crv.ButtonList={getMenu:function(){return this._menu},add:function(b,c,a,e){if(this._menu&&this._menu.layer){this._menu.add(b,c,a,e)}else{this._listItems.push({lbl:b,val:c,sel:a,id:e})}},init:function(){var f=this._menu;this._blOldInit();f.init();this._menuDiv=getLayer(this.id+"_menuDiv");var e=this._listItems;for(var b=0,a=e.length;b<a;++b){var c=e[b];f.add(c.lbl,c.val,c.sel,c.id)}this._listItems=[]},getHTML:function(){var a=bobj.html;var b={id:this.id+"_menuDiv",onmousedown:"event.cancelBubble=true","class":"menuFrame",style:{visibility:"hidden",position:"absolute","z-index":5000}};return this._blOldGetHTML()+a.DIV(b,this._menu.getHTML())},isMenuShowing:function(){return this._menuDiv&&this._menuDiv.style.visibility!="hidden"},hideMenu:function(){if(this._menuDiv){this._menuDiv.style.visibility="hidden"}},showMenu:function(){if(this._menuDiv){this._captureClicks();var l=document.body;if(this._menuDiv.parentNode!==l){l.appendChild(this._menuDiv)}var b=this._menuDiv.style;b.left="-1000px";b.top="-1000px";b.visibility="visible";var c=MochiKit.Style.getViewportDimensions();var q=this._menu.layer.offsetWidth;var k=this._menu.getHeight();if(!this.numLines){k=Math.min(this._menu.layer.scrollHeight+10,c.h-10);this._menu.resize(null,k)}var a=getPosScrolled(this.layer);var p=a.x;var n=a.y+this.getHeight();var f=p+q+4;var m=n+k+4;var g=c.w+l.scrollLeft-Math.max(0,(c.w-l.offsetWidth));if(f>g){p=Math.max(0,p-(f-g))}var e=c.h+l.scrollTop;if(m>e){n=Math.max(0,n-(m-e))}b.left=p+"px";b.top=n+"px"}},_captureClicks:function(){var b=MochiKit.Base.bind;try{this.layer.onmousedown=b(this._onCaptureClick,this,true);this._oldMousedown=document.onmousedown;document.onmousedown=b(this._onCaptureClick,this,false)}catch(a){if(bobj.crv.config.isDebug){throw a}}},_releaseClicks:function(){if(this.layer.onmousedown){this.layer.onmousedown=null;document.onmousedown=this._oldMousedown}},_onClick:function(){if(!this._cancelNextClick){this.showMenu()}this._cancelNextClick=false},_onChange:function(){this._releaseClicks();this.hideMenu();if(this.changeCB){this.changeCB()}},_onCaptureClick:function(a,b){this._cancelNextClick=a;eventCancelBubble(b);this.hideMenu();this._releaseClicks()}};bobj.crv.newReportAlbum=function(e){var k=MochiKit.Base;var g=k.update;var c=k.bind;var a=bobj.crv.ReportAlbum;e=g({id:bobj.uniqueId(),initTabIdx:0,width:800,height:500,displayDrilldownTab:true},e);var b=newNaviBarWidget(bobj.uniqueId(),_HorizTabTopWithClose,null,null,e.width,null,null,null,false,true,false);var f=newTabbedZone(e.id,b,e.width,e.height);b.cb=c(a._onSelectTab,f);b.closeTab=c(a._removeView,f,true);bobj.fillIn(f,e);f.widgetType="ReportAlbum";f._views=[];f._hideFrame=false;f.selectOld=f.select;g(f,a);return f};bobj.crv.ReportAlbum={getTabBarHeight:function(){return this.tabs.getHeight()},init:function(){this.tzOldInit();this.tabs.init();this.showDrilldownTab(this.displayDrilldownTab);var b=this._views;if(b.length>0){for(var c=0,a=b.length;c<a;c++){b[c].init()}if(this.initTabIdx<0||this.initTabIdx>=b.length){this.initTabIdx=0}this.select(this.initTabIdx)}},showDrilldownTab:function(a){this.displayDrilldownTab=a;try{var b;if(_ie&&!_ie8Up){b="block"}else{b="table-row"}this.tabs.layer.parentNode.parentNode.style.display=a?b:"none"}catch(c){}},isDisplayDrilldownTab:function(){return this.displayDrilldownTab},setHideFrame:function(b){this._hideFrame=b;var a=this.getSelectedView();if(a&&this._hideFrame){a.hideFrame()}},update:function(k){if(!k||k.cons!=="bobj.crv.newReportAlbum"){return}for(var e=0,a=k.children.length;e<a;e++){var g=k.children[e];var b=this._views[e];if(b){b.update(g)}}var f=k.children.length;var c=this._views.length;if(f>c){for(var e=c,a=f;e<a;e++){this.delayedAddChild(bobj.crv.createWidget(k.children[e]))}}else{if(f<c){for(var e=c-1,a=f;e>=a;e--){this._removeView(false,e)}}}this.initTabIdx=k.args.initTabIdx;this.select(this.initTabIdx)},findTabNumber:function(e){var b=this._views;for(var c=0,a=b.length;c<a;c++){if(b[c].viewStateId==e){return c}}return -1},delayedAddChild:function(a){this._views.push(a);var c=this.tabs.add(a.label,a.tooltip);var b=this.getTabHTML(this._views.length-1);append(getLayer(this.id+"_container"),b);a.init()},addChild:function(a){if(a){this._views.push(a);this.add(a.label,a.tooltip)}},getHTML:function(){var e=this.beginHTML();var c=this._views;for(var b=0,a=c.length;b<a;++b){e+=this.getTabHTML(b)}e+=this.endHTML();return e},getTabHTML:function(b){var c=this.tabs.items[b];var a=this._views[b];html="";if(c&&a){html+=this.beginTabHTML(c);html+=a.getHTML();html+=this.endTabHTML()}return html},resizeOuter:function(b,f){var e=33;var a=10;if(bobj.isNumber(f)){if(this.displayDrilldownTab){f-=e}f=Math.max(f,0)}if(bobj.isNumber(b)){if(!this._hideFrame){b-=a}b=Math.max(b,0)}this.resize(b,f);this.tabs.resize(b);var c=this.getSelectedView();if(c){c.resize()}},getBestFitSize:function(){var a=this._hideFrame?0:10;var c=this.displayDrilldownTab?33:0;var b=this.getSelectedView();if(b){var e=b.getBestFitSize();a+=e.width;c+=e.height}return{width:a,height:c}},beginTabHTML:function(a){return bobj.html.openTag("div",{id:a.zoneId,style:{display:"none",width:this.w+"px",height:this.h+"px",position:"relative"}})},getSelectedView:function(){return this._views[this.oldIndex]},select:function(c){var b=MochiKit.Base.partial;if(c>=0&&c<this._views.length&&c!=this.oldIndex){var a=this.getSelectedView();if(a){a.dispose()}var a=this._views[c];this.selectOld(c);MochiKit.Signal.signal(this,"viewChanged")}},_removeView:function(b,c){var a=this._views[c];var e=this.tabs.items[c];b=(e!=null&&e.isSelected&&b);if(a){a.dispose();bobj.deleteWidget(a);MochiKit.Signal.signal(this,"removeView",a)}if(e.isSelected){this.oldIndex=-1}arrayRemove(this,"_views",c);this.tabs.remove(c,b);bobj.deleteWidget(e)},_onSelectTab:function(a){if(a!=this.oldIndex){MochiKit.Signal.signal(this,"selectView",this._views[a])}}};if(typeof(bobj.crv.Separator)=="undefined"){bobj.crv.Separator={}}bobj.crv.newSeparator=function(a){var c=MochiKit.Base.update;a=c({id:bobj.uniqueId(),marginLeft:4,marginRight:4,marginTop:0,marginBottom:2},a);var b=newWidget(a.id);bobj.fillIn(b,a);b.widgetType="Separator";c(b,bobj.crv.Separator);return b};bobj.crv.Separator.getHTML=function(){var b=bobj.html;var a="";if(bobj.isBorderBoxModel()){a=b.IMG({id:this.id,src:bobj.skinUri("sep.gif"),style:{height:2+"px",width:"100%","margin-left":this.marginLeft+"px","margin-right":this.marginRight+"px","margin-top":this.marginTop+"px","margin-bottom":this.marginBottom+"px"}})}else{a=b.DIV({id:this.id,style:{height:2+"px","margin-left":this.marginLeft+"px","margin-right":this.marginRight+"px","margin-top":this.marginTop+"px","margin-bottom":this.marginBottom+"px","background-image":"url("+bobj.skinUri("sep.gif")+")","background-repeat":"repeat-x",overflow:"hidden"}})}return a+bobj.crv.getInitHTML(this.widx)};bobj.crv.Separator.getHeight=function(){return this.layer.offsetHeight+this.marginTop+this.marginBottom};bobj.crv.newViewer=function(a){a=MochiKit.Base.update({id:bobj.uniqueId(),isDisplayModalBG:false,isLoadContentOnInit:false,layoutType:bobj.crv.Viewer.LayoutTypes.FIXED,visualStyle:{className:null,backgroundColor:null,borderWidth:null,borderStyle:null,borderColor:null,fontFamily:null,fontWeight:null,textDecoration:null,color:null,width:"800px",height:"600px",fontStyle:null,fontSize:null,top:"0px",left:"0px"}},a);var b=newWidget(a.id);bobj.fillIn(b,a);b.widgetType="Viewer";b._topToolbar=null;b._reportAlbum=null;b._leftPanel=null;b._separator=null;b._print=null;b._export=null;b._promptDlg=null;b._reportProcessing=null;b._eventListeners=[];b._statusbar=null;b._leftPanelResizeGrabber=newGrabberWidget(b.id+"_leftPanelResizeGrabber",bobj.bindFunctionToObject(bobj.crv.Viewer.onGrabberMove,b),0,0,4,1,true);b.initOld=b.init;b._boundaryControl=new bobj.crv.BoundaryControl(a.id+"_bc");b._modalBackground=new bobj.crv.ModalBackground(a.id+"_mb",bobj.bindFunctionToObject(bobj.crv.Viewer.keepFocus,b));MochiKit.Base.update(b,bobj.crv.Viewer);window[b.id]=b;return b};bobj.crv.Viewer={LayoutTypes:{FIXED:"fixed",CLIENT:"client",FITREPORT:"fitreport"},PromptingTypes:{HTML:"html",FLEX:"flex"},onGrabberMove:function(a){if(this._leftPanel){this._leftPanel.resize(a,null);this._doLayout()}},keepFocus:function(){var a=bobj.crv.params.FlexParameterBridge.getSWF(this.id);if(a){a.focus()}},addChild:function(a){if(a.widgetType=="ReportAlbum"){this._reportAlbum=a}else{if(a.widgetType=="Toolbar"){this._topToolbar=a;this._separator=bobj.crv.newSeparator()}else{if(a.widgetType=="Statusbar"){this._statusbar=a}else{if(a.widgetType=="PrintUI"){this._print=a}else{if(a.widgetType=="ExportUI"){this._export=a}else{if(a.widgetType=="ReportProcessingUI"){this._reportProcessing=a}else{if(a.widgetType=="LeftPanel"){this._leftPanel=a}}}}}}}},getHTML:function(){var b=bobj.html;var c={overflow:"hidden",position:"relative",left:this.visualStyle.left,top:this.visualStyle.top};var a=b.DIV({dir:"ltr",id:this.id,style:c,"class":"dialogzone"},this._topToolbar?this._topToolbar.getHTML():"",this._separator?this._separator.getHTML():"",this._leftPanel?this._leftPanel.getHTML():"",this._reportAlbum?this._reportAlbum.getHTML():"",this._leftPanelResizeGrabber?this._leftPanelResizeGrabber.getHTML():"",this._statusbar?this._statusbar.getHTML():"");return a},_onWindowResize:function(){if(this._currWinSize.w!=winWidth()||this._currWinSize.h!=winHeight()){this._doLayout();this._currWinSize.w=winWidth();this._currWinSize.h=winHeight()}},init:function(){this.initOld();this._initSignals();if(this._reportAlbum){this._reportAlbum.init()}if(this._topToolbar){this._topToolbar.init()}if(this._leftPanel){this._leftPanel.init()}if(this._statusbar){this._statusbar.init()}if(this._leftPanelResizeGrabber){this._leftPanelResizeGrabber.init();if(!this._leftPanel||!this._leftPanel.isToolPanelDisplayed()){this._leftPanelResizeGrabber.setDisplay(false)}}this.setDisplayModalBackground(this.isDisplayModalBG);bobj.setVisualStyle(this.layer,this.visualStyle);this._currWinSize={w:winWidth(),h:winHeight()};var a=MochiKit.Signal.connect;var b=MochiKit.Signal.signal;if(this.layoutType.toLowerCase()==bobj.crv.Viewer.LayoutTypes.CLIENT){a(window,"onresize",this,"_onWindowResize")}if(!this._topToolbar&&!this._statusbar&&this._reportAlbum&&!this._reportAlbum.isDisplayDrilldownTab()){this.layer.className+=" hideFrame";this._reportAlbum.setHideFrame(true)}if(this.layer&&_ie&&bobj.checkParent(this.layer,"TABLE")){a(window,"onload",this,"_doLayoutOnLoad");this._oldCssVisibility=this.css.visibility;this.css.visibility="hidden"}else{this._doLayout()}this.scrollToHighlighted();b(this,"initialized",this.isLoadContentOnInit)},_initSignals:function(){var b=MochiKit.Base.partial;var e=MochiKit.Signal.signal;var a=MochiKit.Signal.connect;var c=MochiKit.Iter.forEach;if(this._topToolbar){c(["zoom","drillUp","firstPage","prevPage","nextPage","lastPage","selectPage","refresh","search","export","print"],function(f){a(this._topToolbar,f,b(e,this,f))},this)}this._initLeftPanelSignals();if(this._reportAlbum){c(["selectView","removeView","viewChanged"],function(f){a(this._reportAlbum,f,b(e,this,f))},this)}if(this._print){a(this._print,"printSubmitted",b(e,this,"printSubmitted"))}if(this._export){a(this._export,"exportSubmitted",b(e,this,"exportSubmitted"))}},getLeftPanel:function(){return this._leftPanel},_initLeftPanelSignals:function(){var b=MochiKit.Base.partial;var e=MochiKit.Signal.signal;var a=MochiKit.Signal.connect;var c=MochiKit.Iter.forEach;if(this._leftPanel){c(["grpDrilldown","grpNodeRetrieveChildren","grpNodeCollapse","grpNodeExpand","resetParamPanel","resizeToolPanel"],function(f){a(this._leftPanel,f,b(e,this,f))},this);a(this._leftPanel,"switchPanel",this,"_onSwitchPanel")}},_isMainReportViewSelected:function(){var a=this._reportAlbum.getSelectedView();return a&&a.isMainReport()},_doLayoutOnLoad:function(){this.css.visibility=this._oldCssVisibility;this._doLayout()},_doLayout:function(){var l=this._topToolbar?this._topToolbar.getHeight():0;var e=this._topToolbar?this._topToolbar.getWidth():0;var p=this._separator?this._separator.getHeight():0;var r=this._statusbar?this._statusbar.getHeight():0;var x=this._leftPanel?this._leftPanel.getBestFitWidth():0;var f=this._leftPanelResizeGrabber&&this._leftPanelResizeGrabber.isDisplayed()?this._leftPanelResizeGrabber.getWidth():0;var v=this.layoutType.toLowerCase();var n=this._leftPanel?this._leftPanel.getToolPanel():null;var t=(n&&n.isDisplayed()&&n.hasPercentWidth());if(bobj.crv.Viewer.LayoutTypes.CLIENT==v){this.css.width="100%";this.css.height="100%";if(t){x=Math.max(x,(this.getWidth()*n.getPercentWidth())-f)}}else{if(bobj.crv.Viewer.LayoutTypes.FITREPORT==v){var k=0;var q=0;if(t){x+=200}if(this._reportAlbum){var s=this._reportAlbum.getBestFitSize();k=(s.width+x+f<e)?e:s.width+x+f;q=(s.height+l+p+r)}else{if(this._leftPanel){k=x;q=(this._leftPanel.getBestFitHeight()+l+p+r)}}this.css.height=q+"px";this.css.width=k+"px"}else{this.css.width=this.visualStyle.width;this.css.height=this.visualStyle.height;if(t){x=Math.max(x,(this.getWidth()*n.getPercentWidth())-f)}}}var a=this.getWidth()-x-f;var g=Math.max(0,this.getHeight()-l-p-r);if(this._reportAlbum){this._reportAlbum.resizeOuter(a,g);this._reportAlbum.move(x+f,l+p)}if(this._leftPanel){this._leftPanel.resize(x,g);this._leftPanel.move(0,l+p)}if(this._leftPanelResizeGrabber&&this._leftPanelResizeGrabber.isDisplayed()){this._leftPanelResizeGrabber.resize(null,g);this._leftPanelResizeGrabber.move(x,l+p)}if(this._statusbar){this._statusbar.doLayout();this._statusbar.move(0,l+p+g)}if(this._print&&this._print.layer){this._print.center()}if(this._export&&this._export.layer){this._export.center()}if(this._reportProcessing&&this._reportProcessing.layer){this._reportProcessing.center()}var u=MochiKit.Style.getElementPosition(this.layer);var z=MochiKit.Style.getElementDimensions(this.layer);if(this._modalBackground){this._modalBackground.updateBoundary(z.w,z.h,u.x,u.y)}var b=bobj.getBodyScrollDimension();var m=((u.x+z.w)>=b.w)||((u.y+z.h)>=b.h);if(m&&(v!=bobj.crv.Viewer.LayoutTypes.CLIENT)){this._boundaryControl.updateBoundary(z.w,z.h,u.x,u.y)}else{this._boundaryControl.updateBoundary(0,0,0,0)}var c=bobj.crv.params.FlexParameterBridge;var y=c.getSWF(this.id);if(y){if(this._promptDlg&&this._promptDlg.style.visibility!="hidden"){if(y._isMaximized){c.fitScreen(this.id)}else{c.resize(this.id,y.offsetHeight,y.offsetWidth,true)}}}this._adjustWindowScrollBars()},_onSwitchPanel:function(a){var b=bobj.crv.ToolPanelType;if(b.GroupTree==a){MochiKit.Signal.signal(this,"showGroupTree")}else{if(b.ParameterPanel==a){MochiKit.Signal.signal(this,"showParamPanel")}else{if(b.None==a){MochiKit.Signal.signal(this,"hideToolPanel")}}}this._leftPanelResizeGrabber.setDisplay(!(b.None==a));this._doLayout()},resize:function(a,b){if(bobj.isNumber(a)){a=a+"px"}if(bobj.isNumber(b)){b=b+"px"}this.visualStyle.width=a;this.visualStyle.height=b;this._doLayout()},setPageNumber:function(b,a){if(this._topToolbar){this._topToolbar.setPageNumber(b,a)}},showPromptDialog:function(b,a){if(!this._promptDlg){var c=MochiKit.Base.bind(this._onShowPromptDialog,this);var e=MochiKit.Base.bind(this._onHidePromptDialog,this);this._promptDlg=bobj.crv.params.newParameterDialog({id:this.id+"_promptDlg",showCB:c,hideCB:e})}this._promptDlg.setCloseCB(a);this._promptDlg.setNoCloseButton(!a);this._originalDocumentOnKeyPress=document.onkeypress;this.updatePromptDialog(b)},updatePromptDialog:function(a){a=a||"";var b=function(c,e){return function(){c.updateHtmlAndDisplay(e)}};bobj.loadJSResourceAndExecCallBack(bobj.crv.config.resources.HTMLPromptingSDK,b(this._promptDlg,a));if(bobj.isParentWindowTestRunner()){setTimeout(MochiKit.Base.partial(MochiKit.Signal.signal,this,"promptDialogIsVisible"),5)}},showFlexPromptDialog:function(k,r){var b=bobj.crv.params.FlexParameterBridge;var c=bobj.crv.params.ViewerFlexParameterAdapter;if(!b.checkFlashPlayer()){var f=L_bobj_crv_FlashRequired;this.showError(f.substr(0,f.indexOf("{0}")),b.getInstallHTML());return}c.setViewerLayoutType(this.id,this.layoutType);if(!this._promptDlg){this._promptDlg=document.createElement("div");this._promptDlg.id=this.id+"_promptDlg";this._promptDlg.closeCB=r;var l=this._promptDlg.style;l.border="1px";l.borderStyle="solid";l.borderColor="#000000";l.position="absolute";l.zIndex=bobj.constants.modalLayerIndex;var g=bobj.uniqueId();this._promptDlg.innerHTML='<div id="'+g+'" name="'+g+'"></div>';var m=bobj.bindFunctionToObject(bobj.crv.Viewer.keepFocus,this);var n=MochiKit.DOM.createDOM("BUTTON",{id:this._promptDlg.id+"_firstLink",onfocus:m,style:{width:"0px",height:"0px",position:"absolute",left:"-30px",top:"-30px"}});var p=MochiKit.DOM.createDOM("BUTTON",{id:this._promptDlg.id+"_lastLink",onfocus:m,style:{width:"0px",height:"0px",position:"absolute",left:"-30px",top:"-30px"}});document.body.appendChild(n);document.body.appendChild(this._promptDlg);document.body.appendChild(p);var a=bobj.crv.stateManager.getComponentState(this.id);var q=a.common.reportSourceSessionID;var e=bobj.crv.getLangCode();b.setMasterCallBack(this.id,c);b.createSWF(this.id,g,k,true,e,q)}else{this._promptDlg.closeCB=r;this._promptDlg.style.display="";b.init(this.id)}this.setDisplayModalBackground(true)},sendPromptingAsyncRequest:function(a){MochiKit.Signal.signal(this,"crprompt_asyncrequest",a)},setDisplayModalBackground:function(a){a=this.isDisplayModalBG||a;if(this._modalBackground){this._modalBackground.show(a)}},_onShowPromptDialog:function(){this._adjustWindowScrollBars();this.setDisplayModalBackground(true)},_onHidePromptDialog:function(){this._adjustWindowScrollBars();document.onkeypress=this._originalDocumentOnKeyPress;this.setDisplayModalBackground(false)},isPromptDialogVisible:function(){return this._promptDlg&&this._promptDlg.isVisible&&this._promptDlg.isVisible()},hidePromptDialog:function(){if(this.isPromptDialogVisible()){this._promptDlg.show(false)}},hideFlexPromptDialog:function(){if(this._promptDlg){if(_ie){this._promptDlg.focus()}this._promptDlg.style.visibility="hidden";this._promptDlg.style.display="none";this.setDisplayModalBackground(false);if(this._promptDlg.closeCB){this._promptDlg.closeCB()}}},_adjustWindowScrollBars:function(){if(_ie&&this.layoutType==bobj.crv.Viewer.LayoutTypes.CLIENT&&this._promptDlg&&this._promptDlg.layer&&MochiKit.DOM.currentDocument().body){var f,b;var a=MochiKit.DOM.currentDocument().body;var e=this._promptDlg.layer;if(this.getReportPage()&&this.getReportPage().layer){var c=this.getReportPage().layer}if(!window.bodyOverFlow){window.bodyOverFlow=MochiKit.DOM.getStyle(a,"overflow")}if(a.offsetHeight<(e.offsetTop+e.offsetHeight)){if(window.bodyOverFlow=="hidden"){f="scroll"}b="hidden"}else{f=window.bodyOverFlow;b="auto"}a.style.overflow=f;if(c){c.style.overflow=b}}},showError:function(c,a){var b=bobj.crv.ErrorDialog.getInstance();b.setText(c,a);b.setTitle(L_bobj_crv_Error);b.show(true)},update:function(c){if(!c||c.cons!="bobj.crv.newViewer"){return}if(c.args){this.isDisplayModalBG=c.args.isDisplayModalBG}this.hidePromptDialog();for(var a in c.children){var b=c.children[a];if(b){switch(b.cons){case"bobj.crv.newReportAlbum":if(this._reportAlbum){this._reportAlbum.update(b)}break;case"bobj.crv.newToolbar":if(this._topToolbar){this._topToolbar.update(b)}break;case"bobj.crv.newStatusbar":if(this._statusbar){this._statusbar.update(b)}break;case"bobj.crv.newLeftPanel":if(this._leftPanel){this._leftPanel.update(b)}else{this._leftPanel=bobj.crv.createWidget(b);if(this.layer){append(this.layer,this._leftPanel.getHTML());this._initLeftPanelSignals();this._leftPanel.init()}if(this._leftPanel&&this._leftPanel.isToolPanelDisplayed()){this._leftPanelResizeGrabber.setDisplay(true)}}break;case"bobj.crv.newExportUI":if(this._export){this._export.update(b)}break}}}this._doLayout();this.scrollToHighlighted();this.setDisplayModalBackground(this.isDisplayModalBG)},getToolPanel:function(){if(this._leftPanel){return this._leftPanel.getToolPanel()}return null},getParameterPanel:function(){var a=this.getToolPanel();if(a){return a.getParameterPanel()}return null},getReportPage:function(){if(this._reportAlbum){var a=this._reportAlbum.getSelectedView();if(a){return a.reportPage}}return null},scrollToHighlighted:function(){if(!this._reportAlbum){return}var a=this._reportAlbum.getSelectedView();if(a){a.scrollToHighlighted(this.layoutType.toLowerCase()==bobj.crv.Viewer.LayoutTypes.FITREPORT)}},addViewerEventListener:function(c,b){var a=this._eventListeners[c];if(!a){this._eventListeners[c]=[b];return}a[a.length]=b},removeViewerEventListener:function(g,b){var a=this._eventListeners[g];if(a){for(var f=0,c=a.length;f<c;f++){if(a[f]==b){a.splice(f,1);return}}}},getEventListeners:function(a){return this._eventListeners[a]}};bobj.crv.BoundaryControl=function(a){this.id=a};bobj.crv.BoundaryControl.prototype={updateBoundary:function(b,a,e,c){if(!this.layer){this._init()}if(this.layer){this.layer.style.width=b+"px";this.layer.style.height=a+"px";this.layer.style.left=e+"px";this.layer.style.top=c+"px"}},_getStyle:function(){return{display:"block",visibility:"hidden",position:"absolute"}},_getHTML:function(){return bobj.html.DIV({id:this.id,style:this._getStyle()})},_init:function(){if(!this.layer){append2(_curDoc.body,this._getHTML());this.layer=getLayer(this.id);this.layer.onselectstart=function(){return false};this.layer.onmousedown=eventCancelBubble;if(this.mouseupCB){this.layer.onmouseup=this.mouseupCB}}}};bobj.crv.ModalBackground=function(b,a){this.id=b;this.mouseupCB=a};bobj.crv.ModalBackground.prototype=new bobj.crv.BoundaryControl();MochiKit.Base.update(bobj.crv.ModalBackground.prototype,{_getStyle:function(){return{"background-color":"#888888",position:"absolute",opacity:0.3,display:"block",filter:"alpha(opacity=30);","z-index":bobj.constants.modalLayerIndex-2,visibility:"hidden"}},show:function(a){if(!this.layer){this._init()}this.layer.style.visibility=a?"visible":"hidden"}});if(typeof(bobj.crv.Async)=="undefined"){bobj.crv.Async={}}bobj.crv.ViewerListener=function(c,f){this._name=c;this._viewer=null;this._promptPage=null;this._paramCtrl=null;this._ioHandler=f;this._reportProcessing=null;var b=MochiKit.Signal.connect;var a=bobj.event.subscribe;var g=MochiKit.Base.bind;var e=window[c];if(e){if(e.widgetType=="Viewer"){this._viewer=e;this._reportProcessing=this._viewer._reportProcessing}else{if(e.widgetType=="PromptPage"){this._promptPage=e;this._reportProcessing=this._promptPage._reportProcessing}}}if(this._viewer){b(this._viewer,"selectView",this,"_onSelectView");b(this._viewer,"removeView",this,"_onRemoveView");b(this._viewer,"firstPage",this,"_onFirstPage");b(this._viewer,"prevPage",this,"_onPrevPage");b(this._viewer,"nextPage",this,"_onNextPage");b(this._viewer,"lastPage",this,"_onLastPage");b(this._viewer,"selectPage",this,"_onSelectPage");b(this._viewer,"zoom",this,"_onZoom");b(this._viewer,"drillUp",this,"_onDrillUp");b(this._viewer,"refresh",this,"_onRefresh");b(this._viewer,"search",this,"_onSearch");b(this._viewer,"export",this,"_onExport");b(this._viewer,"print",this,"_onPrint");b(this._viewer,"resizeToolPanel",this,"_onResizeToolPanel");b(this._viewer,"hideToolPanel",this,"_onHideToolPanel");b(this._viewer,"grpDrilldown",this,"_onDrilldownGroupTree");b(this._viewer,"grpNodeRetrieveChildren",this,"_onRetrieveGroupTreeNodeChildren");b(this._viewer,"grpNodeCollapse",this,"_onCollapseGroupTreeNode");b(this._viewer,"grpNodeExpand",this,"_onExpandGroupTreeNode");b(this._viewer,"showParamPanel",this,"_onShowParamPanel");b(this._viewer,"showGroupTree",this,"_onShowGroupTree");b(this._viewer,"viewChanged",this,"_onChangeView");b(this._viewer,"resetParamPanel",this,"_onResetParamPanel");b(this._viewer,"printSubmitted",this,"_onSubmitPrintPdf");b(this._viewer,"exportSubmitted",this,"_onSubmitExport");b(this._viewer,"initialized",this,"_onViewerInitialization")}a("drilldown",this._forwardTo("_onDrilldown"));a("drilldownGraph",this._forwardTo("_onDrilldownGraph"));a("drilldownSubreport",this._forwardTo("_onDrilldownSubreport"));a("sort",this._forwardTo("_onSort"));a("hyperlinkClicked",this._forwardTo("_onHyperlinkClicked"));a("displayError",this._forwardTo("_displayError"));a("crprompt_param",this._forwardTo("_onSubmitStaticPrompts"));a("crprompt_pmtEngine",this._forwardTo("_onSubmitPromptEnginePrompts"));a("crprompt_logon",this._forwardTo("_onSubmitDBLogon"));a("crprompt_cancel",this._forwardTo("_onCancelParamDlg"));a("crprompt_flexparam",this._forwardTo("_onFlexParam"));a("crprompt_flexlogon",this._forwardTo("_onFlexLogon"));a("crprompt_asyncrequest",this._forwardTo("_onPromptingAsyncRequest"));a("pnav",this._forwardTo("_onNavigateReportPart"));a("navbookmark",this._forwardTo("_onNavigateBookmark"));a("updatePromptDlg",this._forwardTo("_onPromptDialogUpdate"));a("saveViewState",g(this._onSaveViewState,this));if(e){e.init()}if(bobj.isObject(window.jsUnit)&&!window.jsUnit.testSuite){window.jsUnit.testSuite=new jsUnit.crViewerTestSuite(this)}};bobj.crv.ViewerListener.prototype={getCurrentView:function(){if(this._viewer&&this._viewer._reportAlbum){return this._viewer._reportAlbum.getSelectedView()}return null},getPromptingType:function(){return this._getCommonProperty("promptingType")},_displayError:function(c){c=MochiKit.Base.parseQueryString(c);if(c&&this._viewer){var b=c.errorMessage||L_bobj_crv_RequestError;var a=c.debug||"";this.showError(b,a)}},_forwardTo:function(a){return MochiKit.Base.bind(function(c){if(c==this._name){var b=bobj.slice(arguments,1);this[a].apply(this,b)}},this)},_onViewerInitialization:function(a){if(a){this._initialLoadViewerContent()}},_onSaveViewState:function(){this._saveViewState()},_onSelectView:function(a){if(a){bobj.crv.logger.info("UIAction View.Select");var b=bobj.crv.stateManager.getComponentState(this._name);if(b){b.curViewId=a.viewStateId;this._request({selectView:a.viewStateId},bobj.crv.config.useAsync,true)}}},_onRemoveView:function(c){if(c){bobj.crv.logger.info("UIAction View.Remove");var b=bobj.crv.stateManager.getComponentState(this._name);if(b){delete b[c.viewStateId]}var e=this._getCommonState();if(e){var a=MochiKit.Base.findValue(e.rptAlbumOrder,c.viewStateId);if(a!=-1){arrayRemove(e,"rptAlbumOrder",a)}}}},_initialLoadViewerContent:function(){bobj.crv.logger.info("UIAction InitLoad");this._request({ajaxInitLoad:true},bobj.crv.config.useAsync,true)},_onFirstPage:function(){bobj.crv.logger.info("UIAction Toolbar.FirstPage");this._request({tb:"first"},bobj.crv.config.useAsync,true)},_onPrevPage:function(){bobj.crv.logger.info("UIAction Toolbar.PrevPage");this._request({tb:"prev"},bobj.crv.config.useAsync,true)},_onNextPage:function(){bobj.crv.logger.info("UIAction Toolbar.NextPage");this._request({tb:"next"},bobj.crv.config.useAsync,true)},_onLastPage:function(){bobj.crv.logger.info("UIAction Toolbar.LastPage");this._request({tb:"last"},bobj.crv.config.useAsync,true)},_onDrillUp:function(){bobj.crv.logger.info("UIAction Toolbar.DrillUp");this._request({tb:"drillUp"},bobj.crv.config.useAsync,true)},_onChangeView:function(){if(this._paramCtrl){this._paramCtrl.onChangeView()}},_onResetParamPanel:function(){if(this._isResettingParamPanel){return}this._isResettingParamPanel=true;if(this._paramCtrl){this._paramCtrl.setParameters([]);delete this._paramCtrl}this.clearAdvancedPromptData();var a=bobj.bindFunctionToObject(function(){this._isResettingParamPanel=false},this);this._setInteractiveParams(a)},_onSelectPage:function(a){bobj.crv.logger.info("UIAction Toolbar.SelectPage "+a);this._request({tb:"gototext",text:a},bobj.crv.config.useAsync,true)},_onZoom:function(a){bobj.crv.logger.info("UIAction Toolbar.Zoom "+a);this._request({tb:"zoom",value:a},bobj.crv.config.useAsync,true)},_onExport:function(b){var a=this._viewer._export;if(a){if(b){a.setCloseCB(b)}bobj.crv.logger.info("UIAction Toolbar.Export");a.show(true)}},_onPrint:function(f){var b=this._viewer._print;if(b){if(f){b.setCloseCB(f)}if(b.isActxPrinting){bobj.crv.logger.info("UIAction Toolbar.Print ActiveX");var e=bobj.crv.stateManager.getCompositeState();var c=this._ioHandler.getPostDataForPrinting(e,this._name);this._viewer._print.show(true,c)}else{var a=this._getCommonProperty("pdfOCP")&&bobj.hasPDFReaderWithJSFunctionality();if(a){this._onSubmitPrintPdf(0,0,a)}else{bobj.crv.logger.info("UIAction Toolbar.Print PDF");this._viewer._print.show(true)}}}},_onResizeToolPanel:function(a){this._setCommonProperty("toolPanelWidth",a);this._setCommonProperty("toolPanelWidthUnit","px")},_onHideToolPanel:function(){bobj.crv.logger.info("UIAction Toolbar.HideToolPanel");this._setCommonProperty("toolPanelType",bobj.crv.ToolPanelType.None)},_onShowParamPanel:function(){bobj.crv.logger.info("UIAction Toolbar.ShowParamPanel");this._setCommonProperty("toolPanelType",bobj.crv.ToolPanelType.ParameterPanel)},_onShowGroupTree:function(){bobj.crv.logger.info("UIAction Toolbar.ShowGroupTree");this._setCommonProperty("toolPanelType",bobj.crv.ToolPanelType.GroupTree)},_onDrilldown:function(a){bobj.crv.logger.info("UIAction Report.Drilldown");this._request(a,bobj.crv.config.useAsync,true)},_onDrilldownSubreport:function(a){bobj.crv.logger.info("UIAction Report.DrilldownSubreport");this._request(a,bobj.crv.config.useAsync,true)},_onDrilldownGraph:function(a,k,n,l,g,m,p,e){if(a){bobj.crv.logger.info("UIAction Report.DrilldownGraph");var f,c;if(_ie||_saf||_ie11Up){f=a.offsetX;c=a.offsetY}else{f=a.layerX;c=a.layerY}var b=parseInt(this._getCommonProperty("zoom"),10);b=(isNaN(b)?1:b/100);this._request({name:encodeURIComponent(k),brch:n,coord:(f*e/b+parseInt(l,10))+"-"+(c*e/b+parseInt(g,10)),pagenumber:m,nextpart:encodeURIComponent(p)},bobj.crv.config.useAsync,true)}},_onDrilldownGroupTree:function(g,c,b,a){bobj.crv.logger.info("UIAction GroupTree.Drilldown");var e=encodeURIComponent(g);var f={drillname:e,gnpath:a};if(b){f.grp=c}else{f.brch=c}this._request(f,bobj.crv.config.useAsync,true)},_onRetrieveGroupTreeNodeChildren:function(a){this._request({grow:a},bobj.crv.config.useAsync,true)},_onCollapseGroupTreeNode:function(f){bobj.crv.logger.info("UIAction GroupTree.CollapseNode");var e=this.getCurrentExpandedPaths();var b=f.split("-");for(var c=0,a=b.length-1;c<=a;c++){var g=b[c];if(e[g]){if(c==a){delete e[g];return}e=e[g]}else{return}}},showError:function(b,a){if(this._viewer){this._viewer.showError(b,a)}},_onExpandGroupTreeNode:function(f){bobj.crv.logger.info("UIAction GroupTree.ExpandNode");var e=this.getCurrentExpandedPaths();var b=f.split("-");for(var c=0,a=b.length;c<a;c++){var g=b[c];if(!e[g]){e[g]={}}e=e[g]}},_onRefresh:function(){bobj.crv.logger.info("UIAction Toolbar.Refresh");var b=this._getCommonState();var a=true;if(b&&b.useAsyncForRefresh!==undefined){a=b.useAsyncForRefresh}this._request({tb:"refresh"},bobj.crv.config.useAsync&&a,true)},_onSearch:function(a){bobj.crv.logger.info("UIAction Toolbar.Search");this._request({tb:"search",text:encodeURIComponent(a)},bobj.crv.config.useAsync,true)},_canUseAsync:function(){return this._viewer!=null&&bobj.crv.config.useAsync},_onFlexParam:function(a){this._request({crprompt:"flexPromptingSetValues",paramList:a,isFullPrompt:true},this._canUseAsync())},_onFlexLogon:function(c){for(var b=0,a=c.length;b<a;b++){this._addRequestField(c[b].field,c[b].value)}this._request({crprompt:"logon"},this._canUseAsync())},_onSubmitPromptEnginePrompts:function(isFullPrompt){isFullPrompt=eval(isFullPrompt);var useAjax=this._viewer&&this._viewer.isPromptDialogVisible();var valueIDKey="ValueID"+this._name;var contextIDKey="ContextID"+this._name;var contextHandleIDKey="ContextHandleID"+this._name;var valueID=document.getElementById(valueIDKey);if(valueID){this._addRequestField(valueIDKey,valueID.value)}var contextID=document.getElementById(contextIDKey);if(contextID){this._addRequestField(contextIDKey,contextID.value)}var contextHandleID=document.getElementById(contextHandleIDKey);if(contextHandleID){this._addRequestField(contextHandleIDKey,contextHandleID.value)}this._request({crprompt:"pmtEngine",isFullPrompt:isFullPrompt},useAjax);this._removeRequestField(valueIDKey);this._removeRequestField(contextIDKey);this._removeRequestField(contextHandleIDKey)},_onSubmitStaticPrompts:function(a){this._addRequestFields(a);this._request({crprompt:"param"},false)},_onSubmitDBLogon:function(b){var a=this._viewer&&this._viewer.isPromptDialogVisible();if(this._viewer){this._viewer.hidePromptDialog()}this._addRequestFieldsFromContent(b);this._request({crprompt:"logon"},a)},_onSubmitPrintPdf:function(c,b,a){this._handlePrintOrExport(c,b,"PDF",a)},_onSubmitExport:function(c,a,b){this._handlePrintOrExport(c,a,b)},_handlePrintOrExport:function(l,c,g,b){var e=true;var a=false;if(!l&&!c){e=false}if(!g){g="PDF"}var k=(g=="PDF"&&b);var f={text:g,range:e+""};f.tb=k?"crpdfprint":"crexport";if(e){f.from=l+"";f.to=c+""}bobj.crv.logger.info("UIAction Export.Submit "+g);if(this._ioHandler instanceof bobj.crv.ServletAdapter||this._ioHandler instanceof bobj.crv.FacesAdapter){a=true;this._ioHandler.redirectToServlet();this._ioHandler.addRequestField("ServletTask","Export");this._ioHandler.addRequestField("LoadInIFrame",a)}else{if(this._ioHandler instanceof bobj.crv.AspDotNetAdapter){a=true}else{a=k}}this._request(f,false,false,a)},_onCancelParamDlg:function(){bobj.crv.logger.info("UIAction PromptDialog.Cancel");this._viewer.hidePromptDialog()},_onReceiveParamDlg:function(a){this._viewer.showPromptDialog(a)},_onSort:function(a){bobj.crv.logger.info("UIAction Report.Sort");this._request(a,bobj.crv.config.useAsync,true)},_onNavigateReportPart:function(a){bobj.crv.logger.info("UIAction ReportPart.Navigate");this._request(a,false)},_onNavigateBookmark:function(a){bobj.crv.logger.info("UIAction Report.Navigate");this._request(a,bobj.crv.config.useAsync,true)},getCurrentExpandedPaths:function(){var a=this._getViewState();if(a){return a.gpTreeCurrentExpandedPaths}return{}},applyParams:function(g){if(g){bobj.crv.logger.info("UIAction ParameterPanel.Apply");var b=[];var c=MochiKit.Base.clone;for(var e=0,a=g.length;e<a;e++){var f=c(g[e]);f.modifiedValue=null;f.value=c(g[e].value);if(this._ioHandler instanceof bobj.crv.ServletAdapter||this._ioHandler instanceof bobj.crv.FacesAdapter){this._encodeParameter(f)}b.push(f)}this._request({crprompt:"paramPanel",paramList:b},bobj.crv.config.useAsync,true)}},getServletURI:function(){var a="";if(this._ioHandler instanceof bobj.crv.ServletAdapter||this._ioHandler instanceof bobj.crv.FacesAdapter){a=this._ioHandler._servletUrl}return a},showAdvancedParamDialog:function(e){var a=this._getCommonProperty("paramOpts");this._focusedParamName=e.paramName;if(this._isPromptingTypeFlex()){if(!a.canOpenAdvancedDialog){this.showError(L_bobj_crv_AdvancedDialog_NoAjax,L_bobj_crv_EnableAjax)}else{var c=bobj.crv.params.ViewerFlexParameterAdapter;c.setCurrentIParamInfo(this._name,this._paramCtrl,e);if(!c.hasIParamPromptUnitData(this._name)){this._request({promptDlg:this._cloneParameter(e)},true)}else{if(e.allowMultiValue&&e.allowRangeValue&&e.modifiedValue.length>5){if(this._reportProcessing){this._reportProcessing.Show()}}var b=this._getPromptDialogCloseCB();this._viewer.showFlexPromptDialog(this.getServletURI(),b)}}}else{this._request({promptDlg:this._cloneParameter(e)},true)}},_cloneParameter:function(b){var a=MochiKit.Base.clone(b);a.defaultValues=null;a.modifiedValue=null;if(this._ioHandler instanceof bobj.crv.ServletAdapter||this._ioHandler instanceof bobj.crv.FacesAdapter){a.value=MochiKit.Base.clone(b.value);a=this._encodeParameter(a)}return a},_encodeParameter:function(c){if(c){if(c.value&&c.valueDataType==bobj.crv.params.DataTypes.STRING){for(var b=0,a=c.value.length;b<a;b++){if(bobj.isString(c.value[b])){c.value[b]=encodeURIComponent(c.value[b])}else{if(bobj.isObject(c.value[b])){var e=null;if(c.value[b].beginValue){e=bobj.crv.params.getValue(c.value[b].beginValue);c.value[b].beginValue=encodeURIComponent(e)}if(c.value[b].endValue){e=bobj.crv.params.getValue(c.value[b].endValue);c.value[b].endValue=encodeURIComponent(e)}}}}}if(c.paramName){c.paramName=encodeURIComponent(c.paramName)}if(c.reportName){c.reportName=encodeURIComponent(c.reportName)}}return c},_setViewProperty:function(b,c){var a=this._getViewState();if(a){a[b]=c}},_getViewProperty:function(b){var a=this._getViewState();if(a){return a[b]}return null},_setCommonProperty:function(b,c){var a=this._getCommonState();if(a){a[b]=c}},_getCommonProperty:function(b){var a=this._getCommonState();if(a){return a[b]}return null},_updateUIState:function(a){},_getViewState:function(){var a=bobj.crv.stateManager.getComponentState(this._name);if(a&&a.curViewId!==undefined){return a[a.curViewId]}return null},_getCommonState:function(){var a=bobj.crv.stateManager.getComponentState(this._name);if(a){return a.common}return null},_setInteractiveParams:function(e){if(!this._ioHandler.canUseAjax()){var c=this._viewer.getParameterPanel();if(c){c.showError(L_bobj_crv_InteractiveParam_NoAjax)}e();return}var g=[];var f=[];var m=this._getCommonProperty("parameterFields");if(m){var l=bobj.crv.params.Parameter;for(var b=0;b<m.length;b++){var a=new l(m[b]);if(a.isInteractive()){f.push(a)}else{g.push(a)}}}if(f&&f.length){var k=function(p,n,q){return function(){var t=p._viewer.getParameterPanel();if(t){var r=p._getCommonProperty("paramOpts");var s=new bobj.crv.params.ParameterController(t,p,r);s.setParameters(n,e);s.setUnusedParameters(q);p.setParameterController(s)}}};bobj.loadJSResourceAndExecCallBack(bobj.crv.config.resources.ParameterControllerAndDeps,k(this,f,g))}else{e()}},_isPromptingTypeFlex:function(){var a=this.getPromptingType();return(a&&a.toLowerCase()==bobj.crv.Viewer.PromptingTypes.FLEX)},setParameterController:function(a){this._paramCtrl=a},clearAdvancedPromptData:function(){if(this._isPromptingTypeFlex()){bobj.crv.params.ViewerFlexParameterAdapter.clearIParamPromptUnitData(this._name)}},_onPromptDialogUpdate:function(e){if(e.resolvedFields){this._viewer.hidePromptDialog();if(this._paramCtrl){for(var b=0;b<e.resolvedFields.length;b++){var c=new bobj.crv.params.Parameter(e.resolvedFields[b]);this._paramCtrl.updateParameter(c.paramName,c.getValue())}this._paramCtrl._updateToolbar()}}else{if(this._isPromptingTypeFlex()){if(e.script){bobj.evalInWindow(e.script);var a=this._getPromptDialogCloseCB();this._viewer.showFlexPromptDialog(this.getServletURI(),a)}}else{if(e.html){if(this._viewer.isPromptDialogVisible()){this._viewer.updatePromptDialog(e.html)}else{var a=this._getPromptDialogCloseCB();this._viewer.showPromptDialog(e.html,a)}}}}},_getPromptDialogCloseCB:function(){var a=null;if(this._paramCtrl&&this._focusedParamName){a=this._paramCtrl.getFocusAdvButtonCB(this._focusedParamName);this._focusedParamName=null}return a},_onPromptingAsyncRequest:function(a){this._request(a,true,false,false)},_request:function(g,f,l,b,m){var k=bobj.crv.stateManager.getCompositeState();var e=MochiKit.Base.bind;var a=m?m:e(this._onResponse,this,g);var c=e(this._onIOError,this);if(!bobj.isBoolean(l)){l=true}if(this._reportProcessing&&l){this._reportProcessing.delayedShow()}var n=this._ioHandler.request(k,this._name,g,f,b,a,c);if(n){if(this._reportProcessing&&l){this._reportProcessing.setDeferred(n)}n.addCallback(a);n.addErrback(c)}},_onResponse:function(f,a){var e=null;if(bobj.isString(a)){e=MochiKit.Base.evalJSON(a)}else{e=MochiKit.Async.evalJSONRequest(a)}if(e){if(e.needsReload){this._request(f,false,true);return}if(e.redirect){window.location=e.redirect;return}if(e.status&&this._viewer&&(e.status.errorMessage||e.status.debug)){var b=e.status.errorMessage||L_bobj_crv_RequestError;this.showError(b,e.status.debug)}if(e.state){var c=e.state;if(bobj.isString(c)){c=MochiKit.Base.evalJSON(c)}bobj.crv.stateManager.setComponentState(this._name,c)}if(e.update){if(e.update.promptDlg){this._onPromptDialogUpdate(e.update.promptDlg);bobj.crv.logger.info("Update InteractiveParams")}else{if(this._viewer){this._viewer.update(e.update);bobj.crv.logger.info("Update Viewer")}}}if(e.script&&e.script.length>0){bobj.evalInWindow(e.script);bobj.crv.logger.info("Execute Script")}}if(this._reportProcessing){this._reportProcessing.cancelShow()}if(bobj.isParentWindowTestRunner()){MochiKit.Signal.signal(this._viewer,"updated")}},_onIOError:function(b){if(this._reportProcessing.wasCancelled()==true){return}if(this._viewer){var c=this._ioHandler.processError(b);var a="";if(bobj.isString(c)){a=c}else{for(var e in c){if(bobj.isString(c[e])||bobj.isNumber(c[e])){a+=e+": "+c[e]+"\n"}}}this.showError(L_bobj_crv_RequestError,a)}if(this._reportProcessing){this._reportProcessing.cancelShow()}},_saveViewState:function(){var a=bobj.crv.stateManager.getCompositeState();this._ioHandler.saveViewState(a,this._name)},_addRequestFields:function(b){var c=document.getElementById(b);if(c){for(var a in c){var e=c[a];if(e&&e.name&&e.value){this._addRequestField(e.name,e.value)}}}},_addRequestFieldsFromContent:function(f){var b=document.getElementById(f);if(!b){return}var c=MochiKit.DOM.getElementsByTagAndClassName("input",null,b);for(var a in c){var e=c[a];if(e.type&&e.type.toLowerCase()=="checkbox"&&e.name){if(e.checked){this._addRequestField(e.name,e.value)}}else{if(e&&e.name&&e.value){this._addRequestField(e.name,e.value)}}}},_addRequestField:function(a,b){this._ioHandler.addRequestField(a,b)},_removeRequestField:function(a){this._ioHandler.removeRequestField(a)},_onHyperlinkClicked:function(e){e=MochiKit.Base.parseQueryString(e);var b=this._viewer.getEventListeners("hyperlinkClicked");var g=false;if(b){for(var f=0,c=b.length;f<c;f++){if(b[f](e)==true){g=true}}}if(g){return}var a=window;if(e.target&&e.target!="_self"){a.open(e.url,e.target)}else{a.location=e.url}}};bobj.crv.StateManager=function(){this._state={}};bobj.crv.StateManager.prototype={setViewState:function(a,e,c){var b=this._state;if(!b[a]){b[a]={}}b[a][e]=c},getViewState:function(a,c){var b=this._state;if(!b[a]){return null}return b[a][c]},setComponentState:function(a,b){this._state[a]=b},getComponentState:function(a){return this._state[a]},getCompositeState:function(){return this._state}};if(typeof bobj.crv.viewerState=="undefined"){bobj.crv.stateManager=new bobj.crv.StateManager()}bobj.crv.IOAdapterBase={request:function(){},addRequestField:function(a,b){},removeRequestField:function(a){},saveViewState:function(a,b){},getPostDataForPrinting:function(a,b){},processError:function(a){return a},canUseAjax:function(){try{return(MochiKit.Async.getXMLHttpRequest()!==null)}catch(a){return false}},_getPostbackIframe:function(){if(!this._iframe){ifrm=document.createElement("IFRAME");ifrm.id=bobj.uniqueId();ifrm.name=ifrm.id;ifrm.style.width="0px";ifrm.style.height="0px";ifrm.style.position="absolute";ifrm.style.top="0px";ifrm.style.left="0px";ifrm.style.visibility="hidden";document.body.appendChild(ifrm);if(!ifrm.contentWindow.name){ifrm.contentWindow.name=ifrm.id}this._iframe=ifrm}return this._iframe}};bobj.crv.ServletAdapter=function(b,a){this._pageUrl=b;this._servletUrl=a;this._form=null};bobj.crv.ServletAdapter._requestParams={STATE:"CRVCompositeViewState",TARGET:"CRVEventTarget",ARGUMENT:"CRVEventArgument"};bobj.crv.ServletAdapter.prototype=MochiKit.Base.merge(bobj.crv.IOAdapterBase,{request:function(g,l,b,a,c){if(!this._form){this._createForm()}var e=bobj.crv.ServletAdapter._requestParams;var k=MochiKit.Base.serializeJSON;this._form[e.STATE].value=encodeURIComponent(k(g));this._form[e.TARGET].value=encodeURIComponent(l);this._form[e.ARGUMENT].value=encodeURIComponent(k(b));var m=null;if(a&&this._servletUrl){var f=MochiKit.Async.getXMLHttpRequest();f.open("POST",this._servletUrl,true);f.setRequestHeader("Content-Type","application/x-www-form-urlencoded");f.setRequestHeader("Accept","application/json");m=MochiKit.Async.sendXMLHttpRequest(f,MochiKit.Base.queryString(this._form))}else{if(c){this._form.target=this._getPostbackIframe().id}this._form.submit()}MochiKit.DOM.removeElement(this._form);this._form=null;return m},redirectToServlet:function(){if(!this._form){this._createForm()}this._form.action=this._servletUrl},_createForm:function(){var b=MochiKit.DOM;var a=bobj.crv.ServletAdapter._requestParams;this._form=b.FORM({name:bobj.uniqueId(),style:"display:none",method:"POST",enctype:"application/x-www-form-urlencoded;charset=utf-8",action:this._pageUrl},b.INPUT({type:"hidden",name:a.STATE}),b.INPUT({type:"hidden",name:a.TARGET}),b.INPUT({type:"hidden",name:a.ARGUMENT}));document.body.appendChild(this._form)},addRequestField:function(a,c){if(a&&c){if(!this._form){this._createForm()}var b=this._form[a];if(b){b.value=c}else{this._form.appendChild(MochiKit.DOM.INPUT({type:"hidden",name:a,value:c}))}}},removeRequestField:function(a){if(a){var c=this._form;if(c){var b=c[a];if(b){MochiKit.DOM.removeElement(b);if(c[a]){c[a]=null}}b=null}}},getPostDataForPrinting:function(c,f){var a=MochiKit.Base.serializeJSON;var e=bobj.crv.ServletAdapter._requestParams;var g=a(c);var b={};b[e.STATE]=encodeURIComponent(g);b[e.TARGET]=encodeURIComponent(f);b[e.ARGUMENT]=encodeURIComponent('"axprint="');if(document.getElementById("com.sun.faces.VIEW")){b["com.sun.faces.VIEW"]=encodeURIComponent(document.getElementById("com.sun.faces.VIEW").value)}return MochiKit.Base.queryString(b)},processError:function(a){if(!(typeof(a.number)=="undefined")&&a.number==404){return L_bobj_crv_ServletMissing}return a}});bobj.crv.AspDotNetAdapter=function(e,a,f,c,g){this._postbackEventReference=e;this._replacementParameter=a;this._stateID=f;this._aspnetVersion=g;this._form=null;this._callbackEventReference=c;this._additionalReqFlds=null;var b=bobj.getElementByIdOrName(this._stateID);if(b){this._form=b.form}if(this._isAspNetVersionPriorToVersion4()){WebForm_CallbackComplete=this.WebForm_CallbackComplete}};bobj.crv.AspDotNetAdapter.prototype=MochiKit.Base.merge(bobj.crv.IOAdapterBase,{request:function(pageState,viewerName,eventArgs,allowAsync,useIframe,callbackHandler,errbackHandler){var toJSON=MochiKit.Base.serializeJSON;if(eventArgs&&this._additionalReqFlds){eventArgs=MochiKit.Base.update(eventArgs,this._additionalReqFlds)}this._additionalReqFlds=null;var jsonEventArgs=toJSON(eventArgs);this.saveViewState(pageState,viewerName);if(allowAsync){if(typeof WebForm_InitCallback=="function"){__theFormPostData="";__theFormPostCollection=[];WebForm_InitCallback()}var callback=this._callbackEventReference.replace("'arg'","jsonEventArgs");callback=callback.replace("'cb'","callbackHandler");callback=callback.replace("'errcb'","errbackHandler");callback=callback.replace("'frmID'","this._form.id");return eval(callback)}else{if(useIframe){this._form.target=this._getPostbackIframe().id}var postbackCall;if(this._postbackEventReference.indexOf("'"+this._replacementParameter+"'")>=0){postbackCall=this._postbackEventReference.replace("'"+this._replacementParameter+"'","jsonEventArgs")}else{postbackCall=this._postbackEventReference.replace('"'+this._replacementParameter+'"',"jsonEventArgs")}eval(postbackCall);this._clearEventFields();this._form.target=""}},_isAspNetVersionPriorToVersion4:function(){if(this._aspnetVersion!=null){var sep=this._aspnetVersion.split(".");if(eval(sep[0])<4){return true}}return false},saveViewState:function(b,c){var a=MochiKit.Base.serializeJSON;var f=b[c];var e=bobj.getElementByIdOrName(this._stateID);if(e){e.value=a(f)}},getPostDataForPrinting:function(c,e){this.saveViewState(c,e);var b=MochiKit.DOM.formContents(this.form);var f=b[0];var a=b[1];f.push("crprint");a.push(e);var g=MochiKit.Base.queryString(f,a);return g},addRequestField:function(a,b){if(!this._additionalReqFlds){this._additionalReqFlds={}}this._additionalReqFlds[a]=b},_clearRequestField:function(a){if(a){if(this._form){var b=this._form[a];if(b){b.value=""}}}},_clearEventFields:function(){this._clearRequestField("__EVENTTARGET");this._clearRequestField("__EVENTARGUMENT")},WebForm_CallbackComplete:function(){for(var b=0;b<__pendingCallbacks.length;b++){callbackObject=__pendingCallbacks[b];if(callbackObject&&callbackObject.xmlRequest&&(callbackObject.xmlRequest.readyState==4)){if(!__pendingCallbacks[b].async){__synchronousCallBackIndex=-1}__pendingCallbacks[b]=null;var a="__CALLBACKFRAME"+b;var c=document.getElementById(a);if(c){c.parentNode.removeChild(c)}WebForm_ExecuteCallback(callbackObject)}}}});bobj.crv.FacesAdapter=function(a,b){this._formName=a;this._servletUrl=b;this._useServlet=false;if(!bobj.crv.FacesAdapter._hasInterceptedSubmit){this._interceptSubmit();bobj.crv.FacesAdapter._hasInterceptedSubmit=true}};bobj.crv.FacesAdapter._requestParams={STATE:"CRVCompositeViewState",TARGET:"CRVEventTarget",ARGUMENT:"CRVEventArgument"};bobj.crv.FacesAdapter.prototype=MochiKit.Base.merge(bobj.crv.IOAdapterBase,{request:function(n,q,b,a,e){var g=bobj.crv.FacesAdapter._requestParams;var p=MochiKit.Base.serializeJSON;var k=MochiKit.DOM.INPUT;var r=null;var c=this._getForm();if(!c){return}if(!c[g.TARGET]){c.appendChild(k({type:"hidden",name:g.TARGET}))}c[g.TARGET].value=encodeURIComponent(q);if(!c[g.ARGUMENT]){c.appendChild(k({type:"hidden",name:g.ARGUMENT}))}c[g.ARGUMENT].value=encodeURIComponent(p(b));if(!c[g.STATE]){c.appendChild(k({type:"hidden",name:g.STATE}))}c[g.STATE].value=encodeURIComponent(p(n));if(a&&this._servletUrl){var m=MochiKit.Async.getXMLHttpRequest();m.open("POST",this._servletUrl,true);m.setRequestHeader("Content-Type","application/x-www-form-urlencoded");m.setRequestHeader("Accept","application/json");r=MochiKit.Async.sendXMLHttpRequest(m,MochiKit.Base.queryString(c))}else{var f=c.action;if(this._useServlet===true){c.action=this._servletUrl}var l=c.target;if(e){c.target=this._getPostbackIframe().id}c.submit();c.action=f;c.target=l;this._useServlet=false}c[g.TARGET].value="";c[g.ARGUMENT].value="";c[g.STATE].value="";this.removeRequestField("ServletTask");return r},redirectToServlet:function(){this._useServlet=true},addRequestField:function(a,c){if(a&&c){var e=this._getForm();if(e){var b=e[a];if(b){b.value=c}else{e.appendChild(MochiKit.DOM.INPUT({type:"hidden",name:a,value:c}))}}}},removeRequestField:function(a){if(a){var c=this._getForm();if(c){var b=c[a];if(b){MochiKit.DOM.removeElement(b);if(c[a]){c[a]=null}}b=null}}},saveViewState:function(c,g){if(!bobj.crv.FacesAdapter._isStateSaved){var f=this._getForm();if(f){var e=bobj.crv.FacesAdapter._requestParams;var a=MochiKit.Base.serializeJSON;var b=MochiKit.DOM.INPUT;if(!f[e.STATE]){f.appendChild(b({type:"hidden",name:e.STATE}))}f[e.STATE].value=encodeURIComponent(a(c))}bobj.crv.FacesAdapter._isStateSaved=true}},_getForm:function(){return document.forms[this._formName]},_interceptSubmit:function(){var b=this._getForm();if(b){var a=b.submit;b.submit=function(){bobj.event.publish("saveViewState");b.submit=a;b.submit()}}},getPostDataForPrinting:function(c,f){var a=MochiKit.Base.serializeJSON;var e=bobj.crv.ServletAdapter._requestParams;var g=a(c);var b={};b[e.STATE]=encodeURIComponent(g);b[e.TARGET]=encodeURIComponent(f);b[e.ARGUMENT]=encodeURIComponent('"axprint="');if(document.getElementById("com.sun.faces.VIEW")){b["com.sun.faces.VIEW"]=encodeURIComponent(document.getElementById("com.sun.faces.VIEW").value)}return MochiKit.Base.queryString(b)},processError:function(a){if(!(typeof(a.number)=="undefined")&&a.number==404){return L_bobj_crv_ServletMissing}return a}});if(typeof bobj=="undefined"){bobj={}}bobj.ArgumentNormalizer=function(){this._rules=[]};bobj.ArgumentNormalizer.prototype={addRule:function(){this._rules.push(arguments)},normalize:function(){for(var b=0,f=this._rules.length;b<f;++b){var p=this._rules[b];if(p.length==arguments.length){var e={};for(var m=0,k=p.length;m<k;++m){var c=arguments[m];var g=p[m];if(bobj.isString(g)){var n=null;var a=g;var l=null}else{if(bobj.isArray(g)){var n=g[0];var a=g[1];var l=g[2]}else{var n=g.test;var a=g.name;var l=g.xform}}if(!n||n(c)){e[a]=l?l(c):c;if(m+1==k){return e}}else{break}}}}return null},normalizeArray:function(a){return this.normalize.apply(this,a)}};if(typeof bobj=="undefined"){bobj={}}if(typeof bobj.event=="undefined"){bobj.event={};bobj.event._topicSubscriptions={};bobj.event._globalSubscriptions=[]}bobj.event.publish=function(c){var b=bobj.slice(arguments,1);var f=bobj.event._topicSubscriptions[c];if(f){for(var e=0;e<f.length;++e){f[e]._notify.apply(null,b)}}var g=bobj.event._globalSubscriptions;for(var a=0;a<g.length;++a){g[a]._notify.apply(null,b)}};bobj.event.subscribe=function(){var a=bobj.event.subscribe._normalizer;if(!a){a=bobj.event.subscribe._normalizer=new bobj.ArgumentNormalizer();a.addRule("topic","target","methName");a.addRule([bobj.isString,"topic"],"callback");a.addRule("target","methName");a.addRule("callback")}return bobj.event.kwSubscribe(a.normalizeArray(arguments))};bobj.event.kwSubscribe=function(b){var e=MochiKit.Base.bind;var c={};if(b.callback){c._notify=b.callback}else{c._notify=e(b.target[b.methName],b.target)}if(b.topic){c.topic=b.topic;var a=bobj.event._topicSubscriptions;if(!a[b.topic]){a[b.topic]=[]}a[b.topic].push(c)}else{bobj.event._globalSubscriptions.push(c)}return c};bobj.event.unsubscribe=function(c){var b=bobj.event._globalSubscriptions;if(c.topic){b=bobj.event._topicSubscriptions[c.topic]}if(b){var a=MochiKit.Base.findIdentical(b,c);if(a!=-1){b.splice(a,1);delete c._notify}}};bobj.crv.newPromptPage=function(a){a=MochiKit.Base.update({id:bobj.uniqueId(),layoutType:"fixed",content:null,width:800,height:600,padding:5,top:0,left:0},a);var b=newWidget(a.id);b.widgetType="PromptPage";b._reportProcessing=null;bobj.fillIn(b,a);b.initOld=b.init;MochiKit.Base.update(b,bobj.crv.PromptPage);window[b.id]=b;return b};bobj.crv.PromptPage={setHTML:function(f){var n=this._pageNode;if(bobj.isString(f)){var b=bobj.html.extractHtml(f);n.innerHTML=b.html;var m=b.links;for(var l=0,g=m.length;l<g;++l){bobj.includeLink(m[l])}var c=b.scripts;for(var a=0,e=c.length;a<e;++a){var k=c[a];if(!k){continue}if(k.text){bobj.evalInWindow(k.text)}}}else{if(bobj.isObject(f)){n.innerHTML="";n.appendChild(f);var p=f.style;p.display="block";p.visibility="visible"}}},getHTML:function(){var k=bobj.html;var b=bobj.isBorderBoxModel();var e=this.height+this.topMargin+this.bottomMargin;var f=this.width+this.leftMargin+this.rightMargin;var m=b?e:this.height;var a=b?f:this.width;var c={position:"relative",width:a+"px",height:m+"px",top:this.top+"px",left:this.left+"px",border:"none","z-index":1,"background-color":this.bgColor};if(this.layoutType=="fixed"){c.overflow="auto"}var l={padding:this.padding+"px"};var g=k.DIV({id:this.id,style:c},k.DIV({id:this.id+"_page",style:l}));return g},init:function(){this._pageNode=document.getElementById(this.id+"_page");this.initOld();if(this.contentId){var b=document.getElementById(this.contentId);if(b){this.setHTML(b)}}else{if(this.content){this.setHTML(this.content);delete this.content}}var a=MochiKit.Signal.connect;if(this.layoutType.toLowerCase()=="client"){a(window,"onresize",this,"_doLayout")}this._doLayout()},_doLayout:function(){var b=this.layoutType.toLowerCase();if("client"==b){this.css.width="100%";this.css.height="100%"}else{if("fitreport"==b){this.css.width="100%";this.css.height="100%"}else{if(this.width!=null&&this.width.length>0){if(this.width.indexOf("px")>0||this.width.indexOf("%")>0){this.css.width=this.width}else{this.css.width=this.width+"px"}}if(this.height!=null&&this.height.length>0){if(this.height.indexOf("px")>0||this.height.indexOf("%")>0){this.css.height=this.height}else{this.css.height=this.height+"px"}}}}var a=this._reportProcessing;if(a&&a.layer){a.center()}},addChild:function(a){if(a.widgetType=="ReportProcessingUI"){this._reportProcessing=a}}};bobj.crv.newFlexPromptPage=function(a){a=MochiKit.Base.update({id:bobj.uniqueId(),layoutType:"fixed",width:800,height:600,padding:5,top:0,left:0},a);var b=newWidget(a.id);b.widgetType="FlexPromptPage";b._reportProcessing=null;bobj.fillIn(b,a);b.initOld=b.init;MochiKit.Base.update(b,bobj.crv.FlexPromptPage);window[b.id]=b;return b};bobj.crv.FlexPromptPage={setHTML:MochiKit.Base.noop,getHTML:function(){var b=bobj.isBorderBoxModel();var f=this.height+this.topMargin+this.bottomMargin;var g=this.width+this.leftMargin+this.rightMargin;var m=b?f:this.height;var a=b?g:this.width;var e=this.layoutType.toLowerCase()==bobj.crv.Viewer.LayoutTypes.FIXED;var c={position:"relative",width:e?a+"px":"100%",height:e?m+"px":"100%",top:this.top+"px",left:this.left+"px",border:"none","z-index":1,"background-color":this.bgColor};var l={padding:this.padding+"px",position:"absolute"};bobj.crv.params.ViewerFlexParameterAdapter.setViewerLayoutType(this.id,this.layoutType);var k=bobj.html;return k.DIV({id:this.id,style:c},k.DIV({id:this.id+"_page",style:l},k.DIV({id:this.contentId})))},init:function(){var a=MochiKit.Signal.connect;if(this.layoutType.toLowerCase()=="client"){a(window,"onresize",this,"_doLayout")}this._doLayout()},_doLayout:function(){var a=this._reportProcessing;if(a&&a.layer){a.center()}},addChild:function(a){if(a.widgetType=="ReportProcessingUI"){this._reportProcessing=a}}};if(typeof bobj.crv.PrintUI=="undefined"){bobj.crv.PrintUI={}}if(typeof bobj.crv.ExportUI=="undefined"){bobj.crv.ExportUI={}}if(typeof bobj.crv.ErrorDialog=="undefined"){bobj.crv.ErrorDialog={}}if(typeof bobj.crv.ReportProcessingUI=="undefined"){bobj.crv.ReportProcessingUI={}}bobj.crv.newPrintUI=function(c){if(!c.id){c=MochiKit.Base.update({id:bobj.uniqueId()},c)}var e=c.submitBtnLabel;if(!e){e=L_bobj_crv_submitBtnLbl}var b=c.infoTitle;if(!b){b=L_bobj_crv_PrintInfoTitle}var a=c.dialogTitle;if(!a){if(c.isActxPrinting){a=L_bobj_crv_ActiveXPrintDialogTitle}else{a=L_bobj_crv_PDFPrintDialogTitle}}var g=c.infoMsg;if(!g){g=L_bobj_crv_PrintInfo1;g+="\n";g+=L_bobj_crv_PrintInfo2}var f=newDialogBoxWidget(c.id+"_dialog",a,300,100,null,bobj.crv.PrintUI._cancel,false);f.infoMsg=g;f.infoTitle=b;f.actxId=f.id+"_actx";f.actxContainerId=f.id+"_actxdiv";f._processingPrinting=false;f._initOld=f.init;f._showOld=f.show;if(!c.isActxPrinting){f._fromBox=newIntFieldWidget(f.id+"_fromBox",null,null,null,null,true,"",50);f._fromBox.setDisabled=bobj.crv.PrintUI.disabledTextFieldWidget;f._toBox=newIntFieldWidget(f.id+"_toBox",null,null,null,null,true,"",50);f._toBox.setDisabled=bobj.crv.PrintUI.disabledTextFieldWidget;f._submitBtn=newButtonWidget(f.id+"_submitBtn",e,MochiKit.Base.bind(bobj.crv.PrintUI._submitBtnCB,f));f._submitBtn.setDelayCallback(false);f._allRadio=newRadioWidget(f.id+"_allRadio",f.id+"_grp",L_bobj_crv_PrintAllLbl,MochiKit.Base.bind(bobj.crv.PrintUI.disabledPageRange,f,true));f._allRadio.layerClass="dlgContent";f._rangeRadio=newRadioWidget(f.id+"_rangeRadio",f.id+"_grp",L_bobj_crv_PrintPagesLbl,MochiKit.Base.bind(bobj.crv.PrintUI.disabledPageRange,f,false));f._rangeRadio.layerClass="dlgContent"}f.widgetType="PrintUI";bobj.fillIn(f,c);MochiKit.Base.update(f,bobj.crv.PrintUI);return f};bobj.crv.PrintUI.disabledTextFieldWidget=function(a){TextFieldWidget_setDisabled.call(this,a);if(a){MochiKit.DOM.addElementClass(this.layer,"textDisabled")}else{MochiKit.DOM.removeElementClass(this.layer,"textDisabled")}};bobj.crv.PrintUI.disabledPageRange=function(a){if(this._fromBox&&this._toBox){this._fromBox.setDisabled(a);this._toBox.setDisabled(a)}};bobj.crv.PrintUI._submitBtnCB=function(){var b=null;var a=null;if(this._rangeRadio.isChecked()){b=parseInt(this._fromBox.getValue(),10);a=parseInt(this._toBox.getValue(),10);if(!b||!a||(b<0)||(b>a)){alert(L_bobj_crv_PrintPageRangeError);return}}if(this.widgetType=="PrintUI"){MochiKit.Signal.signal(this,"printSubmitted",b,a)}else{MochiKit.Signal.signal(this,"exportSubmitted",b,a,this._comboBox.getSelection().value)}this.show(false)};bobj.crv.PrintUI._getRPSafeURL=function(c){if(!c){return}if(c.indexOf("/")===0){return c}var b=window.location.href;var e=b.lastIndexOf("?");if(e>0){b=b.substring(0,e)}var a=b.lastIndexOf("/");if(a<0){return c}b=b.substring(0,a);return b+"/"+c};bobj.crv.PrintUI._getObjectTag=function(b){var c=[];c.push('<OBJECT width="0" height="0" ID="');c.push(this.actxId);c.push('" CLASSID="CLSID:');c.push(bobj.crv.ActxPrintControl_CLSID);c.push('" CODEBASE="');c.push(this._getRPSafeURL(this.codeBase));c.push("#Version=");c.push(bobj.crv.ActxPrintControl_Version);c.push('" VIEWASTEXT>');c.push('<PARAM NAME="PostBackData" VALUE="');c.push(b);c.push('">');c.push('<PARAM NAME="ServerResourceVersion" VALUE="');c.push(bobj.crv.ActxPrintControl_Version);c.push('">');if(this.lcid){c.push('<PARAM NAME="LocaleID" VALUE="');c.push(this.lcid);c.push('">')}if(this.url){c.push('<PARAM NAME="URL" VALUE="');c.push(this._getRPSafeURL(this.url));c.push('">')}if(this.title){c.push('<PARAM NAME="Title" VALUE="');c.push(this.title);c.push('">')}if(this.maxPage){c.push('<PARAM NAME="MaxPageNumber" VALUE="');c.push(this.maxPage);c.push('">')}if(this.paperOrientation){c.push('<PARAM NAME="PageOrientation" VALUE="');c.push(this.paperOrientation);c.push('">')}if(this.paperSize){c.push('<PARAM NAME="PaperSize" VALUE="');c.push(this.paperSize);c.push('">')}if(this.paperWidth){c.push('<PARAM NAME="PaperWidth" VALUE="');c.push(this.paperWidth);c.push('">')}if(this.paperLength){c.push('<PARAM NAME="PaperLength" VALUE="');c.push(this.paperLength);c.push('">')}if(this.driverName){c.push('<PARAM NAME="PrinterDriverName" VALUE="');c.push(this.driverName);c.push('">')}if(this.useDefPrinter){c.push('<PARAM NAME="UseDefaultPrinter" VALUE="');c.push(this.useDefPrinter);c.push('">')}if(this.useDefPrinterSettings){c.push('<PARAM NAME="UseDefaultPrinterSettings" VALUE="');c.push(this.useDefPrinterSettings);c.push('">')}if(this.sendPostDataOnce){c.push('<PARAM NAME="SendPostDataOnce" VALUE="');c.push(this.sendPostDataOnce);c.push('">')}c.push("</OBJECT>");c.push('<table id="');c.push(this.actxId);c.push('_wait" border="0" cellspacing="0" cellpadding="0" width="100%" ><tbody>');c.push('<tr><td align="center" valign="top">');var f=this;var e=f.getContainerWidth()-10;var a=f.getContainerHeight()-(2*f.pad+21+10);c.push('<table style="');c.push(sty("width",e));c.push(sty("height",a));c.push('" id="frame_table_');c.push(f.id);c.push('" cellspacing="0" cellpadding="0" border="0"><tbody><tr><td valign="top" class="dlgFrame" style="padding:5px" id="frame_cont_');c.push(f.id);c.push('">');c.push('<table border="0" cellspacing="0" cellpadding="0" width="100%"><tbody>');c.push('<tr><td align="center" style="padding-top:5px;">');c.push(img(_skin+"wait01.gif",200,40));c.push("</td></tr>");c.push('<tr><td align="left" style="padding-left:2px;padding-right:2px;padding-top:5px;">');c.push('<div class="icontext" style="wordWrap:break_word;">');c.push(convStr(L_bobj_crv_PrintControlProcessingMessage,false,true));c.push("</div></td></tr></tbody></table>");c.push("</td></tr></tbody></table>");c.push("</td></tr></tbody></table>");return c.join("")};bobj.crv.PrintUI._cancel=function(){if(this.isActxPrinting){document.getElementById(this.actxContainerId).innerHTML="";this._processingPrinting=false}};bobj.crv.PrintUI._processPrinting=function(){if(!this._processingPrinting){var b=document.getElementById(this.actxId);var a=document.getElementById(this.actxId+"_wait");if(b&&a){b.width="100%";b.height="100%";a.style.display="none"}this._processingPrinting=true}};bobj.crv.PrintUI.show=function(b,a){this._processingPrinting=false;if(b){if(!this.layer){targetApp(this.getHTML());this.init()}if(this.isActxPrinting){document.getElementById(this.actxContainerId).innerHTML=this._getObjectTag(a)}this._showOld(true)}else{if(this.layer){this._showOld(false)}}};bobj.crv.PrintUI.init=function(){this._initOld();if(!this.isActxPrinting){this._fromBox.init();this._toBox.init();this._submitBtn.init();this._allRadio.init();this._rangeRadio.init();this._allRadio.check(true);this._toBox.setDisabled(true);this._fromBox.setDisabled(true);if(this.widgetType=="ExportUI"){this._updateExportList()}}};bobj.crv.PrintUI.getHTML=function(){var b=bobj.html;var c=this;var a=c.beginHTML();if(!this.isActxPrinting){a+="<table cellspacing=0 cellpadding=0 border=0><tr><td><div class='dlgFrame'><table cellspacing=0 cellpadding=0 border=0 style='height:"+(this.height*0.9)+"px;width:"+this.width+"px;'><tr><td valign='top' class='naviBarFrame naviFrame'>"+(this.isExporting?this._getExportList():"")+"<fieldset style='border:0px;padding:0px'><legend style='position:relative;"+(_ie?"margin:0px -7px":"")+"'><table datatable='0' style='width:100%;line-height:10px;'><tr>"+(_ie?"<td class='dialogTitleLevel2'><label>":"<td class='dialogTitleLevel2'><label>")+L_bobj_crv_PrintRangeLbl+"</label></td></tr></table></legend><div style='margin:10px 25px;'>"+c._allRadio.getHTML()+c._rangeRadio.getHTML()+"<div style='padding-left:25px'><table class=dlgContent datatable='0'><tr><td align=right><label for='"+c._fromBox.id+"'> "+L_bobj_crv_PrintFromLbl+"</label></td><td align=left> "+c._fromBox.getHTML()+"</td></tr><tr><td align=right><label for='"+c._toBox.id+"'> "+L_bobj_crv_PrintToLbl+"</label></td><td align=left>"+c._toBox.getHTML()+"</td></tr></table></div></div></fieldset>"+(!this.isExporting?"<table style='width:100%;line-height:10px;'><tr><td class='dialogTitleLevel2' tabIndex=0><label>"+this.infoTitle+"</label></td></tr></table><div style='margin:10px 0px 10px 25px;' class='dlgHelpText'>"+this.infoMsg+"</div>":"")+"</td></tr></table></div></td></tr><tr><td align='right' valign='top'><table style='margin:6px 9px 0px 0px' cellspacing=0 cellpadding=0 border=0><tbody><tr><td>"+this._submitBtn.getHTML()+"</td></tbody></tr></table></td></tr></table>"}else{a+="<div id='"+this.actxContainerId+"'></div><script for=\""+this.actxId+'" EVENT="Finished(status, statusText)" language="javascript">getWidgetFromID("'+this.id+'").show(false);<\/script><script for="'+this.actxId+'" EVENT="PrintingProgress(pageNumber)" language="javascript">getWidgetFromID("'+this.id+'")._processPrinting();<\/script>'}a+=c.endHTML();a+=bobj.crv.getInitHTML(this.widx);return a};bobj.crv.newExportUI=function(a){a=MochiKit.Base.update({submitBtnLabel:L_bobj_crv_ExportBtnLbl,dialogTitle:L_bobj_crv_ExportDialogTitle,infoTitle:L_bobj_crv_ExportInfoTitle,infoMsg:L_bobj_crv_PrintInfo1,isExporting:true},a);var b=bobj.crv.newPrintUI(a);b._comboBox=newCustomCombo(b.id+"_combo",MochiKit.Base.bind(bobj.crv.ExportUI._onSelectFormat,b),false,270,L_bobj_crv_ExportFormatLbl,_skin+"../transp.gif",0,14);if(b._comboBox){b._comboBox.icon.border=0;b._comboBox.icon.h=14;b._comboBox.arrow.h=12;b._comboBox.arrow.dy+=2;b._comboBox.arrow.disDy+=2}b.widgetType="ExportUI";MochiKit.Base.update(b,bobj.crv.ExportUI);return b};bobj.crv.ExportUI._onSelectFormat=function(){var a=this._comboBox.getSelection().value;if(a=="CrystalReports"||a=="RPTR"||a=="RecordToMSExcel"||a=="RecordToMSExcel2007"||a=="CharacterSeparatedValues"||a=="XML"){this._fromBox.setDisabled(true);this._toBox.setDisabled(true);this._rangeRadio.check(false);this._rangeRadio.setDisabled(true);this._allRadio.check(true)}else{this._rangeRadio.setDisabled(false)}};bobj.crv.ExportUI.update=function(a){if(!a||a.cons!=="bobj.crv.newExportUI"){return}this.availableFormats=a.args.availableFormats;if(this._comboBox.initialized()){this._updateExportList()}};bobj.crv.ExportUI._updateExportList=function(){if(!this._comboBox.initialized()){this._comboBox.init()}this._updateComboItems();var a=this._comboBox.getItemByIndex(0);if(a!=null){this._comboBox.selectItem(a)}this._onSelectFormat()};bobj.crv.ExportUI._updateComboItems=function(){this._comboBox.removeAllMenuItems();var a=(bobj.isArray(this.availableFormats)?this.availableFormats.length:0);for(var b=0;b<a;b++){var c=this.availableFormats[b];this._comboBox.add(c.name,c.value,c.isSelected)}};bobj.crv.ExportUI._getExportList=function(){return"<table datatable='0' style='width:100%;line-height:10px;'><tr>"+(_ie?"<td class='dialogTitleLevel2'><label>":"<td class='dialogTitleLevel2'><label>")+L_bobj_crv_ExportFormatLbl+"</label></td></tr></table><div style='margin:10px 25px;'>"+this._comboBox.getHTML()+"</div>"};bobj.crv.ErrorDialog.getInstance=function(){if(!bobj.crv.ErrorDialog.__instance){bobj.crv.ErrorDialog.__instance=bobj.crv.newErrorDialog()}return bobj.crv.ErrorDialog.__instance};bobj.crv.newErrorDialog=function(a){a=MochiKit.Base.update({id:bobj.uniqueId(),title:L_bobj_crv_Error,text:null,detailText:null,okLabel:L_bobj_crv_OK,promptType:_promptDlgCritical},a);var b=newPromptDialog(a.id,a.title,a.text,a.okLabel,null,a.promptType,null,null,true,true);b.widgetType="ErrorDialog";bobj.fillIn(b,a);b._promptDlgInit=b.init;b._promptDialogSetText=b.setText;b._promptDialogShow=b.show;b._promptDialogSetTitle=b.setTitle;b._promptDialogSetPromptType=b.setPromptType;MochiKit.Base.update(b,bobj.crv.ErrorDialog);b.noCB=MochiKit.Base.bind(b._onClose,b);b.yesCB=b.noCB;b._detailBtn=newIconWidget(b.id+"_detailBtn",bobj.skinUri("../help.gif"),MochiKit.Base.bind(bobj.crv.ErrorDialog._onDetailBtnClick,b),L_bobj_crv_showDetails,L_bobj_crv_showDetails,16,16,0,0,22,0,true);return b};bobj.crv.ErrorDialog.init=function(){this._promptDlgInit();this._detailBtn.init();this._detailRow=document.getElementById(this.id+"_detRow");this._detailArea=document.getElementById(this.id+"_detArea");if(!this.detailText){this._detailBtn.show(false)}};bobj.crv.ErrorDialog.getHTML=function(){var m=bobj.html.TABLE;var b=bobj.html.TBODY;var f=bobj.html.TR;var p=bobj.html.TD;var q=bobj.html.PRE;var l=bobj.html.DIV;var n=PromptDialog_getimgPath(this.promptType);var g=PromptDialog_getimgAlt(this.promptType);var a="320";var e="300px";var k="100px";var c=m({"class":"dlgBody",width:a,cellpadding:"0",cellspacing:"5",border:"0"},b(null,f(null,p(null,m({"class":"dlgBody",cellpadding:"5",cellspacing:"0",border:"0"},b(null,f(null,p({align:"right",width:"32"},img(n,32,32,null,'id="dlg_img_'+this.id+'"',g)),p(),p({id:"dlg_txt_"+this.id,align:"left"},l({tabindex:"0"},convStr(this.text,false,true)))))))),f({id:this.id+"_detRow",style:{display:"none"}},p(null,l({"class":"infozone",style:{width:e,height:k,overflow:"auto"}},q({id:this.id+"_detArea"},this.detailText)))),f(null,p(null,getSep())),f(null,p(null,m({cellpadding:"5",cellspacing:"0",border:"0",width:"100%"},b(null,f(null,p({align:"left"},this._detailBtn.getHTML()),p({align:"right"},this.yes.getHTML()))))))));return this.beginHTML()+c+this.endHTML()};bobj.crv.ErrorDialog.setText=function(c,a){this.text=c;this.detailText=a;if(this.layer){this._promptDialogSetText(c||"");if(this._detailArea){this._detailArea.innerHTML=a||""}var b=a?true:false;this._detailBtn.show(b);if(!b){this.showDetails(false)}}};bobj.crv.ErrorDialog.setTitle=function(a){this.title=a;if(this.layer){this._promptDialogSetTitle(a||"")}};bobj.crv.ErrorDialog.setPromptType=function(a){this.promptType=a;if(this.layer){this._promptDialogSetPromptType(a)}};bobj.crv.ErrorDialog.show=function(a,b){if(typeof a=="undefined"){a=true}if(a){this._closeCB=b;if(!this.layer){targetApp(this.getHTML());this.init()}this.layer.onkeyup=DialogBoxWidget_keypress;DialogBoxWidget_keypress=MochiKit.Base.noop;this._promptDialogShow(true)}else{if(this.layer){this._closeCB=null;this._promptDialogShow(false)}}};bobj.crv.ErrorDialog.showDetails=function(a){if(typeof a=="undefined"){a=true}if(this._detailRow&&this._detailBtn){if(a){this._detailRow.style.display="";this._detailBtn.changeText(L_bobj_crv_hideDetails)}else{this._detailRow.style.display="none";this._detailBtn.changeText(L_bobj_crv_showDetails)}}};bobj.crv.ErrorDialog._onDetailBtnClick=function(){if(this._detailRow){this.showDetails(this._detailRow.style.display=="none")}};bobj.crv.ErrorDialog._onClose=function(){if(this._closeCB){this._closeCB();this._closeCB=null}DialogBoxWidget_keypress=this.layer.onkeyup;this.layer.onkeyup=null};bobj.crv.newReportProcessingUI=function(b){b=MochiKit.Base.update({id:bobj.uniqueId(),delay:250,message:L_bobj_crv_ReportProcessingMessage},b);var e=document.createElement("div");e.style.visibility="hidden";e.innerHTML=b.message;var a=e.innerHTML;e=null;var c=newWaitDialogBoxWidget(b.id,0,0,"",false,bobj.crv.ReportProcessingUI.cancelCB,true,a,true);c.widgetType="ReportProcessingUI";c.delay=b.delay;MochiKit.Base.update(c,bobj.crv.ReportProcessingUI);return c};bobj.crv.reportProcessingDialog=null;bobj.crv.timerID=null;bobj.crv.ReportProcessingUI.cancelCB=function(){bobj.crv.reportProcessingDialog.cancelled=true;if(bobj.crv.reportProcessingDialog.deferred!==null){bobj.crv.reportProcessingDialog.deferred.cancel()}bobj.crv.reportProcessingDialog.cancelShow()};bobj.crv.ReportProcessingUI.wasCancelled=function(){return bobj.crv.reportProcessingDialog.cancelled};bobj.crv.ReportProcessingUI._prepareToShow=function(){if(bobj.crv.reportProcessingDialog!==null){bobj.crv.reportProcessingDialog.cancelShow()}if(!this.layer){append2(document.body,this.getHTML());this.init()}this.deferred=null;bobj.crv.reportProcessingDialog=this};bobj.crv.ReportProcessingUI.Show=function(){this._prepareToShow();bobj.crv.reportProcessingDialog.show(true)};bobj.crv.ReportProcessingUI.delayedShow=function(){this._prepareToShow();bobj.crv.timerID=setTimeout("bobj.crv._showReportProcessingDialog ()",bobj.crv.reportProcessingDialog.delay)};bobj.crv.ReportProcessingUI.cancelShow=function(){if(bobj.crv.timerID){clearTimeout(bobj.crv.timerID)}if(bobj.crv.reportProcessingDialog){bobj.crv.reportProcessingDialog.show(false)}bobj.crv.reportProcessingDialog=null;bobj.crv.timerID=null};bobj.crv.ReportProcessingUI.setDeferred=function(a){bobj.crv.reportProcessingDialog.deferred=a;if(bobj.crv.reportProcessingDialog.wasCancelled()===true){a.cancel()}};bobj.crv._showReportProcessingDialog=function(){if(bobj.crv.reportProcessingDialog&&bobj.crv.reportProcessingDialog.delay!==0){bobj.crv.logger.info("ShowReportProcessingDialog");bobj.crv.reportProcessingDialog.show(true)}};bobj.crv.newStackedTab=function(a){var c=MochiKit.Base.update;a=c({id:bobj.uniqueId(),label:"",width:300,height:null,openAdvCB:null,name:"",isDataFetching:false},a);var b=newWidget(a.id);b.widgetType="StackedTab";bobj.fillIn(b,a);b._content=null;if(b.openAdvCB){b._advanceButton=newIconWidget(b.id+"_advBtn",bobj.crv.allInOne.uri,b.openAdvCB,null,L_bobj_crv_paramsOpenAdvance.replace("%1",b.name),10,10,3,bobj.crv.allInOne.openParameterArrowDy+(_ie?2:3));bobj.crv.setAllClasses(b._advanceButton,"arrow_button");b._advanceButton.margin=0}b._initWidget=b.init;b._resizeWidget=b.resize;c(b,bobj.crv.StackedTab);return b};bobj.crv.StackedTab={setTabDisabled:function(a){if(this._content){this._content.setTabDisabled(a)}if(this._advanceButton&&this._advanceButton.layer){bobj.disableTabbingKey(this._advanceButton.layer,a)}if(this._textCtn){bobj.disableTabbingKey(this._textCtn,a)}if(this._dataFetchLayer){bobj.disableTabbingKey(this._dataFetchLayer,a)}},init:function(){var b=MochiKit.Signal.connect;var e=MochiKit.Signal.signal;var a=MochiKit.Base.partial;this._initWidget();if(this._content){this._content.init()}if(this._advanceButton){this._advanceButton.init();this._onAdvanceButtonClickOld=MochiKit.Base.bind(this._advanceButton.layer.onclick,this._advanceButton.layer);this._advanceButton.layer.onclick=MochiKit.Base.bind(this.advButtonOnClick,this);this._advanceButton.css.width="14px"}this._dataFetchLayer=getLayer(this.id+"_df");this._labelCtn=getLayer(this.id+"_labelCtn");this._textCtn=getLayer(this.id+"_textCtn");this._contentCtn=getLayer(this.id+"_contentCtn");if(this._advanceButton){var c=this._advanceButton.layer;var f=bobj.bindFunctionToObject;b(this.layer,"onclick",f(IconWidget_upCB,c));b(this.layer,"onmouseover",f(IconWidget_realOverCB,c));b(this.layer,"onmouseout",f(IconWidget_realOutCB,c));b(this.layer,"onmousedown",f(IconWidget_downCB,c))}b(this._content,"ParameterUIResized",a(e,this,"StackedTabResized"))},getHTML:function(){var g=bobj.html;var k=g.DIV;var n=g.IMG;var l={"class":"stackedTab",cellpadding:"0",id:this.id,style:{cursor:this._advanceButton?_hand:"default"}};var m={id:this.id+"_labelCtn",cellpadding:"0","class":"crvnoselect stackedTabTitle"};var a=this._content?this._content.getHTML():"";var b=this._advanceButton?g.TD({width:"17px"},this._advanceButton.getHTML()):"";var c="";if(this.isDataFetching){var f="url(%1);";c=g.TD({width:"20px"},n({src:_skin+"../transp.gif",title:L_bobj_crv_ParamsDataTip,tabindex:0,id:this.id+"_df",style:{width:"16px",height:"16px","background-image":f.replace("%1",bobj.crv.allInOne.uri),"background-position":"0px "+(-bobj.crv.allInOne.paramDataFetchingDy)+"px","margin-right":"4px","vertical-align":"middle"}}))}var e=k(l,k(m,g.TABLE({cellpadding:"0",width:"100%",height:"20px",style:{"table-layout":"fixed"}},g.TD({style:{"vertical-align":"top",overflow:"hidden"}},k({"class":"stackedTabText",id:this.id+"_textCtn",title:this.label,tabIndex:0,style:{"font-weight":"bold",color:"#4F5C72"}},convStr(this.label))),c,b)),k({id:this.id+"_contentCtn","class":"stackedTabContentCtn"},a));return e},setDirty:function(b){if(this._textCtn){this._textCtn.style.fontStyle=b?"italic":"";this._textCtn.title=b?this.label+" "+L_bobj_crv_ParamsDirtyTip:this.label}if(this._labelCtn){var a=b?"stackedTabTitleDirty":"stackedTabTitle";this._labelCtn.setAttribute("className",a);this._labelCtn.setAttribute("class",a)}},resize:function(a){a=a-4;if(this._labelCtn){var b=!_saf;bobj.setOuterSize(this._labelCtn,a,null,b)}if(this._content){this._content.resize(a-2)}bobj.setOuterSize(this.layer,a)},setContent:function(a){this._content=a},getContent:function(){return this._content},focusAdvButton:function(){if(this._advanceButton&&this._advanceButton.focus){this._advanceButton.focus()}},advButtonOnClick:function(a){if(this._onAdvanceButtonClickOld){this._onAdvanceButtonClickOld(a);eventCancelBubble(a)}}};if(typeof(bobj.crv.StackedPanel)=="undefined"){bobj.crv.StackedPanel={}}bobj.crv.newStackedPanel=function(b){var f=MochiKit.Base;var e=f.update;var a=f.bind;b=e({id:bobj.uniqueId(),width:null,height:null},b);var c=newWidget(b.id);c.widgetType="StackedPanel";bobj.fillIn(c,b);c._tabs=[];c._initWidget=c.init;c._resizeWidget=c.resize;e(c,bobj.crv.StackedPanel);return c};bobj.crv.StackedPanel={init:function(){this._initWidget();var e=this._tabs;var b=this._numTabsWritten;while(b<e.length){append(this.layer,e[b].getHTML(),document);b++}for(var c=0,a=e.length;c<a;++c){e[c].init()}},setTabDisabled:function(b){for(var c=0,a=this._tabs.length;c<a;c++){this._tabs[c].setTabDisabled(b)}},getHTML:function(){var b=bobj.html.DIV;var a={};if(this.height){a.height=bobj.unitValue(this.height)}if(this.width){a.width=bobj.unitValue(this.width)}return b({id:this.id,style:a,"class":"stackedPanel",tabIndex:"-1"},this._getTabsHTML())},_getTabsHTML:function(){var c="";var b=this._tabs;var e=b.length;for(var a=0;a<e;++a){c+=b[a].getHTML()}this._numTabsWritten=e;return c},addTab:function(a){if(a){this._tabs.push(a);if(this.layer){append(this.layer,a.getHTML());a.init()}if(this.layer){a.resize(this.layer.clientWidth)}MochiKit.Signal.connect(a,"StackedTabResized",this,"_onStackedTabResize")}},getNumTabs:function(){return this._tabs.length},getTab:function(a){return this._tabs[a]},removeTab:function(a){if(a>=0&&a<this._tabs.length){var b=this._tabs[a];this._tabs.splice(a,1);delete _widgets[this._tabs.widx];if(b.layer){b.layer.parentNode.removeChild(b.layer)}}},_onStackedTabResize:function(){this.resize(this.getWidth())},resize:function(a,g){var c=!_saf;bobj.setOuterSize(this.layer,a,g,c);var f=this._tabs;var k=f.length;if(k){var b=this.layer.clientWidth;f[0].resize(b);if(b!=this.layer.clientWidth){b=this.layer.clientWidth;f[0].resize(b)}for(var e=1;e<k;++e){f[e].resize(b)}}}};if(typeof bobj.crv.params=="undefined"){bobj.crv.params={}}bobj.crv.params.DataTypes={DATE:"d",DATE_TIME:"dt",TIME:"t",STRING:"s",NUMBER:"n",CURRENCY:"c",BOOLEAN:"b"};bobj.crv.params.RangeBoundTypes={UNBOUNDED:0,EXCLUSIVE:1,INCLUSIVE:2};bobj.crv.params.DefaultDisplayTypes={Description:0,DescriptionAndValue:1};bobj.crv.params.CompareResults={TOO_BIG:1,TOO_SMALL:-1,EQUAL:0};bobj.crv.params.Parameter=function(b){var c=bobj.crv.params;var a=c.DefaultDisplayTypes;MochiKit.Base.update(this,{paramName:null,reportName:null,description:null,valueDataType:null,value:null,modifiedValue:null,defaultValues:null,defaultDisplayType:a.DescriptionAndValue,maxValue:null,minValue:null,allowCustomValue:true,allowDiscreteValue:true,allowMultiValue:false,allowNullValue:false,allowRangeValue:false,editMask:null,isOptionalPrompt:false,isEditable:true,isHidden:false,isDataFetching:false,attributes:null,isInUse:false,isShowOnPanel:false},b)};bobj.crv.params.Parameter.prototype={getTitle:function(){return(this.description||this.paramName)},isInteractive:function(){return this.isInUse&&(this.isShowOnPanel||this.isEditable)},getName:function(){return this.paramName},hasLOV:function(){return(this.defaultValues!=null&&this.defaultValues.length>0)},hasDescription:function(){return this.description!=null},isPassword:function(){return(this.editMask!==null&&this.editMask.toLowerCase()=="password")},getValue:function(){this._initModifiedValue();return this.modifiedValue},reset:function(){delete this.modifiedValue},removeValueAt:function(a){this._initModifiedValue();var b=this.modifiedValue[a];this.modifiedValue.splice(a,1)},setValue:function(b,e){this._initModifiedValue();if(arguments.length==1&&bobj.isArray(arguments[0])){var c=arguments[0];this.modifiedValue=c}else{if(arguments.length==2){var a=this.modifiedValue[b];this.modifiedValue[b]=e}}},clearValue:function(){this._initModifiedValue();this.modifiedValue=[]},commitValue:function(){this._initModifiedValue();this.value=this.modifiedValue.slice(0)},_initModifiedValue:function(){if(!this.modifiedValue){if(bobj.isArray(this.value)){this.modifiedValue=this.value.slice(0)}else{this.modifiedValue=[]}}},isDCP:function(){if(this.attributes!=null){if(this.attributes.IsDCP===true){return true}}return false}};bobj.crv.params.Validator=function(){};bobj.crv.params.Validator.ValueStatus={OK:0,ERROR:1,VALUE_MISSING:2,VALUE_INVALID_TYPE:3,VALUE_TOO_LONG:4,VALUE_TOO_SHORT:5,VALUE_TOO_BIG:6,VALUE_TOO_SMALL:7,VALUE_DUPLICATE:8};bobj.crv.params.Validator.getInstance=function(){if(!bobj.crv.params.Validator.__instance){bobj.crv.params.Validator.__instance=new bobj.crv.params.Validator()}return bobj.crv.params.Validator.__instance};bobj.crv.params.Validator.prototype={validateParameter:function(l){var k=bobj.crv.params;if(!l){return null}var b=k.Validator.ValueStatus;if(!bobj.isArray(l.value)||!l.value.length){return{isValid:false,reason:b.VALUE_MISSING}}var g=true;var f=[];for(var e=0,a=l.values.length;e<a;++e){var c=k.validateValue(l,e);f.push(c);g=g&&(c===ValueStatus.OK)}return{isValid:g,statusList:f}},validateValue:function(e,b){var a=bobj.crv.params.Validator.ValueStatus;if(!e||!bobj.isArray(e.value)||(b===undefined)){return a.ERROR}var c=this._getTypeValidatorFunc(e.valueDataType);if(!c){return a.ERROR}var f=bobj.crv.params.getValue(b);return c(e,f)},_getTypeValidatorFunc:function(b){var a=bobj.crv.params.DataTypes;switch(b){case a.STRING:return this._validateString;case a.NUMBER:case a.CURRENCY:return this._validateNumber;case a.DATE:case a.TIME:case a.DATE_TIME:return this._validateDateTime;case a.BOOLEAN:return this._validateBoolean;default:return null}},_validateString:function(f,c){var a=bobj.crv.params.Validator.ValueStatus;if(!bobj.isString(c)){return a.VALUE_INVALID_TYPE}var e=f.maxValue;var b=f.minValue;if(bobj.isNumber(e)&&c.length>e){return a.VALUE_TOO_LONG}if(bobj.isNumber(b)&&c.length<b){return a.VALUE_TOO_SHORT}return a.OK},_validateNumber:function(g,e){var a=bobj.crv.params.Validator.ValueStatus;var b=/^(\+|-)?(\d+((\.\d+)|(\.))?|\.\d+)$/;if(bobj.isString(e)&&b.test(e)){e=parseFloat(e)}else{if(!bobj.isNumber(e)){return a.VALUE_INVALID_TYPE}}var f=g.maxValue;var c=g.minValue;if(f!==null&&e>f){return a.VALUE_TOO_BIG}else{if(c!==null&&e<c){return a.VALUE_TOO_SMALL}else{return a.OK}}},_validateDateTime:function(f,e){var g=bobj.crv.params.CompareResults;var a=bobj.crv.params.Validator.ValueStatus;if(bobj.isObject(e)){var b=function(k){return bobj.isNumber(e[k])};if(MochiKit.Iter.every(["d","m","y","h","min","s","ms"],b)){var c=bobj.crv.params.getDateCompareFunc(f.valueDataType);if(f.minValue&&c(f.minValue,e)==g.TOO_BIG){return a.VALUE_TOO_SMALL}else{if(f.maxValue&&c(f.maxValue,e)==g.TOO_SMALL){return a.VALUE_TOO_BIG}else{return a.OK}}}}return a.VALUE_INVALID_TYPE},_validateBoolean:function(b,a){return bobj.crv.params.Validator.ValueStatus.OK}};bobj.crv.params.dateToJson=function(a){return{d:a.getDate(),m:a.getMonth(),y:a.getFullYear(),h:a.getHours(),min:a.getMinutes(),s:a.getSeconds(),ms:a.getMilliseconds()}};bobj.crv.params.getDateCompareFunc=function(b){var c=bobj.crv.params;var a=c.DataTypes;switch(b){case a.DATE:return c.compareDate;case a.TIME:return c.compareTime;case a.DATE_TIME:return c.compareDateTime;default:return null}};bobj.crv.params.compareDateTime=function(b,a){var f=bobj.crv.params;var g=f.CompareResults;var e=f.compareDate(b,a);var c=f.compareTime(b,a);if(e==g.EQUAL&&c==g.EQUAL){return g.EQUAL}if(e!=g.EQUAL){return e}else{return c}};bobj.crv.params.compareDate=function(b,a){var c=bobj.crv.params.CompareResults;if(b.d==a.d&&b.m==a.m&&b.y==a.y){return c.EQUAL}if(b.y>a.y){return c.TOO_BIG}else{if(b.y<a.y){return c.TOO_SMALL}}if(b.m>a.m){return c.TOO_BIG}else{if(b.m<a.m){return c.TOO_SMALL}}if(b.d>a.d){return c.TOO_BIG}else{if(b.d<a.d){return c.TOO_SMALL}}};bobj.crv.params.compareTime=function(b,a){var c=bobj.crv.params.CompareResults;if(b.h==a.h&&b.min==a.min&&b.s==a.s&&b.ms==a.ms){return c.EQUAL}if(b.h>a.h){return c.TOO_BIG}else{if(b.h<a.h){return c.TOO_SMALL}}if(b.min>a.min){return c.TOO_BIG}else{if(b.min<a.min){return c.TOO_SMALL}}if(b.s>a.s){return c.TOO_BIG}else{if(b.s<a.s){return c.TOO_SMALL}}if(b.ms>a.ms){return c.TOO_BIG}else{if(b.ms<a.ms){return c.TOO_SMALL}}};bobj.crv.params.jsonToDate=function(b){var a=new Date();if(b){a.setFullYear(b.y||0,b.m||0,b.d||1);a.setHours(b.h||0);a.setMinutes(b.min||0);a.setSeconds(b.s||0);a.setMilliseconds(b.ms||0)}return a};bobj.crv.params.getValue=function(a){if(a===undefined||a===null||a.value===undefined){return a}return a.value};bobj.crv.params.getDescription=function(a){if(a===undefined||a===null||a.desc===undefined){return null}return a.desc};bobj.crv.params.newParameterDialog=function(a){a=MochiKit.Base.update({id:bobj.uniqueId(),prompt:null,showCB:null,hideCB:null},a);var b=newDialogBoxWidget(a.id,L_bobj_crv_ParamsDlgTitle,a.width,a.height);bobj.fillIn(b,a);b._showDialogBox=b.show;b._initDialogBox=b.init;b._resizeSignal=null;MochiKit.Base.update(b,bobj.crv.params.ParameterDialog);return b};bobj.crv.params.ParameterDialog={init:function(){this._initDialogBox();this._form=document.getElementById(this.id+"_form");window.paramWindow=this},_checkInitialization:function(){if(!this.layer){targetApp(this.getHTML());this.init()}},show:function(a){if(a){this._checkInitialization();this.doLayout();this.setResize(MochiKit.Base.noop);this._showDialogBox(true);o._resizeSignal=MochiKit.Signal.connect(window,"onresize",this,"_onWindowResize")}else{if(this.layer){this._showDialogBox(false)}bobj.crv.SignalDisposer.dispose(o._resizeSignal,true)}if(a&&this.showCB){this.showCB()}else{if(!a&&this.hideCB){this.hideCB()}}},isVisible:function(){return(this.initialized()&&this.isDisplayed())},getPreferredHeight:function(){return Math.min(Math.max(100,winHeight()-100),this.getFormHeight())},getFormHeight:function(){var a=this._form.cloneNode(true);a.style.display="none";a.style.height="";document.body.appendChild(a);var b=MochiKit.Style.getElementDimensions(a);a.innerHTML="";document.body.removeChild(a);return b.h},_onWindowResize:function(){window.paramWindow.doLayout();window.paramWindow.center()},doLayout:function(){if(this._form){this._form.style.height=this.getPreferredHeight()+"px"}},updateHtmlAndDisplay:function(c){if(c){this._checkInitialization();if(this.isDisplayed()){this.show(false)}var e=bobj.html.extractHtml(c);var l="";for(var b=0,a=e.styles.length;b<a;b++){l+=e.styles[b].text+"\n"}var f=this.id+"_stylesheet";var k=getLayer(f);if(k){MochiKit.DOM.removeElement(k)}if(l.length>0){bobj.addStyleSheet(l,f)}if(this._form){this._form.innerHTML='<div style="overflow:auto">'+e.html+"</div>"}var g=function(n,m){return function(){n.show(true);for(var r=0,p=m.length;r<p;++r){var q=m[r];if(!q){continue}if(q.text){bobj.evalInWindow(q.text)}}}}(this,e.scripts);bobj.includeCSSLinksAndExecuteCallback(e.links,g)}},getHTML:function(c){var a=bobj.html.FORM;var e=bobj.html.DIV;var b="eventCancelBubble(event);return false;";return this.beginHTML()+e({"class":"dlgFrame naviBarFrame",style:{padding:"20px 15px 5px 20px"}},a({id:this.id+"_form",style:{overflow:"auto"},onsubmit:b}))+this.endHTML()}};bobj.crv.params.newParameterPanelToolbar=function(a){a=MochiKit.Base.update({id:bobj.uniqueId()},a);var b=newPaletteContainerWidget(a.id);bobj.fillIn(b,a);b.widgetType="ParameterPanelToolbar";b._paletteContainerInit=b.init;MochiKit.Base.update(b,bobj.crv.params.ParameterPanelToolbar);b._palette=newPaletteWidget(b.id+"_palette");b.add(b._palette);var c=MochiKit.Base.bind;b.applyButton=newIconWidget(b.id+"_applyBtn",bobj.crv.allInOne.uri,c(b._onApplyClick,b),L_bobj_crv_ParamsApply,L_bobj_crv_ParamsApplyDisabledTip,16,16,3,3+bobj.crv.allInOne.paramRunDy,25,3+bobj.crv.allInOne.paramRunDy,false);b.applyButton.setClasses("","","","");b.resetButton=newIconWidget(b.id+"_resetBtn",bobj.crv.allInOne.uri,c(b._onResetClick,b),L_bobj_crv_Reset,L_bobj_crv_ResetDisabledTip,16,16,0,bobj.crv.allInOne.undoDy,16,bobj.crv.allInOne.undoDy,false);b.resetButton.setClasses("","","","");b._palette.add(b.applyButton);b._palette.add();b._palette.add(b.resetButton);return b};bobj.crv.params.ParameterPanelToolbar={init:function(){this._paletteContainerInit();this._palette.init();this.applyButton.setDisabled(true);this.resetButton.setDisabled(true)},setTabDisabled:function(c){var b=[this.applyButton,this.resetButton];for(var e=0,a=b.length;e<a;e++){var f=b[e];if(f){bobj.disableTabbingKey(f.layer,c)}}},beginHTML:function(){return bobj.html.openTag("div",{id:this.id,"class":"parameterPanelToolbar"})},getHTML:function(){return(this.beginHTML()+this._palette.getHTML()+this.endHTML())},_onApplyClick:function(){if(this.applyClickCB){bobj.crv.logger.info("UIAction ParameterPanel.Apply");this.applyClickCB()}},_onResetClick:function(){if(this.resetClickCB){this.resetClickCB()}}};bobj.crv.params.newParameterPanel=function(a){a=MochiKit.Base.update({id:bobj.uniqueId()+"_IPPanel"},a);var b=newWidget(a.id);b.widgetType="ParameterPanel";bobj.fillIn(b,a);MochiKit.Base.update(b,bobj.crv.params.ParameterPanel);b._tabPanel=bobj.crv.newStackedPanel({id:b.id+"_ParamtersStack"});b._overlayLayer=new bobj.crv.params.ParameterPanel.OverlayLayer(b.id);b._toolbar=bobj.crv.params.newParameterPanelToolbar({id:b.id+"_IPToolbar"});return b};bobj.crv.params.ParameterPanel={setToolbarCallBacks:function(b,a){if(this._toolbar){this._toolbar.applyClickCB=b;this._toolbar.resetClickCB=a}},setDisabled:function(a){this._overlayLayer.setVisible(a);this.setTabDisabled(a)},setTabDisabled:function(a){this._toolbar.setTabDisabled(a);this._tabPanel.setTabDisabled(a)},init:function(){Widget_init.call(this);this._toolbar.init();if(this._tabPanel){this._tabPanel.init()}MochiKit.Signal.signal(this,"resetParamPanel")},update:function(a){if(a&&a.cons=="bobj.crv.params.newParameterPanel"){if(a.args&&a.args.isResetParamPanel){MochiKit.Signal.signal(this,"resetParamPanel")}}},getHTML:function(){var b=bobj.html.DIV;var a={overflow:"hidden",width:this.width?bobj.unitValue(this.width):"auto",height:this.height?bobj.unitValue(this.height):"auto"};var c=this._toolbar.getHTML();if(this._tabPanel){c+=this._tabPanel.getHTML()}return b({id:this.id,style:a},c)},getBestFitHeight:function(){var a=0;if(this._tabPanel){a+=bobj.getHiddenElementDimensions(this._tabPanel.layer).h}if(this._toolbar){a+=this._toolbar.getHeight()}return a},resize:function(a,b){Widget_resize.call(this,a,b);if(this._toolbar){a=this.layer.clientWidth;this._toolbar.resize(a);if(this._tabPanel){b=this.layer.clientHeight-this._toolbar.getHeight();this._tabPanel.resize(a,b)}}},addParameter:function(b){b=MochiKit.Base.update({paramUI:null,label:null,isDataFetching:false,openAdvCB:null,clearValuesCB:null,id:this._tabPanel.id+"_P"+(this._tabPanel.getNumTabs()+1)},b);if(b.paramUI){var a=bobj.crv.newStackedTab(b);a.setContent(b.paramUI);this._tabPanel.addTab(a)}},removeParameter:function(a){this._tabPanel.removeTab(a)},getWidth:function(){if(this.layer){return this.layer.offsetWidth}return this.width},setResetButtonEnabled:function(a){this._toolbar.resetButton.setDisabled(!a);var b=a?L_bobj_crv_ResetTip:L_bobj_crv_ResetDisabledTip;this._toolbar.resetButton.changeTooltip(b,true)},setApplyButtonEnabled:function(a){this._toolbar.applyButton.setDisabled(!a);var b=a?L_bobj_crv_ParamsApplyTip:L_bobj_crv_ParamsApplyDisabledTip;this._toolbar.applyButton.changeTooltip(b,true)},isApplyButtonEnabled:function(){return this._toolbar!=null&&this._toolbar.applyButton!=null&&!this._toolbar.applyButton.isDisabled()},getIndex:function(e){var c=this._tabPanel.getNumTabs();for(var a=0;a<c;++a){var b=this._tabPanel.getTab(a);if(b.getContent()===e){return a}}return -1},getParameterTabByWidget:function(b){var a=this.getIndex(b);if(a>=0){return this._tabPanel.getTab(a)}return null},getParameter:function(a){var b=this._tabPanel.getTab(a);if(b){return b.getContent()}return null},getParameterTab:function(a){return this._tabPanel.getTab(a)},getParameterCount:function(){return this._tabPanel.getNumTabs()}};bobj.crv.params.ParameterPanel.OverlayLayer=function(a){this.paramPanelId=a;this.layer=null;this.id=bobj.uniqueId();this.widx=_widgets.length;_widgets[this.widx]=this;return this};bobj.crv.params.ParameterPanel.OverlayLayer.prototype={setVisible:function(a){if(!this.layer){this.init()}if(this.css){this.css.visibility=a?"visible":"hidden"}},isVisible:function(){if(!this.layer){this.init()}return this.css.visibility=="visible"},getHTML:function(){return"<div id = "+this.id+' onselectstart="return false" ondragstart="return false" onmousedown="'+_codeWinName+'.eventCancelBubble(event)" border="0" hspace="0" vspace="0" src="'+_skin+'../transp.gif" class="paramPanelOverLay">'+(_ie?img(_skin+"../transp.gif","100%","100%",null,"ISMAP"):"")+"</div>"},init:function(){var a=getLayer(this.paramPanelId);if(a){append2(a,this.getHTML())}Widget_init.call(this)}};function bobj_WebForm_Callback(f,l,b){if(!f||!b){return}var m=document.getElementById(b);if(!m){return}var n=[];for(var e=0,a=m.elements.length;e<a;e++){var c=m.elements[e];if(c.name&&c.value){n.push(c.name);n.push("=");n.push(encodeURIComponent(c.value));n.push("&")}}n.push("__BOBJ_CALLBACK_EVENTTARGET=");n.push(encodeURIComponent(f));n.push("&__BOBJ_CALLBACK_EVENTARGUMENT=");n.push(encodeURIComponent(l));var k=n.join("");var g=MochiKit.Async.getXMLHttpRequest();g.open("POST",m.action,true);g.setRequestHeader("Content-Type","application/x-www-form-urlencoded");g.setRequestHeader("Accept","application/json");return MochiKit.Async.sendXMLHttpRequest(g,k)}if(typeof bobj=="undefined"){bobj={}}if(typeof bobj.crv=="undefined"){bobj.crv={}}if(typeof bobj.crv.Calendar=="undefined"){bobj.crv.Calendar={}}bobj.crv.Calendar.getInstance=function(){if(!bobj.crv.Calendar.__instance){bobj.crv.Calendar.__instance=bobj.crv.newCalendar()}return bobj.crv.Calendar.__instance};bobj.crv.Calendar.Signals={OK_CLICK:"okClick",CANCEL_CLICK:"cancelClick",ON_HIDE:"onHide"};bobj.crv.newCalendar=function(a){var c=MochiKit.Base.update;a=c({id:bobj.uniqueId()+"_calendar",showTime:false,date:new Date(),timeFormats:["HH:mm:ss","H:mm:ss","H:m:s","HH:mm","H:mm","H:m","h:mm:ss a","h:m:s a","h:mm:ssa","h:m:sa","h:mm a","h:m a","h:mma","h:ma"]},a);var b=newMenuWidget();b.widgetType="Calendar";bobj.fillIn(b,a);b._menuJustInTimeInit=b.justInTimeInit;c(b,bobj.crv.Calendar);b._curTimeFormat=b.timeFormats[0];b._cells=[];b._firstDay=0;b._numDays=0;return b};bobj.crv.Calendar._createHeaderButtons=function(){var a=8;var c=4;var b=46;var g=0;var e=12;var f=MochiKit.Base.bind;this._prevMonthBtn=newIconWidget(this.id+"_pm",_skin+"../lov.gif",f(this._onPrevMonthClick,this),"",_calendarPrevMonthLab,a,c,b,e);this._prevYearBtn=newIconWidget(this.id+"_py",_skin+"../lov.gif",f(this._onPrevYearClick,this),"",_calendarPrevYearLab,a,c,b,e);this._nextMonthBtn=newIconWidget(this.id+"_nm",_skin+"../lov.gif",f(this._onNextMonthClick,this),"",_calendarNextMonthLab,a,c,b,g);this._nextYearBtn=newIconWidget(this.id+"_ny",_skin+"../lov.gif",f(this._onNextYearClick,this),"",_calendarNextYearLab,a,c,b,g)};bobj.crv.Calendar._createTimeTextField=function(){var a=MochiKit.Base.bind;this._timeField=newTextFieldWidget(this.id+"_time",a(this._onTimeChange,this),null,null,null,true,null,null,null,null)};bobj.crv.Calendar._createOKCancelButtons=function(){var a=MochiKit.Base.bind;this._okBtn=newButtonWidget(this.id+"_ok",L_bobj_crv_OK,a(this._onOKClick,this));this._cancelBtn=newButtonWidget(this.id+"_cancel",L_bobj_crv_Cancel,a(this._onCancelClick,this))};bobj.crv.Calendar.justInTimeInit=function(){this._menuJustInTimeInit();this._prevMonthBtn.init();this._prevYearBtn.init();this._nextMonthBtn.init();this._nextYearBtn.init();this._okBtn.init();this._cancelBtn.init();this._timeField.init();this._timeField.layer.style.width="100%";this._timeField.setValue(bobj.external.date.formatDate(this.date,this._curTimeFormat));this._timeRow=getLayer(this.id+"_timeRow");this._timeSep=getLayer(this.id+"_timeSep");this._month=getLayer(this.id+"_month");this._year=getLayer(this.id+"_year");var b=6*7;for(var a=0;a<b;a++){this._cells[a]=getLayer(this.id+"_c"+a)}this._update()};bobj.crv.Calendar.getHTML=function(){var l=bobj.html;var p=l.TABLE;var a=l.TBODY;var c=l.TR;var s=l.TD;var n=l.DIV;var r=l.SPAN;var b=l.A;this._createHeaderButtons();this._createTimeTextField();this._createOKCancelButtons();var m="MenuWidget_keyDown('"+this.id+"', event); return true";var e="eventCancelBubble(event)";var q="eventCancelBubble(event)";var g="eventCancelBubble(event)";var f={"class":"calendarTextPart"};var k=p({dir:"ltr",id:this.id,border:"0",cellpadding:"0",cellspacing:"0",onkeydown:m,onmousedown:e,onmouseup:q,onkeypress:g,"class":"menuFrame",style:{cursor:"default",visibility:"hidden","z-index":10000}},a(null,c(null,s(null,this._getMonthYearHTML())),c(null,s({align:"center"},p({border:"0",cellspacing:"0",cellpadding:"0",style:{margin:"2px","margin-top":"6px"}},c({align:"center"},s(f,L_bobj_crv_SundayShort),s(f,L_bobj_crv_MondayShort),s(f,L_bobj_crv_TuesdayShort),s(f,L_bobj_crv_WednesdayShort),s(f,L_bobj_crv_ThursdayShort),s(f,L_bobj_crv_FridayShort),s(f,L_bobj_crv_SaturdayShort)),c(null,s({colspan:"7",style:{padding:"2px"}},this._getSeparatorHTML())),this._getDaysHTML(),c(null,s({colspan:"7",style:{padding:"2px"}},this._getSeparatorHTML())),c({id:this.id+"_timeRow",style:{display:this.showTime?"":"none"}},s({colspan:"7",style:{"padding-top":"3px","padding-bottom":"3px","padding-right":"10px","padding-left":"10px"}},this._timeField.getHTML())),c({id:this.id+"_timeSep",style:{display:this.showTime?"":"none"}},s({colspan:"7",style:{padding:"2px"}},this._getSeparatorHTML())),c(null,s({colspan:"7",align:"right",style:{"padding-bottom":"3px","padding-top":"3px"}},p(null,a(null,c(null,s(null,this._okBtn.getHTML()),s(null,this._cancelBtn.getHTML())))))))))));return this._getLinkHTML("startLink_"+this.id)+k+this._getLinkHTML("endLink_"+this.id)};bobj.crv.Calendar._getMonthYearHTML=function(){var c=bobj.html;var e=c.TABLE;var b=c.TBODY;var g=c.TR;var a=c.TD;var f=c.DIV;var k=c.SPAN;return e({"class":"dialogzone",width:"100%",cellpadding:"0",cellspacing:"0"},b(null,g(null,a({style:{"padding-top":"1px"}},this._nextMonthBtn.getHTML()),a({rowspan:"2",width:"100%",align:"center","class":"dialogzone"},k({id:this.id+"_month",tabIndex:"0"},_month[this.date.getMonth()]),"&nbsp;&nbsp;",k({id:this.id+"_year",tabIndex:"0"},this.date.getFullYear())),a({style:{"pading-top":"1px"}},this._nextYearBtn.getHTML())),g({valign:"top"},a({style:{"padding-bottom":"1px"}},this._prevMonthBtn.getHTML()),a({style:{"padding-bottom":"1px"}},this._prevYearBtn.getHTML()))))};bobj.crv.Calendar._getSeparatorHTML=function(){var c=bobj.html;var e=c.TABLE;var b=c.TBODY;var f=c.TR;var a=c.TD;return e({width:"100%",height:"3",cellpadding:"0",cellspacing:"0",border:"0",style:backImgOffset(_skin+"menus.gif",0,80)},b(null,f(null,a())))};bobj.crv.Calendar._getLinkHTML=function(a){return bobj.html.A({id:a,href:"javascript:void(0)",onfocus:"MenuWidget_keepFocus('"+this.id+"')",style:{visibility:"hidden",position:"absolute"}})};bobj.crv.Calendar._getDaysHTML=function(){var a=bobj.html.TD;var e=bobj.html.DIV;var c="";for(i=0;i<6;++i){c+='<tr align="right">';for(j=0;j<7;++j){var f=j+(i*7);var b="(this,"+f+",event);";c+=a({id:this.id+"_c"+(i*7+j),"class":"calendarTextPart",onmousedown:"bobj.crv.Calendar._onDayMouseDown"+b,onmouseover:"bobj.crv.Calendar._onDayMouseOver"+b,onmouseout:"bobj.crv.Calendar._onDayMouseOut"+b,ondblclick:"bobj.crv.Calendar._onDayDoubleClick"+b,onkeydown:"bobj.crv.Calendar._onDayKeyDown"+b},e({"class":"menuCalendar"}))}c+="</tr>"}return c};bobj.crv.Calendar._update=function(){var l=6*7;var c=this.date.getDate();var b=this._getMonthInfo(this.date.getMonth(),this.date.getFullYear());var a=b.firstDay;this._firstDay=b.firstDay;this._numDays=b.numDays;var g=""+this.date.getFullYear();while(g.length<4){g="0"+g}this._year.innerHTML=g;this._month.innerHTML=_month[this.date.getMonth()];this._selectedDate=null;for(var f=0;f<l;f++){var k=this._cells[f].firstChild;var m="menuCalendar";var e=this._getDateFromCellNum(f);if(e<1||e>b.numDays){k.innerHTML="";k.tabIndex="-1"}else{k.innerHTML=""+e;k.tabIndex="0";if(e==c){m="menuCalendarSel";this._selectedDay=k}}k.className=m}};bobj.crv.Calendar._getMonthInfo=function(g,f){var c=new Date();c.setDate(1);c.setFullYear(f);c.setMonth(g);var a=c.getDay();c.setDate(28);var b=28;for(var e=29;e<32;e++){c.setDate(e);if(c.getMonth()!=g){break}b=e}return{firstDay:a,numDays:b}};bobj.crv.Calendar._setDayOfMonth=function(a){if(a>0&&a<=this._numDays){var c=this.date.getDate();if(a!=c){var b=this._getCellFromDate(c);if(b){b.firstChild.className="menuCalendar"}this._getCellFromDate(a).firstChild.className="menuCalendarSel";this.date.setDate(a)}}};bobj.crv.Calendar._getCellFromDate=function(a){var b=a+this._firstDay-1;return this._cells[b]};bobj.crv.Calendar._getDateFromCellNum=function(a){return a-this._firstDay+1};bobj.crv.Calendar._onDayMouseOver=function(c,f,b){var e=getWidget(c);var g=c.firstChild;var a=f-e._firstDay+1;if(a<1||a>e._numDays){g.className="menuCalendar"}else{g.className="menuCalendarSel"}};bobj.crv.Calendar._onDayMouseOut=function(c,f,b){var e=getWidget(c);var g=c.firstChild;var a=f-e._firstDay+1;if(a!=e.date.getDate()){g.className="menuCalendar"}};bobj.crv.Calendar._onDayMouseDown=function(c,f,b){var e=getWidget(c);var a=f-e._firstDay+1;e._setDayOfMonth(a)};bobj.crv.Calendar._onDayDoubleClick=function(b,e,a){var c=getWidget(b);c._onOKClick()};bobj.crv.Calendar._onDayKeyDown=function(e,g,c){c=new MochiKit.Signal.Event(e,c);var b=c.key().string;if(b==="KEY_ENTER"){var f=getWidget(e);var a=g-f._firstDay+1;f._setDayOfMonth(a)}};bobj.crv.Calendar._onPrevMonthClick=function(){var b=this.date;var a=b.getMonth();if(b.getMonth()===0){b.setYear(b.getFullYear()-1);b.setMonth(11)}else{b.setMonth(b.getMonth()-1);if(a===b.getMonth()){b.setMonth(a-1)}}this._update()};bobj.crv.Calendar._onPrevYearClick=function(){this.date.setFullYear(this.date.getFullYear()-1);this._update()};bobj.crv.Calendar._onNextMonthClick=function(){var b=this.date;var a=b.getMonth();b.setMonth(b.getMonth()+1);if((a+1)<b.getMonth()){b.setMonth(a+1)}this._update()};bobj.crv.Calendar._onNextYearClick=function(){this.date.setFullYear(this.date.getFullYear()+1);this._update()};bobj.crv.Calendar._onOKClick=function(){this.restoreFocus();MochiKit.Signal.signal(this,this.Signals.OK_CLICK,this._copyDate(this.date));this.show(false)};bobj.crv.Calendar._copyDate=function(a){if(a){return new Date(a.getFullYear(),a.getMonth(),a.getDate(),a.getHours(),a.getMinutes(),a.getSeconds(),a.getMilliseconds())}return new Date()};bobj.crv.Calendar._onCancelClick=function(){this.restoreFocus();this.show(false);MochiKit.Signal.signal(this,this.Signals.CANCEL_CLICK)};bobj.crv.Calendar._onTimeChange=function(){var e=this._timeField.getValue();var a=null;var c=null;for(var b=0;b<this.timeFormats.length&&a===null;++b){c=this.timeFormats[b];a=bobj.external.date.getDateFromFormat(e,c)}if(a){this._curTimeFormat=c;this.date.setHours(a.getHours());this.date.setMinutes(a.getMinutes());this.date.setSeconds(a.getSeconds());this.date.setMilliseconds(a.getMilliseconds())}else{this._timeField.setValue(bobj.external.date.formatDate(this.date,this._curTimeFormat))}};bobj.crv.Calendar.setShowTime=function(b){var a=b?"":"none";this.showTime=b;if(this.layer){this._timeRow.style.display=a;this._timeSep.style.display=a}};bobj.crv.Calendar.setDate=function(a){this.date=a;if(this.layer){this._timeField.setValue(bobj.external.date.formatDate(this.date,this._curTimeFormat));this._update()}};bobj.crv.Calendar.show=function(e,a,f,c,b){ScrollMenuWidget_show.call(this,e,a,f);if(e){this.focus()}else{MochiKit.Signal.signal(this,this.Signals.ON_HIDE)}};bobj.crv.Calendar.focus=function(){if(this._selectedDay){this._selectedDay.focus()}};bobj.crv.params.ViewerFlexParameterAdapter={_viewerLayoutType:[],_promptData:[],_paramCtrl:[],_iParam:[],_iPromptUnitData:[],_iParamData:[],_moveArea:null,setViewerLayoutType:function(b,a){this._viewerLayoutType[b]=a},setPromptData:function(g,f,b){if(!b){this._promptData[g]=f;this.clearIParamPromptUnitData(g)}else{for(var c=0,a=f.length;c<a;c++){var e=f[c];this._addIParamPromptUnitData(g,e.id,e.names,e.data)}}},setCurrentIParamInfo:function(e,b,a){this._paramCtrl[e]=b;this._iParam[e]=a},getShowMinUI:function(a){return this.hasIParamPromptUnitData(a)},getWidth:function(a){if(this.hasIParamPromptUnitData(a)){return 300}else{return this.getSWFWidth(a)}},getHeight:function(a){if(this.hasIParamPromptUnitData(a)){return 315}else{return this.getSWFHeight(a)}},getScreenHeight:function(b){var a=MochiKit.Style.getElementDimensions(getLayer(b));return a.h-2},getScreenWidth:function(b){var a=MochiKit.Style.getElementDimensions(getLayer(b));return a.w-2},getSWFHeight:function(f){var b=bobj.crv.Viewer.LayoutTypes;var e=b.CLIENT;if(this._viewerLayoutType[f]){e=this._viewerLayoutType[f]}var c=e===b.FIXED?0:480;var a=this.getScreenHeight(f);return Math.max(c,a-200)},getSWFWidth:function(b){var a=this.getScreenWidth(b);return Math.min(600,a-20)},getAllowFullScreen:function(a){return !this.hasIParamPromptUnitData(a)},hasIParamPromptUnitData:function(a){return(this._iPromptUnitData[a]!=null)&&(this._iParamData[a]!=null)&&(this._iParam[a]!=null)},_addIParamPromptUnitData:function(g,f,e,c){if(!this.hasIParamPromptUnitData(g)){this._iPromptUnitData[g]=[];this._iParamData[g]=[]}this._iPromptUnitData[g][f]=c;for(var b=0,a=e.length;b<a;b++){this._iParamData[g][e[b]]=f}},clearIParamPromptUnitData:function(a){if(!this.hasIParamPromptUnitData(a)){return}delete this._iPromptUnitData[a];delete this._iParamData[a];delete this._iParam[a]},getPromptData:function(b){if(this.hasIParamPromptUnitData(b)){var a=this._iParamData[b][this._iParam[b].paramName];if(a){return this._iPromptUnitData[b][a]}}return this._promptData[b]},startDrag:function(e){var b=bobj.crv.params.FlexParameterBridge.getSWF(e);if(b){if(this._moveArea){return}this._moveArea=document.createElement("div");this._moveArea.id=bobj.uniqueId();MOVE_STYLE=this._moveArea.style;var a=b.style;var c=b.parentNode.style;MOVE_STYLE.top=c.top;MOVE_STYLE.left=c.left;MOVE_STYLE.width=a.width?a.width:b.width+"px";MOVE_STYLE.height=a.height?a.height:b.height+"px";MOVE_STYLE.border="1px";MOVE_STYLE.borderStyle="solid";MOVE_STYLE.borderColor="#000000";MOVE_STYLE.backgroundColor="#FFFFFF";MOVE_STYLE.position="absolute";MOVE_STYLE.opacity=0.5;MOVE_STYLE.filter="alpha(opacity=50)";MOVE_STYLE.zIndex=bobj.constants.modalLayerIndex-1;document.body.appendChild(this._moveArea);document.body.style.cursor="move"}},stopDrag:function(b){if(this._moveArea){var a=MochiKit.Style.getElementPosition(this._moveArea);this.move(b,a.x,a.y);document.body.removeChild(this._moveArea);delete this._moveArea;document.body.style.cursor="default"}},drag:function(e,s,r){var v=bobj.crv.logger;v.info("doMove Called viewer:"+e+" x:"+s+" y:"+r);var q=getLayer(e);if(!q){v.error("Shifting SWF could not find the viewer:"+e);return}var n=this._moveArea;if(!n){v.error("Unable to move SWF, no move area available");return}var k=n.offsetLeft;var g=n.offsetTop;var u=n.offsetHeight;var p=n.offsetWidth;var c=q.offsetLeft;var b=q.offsetTop;var t=q.offsetHeight;var f=q.offsetWidth;var a=k+s;var z=g+r;if(z<b){z=b}else{if(z+u>b+t){z=t-u}}if(a<c){a=c}else{if(a+p>c+f){a=f-p}}n.style.top=z+"px";n.style.left=a+"px";v.info("Moved the SWF to x:"+a+" y:"+z)},move:function(f,a,e){var b=bobj.crv.params.FlexParameterBridge.getSWF(f);if(b){var c=new MochiKit.Style.Coordinates(a,e);MochiKit.Style.setElementPosition(b.parentNode,c)}},setParamValues:function(b,a){bobj.crv.logger.info("setting parameter values");if(this.hasIParamPromptUnitData(b)){this._setIParamValues(b,a)}else{this._setFullParamValues(b,a);this.closeDialog(b)}},_setFullParamValues:function(b,a){bobj.event.publish("crprompt_flexparam",b,a)},_setIParamValues:function(c,b){var g=this._iParam[c];var a=this._paramCtrl[c];var l=this._iParamData[c];var m=this._iPromptUnitData[c];if(!g||!a||!l||!m||b.length!=1){return}var p=b[0];var e=p.prompts;for(var k=0,n=e.length;k<n;k++){var f=e[k];if(!f||!f.name||!f.values){continue}a.updateParameter(decodeURI(f.name),this._convertFlexValues(f,g.valueDataType))}a._updateToolbar();this._updatePromptData(c,p,g.valueDataType);this.closeDialog(c)},_updatePromptData:function(a,m,p){var r=m.prompts;var g=this._iPromptUnitData[a][m.id];var l=g.promptUnits[0];var n=l.prompts;for(var k=0,f=n.length;k<f;k++){var b=n[k];for(var e=0,q=r.length;e<q;e++){var c=r[e];if(b.id==c.id){b.values=this._unescapeFlexValues(c.values,p);break}}}},_unescapeFlexValues:function(b,e){if(e!=bobj.crv.params.DataTypes.STRING){return b}for(var c=0,a=b.length;c<a;c++){this._unescapeFlexValue(b[c],e)}return b},_unescapeFlexValue:function(b,e){if(e!=bobj.crv.params.DataTypes.STRING){return}if((b.value!==undefined&&b.value!==null)){b.value=decodeURI(b.value);if(b.labels!==undefined&&b.labels!==null){for(var c=0,a=b.labels.length;c<a;c++){b.labels[c]=decodeURI(b.labels[c])}}}else{if(b.start){this._unescapeFlexValue(b.start,e)}if(b.end){this._unescapeFlexValue(b.end,e)}}},_getDescriptionIndex:function(b){var c=b.lovValueIndex;var f=b.lovFieldTypes;if(c!==undefined&&f!==undefined){for(var e=0,a=f.length;e<a;e++){if(e!=c&&f[e]=="s"){return e}}}return -1},_convertFlexValues:function(b,f){var k=this._getDescriptionIndex(b);var c=b.values;var g=[];for(var e=0,a=c.length;e<a;e++){g.push(this._convertFlexValue(c[e],f,k))}return g},_convertFlexValue:function(a,e,f){var c={};if((a.value!==undefined&&a.value!==null)){if(f>-1&&a.labels&&a.labels.length>f){c.desc=decodeURI(a.labels[f])}var b=bobj.crv.params.DataTypes;switch(e){case b.DATE:case b.TIME:case b.DATE_TIME:c.value=this._convertDateTimeFlexValue(a.value,e);break;default:c.value=decodeURI(a.value);break}}else{if(a.start){c.lowerBoundType=a.start.inc==true?2:1;c.beginValue=this._convertFlexValue(a.start,e)}else{c.lowerBoundType=0}if(a.end){c.upperBoundType=a.end.inc==true?2:1;c.endValue=this._convertFlexValue(a.end,e)}else{c.upperBoundType=0}}return c},_convertDateTimeFlexValue:function(a,e){var b=bobj.crv.params.DataTypes;var c={};var f=a.split(",");switch(e){case b.DATE:c.y=parseInt(f[0].substring(5),10);c.m=parseInt(f[1],10)-1;c.d=parseInt(f[2].substring(f[2].length-1,0),10);break;case b.TIME:c.h=parseInt(f[0].substring(5),10);c.min=parseInt(f[1],10);c.s=parseInt(f[2].substring(f[2].length-1,0),10);c.ms=0;break;case b.DATE_TIME:c.y=parseInt(f[0].substring(9),10);c.m=parseInt(f[1],10)-1;c.d=parseInt(f[2],10);c.h=parseInt(f[3],10);c.min=parseInt(f[4]);c.s=parseInt(f[5].substring(f[5].length-1,0),10);c.ms=0;break}return c},logon:function(b,a){bobj.crv.logger.info("logging on");this.closeDialog(b);bobj.event.publish("crprompt_flexlogon",b,a)},processingCancel:function(b){var a=getWidgetFromID(b);if(a&&a._reportProcessing){a._reportProcessing.cancelShow()}},processingDelayedShow:function(b){var a=getWidgetFromID(b);if(a&&a._reportProcessing){a._reportProcessing.delayedShow()}},logger:function(a){bobj.crv.logger.info(a)},getSWFBaseURL:function(){return bobj.crvUri("../../swf/")},getSWFID:function(){return bobj.uniqueId()},getZIndex:function(){return bobj.constants.modalLayerIndex},getUseSavedData:function(a){return this.hasIParamPromptUnitData(a)},closeDialog:function(b){var a=getWidgetFromID(b);if(a){a.hideFlexPromptDialog()}},getUseOKCancelButtons:function(a){return this.hasIParamPromptUnitData(a)},getIsDialog:function(a){return true},getShouldAutoResize:function(a){return true},setVisibility:function(c){var a=bobj.crv.params.FlexParameterBridge.getSWF(c);if(a){var b=a.parentNode.style;b.position="absolute";b.visibility="visible";b.borderStyle="none";b.opacity=1;if(a.focus!==undefined){a.focus()}}},getReportStateInfo:function(b){var a=bobj.crv.stateManager.getComponentState(b);if(a&&a.common&&a.common.reqCtx){return MochiKit.Base.serializeJSON(a.common.reqCtx)}},setReportStateInfo:function(c,a){var b=bobj.crv.stateManager.getComponentState(c);if(b&&b.common&&b.common.reqCtx){b.common.reqCtx=MochiKit.Base.evalJSON(unescape(a))}},sendAsyncRequest:function(b,a){bobj.event.publish("crprompt_asyncrequest",b,a)},readyToShow:function(a){this.processingCancel(a)}};if(typeof(bobj.crv.SignalDisposer)=="undefined"){bobj.crv.SignalDisposer=new function(){var a=[];var e=null;var c=20;var b=MochiKit.Signal.disconnect;this.dispose=function(g,f){if(g!=null){if(f){b(g)}else{a.push(g);if(e==null){e=setInterval(bobj.bindFunctionToObject(cleanTask,this),100)}}}};cleanTask=function(){var f=c;while(a.length>0&&f>0){b(a.pop());f--}if(a.length==0&&e!=null){clearInterval(e);e=null}}}};