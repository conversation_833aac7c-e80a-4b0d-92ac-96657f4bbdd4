// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "Heute";
var L_January   = "Januar";
var L_February  = "Februar";
var L_March     = "M\u00e4rz";
var L_April     = "April";
var L_May       = "Mai";
var L_June      = "Juni";
var L_July      = "Juli";
var L_August    = "August";
var L_September = "September";
var L_October   = "Oktober";
var L_November  = "November";
var L_December  = "Dezember";
var L_Su        = "So";
var L_Mo        = "Mo";
var L_Tu        = "Di";
var L_We        = "Mi";
var L_Th        = "Do";
var L_Fr        = "Fr";
var L_Sa        = "Sa";

// strings for dt_param.js
var L_TIME_SEPARATOR = ":";
var L_AM_DESIGNATOR = "AM";
var L_PM_DESIGNATOR = "PM";

// strings for range parameter
var L_FROM = "Von {0}";
var L_TO = "Bis {0}";
var L_AFTER = "Nach {0}";
var L_BEFORE = "Vor {0}";
var L_FROM_TO = "Von {0} bis {1}";
var L_FROM_BEFORE = "Von {0} bis vor {1}";
var L_AFTER_TO = "Nach {0} bis {1}";
var L_AFTER_BEFORE = "Nach {0} bis vor {1}";

// Strings for prompts.js and prompts_param.js
var L_BadNumber		= "Dieser Parameter ist vom Typ \"Number\" und kann nur ein vorangestelltes Minuszeichen, Ziffern (\"0-9\"), Zifferngruppierungssymbole oder ein Dezimalsymbol enthalten. Korrigieren Sie den eingegebenen Parameterwert.";
var L_BadCurrency	= "Dieser Parameter ist vom Typ \"Currency\" und kann nur ein Minuszeichen, Ziffern (\"0-9\"), Zifferngruppierungssymbole oder ein Dezimalsymbol enthalten. Korrigieren Sie den eingegebenen Parameterwert.";
var L_BadDate		= "Dieser Parameter ist vom Typ \"Date\" und sollte im Format \"Date(jjjj,mm,tt)\" angegeben werden, wobei \"jjjj\" die vierstellige Jahreszahl, \"mm\" die Monatszahl (z.B. Januar = 1) und \"tt\" der Tag des angegebenen Monats ist.";
var L_BadDateTime   = "Dieser Parameter ist vom Typ \"DateTime\", und das korrekte Format lautet \"DateTime(jjjj,mm,tt,hh,mm,ss)\". \"jjjj\" ist die vierstellige Jahreszahl, \"mm\" ist die Monatszahl (z.B. Januar = 1), \"tt\" ist der Tag des Monats, \"hh\" die Stunden im 24-Stunden-Format, \"mm\" die Minuten und \"ss\" die Sekunden.";
var L_BadTime       = "Dieser Parameter ist vom Typ \"Time\" und sollte dem Format \"Time(hh,mm,ss)\" entsprechen, wobei \"hh\" die Stunden im 24-Stunden-Format, \"mm\" die Minuten einer Stunde und \"ss\" die Sekunden einer Minute sind.";
var L_NoValue       = "Kein Wert";
var L_BadValue      = "Um \"Kein Wert\" festzulegen, m\u00fcssen Sie f\u00fcr die Werte \"von\" und \"bis\" die Option \"Kein Wert\" festlegen.";
var L_BadBound      = "Sie k\u00f6nnen \"Keine Untergrenze\" nicht zusammen mit \"Keine Obergrenze\" festlegen.";
var L_NoValueAlready = "F\u00fcr diesen Parameter wurde bereits \"Kein Wert\" festgelegt. Entfernen Sie \"Kein Wert\", bevor Sie weitere Werte hinzuf\u00fcgen.";
var L_RangeError    = "Der Anfangswert des Bereichs darf nicht h\u00f6her als der Endwert des Bereichs sein.";
var L_NoDateEntered = "Sie m\u00fcssen ein Datum eingeben.";

// Strings for ../html/crystalexportdialog.htm
var L_ExportOptions     = "Exportoptionen";
var L_PrintOptions      = "Druckoptionen";
var L_PrintPageTitle    = "Bericht drucken";
var L_ExportPageTitle   = "Bericht exportieren";
var L_OK                = "OK";
var L_PrintPageRange    = "Geben Sie den zu druckenden Seitenbereich ein.";
var L_ExportPageRange   = "Geben Sie den zu exportierenden Seitenbereich ein.";
var L_InvalidPageRange  = "Die Seitenbereichswerte sind ung\u00fcltig. Geben Sie g\u00fcltige Werte f\u00fcr den Seitenbereich ein.";
var L_ExportFormat      = "W\u00e4hlen Sie ein Exportformat aus der Liste.";
var L_Formats           = "Formate:";
var L_All               = "Alle";
var L_Pages             = "Seiten";
var L_From              = "von:";
var L_To                = "bis:";
var L_PrintStep0        = "Zu drucken:";
var L_PrintStep1        = "1.  W\u00e4hlen Sie im n\u00e4chsten Dialogfeld die Option \"Diese Datei \u00f6ffnen\" aus, und klicken Sie auf \"OK\".";
var L_PrintStep2        = "2.  Klicken Sie vorzugsweise auf das Drucksymbol im Men\u00fc von Acrobat Reader, statt auf das Drucksymbol des Internet-Browsers.";
var L_RTFFormat         = "Rich Text Format";
var L_AcrobatFormat     = "Acrobat Format (PDF)";
var L_CrystalRptFormat  = "Crystal Reports (RPT)";
var L_WordFormat        = "MS Word";
var L_ExcelFormat       = "MS Excel 97-2000";
var L_ExcelRecordFormat = "MS Excel 97-2000 (nur Daten)";
if(typeof(Sys)!=='undefined')Sys.Application.notifyScriptLoaded();
