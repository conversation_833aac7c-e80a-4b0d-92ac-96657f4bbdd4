/* Copyright (c) Business Objects 2006. All rights reserved. */

// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "Vandaag";
var L_January   = "januari";
var L_February  = "februari";
var L_March     = "maart";
var L_April     = "april";
var L_May       = "mei";
var L_June      = "juni";
var L_July      = "juli";
var L_August    = "augustus";
var L_September = "september";
var L_October   = "oktober";
var L_November  = "november";
var L_December  = "december";
var L_Su        = "zo";
var L_Mo        = "ma";
var L_Tu        = "di";
var L_We        = "wo";
var L_Th        = "do";
var L_Fr        = "vr";
var L_Sa        = "za";

// Strings for prompts.js and prompts_param.js
var L_YYYY          = "jjjj";
var L_MM            = "mm";
var L_DD            = "dd";
var L_BadNumber     = "Deze parameter is van het type \"Getal\" en mag alleen een minteken, cijfers (\"0-9\"), cijfergroepeersymbolen of een decimaalteken bevatten. Corrigeer de ingevoerde parameterwaarde.";
var L_BadCurrency   = "Deze parameter is van het type \"Valuta\" en kan alleen bestaan uit een minteken, cijfers (\"0-9\"), cijfergroepeersymbolen of een decimaalteken. Corrigeer de ingevoerde parameterwaarde.";
var L_BadDate       = "Deze parameter is van het type \"Datum\" en moet de notatie \"%1\" hebben, waarbij \"jjjj\" het viercijferige jaar, \"mm\" de maand (bijvoorbeeld januari = 1) en \"dd\" de dag van de maand zijn.";
var L_BadDateTime   = "Deze parameter is van het type \"DatumTijd\" en de juiste notatie is \"%1 uu:mm:ss\". \"jjjj\" is het viercijferige jaartal, \"mm\" is de maand (bijvoorbeeld januari = 1), \"dd\" is de dag van de maand, \"uu\" is het uur in een 24-uurs klok, \"mm\" zijn de minuten en \"ss\" de seconden.";
var L_BadTime       = "Deze parameter is van het type \"Tijd\" en moet de notatie \"uu:mm:ss\" hebben, waarbij \"uu\" het uur in een 24-uurs klok, \"mm\" de minuten en \"ss\" de seconden zijn.";
var L_NoValue       = "Geen waarde";
var L_BadValue      = "Als u \"Geen waarde\" wilt instellen, moet u \"Geen waarde\" instellen in zowel het vak Van als het vak Tot.";
var L_BadBound      = "U kunt niet tegelijk \"Geen ondergrens\" en \"Geen bovengrens\" instellen.";
var L_NoValueAlready = "Deze parameter is al ingesteld op \"Geen waarde\". Verwijder \"Geen waarde\" voordat u andere waarden toevoegt.";
var L_RangeError    = "De beginwaarde van het bereik mag niet groter zijn dan de eindwaarde.";
var L_NoDateEntered = "U moet een datum invoeren.";
var L_Empty         = "Voer een waarde in.";

// Strings for filter dialog
var L_closeDialog="Venster sluiten";

var L_SetFilter = "Filter instellen";
var L_OK        = "OK";
var L_Cancel    = "Annuleren";

 /* Crystal Decisions Confidential Proprietary Information */
