﻿<%@ Page Title="Application Issue | View" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="ApplicationIssue_View.aspx.cs" Inherits="KSDCSCST_Portal.ApplicationIssue_View" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
       <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">

    <style>
        input:disabled {
    border: 0;
}
    </style>

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Application Issue View</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">Application issue View</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">
                                   
                                          <div class="form-group row">
                                        <label for="inputName" class="col-sm-3 col-form-label">Date(DD/MM/YYYY)</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="inputName" placeholder="Date(DD/MM/YYYY)">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">District *</label>
                                        <div class="col-sm-9">
                                            <select class="form-control">
                                                <option>Select District</option>
                                                 <option>Trivandrum</option>
                                                 <option>Kannur</option>
                                                
                                            </select>
                                        </div>
                                    </div>
                                       <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Sub District *</label>
                                        <div class="col-sm-9">
                                            <select class="form-control">
                                                <option>Select Sub District</option>
                                                 <option>Trivandrum</option>
                                                 <option>Kannur</option>
                                                
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Application Number</label>
                                        <div class="col-sm-9">
                                            <input disabled="disabled" type="text" class="form-control" id="inputName2" value="255" placeholder="Mobile Number">
                                        </div>
                                    </div>
                                      <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Name of Applicant *</label>
                                        <div class="col-sm-9">
                                            <input  type="text" class="form-control" id="inputName2"   placeholder="Name of Applicant">
                                        </div>
                                    </div>

                                      <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Loan Scheme *</label>
                                        <div class="col-sm-9">
                                            <select class="form-control">
                                                <option>Select Loan Scheme</option>
                                                 <option>Scheme 1</option>
                                                 <option>Scheme 2</option>
                                                
                                            </select>
                                        </div>
                                    </div>
                                          <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Caste *</label>
                                        <div class="col-sm-9">
                                            <select class="form-control">
                                                <option>Select Caste</option>
                                                 <option>Caste 1</option>
                                                 <option>Caste 2</option>
                                                
                                            </select>
                                        </div>
                                    </div>
                                        <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Religion *</label>
                                        <div class="col-sm-9">
                                            <select class="form-control">
                                                <option>Select Religion</option>
                                                 <option>Religion 1</option>
                                                 <option>Religion 2</option>
                                                
                                            </select>
                                        </div>
                                    </div>
                                     <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Receipt No *</label>
                                        <div class="col-sm-9">
                                            <input disabled="disabled" type="text" class="form-control" id="inputName2" value="65758"   placeholder="Name of Applicant">
                                        </div>
                                    </div>
                                       <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Amount *</label>
                                        <div class="col-sm-9">
                                            <input disabled="disabled" type="text" class="form-control" id="inputName2" value="10"   placeholder="Name of Applicant">
                                        </div>
                                    </div>
                                       <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Remarks *</label>
                                        <div class="col-sm-9">
                                            <textarea class="form-control" id="inputExperience" placeholder="Remarks"></textarea>
                                        </div>
                                    </div>
                                       
                                       
                                        <div class="form-group row">
                                            <div class="col-sm-12 text-right">
                                               
                                                <a  href="ApplicationIssue_List.aspx" class="btn btn-dark">Back</a>
                                            </div>
                                        </div>
                                   
                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->


    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
</asp:Content>

