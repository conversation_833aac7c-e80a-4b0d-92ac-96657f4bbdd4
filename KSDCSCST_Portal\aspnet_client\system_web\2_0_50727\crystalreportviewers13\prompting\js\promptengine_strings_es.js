/* Copyright (c) Business Objects 2006. All rights reserved. */

// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "Hoy";
var L_January   = "Enero";
var L_February  = "Febrero";
var L_March     = "Marzo";
var L_April     = "Abril";
var L_May       = "Mayo";
var L_June      = "Junio";
var L_July      = "Julio";
var L_August    = "Agosto";
var L_September = "Septiembre";
var L_October   = "Octubre";
var L_November  = "Noviembre";
var L_December  = "Diciembre";
var L_Su        = "Do";
var L_Mo        = "Lu";
var L_Tu        = "Ma";
var L_We        = "Mi";
var L_Th        = "Ju";
var L_Fr        = "Vi";
var L_Sa        = "S\u00E1";

// Strings for prompts.js and prompts_param.js
var L_YYYY          = "aaaa";
var L_MM            = "mm";
var L_DD            = "dd";
var L_BadNumber     = "Este par\u00E1metro es de tipo \"N\u00FAmero\" y s\u00F3lo puede contener un s\u00EDmbolo de signo negativo, d\u00EDgitos (\"0-9\"), s\u00EDmbolos de agrupaci\u00F3n de d\u00EDgitos o un s\u00EDmbolo decimal. Corrija el valor del par\u00E1metro especificado.";
var L_BadCurrency   = "Este par\u00E1metro es de tipo \"Moneda\" y s\u00F3lo puede contener un s\u00EDmbolo de signo negativo, d\u00EDgitos (\"0-9\"), s\u00EDmbolos de agrupaci\u00F3n de d\u00EDgitos o un s\u00EDmbolo decimal. Corrija el valor del par\u00E1metro especificado.";
var L_BadDate       = "Este par\u00E1metro es de tipo \"Fecha\" y debe estar en formato \"%1\", donde \"aaaa\" es el a\u00F1o con cuatro d\u00EDgitos, \"mm\" es el mes (por ejemplo, enero = 1) y \"dd\" es el d\u00EDa del mes.";
var L_BadDateTime   = "Este par\u00E1metro es de tipo \"FechaHora\" y el formato correcto es \"%1 hh:mm:ss\". \"aaaa\" es el a\u00F1o con cuatro d\u00EDgitos, \"mm\" es el mes (por ejemplo, enero = 1), \"dd\" es el d\u00EDa del mes, \"hh\" son las horas en formato de 24 horas, \"mm\" son los minutos y \"ss\" son los segundos.";
var L_BadTime       = "Este par\u00E1metro es de tipo \"Hora\" y debe estar en formato \"hh:mm:ss\", donde \"hh\" son las horas en formato de 24 horas, \"mm\" son los minutos y \"ss\" son los segundos.";
var L_NoValue       = "Ning\u00FAn valor";
var L_BadValue      = "Para establecer \"Ning\u00FAn valor\", debe establecer los valores De y A en \"Ning\u00FAn valor\".";
var L_BadBound      = "No puede establecer \"Ning\u00FAn l\u00EDmite inferior\" junto con \"Ning\u00FAn l\u00EDmite superior\".";
var L_NoValueAlready = "Este par\u00E1metro ya se ha establecido en \"Ning\u00FAn valor\". Quite \"Ning\u00FAn valor\" antes de agregar otros valores.";
var L_RangeError    = "El inicio del rango no puede ser mayor que el final del rango.";
var L_NoDateEntered = "Debe especificar una fecha.";
var L_Empty         = "Especifique un valor.";

// Strings for filter dialog
var L_closeDialog="Cerrar ventana";

var L_SetFilter = "Establecer filtro";
var L_OK        = "Aceptar";
var L_Cancel    = "Cancelar";

 /* Crystal Decisions Confidential Proprietary Information */
