/* Copyright (c) Business Objects 2006. All rights reserved. */

// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "Aujourd\'hui";
var L_January   = "Janvier";
var L_February  = "F\u00E9vrier";
var L_March     = "Mars";
var L_April     = "Avril";
var L_May       = "Mai";
var L_June      = "Juin";
var L_July      = "Juillet";
var L_August    = "Ao\u00FBt";
var L_September = "Septembre";
var L_October   = "Octobre";
var L_November  = "Novembre";
var L_December  = "D\u00E9cembre";
var L_Su        = "di";
var L_Mo        = "lu";
var L_Tu        = "ma";
var L_We        = "me";
var L_Th        = "je";
var L_Fr        = "ve";
var L_Sa        = "sa";

// Strings for prompts.js and prompts_param.js
var L_YYYY          = "aaaa";
var L_MM            = "mm";
var L_DD            = "jj";
var L_BadNumber     = "Un param\u00E8tre de type \"Nombre\" peut uniquement contenir un signe n\u00E9gatif, des chiffres (\"0-9\"), des symboles de regroupement de chiffres ou un s\u00E9parateur d\u00E9cimal. Veuillez corriger la valeur saisie pour ce param\u00E8tre.";
var L_BadCurrency   = "Un param\u00E8tre de type \"Devise\" peut uniquement contenir un signe n\u00E9gatif, des chiffres (\"0-9\"), des symboles de regroupement de chiffres ou un s\u00E9parateur d\u00E9cimal. Veuillez corriger la valeur saisie pour ce param\u00E8tre.";
var L_BadDate       = "Un param\u00E8tre de type \"Date\" doit se pr\u00E9senter sous la forme \"%1\", \"aaaa\" correspondant aux quatre chiffres de l\'ann\u00E9e, \"mm\" au mois (ex. janvier = 1) et \"jj\" au jour du mois donn\u00E9.";
var L_BadDateTime   = "Un param\u00E8tre de type \"DateHeure\" doit se pr\u00E9senter sous la forme \"%1 hh:mm:ss\". \"aaaa\" correspond aux quatre chiffres de l\'ann\u00E9e, \"mm\" au mois (ex. janvier = 1), \"jj\" au jour du mois, \"hh\" au nombre d\'heures en mode 24H, \"mm\" au nombre de minutes et \"ss\" au nombre de secondes.";
var L_BadTime       = "Un param\u00E8tre de type \"Heure\" doit se pr\u00E9senter sous la forme \"hh:mm:ss\", \"hh\" correspondant au nombre d\'heures en mode 24H, \"mm\" au nombre de minutes et \"ss\" au nombre de secondes.";
var L_NoValue       = "Aucune valeur";
var L_BadValue      = "Pour d\u00E9finir \"Aucune valeur\", vous devez d\u00E9finir les deux valeurs De et A \u00E0 \"Aucune valeur\".";
var L_BadBound      = "Vous ne pouvez pas d\u00E9finir \"Aucune limite inf\u00E9rieure\" en m\u00EAme temps que \"Aucune limite sup\u00E9rieure\".";
var L_NoValueAlready = "Ce param\u00E8tre a d\u00E9j\u00E0 la valeur \"Aucune valeur\". Supprimez \"Aucune valeur\" avant d\'ajouter d\'autres valeurs.";
var L_RangeError    = "Le d\u00E9but de la plage ne peut pas \u00EAtre sup\u00E9rieur \u00E0 la fin de la plage.";
var L_NoDateEntered = "Vous devez saisir une date.";
var L_Empty         = "Saisissez une valeur.";

// Strings for filter dialog
var L_closeDialog="Fermer la fen\u00EAtre";

var L_SetFilter = "D\u00E9finir un filtre";
var L_OK        = "OK";
var L_Cancel    = "Annuler";

 /* Crystal Decisions Confidential Proprietary Information */
