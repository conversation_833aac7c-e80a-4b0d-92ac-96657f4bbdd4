﻿<%@ Page Title="Role | Permission" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Role_Permission.aspx.cs" Inherits="KSDCSCST_Portal.Role_Permission" %>





<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">
    <link rel="stylesheet" href="sweetalert2.min.css">
    <link href="https://www.jqueryscript.net/css/jquerysctipttop.css" rel="stylesheet" type="text/css">

    <style>
        ul.tree, ul.tree * {
            list-style-type: none;
            margin: 0;
            padding: 0 0 5px 0;
        }

            ul.tree img.arrow {
                padding: 2px 0 0 0;
                border: 0;
                width: 20px;
            }

            ul.tree li {
                padding: 4px 0 0 0;
                line-height: 18px;
            }

                ul.tree li ul {
                    padding: 0 0 0 20px;
                    margin: 0;
                }

            ul.tree label {
                cursor: pointer;
                font-weight: bold;
                padding: 2px 0;
            }

                ul.tree label.hover {
                    color: red;
                }

        ul {
            margin-top: 5px;
            margin-bottom: 5px;
        }

            ul.tree li .arrow {
                width: 20px;
                height: 18px;
                padding: 0;
                margin: 0;
                cursor: pointer;
                float: left;
                background: transparent no-repeat 0 0px;
            }

            ul.tree li .collapsed {
                background-image: url(assets/img/right.svg);
            }

            ul.tree li .expanded {
                background-image: url(assets/img/down.svg);
            }

            ul.tree li .checkbox {
                width: 20px;
                height: 18px;
                padding: 0;
                margin: 0;
                cursor: pointer;
                float: left;
                background: url(assets/img/square.svg) no-repeat 0 0px;
            }

            ul.tree li .checked {
                background-image: url(assets/img/check.svg);
            }

            ul.tree li .half_checked {
                background-image: url(assets/img/square-minus.svg);
            }
    </style>

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Role Add</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item"><a href="User_List.aspx">User List</a></li>
                        <li class="breadcrumb-item active">User Add</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="DIV_Agency">


                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Role *</label>
                                        <div class="col-sm-9">
                                            <select id="dropRole" class="form-control">
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label for="inputName" class="col-sm-3 col-form-label">Menu</label>
                                        <div class="col-sm-9">
                                            <ul class="tree" style="margin-left: 15px;" id="treeview">
                                                <!-- Dynamic tree will be generated here -->
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                            <a href="User_List.aspx" class="btn btn-dark">Back</a>
                                            <a onclick="Save();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->


    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>

     <script src="assets/js/jquery.checktree.js"></script>
    <script>

        $(function () {
            $('ul.tree').checkTree({

            });
            Load_All_Roles();
            $('#dropRole').change(function () {

                GetMenus($(this).val());
            });
        });

        function Load_All_Roles() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Roles_ACTIVE",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropRole').empty();
                    $('#dropRole').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Name = value.Name;

                        var html = '<option value="' + Id + '">' + Name + '</option>';
                        $('#dropRole').append(html);
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                GetMenus();
            });

        }


 


        function Save() {
            var IsActive = $("#checkActive").is(":checked") ? $("#checkActive").val() : "off";
            if (IsActive == 'on') {
                IsActive = '1';
            }
            else {
                IsActive = '0';
            }


            if ($("#txt_New_Password").val().trim() != $("#txt_Confirm_Password").val().trim()) {
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'It appears that the new password and the confirm password you provided do not match.',

                })
                return;
            }

            $.ajax({
                type: "POST", // or "GET" depending on your web method
                url: "WebService.asmx/Insert_To_tbl_Users",
                data: JSON.stringify({ name: $("#txt_Name").val(), username: $("#txt_Username").val(), password: $("#txt_Confirm_Password").val(), Role_Id: $("#dropRole").val(), office_Id: $("#dropOffice").val(), emailId: $("#txt_EmailId").val(), mobile: $("#txt_Mobile").val(), address: $("#txtAddress").val(), isActive: IsActive }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (response) {

                    // Handle the successful response from the web method
                    console.log(response.d); // "d" is the default property name for the response
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'User Successfully Saved !',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked

                            location.href = 'User_List.aspx';
                            // Your code here
                        }
                    });

                },
                error: function (xhr, status, error) {
                    // Handle the error response

                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                },
                done: function (response) {
                    // Handle the error response
                    alert(response);

                }
            });


        }

        function GetMenus() {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/GetMenuItems_Permission_By_Role",
                data: JSON.stringify({ Role_Id: $("#dropRole").val() }),
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    if (data.d.length != 0) {
                        buildMenu('#treeview', data.d);
                    }

                    //      $('#treeview').menu();

                    // var $tree = buildTree(null, $('#treeview'),data.d);
                    // $('#treeview').append($tree);

                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                $('ul.tree').checkTree({

                });
            });
        }

        function buildMenu(parent, Items) {
            $.each(Items, function () {
                var li = $('<li>');
                var label = $('<label>');
                var checkbox = $('<input type="checkbox">');
                label.append(checkbox, this.Label);
                li.append(label);


                li.appendTo(parent);
                if (this.List && this.List.length > 0) {
                    var ul = $("<ul></ul>");
                    ul.appendTo(li);
                    buildMenu(li, this.List);
                }
                li.appendTo(parent);
            });
        }
        function buildTree(parentId, $parent, data) {
            var $ul = $('<ul>');

            $.each(data, function (key, item) {
                if (item.ParentMenuItemID === parentId) {
                    var $li = $('<li>');
                    var $label = $('<label>');
                    var $checkbox = $('<input type="checkbox">');

                    $label.append($checkbox, item.Label);
                    $li.append($label);

                    // If there are child items, recursively build them
                    var $children = buildTree(item.Id, $li);
                    if ($children.length > 0) {
                        $li.append($children);
                    }

                    $ul.append($li);
                }
            });

            return $ul;
        }
    </script>
</asp:Content>
