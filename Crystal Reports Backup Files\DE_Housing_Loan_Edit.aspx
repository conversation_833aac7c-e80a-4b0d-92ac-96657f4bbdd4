﻿<%@ Page Title="Application Data Entry - Housing Loan | Edit" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="DE_Housing_Loan_Edit.aspx.cs" Inherits="KSDCSCST_Portal.DE_Housing_Loan_Edit" %>
 <asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
      <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>To be filled for House Loan</h1>
                        
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="Welcome.aspx">Home</a></li>
						  <li class="breadcrumb-item  "><a href="ApplicationDataEntry_List.aspx">Application DataEntry</a></li>
                        <li class="breadcrumb-item active">To be filled for House Loan</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">

                                    <div class="Sub_Header">Applicant Details*</div>
									 
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Application Register No*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtApplicationRegNo" disabled="disabled"  placeholder="Application Register No">
                                            <input type="hidden" id="Max_Loan_Amount" name="Max_Loan_Amount" value="0">
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Loanee Name*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtLoanee_Name" disabled="disabled" placeholder="Loanee Name">
                                        </div>
                                    </div>
									
									<div class="Sub_Header">Co-Applicant Details*</div>
									 
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtName" placeholder="Name">
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name of Father*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtName_Of_Father" placeholder="Name of Father">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name of Mother*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtName_of_Mother" placeholder="Name of Mother">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name of Spouse*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtName_Of_Spouse" placeholder="Name Of Spouse">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">Relationship With Applicant*</label>
                                        <div class="col-sm-9">
                                            <select id="dropRelation_With_Applicant"   class="form-control">
											<option value="0" selected="selected">Select</option>
											<option value="Husband">Husband</option>
											<option value="Wife">Wife</option>
											<option value="Son">Son</option>
											<option value="Daughter">Daughter</option>
											<option value="Father">Father</option>
											<option value="Mother">Mother</option>
											<option value="Brother">Brother</option>
											<option value="Sister">Sister</option>
                                            </select>
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">Date Of Birth*</label>
                                        <div class="col-sm-9">
                                            <input     type="date"  class="dateandtime form-control" id="txtDate_Of_Birth" placeholder="dd/mm/yyyy"  value="">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Age*</label>
                                        <div class="col-sm-9">
                                            <input   type="number"  disabled="disabled"  class="form-control" id="txtAge"   value="0"  placeholder="Age">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Aadhar Number*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtAadhar_Number" oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);" maxlength="12" placeholder="Aadhar Number">
                                        </div>
                                    </div>
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Election Id Number*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtElection_Id_Number" placeholder="Election Id Number">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Ration Card No.*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtRation_Card_No" oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);" maxlength="10" placeholder="Ration Card No.">
                                        </div>
                                    </div>
									
                                     <div class="Sub_Header">Income Details Of the Applicant*</div>
									 
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Income Sources*</label>
                                        <div class="col-sm-9">
                                            <label class="col-sm-9 col-form-label">Annual Income*</label>
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Salary / Wages*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtSalary_Or_Wages" placeholder="Salary / Wages"  onkeyup="return CalcIncome();" >
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Agriculture*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtAgriculture" placeholder="Agriculture"  onkeyup="return CalcIncome();" >
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Others*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtOthers" placeholder="Others"  onkeyup="return CalcIncome();" >
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Total*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" disabled="disabled" class="form-control" id="txtTotal_Annual_Income" placeholder="Total">
                                        </div>
                                    </div>
									
								 
									
									<div class="Sub_Header">Land Details*</div>
									
									 
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name of Land Owner*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtName_Of_Land_Owner" placeholder="Name of Land Owner">
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Extent*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtExtent" placeholder="Extent">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Survey No.*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtSurvey_No" placeholder="Survey No">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Block No.*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtBlock_No" placeholder="Block No.">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Tandaperu*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtTandaperu" placeholder="Tandaperu">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Village*</label>
                                         <div class="col-sm-9">
                                            <select id="dropVillage"   class="form-control">
											<option value="0" selected="selected">Select</option>
                                            </select>
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Taluk*</label>
                                         <div class="col-sm-9">
                                            <select id="dropTaluk"   class="form-control">
											<option value="0" selected="selected">Select</option>
                                            </select>
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">District*</label>
                                         <div class="col-sm-9">
                                            <select id="dropDistrict"   class="form-control">
											<option value="0" selected="selected">Select</option>
                                            </select>
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Type of Title Deed*</label>
                                         <div class="col-sm-9">
                                            <select id="dropdeedtype" class="form-control">
                                                <option value="0">[Select]</option>
                                                <option value="Sale">Sale</option>
                                                <option value="Settlement">Settlement</option>
                                                <option value="Partition">Partition</option>
                                                <option value="Pattayam">Pattayam</option>
                                                <option value="Gift">Gift</option>
                                                <option value="RightRedemtion">RightRedemtion</option>
                                                <option value="Others">Others</option>
                                            </select>
                                        </div>
                                    </div>
									
                                    <div class="form-group row" id="DeedType_Others">
                                        <label class="col-sm-3 col-form-label">Specify Others*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtDeedType_Others" placeholder="Others">
                                        </div>
                                    </div>

									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Deed No.*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtDeed_No"  >
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name of SubRegistrar Office*</label>
                                         <div class="col-sm-9">
                                            <select id="dropSubRegistrar_Office"   class="form-control">
											<option value="0">[Select]</option>
                                            </select>
                                             <input type="hidden" id="SubRegistrar_Office" name="SubRegistrar_Office" value="">
                                        </div>
                                    </div>
									
									
									<div class="Sub_Header">Construction Details*</div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Basement Area*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtBasement_Area" >
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">No of Floors*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtNo_Of_Floors"  >
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Carpet Area*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtCarpet_Area"  >
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Construction Type*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtConstruction_Type" placeholder="Construction Type">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Total Cost.*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtTotal_Cost_Of_Construction"  >
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Permit / NOC Number*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtPermit_NOC_Number"  >
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Permit / NOC Date*</label>
                                        <div class="col-sm-9">
                                            <input   type="Date"  class="dateandtime form-control" id="txtPermit_NOC_Date"  >
                                        </div>
                                    </div>
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Validity of Permit*</label>
                                        <div class="col-sm-9">
                                            <input   type="Date" class="form-control" id="txtValidity_Of_Permit"  >
                                        </div>
                                    </div>
									 <div class="form-group row">
									 <label class="col-sm-3 col-form-label"></label>
                                        <div class="col-sm-9">
										 <label class="col-sm-8 col-form-label">Received Any Concession / Benifit from Govt</label>
                                     
                                            <input class="col-sm-1 form-check-input" style="margin: 0;position: relative;" type="checkbox" id="checkBenifit_Gov" >
                                        </div>
									    
                                        

                                    </div>
					
                                    <div class="form-group row" id="Benifit_Gov_Details">
                                        <label class="col-sm-3 col-form-label">Details*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtBenifit_Gov_Details" placeholder="Concession / Benifit from Govt Details">
                                        </div>
                                    </div>
					
                                    <div class="form-group row" id="Benifit_Gov_Amount">
                                        <label class="col-sm-3 col-form-label">Amount Received*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtBenifit_Gov_Amount" placeholder="Amount Received">
                                        </div>
                                    </div>
								 
									<div class="Sub_Header">Loan Details*</div>
									
									
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Total Project Cost*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtTotal_Project_Cost" onkeyup="return LoanRequired();"   >
                                        </div>
                                    </div>
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Cash in Hand*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtCash_Hand"  onkeyup="return LoanRequired();"    >
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Subsidy Amount*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtSubsidy_Amount"  onkeyup="return LoanRequired();"  value="0" >
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Loan Amount Required*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" readonly="readonly" class="form-control" id="txtLoan_Amount_Required"  >
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                          <label class="col-sm-3 col-form-label">Repayment Period (Maximum 240 Months)*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txt_RepayPeriod"  >
                                        </div>
                                    </div>
									
                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                            <a onclick="Save();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 -->
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <!-- Bootstrap4 Duallistbox -->
    <script src="assets/plugins/bootstrap4-duallistbox/jquery.bootstrap-duallistbox.min.js"></script>
    <!-- InputMask -->
    <script src="assets/plugins/moment/moment.min.js"></script>
    <script src="assets/plugins/inputmask/jquery.inputmask.min.js"></script>
    <!-- date-range-picker -->
    <script src="assets/plugins/daterangepicker/daterangepicker.js"></script>
    <!-- bootstrap color picker -->
    <script src="assets/plugins/bootstrap-colorpicker/js/bootstrap-colorpicker.min.js"></script>
    <!-- Tempusdominus Bootstrap 4 -->
    <script src="assets/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js"></script>
    <!-- BS-Stepper -->
    <script src="assets/plugins/bs-stepper/js/bs-stepper.min.js"></script>
    <!-- dropzonejs -->
    <script src="assets/plugins/dropzone/min/dropzone.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script src="assets/js/jquery.dateandtime.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script>
        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;
        $('#DeedType_Others').hide();
        $(document).ready(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            // Get the current date
            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtApplication_Date').val(formattedDate);

          //  $("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
          //  $("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");
            // Load_All_Schemes();
            //   Load_All_Districts();

            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);


            //$("#txt_RepayPeriod").on("input", function () {
            //    var inputValue = $(this).val();
            //    if (parseInt(inputValue) > 20) {
            //        $("#txt_RepayPeriod").val('');
            //        Toast.fire({
            //            icon: 'error',
            //            title: 'Repayment Period is Maximum 20 years !'
            //        })
            //    }
            //});



            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }

            // $('#txtDate_Of_Birth').dateAndTime();

            var previousValue = $("#txtDate_Of_Birth").val();
            $("#txtDate_Of_Birth").on("input", function () {
                var currentValue = $(this).val();
                // Check if the input value has changed
                if (currentValue !== previousValue) {
                    $("#txtAge").val(calculateAge(currentValue));
                    //alert("Date selected or changed:"+ currentValue);
                    // Add your custom code here to handle the selected date
                    previousValue = currentValue; // Update the previous value
                }
            });

            $('#Benifit_Gov_Details').hide();
            $('#Benifit_Gov_Amount').hide();

            Load_All_Application_Issue_By_Id(getParameterByName("Id"));
        });

        $(document).on('click', '#checkBenifit_Gov', function () {
            var ckboxG = $('#checkBenifit_Gov');
            if (ckboxG.is(':checked')) {
                $('#Benifit_Gov_Details').show();
                $('#Benifit_Gov_Amount').show();
                $("#txtBenifit_Gov_Details").val('');
                $("#txtBenifit_Gov_Amount").val('');
            }
            else {
                $('#Benifit_Gov_Details').hide();
                $('#Benifit_Gov_Amount').hide();
                $("#txtBenifit_Gov_Details").val('');
                $("#txtBenifit_Gov_Amount").val('');
            }
        });

        function calculateAge(dateOfBirth) {
            // Parse the date of birth string into a Date object
            const dob = new Date(dateOfBirth);

            // Get the current date
            const currentDate = new Date();

            // Calculate the difference in milliseconds
            const ageInMilliseconds = currentDate - dob;

            // Convert the milliseconds to years
            const ageInYears = ageInMilliseconds / (365 * 24 * 60 * 60 * 1000);

            // Round down to the nearest whole number to get the age
            const age = Math.floor(ageInYears);

            return age;
        }

        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }
        var Scheme_Id;
        function Load_All_Application_Issue_By_Id(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Id: Id }), // If you have parameters
                url: "WebService.asmx/Select_tbl_LoanApp_By_LoanAppId",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        $(".LABEL_APP_Status").text(value.chr_status);
                        var int_loanappid = value.int_loanappid;
                        var Name_Applicant = value.vchr_applname;
                        Scheme_Id = value.int_schemeid;
                        Cast_Id = value.vchr_caste;
                        Sub_Cast_Id = value.SubCast;
                        var vchr_appreceivregno = value.vchr_appreceivregno;
                        $("#txtLoanee_Name").val(Name_Applicant);
                        $("#txtApplicationRegNo").val(vchr_appreceivregno);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Villages();

            });
        }

        function Load_All_Schemes_By_Id(Scheme_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Schemes_By_ID",
                data: JSON.stringify({ Id: Scheme_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Scheme = value.Scheme;
                        $("#Max_Loan_Amount").val(value.Loan_Amount);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }

        function getFormattedDate(date) {
            date = date.split('/')[2] + "-" + date.split('/')[1] + "-" + date.split('/')[0];
            return date;
        }

        function Load_All_Housing_Loan_By_Id(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ int_loanappid: Id }), // If you have parameters
                url: "WebService.asmx/Select_tbl_House_Loan_By_int_loanappid",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        $("#SubRegistrar_Office").val(value.vchr_subregoffiname);
                        $("#txtName").val(value.vchr_coapp_name);
                        $("#txtName_Of_Father").val(value.vchr_Father);
                        $("#txtName_of_Mother").val(value.vchr_Mother);
                        $("#txtName_Of_Spouse").val(value.vchr_Spouse);
                        $("#dropRelation_With_Applicant").val(value.vchr_Relation);
                        $("#txtDate_Of_Birth").val(getFormattedDate(value.dte_dob));
                        $("#txtAge").val(value.int_age);
                        $("#txtAadhar_Number").val(value.vchr_Aadhaar);
                        $("#txtElection_Id_Number").val(value.vchr_electionid);
                        $("#txtRation_Card_No").val(value.vchr_ration);
                        $("#txtSalary_Or_Wages").val(value.int_income_salary);
                        $("#txtAgriculture").val(value.int_income_agr);
                        $("#txtOthers").val(value.int_income_other);
                        $("#txtTotal_Annual_Income").val(value.int_income_total);
                        $("#txtName_Of_Land_Owner").val(value.vchr_ownername);
                        $("#txtDeed_No").val(value.vchr_electionid);
                        $("#txtSurvey_No").val(value.vchr_surveyno);
                        $("#txtExtent").val(value.vchr_extent);
                        $("#txtTandaperu").val(value.vchr_TPno);
                        $("#txtBlock_No").val(value.vchr_Blockno);
                        $("#dropVillage").val(value.vchr_land_village);
                        $("#dropVillage").trigger('change');
                        var DeedType = value.vchr_deedtype;
                        if (!$('#dropdeedtype option[value="' + DeedType + '"]').prop("selected", true).length) {
                            $("#dropdeedtype option:contains(Others)").attr('selected', 'selected');
                            $("#dropdeedtype").trigger('change');
                            $('#txtDeedType_Others').val(DeedType);
                        }
                        $("#txtBasement_Area").val(value.vchr_base_area);
                        $("#txtNo_Of_Floors").val(value.int_no_floor);
                        $("#txtCarpet_Area").val(value.vchr_carpet_area);
                        $("#txtConstruction_Type").val(value.vchr_construction_type);
                        $("#txtTotal_Cost_Of_Construction").val(value.int_cost);
                        $("#txtPermit_NOC_Number").val(value.vchr_permit_no);

                        var Validity_Of_Permit = value.vchr_validity;
                        var Formated_Date = Validity_Of_Permit.split('-')[2] + '/' + Validity_Of_Permit.split('-')[1] + '/' + Validity_Of_Permit.split('-')[0];
                        $("#txtValidity_Of_Permit").val(getFormattedDate(Formated_Date));
                        $("#txtPermit_NOC_Date").val(getFormattedDate(value.dte_permit_date));
                        var Benifit_Gov = value.int_concession;

                        if (Benifit_Gov == "1") {
                            $('#checkBenifit_Gov').prop('checked', true);
                            $('#Benifit_Gov_Details').show();
                            $('#Benifit_Gov_Amount').show();
                            $("#txtBenifit_Gov_Details").val(value.vchr_details);
                            $("#txtBenifit_Gov_Amount").val(value.int_concessionamt);
                        }
                        else {
                            $('#Benifit_Gov_Details').hide();
                            $('#Benifit_Gov_Amount').hide();
                            $("#txtBenifit_Gov_Details").val('');
                            $("#txtBenifit_Gov_Amount").val('');
                        }
                        $("#txtTotal_Project_Cost").val(value.int_total_cost);
                        $("#txtCash_Hand").val(value.int_cash_hand);
                        $("#txtSubsidy_Amount").val(value.int_subsidy);
                        $("#txtLoan_Amount_Required").val(value.int_loan_req);
                        $("#txt_RepayPeriod").val(value.int_repay_year);




                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }

        function Load_All_Villages() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Villages",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropVillage').empty();
                    $('#dropVillage').append('<option value="0">[Select]</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Village = value.Village;
                        var html = '<option value="' + Id + '">' + Village + '</option>';

                        $('#dropVillage').append(html);
                    });


                },
                error: function (error) {


                    alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Housing_Loan_By_Id(getParameterByName("Id"))
            });

        }

        $('#dropVillage').change(function () {
            var optionSelected = $(this).find("option:selected");
            var valueSelected = optionSelected.val();
            $('#dropTaluk').empty();
            $('#dropTaluk').append('<option value="0">[Select]</option>');
            Load_All_tbl_Taluk_By_Village_Id(valueSelected);
            $('#dropDistrict').empty();
            $('#dropDistrict').append('<option value="0">[Select]</option>');
            Select_All_Districts_By_Village_Id(valueSelected);
        });

        function Load_All_tbl_Taluk_By_Village_Id(Village_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_Taluk_By_Id",
                data: JSON.stringify({ Village_Id: Village_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {


                    // $('#dropTaluk').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        $('#dropTaluk').empty();
                        var Id = value.Id;
                        var Taluk = value.Taluk;
                        var html = '<option value="' + Id + '">' + Taluk + '</option>';

                        $('#dropTaluk').append(html);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }

        var DistrictId;
        function Select_All_Districts_By_Village_Id(Village_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts_By_Village_Id",
                data: JSON.stringify({ Village_Id: Village_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {


                    //$('#dropDistrict').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        $('#dropDistrict').empty();
                        var Id = value.Id;
                        DistrictId = Id;
                        var District_Name = value.District_Name;
                        var html = '<option value="' + Id + '">' + District_Name + '</option>';
                        $('#dropDistrict').append(html);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Select_tbl_Sub_Reg_Office_By_District_Id(DistrictId)
            });
        }

        $('#dropdeedtype').change(function () {
            var optionSelected = $(this).find("option:selected");
            var valueSelected = optionSelected.val();
            if (valueSelected == "Others") {
                $('#DeedType_Others').show();
            }
            else {
                $('#DeedType_Others').hide();
            }
        });

        function Select_tbl_Sub_Reg_Office_By_District_Id(DistrictId) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_tbl_Sub_Reg_Office_By_District_Id",
                data: JSON.stringify({ district_Id: DistrictId }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubRegistrar_Office').empty();
                    $('#dropSubRegistrar_Office').append('<option value="0">[Select]</option>');
                    $.each(data.d, function (key, value) {
                        var Id = value.int_sroid;
                        var SRO = value.Sub_Reg_Office_Name;
                        var html = '<option value="' + Id + '">' + SRO + '</option>';
                        $('#dropSubRegistrar_Office').append(html);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                var SubRegistrarOffice = $("#SubRegistrar_Office").val();
                if (SubRegistrarOffice != "") {
                    $('#dropSubRegistrar_Office').val(SubRegistrarOffice);
                    $("#SubRegistrar_Office").val('');
                }
            });
        }

        function CalcIncome() {
            var sw = $('#txtSalary_Or_Wages').val(); if (sw == "") { sw = 0; }
            var Agri = $('#txtAgriculture').val(); if (Agri == "") { Agri = 0; }
            var Others = $('#txtOthers').val(); if (Others == "") { Others = 0; }
            var Tincome = parseInt(sw) + parseInt(Agri) + parseInt(Others);
            $('#txtTotal_Annual_Income').val('');
            if (Tincome <= 0) {
                Focus_Error($('#txtTotal_Annual_Income'));
                Toast.fire({
                    icon: 'error',
                    title: 'Please Check the Annual Income !'
                })
            }
            else { $('#txtTotal_Annual_Income').val(Tincome); }
        }

        function LoanRequired() {
            var PC = $('#txtTotal_Project_Cost').val(); if (PC == "") { PC = 0; }
            var CH = $('#txtCash_Hand').val(); if (CH == "") { CH = 0; }
            var SAMT = $('#txtSubsidy_Amount').val(); if (SAMT == "") { SAMT = 0; }
            var TAMT = parseInt(PC) - parseInt(CH) - parseInt(SAMT);
            $('#txtLoan_Amount_Required').val('');
            if (TAMT <= 0) {
                Focus_Error($('#txtLoan_Amount_Required'));
                Toast.fire({
                    icon: 'error',
                    title: 'Please Check the Amount !'
                })
            }
            else {
                if ((TAMT > $("#txtTotal_Project_Cost").val())) {
                    $('#txtLoan_Amount_Required').val('');
                    Focus_Error($('#txtLoan_Amount_Required'));
                    Toast.fire({
                        icon: 'error',
                        title: 'Loan amount should be less than or equal to project cost!'
                    })
                }
                else {
                    $('#txtLoan_Amount_Required').val(TAMT);
                }
            }
        }

        function Save() {

            var Co_Applicant_Name = $("#txtName");
            var Father_Name = $("#txtName_Of_Father");
            var Mother_Name = $("#txtName_of_Mother");
            var Spouse_Name = $("#txtName_Of_Spouse");
            var Relation_With_Applicant = $("#dropRelation_With_Applicant");
            var DOB = $("#txtDate_Of_Birth");
            var Age = $("#txtAge");
            var AadharNumber = $("#txtAadhar_Number");
            var Election_Id_Number = $("#txtElection_Id_Number");
            var Ration_Card_No = $("#txtRation_Card_No");
            var Salary_Or_Wages = $("#txtSalary_Or_Wages");
            var IncomeAgriculture = $("#txtAgriculture");
            var IncomeOthers = $("#txtOthers");
            var Income = $("#txtTotal_Annual_Income");
            var Land_Owner_Name = $("#txtName_Of_Land_Owner");
            var DeedNo = $("#txtDeed_No");
            var SurveyNo = $("#txtSurvey_No");
            var Extent = $("#txtExtent");
            var Tandaperu = $("#txtTandaperu");
            var BlockNo = $("#txtBlock_No");
            var Village = $("#dropVillage");
            var Taluk = $("#dropTaluk");
            var District = $("#dropDistrict");
            var DeedType = $("#dropdeedtype");
            var OtherDeedType = $("#txtDeedType_Others");
            var SubRegistrar_Office = $("#dropSubRegistrar_Office");
            var Basement_Area = $("#txtBasement_Area");
            var No_Of_Floors = $("#txtNo_Of_Floors");
            var Carpet_Area = $("#txtCarpet_Area");
            var Construction_Type = $("#txtConstruction_Type");
            var Cost_Of_Construction = $("#txtTotal_Cost_Of_Construction");
            var Permit_NOC_Number = $("#txtPermit_NOC_Number");
            var Validity_Of_Permit = $("#txtValidity_Of_Permit");
            var Permit_NOC_Date = $("#txtPermit_NOC_Date");
            var ckboxG = $('#checkBenifit_Gov');
            var Benifit_Gov = 0;
            var Benifit_Gov_Details = $('#txtBenifit_Gov_Details');
            var Benifit_Gov_Amount = $('#txtBenifit_Gov_Amount');
            var Total_Project_Cost = $('#txtTotal_Project_Cost');
            var Cash_Hand = $('#txtCash_Hand');
            var Subsidy_Amount = $('#txtSubsidy_Amount');
            var Loan_Amount_Required = $('#txtLoan_Amount_Required');
            var RepayPeriod = $('#txt_RepayPeriod');
            var SubsidyAmount = Subsidy_Amount.val();

            if (Co_Applicant_Name.val().trim() == "") {
                Focus_Error(Co_Applicant_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Name is required !'
                })
            }
            else if (!Is_Valid_Text(Co_Applicant_Name.val().trim())) {
                Focus_Error(Co_Applicant_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed !'
                })
            }
            else if (Father_Name.val().trim() == "") {
                Focus_Error(Father_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Father Name is required !'
                })
            }
            else if (!Is_Valid_Text(Father_Name.val().trim())) {
                Focus_Error(Father_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed !'
                })
            }
            else if (Mother_Name.val().trim() == "") {
                Focus_Error(Mother_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Mother Name is required !'
                })
            }
            else if (!Is_Valid_Text(Mother_Name.val().trim())) {
                Focus_Error(Mother_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed !'
                })
            }
            else if (Spouse_Name.val().trim() == "") {
                Focus_Error(Spouse_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Spouse Name is required !'
                })
            }
            else if (!Is_Valid_Text(Spouse_Name.val().trim())) {
                Focus_Error(Spouse_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed !'
                })
            }

            else if (Relation_With_Applicant.val() == "0") {
                Focus_Error(Relation_With_Applicant);
                Toast.fire({
                    icon: 'error',
                    title: 'Relationship With Applicant is required !'
                })
            }

            else if (DOB.val().trim() == "") {
                Focus_Error(DOB);
                Toast.fire({
                    icon: 'error',
                    title: 'Date of Birth is required !'
                })
            }
            else if (Age.val() == "0") {
                Focus_Error(Age);
                Toast.fire({
                    icon: 'error',
                    title: 'Age is required !'
                })
            }
            else if (AadharNumber.val().trim() == "") {
                Focus_Error(AadharNumber);
                Toast.fire({
                    icon: 'error',
                    title: 'Aadhar Number is required !'
                })
            }
            else if (AadharNumber.val().trim().length != "12") {
                Focus_Error(AadharNumber);
                Toast.fire({
                    icon: 'error',
                    title: 'A Valid Aadhar Number is required !'
                })
            }
            else if (Election_Id_Number.val().trim() == "") {
                Focus_Error(Election_Id_Number);
                Toast.fire({
                    icon: 'error',
                    title: 'Election Id Number is required !'
                })
            }

            else if (Ration_Card_No.val().trim() == "") {
                Focus_Error(Ration_Card_No);
                Toast.fire({
                    icon: 'error',
                    title: 'Ration Card Number is required !'
                })
            }
            else if (!Is_Valid_RationCard_No(Ration_Card_No.val().trim())) {
                Focus_Error(Ration_Card_No);
                Toast.fire({
                    icon: 'error',
                    title: 'Invalid Ration Card Number. Please enter 10 digits !'
                })
            }
            else if (Salary_Or_Wages.val().trim() == "") {
                Focus_Error(Salary_Or_Wages);
                Toast.fire({
                    icon: 'error',
                    title: 'Salary / Wages is required !'
                })
            }
            else if (IncomeAgriculture.val().trim() == "") {
                Focus_Error(IncomeAgriculture);
                Toast.fire({
                    icon: 'error',
                    title: 'Income from Agriculture is required !'
                })
            }
            else if (IncomeOthers.val().trim() == "") {
                Focus_Error(IncomeOthers);
                Toast.fire({
                    icon: 'error',
                    title: 'Income from Othres is required !'
                })
            }
            else if (Income.val().trim() == "") {
                Focus_Error(Income);
                Toast.fire({
                    icon: 'error',
                    title: 'Income is required !'
                })
            }
            else if (Land_Owner_Name.val().trim() == "") {
                Focus_Error(Land_Owner_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Land Owner Name is required !'
                })
            }
            else if (!Is_Valid_Text(Land_Owner_Name.val().trim())) {
                Focus_Error(Land_Owner_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed !'
                })
            }
            else if (DeedNo.val().trim() == "") {
                Focus_Error(DeedNo);
                Toast.fire({
                    icon: 'error',
                    title: 'Deed No is required !'
                })
            }
            else if (Extent.val().trim() == "") {
                Focus_Error(Extent);
                Toast.fire({
                    icon: 'error',
                    title: 'Extent is required !'
                })
            }
            else if (SurveyNo.val().trim() == "") {
                Focus_Error(SurveyNo);
                Toast.fire({
                    icon: 'error',
                    title: 'Surevey Number is required !'
                })
            }
            else if (Tandaperu.val().trim() == "") {
                Focus_Error(Tandaperu);
                Toast.fire({
                    icon: 'error',
                    title: 'Tandaperu is required !'
                })
            }
            else if (BlockNo.val().trim() == "") {
                Focus_Error(BlockNo);
                Toast.fire({
                    icon: 'error',
                    title: 'Block Number is required !'
                })
            }
            else if (Village.val().trim() == "0") {
                Focus_Error(Village);
                Toast.fire({
                    icon: 'error',
                    title: 'Village is required !'
                })
            }
            else if (Taluk.val().trim() == "0") {
                Focus_Error(Taluk);
                Toast.fire({
                    icon: 'error',
                    title: 'Taluk is required !'
                })
            }
            else if (District.val().trim() == "0") {
                Focus_Error(District);
                Toast.fire({
                    icon: 'error',
                    title: 'District is required !'
                })
            }
            else if (DeedType.val().trim() == "0") {
                Focus_Error(DeedType);
                Toast.fire({
                    icon: 'error',
                    title: 'Deed Type is required !'
                })
            }
            else if ((DeedType.val().trim() == "Others") && (OtherDeedType.val().trim() == "")) {
                Focus_Error(OtherDeedType);
                Toast.fire({
                    icon: 'error',
                    title: 'Deed Type is required !'
                })
            }
            else if (SubRegistrar_Office.val().trim() == "0") {
                Focus_Error(SubRegistrar_Office);
                Toast.fire({
                    icon: 'error',
                    title: 'Sub Registrar Office is required !'
                })
            }
            else if (Basement_Area.val().trim() == "") {
                Focus_Error(Basement_Area);
                Toast.fire({
                    icon: 'error',
                    title: 'Basement Area is required !'
                })
            }
            else if (No_Of_Floors.val().trim() == "") {
                Focus_Error(No_Of_Floors);
                Toast.fire({
                    icon: 'error',
                    title: 'Number Of Floors is required !'
                })
            }
            else if (Carpet_Area.val().trim() == "") {
                Focus_Error(Carpet_Area);
                Toast.fire({
                    icon: 'error',
                    title: 'Carpet Area is required !'
                })
            }
            else if (Construction_Type.val().trim() == "") {
                Focus_Error(Construction_Type);
                Toast.fire({
                    icon: 'error',
                    title: 'Construction Type is required !'
                })
            }
            else if (Cost_Of_Construction.val().trim() == "") {
                Focus_Error(Cost_Of_Construction);
                Toast.fire({
                    icon: 'error',
                    title: 'Cost Of Construction is required !'
                })
            }
            else if (Permit_NOC_Number.val().trim() == "") {
                Focus_Error(Permit_NOC_Number);
                Toast.fire({
                    icon: 'error',
                    title: 'Permit / NOC Number is required !'
                })
            }
            else if (Validity_Of_Permit.val().trim() == "") {
                Focus_Error(Validity_Of_Permit);
                Toast.fire({
                    icon: 'error',
                    title: 'Validity Of Permit is required !'
                })
            }
            else if (Permit_NOC_Date.val().trim() == "") {
                Focus_Error(Permit_NOC_Date);
                Toast.fire({
                    icon: 'error',
                    title: 'Permit / NOC Date is required !'
                })
            }
            else if ((ckboxG.is(':checked')) && (Benifit_Gov_Details.val().trim() == "")) {
                Focus_Error(Benifit_Gov_Details);
                Toast.fire({
                    icon: 'error',
                    title: 'Details of Any Concession / Benifit  Received from Govt is required !'
                })
            }
            else if ((ckboxG.is(':checked')) && (Benifit_Gov_Amount.val().trim() == "")) {
                Focus_Error(Benifit_Gov_Amount);
                Toast.fire({
                    icon: 'error',
                    title: 'Details of Any Concession / Benifit Amount Received from Govt  is required !'
                })
            }
            else if (Total_Project_Cost.val().trim() == "") {
                Focus_Error(Total_Project_Cost);
                Toast.fire({
                    icon: 'error',
                    title: 'Total Project Cost is required !'
                })
            }
            else if (Total_Project_Cost.val().trim() == "0") {
                Focus_Error(Total_Project_Cost);
                Toast.fire({
                    icon: 'error',
                    title: 'Total Project Cost is required !'
                })
            }
            else if (Cash_Hand.val().trim() == "") {
                Focus_Error(Cash_Hand);
                Toast.fire({
                    icon: 'error',
                    title: 'Cash in Hand is required !'
                })
            }
            else if (Cash_Hand.val().trim() == "0") {
                Focus_Error(Cash_Hand);
                Toast.fire({
                    icon: 'error',
                    title: 'Cash in Hand is required !'
                })
            }
            else if (Subsidy_Amount.val().trim() == "") {
                SubsidyAmount = 0;
            }
            else if (Loan_Amount_Required.val().trim() == "") {
                Focus_Error(Loan_Amount_Required);
                Toast.fire({
                    icon: 'error',
                    title: 'Loan Amount is required !'
                })
            }
            else if (RepayPeriod.val().trim() == "") {
                Focus_Error(RepayPeriod);
                Toast.fire({
                    icon: 'error',
                    title: 'Repayment Period is required !'
                })
            }
            else if (RepayPeriod.val().trim() > "240") {
                Focus_Error(RepayPeriod);
                Toast.fire({
                    icon: 'error',
                    title: 'Repayment Period is Maximum 240 Months !'
                })
            }



            else {

                if (ckboxG.is(':checked')) { Benifit_Gov = 1; }
                else { Benifit_Gov = 0; Benifit_Gov_Details.val(''); Benifit_Gov_Amount.val(0) }

                var Deed_Type;

                if (DeedType.val().trim() == "Others") { Deed_Type = OtherDeedType.val(); }
                else { Deed_Type = DeedType.val(); }

                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/Update_To_tbl_House_Loan",
                    data: JSON.stringify({ int_loanappid: getParameterByName("Id"), vchr_coapp_name: Co_Applicant_Name.val(), vchr_Father: Father_Name.val(), vchr_Mother: Mother_Name.val(), vchr_Spouse: Spouse_Name.val(), vchr_Relation: Relation_With_Applicant.val(), dte_dob: DOB.val(), int_age: Age.val(), vchr_Aadhaar: AadharNumber.val(), vchr_electionid: Election_Id_Number.val(), vchr_ration: Ration_Card_No.val(), int_income_salary: Salary_Or_Wages.val(), int_income_agr: IncomeAgriculture.val(), int_income_other: IncomeOthers.val(), int_income_total: Income.val(), vchr_ownername: Land_Owner_Name.val(), vchr_extent: Extent.val(), vchr_surveyno: SurveyNo.val(), vchr_Blockno: BlockNo.val(), vchr_TPno: Tandaperu.val(), vchr_land_village: Village.val(), vchr_land_taluk: Taluk.val(), vchr_land_district: District.val(), vchr_deedtype: Deed_Type, vchr_deedno: DeedNo.val(), vchr_subregoffiname: SubRegistrar_Office.val(), vchr_base_area: Basement_Area.val(), int_no_floor: No_Of_Floors.val(), vchr_carpet_area: Carpet_Area.val(), vchr_construction_type: Construction_Type.val(), int_cost: Cost_Of_Construction.val(), vchr_permit_no: Permit_NOC_Number.val(), dte_permit_date: Permit_NOC_Date.val(), vchr_validity: Validity_Of_Permit.val(), int_concession: Benifit_Gov, vchr_details: Benifit_Gov_Details.val(), int_concessionamt: Benifit_Gov_Amount.val(), int_total_cost: Total_Project_Cost.val(), int_cash_hand: Cash_Hand.val(), int_subsidy: SubsidyAmount, int_loan_req: Loan_Amount_Required.val(), int_repay_year: RepayPeriod.val() }),
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {

                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        // Handle AJAX error
                        //   alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);

                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                        })

                    }

                }).done(function () {
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Application Data Entry Successfully Updated!',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked

                            location.href = 'ApplicationDataEntry_Modification_List.aspx';
                            // Your code here
                        }
                    });

                });


            }




        }

        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }

        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }


        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }

        function Is_Valid_RationCard_No(Ration_Card_No) {
            var rationPattern = /^\d{10}$/;

            if (rationPattern.test(Ration_Card_No)) {
                return true;
            } else {
                return false;
            }
        }

    </script>
</asp:Content>