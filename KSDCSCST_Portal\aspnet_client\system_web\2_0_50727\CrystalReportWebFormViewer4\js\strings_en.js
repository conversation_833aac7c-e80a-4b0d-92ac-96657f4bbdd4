// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "Today";
var L_January   = "January";
var L_February  = "February";
var L_March     = "March";
var L_April     = "April";
var L_May       = "May";
var L_June      = "June";
var L_July      = "July";
var L_August    = "August";
var L_September = "September";
var L_October   = "October";
var L_November  = "November";
var L_December  = "December";
var L_Su        = "Su";
var L_Mo        = "Mo";
var L_Tu        = "Tu";
var L_We        = "We";
var L_Th        = "Th";
var L_Fr        = "Fr";
var L_Sa        = "Sa";

// strings for dt_param.js
var L_TIME_SEPARATOR = ":";
var L_AM_DESIGNATOR = "AM";
var L_PM_DESIGNATOR = "PM";

// strings for range parameter
var L_FROM = "From {0}";
var L_TO = "To {0}";
var L_AFTER = "After {0}";
var L_BEFORE = "Before {0}";
var L_FROM_TO = "From {0} to {1}";
var L_FROM_BEFORE = "From {0} to before {1}";
var L_AFTER_TO = "After {0} to {1}";
var L_AFTER_BEFORE = "After {0} to before {1}";

// Strings for prompts.js and prompts_param.js
var L_BadNumber		= "This parameter is of type \"Number\" and can only contain a negative sign symbol, digits (\"0-9\"), digit grouping symbols or a decimal symbol. Please correct the entered parameter value.";
var L_BadCurrency	= "This parameter is of type \"Currency\" and can only contain a negative sign symbol, digits (\"0-9\"), digit grouping symbols or a decimal symbol. Please correct the entered parameter value.";
var L_BadDate		= "This parameter is of type \"Date\" and should be in the format \"Date(yyyy,mm,dd)\" where \"yyyy\" is the four digit year, \"mm\" is the month (e.g. January = 1), and \"dd\" is the number of days into the given month.";
var L_BadDateTime   = "This parameter is of type \"DateTime\" and the correct format is \"DateTime(yyyy,mm,dd,hh,mm,ss)\". \"yyyy\" is the four digit year, \"mm\" is the month (e.g. January = 1), \"dd\" is the day of the month, \"hh\" is hours in a 24 hour, \"mm\" is minutes and \"ss\" is seconds.";
var L_BadTime       = "This parameter is of type \"Time\" and should be in the format \"Time(hh,mm,ss)\" where \"hh\" is hours in 24 a hour clock, \"mm\" is minutes into the hour, and \"ss\" is seconds into the minute.";
var L_NoValue       = "No Value";
var L_BadValue      = "To set \"No Value\", you must set both From and To values to \"No Value\".";
var L_BadBound      = "You cannot set \"No Lower Bound\" together with \"No Upper Bound\".";
var L_NoValueAlready = "This parameter is already set to \"No Value\". Remove \"No Value\" before adding other values";
var L_RangeError    = "The start of range cannot be greater than the end of range.";
var L_NoDateEntered = "You must enter a date.";

// Strings for ../html/crystalexportdialog.htm
var L_ExportOptions     = "Export Options";
var L_PrintOptions      = "Print Options";
var L_PrintPageTitle    = "Print the Report";
var L_ExportPageTitle   = "Export the Report";
var L_OK                = "OK";
var L_PrintPageRange    = "Enter the page range that you want to Print.";
var L_ExportPageRange   = "Enter the page range that you want to Export.";
var L_InvalidPageRange  = "The page range value(s) are incorrect.  Please enter a valid page range.";
var L_ExportFormat      = "Please select an Export format from the list.";
var L_Formats           = "Formats:";
var L_All               = "All";
var L_Pages             = "Pages";
var L_From              = "From:";
var L_To                = "To:";
var L_PrintStep0        = "To Print:";
var L_PrintStep1        = "1.  In the next dialog that appears, select the \"Open this file\" option and click the OK button.";
var L_PrintStep2        = "2.  Click the printer icon on the Acrobat Reader Menu rather than the print button on your internet browser.";
var L_RTFFormat         = "Rich Text Format";
var L_AcrobatFormat     = "Acrobat Format (PDF)";
var L_CrystalRptFormat  = "Crystal Reports (RPT)";
var L_WordFormat        = "MS Word";
var L_ExcelFormat       = "MS Excel 97-2000";
var L_ExcelRecordFormat = "MS Excel 97-2000 (Data Only)";
if(typeof(Sys)!=='undefined')Sys.Application.notifyScriptLoaded();