//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CorpNet_To_KSDC_SMART
{
    using System;
    
    public partial class Select_tbl_empsurety_By_Id_Result
    {
        public int int_empid { get; set; }
        public Nullable<decimal> int_loanappid { get; set; }
        public string vchr_appreceivregno { get; set; }
        public string vchr_empname { get; set; }
        public string vchr_fathername { get; set; }
        public string vchr_spousename { get; set; }
        public string vchr_empdesig { get; set; }
        public string vchr_empoffname { get; set; }
        public string vchr_empofflane1 { get; set; }
        public string vchr_empofflane2 { get; set; }
        public string vchr_empoffpost { get; set; }
        public Nullable<decimal> vchr_empoffpin { get; set; }
        public string vchr_empoffphno { get; set; }
        public string vchr_presenthsename { get; set; }
        public string vchr_presentlane1 { get; set; }
        public string vchr_presentlane2 { get; set; }
        public string vchr_presentpost { get; set; }
        public string vchr_presentpin { get; set; }
        public string vchr_presentphno { get; set; }
        public string vchr_emppermhsename { get; set; }
        public string vchr_emppermlane1 { get; set; }
        public string vchr_emppermlane2 { get; set; }
        public string vchr_emppermpost { get; set; }
        public string vchr_emppermpin { get; set; }
        public string vchr_emppermphno { get; set; }
        public string dte_dob { get; set; }
        public string dte_eos { get; set; }
        public string dte_empdateofretire { get; set; }
        public Nullable<decimal> int_basicpay { get; set; }
        public Nullable<decimal> int_netsal { get; set; }
        public Nullable<decimal> int_grosssal { get; set; }
        public string vchr_senior_empname { get; set; }
        public string vchr_senior_empdes { get; set; }
        public string vchr_senior_empoffname { get; set; }
        public string vchr_senior_emplane1 { get; set; }
        public string vchr_senior_emplane2 { get; set; }
        public string vchr_senior_emppost { get; set; }
        public string vchr_senior_emppin { get; set; }
        public string vchr_senior_empphone { get; set; }
        public string vchr_auditno { get; set; }
        public string vchr_depart { get; set; }
        public Nullable<bool> int_sr_officer { get; set; }
        public string vchr_scale { get; set; }
        public Nullable<System.DateTime> dte_conf_send_date { get; set; }
        public Nullable<System.DateTime> dte_conf_rec_date { get; set; }
        public string int_loanno { get; set; }
        public string vchr_oldno { get; set; }
        public string vchr_presentVillage { get; set; }
        public string vchr_presentTaluk { get; set; }
        public string vchr_presentDistrict { get; set; }
        public string vchr_PermVillage { get; set; }
        public string vchr_PermTaluk { get; set; }
        public string vchr_PermDistrict { get; set; }
        public string vchr_empoffname1 { get; set; }
        public string vchr_empofflane11 { get; set; }
        public string vchr_empofflane21 { get; set; }
        public string vchr_empoffpost1 { get; set; }
        public Nullable<decimal> vchr_empoffpin1 { get; set; }
        public string vchr_empoffphno1 { get; set; }
        public Nullable<bool> int_loanee { get; set; }
        public Nullable<bool> int_under { get; set; }
        public string vchr_Ashwas { get; set; }
        public string vchr_pen { get; set; }
        public string vchr_PanNo { get; set; }
        public Nullable<decimal> vchr_ITAckNo { get; set; }
        public string vchr_Aadhar { get; set; }
        public Nullable<decimal> int_personal { get; set; }
        public Nullable<decimal> int_eligibleloan { get; set; }
        public string vchr_scaleofpay { get; set; }
        public string vchr_PensionScheme { get; set; }
        public string vchr_PRAN { get; set; }
        public string vchr_PFNo { get; set; }
        public string Gaurantee_Type { get; set; }
        public string Aadhar_No { get; set; }
    }
}
