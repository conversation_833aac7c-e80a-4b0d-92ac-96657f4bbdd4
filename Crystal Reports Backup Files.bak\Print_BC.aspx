﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Print_BC.aspx.cs" Inherits="KSDCSCST_Portal.Print_BC" %>


 
<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Print BC Fee</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">Application Issue Print</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>
    <section class="content" style="display:none;">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">

                                    <div class="form-group row">
                                        <label for="inputName" class="col-sm-3 col-form-label">Date(DD/MM/YYYY)</label>
                                        <div class="col-sm-9">
                                            <input disabled="disabled" type="text" class="form-control" id="txtApplication_Date" placeholder="Date(DD/MM/YYYY)">
                                        </div>
                                    </div>
                                     <div class="form-group row"  >
                                        <label for="inputEmail" class="col-sm-3 col-form-label">District *</label>
                                        <div class="col-sm-9">
                                            <select id="dropDistrict" disabled="disabled" class="form-control">
                                               

                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row"  >
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Branch *</label>
                                        <div class="col-sm-9">
                                            <select id="dropSubDistrict"  disabled="disabled" class="form-control">
                                        


                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row"  >
                                        <label for="inputName2" class="col-sm-3 col-form-label">Application Number</label>
                                        <div class="col-sm-9">
                                            <input disabled="disabled" type="text" class="form-control" id="txtApplicationNo" value="255" placeholder="Mobile Number">
                                        </div>
                                    </div>
                                      <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Name of Applicant *</label>
                                        <div class="col-sm-9">
                                            <input  type="text" class="form-control" id="txtApplicant_Name"   placeholder="Name of Applicant">
                                        </div>
                                    </div>
                                      <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Aadhar Number*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtAadharNumber" placeholder="Aadhar Number">
                                        </div>
                                    </div>


                                      <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Loan Scheme *</label>
                                        <div class="col-sm-9">
                                            <select id="dropScheme" class="form-control">
                                                <option>Select</option>
                                               
                                                
                                            </select>
                                        </div>
                                    </div>
                                          <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Caste *</label>
                                        <div class="col-sm-9">
                                            <select  id="dropCast" class="form-control">
                                                <option>Select</option> 
                                                
                                            </select>
                                        </div>
                                    </div>
                                        <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Sub Caste *</label>
                                        <div class="col-sm-9">
                                            <select  id="dropSubCast"  class="form-control">
                                                <option>Select</option>
                                                 
                                                
                                            </select>
                                        </div>
                                    </div>
                                     <div class="form-group row" >
                                        <label for="inputName2" class="col-sm-3 col-form-label">Receipt No *</label>
                                        <div class="col-sm-9">
                                            <input disabled="disabled" type="text" class="form-control" id="txtReceiptNo" value="65758"   placeholder="Name of Applicant">
                                        </div>
                                    </div>
                                      <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Phone No *</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtPhoneNo" placeholder="Phone No">
                                            <span class="validation_message">seperate numbers by comma ","</span>
                                        </div>
                                    </div>
                                       <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Amount *</label>
                                        <div class="col-sm-9">
                                            <input disabled="disabled" type="text" class="form-control" id="txtAmount" value="10"   placeholder="Name of Applicant">
                                        </div>
                                    </div>
                                       <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Remarks</label>
                                        <div class="col-sm-9">
                                            <textarea class="form-control" maxlength="200" id="txtRemarks" placeholder="Remarks"></textarea>
                                              <span  class="validation_message">Maximum text length is 200</span>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                            <a href="ApplicationIssue_List.aspx" class="btn btn-dark">Back</a>
                                            <a onclick="Save();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-7 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">

                                    <div id="Print_Section" style="display: block;">

                                        <div style="padding: 0px; float: left; width: 100%;">

                                            <div style="margin: 20px;">
                                                <div style="width: 100%">
                                                    <div style="width: 30%; float: left;">
                                                        <img style="width: 220px;" src="assets/img/Logo_Black_And_White.jpg" />
                                                        <p style="font-size: 12px; font-weight: bold; margin: 0; margin-top: 9px;">A GOVT.OF KERALA UNDER TAKING</p>
                                                    </div>
                                                    <div style="width: 70%; float: left;">
                                                        <p style="font-size: 16px; font-weight: bold; line-height: 1.5; margin-bottom: 0px !important; margin-top: 0px;">കേരള സംസ്ഥാന പട്ടികജാതി  പട്ടിക വർഗ്ഗ വികസന  കോർപ്പറേഷൻ  ലിമിറ്റഡ്</p>
                                                        <p style="margin-bottom: 0; font-size: 14px; margin-top: 5px;">CIN: U91990KL1972SGC002466</p>
                                                        <p style="margin-bottom: 0; font-size: 12px; margin-top: 5px;">
                                                            <b>CORPORATE OFFICE:</b> P.B. No. 523, Town Hall Road, Thrissur - 680020 |
                                                       
                                                        <b>Off.Ph:</b> 0487 2331064 |
                                                            <br />
                                                            <b>Email : </b><EMAIL> | Website : ksdcscst.kerala.gov.in | GST : 32AAECK5412D1Z5
                                                        </p>
                                                    </div>
                                                </div>

                                                <div style="width: 100%; float: left; margin-top: 5px;">
                                                    <div style="width: 25%; float: left;">
                                                        <div style="text-align: left; font-weight: bold;">
                                                            <span  style="font-size: 14px;">Receipt No: </span>
                                                            <label id="lbl_Receipt_No" style="font-size: 14px; font-weight: normal !important;"></label>
                                                        </div>
                                                    </div>
                                                    <div style="width: 50%; float: left;">
                                                        <div style="text-align: center; font-weight: bold;">
                                                            <p style="font-size: 14px; text-decoration: underline;">DISTRICT OFFICE :  <span id="span_Office_Name"></span></p>
                                                        </div>
                                                    </div>
                                                    <div style="width: 25%; float: left;">
                                                        <div style="text-align: right; font-weight: bold;">
                                                            <span style="font-size: 14px;">Date : </span>
                                                            <label id="lblDate" style="font-size: 14px; font-weight: normal !important;"></label>
                                                        </div>
                                                    </div>
                                                </div>


                                                <hr style="float: left; width: 100%; margin-top: 0px;" />
                                                <div style="width: 100%; float: left; margin-top: 5px;">
                                                    <div style="width: 100%; float: left;">
                                                        <div style="text-align: left; font-weight: bold;">
                                                            <span style="font-size: 14px; float: left; font-weight: normal !important;">Received From </span>
                                                            <label id="lblName" style="font-size: 14px; float: left; margin-left: 5px; margin-right: 5px;"></label>
                                                              <label id="lbl_Address" style="font-weight: bold; font-size: 14px; float: left; margin-left: 5px; margin-right: 5px;"></label>

                                                            <span style="font-size: 14px; float: left; font-weight: normal !important;">the sum of Rupees </span>
                                                            <label style="font-size: 14px; float: left; margin-left: 5px; margin-right: 5px;"><span id="span_Amount_Text"></span></label>

                                                        </div>
                                                    </div>
                                                    <div style="width: 100%; float: left;">
                                                        <span style="font-size: 14px; font-weight: bold;">in cash towards the following </span>
                                                    </div>
                                                    <div style="width: 100%; float: left;">
                                                        <table border="1" style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                                                            <tr>
                                                                <td rowspan="2" style="text-align: center; font-weight: bold; font-size: 12px;">തിയ്യതി</td>
                                                                <td rowspan="2" style="text-align: center; font-weight: bold; font-size: 12px;">ഇനം</td>
                                                                <td colspan="2" style="text-align: center; font-weight: bold; font-size: 12px;">തുക</td>

                                                            </tr>
                                                            <tr>

                                                                <td style="text-align: center; font-weight: bold;">Rs.</td>
                                                                <td style="text-align: center; font-weight: bold;">Ps.</td>

                                                            </tr>
                                                            <tr>
                                                                <td style="text-align: center;"><span id="span_Date"></span></td>
                                                                <td style="font-size: 12px;">ബെനിഫിഷ്യറി കോൺട്രിബ്യുഷൻ(<span id="span_Scheme"></span>)</td>

                                                                <td style="text-align: right;"><span id="span_Amount_1"></span></td>
                                                                <td style="text-align: right;">0</td>
                                                            </tr>
                                                            <tr>
                                                                <td colspan="2" style="padding-right: 10px; font-weight: bold; text-align: right; font-size: 12px;">ആകെ</td>

                                                                <td style="font-weight: bold; text-align: right;" colspan="2"><span id="span_Amount_2"></span></td>

                                                            </tr>
                                                            <tr>
                                                                <td style="font-weight: bold;">Prepared</td>
                                                                <td style="font-weight: bold;">Checked</td>
                                                                <td style="text-align: right;" colspan="2"></td>

                                                            </tr>
                                                        </table>
                                                    </div>
                                                    <div style="width: 100%; float: right;">
                                                        <span style="width: 45%; font-size: 14px; text-align: right; line-height: 1.5; margin-bottom: 0px !important; margin-top: 10px; float: right;">For The Kerala State Development Corporation For Scheduled Castes & Scheduled Tribes Limited </span>

                                                    </div>
                                                    <div style="width: 100%; float: right;">
                                                        <span style="width: 45%; font-size: 14px; text-align: right; line-height: 1.5; margin-bottom: 0px !important; margin-top: 65px; float: right; font-weight: bold;">ജില്ലാ മാനേജർ</span>

                                                    </div>
                                                    <div style="width: 100%; float: left; text-align: center;">
                                                        <span style="font-size: 14px; text-align: center; line-height: 1.5; margin-bottom: 0px !important; margin-top: 10px;">This Receipt is valid only on realisation of Cheque/Draft</span>

                                                    </div>
                                                </div>
                                            </div>

                                        </div>

                                    </div>


                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                            <a href="Reciept_Processing_Fee.aspx" class="btn btn-dark">Back</a>
                                            <a onclick="Print();" class="btn btn-success">Print</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    
  <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>

    <script>
        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;
        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }
        $(document).ready(function () {
            // Get the current date

            Load_Acc_Trans();
            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });
            $("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
            $("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");
            //  Load_All_Schemes();
            //              Load_All_Districts();


            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }



            }

            $(document).on("focusout", ".form-control", function () {
                $(".form-control").removeAttr("style");

                var This_Id = $(this).attr("id");
                var This_Control = $(this);
                if (This_Id == "txtApplicant_Name") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Name of Applicant is required !'
                        })
                    }
                    else if (!Is_Valid_Text(This_Control.val().trim())) {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Special characters or Numbers not allowed in Name of Applicant !'
                        })
                    }
                }
                else if (This_Id == "dropScheme") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Loan Scheme is required !'
                        })
                    }
                }
                else if (This_Id == "dropCast") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Caste is required !'
                        })
                    }
                }
                else if (This_Id == "dropSubCast") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Sub Caste is required !'
                        })
                    }
                }
                else if (This_Id == "txtPhoneNo") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Phone No is required !'
                        })
                    }
                }
                //else if (This_Id == "txtAadharNumber") {
                //    if (This_Control.val().trim() == "") {
                //        Focus_Error(This_Control);
                //        Toast.fire({
                //            icon: 'error',
                //            title: 'Aadhar No is required !'
                //        })
                //    }
                //}
                // Your common focusout function code goes here
                //   alert("Input value changed: " + value);
            });


            //$(document).on("input", ".form-control", function () {
            //    // Get the current value of the input
            //    var currentValue = $(this).val();

            //    // Convert the value to propcase
            //    var propcaseValue = currentValue.replace(/\w\S*/g, function (txt) {
            //        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
            //    });

            //    // Update the input with the propcase value
            //    $(this).val(propcaseValue);
            //});


            var currentDate = new Date();

            // Format the date using jQuery UI's formatDate method
            var formattedDate = $.datepicker.formatDate('dd-mm-yy', currentDate);

            // Display the formatted date
            //  alert(formattedDate);

            $("#lblDate").text(formattedDate);
            $("#span_Date").text(formattedDate);
            $("#span_Amount_1").text(getParameterByName("Amount"));
            $("#span_Amount_2").text(getParameterByName("Amount"));
            $("#span_Office_Name").text("<%= HttpContext.Current.Session["SubDistrict_Name"]%>");
            $("#span_Amount_Text").text(convertNumberToWords(getParameterByName("Amount")));
            $("#lbl_Receipt_No").text(getParameterByName("Rec_No"));
            $("#lblName").text(getParameterByName("Name") + " (Reg No : " + getParameterByName("Reg_No") + ")");
            Select_Lonees_By_Reg_No();

        });

        function Select_Lonees_By_Reg_No() {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ reg_No: getParameterByName("Reg_No") }), // If you have parameters
                url: "WebService.asmx/Select_Lonees_By_Reg_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $.each(data.d, function (key, value) {


                        $("#lbl_Address").text(value.vchr_hsename);
                        $("#lbl_Post_Office").text('Post Office : (' + value.vchr_post + '->PIN:' + value.int_pincode + ')');
                        $("#span_Scheme").text(value.Remarks_Manager);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });
        }


        function Load_Acc_Trans() {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ reg_No: getParameterByName("Reg_No") }), // If you have parameters
                url: "WebService.asmx/Get_BC_Fee_Rec_No",
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    $.each(data.d, function (key, value) {


                        $("#lbl_Receipt_No").text(value.vchr_TransNo);


                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });
        }
        function convertNumberToWords(number) {
            var units = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
            var teens = ['', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
            var tens = ['', 'Ten', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];

            var result = '';

            var crores = Math.floor(number / 10000000);
            var remainder = number % 10000000;

            var lakhs = Math.floor(remainder / 100000);
            remainder %= 100000;

            var thousands = Math.floor(remainder / 1000);
            remainder %= 1000;

            var hundreds = Math.floor(remainder / 100);
            remainder %= 100;

            var tensAndUnits = remainder;

            if (crores > 0) {
                result += convertThreeDigitNumberToWords(crores) + ' Crore ';
            }

            if (lakhs > 0) {
                result += convertTwoDigitNumberToWords(lakhs) + ' Lakh ';
            }

            if (thousands > 0) {
                result += convertTwoDigitNumberToWords(thousands) + ' Thousand ';
            }

            if (hundreds > 0) {
                result += convertThreeDigitNumberToWords(hundreds) + ' Hundred ';
            }

            if (tensAndUnits > 0) {
                if (tensAndUnits >= 1 && tensAndUnits <= 99) {
                    result += convertTwoDigitNumberToWords(tensAndUnits);
                } else if (tensAndUnits === 100) {
                    result += 'One Hundred';
                }
            }

            return result.trim();
        }

        function convertThreeDigitNumberToWords(number) {
            var units = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
            var result = '';

            var hundreds = Math.floor(number / 100);
            var remainder = number % 100;

            if (hundreds > 0) {
                result += units[hundreds] + ' Hundred';
                if (remainder > 0) {
                    result += ' and ';
                }
            }

            result += convertTwoDigitNumberToWords(remainder);
            return result;
        }

        function convertTwoDigitNumberToWords(number) {
            var units = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
            var teens = ['', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
            var tens = ['', 'Ten', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];

            var result = '';

            if (number >= 11 && number <= 19) {
                result += teens[number - 10];
            } else {
                var tensDigit = Math.floor(number / 10);
                var unitsDigit = number % 10;

                if (tensDigit > 0) {
                    result += tens[tensDigit];
                    if (unitsDigit > 0) {
                        result += ' ';
                    }
                }

                if (unitsDigit > 0) {
                    result += units[unitsDigit];
                }
            }

            return result;
        }

        function Print() {
            var printWindow = window.open('', '_blank');
            printWindow.document.open();
            printWindow.document.write('<html><head><title>Print</title></head><body style="width: 210mm;height: 297mm;margin: 0;  ">');
            printWindow.document.write($("#Print_Section").html());
            printWindow.document.write('</body></html>');
            printWindow.document.close();
            printWindow.print();
            if (confirm('Print dialog initiated. Please close the window after printing.')) {
                printWindow.close();
                Update_Payemnt();
            }
        }
        function Update_Payemnt() {



            $.ajax({
                type: "POST", // or "GET" depending on your web method
                url: "WebService.asmx/Update_BenifContribution_Payemnt",
                data: JSON.stringify({ Reg_No: getParameterByName("Reg_No") }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (response) {

                    // Handle the successful response from the web method
                    console.log(response.d); // "d" is the default property name for the response
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Beneficiary Contribution Successfully Done !',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked

                            location.href = 'Receipt_Beneficiary_Contribution_List.aspx';
                            // Your code here
                        }
                    });

                },
                error: function (xhr, status, error) {
                    // Handle the error response

                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                },
                done: function (response) {
                    // Handle the error response
                    //  alert(response);

                }
            });


        }



    </script>
</asp:Content>


