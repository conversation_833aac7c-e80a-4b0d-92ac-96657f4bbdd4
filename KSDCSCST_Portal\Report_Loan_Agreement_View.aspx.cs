﻿// Crystal Reports related using statements commented out due to missing assembly references
// using CrystalDecisions.CrystalReports.Engine;
// using CrystalDecisions.Shared;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Collections;
// using CrystalDecisions.Web;
using System.Data.SqlClient;
using System.Data;

namespace KSDCSCST_Portal
{
    public partial class Report_Loan_Agreement_View : System.Web.UI.Page
    {
        // Crystal Reports related code commented out due to missing assembly references
        // ReportDocument reportDocument = new ReportDocument();

        protected void Page_Load(object sender, EventArgs e)
        {
            Response.Cache.SetCacheability(HttpCacheability.NoCache);
            Response.Cache.SetNoStore();

            if (Convert.ToString(Session["User_Id"]) == "")
            {
                Response.Redirect("Default.aspx");
            }
            else
            {
                // LoadReport(); // Crystal Reports functionality commented out
                Response.Write("Crystal Reports functionality is currently disabled due to missing assembly references.");
            }

        }

        // Crystal Reports related code commented out due to missing assembly references
        /*
        private void LoadReport()
        {
            reportDocument.Load(Server.MapPath("~/Crystal_Reports/LoanAgreementRpt.rpt")); // Load your report file
            string qry = String.Empty;
            string daterange, office, pdfPath,pdfFileName;
            pdfPath = string.Empty;

            DateTime DtFrom = Convert.ToDateTime(HttpContext.Current.Request.QueryString["From"]);
            DateTime DtTo = Convert.ToDateTime(HttpContext.Current.Request.QueryString["To"]);
            office = HttpContext.Current.Session["SubDistrict_Name"].ToString();

            if (DtFrom == DtTo)
            {
                daterange = "Checklit Approved On " + DtFrom.ToString("dd/MM/yyyy");
            }
            else
            {
                daterange = "Checklit Approved From " + DtFrom.ToString("dd/MM/yyyy") + " To " + DtTo.ToString("dd/MM/yyyy");
            }

            TableLogOnInfo ConInfo = new TableLogOnInfo();

            foreach (CrystalDecisions.CrystalReports.Engine.Table Table in reportDocument.Database.Tables)
            {
                ConInfo = Table.LogOnInfo;
                ConInfo.ConnectionInfo.UserID = System.Configuration.ConfigurationManager.AppSettings["UID"];
                ConInfo.ConnectionInfo.Password = System.Configuration.ConfigurationManager.AppSettings["PWD"];
                ConInfo.ConnectionInfo.ServerName = System.Configuration.ConfigurationManager.AppSettings["SNAME"];
                ConInfo.ConnectionInfo.DatabaseName = System.Configuration.ConfigurationManager.AppSettings["DB"];
                Table.ApplyLogOnInfo(ConInfo);
            }

            qry = "{tbl_loanapp.dte_agrement_date}  in  DateTime (" + DtFrom.Year + ", " + DtFrom.Month + ", " + DtFrom.Day + ",00, 00, 00) to DateTime (" + DtTo.Year + ", " + DtTo.Month + ", " + DtTo.Day + ",23, 59, 59) and {tbl_loanapp.vchr_offid} = '" + Convert.ToString(HttpContext.Current.Session["Office_Id"]) + "'  and {tbl_loanapp.int_AgrChk}=1 ";

            reportDocument.RecordSelectionFormula = qry;

            ArrayList myArrayList = new ArrayList();
            ArrayList myArrayList2 = new ArrayList();

            if (!IsPostBack)
            {
                myArrayList.Add(daterange);
                myArrayList2.Add("daterange");
                myArrayList.Add(office);
                myArrayList2.Add("office");

                Session["myArrayList"] = myArrayList;
                Session["myArrayList2"] = myArrayList2;


            }
            else
            {
                myArrayList = (ArrayList)Session["myArrayList"];
                myArrayList2 = (ArrayList)Session["myArrayList2"];
            }

            reportDocument.SetParameterValue("daterange", daterange);
            reportDocument.SetParameterValue("office", office);

            // reportDocument.Refresh();

            CrystalReportViewer1.ReportSource = reportDocument;
            //reportDocument.Refresh();
            CrystalReportViewer1.Visible = true;
            CrystalReportViewer1.RefreshReport();


            try
            {
                // Construct the PDF file path
                string fileName = "CheckList_Approved_Report" + HttpContext.Current.Session["User_Id"] + "_" + DateTime.Now.ToString("dd_MM_yyyy_hh_MM_ss");
                pdfPath = Server.MapPath("~/Reports/" + fileName);

                // Ensure the directory exists
                string directoryPath = Server.MapPath("~/Reports/");
                if (!Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                }

                // Export the report to PDF
                reportDocument.ExportToDisk(ExportFormatType.PortableDocFormat, pdfPath);

                pdfFileName = "CheckList_Approved_Report_" + office+".pdf";

                // Serve the PDF file to the client
                Response.Clear();
                Response.ContentType = "application/pdf";
                Response.AppendHeader("Content-Disposition", "inline; filename=" + fileName);
                Response.TransmitFile(pdfPath);
                Response.Flush();
                //Response.End();  // Ensure the response is properly ended

                // Optionally, you may want to delete the file after transmission if it should not be stored on the server
                //File.Delete(pdfPath);
            }
            catch (UnauthorizedAccessException ex)
            {
                // Handle permission issues
                Console.WriteLine("Permission error: " + ex.Message);
            }
            catch (IOException ex)
            {
                // Handle file system issues
                Console.WriteLine("IO error: " + ex.Message);
            }
            catch (Exception ex)
            {
                // Handle other issues
                Console.WriteLine("An error occurred: " + ex.Message);
                // Log exception details
            }
            finally
            {
                Response.Close();
                File.Delete(pdfPath);
            }

        }
        private void Page_Unload(object sender, System.EventArgs e)
        {
            reportDocument.Close();
            reportDocument.Dispose();
            CrystalReportViewer1.Dispose();
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
        }
        */


    }
}