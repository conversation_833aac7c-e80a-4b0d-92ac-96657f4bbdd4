﻿<?xml version="1.0" encoding="utf-8" ?>


<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />

  </configSections>
   
  <connectionStrings>
    <!-- <add name="KSDCEntities" connectionString="metadata=res://*/KSDCEntities.csdl|res://*/KSDCEntities.ssdl|res://*/KSDCEntities.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=SQL5109.site4now.net;initial catalog=db_a7a28a_ksdc;user id=db_a7a28a_ksdc_admin;password=************;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
    <!-- <add name="KSDCEntities" connectionString="metadata=res://*/KSDCEntities.csdl|res://*/KSDCEntities.ssdl|res://*/KSDCEntities.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=DESKTOP-JP80BH3\SQLEXPRESS;initial catalog=ksdc;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
    <!-- <add name="db_a7a28a_ksdcEntities" connectionString="metadata=res://*/KSDCEntities.csdl|res://*/KSDCEntities.ssdl|res://*/KSDCEntities.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=SQL5109.site4now.net;initial catalog=db_a7a28a_ksdc;user id=db_a7a28a_ksdc_admin;password=************;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" /></connectionStrings>-->
    <add name="db_a7a28a_ksdcEntities" connectionString="metadata=res://*/KSDCEntities.csdl|res://*/KSDCEntities.ssdl|res://*/KSDCEntities.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=DESKTOP-JP80BH3\SQLEXPRESS;initial catalog=ksdc;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  </connectionStrings>
   
</configuration>
