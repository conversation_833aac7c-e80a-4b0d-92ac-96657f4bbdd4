﻿<%@ Page Title="Agency | List" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Agency_List.aspx.cs" Inherits="KSDCSCST_Portal.Agency_List" %>



<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- DataTables -->
    <link rel="stylesheet" href="assets/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
    <link rel="stylesheet" href="assets/plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">


    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Application Issues</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                       
                           <li class="breadcrumb-item">Settings</li>
                        <li class="breadcrumb-item active">Agency List</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">


                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">List All Application Issues</h3>
                            <div style="float: right;"><a href="Agency_Add.aspx" style="background: #0f6caa;border-color: #0f6caa;" class="btn btn-block btn-success">Create New Agency</a></div>
                        </div>
                        <!-- /.card-header -->
                        <div class="card-body">
                            <table id="AgencyList" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Id</th>
                                        <th>Agency Name</th>
                                        <th>Description</th>
                                          <th>Caste</th>
                                           <th>Is Active</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="tblBody">
                                    

                                </tbody>
                            </table>
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->


    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables  & Plugins -->
    <script src="assets/plugins/datatables/jquery.dataTables.min.js"></script>
    <script src="assets/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="assets/plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
    <script src="assets/plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
    <script src="assets/plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
    <script src="assets/plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
    <script src="assets/plugins/jszip/jszip.min.js"></script>
    <script src="assets/plugins/pdfmake/pdfmake.min.js"></script>
    <script src="assets/plugins/pdfmake/vfs_fonts.js"></script>
    <script src="assets/plugins/datatables-buttons/js/buttons.html5.min.js"></script>
    <script src="assets/plugins/datatables-buttons/js/buttons.print.min.js"></script>
    <script src="assets/plugins/datatables-buttons/js/buttons.colVis.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
     <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <!-- Page specific script -->
    <script>
        $(function () {

            LoadAgencyList();




           
        });
        function LoadAgencyList() {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Agency",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Name = value.Name;
                        var Description = value.Description;
                        var Cast = value.Cast;
                        var IsActive = value.IsActive;

                        var Active_HTML = "";
                        if (IsActive == 0) {
                            Active_HTML = '<span class="SPAN_Active_No">No</span>';
                        }
                        else {
                            Active_HTML = '<span class="SPAN_Active_Yes">Yes</span>';
                        }


                        var html = '<tr>' +
                            '    <td>' + Id+'</td>' +
                            '    <td>' + Name+'</td>' +
                            '    <td>' + Description+'</td>' +
                            '    <td>' + Cast + '</td>' +
                            '    <td style="text-align:center;vertical-align:middle;">' + Active_HTML + '</td>' +
                            '    <td>' +
                            '        <div class="btn-group">' +
                            '            <button type="button" class="btn btn-success">Action</button>' +
                            '            <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-expanded="false">' +
                            '                <span class="sr-only">Toggle Dropdown</span>' +
                            '            </button>' +
                            '            <div class="dropdown-menu" role="menu" style="">' +
                            '                <a class="dropdown-item" href="Agency_Edit.aspx?Id=' + Id + '">Edit</a>' +
                            '                <a class="dropdown-item" data-id="' + Id + '"  onclick="Delete(this);" >Delete</a>' +
                        '            </div>' +
                            '        </div>' +
                            '    </td>' +
                            '</tr>';
                        $('#tblBody').append(html);
                    });


                },
                error: function (error) {


                   // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                $("#AgencyList").DataTable({
                    "responsive": true, "lengthChange": false, "autoWidth": false,
                    "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"]
                }).buttons().container().appendTo('#example1_wrapper .col-md-6:eq(0)');
               

            });
        }

        function Delete(This_Val) {
            $.ajax({
                type: "POST", // or "GET" depending on your web method
                url: "WebService.asmx/Delete_From_tbl_Agency",
                data: JSON.stringify({ Id: $(This_Val).attr("data-Id")}), // If you have parameters
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (response) {

                    // Handle the successful response from the web method
                    console.log(response.d); // "d" is the default property name for the response
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Agency Successfully Deleted !',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked

                            location.href = 'Agency_List.aspx';
                            // Your code here
                        }
                    });

                },
                error: function (xhr, status, error) {
                    // Handle the error response

                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                },
                done: function (response) {
                    // Handle the error response
                    alert(response);

                }
            });
        }
</script>
</asp:Content>

