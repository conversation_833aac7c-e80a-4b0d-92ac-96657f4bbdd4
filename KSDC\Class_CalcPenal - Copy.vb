﻿Imports Microsoft.ApplicationBlocks.Data

Public Class Class_CalcPenal
    Function CalcPenal(ByVal Curdate As Date, ByVal loanno As String)
        Dim Rcvamt, Dmdamt, instamt, Dbtamt, pnl, bamt, fr As Double
        Dim fdate, duedt, dte, lduedate As Date
        Dim dt1 As DataTable
        Dim m, i, n, intgr
        Dim dr As DataRow
        Dim offid
        pnl = 0 : n = 0 : m = 0
        offid = "" '"0," & Session("officeid")
        Dim PeriodExt As Integer
        Dim rvcdflag As <PERSON>olean
        Dim rvcddate As Date
        Dim strconn As String
        Dim TEST As String
        Dim txtr As String
        Dim ds As DataSet
        Dim pnlrate As Double
        txtr = ""
        strconn = ""
        strconn = "data source=DESKTOP-JP80BH3\SQLEXPRESS;initial catalog=ksdc;integrated security=True;Connect Timeout=30;"
        '------------------------------------------------------
        ds = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select * from tbl_loanreg where int_loanno='" & loanno & "'")
        If ds.Tables(0).Rows.Count > 0 Then
            dr = ds.Tables(0).Rows(0)
            fdate = dr("dt_first_due_date")
            instamt = dr("mny_repayamt")
            lduedate = DateAdd(DateInterval.Month, dr("int_repay_inst") - 1, fdate)
            PeriodExt = dr("int_repay_inst_Ext")
            If dr("int_rvcd") = 1 Then
                rvcdflag = True
                rvcddate = dr("dt_rvcd_date")
            Else
                rvcdflag = False
            End If
        End If

        If txtr = "" Then
            If rvcdflag = False Then
                ' TEST = "Select sum(int_amt),sum(int_penal_r),sum(int_bank_charge_r) from tbl_loantrans where int_loanno='" & loanno & "' and chr_transtype='Direct' and int_type in (1,10) and  dt_transaction  <='" & (Curdate) & "'"
                'ds = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select sum(int_amt),sum(int_penal_r),sum(int_bank_charge_r) from tbl_loantrans where int_loanno='" & loanno & "' and chr_transtype='Receipt' and int_type in (1,2,3,6,15,22) and dt_transaction  <='" & (Curdate) & "'")
                ds = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select sum(int_amt),sum(int_penal_r),sum(int_bank_charge_r) from tbl_loantrans where int_loanno='" & loanno & "' and chr_transtype!='LOAN DISBURSED' and int_type in (1,10) and  dt_transaction  <='" & (Curdate.ToString("yyyy-MM-dd HH:mm:ss.fff")) & "'")
            Else
                ds = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select sum(int_amt),sum(int_penal_r),sum(int_bank_charge_r) from tbl_loantrans where int_loanno='" & loanno & "' and chr_transtype='Direct' and   dt_transaction  <='" & (Curdate.ToString("yyyy-MM-dd HH:mm:ss.fff")) & "' and dt_transaction >= '" & rvcddate & "'")
                'ds = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "Select sum(int_amt),sum(int_penal_r),sum(int_bank_charge_r) from tbl_loantrans where int_loanno='" & loanno & "' and chr_transtype='Receipt' and int_type in (1,2,3,6,15,20,22) and dt_transaction  <='" & (Curdate) & "' and dt_transaction >= '" & rvcddate & "' and int_type <> 19")

            End If
            If ds.Tables.Count > 0 Then
                If ds.Tables(0).Rows.Count > 0 Then
                    For Each dr In ds.Tables(0).Rows
                        If Not IsDBNull(dr(0)) Then Rcvamt = dr(0) - (dr(1) + dr(2))
                        txtr = Rcvamt
                    Next
                End If
            End If

        End If
        '--------------------------

        If Month(Curdate) = 2 And Day(fdate) > 28 Then
            duedt = DateSerial(Year(Curdate), Month(Curdate), 28)
        ElseIf (Month(Curdate) = 4 Or Month(Curdate) = 6 Or Month(Curdate) = 9 Or Month(Curdate) = 11) And Day(fdate) > 30 Then
            duedt = DateSerial(Year(Curdate), Month(Curdate), 30)
        Else
            duedt = DateSerial(Year(Curdate), Month(Curdate), Day(fdate))
        End If
        'duedt = DateSerial(Year(Curdate), Month(Curdate), Day(fdate))

        If duedt > Curdate Then duedt = DateAdd(DateInterval.Month, -1, duedt)
        If duedt > lduedate Then duedt = lduedate

        Dmdamt = (DateDiff(DateInterval.Month, fdate, duedt) + 1) * instamt
        Dmdamt = ((DateDiff(DateInterval.Month, fdate, duedt) + 1) - PeriodExt) * instamt
        Rcvamt = CDbl(txtr)
        If Rcvamt >= Dmdamt Then Exit Function
        'If Rcvamt - Dbtamt >= Dmdamt Then Exit Function

        '--------------------------
        'ds = SqlHelper.ExecuteDataset(strconn, CommandType.Text, "select * FROM tbl_holiday WHERE dte_date>='" & fdate & "' and dte_date<='" & Curdate & "' and vchr_offid in (" & offid & ")")
        'Dim dColumn As DataColumn = ds.Tables(0).Columns("dte_date")
        'ds.Tables(0).PrimaryKey = New DataColumn() {dColumn}
        '--------------------------
        'bamt = Dmdamt - (Rcvamt - Dbtamt)
        bamt = Dmdamt - (Rcvamt)
        intgr = Decimal.Truncate(bamt / instamt)
        fr = bamt Mod instamt
        If intgr > 0 And fr > 0 Then m = intgr + 1
        If intgr > 0 And fr = 0 Then m = intgr
        If intgr <= 0 And fr > 0 Then m = 1

        Dim ary(m - 1, 2)
        Dim j As Integer : j = 0
        '--------------------------
        If fr > 0 Then : i = 1 : Else : i = 0 : End If
        n = 0
        If intgr > 0 Then
            dte = duedt
            Do Until m - 1 < i
                If n = 1 Then dte = DateAdd("m", DateDiff(DateInterval.Month, fdate, dte) - 1, fdate)
                dte = DateAdd(DateInterval.Month, -(j), duedt)
                n = 0
jmp2:          ' Dim dr2 As DataRow = ds.Tables(0).Rows.Find(dte)
                ' If Not dr2 Is Nothing Then
                '     If Month(DateAdd("d", 1, dte)) > Month(dte) Then n = 1
                '     dte = DateAdd("d", 1, dte)
                '     GoTo jmp2
                ' End If
                pnl = pnl + (instamt * DateDiff(DateInterval.Day, dte, Curdate) * pnlrate / 36500)
                ary(m - 1, 0) = dte
                ary(m - 1, 1) = instamt
                ary(m - 1, 2) = (instamt * DateDiff(DateInterval.Day, dte, Curdate) * pnlrate / 36500)
                m = m - 1
                j = j + 1
            Loop
        End If
        '--------------------------

        If fr > 0 Then
            dte = DateAdd(DateInterval.Month, -(intgr), duedt)
jmp1:       Dim dr1 As DataRow = ds.Tables(0).Rows.Find(dte)
            If Not dr1 Is Nothing Then
                dte = DateAdd("d", 1, dte)
                n = 1
                GoTo jmp1
            End If
            pnl = pnl + (fr * DateDiff(DateInterval.Day, dte, Curdate) * pnlrate / 36500)
            ary(0, 0) = dte
            ary(0, 1) = fr
            ary(0, 2) = (fr * DateDiff(DateInterval.Day, dte, Curdate) * pnlrate / 36500)

        End If
        '--------------------------
        'Return ary
        Return pnl
    End Function

End Class
