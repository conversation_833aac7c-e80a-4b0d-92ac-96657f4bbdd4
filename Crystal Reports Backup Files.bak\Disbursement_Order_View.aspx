﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Disbursement_Order_View.aspx.cs" Inherits="KSDCSCST_Portal.Disbursement_Order_View" %>




<asp:Content ID="Content2" ContentPlaceHolderID="MainContent" runat="server">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">

    <style>
        input:disabled {
            border: 0;
        }

        label {
            margin: 0;
        }
    </style>

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Proceedings</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">Proceedings</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                             

                                <div class="row" style="margin-top: 20px;">
                                    <div class="col-sm-12">
                                        <div class="Sub_Header">Proceedings</div>

                                        <table style="width: 100%;">
                                            <tr>
                                                <td style="width: 49%; text-align: right;">Date</td>
                                                <td style="width: 2%; text-align: center;">:</td>
                                                <td style="width: 49%">
                                                    <label id="txtOrder_Date"></label>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="width: 49%; text-align: right;">Reg.No : 
                                                </td>
                                                <td style="width: 2%; text-align: center;">:</td>
                                                <td style="width: 49%">
                                                    <label id="txtReg_No"></label>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="width: 49%; text-align: right;">Applicant Name</td>
                                                <td style="width: 2%; text-align: center;">:</td>
                                                <td style="width: 49%">
                                                    <label id="txtApplicant_Name"></label>
                                                    <input type="hidden" id="RegNo" name="RegNo" value="">
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="width: 49%; text-align: right;">Loan Scheme</td>
                                                <td style="width: 2%; text-align: center;">:</td>
                                                <td style="width: 49%">
                                                    <label id="txtLoan_Scheme"></label>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="width: 49%; text-align: right;">Loan Fund</td>
                                                <td style="width: 2%; text-align: center;">:</td>
                                                <td style="width: 49%">
                                                    <label id="txtLoan_Fund"></label>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="width: 49%; text-align: right;">Max-Loan Amount</td>
                                                <td style="width: 2%; text-align: center;">:</td>
                                                <td style="width: 49%">
                                                    <label id="txtloan_Amount"></label>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="width: 49%; text-align: right;">Requested-Loan Amount</td>
                                                <td style="width: 2%; text-align: center;">:</td>
                                                <td style="width: 49%">
                                                    <label id="txtRequested_Loan_Amount"></label>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="width: 49%; text-align: right;">Sanctioned-Loan Amount</td>
                                                <td style="width: 2%; text-align: center;">:</td>
                                                <td style="width: 49%">
                                                    <label id="txtSanctioned_Loan_Amount"></label>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="width: 49%; text-align: right;">No. of Instalment</td>
                                                <td style="width: 2%; text-align: center;">:</td>
                                                <td style="width: 49%">
                                                    <label id="txtNo_Of_Instalment"></label>
                                                </td>
                                            </tr>

                                            <tr>

                                                <td colspan="3">
                                                    <table id="TABLE_Installment_Amount" style="text-align: center; float: right; border: 1px solid;">
                                                    </table>
                                                </td>

                                            </tr>
                                            <tr>
                                                <td style="width: 49%; text-align: right;">Period</td>
                                                <td style="width: 2%; text-align: center;">:</td>
                                                <td style="width: 49%">
                                                    <label id="txtPeriod"></label>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="width: 49%; text-align: right;">EMI</td>
                                                <td style="width: 2%; text-align: center;">:</td>
                                                <td style="width: 49%">
                                                    <label id="txtEMI"></label>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="width: 49%; text-align: right;">Interest</td>
                                                <td style="width: 2%; text-align: center;">:</td>
                                                <td style="width: 49%">
                                                    <label id="txtInterest"></label>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="width: 49%; text-align: right;">Penal Interest </td>
                                                <td style="width: 2%; text-align: center;">:</td>
                                                <td style="width: 49%">
                                                    <label id="txtPenal_Interest"></label>
                                                </td>
                                            </tr>

                                        </table>



                                    </div>
                                </div>


                                <div class="row" style="margin-top: 20px;display:none; ">
                                    <div class="col-sm-12">
                                        <div class="Sub_Header">Remittance</div>
                                        <table style="width: 100%;">


                                            <tr>
                                                <td style="width: 49%; text-align: right;">Processing Fee</td>
                                                <td style="width: 2%; text-align: center;">:</td>
                                                <td style="width: 49%">
                                                    <label id="txtProcessing_Fee">0.00</label></td>

                                            </tr>

                                            <tr>
                                                <td style="width: 49%; text-align: right;">LDRF</td>
                                                <td style="width: 2%; text-align: center;">:</td>
                                                <td style="width: 49%">
                                                    <label id="txtLDRF">0.00</label></td>
                                            </tr>
                                            <tr>
                                                <td style="width: 49%; text-align: right;">Legal Fee</td>
                                                <td style="width: 2%; text-align: center;">:</td>
                                                <td style="width: 49%">
                                                    <label id="txtLegal_Fee">0.00</label></td>
                                            </tr>
                                            <tr>
                                                <td style="width: 49%; text-align: right;">Land Verification Fee</td>
                                                <td style="width: 2%; text-align: center;">:</td>
                                                <td style="width: 49%">
                                                    <label id="txtLand_Verification_Fee">0.00</label></td>
                                            </tr>
                                            <tr>
                                                <td style="width: 49%; text-align: right;">Postage Fee</td>
                                                <td style="width: 2%; text-align: center;">:</td>
                                                <td style="width: 49%">
                                                    <label id="txtPostage_Fee">0.00</label></td>
                                            </tr>
                                            <tr>
                                                <td style="width: 49%; text-align: right;">Others</td>
                                                <td style="width: 2%; text-align: center;">:</td>
                                                <td style="width: 49%">
                                                    <label id="txtOthers">0.00</label></td>
                                            </tr>
                                            <tr>
                                                <td style="width: 49%; text-align: right;">TOTAL</td>
                                                <td style="width: 2%; text-align: center;">:</td>
                                                <td style="width: 49%">
                                                    <label id="txtTOTAL">0.00</label></td>
                                            </tr>


                                        </table>



                                    </div>
                                </div>






                                <div class="form-group row">
                                    <div class="col-sm-12 text-right">

                                        <a href="Disbursement_Order_List.aspx" class="btn btn-dark">Back</a>
                                        <a onclick="Save();" class="btn btn-success">Approve</a>
                                    </div>
                                </div>

                            </div>
                            <!-- /.tab-pane -->
                        </div>
                        <!-- /.tab-content -->
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->



    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 -->
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <!-- Bootstrap4 Duallistbox -->
    <script src="assets/plugins/bootstrap4-duallistbox/jquery.bootstrap-duallistbox.min.js"></script>
    <!-- InputMask -->
    <script src="assets/plugins/moment/moment.min.js"></script>
    <script src="assets/plugins/inputmask/jquery.inputmask.min.js"></script>
    <!-- date-range-picker -->
    <script src="assets/plugins/daterangepicker/daterangepicker.js"></script>
    <!-- bootstrap color picker -->
    <script src="assets/plugins/bootstrap-colorpicker/js/bootstrap-colorpicker.min.js"></script>
    <!-- Tempusdominus Bootstrap 4 -->
    <script src="assets/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js"></script>
    <!-- BS-Stepper -->
    <script src="assets/plugins/bs-stepper/js/bs-stepper.min.js"></script>
    <!-- dropzonejs -->
    <script src="assets/plugins/dropzone/min/dropzone.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script src="assets/js/jquery.dateandtime.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script>

        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }
        function getFormattedDate(date) {
            date = date.split('/')[2] + "-" + date.split('/')[1] + "-" + date.split('/')[0];
            return date;
        }

        $(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }

        })
        $(document).ready(function () {

            // Get the current date
            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtOrder_Date').text(formattedDate);


            Load_All_Application_Issue_By_Id(getParameterByName("Id"));
        });

        var Caste_Id;
        var SubCaste_Id;
        var Village_Id;
        var SubDistrict_Id;
        var Scheme_Id;
        var RegNo;
        var LoanNo;
        var LoanAppId;

        function Load_All_Application_Issue_By_Id(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Id: Id }), // If you have parameters
                url: "WebService.asmx/Select_tbl_LoanApp_By_LoanAppId",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        LoanAppId = value.int_loanappid;
                        RegNo = value.vchr_appreceivregno;
                        LoanNo = value.int_loanno;
                        $("#txtReg_No").text(value.vchr_appreceivregno);
                        $("#txtApplicant_Name").text(value.vchr_applname);
                        Caste_Id = value.vchr_caste;
                        SubCaste_Id = value.SubCast;
                        Village_Id = value.vchr_village;
                        SubDistrict_Id = value.vchr_subdistrict;
                        Scheme_Id = value.int_schemeid;
                        $("#txtLoan_Fund").text(value.vchr_fund);
                        $("#txtRequested_Loan_Amount").text(value.int_loanamt_req);
                        $("#txtSanctioned_Loan_Amount").text(value.int_amtsanction);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Schemes_By_Id(Scheme_Id);
                Load_All_tbl_loanreg_By_RegNo_Or_LoanNo(RegNo, LoanNo);
            });
        }
        function Load_All_Schemes_By_Id(Scheme_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Schemes_By_ID",
                data: JSON.stringify({ Id: Scheme_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtLoan_Scheme').val('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Scheme = value.Scheme;
                        $('#txtLoan_Scheme').text(Scheme);
                        $('#txtloan_Amount').text(value.Loan_Amount);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                //alert(Present_District);
            });

        }

        function Load_All_tbl_loanreg_By_RegNo_Or_LoanNo(RegNo, LoanNo) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_tbl_loanreg_By_RegNo_Or_LoanNo",
                data: JSON.stringify({ appreceivr_RegNo: RegNo, loanNo: LoanNo }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                  
                    $('#txtNo_Of_Instalment').text('');
                    $('#txtEMI').text('');
                    $('#txtPeriod').text('');
                    $('#txtInterest').text('');
                    $('#txtPenal_Interest').text('');
                    $.each(data.d, function (key, value) {
                       
                        $('#txtNo_Of_Instalment').text(value.int_disb_inst);
                        $('#txtEMI').text(value.mny_repayamt);
                        $('#txtPeriod').text(value.int_repay_inst);
                        $('#txtInterest').text(value.int_rate_int);
                        $('#txtPenal_Interest').text(value.int_rate_penal);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_All_Disbursement_By_RegNo(RegNo);
            });
        }

        function Load_All_Disbursement_By_RegNo(App_Reg_No) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_tbl_disbursement_By_RegNo",
                data: JSON.stringify({ appreceivr_RegNo: App_Reg_No, loanNo: "" }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    var i = 0;
                    $("#TR_Installment_Amount").empty();
                    $.each(data.d, function (key, value) {

                        i = i + 1;



                        $("#TABLE_Installment_Amount").append('<tr><td style="width: 49%; text-align: right;">Installment Amount ' + i + '</td>'
                            + '<td style="width: 2%; text-align: center;">:</td>'
                            + '<td style="width: 49%">' + value.mny_InstallAmt + '</td></tr>');




                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }

        function Save() {

            var LoanAppId = getParameterByName("Id");
            var RegNo = $("#txtReg_No");

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Update_Loan_Order_Status",
                data: JSON.stringify({ int_loanappid: LoanAppId, status: "Order Executed", regNo: RegNo.text() }),
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Contact your administrator for more information, Email Id: <EMAIL>',
                    })
                }
            }).done(function () {
                Swal.fire({
                    icon: 'success',
                    title: 'Message',
                    text: 'Proceedings Successfully Approved !',
                }).then((result) => {
                    if (result.isConfirmed) {
                        // OK button clicked
                        location.href = 'Disbursement_Order_List.aspx';
                        // Your code here
                    }
                });
            });

        }
    </script>

</asp:Content>



