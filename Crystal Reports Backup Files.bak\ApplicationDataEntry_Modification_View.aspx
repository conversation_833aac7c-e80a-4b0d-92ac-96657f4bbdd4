﻿<%@ Page Title="Application Data Entry Modification | View" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="ApplicationDataEntry_Modification_View.aspx.cs" Inherits="KSDCSCST_Portal.ApplicationDataEntry_Modification_View" %>



<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- daterange picker -->
    <link rel="stylesheet" href="assets/plugins/daterangepicker/daterangepicker.css">
    <!-- iCheck for checkboxes and radio inputs -->
    <link rel="stylesheet" href="assets/plugins/icheck-bootstrap/icheck-bootstrap.min.css">
    <!-- Bootstrap Color Picker -->
    <link rel="stylesheet" href="assets/plugins/bootstrap-colorpicker/css/bootstrap-colorpicker.min.css">
    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet" href="assets/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css">
    <!-- Select2 -->
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">
    <!-- Bootstrap4 Duallistbox -->
    <link rel="stylesheet" href="assets/plugins/bootstrap4-duallistbox/bootstrap-duallistbox.min.css">
    <!-- BS Stepper -->
    <link rel="stylesheet" href="assets/plugins/bs-stepper/css/bs-stepper.min.css">
    <!-- dropzonejs -->
    <link rel="stylesheet" href="assets/plugins/dropzone/min/dropzone.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">
   <style>
     input:disabled {
         border: 0;
     }
 </style>
 <style>
     /* Styles for the search input */
     txtPostOffices {
         width: 300px;
         padding: 10px;
         font-size: 16px;
     }

     /* Styles for the search results container */
     #search-results {
         list-style: none;
         padding: 0;
         margin-top: 5px;
         border: 1px solid #ccc;
         max-height: 150px;
         overflow-y: auto;
     }

         /* Styles for individual search result items */
         #search-results li {
             padding: 5px;
             cursor: pointer;
         }

             /* Highlight the selected result */
             #search-results li:hover {
                 background-color: #f2f2f2;
             }

     #search-results-ifsc {
         list-style: none;
         padding: 0;
         margin-top: 5px;
         border: 1px solid #ccc;
         max-height: 150px;
         overflow-y: auto;
     }

         /* Styles for individual search result items */
         #search-results-ifsc li {
             padding: 5px;
             cursor: pointer;
         }

             /* Highlight the selected result */
             #search-results-ifsc li:hover {
                 background-color: #f2f2f2;
             }



     #search-results_Prathyasa {
         list-style: none;
         padding: 0;
         margin-top: 5px;
         border: 1px solid #ccc;
         max-height: 150px;
         overflow-y: auto;
     }

         /* Styles for individual search result items */
         #search-results_Prathyasa li {
             padding: 5px;
             cursor: pointer;
         }

             /* Highlight the selected result */
             #search-results_Prathyasa li:hover {
                 background-color: #f2f2f2;
             }
 </style>


    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Application Modification View</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">Application Modification View</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">
                                    <div class="Sub_Header_Status row">

                                        <div class="col-sm-12">
                                            <i class="fa fa-battery-half" aria-hidden="true"></i><span class="SPAN_APP_Status_Text">Application Status</span>
                                            <label class="LABEL_APP_Status"></label>
                                        </div>

                                    </div>
                                    <div class="Sub_Header">Applicant Details</div>
                                    <div class="form-group row">
                                        <label for="inputName" class="col-sm-3 col-form-label">Today's Date *</label>
                                        <div class="col-sm-9">
                                            <input type="text" disabled="disabled" class="form-control" id="txtApplication_Date" placeholder="Today's Date(DD/MM/YYYY)">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName" class="col-sm-3 col-form-label">Application Register No</label>
                                        <div class="col-sm-9">
                                            <input disabled="disabled" type="text" class="form-control" id="txtApplicationRegNo" value="TVM/5678" placeholder="Date(DD/MM/YYYY)">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Name of Applicant *</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtApplicant_Name" placeholder="Name of Applicant">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Aadhar Number*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtAadharNumber" placeholder="Aadhar Number">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Loan Scheme *</label>
                                        <div class="col-sm-9">
                                            <select id="dropScheme" class="form-control">
                                                <option>Select</option>

                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row" id="DIV_Prathyasa" style="display: none;">

                                        <label class="col-sm-3 col-form-label">Select Existing Prathyasa Loan No *</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txt_Prathyasa" placeholder="Search...">
                                            <ul id="search-results_Prathyasa"></ul>
                                        </div>

                                    </div>
                                    <div class="form-group row" style="margin-bottom: 10px !important;">

                                        <label class="col-sm-3 col-form-label"></label>
                                        <div class="col-sm-9" style="border: 1px solid #dadada; float: right !important; max-width: 73%; border-radius: 5px; margin-left: 8px;">
                                            <label class="col-sm-3 col-form-label">Scheme Details</label>
                                            <table class="tbl_Scheme_Details">
                                                <tr>
                                                    <td><span>Annual Income Rural Max Amount</span>

                                                    </td>
                                                    <td style="text-align: right;">
                                                        <span id="lblAnnual_Rural_Max">0</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><span>Annual Income Urban Max Amount</span>

                                                    </td>
                                                    <td style="text-align: right;">
                                                        <span id="lblAnnual_Urban_Max">0</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><span>Minimum Age</span>

                                                    </td>
                                                    <td style="text-align: right;">
                                                        <span id="lblMin_Age">0</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><span>Maximum Age</span>

                                                    </td>
                                                    <td style="text-align: right;">
                                                        <span id="lblMax_Age">0</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><span>Loan Amount</span>

                                                    </td>
                                                    <td style="text-align: right;">
                                                        <span id="lblLoan_Amount">0</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><span>Loan Period</span>

                                                    </td>
                                                    <td style="text-align: right;">
                                                        <span id="lblLoan_Period">0</span>
                                                    </td>
                                                </tr>

                                            </table>

                                        </div>
                                    </div>
                                </div>
                                <div class="Sub_Header">Address Details</div>

                                <div class="form-group row">
                                    <label for="inputName2" class="col-sm-3 col-form-label">House Name/No *</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="txtHouseName" placeholder="House Name/No">
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="inputName2" class="col-sm-3 col-form-label">Lane1 *</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="txtPlace1" placeholder="Lane1">
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="inputName2" class="col-sm-3 col-form-label">Lane2 *</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="txtPlace2" placeholder="Lane2">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="inputEmail" class="col-sm-3 col-form-label">District *</label>
                                    <div class="col-sm-9">
                                        <select id="dropDistrict" disabled="disabled" class="form-control">
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="inputEmail" class="col-sm-3 col-form-label">Branch *</label>
                                    <div class="col-sm-9">
                                        <select id="dropSubDistrict" disabled="disabled" class="form-control">
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="inputName2" class="col-sm-3 col-form-label">Pincode *</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="txtPincode" placeholder="Search...">
                                        <ul id="search-results"></ul>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="inputEmail" class="col-sm-3 col-form-label">Post Office *</label>
                                    <div class="col-sm-9">
                                        <input type="text" disabled="disabled" class="form-control" id="txtPostOffices" placeholder="Post Office">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="inputName2" class="col-sm-3 col-form-label">Phone No *</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="txtPhoneNo" placeholder="Phone No">
                                        <span style="font-style: italic; color: red; font-size: 14px; height: 35px; display: block;">seperate numbers by comma ","</span>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="inputName2" class="col-sm-3 col-form-label">Landmark</label>
                                    <div class="col-sm-9">
                                        <textarea class="form-control" maxlength="200" id="txtLandMark" placeholder="Landmark"></textarea>
                                        <span class="validation_message">Maximum text length is 200</span>
                                    </div>
                                </div>


                                <div class="Sub_Header">Personal Details</div>

                                <div class="form-group row">
                                    <label for="inputEmail" class="col-sm-3 col-form-label">Name of Father *</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="txtFatherName" placeholder="Name of Father">
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="inputEmail" class="col-sm-3 col-form-label">Name of Spouse</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="txtNameOfSpouse" placeholder="Name of Spouse">
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="inputEmail" class="col-sm-3 col-form-label">Caste *</label>
                                    <div class="col-sm-9">
                                        <select id="dropCast" class="form-control">
                                            <option>Select</option>

                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="inputEmail" class="col-sm-3 col-form-label">Sub Caste *</label>
                                    <div class="col-sm-9">
                                        <select id="dropSubCast" class="form-control">
                                            <option>Select</option>


                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="inputEmail" class="col-sm-3 col-form-label">Sex *</label>
                                    <div class="col-sm-9">
                                        <select id="dropSex" class="form-control">
                                            <option value="0">Select Sex</option>
                                            <option value="Male">Male</option>
                                            <option value="Female">Female</option>
                                            <option value="Other">Other</option>

                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-3 col-form-label">Date of Birth *</label>
                                    <div class="col-sm-9">
                                        <input class="dateandtime form-control" id="txtDOB" type="text" name="machin-b" value="">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-3 col-form-label">Age *</label>
                                    <div class="col-sm-9">
                                        <input type="text" disabled="disabled" class="form-control" id="txtAge" value="0" placeholder="Age">
                                    </div>
                                </div>
                                <div id="DIV_Retirement" class="form-group row" style="display: none;">
                                    <label class="col-sm-3 col-form-label">Retirement Date *</label>
                                    <div class="col-sm-9">
                                        <input class="dateandtime form-control" id="txtRetirement" type="text" name="machin-b" value="">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="inputEmail" class="col-sm-3 col-form-label">Village *</label>
                                    <div class="col-sm-9">
                                        <select id="dropVillage" class="form-control">
                                            <option value="0">Select Village</option>


                                        </select>
                                    </div>
                                </div>


                                <div class="form-group row">
                                    <label for="inputEmail" class="col-sm-3 col-form-label">Taluk *</label>
                                    <div class="col-sm-9">
                                        <select id="dropTaluk" disabled="disabled" class="form-control">
                                        </select>
                                    </div>
                                </div>




                                <div class="form-group row">
                                    <label for="inputName2" class="col-sm-3 col-form-label">Local Body *</label>
                                    <div class="col-sm-9">
                                        <select id="dropLocalBody" class="form-control">
                                            <option value="0">Select</option>
                                            <option value="1">Panchayat</option>
                                            <option value="2">Municipality</option>
                                            <option value="3">Corporation</option>


                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row" id="DIV_Block" style="display: none;">
                                    <label for="inputEmail" class="col-sm-3 col-form-label">Block *</label>
                                    <div class="col-sm-9">
                                        <select id="dropBlock" class="form-control">
                                            <option value="0">Select Block</option>


                                        </select>
                                    </div>
                                </div>
                                <div id="DIV_Panchayat" style="display: none;" class="form-group row">
                                    <label class="col-sm-3 col-form-label">Panchayat *</label>
                                    <div class="col-sm-9">
                                        <select id="dropPanchayat" class="form-control">
                                            <option value="0">Select Panchayat</option>


                                        </select>
                                    </div>
                                </div>
                                <div id="DIV_Municipality" style="display: none;" class="form-group row">
                                    <label class="col-sm-3 col-form-label">Municipality *</label>
                                    <div class="col-sm-9">
                                        <select id="dropMunicipality" class="form-control">
                                            <option value="0">Select Municipality</option>


                                        </select>
                                    </div>
                                </div>
                                <div id="DIV_Corporation" style="display: none;" class="form-group row">
                                    <label class="col-sm-3 col-form-label">Corporation *</label>
                                    <div class="col-sm-9">
                                        <select id="dropCorporation" class="form-control">
                                            <option value="0">Select Corporation</option>


                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="inputEmail" class="col-sm-3 col-form-label">Ward *</label>
                                    <div class="col-sm-9">
                                        <input type="number" class="form-control" id="txtWard" placeholder="Enter Ward Number">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-3 col-form-label">Lok-Sabha *</label>
                                    <div class="col-sm-9">
                                        <select id="dropLokSabha" class="form-control">
                                            <option value="0">Select Lok-Sabha</option>


                                        </select>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label class="col-sm-3 col-form-label">Assembly *</label>
                                    <div class="col-sm-9">
                                        <select id="dropConstituency" class="form-control">
                                            <option value="0">Select Assembly</option>


                                        </select>
                                    </div>
                                </div>


                                <div class="form-group row" id="DIV_Local_Body_Input" style="display: none;">
                                    <label id="lblLocal_Body" class="col-sm-3 col-form-label"></label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="txtLocal_Body" value="0" placeholder="">
                                    </div>
                                </div>






                                <div class="form-group row">
                                    <label for="inputName2" class="col-sm-3 col-form-label">Annual Income *</label>
                                    <div class="col-sm-9">
                                        <input type="number" class="form-control" id="txtAnnualIncome" value="0" placeholder="Annual Income">
                                    </div>
                                </div>



                                <div class="form-group row">
                                    <label for="inputName2" class="col-sm-3 col-form-label">Ration Card Number *</label>
                                    <div class="col-sm-9">
                                        <input type="number" class="form-control" id="txtRationCardNumber" placeholder="Ration Card Number">
                                    </div>
                                </div>

                                <div class="form-group row" style="display: none;">
                                    <label for="inputEmail" class="col-sm-3 col-form-label">If Other ID Card *</label>
                                    <div class="col-sm-9">
                                        <select id="dropOther" class="form-control">
                                            <option value="0">Select Other</option>


                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row" style="display: none;">
                                    <label for="inputName2" class="col-sm-3 col-form-label">ID Number *</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="txtIDNumber" placeholder="ID Number">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="inputName2" class="col-sm-3 col-form-label">Required Loan Amount *</label>
                                    <div class="col-sm-9">
                                        <input type="number" class="form-control" id="txtLoanAmount" placeholder="Required Amount for Loan">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="inputName2" class="col-sm-3 col-form-label">Sactioned Loan Amount *</label>
                                    <div class="col-sm-9">
                                        <input type="number" class="form-control" id="txtAmount_Saction" placeholder="Sanctioned Amount for Loan">
                                    </div>
                                </div>
                                <div class="Sub_Header">Bank Account Details</div>


                                <div class="form-group row">
                                    <label for="inputName" class="col-sm-3 col-form-label">IFSC Code*</label>
                                    <div class="col-sm-9">

                                        <input type="text" class="form-control" id="txtIFSC" placeholder="IFSC Code">
                                        <ul id="search-results-ifsc"></ul>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="inputName" class="col-sm-3 col-form-label">Bank Name*</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="txtBank_Name" placeholder="Bank Name">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="inputName" class="col-sm-3 col-form-label">Branch*</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="txtBranch" placeholder="Branch">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="inputName" class="col-sm-3 col-form-label">Bank Account No *</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="txtBank_Account_No" placeholder="Bank Account No">
                                    </div>
                                </div>



                                <div class="form-group row">
                                    <div class="col-sm-12 text-right">

                                        <a href="ApplicationDataEntry_List.aspx" class="btn btn-dark">Back</a>
                                        <a onclick="Save();" class="btn btn-success">Next</a>
                                    </div>
                                </div>
                                <input type="hidden" id="hdn_Retirement_Age" />
                            </div>
                            <!-- /.tab-pane -->
                        </div>
                        <!-- /.tab-content -->
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->

        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->


    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 -->
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <!-- Bootstrap4 Duallistbox -->
    <script src="assets/plugins/bootstrap4-duallistbox/jquery.bootstrap-duallistbox.min.js"></script>
    <!-- InputMask -->
    <script src="assets/plugins/moment/moment.min.js"></script>
    <script src="assets/plugins/inputmask/jquery.inputmask.min.js"></script>
    <!-- date-range-picker -->
    <script src="assets/plugins/daterangepicker/daterangepicker.js"></script>
    <!-- bootstrap color picker -->
    <script src="assets/plugins/bootstrap-colorpicker/js/bootstrap-colorpicker.min.js"></script>
    <!-- Tempusdominus Bootstrap 4 -->
    <script src="assets/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js"></script>
    <!-- BS-Stepper -->
    <script src="assets/plugins/bs-stepper/js/bs-stepper.min.js"></script>
    <!-- dropzonejs -->
    <script src="assets/plugins/dropzone/min/dropzone.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script src="assets/js/jquery.dateandtime.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>


    <script>

        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;
        const $searchInput = $('#txtPincode');
        const $searchInput_IFSC = $('#txtIFSC');
        const $searchInput_Prathyasa = $('#txt_Prathyasa');

        var Rural_Max = "";
        var Urban_Max = "";
        var Min_Age = "";
        var Max_Age = "";
        var Loan_Amount = "";
        var Loan_Period = "";




        // Results container element
        const $searchResults = $('#search-results');
        const $searchResults_IFSC = $('#search-results-ifsc');
        const $searchResults_Prathyasa = $('#search-results_Prathyasa');

        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }
        $(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtApplication_Date').val(formattedDate);


            //Date picker
            //$('#txtDOB').datetimepicker({
            //    format: 'DD/MM/YYYY',
            //    allowInputToggle: false
            //});

            //$('#txtRetirement').datetimepicker({
            //    format: 'DD/MM/YYYY',
            //    allowInputToggle: false
            //});
            $('#txtDOB').dateAndTime();
            $('#txtRetirement').dateAndTime();




            // Load_All_Districts();

            Load_All_Schemes();
            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            $('#dropLokSabha').change(function () {

                Load_All_Constituency($(this).val());
            });


            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }



            //$(document).on("input", ".form-control", function () {
            //    // Get the current value of the input
            //    var currentValue = $(this).val();

            //    // Convert the value to propcase
            //    var propcaseValue = currentValue.replace(/\w\S*/g, function (txt) {
            //        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
            //    });

            //    // Update the input with the propcase value
            //    $(this).val(propcaseValue);
            //});


            $searchInput.on('input', function () {
                const query = $searchInput.val();
                fetchAutocompleteResults(query);
            });
            $searchInput_IFSC.on('input', function () {
                const query = $searchInput_IFSC.val();
                fetchAutocompleteResults_IFSC(query);
            });

            $searchInput_Prathyasa.on('input', function () {

                const query = $searchInput_Prathyasa.val();
                fetchAutocompleteResults_Prathyasa(query);
            });


            // Event handler for selecting a result
            $searchResults.on('click', 'li', function () {
                $("#txtPincode").val($(this).attr("data-pincode"));
                $("#txtPostOffices").val($(this).html());
                //  const selectedResult = $(this).text();
                //  $searchInput.val(selectedResult);
                $searchResults.hide();
            });
            $searchResults_IFSC.on('click', 'li', function () {
                $("#txtIFSC").val($(this).attr("data-vchr_IFSC"));
                $("#txtBank_Name").val($(this).attr("data-vchr_Bank"));
                $("#txtBranch").val($(this).attr("data-vchr_Branch"));
                //  const selectedResult = $(this).text();
                //  $searchInput.val(selectedResult);
                $searchResults_IFSC.hide();
            });

            $searchResults_Prathyasa.on('click', 'li', function () {
                $("#txt_Prathyasa").val($(this).attr("data_int_loanno"));
                //  const selectedResult = $(this).text();
                //  $searchInput.val(selectedResult);
                $searchResults_Prathyasa.hide();
            });


            $("#dropScheme").on("change", function () {
                Load_Scheme_Details();

                if ($("#dropScheme").val() == "53" || $("#dropScheme").val() == "53") {
                    $("#DIV_Prathyasa").css("display", "flex");
                }
                else {
                    $("#DIV_Prathyasa").css("display", "none");
                    $("#txt_Prathyasa").val("");
                }

            });

        })

        $(document).ready(function () {


            var previousValue = $("#txtDOB").val();
            var previousValue_Retirement = $("#txtRetirement").val(); // Initialize with the initial value

            $("#txtDOB").on("input", function () {
                var currentValue = $(this).val();

                // Check if the input value has changed
                if (currentValue !== previousValue) {
                    $("#txtAge").val(calculateAge(currentValue));
                    //alert("Date selected or changed:"+ currentValue);
                    // Add your custom code here to handle the selected date
                    previousValue = currentValue; // Update the previous value
                }
            });
            $("#txtRetirement").on("input", function () {
                var currentValue = $(this).val();

                // Check if the input value has changed
                if (currentValue !== previousValue_Retirement) {

                    $("#hdn_Retirement_Age").val(calculate_Retirement_Age(currentValue));
                    //alert("Date selected or changed:"+ currentValue);
                    // Add your custom code here to handle the selected date
                    previousValue_Retirement = currentValue; // Update the previous value
                }
            });


            $("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
            $("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");


            $("#dropLocalBody").on("change", function () {
                var selectedValue = $(this).val();

                $("#dropPanchayat").val("0");
                $("#dropMunicipality").val("0");
                $("#dropCorporation").val("0");

                if (selectedValue == "0") {
                    $("#DIV_Panchayat").css("display", "none");
                    $("#DIV_Municipality").css("display", "none");
                    $("#DIV_Corporation").css("display", "none");
                    $("#DIV_Block").css("display", "none");

                }
                else if (selectedValue == "1") {
                    $("#DIV_Panchayat").css("display", "flex");
                    $("#DIV_Municipality").css("display", "none");
                    $("#DIV_Corporation").css("display", "none");
                    $("#DIV_Block").css("display", "flex");

                }
                else if (selectedValue == "2") {
                    $("#DIV_Panchayat").css("display", "none");
                    $("#DIV_Municipality").css("display", "flex");
                    $("#DIV_Corporation").css("display", "none");
                    $("#DIV_Block").css("display", "none");
                }
                else if (selectedValue == "3") {
                    $("#DIV_Panchayat").css("display", "none");
                    $("#DIV_Municipality").css("display", "none");
                    $("#DIV_Corporation").css("display", "flex");
                    $("#DIV_Block").css("display", "none");
                }

            });



        });


        function Load_Scheme_Details() {
            Rural_Max = $("#dropScheme option:selected").attr("data-anninc_rural_max");
            Urban_Max = $("#dropScheme option:selected").attr("data-anninc_urban_max");
            Min_Age = $("#dropScheme option:selected").attr("data-min_age");
            Max_Age = $("#dropScheme option:selected").attr("data-max_age");
            Loan_Amount = $("#dropScheme option:selected").attr("data-loan_amount");
            Loan_Period = $("#dropScheme option:selected").attr("data-Loan_Period");

            var Loan_Period_Month_Year = Loan_Period + " - Months / " + parseInt(Loan_Period) / 12 + " - Years"
            $("#lblAnnual_Rural_Max").text(Rural_Max);
            $("#lblAnnual_Urban_Max").text(Urban_Max);
            $("#lblMin_Age").text(Min_Age);
            $("#lblMax_Age").text(Max_Age);
            $("#lblLoan_Amount").text(Loan_Amount);
            $("#lblLoan_Period").text(Loan_Period_Month_Year);

            if (Max_Age == "0") {
                $("#DIV_Retirement").css("display", "flex");
            }
            else {
                $("#DIV_Retirement").css("display", "none");
            }
        }

        function calculateAge(dateOfBirth) {
            // Parse the date of birth string into a Date object
            const dob = new Date(dateOfBirth);

            // Get the current date
            const currentDate = new Date();

            // Calculate the difference in milliseconds
            const ageInMilliseconds = currentDate - dob;

            // Convert the milliseconds to years
            const ageInYears = ageInMilliseconds / (365 * 24 * 60 * 60 * 1000);

            // Round down to the nearest whole number to get the age
            const age = Math.floor(ageInYears);

            return age;
        }

        function calculate_Retirement_Age(Retirement_Date) {
            // Parse the date of birth string into a Date object
            const dob = new Date(Retirement_Date);

            // Get the current date
            const currentDate = new Date();

            // Calculate the difference in milliseconds
            const ageInMilliseconds = dob - currentDate;

            // Convert the milliseconds to years
            const ageInYears = ageInMilliseconds / (365 * 24 * 60 * 60 * 1000);

            // Round down to the nearest whole number to get the age
            const age = Math.floor(ageInYears);

            return age;
        }


        function Load_All_Districts() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropDistrict').empty();
                    $('#dropDistrict').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var District_Name = value.District_Name;
                        var html = '<option value="' + Id + '">' + District_Name + '</option>';
                        if (Id != 15) {
                            $('#dropDistrict').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Schemes();


                //$('#dropDistrict').change(function () {
                //    $('#dropTaluk').empty();
                //    $('#dropVillage').empty();
                //    $('#dropBlock').empty();
                //    $('#dropPanjayath').empty();
                //    Load_All_Sub_Districts($(this).val());
                //    Load_All_Taluk($(this).val());
                //});

            });

        }
        function Load_All_Sub_Districts(District_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Districts",
                data: JSON.stringify({ District_Id: District_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubDistrict').empty();
                    $('#dropSubDistrict').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Sub_District_Name = value.Sub_District_Name;
                        var html = '<option value="' + Id + '">' + Sub_District_Name + '</option>';

                        $('#dropSubDistrict').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });

        }
        function fetchAutocompleteResults(query) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ pincode: query }), // If you have parameters
                url: "WebService.asmx/Select_All_PostOffices",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $searchResults.empty();
                    if (data.d.length > 0) {

                        $searchResults.show();
                    } else {
                        $searchResults.hide();
                        $("#txtPostOffices").val('');
                    }

                    $.each(data.d, function (key, value) {
                        $searchResults.append('<li data-pincode="' + value.Pincode + '">' + value.Post_office + '</li>');



                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });


        }
        function fetchAutocompleteResults_IFSC(query) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ IFSC: query }), // If you have parameters
                url: "WebService.asmx/Select_All_IFSC",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $searchResults_IFSC.empty();
                    if (data.d.length > 0) {

                        $searchResults_IFSC.show();
                    } else {
                        $searchResults_IFSC.hide();
                        $("#txtBank_Name").val('');
                        $("#txtBranch").val('');
                    }

                    $.each(data.d, function (key, value) {
                        $searchResults_IFSC.append('<li data-vchr_IFSC="' + value.vchr_IFSC + '" data-vchr_Bank="' + value.vchr_Bank + '" data-vchr_Branch="' + value.vchr_Branch + '">' + value.vchr_IFSC + '</li>');



                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });


        }

        function fetchAutocompleteResults_Prathyasa(query) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Loan_No: query }), // If you have parameters
                url: "WebService.asmx/Select_All_Prathyasa_Loans",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $searchResults_Prathyasa.empty();
                    if (data.d.length > 0) {

                        $searchResults_Prathyasa.show();
                    } else {
                        $searchResults_Prathyasa.hide();
                    }

                    $.each(data.d, function (key, value) {
                        $searchResults_Prathyasa.append('<li data_int_loanno="' + value.int_loanno + '" data_Prathyaksha_New_Loan_No="' + value.Prathyaksha_New_Loan_No + '" >' + value.int_loanno + '</li>');



                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });


        }


        function Load_All_Schemes() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Schemes",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropScheme').empty();
                    $('#dropScheme').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var IsActive = value.IsActive;
                        var Scheme = value.Scheme;
                        var Loan_Amount = value.Loan_Amount;
                        var Loan_Period = value.Loan_Period;
                        var html = '<option data-Loan_Period="' + Loan_Period + '" data-Loan_Amount="' + Loan_Amount + '" data-Min_Age="' + value.Min_Age + '" data-Max_Age="' + value.Max_Age + '" data-Anninc_Rural_Max="' + value.Anninc_Rural_Max + '" data-Anninc_Urban_Max="' + value.Anninc_Urban_Max + '" data-loan_amount="' + value.Loan_Amount + '"  value="' + Id + '">' + Scheme + '</option>';

                        if (IsActive == 1) {
                            $('#dropScheme').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Village();
                Load_All_Lok_Sabha();


            });

        }

        function Load_All_Cast() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Cast",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropCast').empty();
                    $('#dropCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var Cast = value.Cast;
                        var html = '<option value="' + Id + '">' + Cast + '</option>';

                        $('#dropCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $('#dropCast').change(function () {
                    Load_All_SubCast($(this).val());
                });


            });

        }

        var District_Id;
        var SubDistrict_id;
        var Cast_Id;
        var Sub_Cast_Id;

        var vchr_village;
        var vchr_taluk;

        var LokSabha_Id;
        var Assemply_Id;

        var Block_Id;
        var vchr_panchayat;


        function Load_All_Application_Issue_By_Id(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Id: Id }), // If you have parameters
                url: "WebService.asmx/Select_tbl_LoanApp_By_LoanAppId",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        $(".LABEL_APP_Status").text(value.chr_status);
                        var int_loanappid = value.int_loanappid;
                        District_Id = value.vchr_district;
                        SubDistrict_id = value.vchr_subdistrict;
                        var Name_Applicant = value.vchr_applname;
                        var Application_Submitted_Date = value.Submitted_Date_Formated;
                        var Scheme_Id = value.int_schemeid;

                        Cast_Id = value.vchr_caste;
                        Sub_Cast_Id = value.SubCast;

                        var vchr_appreceivregno = value.vchr_appreceivregno;
                        var vchr_hsename = value.vchr_hsename;
                        var vchr_place1 = value.vchr_place1;
                        var vchr_place2 = value.vchr_place2;
                        var vchr_post = value.vchr_post;
                        var int_pincode = value.int_pincode;
                        var vchr_phno = value.vchr_phno;
                        var vchr_sex = value.vchr_sex;
                        var dte_dob = value.dte_dob;
                        var Age = value.Age;
                        var int_anninc = value.int_anninc;
                        var AadharNumber = value.vchr_Aadhaar;


                        var LandMark = value.LandMark;
                        var vchr_fathername = value.vchr_fathername;
                        var vchr_spousename = value.vchr_spousename;
                        vchr_village = value.vchr_village;
                        vchr_taluk = value.vchr_taluk;
                        Block_Id = value.Block_Id;
                        vchr_panchayat = value.vchr_panchayat;
                        var Municipality_Id = value.Municipality_Id;
                        var Corporation_Id = value.Corporation_Id;
                        var Ward = value.Ward;
                        LokSabha_Id = value.LokSabha_Id;
                        Assemply_Id = value.Assemply_Id;
                        var vchr_ration = value.vchr_ration;
                        var int_loanamt_req = value.int_loanamt_req;
                        var Retirement_Date = value.Retirement_Date;
                        var int_amtsanction = value.int_amtsanction;


                        if (vchr_panchayat != "0") {
                            $("#dropLocalBody").val("1");
                            $("#DIV_Block").css("display", "flex");
                            $("#DIV_Panchayat").css("display", "flex");
                            $("#DIV_Municipality").css("display", "none");
                            $("#DIV_Corporation").css("display", "none");

                        }
                        else if (Municipality_Id != "0") {
                            $("#dropLocalBody").val("2");
                            $("#DIV_Block").css("display", "none");
                            $("#DIV_Panchayat").css("display", "none");
                            $("#DIV_Municipality").css("display", "flex");
                            $("#DIV_Corporation").css("display", "none");
                        }
                        else if (Corporation_Id != "0") {
                            $("#dropLocalBody").val("3");
                            $("#DIV_Block").css("display", "none");
                            $("#DIV_Block").css("display", "none");
                            $("#DIV_Panchayat").css("display", "none");
                            $("#DIV_Municipality").css("display", "none");
                            $("#DIV_Corporation").css("display", "flex");
                        }





                        $("#txtApplication_Date").val(Application_Submitted_Date);
                        $("#dropDistrict").val(District_Id);

                        $("#txtApplicant_Name").val(Name_Applicant);
                        $("#dropScheme").val(Scheme_Id);
                        $("#dropCast").val(Cast_Id);






                        $("#txtApplicationRegNo").val(vchr_appreceivregno);
                        $("#txtHouseName").val(vchr_hsename);
                        $("#txtPlace1").val(vchr_place1);
                        $("#txtPlace2").val(vchr_place2);
                        $("#txtPostOffices").val(vchr_post);
                        $("#txtPincode").val(int_pincode);
                        $("#txtPhoneNo").val(vchr_phno);
                        $("#dropSex").val(vchr_sex);

                        const inputDate = dte_dob;
                        const parts = inputDate.split('/');
                        if (parts.length === 3) {
                            const yyyy = parts[2];
                            const mm = parts[1];
                            const dd = parts[0];

                            const formattedDate = `${yyyy}-${mm}-${dd}`;

                            // Set the value of the date input
                            $('#txtDOB').val(formattedDate);
                        }


                        // $("#txtDOB_Input").val(dte_dob);
                        $("#txtAge").val(Age);
                        $("#txtAnnualIncome").val(int_anninc);
                        $("#txtAadharNumber").val(AadharNumber);



                        $("#txtLandMark").val(LandMark);
                        $("#txtFatherName").val(vchr_fathername);
                        $("#txtNameOfSpouse").val(vchr_spousename);
                        $("#dropVillage").val(vchr_village);
                        $("#dropTaluk").val(vchr_taluk);
                        $("#dropBlock").val(Block_Id);
                        $("#dropPanchayat").val(vchr_panchayat);
                        $("#dropMunicipality").val(Municipality_Id);
                        $("#dropCorporation").val(Corporation_Id);
                        $("#txtWard").val(Ward);
                        $("#dropLokSabha").val(LokSabha_Id);
                        $("#dropConstituency").val(Assemply_Id);
                        $("#txtRationCardNumber").val(vchr_ration);
                        $("#txtLoanAmount").val(int_loanamt_req);
                        $("#txtAmount_Saction").val(int_amtsanction);

                        const inputDate_2 = Retirement_Date;
                        const parts_2 = inputDate_2.split('/');
                        if (parts_2.length === 3) {
                            const yyyy_2 = parts_2[2];
                            const mm_2 = parts_2[1];
                            const dd_2 = parts_2[0];

                            const formattedDate_2 = `${yyyy_2}-${mm_2}-${dd_2}`;

                            // Set the value of the date input
                            $('#txtRetirement').val(formattedDate_2);
                        }




                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_Sub_District_And_Set(District_Id, SubDistrict_id, Cast_Id, Sub_Cast_Id);
                Load_All_Taluk_Update(vchr_village, vchr_taluk);
                Load_All_Constituency_Update(LokSabha_Id, Assemply_Id)
                Load_All_Panjayath_Update(Block_Id, vchr_panchayat);
                Load_All_Block($("#dropDistrict").val(), Block_Id);

                Load_All_Bank_Details();

            });
        }
        var Sub_District_Count;
        function Load_Sub_District_And_Set(District_Id, SubDistrict_id, Cast_Id, Sub_Cast_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Districts",
                data: JSON.stringify({ District_Id: District_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubDistrict').empty();
                    $('#dropSubDistrict').append('<option value="0">Select</option>');
                    Sub_District_Count = data.d.length;
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Sub_District_Name = value.Sub_District_Name;
                        var html = '<option value="' + Id + '">' + Sub_District_Name + '</option>';

                        $('#dropSubDistrict').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                if (Sub_District_Count == 0) {
                    $("#dropSubDistrict").val('0');
                }
                else {
                    $("#dropSubDistrict").val(SubDistrict_id);
                }


                Load_All_SubCast_And_Set(Cast_Id, Sub_Cast_Id);
            });

        }

        function Load_All_SubCast_And_Set(Cast_Id, Sub_Cast_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Cast",
                data: JSON.stringify({ Cast_Id: Cast_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubCast').empty();
                    $('#dropSubCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var SubCast = value.SubCast;
                        var html = '<option value="' + Id + '">' + SubCast + '</option>';

                        $('#dropSubCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_Scheme_Details();

                $("#dropSubCast").val(Sub_Cast_Id);
                //   Load_All_Taluk($("#dropSubCast").val());

            });

        }
        function Load_All_Taluk(Village_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_Taluk_By_Id",
                data: JSON.stringify({ Village_Id: Village_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropTaluk').empty();
                    // $('#dropTaluk').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Taluk = value.Taluk;
                        var html = '<option value="' + Id + '">' + Taluk + '</option>';
                        if (Id != 15) {
                            $('#dropTaluk').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                //$('#dropTaluk').change(function () {

                //    Load_All_Village($(this).val());
                //});

            });

        }
        function Load_All_Taluk_Update(Village_Id, Taluk_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_Taluk_By_Id",
                data: JSON.stringify({ Village_Id: Village_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropTaluk').empty();
                    // $('#dropTaluk').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Taluk = value.Taluk;
                        var html = '<option value="' + Id + '">' + Taluk + '</option>';
                        if (Id != 15) {
                            $('#dropTaluk').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                $('#dropTaluk').val(Taluk_Id);
                //$('#dropTaluk').change(function () {

                //    Load_All_Village($(this).val());
                //});

            });

        }

        //  dropPanjayath

        function Load_All_Village() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Village_By_Office_Id",
                //   data: JSON.stringify({ taluk_Id: Taluk_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropVillage').empty();
                    $('#dropVillage').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Village = value.Village;
                        var html = '<option value="' + Id + '">' + Village + '</option>';
                        if (Id != 15) {
                            $('#dropVillage').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $('#dropVillage').change(function () {

                    Load_All_Taluk($(this).val());
                });


                Load_All_Cast();
            });

        }



        function Load_All_Block(district_Id, Block_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Block_By_District_Id",
                data: JSON.stringify({ district_Id: district_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropBlock').empty();
                    $('#dropBlock').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Block = value.Block;
                        var html = '<option value="' + Id + '">' + Block + '</option>';

                        $('#dropBlock').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                $("#dropBlock").val(Block_Id);
                Load_All_Municipality(district_Id);
                $('#dropBlock').change(function () {

                    Load_All_Panjayath($(this).val());
                });
            });

        }


        function Load_All_Lok_Sabha() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Lok_Sabha_By_District_Id",
                // data: JSON.stringify({ block_Id: block_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropLokSabha').empty();
                    $('#dropLokSabha').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Lok_Sabha = value.Lok_Sabha;
                        var html = '<option value="' + Id + '">' + Lok_Sabha + '</option>';

                        $('#dropLokSabha').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Application_Issue_By_Id(getParameterByName("Id"));
            });

        }



        function Load_All_Constituency(Lok_Sabha_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Constituency_By_Lok_Sabha_Id",
                data: JSON.stringify({ lok_Sabha_Id: Lok_Sabha_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropConstituency').empty();
                    $('#dropConstituency').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Constituency = value.Constituency;
                        var html = '<option value="' + Id + '">' + Constituency + '</option>';

                        $('#dropConstituency').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });

        }

        function Load_All_Constituency_Update(Lok_Sabha_Id, Constituency_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Constituency_By_Lok_Sabha_Id",
                data: JSON.stringify({ lok_Sabha_Id: Lok_Sabha_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropConstituency').empty();
                    $('#dropConstituency').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Constituency = value.Constituency;
                        var html = '<option value="' + Id + '">' + Constituency + '</option>';

                        $('#dropConstituency').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                $('#dropConstituency').val(Constituency_Id);
            });

        }


        function Load_All_Panjayath(block_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Panjayath_By_Block_Id",
                data: JSON.stringify({ block_Id: block_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropPanchayat').empty();
                    $('#dropPanchayat').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Panjayath = value.Panjayath;
                        var html = '<option value="' + Id + '">' + Panjayath + '</option>';

                        $('#dropPanchayat').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });

        }


        function Load_All_Panjayath_Update(block_Id, vchr_panchayat) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Panjayath_By_Block_Id",
                data: JSON.stringify({ block_Id: block_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropPanchayat').empty();
                    $('#dropPanchayat').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Panjayath = value.Panjayath;
                        var html = '<option value="' + Id + '">' + Panjayath + '</option>';

                        $('#dropPanchayat').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                $('#dropPanchayat').val(vchr_panchayat);
            });

        }

        function Load_All_Municipality(District_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Municipality_By_District_Id",
                data: JSON.stringify({ District_Id: District_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropMunicipality').empty();
                    $('#dropMunicipality').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Municipality = value.Municipality;
                        var html = '<option value="' + Id + '">' + Municipality + '</option>';

                        $('#dropMunicipality').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Corporation(District_Id);
            });

        }


        function Load_All_Corporation(District_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Corporation_By_District_Id",
                data: JSON.stringify({ District_Id: District_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropCorporation').empty();
                    $('#dropCorporation').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Corporation = value.Corporation;
                        var html = '<option value="' + Id + '">' + Corporation + '</option>';

                        $('#dropCorporation').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });

        }


        function Load_All_SubCast(Cast_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Cast",
                data: JSON.stringify({ Cast_Id: Cast_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubCast').empty();
                    $('#dropSubCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var SubCast = value.SubCast;
                        var html = '<option value="' + Id + '">' + SubCast + '</option>';

                        $('#dropSubCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {


            });

        }


        function Load_All_Bank_Details() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_tbl_BankDetails_By_int_loanappid",
                data: JSON.stringify({ int_loanappid: getParameterByName("Id") }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {


                    $.each(data.d, function (key, value) {
                        var Bank_Account_No = value.int_BankAccNo;
                        var IFSC = value.vchr_IFSC;
                        var Bank_Name = value.vchr_Bank;
                        var Branch = value.vchr_Branch;

                        $("#txtBank_Account_No").val(Bank_Account_No);
                        $("#txtIFSC").val(IFSC);
                        $("#txtBank_Name").val(Bank_Name);
                        $("#txtBranch").val(Branch);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {


            });

        }

        function Next() {
            var Scheme_Id = $("#dropScheme").val();
            if (Scheme_Id == "2" || Scheme_Id == "3" || Scheme_Id == "6" || Scheme_Id == "7" || Scheme_Id == "8" || Scheme_Id == "9" || Scheme_Id == "10" || Scheme_Id == "13" || Scheme_Id == "17" || Scheme_Id == "33" || Scheme_Id == "34" || Scheme_Id == "35" || Scheme_Id == "36" || Scheme_Id == "37" || Scheme_Id == "38" || Scheme_Id == "39" || Scheme_Id == "40" || Scheme_Id == "41" || Scheme_Id == "42" || Scheme_Id == "44" || Scheme_Id == "45" || Scheme_Id == "46") {
                location.href = 'DE_Self_Employement_Loan_Edit.aspx?Id=' + getParameterByName("Id") + '&Scheme_Id=' + Scheme_Id;
            }
            else if (Scheme_Id == "2" || Scheme_Id == "3") {
                location.href = 'DE_Foreign_Loan_Edit.aspx?Id=' + getParameterByName("Id") + 'Scheme_Id=' + Scheme_Id;
            }

            else if (Scheme_Id == "14" || Scheme_Id == "15" || Scheme_Id == "16") {
                location.href = 'DE_Education_Loan_Edit.aspx?Id=' + getParameterByName("Id") + '&Scheme_Id=' + Scheme_Id
            }
            else if (Scheme_Id == "18") {
                location.href = 'DE_Working_Capital_Or_Business_Development_Loan_Edit.aspx?Id=' + getParameterByName("Id") + '&Scheme_Id=' + Scheme_Id;
            }
            else if (Scheme_Id == "19") {
                location.href = 'DE_Marriage_Loan_Edit.aspx?Id=' + getParameterByName("Id") + '&Scheme_Id=' + Scheme_Id;
            }
            else if (Scheme_Id == "20" || Scheme_Id == "21" || Scheme_Id == "22" || Scheme_Id == "23") {
                location.href = 'DE_Housing_Loan_Edit.aspx?Id=' + getParameterByName("Id") + '&Scheme_Id=' + Scheme_Id;
            }
            else if (Scheme_Id == "25" || Scheme_Id == "26") {
                location.href = 'DE_Housing_Maintenance_Loan_Edit.aspx?Id=' + getParameterByName("Id") + '&Scheme_Id=' + Scheme_Id;
            }
            else if (Scheme_Id == "27" || Scheme_Id == "28" || Scheme_Id == "31") {
                location.href = 'DE_Vehicle_Or_Home_Appliance_Loan_Edit.aspx?Id=' + getParameterByName("Id") + '&Scheme_Id=' + Scheme_Id;
            }
            else if (Scheme_Id == "29") {
                location.href = 'DE_Personal_Loan_Edit.aspx?Id=' + getParameterByName("Id") + '&Scheme_Id=' + Scheme_Id;
            }
            else if (Scheme_Id == "32") {
                location.href = 'DE_Agriculture_Loan_Edit.aspx?Id=' + getParameterByName("Id") + '&Scheme_Id=' + Scheme_Id;
            }
        }



        function Save() {

            var Application_Date = $("#txtApplication_Date");
            var ApplicationRegNo = $("#txtApplicationRegNo");
            var Applicant_Name = $("#txtApplicant_Name");
            var Scheme = $("#dropScheme");
            var HouseName = $("#txtHouseName");
            var Place1 = $("#txtPlace1");
            var Place2 = $("#txtPlace2");
            var District = $("#dropDistrict");
            var SubDistrict = $("#dropSubDistrict");
            var PostOffices = $("#txtPostOffices");
            var Pincode = $("#txtPincode");
            var PhoneNo = $("#txtPhoneNo");
            var Cast = $("#dropCast");
            var SubCast = $("#dropSubCast");
            var Age = $("#txtAge");
            var Annual_Income = $("#txtAnnualIncome");
            var Sex = $("#dropSex");
            var DOB_Input = $("#txtDOB");
            var Retirement_Date = $("#txtRetirement");

            var Father_Name = $("#txtFatherName");
            var Name_Of_Spouse = $("#txtNameOfSpouse");
            var Taluk = $("#dropTaluk");
            var Village = $("#dropVillage");
            var Block = $("#dropBlock");
            var LocalBody = $("#dropLocalBody");
            var Panchayat = $("#dropPanchayat");
            var Municipality = $("#dropMunicipality");
            var Corporation = $("#dropCorporation");
            var LokSabha = $("#dropLokSabha");
            var Constituency = $("#dropConstituency");
            var AadharNo = $("#txtAadharNumber");
            var RationCardNo = $("#txtRationCardNumber");
            var Other_Id = $("#dropOther");
            var Other_Id_No = $("#txtIDNumber");
            var Loan_Amount = $("#txtLoanAmount");
            var Amount_Saction = $("#txtAmount_Saction");

            var LandMark = $("#txtLandMark");
            var Ward = $("#txtWard");


            var Bank_Account_No = $("#txtBank_Account_No");
            var IFSC = $("#txtIFSC");
            var Bank_Name = $("#txtBank_Name");
            var Branch = $("#txtBranch");


            Loan_Period_In_Year = parseInt(Loan_Period) / 12;

            $(".form-control").removeAttr("style");



            if ($("#txt_Prathyasa"))




            if (Applicant_Name.val().trim() == "") {
                Focus_Error(Applicant_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Name of Applicant is required !'
                })
            }
            else if (!Is_Valid_Text(Applicant_Name.val().trim())) {
                Focus_Error(Applicant_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed !'
                })
            }
            else if (AadharNo.val() == "") {
                Focus_Error(AadharNo);
                Toast.fire({
                    icon: 'error',
                    title: 'Aadhar No is required !'
                })
            }
            else if (Scheme.val() == "0") {
                Focus_Error(Scheme);
                Toast.fire({
                    icon: 'error',
                    title: 'Loan Scheme is required !'
                })
            }
            else if (HouseName.val() == "") {
                Focus_Error(HouseName);
                Toast.fire({
                    icon: 'error',
                    title: 'House Name/No is required !'
                })
            }

            else if (Place1.val() == "") {
                Focus_Error(Place1);
                Toast.fire({
                    icon: 'error',
                    title: 'Lane1 is required !'
                })
            }

            else if (Place2.val() == "") {
                Focus_Error(Place2);
                Toast.fire({
                    icon: 'error',
                    title: 'Lane2 is required !'
                })
            }
            else if (Pincode.val() == "") {
                Focus_Error(Pincode);
                Toast.fire({
                    icon: 'error',
                    title: 'Pincode is required !'
                })
            }


            else if (Cast.val() == "0") {
                Focus_Error(Cast);
                Toast.fire({
                    icon: 'error',
                    title: 'Caste is required !'
                })
            }
            else if (SubCast.val() == "0") {
                Focus_Error(SubCast);
                Toast.fire({
                    icon: 'error',
                    title: 'Sub Caste is required !'
                })
            }
            else if (PhoneNo.val().trim() == "") {
                Focus_Error(PhoneNo);
                Toast.fire({
                    icon: 'error',
                    title: 'Phone No is required !'
                })
            }








            else if (Father_Name.val().trim() == "") {
                Focus_Error(Father_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Father Name is required !'
                })
            }
            else if (!Is_Valid_Text(Father_Name.val().trim())) {
                Focus_Error(Father_Name);

                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed !'
                })
            }
            //else if (Name_Of_Spouse.val().trim() == "") {
            //    Focus_Error(Name_Of_Spouse);
            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Spouse Name is required !'
            //    })
            //}
            //else if (!Is_Valid_Text(Name_Of_Spouse.val().trim())) {
            //    Focus_Error(Name_Of_Spouse);
            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Special characters or Numbers not allowed !'
            //    })
            //}
            else if (Sex.val().trim() == "") {
                Focus_Error(Sex);
                Toast.fire({
                    icon: 'error',
                    title: 'Sex is required !'
                })
            }
            else if (DOB_Input.val().trim() == "") {
                Focus_Error(DOB_Input);
                Toast.fire({
                    icon: 'error',
                    title: 'Date of Birth is required !'
                })
            }
            else if (Retirement_Date.val().trim() == "" && Max_Age == "0") {
                Focus_Error(Retirement_Date);
                Toast.fire({
                    icon: 'error',
                    title: 'Retirement Date is required !'
                })
            }
            else if ((parseInt($("#hdn_Retirement_Age").val()) + 1) < parseInt(Loan_Period_In_Year)) {

                Focus_Error(Retirement_Date);
                Toast.fire({
                    icon: 'error',
                    title: 'Due to your retirement date, you are not eligible to apply for a loan !'
                })
            }
            else if (Village.val() == "0") {
                Focus_Error(Village);
                Toast.fire({
                    icon: 'error',
                    title: 'Village is required !'
                })
            }

            else if (LocalBody.val() == "0") {
                Focus_Error(LocalBody);
                Toast.fire({
                    icon: 'error',
                    title: 'Local Body is required !'
                })
            }
            else if (Block.val() == "0" && LocalBody.val() == "1") {
                Focus_Error(Block);
                Toast.fire({
                    icon: 'error',
                    title: 'Block is required !'
                })
            }
            else if (Ward.val() == "") {
                Focus_Error(Ward);
                Toast.fire({
                    icon: 'error',
                    title: 'Ward Number is required !'
                })
            }
            else if (LokSabha.val() == "0") {
                Focus_Error(LokSabha);
                Toast.fire({
                    icon: 'error',
                    title: 'Lok-Sabha is required !'
                })
            }

            else if (Constituency.val() == "0") {
                Focus_Error(Constituency);
                Toast.fire({
                    icon: 'error',
                    title: 'Constituency is required !'
                })
            }


            //else if (Other_Id.val() != "0") {
            //    Focus_Error(Other_Id);
            //    Toast.fire({
            //        icon: 'error',
            //        title: 'Other Id No is required !'
            //    })
            //}
            //
            else if (Loan_Amount.val() == "0") {
                Focus_Error(Loan_Amount);
                Toast.fire({
                    icon: 'error',
                    title: 'Loan Amount is required !'
                })
            }
            else if (!Is_Number(Loan_Amount.val().trim())) {
                Focus_Error(Loan_Amount);
                Toast.fire({
                    icon: 'error',
                    title: 'Loan Amount only accept numbers !'
                })
            }
            else if (Loan_Amount.val() == "") {
                Focus_Error(Loan_Amount);
                Toast.fire({
                    icon: 'error',
                    title: 'Loan Amount is required !'
                })
            }
            else if (Amount_Saction.val() == "0") {
                Focus_Error(Amount_Saction);
                Toast.fire({
                    icon: 'error',
                    title: 'Loan Sanction Amount is required !'
                })
            }
            else if (Amount_Saction.val() == "") {
                Focus_Error(Amount_Saction);
                Toast.fire({
                    icon: 'error',
                    title: 'Loan Sanction Amount is required !'
                })
            }

            else if (IFSC.val() == "") {
                Focus_Error(IFSC);
                Toast.fire({
                    icon: 'error',
                    title: 'IFSC Code is required !'
                })
            }

            else if (Bank_Name.val() == "") {
                Focus_Error(Bank_Name);
                Toast.fire({
                    icon: 'error',
                    title: 'Bank Name is required !'
                })
            }
            else if (Branch.val() == "") {
                Focus_Error(Branch);
                Toast.fire({
                    icon: 'error',
                    title: 'Branch Name is required !'
                })
            }
            else if (Bank_Account_No.val() == "") {
                Focus_Error(Bank_Account_No);
                Toast.fire({
                    icon: 'error',
                    title: 'Bank Account No is required !'
                })
            }





            else if (!Is_Number(Amount_Saction.val().trim())) {
                Focus_Error(Amount_Saction);
                Toast.fire({
                    icon: 'error',
                    title: 'Loan Sanction Amount only accept numbers !'
                })
            }
            else if (LocalBody.val().trim() == "0") {
                Focus_Error(LocalBody);
                Toast.fire({
                    icon: 'error',
                    title: 'Select Panchayat/Municipality/Corporation is required !'
                })
            }
            else if (AadharNo.val().trim() == "") {
                Focus_Error(AadharNo);
                Toast.fire({
                    icon: 'error',
                    title: 'Aadhar No is required !'
                })
            }
            else if (!Is_Valid_Aadhar_No(AadharNo.val().trim())) {
                Focus_Error(AadharNo);
                Toast.fire({
                    icon: 'error',
                    title: 'Invalid Aadhar number. Please enter 12 digits !'
                })
            }

            else if (Annual_Income.val().trim() == "") {
                Focus_Error(Annual_Income);
                Toast.fire({
                    icon: 'error',
                    title: 'Annual Income is required !'
                })
            }
            else if (RationCardNo.val() == "") {
                Focus_Error(RationCardNo);
                Toast.fire({
                    icon: 'error',
                    title: 'Ration Card Number is required !'
                })
            }
            else if (!Is_Valid_RationCard_No(RationCardNo.val().trim())) {
                Focus_Error(RationCardNo);
                Toast.fire({
                    icon: 'error',
                    title: 'Invalid Ration Card Number. Please enter 10 digits !'
                })
            }
            else if (!Check_Loan_Amount_Limit(Loan_Amount.val().trim())) {

                Focus_Error(Loan_Amount);
                Toast.fire({
                    icon: 'error',
                    title: 'Required Loan amount is above the limit of the scheme you selected !'
                })
            }

            else {

                if (PhoneNo.val().trim() != "") {

                    var values = PhoneNo.val().trim().split(",");
                    for (var i = 0; i < values.length; i++) {
                        if (values[values.length - 1] != "") {
                            if (!Is_Valid_Mobile_Number(values[i])) {
                                Focus_Error(PhoneNo);
                                Toast.fire({
                                    icon: 'error',
                                    title: 'Enter valid phone number !'
                                })
                                return;
                            }
                        }
                    }
                }

                if (Max_Age != "0") {
                    if (!Check_Age_Limit(Age.val().trim()) && Max_Age != "0") {
                        Focus_Error(Age);
                        Toast.fire({
                            icon: 'error',
                            title: 'Age is below the limit of the scheme you selected !'
                        })
                        return;
                    }
                }



                if (LocalBody.val() == "1") {
                    if (Panchayat.val() == "0") {
                        Focus_Error(Panchayat);
                        Toast.fire({
                            icon: 'error',
                            title: 'Panchayat is required !'
                        })
                        return;
                    }
                }
                else if (LocalBody.val() == "2") {
                    if (Municipality.val() == "0") {
                        Focus_Error(Municipality);
                        Toast.fire({
                            icon: 'error',
                            title: 'Municipality is required !'
                        })
                        return;
                    }

                }
                else if (LocalBody.val() == "3") {
                    if (Corporation.val() == "0") {
                        Focus_Error(Corporation);
                        Toast.fire({
                            icon: 'error',
                            title: 'Corporation is required !'
                        })
                        return;
                    }
                }

                if (Rural_Max != "0" & Urban_Max != "0") {
                    if (!Check_Annual_Income_Limit(Annual_Income.val())) {
                        Focus_Error(Annual_Income);
                        Toast.fire({
                            icon: 'error',
                            title: 'Annual Income is above the limit of the scheme you selected !'
                        })
                        return;
                    }

                }



                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/New_Update_To_tbl_loanapp",
                    data: JSON.stringify({ int_loanappid: getParameterByName("Id"), vchr_applname: Applicant_Name.val().trim(), vchr_Aadhaar: AadharNo.val().trim(), int_schemeid: Scheme.val(), vchr_hsename: HouseName.val(), vchr_place1: Place1.val(), vchr_place2: Place2.val(), vchr_district: District.val(), vchr_subdistrict: SubDistrict.val(), int_pincode: Pincode.val().trim(), vchr_post: $("#txtPostOffices").val(), vchr_phno: PhoneNo.val(), vchr_fathername: Father_Name.val().trim(), vchr_spousename: Name_Of_Spouse.val().trim(), vchr_caste: Cast.val(), subCast: SubCast.val(), vchr_sex: Sex.val(), dte_dob: DOB_Input.val(), age: $("#txtAge").val(), vchr_village: Village.val(), vchr_taluk: Taluk.val(), block_Id: Block.val(), municipality_Id: Municipality.val(), vchr_panchayat: Panchayat.val(), corporation_Id: Corporation.val(), lokSabha_Id: LokSabha.val(), assemply_Id: Constituency.val(), int_anninc: Annual_Income.val(), vchr_ration: RationCardNo.val(), int_loanamt_req: Loan_Amount.val(), Status: "Application Data Entry", LandMark: LandMark.val(), Ward: Ward.val(), Retirement_Date: Retirement_Date.val(), int_amtsanction: Amount_Saction.val() }),

                    contentType: "application/json; charset=utf-8",
                    success: function (data) {

                    },
                    error: function (error) {


                        // alert("error" + error.responseText);
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                        })
                    }
                }).done(function () {
                    Save_Bank_Details();


                });


            }

        }

        function Save_Bank_Details() {

            var Bank_Account_No = $("#txtBank_Account_No").val();
            var IFSC = $("#txtIFSC").val();
            var Bank_Name = $("#txtBank_Name").val();
            var Branch = $("#txtBranch").val();


            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Insert_To_tbl_BankDetails",
                data: JSON.stringify({ int_loanappid: getParameterByName("Id"), int_BankAccNo: Bank_Account_No, vchr_IFSC: IFSC, vchr_Bank: Bank_Name, vchr_Branch: Branch }),

                contentType: "application/json; charset=utf-8",
                success: function (data) {

                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                    })
                }
            }).done(function () {
                //  Next();
                Swal.fire({
                    icon: 'success',
                    title: 'Message',
                    text: 'Application Data Entry Successfully Submitted!',

                }).then((result) => {
                    if (result.isConfirmed) {
                        Next();
                        // OK button clicked
                        //  location.href = 'ApplicationDataEntry_List.aspx';
                        // location.href = 'ApplicationDataEntry_View.aspx';
                        // Your code here
                    }
                });



            });

        }


        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }
        function Is_Number(inputText) {


            if (isNaN(inputText)) {
                return false;
            } else {
                return true;
            }


        }

        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }

        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }



        function Check_Age_Limit(Age) {
            //var Age = $("#txtAge").val();
            var Min_Age = $("#dropScheme option:selected").attr("data-min_age");
            var Max_Age = $("#dropScheme option:selected").attr("data-max_age");

            Age = parseInt(Age);
            Min_Age = parseInt(Min_Age);
            Max_Age = parseInt(Max_Age);

            if (Min_Age <= Age && Max_Age >= Age) {
                return true;
            }
            else {
                return false;
            }

        }

        function Check_Annual_Income_Limit(Annual_Income) {
            //  var Annual_Income = $("#txtAnnualIncome").val();
            //    var Anninc_Rural_Max = $("#dropScheme option:selected").attr("data-anninc_rural_max");
            //    var Anninc_Urban_Max = $("#dropScheme option:selected").attr("data-anninc_urban_max");

            Annual_Income = parseInt(Annual_Income);
            Rural_Max = parseInt(Rural_Max);
            Urban_Max = parseInt(Urban_Max);

            if (Rural_Max >= Annual_Income && Urban_Max >= Annual_Income) {
                return true;
            }
            else {
                return false;
            }

        }


        function Check_Loan_Amount_Limit(Loan_Amount) {
            //   var Loan_Amount = $("#txtLoanAmount").val();
            var Req_Loan_Amount = $("#dropScheme option:selected").attr("data-loan_amount");


            Loan_Amount = parseInt(Loan_Amount);
            Req_Loan_Amount = parseInt(Req_Loan_Amount);

            if (Loan_Amount <= Req_Loan_Amount) {
                return true;
            }
            else {
                return false;
            }

        }

        function Is_Valid_Aadhar_No(Aadhar_No) {
            var aadharPattern = /^\d{12}$/;

            if (aadharPattern.test(Aadhar_No)) {
                return true;
            } else {
                return false;
            }
        }


        function Is_Valid_RationCard_No(Ration_Card_No) {
            var rationPattern = /^\d{10}$/;

            if (rationPattern.test(Ration_Card_No)) {
                return true;
            } else {
                return false;
            }
        }



    </script>
</asp:Content>
