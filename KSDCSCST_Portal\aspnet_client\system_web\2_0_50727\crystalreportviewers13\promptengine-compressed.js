var PE_VALUE_DESC_SEPARATOR=" - ";if(typeof(_pe)=="undefined"){_pe=new function(){var a=this;a._ie=(document.all!=null)?true:false;a._dom=(document.getElementById!=null)?true:false;a._isQuirksMode=(document.compatMode!="CSS1Compat");a._moz=a._dom&&!a._ie;a._appVer=navigator.appVersion.toLowerCase();a._mac=(a._appVer.indexOf("macintosh")>=0)||(a._appVer.indexOf("macos")>=0);a._userAgent=navigator.userAgent?navigator.userAgent.toLowerCase():null;a._saf=a._moz&&(a._userAgent.indexOf("safari")>=0);a._ie6=a._ie&&(a._appVer.indexOf("msie 6")>=0);a._root="";a._images=a._root+"/images/";a._prompts=new Array;a._lovBS=1000;a._st="s";a._nm="n";a._cy="c";a._bo="b";a._da="d";a._tm="t";a._dt="dt";_BlockWaitWidgetID="PEBlockWidgetID";a._theLYR=null;a._dlgResize=null;a._widgets=new Array;a.DlgBox_modals=new Array;a.DlgBox_instances=new Array;a.DlgBox_current=null;a._show="visible";a._hide="hidden";a._hand=a._ie?"hand":"pointer";a.init=PE_init;a.canSubmit=PE_canSubmit;a.beginBlocking=PE_beginBlocking;a.endBlocking=PE_endBlocking;a.setLOVMsg=PE_setLOVMsg}}function PE_init(b,a){var d=this;if(b&&b.length>0){if(b.charAt(b.length-1)!="/"){b+="/"}d._root=b;d._images=b+"images/"}else{d._root=null;d._images=null}if(a>0){d._lovBS=a}}function PE_canSubmit(){return(this.DlgBox_current)?false:true}function PE_setLOVMsg(d,a,b){var e=document.getElementById(a);var f=e.value;if(f.length>0){f+="&"}f+="cmd=1&ap="+b;e.value=f}var DateTimeFormatSetting={datePattern:"Y-M-D",isTwoDigitMonth:true,isTwoDigitDay:true,dateRegex:null,dateTimeRegex:null};function promptengine_getDatePattern(){return DateTimeFormatSetting.datePattern}function promptengine_setDatePattern(a){DateTimeFormatSetting.datePattern=a}function promptengine_getIsTwoDigitMonth(){return DateTimeFormatSetting.isTwoDigitMonth}function promptengine_setIsTwoDigitMonth(a){DateTimeFormatSetting.isTwoDigitMonth=a}function promptengine_getIsTwoDigitDay(){return DateTimeFormatSetting.isTwoDigitDay}function promptengine_setIsTwoDigitDay(a){DateTimeFormatSetting.isTwoDigitDay=a}function promptengine_getDateRegex(){return DateTimeFormatSetting.dateRegex}function promptengine_setDateRegex(a){DateTimeFormatSetting.dateRegex=a}function promptengine_getDateTimeRegex(){return DateTimeFormatSetting.dateTineRegex}function promptengine_setDateTimeRegex(a){DateTimeFormatSetting.dateTineRegex=a}function _convStr(e,a,d){e=""+e;var b=e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;");if(a){b=b.replace(/ /g,"&nbsp;")}if(d){b=b.replace(/\n/g,"<br>")}return b}function _opt(d,a,b){return'<option value="'+_convStr(d)+'" '+(b?"selected":"")+">"+_convStr(a)+"</option>"}function _canScanFrames(a){var b=true,f=null;if(_pe._moz){_oldErrHandler=window.onerror;window.onerror=localErrHandler}try{f=a.document;b=false}catch(e){}if(_pe._moz){window.onerror=_oldErrHandler}return(!b&&(f!=null))}function _restoreAllDisabledInputs(j,a){if(_pe._ie&&window._peInputStackLevel!=null){j=j?j:window;if(_canScanFrames(j)){if(a==null){a=--window._peInputStackLevel}var n=j.document.body,f=n?n.getElementsByTagName("SELECT"):null,l=f?f.length:0;for(var e=0;e<l;e++){var g=f[e];if(g._peDisableLevel==a){g.disabled=false;g._peDisableLevel=null}}var m=j.frames,o=m.length;for(var d=0;d<o;d++){_restoreAllDisabledInputs(m[d],a)}}}}function _disableAllInputs(r,p,s,g,n,a){if(_pe._ie){n=n?n:window;if(_canScanFrames(n)){var q=n.document.body,j=q?q.getElementsByTagName("SELECT"):null,m=j?j.length:0;if(a==null){if(window._peInputStackLevel==null){window._peInputStackLevel=0}a=window._peInputStackLevel++}for(var f=0;f<m;f++){var l=j[f];var d=(r==null)||_isLayerIntersectRect(l,r,p,s,g);if(!l.disabled&&d){l._peDisableLevel=a;l.disabled=true}}var o=n.frames,t=o.length;for(var e=0;e<t;e++){_disableAllInputs(null,null,null,null,o[e],a)}}}}function _getBGIframe(a){return'<iframe id="'+a+'" style="display:none;left:0px;position:absolute;top:0px" src="'+_pe._images+'transp.gif" frameBorder="0" scrolling="no"></iframe>'}function _eventCancelBubble(b,a){a=a?a:window;_pe._ie?a.event.cancelBubble=true:b.cancelBubble=true}function _append(f,b){if(_pe._ie){f.insertAdjacentHTML("BeforeEnd",b)}else{var a=document;var d=a.createRange();d.setStartBefore(f);var g=d.createContextualFragment(b);f.appendChild(g)}}function _targetApp(a){_append(document.body,a)}function _isLayerIntersectRect(d,b,n,o,g){var f=_getPos(d).x,k=_getPos(d).y,e=f+d.offsetWidth,j=k+d.offsetHeight,a=b+o,m=n+g;return((b>f)||(a>f))&&((b<e)||(a<e))&&((n>k)||(m>k))&&((n<j)||(m<j))}function _getPos(b,a){a=a?a:null;for(var e=0,d=0;(b!=null)&&(b!=a);e+=b.offsetLeft,d+=b.offsetTop,b=b.offsetParent){}return{x:e,y:d}}function _getLayer(a){return document.getElementById(a)}function _getWidget(b){if(b==null){return null}var a=b._widget;if(a!=null){return _pe._widgets[a]}else{return _getWidget(b.parentNode)}}function _isHidden(b){if((b==null)||(b.tagName=="BODY")){return false}var a=b.style;if((a==null)||(a.visibility==_pe._hide)||(a.display=="none")){return true}return _isHidden(b.parentNode)}function _attr(a,b){return(b!=null?" "+a+'="'+b+'" ':"")}function _img(f,a,d,g,b,e){b=(b?b:"");if(e==null){e=""}return"<img"+_attr("width",a)+_attr("height",d)+_attr("src",f)+_attr(_pe._ie?"alt":"title",e)+_attr("align",g)+' border="0" hspace="0" vspace="0" '+(b?b:"")+">"}function _imgOffset(a,j,e,m,l,b,g,d,k,f){return _img(_pe._images+"transp.gif",j,e,f,(g?g:"")+" "+_attr("id",b)+' style="'+_backImgOffset(a,m,l)+(k?k:"")+'"',d)}function _changeOffset(f,b,a,e,g){var d=f.style;if(d){if((b!=null)&&(a!=null)){d.backgroundPosition=""+(-b)+"px "+(-a)+"px"}if(e){d.backgroundImage="url('"+e+"')"}}if(g){f.alt=g}}function _simpleImgOffset(a,j,e,m,l,b,g,d,k,f){if(_pe._ie){if(m==null){m=0}if(l==null){l=0}return"<div "+(g?g:"")+" "+_attr("id",b)+' style="position:relative;padding:0px;width:'+j+"px;height:"+e+"px;overflow:hidden;"+(k?k:"")+'">'+_img(a,null,null,"top",'style="margin:0px;position:relative;top:'+(-l)+"px;left:"+(-m)+'px"',d)+"</div>"}else{return _imgOffset(a,j,e,m,l,b,g,d,k,f)}}function _changeSimpleOffset(f,b,a,e,g){if(_pe._ie){f=f.childNodes[0];var d=f.style;if((e!=null)&&(e!=f.src)){f.src=e}if(b!=null){d.left=""+(-b)+"px"}if(a!=null){d.top=""+(-a)+"px"}if(g!=null){f.alt=g}}else{_changeOffset(f,b,a,e,g)}}function _backImgOffset(d,b,a){return"background-image:url('"+d+"');background-position:"+(-b)+"px "+(-a)+"px;"}function _sty(a,b){return(b!=null?a+":"+b+";":"")}function _getSpace(a,b){return'<table height="'+b+'" border="0" cellspacing="0" cellpadding="0"><tr><td>'+_img(_pe._images+"transp.gif",a,b)+"</td></tr></table>"}function _isTextInput(a){var b=_pe._ie?a.srcElement:a.target;var d=false;if(b.tagName=="TEXTAREA"){d=true}if((b.tagName=="INPUT")&&(b.type.toLowerCase()=="text")){d=true}return d}function _documentWidth(b){var b=b?b:window;var a=Math.max(document.body.clientWidth,document.documentElement.clientWidth);a=Math.max(a,document.body.scrollWidth);return a}function _documentHeight(b){var b=b?b:window;var a=Math.max(document.body.clientHeight,document.documentElement.clientHeight);a=Math.max(a,document.body.scrollHeight);return a}function _winWidth(b){var b=b?b:window;var a;if(_pe._ie){if(_pe._isQuirksMode){a=b.document.body.clientWidth}else{a=b.document.documentElement.clientWidth}}else{a=b.innerWidth}return a}function _winHeight(b){var b=b?b:window;var a;if(_pe._ie){if(_pe._isQuirksMode){a=document.body.clientHeight}else{a=document.documentElement.clientHeight}}else{a=b.innerHeight}return a}function _getScrollX(a){var b=0;var a=a?a:window;if(typeof(a.scrollX)=="number"){b=a.scrollX}else{b=Math.max(a.document.body.scrollLeft,a.document.documentElement.scrollLeft)}return b}function _getScrollY(b){var a=0;var b=b?b:window;if(typeof(b.scrollY)=="number"){a=window.scrollY}else{a=Math.max(b.document.body.scrollTop,b.document.documentElement.scrollTop)}return a}function _eventGetX(a){return _pe._ie?window.event.clientX:a.clientX?a.clientX:a.pageX}function _eventGetY(a){return _pe._ie?window.event.clientY:a.clientY?a.clientY:a.pageY}function _eventGetKey(b,a){a=a?a:window;return _pe._ie?a.event.keyCode:b.keyCode}function _isLayerDisplayed(a){var b=a?a.style:null;if(b){if(b.display=="none"||b.visibility=="hidden"){return false}else{var d=a.parentNode;if(d!=null){return _isLayerDisplayed(d)}else{return true}}}else{return true}}function _safeSetFocus(a){if(a&&a.focus&&_isLayerDisplayed(a)&&!a.disabled){a.focus()}}function PE_getLB(a){var b=new Object;b.lyr=a;b.arr=new Array;b.size=0;b.add=LB_add;b.update=LB_update;return b}function LB_add(e,a,b){var d=this;d.arr[++d.size]=_opt(e,a,b)}function LB_update(){var j=this;var b=j.arr;if(!j.lyr){return}var g=j.lyr.parentNode;var e=g.innerHTML;var d=e.indexOf(">");if(d==-1){return}var f=e.lastIndexOf("<");if(f<=d){return}b[0]=e.substring(0,d+1);b[j.size+1]=e.substr(f);g.innerHTML=b.join("")}function newUnits(a,b){var d=new Object;d.parr=new Array;if(b){d.idh=b}else{d.idh=""}d.num=a;d.init=Units_init;d.show=Units_show;d.toggle=Units_toggle;d.updateForm=Units_updateForm;d.activate=Units_activate;d.addP=Units_addP;return d}function Units_init(a){var b=this;b.toggle(a)}function Units_show(f,a){var g=this;var d=document.getElementById(g.idh+f+"_PU");if(d){var e=d.style;if(a){e.display=""}else{e.display="none"}}}function Units_activate(f){var j=this;var a=document.getElementById(j.idh+f+"_PU");if(a){var b=_getScrollY(),e=_getScrollX();var g=a.offsetHeight,d=_winHeight(),k=_getPos(a).y;if(f==0){window.scrollTo(e,0)}else{if(k<=b){window.scrollTo(e,k)}else{if(k+g>b+d){window.scrollTo(e,Math.max(k,k+g-d))}}}}}function Units_toggle(b){var d=this,e=d.num;for(var a=0;a<e;a++){d.show(a,true)}d.activate(b)}function Units_addP(b){var d=this;var a=d.parr;a[a.length]=b}function Units_updateForm(f,a,b){var j=this,e=j.parr;for(var d in e){var g=e[d];if(g){if(!g.updateForm(f,a,b)){j.toggle(g.uid);return false}}}return true}function P_updateForm(f,d,e){var g=this,a=false;if(g.readonly==true){return true}if(g.mul){a=promptengine_updateMultiValue(f,d,g.id,g.vt,e,g.valueRequired)}else{if(g.rn){a=promptengine_updateRangeValue(f,d,g.id,g.vt,e,g.valueRequired)}else{a=promptengine_updateDiscreteValue(f,d,g.id,g.vt,e,g.valueRequired)}}return a}function P_addV(b,f){var e=this;if(e.vl==null){e.vl=new Array;if(f){e.dl=new Array}}var a=e.vl.length;e.vl[a]=b;if(e.dl){e.dl[a]=f}}function P_findBatch(g,d){if(!d){return(-1)}var j=this;var f=j.vl;if(g){var a=j.lov[g];if(a&&a.vl){f=a.vl}}if(f){for(var e in f){var b=f[e];if(b&&b==d){return(Math.floor(e/_pe._lovBS))}}}return(-1)}function P_updateLOVNB(g,q){var f=this;var l=f.id;var a=document.getElementById(l+g+"Batch");if(!a){return}var p=f.lov[g];if(q){var e=a.options;e.length=0;var j=f.vl,k=0;if(p.vl){j=p.vl}var m=Math.ceil(j.length/_pe._lovBS);while(k<m){var n=k+1;if(p.sidx==k){n+="*"}e[e.length]=new Option(n,k,false,false);k++}}if(p.bidx>=0){a.selectedIndex=p.bidx}}function P_updateLOV(u,b){var n=this;var m=n.id;var f=m+u;var q=document.getElementById(f);if(!q){return}var a=n.lov[u];var r=null;var A=n.vl;var s=n.dl;var B=-1;if(a){r=a.sel;if(a.vl){A=a.vl;s=a.dl}}var k=PE_getLB(q);if(b){k.add("","...")}var p=a.sidx;if(a.bidx<0){a.sidx=n.findBatch(u,r);if(a.sidx>=0){a.bidx=a.sidx}else{a.bidx=0}p=-2}var e=a.bidx;var x=e*_pe._lovBS,y=A.length,t=0;while(x<y){if(t>=_pe._lovBS){break}var g=A[x];var z=null;if(s){z=s[x];if(z==""){z=g}else{if(n.dop==0){z=g+PE_VALUE_DESC_SEPARATOR+z}}}else{z=g}if(r&&r==g){B=t;n.sidx=e}k.add(g,z);x++;t++}k.update();q=document.getElementById(f);if(B!=-1){if(b){B++}q.selectedIndex=B}n.updateLOVNB(u,p!=a.sidx)}function P_getDesc(a){if(!a){return null}var g=this;var f=g.vl;var d=g.dl;if(!d){return null}var b=-1;for(var e in f){if(f[e]==a){b=e;break}}if(b>=0){return d[b]}return null}function P_updateSLOV(){var a=this;var p=a.id;var n=document.getElementById(p+"ListBox");if(!n){return}var b=a.sl;if(typeof(b)!="object"){return}var r=PE_getLB(n);var g=a.vl;var q=a.dl;for(var m in b){var u=b[m];if(typeof(u)=="string"){var t=a.getDesc(u);var l;if(t&&t!=""){if(a.dop){l=t}else{l=u+PE_VALUE_DESC_SEPARATOR+t}}else{l=u}r.add(u,l,false)}else{var s=u.l;var k=u.u;var j=u.lt;var f=u.ut;var e=null;var l=null;if(j==0||j==1){e="(";l="("}else{e="[";l="["}if(j){e+=s;var t=a.getDesc(s);if(t&&t!=""){if(a.dop){l+=t}else{l+=s+PE_VALUE_DESC_SEPARATOR+t}}else{l+=s}}e+="_crRANGE_";l+="  ..  ";if(f){e+=k;var t=a.getDesc(k);if(t&&t!=""){if(a.dop){l+=t}else{l+=k+PE_VALUE_DESC_SEPARATOR+t}}else{l+=k}}if(f==0||f==1){e+=")";l+=")"}else{e+="]";l+="]"}r.add(e,l,false)}}r.update()}function P_update(a){var b=this;if(a){if(a=="AvailableList"){b.updateLOV(a)}else{if(a=="ListBox"){b.updateSLOV()}else{b.updateLOV(a,true)}}}else{b.updateLOV("SelectValue",true);b.updateLOV("AvailableList");b.updateLOV("SelectLowerRangeValue",true);b.updateLOV("SelectUpperRangeValue",true);b.updateSLOV()}}function P_setLOV(b,a){var d=this;d.vl=b;if(b){if(!a||b.length!=a.length){d.dl=null}else{d.dl=a}}else{d.dl=null}}function P_setInitSel(b){var d=this;var a=d.lov.SelectValue;a.sel=b}function P_setInitSelList(a){this.sl=a}function P_setInitBound(b,a){var f=this;var e=f.lov.SelectLowerRangeValue;e.sel=b;var d=f.lov.SelectUpperRangeValue;d.sel=a}function P_back(d){var e=this;var a=e.lov[d];if(!a){return}var b=a.bidx;if(b>0){b--;if(b<0){b=0}a.bidx=b;e.update(d)}}function P_next(e){var f=this;var b=f.lov[e];if(!b){return}var a=((b.vl)?b.vl.length:f.vl.length);var d=b.bidx;if((d+1)*_pe._lovBS<a){b.bidx=d+1;f.update(e)}}function newLOV(){var a=new Object;a.bidx=-1;a.sidx=-1;a.sel=null;a.filter=null;a.vl=null;a.dl=null;return a}function newP(l,k,b,n,f,m,j,e,g,a){var d=new Object;d.id=b;d.vt=n;d.mul=f;d.di=m;d.rn=j;d.dop=e;d.readonly=g;d.valueRequired=a;d.units=l;d.uid=k;d.lov=new Array;d.lov.SelectValue=newLOV();d.lov.AvailableList=newLOV();d.lov.SelectLowerRangeValue=newLOV();d.lov.SelectUpperRangeValue=newLOV();d.vl=null;d.dl=null;d.sl=null;d.addV=P_addV;d.update=P_update;d.setLOV=P_setLOV;d.updateLOV=P_updateLOV;d.updateSLOV=P_updateSLOV;d.updateLOVNB=P_updateLOVNB;d.getDesc=P_getDesc;d.findBatch=P_findBatch;d.back=P_back;d.next=P_next;d.showFilter=P_showFilter;d.applyFilter=P_applyFilter;d.setInitSel=P_setInitSel;d.setInitBound=P_setInitBound;d.setInitSelList=P_setInitSelList;d.updateForm=P_updateForm;_pe._prompts[b]=d;if(l){l.addP(d)}return d}function P_navigateCB(f,a,d,b){var e=_pe._prompts[a];if(!e){return}if(b=="p"){e.back(d)}else{if(b=="n"){e.next(d)}}}function P_selectCB(e,b,j,g){var k=_pe._prompts[b];if(!k){return}var d=b+g;var f=b+j;promptengine_selectValue(e,f,d);var a=k.lov[j];a.sel=document.getElementById(d).value;if(!a.sel||a.sel==""){a.sidx=-1;a.sel=null}else{if(a.sidx!=a.bidx){a.sidx=a.bidx}}k.updateLOVNB(j,true)}function P_batchCB(j,b,f){var g=_pe._prompts[b];var e=document.getElementById(b+f+"Batch");if(!g||!e){return}var d=e.selectedIndex;if(d>=0){var a=g.lov[f];if(!a){return}a.bidx=d;g.update(f)}}function P_applyFilter(e,b){if(b==null){return}var d=this;var f=d.vl;var n=d.dl;if(!f||f.constructor!=Array||f.length==0){return}var m=true;if(!n||n.constructor!=Array){m=false}var r=d.lov[e];if(!r){return}var u=r.filter;if(!u){u=""}if(b==u){return}var a=null;var k=null;if(b==""){b=null}else{a=[];if(m){k=[]}b=b.replace(String.fromCharCode(160)," ");var g=0;for(var l=0,q=f.length;l<q;l++){var t=f[l];var p=(m?n[l]:"");var s="";if(d.dop==1){if(p==""){s=t}else{s=p}}else{s=t;if(p!=""){s+=PE_VALUE_DESC_SEPARATOR;s+=p}}s=s.replace(String.fromCharCode(160)," ");if(s&&s.toLowerCase().indexOf(b.toLowerCase())!=-1){a[g]=t;if(m){k[g]=n[l]}g++}}}r.filter=b;r.vl=a;r.dl=k;r.bidx=-1;r.sidx=-1;d.updateLOV(e,true)}function P_promptFilter(j,f,l){var d=_pe._prompts[j];if(!d){return}var g=d.vl;var k=d.dl;if(!g||g.length==0){return}var p=d.lov[f];if(!p){return}var b=p.filter;if(!b){b=""}var a=l.target?l.target:l.srcElement;var n=_findPos(a);var q=n.x+a.offsetWidth;var m=n.y+a.offsetHeight;d.showFilter(f,b,q,m)}function P_promptClearFilter(a,d,b){var f=_pe._prompts[a];if(!f){return}if(f.filterDlg){f.filterDlg.setValue("");f.applyFilter(d,"")}}function P_showFilter(d,b,a,g){var f=this;if(!f.filterDlg){f.filterDlg=newFilterDlg(f.id)}var e=f.filterDlg;e.wty=d;e.setValue(b);e.show(true);e.initDlg(a,g)}function _findPos(b,a){var a=a?a:null;var e=0;var d=0;while(b.parentNode||b.offsetParent){if(b.offsetParent){e+=b.offsetLeft;d+=b.offsetTop;b=b.offsetParent}else{if(b.parentNode){if(b.style){if(b.style.left){e+=b.style.left}if(b.style.top){d+=b.style.top}}b=b.parentNode}else{break}}}if(a){relToCord=getPos2(a);e-=relToCord.x;d-=relToCord.y}return{x:e,y:d}}function FilterDlg_okCB(b){var e=this;if(b){e=_getWidget(_getLayer(b))}if(e){var d=_pe._prompts[e.promptid];var a=e.getValue();e.show(false);d.applyFilter(e.wty,a)}}function FilterDlg_cancelCB(a){var b=this;if(a){b=_getWidget(_getLayer(a))}if(b){b.show(false)}}function FilterDlg_enterCB(){}function newFilterDlg(g){var l=60;var n=52;var e=300;var m=100;var j=0.9*e;var b="filterDlg"+g;var a=newDlgBox(b,L_SetFilter,e,m,FilterDlg_okCB,FilterDlg_cancelCB,false);a.promptid=g;a.setValue=FilterDlg_setValue;a.getValue=FilterDlg_getValue;a.initDlg=FilterDlg_initDlg;var k=newBtn(b+"_okBtn",L_OK,"FilterDlg_okCB('"+b+"')",l,"OK","OK",0,0);var d=newBtn(b+"_cancelBtn",L_Cancel,"FilterDlg_cancelCB('"+b+"')",l,"Cancel","Cancel",0,0);var f=newTextField(b+"_textFld",null,null,null,FilterDlg_enterCB,true,null,j);_targetApp(a.beginHTML()+'<table cellspacing="0" cellpadding="5" border="0"><tbody><tr><td><table cellspacing="0" cellpadding="0" border="0"><tbody><tr><td><div style="overflow:auto">'+f.getHTML()+'</div></td></tr></tbody></table></td></tr><tr><td align="center" valign="right"></td></tr><tr><td align="right" valign="center"><table cellspacing="0" cellpadding="0" border="0"><tbody><tr><td>'+k.getHTML()+"</td><td>"+_getSpace(5,1)+"</td><td>"+d.getHTML()+"</td></tr></tbody></table></td></tr></table>"+a.endHTML());a.init();k.init();d.init();f.init();a.textField=f;return a}function FilterDlg_setValue(a){var b=this;b.textField.setValue(a)}function FilterDlg_getValue(){var a=this;if(a.textField){return a.textField.getValue()}return null}function FilterDlg_initDlg(a,e){var d=this;if(a+d.getWidth()>_winWidth()+_getScrollX()){a=Math.max(0,_winWidth()+_getScrollX()-d.getWidth()-10)}if(e+d.getHeight()>_winHeight()+_getScrollY()){e=Math.max(0,_winHeight()+getScrollY()-d.getHeight()-10)}d.move(a,e);d.placeIframe(true,true);var b=d.textField;b.select();b.focus()}function newCtl(b){var a=new Object;a.id=b;a.layer=null;a.css=null;a.getHTML=Ctl_getHTML;a.beginHTML=Ctl_getHTML;a.endHTML=Ctl_getHTML;a.write=Ctl_write;a.begin=Ctl_begin;a.end=Ctl_end;a.init=Ctl_init;a.move=Ctl_move;a.resize=Ctl_resize;a.setBgColor=Ctl_setBgColor;a.show=Ctl_show;a.getWidth=Ctl_getWidth;a.getHeight=Ctl_getHeight;a.setHTML=Ctl_setHTML;a.setDisabled=Ctl_setDisabled;a.focus=Ctl_focus;a.setDisplay=Ctl_setDisplay;a.isDisplayed=Ctl_isDisplayed;a.setTooltip=Ctl_setTooltip;a.initialized=Ctl_initialized;a.widx=_pe._widgets.length;_pe._widgets[a.widx]=a;return a}function Ctl_getHTML(){return""}function Ctl_write(b){var a=this.getHTML(b);if(parent.writeSource){parent.writeSource(a)}document.write(a)}function Ctl_begin(){document.write(this.beginHTML())}function Ctl_end(){document.write(this.endHTML())}function Ctl_init(){var a=this;a.layer=_getLayer(a.id);a.css=a.layer.style;a.layer._widget=a.widx;if(a.initialHTML){a.setHTML(a.initialHTML)}}function Ctl_move(a,b){c=this.css;if(a!=null){if(_pe._moz){c.left=""+a+"px"}else{c.pixelLeft=a}}if(b!=null){if(_pe._moz){c.top=""+b+"px"}else{c.pixelTop=b}}}function Ctl_focus(){_safeSetFocus(this.layer)}function Ctl_setBgColor(a){this.css.backgroundColor=a}function Ctl_show(a){this.css.visibility=a?_pe._show:_pe._hide}function Ctl_getWidth(){return this.layer.offsetWidth}function Ctl_getHeight(){return this.layer.offsetHeight}function Ctl_setHTML(a){var b=this;if(b.layer){b.layer.innerHTML=a}else{b.initialHTML=a}}function Ctl_setDisplay(a){this.css.display=a?"":"none"}function Ctl_isDisplayed(){if(this.css.display=="none"){return false}else{return true}}function Ctl_setDisabled(a){if(this.layer){this.layer.disabled=a}}function Ctl_resize(a,b){if(a!=null){this.css.width=""+(Math.max(0,a))+"px"}if(b!=null){this.css.height=""+(Math.max(0,b))+"px"}}function Ctl_setTooltip(a){this.layer.title=a}function Ctl_initialized(){return this.layer!=null}function PE_beginBlocking(){var a=newBlockWidget();a.show(true)}function PE_endBlocking(){var a=_getLayer(_BlockWaitWidgetID);if(a){a.style.display="none"}}function newBlockWidget(){if(window._PEBlockWidget!=null){return window._PEBlockWidget}var a=newCtl(_BlockWaitWidgetID);a.getPrivateHTML=BlockWidget_getPrivateHTML;a.init=BlockWidget_init;a.show=BlockWidget_show;window._PEBlockWidget=a;return a}function BlockWidget_init(){}function BlockWidget_getPrivateHTML(){return'<div id="'+this.id+'" onselectstart="return false" ondragstart="return false" onmousedown="_eventCancelBubble(event)" border="0" hspace="0" vspace="0"  style="background-image:url(\''+_pe._images+'transp.gif\')";z-index:6000;cursor:wait;position:absolute;top:0px;left:0px;width:100%;height:100%"></div>'}function BlockWidget_show(a){var b=this;if(b.layer==null){b.layer=_getLayer(b.id);if(b.layer==null){_targetApp(b.getPrivateHTML());b.layer=_getLayer(b.id);b.css=b.layer.style}else{b.css=b.layer.style}}b.setDisplay(a)}function newBtn(d,p,k,e,g,u,j,l,b,q,n,t,r,m,a,s){var f=newCtl(d);f.label=p;f.cb=k;f.width=e;f.hlp=g;f.tooltip=u;f.tabIndex=j;f.isGray=false;f.txt=null;f.icn=null;f.margin=l?l:0;f.extraStyle="";if(b){f.url=b;f.w=q;f.h=n;f.dx=t;f.dy=r;f.disDx=(a!=null)?a:t;f.disDy=(s!=null)?s:r;f.imgRight=m?true:false}f.getHTML=Btn_getHTML;f.setDisabled=Btn_setDisabled;f.setText=Btn_setText;f.changeImg=Btn_changeImg;f.oldInit=f.init;f.init=Btn_init;f.isDisabled=Btn_isDisabled;f.instIndex=Btn_currInst;Btn_inst[Btn_currInst++]=f;return f}Btn_inst=new Array;Btn_currInst=0;function Btn_getHTML(){with(this){var clk="Btn_clickCB("+this.instIndex+');return false;"';var clcbs='onclick="'+clk+'" ';if(_pe._ie){clcbs+='ondblclick="'+clk+'" '}var url1=_pe._images+"button.gif",addPar=' style="'+extraStyle+"cursor:"+_pe._hand+";margin-left:"+margin+"px; margin-right:"+margin+'px; "'+clcbs+" ",tip=_attr("title",tooltip),idText="theBttn"+id,bg=_backImgOffset(url1,0,42),idIcon="theBttnIcon"+id;var lnkB="<a "+_attr("id",idText)+" "+tip+" "+_attr("tabindex",tabIndex)+' href="javascript:void(0)" class="wizbutton">';var l=(label!=null);var im=(this.url?('<td align="'+(l?(this.imgRight?"right":"left"):"center")+'" style="'+bg+'" width="'+(!l&&(width!=null)?width+6:w+6)+'">'+(l?"":lnkB)+_simpleImgOffset(url,w,h,this.isGray?disDs:dx,this.isGray?disDy:dy,idIcon,null,(l?"":tooltip),"cursor:"+_pe._hand)+(l?"":"</a>")+"</td>"):"");return"<table "+_attr("id",id)+" "+addPar+' border="0" cellspacing="0" cellpadding="0"><tr valign="middle"><td width="5">'+_simpleImgOffset(url1,5,21,0,0)+"</td>"+(this.imgRight?"":im)+(l?("<td "+_attr("width",width)+' align="center" class="'+(this.isGray?"wizbuttongray":"wizbutton")+'" style="padding-left:3px;padding-right:3px;'+bg+'"><nobr>'+lnkB+label+"</a></nobr></td>"):"")+(this.imgRight?im:"")+'<td width="5">'+_simpleImgOffset(url1,5,21,0,21)+"</td></tr></table>"}}function Btn_setDisabled(e){var b=this,a=e?"default":_pe._hand;b.isGray=e;if(b.layer){b.txt.className=e?"wizbuttongray":"wizbutton";b.txt.style.cursor=a;b.css.cursor=a;if(b.icn){_changeSimpleOffset(b.icn,b.isGray?b.disDx:b.dx,b.isGray?b.disDy:b.dy);b.icn.style.cursor=a}}}function Btn_isDisabled(){return this.isGray}function Btn_setText(a){this.txt.innerHTML=convStr(a)}function Btn_init(){var b=this;b.oldInit();b.txt=_getLayer("theBttn"+this.id);b.icn=_getLayer("theBttnIcon"+this.id);var a=b.isGray?"wizbuttongray":"wizbutton";if(b.txt.className!=a){b.txt.className=a}}function Btn_changeImg(b,a,f,e,d,g){var j=this;if(d){j.url=d}if(b!=null){j.dx=b}if(a!=null){j.dy=a}if(f!=null){j.disDx=f}if(e!=null){j.disDy=e}if(g!=null){j.tooltip=g}if(j.icn){_changeSimpleOffset(j.icn,j.isGray?j.disDx:j.dx,j.isGray?j.disDy:j.dy,j.url,j.tooltip)}}function Btn_clickCB(a){var b=Btn_inst[a];if(b&&!b.isGray){setTimeout("Btn_delayClickCB("+a+")",1)}}function Btn_delayClickCB(index){var btn=Btn_inst[index];if(btn.cb){if(typeof btn.cb!="string"){btn.cb()}else{eval(btn.cb)}}}function newTextField(d,j,m,g,l,b,n,e,a,k){var f=newCtl(d);f.tooltip=n;f.changeCB=j;f.maxChar=m;f.keyUpCB=g;f.enterCB=l;f.noMargin=b;f.width=e==null?null:""+e+"px";f.focusCB=a;f.blurCB=k;f.getHTML=TextField_getHTML;f.getValue=TextField_getValue;f.setValue=TextField_setValue;f.intValue=TextField_intValue;f.intPosValue=TextField_intPosValue;f.select=TextField_select;f.beforeChange=null;f.wInit=f.init;f.init=TextField_init;f.oldValue="";return f}function TextField_init(){var a=this;a.wInit();a.layer.value=""+a.oldValue}function TextField_getHTML(){return'<input oncontextmenu="event.cancelBubble=true;return true" style="'+_sty("width",this.width)+(_pe._moz?"padding-left:3px;padding-right:3px;":"")+"margin-left:"+(this.noMargin?0:10)+'px" onfocus="TextField_focus(this)" onblur="TextField_blur(this)" onchange="TextField_changeCB(event,this)" onkeyup="TextField_keyUpCB(event,this);return true" type="text" '+_attr("maxLength",this.maxChar)+' ondragstart="event.cancelBubble=true;return true" onselectstart="event.cancelBubble=true;return true" class="textinputs" id="'+this.id+'" name="'+this.id+'"'+_attr("title",this.tooltip)+' value="">'}function TextField_getValue(){return this.layer.value}function TextField_setValue(a){if(this.layer){this.layer.value=""+a}else{this.oldValue=a}}function TextField_changeCB(b,a){var d=_getWidget(a);if(d.beforeChange){d.beforeChange()}if(d.changeCB){d.changeCB(b)}}function TextField_keyUpCB(b,a){var d=_getWidget(a);if(_eventGetKey(b)==13){if(d.beforeChange){d.beforeChange()}if(d.enterCB){d.enterCB(b)}return false}else{if(d.keyUpCB){d.keyUpCB(b);return true}}}function TextField_focus(a){var b=_getWidget(a);if(b.focusCB){b.focusCB()}}function TextField_blur(a){var b=_getWidget(a);if(b.beforeChange){b.beforeChange()}if(b.blurCB){b.blurCB()}}function TextField_intValue(a){var b=parseInt(this.getValue());return isNaN(b)?a:b}function TextField_intPosValue(a){var b=this.intValue(a);return(b<0)?a:b}function TextField_select(){this.layer.select()}function newDlgBox(k,g,e,a,b,j,d){var f=newCtl(k);f.title=g;f.width=e;f.height=a;f.defaultCB=b;f.cancelCB=j;f.noCloseButton=d?d:false;f.resizeable=false;f.oldKeyPress=null;f.oldMouseDown=null;f.oldCurrent=null;f.modal=null;f.hiddenVis=new Array;f.lastLink=null;f.firstLink=null;f.titleLayer=null;f.oldInit=f.init;f.oldShow=f.show;f.init=DlgBox_init;f.setResize=DlgBox_setResize;f.beginHTML=DlgBox_beginHTML;f.endHTML=DlgBox_endHTML;f.show=DlgBox_Show;f.center=DlgBox_center;f.focus=DlgBox_focus;f.setTitle=DlgBox_setTitle;f.getContainerWidth=DlgBox_getContainerWidth;f.getContainerHeight=DlgBox_getContainerHeight;_pe.DlgBox_instances[k]=f;f.modal=newCtl("modal_"+k);f.placeIframe=DlgBox_placeIframe;f.oldResize=f.resize;f.resize=DlgBox_resize;return f}function DlgBox_setResize(a,d,e,b,g){var f=this;f.resizeable=true;f.resizeCB=a;f.minWidth=d?d:50;f.minHeight=e?e:50;f.noResizeW=b;f.noResizeH=g}function DlgBox_setTitle(b){var a=this;a.title=b;if(a.titleLayer==null){a.titleLayer=_getLayer("titledialog_"+this.id)}a.titleLayer.innerHTML=_convStr(b)}function DlgBox_setCloseIcon(a,b){_changeOffset(a,0,(b==1?0:18))}function DlgBox_beginHTML(){with(this){var moveableCb=' onselectstart="return false" ondragstart="return false" onmousedown="DlgBox_down(event,\''+id+"',this,false);return false;\" ";var mdl=_pe._ie?('<img onselectstart="return false" ondragstart="return false" onmousedown="_eventCancelBubble(event)" border="0" hspace="0" vspace="0" src="'+_pe._images+'transp.gif" id="modal_'+id+'" style="display:none;position:absolute;top:0px;left:0px;width:1px;height:1px">'):('<div onselectstart="return false" ondragstart="return false" onmousedown="_eventCancelBubble(event)" border="0" hspace="0" vspace="0" src="'+_pe._images+'transp.gif" id="modal_'+id+'" style="position:absolute;top:0px;left:0px;width:1px;height:1px"></div>');var titleBG="background-image:url('"+_pe._images+"dialogtitle.gif')";return mdl+'<a style="position:absolute;left:-30px;top:-30px; visibility:hidden" id="firstLink_'+this.id+'" href="javascript:void(0)" onfocus="DlgBox_keepFocus(\''+this.id+"');return false;\" ></a>"+_getBGIframe("dlgIF_"+id)+'<table  border="0" cellspacing="0" cellpadding="2" id="'+id+'" class="dialogbox" style="display:none;padding:0px;visibility:'+_pe._hide+";position:absolute;top:-2000px;left:-2000px;"+_sty("width",width?(""+width+"px"):null)+_sty("height",height?(""+height+"px"):null)+'"><tr><td id="dlgFirstTr_'+id+'" valign="top"><table width="100%" border="0" cellspacing="0" cellpadding="0"><tr valign="top"><td '+moveableCb+' style="cursor:move;'+titleBG+'" class="titlezone">'+_getSpace(5,18)+"</td><td "+moveableCb+' style="cursor:move;'+titleBG+'" class="titlezone" width="100%" valign="middle" align="left"><nobr><span id="titledialog_'+id+'" tabIndex="0" class="titlezone">'+_convStr(title)+'</span></nobr></td><td class="titlezone" style="'+titleBG+'">'+(noCloseButton?"":'<a href="javascript:void(0)" onclick="DlgBox_close(\''+id+'\');return false;" title="'+L_closeDialog+'">'+_imgOffset(_pe._images+"dialogelements.gif",18,18,0,18,"dialogClose_"+this.id,'onmouseover="DlgBox_setCloseIcon(this,1)" onmouseout="DlgBox_setCloseIcon(this,0)" ',L_closeDialog,"cursor:"+_pe._hand)+"</a>")+'</td></tr></table></td></tr><tr valign="top" height="100%"><td id="dlgSecTr_'+id+'" >'}}function DlgBox_endHTML(){var b=' onselectstart="return false" ondragstart="return false" onmousedown="DlgBox_down(event,\''+this.id+"',this,true);return false;\" ";var a=this.resizeable?('<tr  onselectstart="return false" height="18" valign="bottom" align="right"><td>'+_img(_pe._images+"resize.gif",14,14,null,b+' style="cursor:NW-resize" ')+"</td></tr>"):"";return"</td></tr>"+a+'</table><a style="position:absolute;left:-30px;top:-30px; visibility:hidden" id="lastLink_'+this.id+'" href="javascript:void(0)" onfocus="DlgBox_keepFocus(\''+this.id+"');return false;\" ></a>"}function DlgBox_getContainerWidth(){var a=this;return a.width-(2+2)}function DlgBox_getContainerHeight(){var a=this;return a.height-(2+18+2+2+2)}function DlgBox_close(b){var a=_pe.DlgBox_instances[b];if(a){a.show(false);if(a.cancelCB!=null){a.cancelCB()}}}function DlgBox_resizeIframeCB(a){_pe.DlgBox_instances[a].placeIframe(true,false)}function DlgBox_placeIframe(b,a){var d=this;if(d.iframe){if(b){d.iframe.resize(d.getWidth(),d.getHeight())}if(a){d.iframe.move(d.layer.offsetLeft,d.layer.offsetTop)}}}function DlgBox_resize(a,b){var d=this;d.oldResize(a,b);if(d.iframe){d.iframe.resize(a,b);if(d.firstTR){if(a!=null){d.firstTR.style.width=a-4}if(b!=null){d.secondTR.style.height=b-44}}}}function DlgBox_init(){if(this.layer!=null){return}var a=this;a.oldInit();a.modal.init();a.lastLink=newCtl("lastLink_"+a.id);a.firstLink=newCtl("firstLink_"+a.id);a.lastLink.init();a.firstLink.init();if(!a.noCloseButton){a.closeButton=_getLayer("dialogClose_"+a.id);DlgBox_setCloseIcon(a.closeButton,false)}if(_pe._moz&&!_pe._saf){a.firstTR=_getLayer("dlgFirstTr_"+a.id);a.secondTR=_getLayer("dlgSecTr_"+a.id)}a.iframe=newCtl("dlgIF_"+a.id);a.iframe.init()}function DlgBox_down(e,id,obj,isResize){_pe._dlgResize=isResize;var o=_pe.DlgBox_instances[id],lyr=o.layer,mod=o.modal.layer;lyr.onmousemove=mod.onmousemove=eval("DlgBox_move");lyr.onmouseup=mod.onmouseup=eval("DlgBox_up");lyr.dlgStartPosx=mod.dlgStartPosx=parseInt(lyr.style.left);lyr.dlgStartPosy=mod.dlgStartPosy=parseInt(lyr.style.top);lyr.dlgStartx=mod.dlgStartx=_eventGetX(e);lyr.dlgStarty=mod.dlgStarty=_eventGetY(e);lyr.dlgStartw=mod.dlgStartw=o.getWidth();lyr.dlgStarth=mod.dlgStarth=o.getHeight();lyr._widget=mod._widget=o.widx;_pe._theLYR=lyr;_eventCancelBubble(e);if(lyr.setCapture){lyr.setCapture(true)}}function DlgBox_move(d){var g=_pe._theLYR,f=_getWidget(g);if(f){if(_pe._dlgResize){var k=Math.max(f.minWidth,g.dlgStartw+_eventGetX(d)-g.dlgStartx);var b=Math.max(f.minHeight,g.dlgStarth+_eventGetY(d)-g.dlgStarty);f.resize(f.noResizeW?null:k,f.noResizeH?null:b);if(f.firstTR){if(!f.noResizeW){f.firstTR.style.width=k-4}if(!f.noResizeH){f.secondTR.style.height=b-44}}if(f.resizeCB){f.resizeCB(k,b)}}else{var a=Math.max(0,g.dlgStartPosx-g.dlgStartx+_eventGetX(d));var j=Math.max(0,g.dlgStartPosy-g.dlgStarty+_eventGetY(d));a=Math.min(Math.max(10,_winWidth()-10),a);j=Math.min(Math.max(10,_winHeight()-18),j);f.iframe.move(a,j);f.move(a,j)}}_eventCancelBubble(d);return false}function DlgBox_up(d){var f=_getWidget(_pe._theLYR),a=f.layer,b=f.modal.layer;a.onmousemove=b.onmousemove=null;a.onmouseup=b.onmouseup=null;if(a.releaseCapture){a.releaseCapture()}_pe._theLYR=null}function DlgBox_keypress(b){var d=_pe.DlgBox_current;if(d!=null){switch(_eventGetKey(b)){case 13:var a=_pe._ie?window.event.srcElement.id:b.target.id;if((a=="insRepText"||a=="renRepText")&&(d.defaultCB!=null)){d.defaultCB();return false}if(d.yes&&!d.no){d.defaultCB();return false}break;case 27:d.show(false);if(d.cancelCB!=null){d.cancelCB()}return false;break;case 8:return _isTextInput(_pe._ie?window.event:b);break}}}function DlgBoxResizeModals(b){for(var a in _pe.DlgBox_modals){m_sty=_pe.DlgBox_modals[a];m_sty.width=_documentWidth();m_sty.height=_documentHeight()}}function DlgBox_center(){var d=this,a=_getScrollY(),b=_getScrollX();d.height=d.layer.offsetHeight;d.width=d.layer.offsetWidth;d.move(Math.max(0,b+(_winWidth()-d.width)/2),Math.max(0,a+(_winHeight()-d.height)/2));d.placeIframe(true,true)}function DlgBox_Show(sh){with(this){m_sty=modal.css;l_sty=css;if(sh){oldCurrent=_pe.DlgBox_current;_pe.DlgBox_current=this;if(_pe._ie){oldKeyPress=document.onkeydown;document.onkeydown=eval("window.DlgBox_keypress")}else{document.addEventListener("keydown",eval("window.DlgBox_keypress"),false)}oldMouseDown=document.onmousedown;document.onmousedown=null;_disableAllInputs()}else{_pe.DlgBox_current=oldCurrent;oldCurrent=null;if(_pe._ie){document.onkeydown=oldKeyPress}else{document.removeEventListener("keydown",eval("window.DlgBox_keypress"),false)}document.onmousedown=oldMouseDown;_restoreAllDisabledInputs()}var sameState=(layer.isShown==sh);if(sameState){return}layer.isShown=sh;if(sh){if(window.DialogBoxWidget_zindex==null){window.DialogBoxWidget_zindex=1000}this.iframe.css.zIndex=window.DialogBoxWidget_zindex++;m_sty.zIndex=window.DialogBoxWidget_zindex++;l_sty.zIndex=window.DialogBoxWidget_zindex++;_pe.DlgBox_modals[_pe.DlgBox_modals.length]=m_sty;m_sty.display="";l_sty.display="block";this.iframe.setDisplay(true);DlgBoxResizeModals();this.height=layer.offsetHeight;this.width=layer.offsetWidth;if(_isHidden(layer)){this.center()}if(this.firstTR){this.firstTR.style.width=this.getWidth()-4;this.secondTR.style.height=this.getHeight()-44}if(this.resizeCB){this.resizeCB(this.width,this.height)}}else{var l=_pe.DlgBox_modals.length=Math.max(0,_pe.DlgBox_modals.length-1);m_sty.width="1px";m_sty.height="1px";m_sty.display="none";l_sty.display="none";move(-2000,-2000);this.iframe.setDisplay(false)}modal.show(sh);firstLink.show(sh);lastLink.show(sh);oldShow(sh);if(_pe.DlgBox_current!=null&&sh==true){_pe.DlgBox_current.focus()}}}function DlgBox_keepFocus(b){var a=_pe.DlgBox_instances[b];if(a){a.focus()}}function DlgBox_focus(){with(this){if(titleLayer==null){titleLayer=_getLayer("titledialog_"+id)}if(titleLayer.focus){titleLayer.focus()}}}var isJava=false;var isNetscape=navigator.appName.indexOf("Netscape")!=-1;var LEFT_ARROW_KEY=37;var RIGHT_ARROW_KEY=39;var ENTER_KEY=13;function promptengine_encodePrompt(a){if(isJava){return encodeURIComponent(a)}else{return promptengine_urlEncode(a)}}function promptengine_addDiscreteValue(e,l,k){var b=document.getElementById(e);var j=document.getElementById(k+"DiscreteValue");var a=j;var n=j.type.toLowerCase();var m=false;if(n!="text"&&n!="hidden"&&n!="password"){a=j.options[j.selectedIndex];m=true}var d=a.value;if(!promptengine_checkValue(d,l)){_safeSetFocus(j);return false}var f=document.getElementById(k+"ListBox");PE_clearSel(f);var g=promptengine_findOptionInList(f,d);if(g<0){g=f.length;f.options[g]=new Option(((a.text)?a.text:d),d,false,false)}f.options[g].selected=true;_safeSetFocus(j);if(j.select){j.select()}if(m&&j.selectedIndex<j.length-1){j.selectedIndex=j.selectedIndex+1}}function PE_clearSel(b){var a=0,d=b.length;if(b.type=="select-one"){a=b.selectedIndex;if(a<0){return}d=a+1}while(a<d){b.options[a++].selected=false}}function promptengine_addValueFromPickList(d,b,a){return PE_addValues(d,b,a,false)}function promptengine_addAllValues(d,b,a){return PE_addValues(d,b,a,true)}function PE_addValues(d,f,p,e){var r=document.getElementById(p+"AvailableList");var x=document.getElementById(p+"ListBox");var g=r.length;if(g==0){return false}var m=x.length;var j=r.options;var o=x.options;var q=new Array(g);var u=new Array(m);var s=false;var b=-1;for(var t=0;t<g;t++){if(e||j[t].selected){var n=j[t].value;var y=promptengine_findOptionInList(x,n,j[t].text);if(y<0){q[t]=n}else{u[y]=n}s=true;if(!e){b=t}}}if(!s){return false}var l=PE_getLB(x);for(var t=0;t<m;t++){var a=o[t];l.add(a.value,a.text,u[t]!=null)}var k=false;for(var t=0;t<g;t++){if(q[t]){var a=j[t];l.add(a.value,a.text,true);k=true}}l.update();if(!e&&b>=0&&b+1<g){PE_clearSel(r);j[b+1].selected=true}return k}function promptengine_addRangeValue(a,f,n){var d=document.getElementById(n+"SelectLowerRangeValue");var l=document.getElementById(n+"SelectUpperRangeValue");lowerBound=document.getElementById(n+"LowerBound");upperBound=document.getElementById(n+"UpperBound");if(lowerBound.type.toLowerCase()!="text"&&lowerBound.type.toLowerCase()!="hidden"&&lowerBound.type.toLowerCase()!="password"){lowerBound=lowerBound.options[lowerBound.selectedIndex];upperBound=upperBound.options[upperBound.selectedIndex]}lowerUnBounded=document.getElementById(n+"NoLBoundCheck").checked;upperUnBounded=document.getElementById(n+"NoUBoundCheck").checked;lvalue=uvalue="";if(!lowerUnBounded){if(!promptengine_checkRangeBoundValue(lowerBound.value,f)){if(lowerBound.focus&&lowerBound.type.toLowerCase()!="hidden"){lowerBound.focus()}return false}lvalue=lowerBound.value}if(!upperUnBounded){if(!promptengine_checkRangeBoundValue(upperBound.value,f)){if(upperBound.focus&&upperBound.type.toLowerCase()!="hidden"){upperBound.focus()}return false}uvalue=upperBound.value}var b="";var e="";var o=false;if(d!=null&&lvalue!=null&&lvalue.length>0){var k=d.length;for(var m=0;m<k;m++){var j=d.options[m].value;if(j!=null&&j.length>0&&j==lvalue){b=d.options[m].text;o=true;break}}}if(!o){b=(lowerBound.text&&!lowerUnBounded)?lowerBound.text:lvalue}o=false;if(l!=null&&uvalue!=null&&uvalue.length>0){var k=l.length;for(var m=0;m<k;m++){var j=l.options[m].value;if(j!=null&&j==uvalue){e=l.options[m].text;o=true;break}}}if(!o){e=(upperBound.text&&!upperUnBounded)?upperBound.text:uvalue}lowerChecked=document.getElementById(n+"LowerCheck").checked;upperChecked=document.getElementById(n+"UpperCheck").checked;j=(lowerChecked&&!lowerUnBounded)?"[":"(";if(!lowerUnBounded){j+=(lvalue)}j+="_crRANGE_";if(!upperUnBounded){j+=(uvalue)}j+=(upperChecked&&!upperUnBounded)?"]":")";display=(lowerChecked&&!lowerUnBounded)?"[":"(";display+=b;display+=" .. ";display+=e;display+=(upperChecked&&!upperUnBounded)?"]":")";promptEntry=new Option(display,j,false,false);theList=document.getElementById(n+"ListBox");var g=promptengine_findOptionInList(theList,j);if(g>-1){theList.selectedIndex=g}else{theList.options[theList.length]=promptEntry}return true}function promptengine_findOptionInList(f,d){if(f==null||d==null){return -1}var e=f.length,b=f.options;for(var a=0;a<e;a++){if(b[a].value==d){return a}}return -1}function promptengine_onNoBoundCheckClicked(b,a,d){if(d==0){if(document.getElementById(a+"NoLBoundCheck").checked){document.getElementById(a+"NoUBoundCheck").disabled=true;document.getElementById(a+"LowerCheck").disabled=true;document.getElementById(a+"LowerBound").disabled=true;if(document.getElementById(a+"SelectLowerRangeValue")!=null){document.getElementById(a+"SelectLowerRangeValue").disabled=true}}else{document.getElementById(a+"NoUBoundCheck").disabled=false;document.getElementById(a+"LowerCheck").disabled=false;document.getElementById(a+"LowerBound").disabled=false;if(document.getElementById(a+"SelectLowerRangeValue")!=null){document.getElementById(a+"SelectLowerRangeValue").disabled=false}}}else{if(d==1){if(document.getElementById(a+"NoUBoundCheck").checked){document.getElementById(a+"NoLBoundCheck").disabled=true;document.getElementById(a+"UpperCheck").disabled=true;document.getElementById(a+"UpperBound").disabled=true;if(document.getElementById(a+"SelectUpperRangeValue")!=null){document.getElementById(a+"SelectUpperRangeValue").disabled=true}}else{document.getElementById(a+"NoLBoundCheck").disabled=false;document.getElementById(a+"UpperCheck").disabled=false;document.getElementById(a+"UpperBound").disabled=false;if(document.getElementById(a+"SelectUpperRangeValue")!=null){document.getElementById(a+"SelectUpperRangeValue").disabled=false}}}}}function promptengine_onSetNullCheckClicked(b,a){if(document.getElementById(a+"NULL").checked){if(document.getElementById(a+"DiscreteValue")!=null){document.getElementById(a+"DiscreteValue").disabled=true}if(document.getElementById(a+"SelectValue")!=null){document.getElementById(a+"SelectValue").disabled=true}}else{if(document.getElementById(a+"DiscreteValue")!=null){document.getElementById(a+"DiscreteValue").disabled=false}if(document.getElementById(a+"SelectValue")!=null){document.getElementById(a+"SelectValue").disabled=false}}}function promptengine_selectValue(b,a,e){if(document.getElementById(a).selectedIndex<0){return false}selectedOption=document.getElementById(a).options[document.getElementById(a).selectedIndex];if(selectedOption.value==null&&document.getElementById(e).value==null){return false}var d=true;if(selectedOption.value==document.getElementById(e).value){d=false}document.getElementById(e).value=selectedOption.value;return d}function promptengine_hasValueInTextBox(b,a){if(document.getElementById(a).value==null){return false}return true}function promptengine_setCascadingPID(d,a,b){valueField=document.getElementById(a);curVal=valueField.value;if(curVal.length>0){curVal+="&"}curVal+="cascadingPID="+b;valueField.value=curVal;return true}function PE_removeValue(d,j,n){var g=document.getElementById(j+"ListBox");var a=g.options;var l=g.length;if(l==0){return false}var f=false;var m=-1;var k=PE_getLB(g);for(var e=0;e<l;e++){if(!n){var b=a[e];if(!b.selected){k.add(b.value,b.text);continue}m=e}f=true}if(!f){return false}k.update();if(m>=0){g=document.getElementById(j+"ListBox");if(m<g.length){g.options[m].selected=true}else{if(m==g.length&&m>0){g.options[m-1].selected=true}}}return true}function promptengine_removeValue(b,a){return PE_removeValue(b,a,false)}function promptengine_onRemoveValue(b,a){promptengine_removeValue(b,a)}function promptengine_removeAllValues(b,a){return PE_removeValue(b,a,true)}function promptengine_onRemoveAllValues(b,a){promptengine_removeAllValues(b,a)}function promptengine_updateValueField(d,a,b,e){valueField=document.getElementById(a);curVal=valueField.value;if(curVal.length>0){curVal+="&"}var f=promptengine_encodeValueField(e);curVal+=b+"="+f;valueField.value=curVal;return true}function promptengine_resetValueField(b,a){valueField=document.getElementById(a);valueField.value=""}function promptengine_updateDiscreteValue(j,a,b,f,e,g){var k="";if(document.getElementById(b+"NULL")!=null&&document.getElementById(b+"NULL").checked){k="_crNULL_"}else{valueField=document.getElementById(b+"DiscreteValue");if(valueField.type.toLowerCase()!="text"&&valueField.type.toLowerCase()!="hidden"&&valueField.type.toLowerCase()!="password"){k=valueField.options[valueField.selectedIndex].value}else{k=valueField.value}if(!g&&(k==null||k.length==0)){return promptengine_updateValueField(j,a,b,"")}if(e&&!promptengine_checkValue(k,f)){if(valueField.focus&&valueField.type.toLowerCase()!="hidden"){valueField.focus()}else{var d=document.getElementById(b+"SelectValue");if(d!=null&&d.focus){d.focus()}}return false}}return promptengine_updateValueField(j,a,b,k)}function promptengine_updateRangeValue(j,a,b,f,e,g){if(document.getElementById(b+"NULL")!=null&&document.getElementById(b+"NULL").checked){value="_crNULL_"}else{lowerBound=document.getElementById(b+"LowerBound");upperBound=document.getElementById(b+"UpperBound");if(lowerBound.type.toLowerCase()!="text"&&lowerBound.type.toLowerCase()!="hidden"&&lowerBound.type.toLowerCase()!="password"){lowerBound=lowerBound.options[lowerBound.selectedIndex];upperBound=upperBound.options[upperBound.selectedIndex]}lowerUnBounded=document.getElementById(b+"NoLBoundCheck").checked;upperUnBounded=document.getElementById(b+"NoUBoundCheck").checked;lowerChecked=document.getElementById(b+"LowerCheck").checked;upperChecked=document.getElementById(b+"UpperCheck").checked;uvalue=lvalue="";if(!g&&(lowerBound.value==null||lowerBound.value.length==0||lowerUnBounded)&&(upperBound.value==null||upperBound.value.length==0||upperUnBounded)){return promptengine_updateValueField(j,a,b,"")}if(!lowerUnBounded){if(e&&!promptengine_checkRangeBoundValue(lowerBound.value,f)){if(lowerBound.focus&&lowerBound.type.toLowerCase()!="hidden"){lowerBound.focus()}else{var d=document.getElementById(b+"SelectLowerRangeValue");if(d!=null&&d.focus){d.focus()}}return false}lvalue=lowerBound.value}if(!upperUnBounded){if(e&&!promptengine_checkRangeBoundValue(upperBound.value,f)){if(upperBound.focus&&upperBound.type.toLowerCase()!="hidden"){upperBound.focus()}else{var d=document.getElementById(b+"SelectUpperRangeValue");if(d!=null&&d.focus){d.focus()}}return false}uvalue=upperBound.value}value=(lowerChecked&&!lowerUnBounded)?"[":"(";if(!lowerUnBounded){value+=lvalue}value+="_crRANGE_";if(!upperUnBounded){value+=uvalue}value+=(upperChecked&&!upperUnBounded)?"]":")"}return promptengine_updateValueField(j,a,b,value)}function promptengine_updateMultiValue(j,a,b,f,e,g){values=document.getElementById(b+"ListBox").options;value="";if(document.getElementById(b+"NULL")!=null&&document.getElementById(b+"NULL").checked){value="_crNULL_"}else{if(values.length==0){if(e&&g){var d=document.getElementById(b+"ListBox");if(d!=null&&d.focus){d.focus()}return false}value="_crEMPTY_"}else{for(i=0;i<values.length;i++){if(i!=0){value+="_crMULT_"}value+=values[i].value}}}return promptengine_updateValueField(j,a,b,value)}var regNumber=/^(\+|-)?((\d+(\.|,|'| |\xA0)?\d*)+|(\d*(\.|,| |\xA0)?\d+)+)$/;var regCurrency=regNumber;var regDate=/^(D|d)(A|a)(T|t)(E|e) *\( *\d{4} *, *(0?[1-9]|1[0-2]) *, *((0?[1-9]|[1-2]\d)|3(0|1)) *\)$/;var regDateTime=/^(D|d)(A|a)(T|t)(E|e)(T|t)(I|i)(M|m)(E|e) *\( *\d{4} *, *(0?[1-9]|1[0-2]) *, *((0?[1-9]|[1-2]\d)|3(0|1)) *, *([0-1]?\d|2[0-3]) *, *[0-5]?\d *, *[0-5]?\d *\)$/;var regTime=/^(T|t)(I|i)(M|m)(E|e) *\( *([0-1]?\d|2[0-3]) *, *[0-5]?\d *, *[0-5]?\d *\)$/;var regDateTimeHTML=/^ *\d{4} *- *(0?[1-9]|1[0-2]) *- *((0?[1-9]|[1-2]\d)|3(0|1)) *  *([0-1]?\d|2[0-3]) *: *[0-5]?\d *: *[0-5]?\d *$/;var regDateHTML=/^ *\d{4} *- *(0?[1-9]|1[0-2]) *- *((0?[1-9]|[1-2]\d)|3(0|1)) *$/;var regTimeHTML=/^ *([0-1]?\d|2[0-3]) *: *[0-5]?\d *: *[0-5]?\d *$/;function promptengine_getDateSpec(){var a=promptengine_getDatePattern();a=a.replace("Y",L_YYYY);a=a.replace("M",L_MM);a=a.replace("D",L_DD);return a}function promptengine_checkValue(f,d){if(f==null){return false}if(f=="_crNULL_"){return true}if(d==_pe._nm&&!regNumber.test(f)){if(f.length>0){alert(L_BadNumber)}else{alert((typeof L_Empty)!="undefined"?L_Empty:L_NoValue)}return false}else{if(d==_pe._cy&&!regCurrency.test(f)){if(f.length>0){alert(L_BadCurrency)}else{alert((typeof L_Empty)!="undefined"?L_Empty:L_NoValue)}return false}else{if(d==_pe._da){var e=promptengine_getDateRegex();if((e==null||!e.test(f))&&!regDate.test(f)&&!regDateHTML.test(f)){if(f.length>0){var a=L_BadDate.replace("%1",promptengine_getDateSpec());alert(a)}else{alert((typeof L_Empty)!="undefined"?L_Empty:L_NoValue)}return false}}else{if(d==_pe._dt){var e=promptengine_getDateTimeRegex();if((e==null||!e.test(f))&&!regDateTime.test(f)&&!regDateTimeHTML.test(f)){if(f.length>0){var b=L_BadDateTime.replace("%1",promptengine_getDateSpec());alert(b)}else{alert((typeof L_Empty)!="undefined"?L_Empty:L_NoValue)}return false}}else{if(d==_pe._tm&&!regTime.test(f)&&!regTimeHTML.test(f)){if(f.length>0){alert(L_BadTime)}else{alert((typeof L_Empty)!="undefined"?L_Empty:L_NoValue)}return false}}}}}return true}function promptengine_checkRangeBoundValue(b,a){if(b==null||b.length==0){alert((typeof L_Empty)!="undefined"?L_Empty:L_NoValue);return false}return promptengine_checkValue(b,a)}function promptengine_isSubmitEvent(d){var a=false;if(isNetscape){if(d.which==ENTER_KEY&&(d.target.type=="text"||d.target.type=="password")){a=true}}else{if(window.event.keyCode==ENTER_KEY&&(window.event.srcElement.type=="text"||window.event.srcElement.type=="password")){a=true}}if(a){_eventCancelBubble(d)}return a}function promptengine_isEnterKey(d){var a=false;if(isNetscape){if(d.which==ENTER_KEY&&d.target.tagName.toLowerCase()!="a"){a=true}}else{if(window.event.keyCode==ENTER_KEY&&window.event.srcElement.tagName.toLowerCase()!="a"){a=true}}if(a){_eventCancelBubble(d)}return a}function promptengine_urlEncode(a){var d=new String("");for(var b=0;b<a.length;b++){var e=a.charAt(b);switch(e){case"%":d+="%25";break;case"+":d+="%2B";break;case" ":d+="%20";break;case"<":d+="%3C";break;case">":d+="%3E";break;case'"':d+="%22";break;case"'":d+="%27";break;case"#":d+="%23";break;case"{":d+="%7B";break;case"}":d+="%7D";break;case"|":d+="%7C";break;case"\\":d+="%5C";break;case"^":d+="%5E";break;case"~":d+="%7E";break;case"`":d+="%60";break;case"[":d+="%5B";break;case"]":d+="%5D";break;case";":d+="%3B";break;case"/":d+="%2F";break;case"?":d+="%3F";break;case":":d+="%3A";break;case"@":d+="%40";break;case"=":d+="%3D";break;case"&":d+="%26";break;default:d+=e;break}}return d}function promptengine_CancelRightClick(a){if(isNetscape){if(a.target.type!="text"&&a.target.type!="textarea"){a.preventDefault();a.cancelBubble=true;return true}}else{if(window.event.srcElement.type!="text"&&window.event.srcElement.type!="textarea"){window.event.cancelBubble=true;window.event.returnValue=false}}}function promptengine_showHidePromptByKey(b,e,j,g,a){var d=false;var f=document.getElementById(b);if(f==null){return}if(isNetscape){if((a.which==LEFT_ARROW_KEY&&f.style.display=="")||(a.which==RIGHT_ARROW_KEY&&f.style.display=="none")){d=true}}else{if((window.event.keyCode==LEFT_ARROW_KEY&&f.style.display=="")||(window.event.keyCode==RIGHT_ARROW_KEY&&f.style.display=="none")){d=true}}if(d==true){promptengine_showHidePrompt(b,e,j,g,a)}}function promptengine_showHidePrompt(b,d,j,g,a){var f;f=document.getElementById(d);if(f!=null&&b!=null){if(!f.origImage){f.origImage=f.src}var e=document.getElementById(b);if(e!=null){if(e.style.display==""){e.style.display="none"}else{e.style.display=""}if(!f.changed||f.changed==false){f.src=g;f.changed=true}else{f.src=j;f.changed=false}}}}function promptengine_scrollTo(e,g,f){if(!e){return}var a=_getScrollY(),d=_getScrollX();if(g==1){var j=e.form.offsetHeight,b=e.form.clientHeight,k=_getPos(e,e.form).y;if(f==0){window.scrollTo(d,k)}else{if(k<a){window.scrollTo(d,k)}else{if(k+j>a+b){window.scrollTo(d,Math.max(k,k+j-b))}}}}else{if(e.form){var j=e.form.offsetHeight,b=e.form.clientHeight,k=_getPos(e,e.form).y;e.form.scrollLeft=d;if(f==0){e.form.scrollTop=0}else{if(k<a){e.form.scrollTop=k}else{if(k+j>a+b){e.form.scrollTop=Math.max(k,k+j-b)}}}}else{var j=e.offsetHeight,b=_winHeight(),k=_getPos(e).y;if(f==0){window.scrollTo(d,k)}else{if(k<a){window.scrollTo(d,k)}else{if(k+j>a+b){window.scrollTo(d,Math.max(k,k+j-b))}}}}}}function doNothing(){}function promptengine_anchorOnKeyPress(d){var a=d?d:window.event;var b=a.srcElement?a.srcElement:a.target;if(a.keyCode==13&&b.onclick){b.onclick.apply(b,[d])}return true}function promptengine_encodeUTF8(b){var a=[];var f=b.length;for(var e=0;e<f;e++){var g=b.charCodeAt(e);if(g<128){a.push(g)}else{if(g<2048){a.push((g>>6)|192);a.push(g&63|128)}else{if(g<55296||g>=57344){a.push((g>>12)|224);a.push((g>>6)&63|128);a.push(g&63|128)}else{if(g<56320){var d=b.charCodeAt(e+1);if(isNaN(d)||d<56320||d>=57344){a.push(239,191,189);continue}e++;val=((g&1023)<<10)|(d&1023);val+=65536;a.push((val>>18)|240);a.push((val>>12)&63|128);a.push((val>>6)&63|128);a.push(val&63|128)}else{a.push(239,191,189)}}}}}return a}function promptengine_encodeBASE64(a){var b="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";var j=[];var g,e,d,o,m,l,k;var f=0,n=a.length;while(f<n){g=a[f++];e=a[f++];d=a[f++];o=g>>2;m=((g&3)<<4)|(e>>4);l=((e&15)<<2)|(d>>6);k=d&63;if(isNaN(e)){l=k=64}else{if(isNaN(d)){k=64}}j.push(b.charAt(o));j.push(b.charAt(m));j.push(b.charAt(l));j.push(b.charAt(k))}return j.join("")}function promptengine_encodeValueField(a){return promptengine_encodePrompt(promptengine_encodeBASE64(promptengine_encodeUTF8(a)))}if(typeof(bobj)=="undefined"){bobj={}}if(typeof(bobj.prompt)=="undefined"){bobj.prompt={}}bobj.prompt.Calendar=function(e,b,a,d){this.locale=a;this.crystalreportviewerPath=d+"/../";this.loadFiles();this.formName=e;this.dateFormat=b;this.dateTimeFormat=b+" H:mm:ss";this.isDateTime=false};bobj.prompt.Calendar.prototype={show:function(d,a){this.calendar=bobj.crv.Calendar.getInstance();this.input=document.getElementById(a);var b=d.target?d.target:d.srcElement;var f=this._getPosition(b);this._setValue(this.input.value);this._setSignals(true);this.calendar.setShowTime(this.isDateTime);this.calendar.show(true,f.x,f.y)},setIsDateTime:function(a){this.isDateTime=a},_getPosition:function(b){var a={x:0,y:0};var f={x:0,y:0};var d=b;while(d!=null){f.x+=d.offsetLeft;f.y+=d.offsetTop;d=d.offsetParent}var d=b;while(d!=null&&d.tagName.toLowerCase()!="body"){a.x+=d.scrollLeft;a.y+=d.scrollTop;d=d.parentNode}return{x:f.x-a.x,y:f.y-a.y}},_setValue:function(b){var a=this._getDateValue(b);if(!a){a=new Date()}this.calendar.setDate(a)},_onOkayClick:function(a){this._setFieldValue(a)},_setFieldValue:function(a){if(this.input){this.input.value=this._getStringValue(a)}},_onHide:function(){this._removeSignals()},_getStringValue:function(a){var b=this.isDateTime?this.dateTimeFormat:this.dateFormat;return bobj.external.date.formatDate(a,b)},_getDateValue:function(a){var b=this.isDateTime?this.dateTimeFormat:this.dateFormat;return bobj.external.date.getDateFromFormat(a,b)},_setSignals:function(b){var a=b?MochiKit.Signal.connect:MochiKit.Signal.disconnect;a(this.calendar,this.calendar.Signals.OK_CLICK,this,"_onOkayClick");a(this.calendar,this.calendar.Signals.ON_HIDE,this,"_onHide")},_removeSignals:function(){this._setSignals(false)},loadJsResources:function(){var b=["js/external/date.js","js/MochiKit/Base.js","js/MochiKit/DOM.js","js/MochiKit/Style.js","js/MochiKit/Signal.js","js/dhtmllib/dom.js","prompting/js/initDhtmlLib.js","js/dhtmllib/palette.js","js/dhtmllib/menu.js","js/crviewer/html.js","js/crviewer/common.js","js/crviewer/Calendar.js"];for(var a=0;a<b.length;a++){this.loadJsFile(b[a])}},loadJsFile:function(a){document.write('<script src="'+this.crystalreportviewerPath+a+'" language="javascript"><\/script>')},loadLocaleStrings:function(){var a=["js/dhtmllib/language/en/labels.js","js/crviewer/strings_en.js"];var e="_";if(this.locale.indexOf("-")>0){e="-"}var d=this.locale.split(e);if(d.length>=1){a.push("js/dhtmllib/language/"+d[0]+"/labels.js");a.push("js/crviewer/strings_"+d[0]+".js")}if(d.length>=2){a.push("js/dhtmllib/language/"+d[0]+"_"+d[1]+"/labels.js");a.push("js/crviewer/strings_"+d[0]+"_"+d[1]+".js")}for(var b=0;b<a.length;b++){this.loadJsFile(a[b])}},loadFiles:function(){if(typeof(bobj.crv)=="undefined"||typeof(bobj.crv.Calendar)=="undefined"){window.promptengine_skin=this.crystalreportviewerPath+"js/dhtmllib/images/skin_standard/";window.promptengine_style=this.crystalreportviewerPath+"js/crviewer/images/";window.promptengine_lang=this.locale;this.loadLocaleStrings();this.loadJsResources()}}};