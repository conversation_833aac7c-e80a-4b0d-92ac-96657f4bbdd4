/* Copyright (c) Business Objects 2006. All rights reserved. */

// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "\uC624\uB298";
var L_January   = "1\uC6D4";
var L_February  = "2\uC6D4";
var L_March     = "3\uC6D4";
var L_April     = "4\uC6D4";
var L_May       = "5\uC6D4";
var L_June      = "6\uC6D4";
var L_July      = "7\uC6D4";
var L_August    = "8\uC6D4";
var L_September = "9\uC6D4";
var L_October   = "10\uC6D4";
var L_November  = "11\uC6D4";
var L_December  = "12\uC6D4";
var L_Su        = "\uC77C";
var L_Mo        = "\uC6D4";
var L_Tu        = "\uD654";
var L_We        = "\uC218";
var L_Th        = "\uBAA9";
var L_Fr        = "\uAE08";
var L_Sa        = "\uD1A0";

// Strings for prompts.js and prompts_param.js
var L_YYYY          = "yyyy";
var L_MM            = "mm";
var L_DD            = "dd";
var L_BadNumber     = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uC22B\uC790\"\uC774\uACE0 \uC74C\uC218 \uBD80\uD638, \uC22B\uC790(\"0-9\"), \uC22B\uC790 \uADF8\uB8F9 \uAE30\uD638 \uB610\uB294 \uC18C\uC218\uC810 \uAE30\uD638\uB9CC \uC0AC\uC6A9\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4. \uC785\uB825\uD55C \uB9E4\uAC1C \uBCC0\uC218 \uAC12\uC744 \uC218\uC815\uD558\uC2ED\uC2DC\uC624.";
var L_BadCurrency   = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uD1B5\uD654\"\uC774\uACE0 \uC74C\uC218 \uBD80\uD638, \uC22B\uC790(\"0-9\"), \uC22B\uC790 \uADF8\uB8F9 \uAE30\uD638 \uB610\uB294 \uC18C\uC218\uC810 \uAE30\uD638\uB9CC \uC0AC\uC6A9\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4. \uC785\uB825\uD55C \uB9E4\uAC1C \uBCC0\uC218 \uAC12\uC744 \uC218\uC815\uD558\uC2ED\uC2DC\uC624.";
var L_BadDate       = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uB0A0\uC9DC\"\uC774\uACE0 \"%1\" \uD615\uC2DD\uC73C\uB85C \uC9C0\uC815\uD574\uC57C \uD569\uB2C8\uB2E4. \uC5EC\uAE30\uC11C \"yyyy\"\uB294 \uC5F0\uB3C4(4\uC790\uB9AC), \"mm\"\uC740 \uC6D4(\uC608: 1\uC6D4 = 1), \"dd\"\uB294 \uD574\uB2F9 \uC6D4\uC758 \uB0A0\uC9DC\uB97C \uB098\uD0C0\uB0C5\uB2C8\uB2E4.";
var L_BadDateTime   = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uB0A0\uC9DC \uC2DC\uAC04\"\uC774\uACE0 \"%1 hh:mm:ss\" \uD615\uC2DD\uC73C\uB85C \uC9C0\uC815\uD574\uC57C \uD569\uB2C8\uB2E4. \uC5EC\uAE30\uC11C \"yyyy\"\uB294 \uC5F0\uB3C4(4\uC790\uB9AC), \"mm\"\uC740 \uC6D4(\uC608: 1\uC6D4 = 1), \"dd\"\uB294 \uD574\uB2F9 \uC6D4\uC758 \uB0A0\uC9DC, \"hh\"\uB294 \uC2DC\uAC04(24\uC2DC\uAC04\uC81C), \"mm\\uC740 \uBD84, \"ss\"\uB294 \uCD08\uB97C \uB098\uD0C0\uB0C5\uB2C8\uB2E4.";
var L_BadTime       = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uC2DC\uAC04\"\uC774\uACE0 \"hh:mm:ss\" \uD615\uC2DD\uC73C\uB85C \uC9C0\uC815\uD574\uC57C \uD569\uB2C8\uB2E4. \uC5EC\uAE30\uC11C \"hh\"\uB294 \uC2DC\uAC04(24\uC2DC\uAC04\uC81C), \"mm\"\uC740 \uBD84, \"ss\"\uB294 \uCD08\uB97C \uB098\uD0C0\uB0C5\uB2C8\uB2E4.";
var L_NoValue       = "\uAC12 \uC5C6\uC74C";
var L_BadValue      = "\"\uAC12 \uC5C6\uC74C\"\uC73C\uB85C \uC124\uC815\uD558\uB824\uBA74 [\uC2DC\uC791] \uBC0F [\uB05D] \uAC12\uC744 \uBAA8\uB450 \"\uAC12 \uC5C6\uC74C\"\uC73C\uB85C \uC124\uC815\uD574\uC57C \uD569\uB2C8\uB2E4.";
var L_BadBound      = "\"\uD558\uD55C \uC5C6\uC74C\"\uACFC \"\uC0C1\uD55C \uC5C6\uC74C\"\uC744 \uD568\uAED8 \uC124\uC815\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.";
var L_NoValueAlready = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uB294 \uC774\uBBF8 \"\uAC12 \uC5C6\uC74C\"\uC73C\uB85C \uC124\uC815\uB418\uC5B4 \uC788\uC2B5\uB2C8\uB2E4. \uB2E4\uB978 \uAC12\uC744 \uCD94\uAC00\uD558\uB824\uBA74 \uBA3C\uC800 \"\uAC12 \uC5C6\uC74C\"\uC744 \uC81C\uAC70\uD558\uC2ED\uC2DC\uC624.";
var L_RangeError    = "\uBC94\uC704\uC758 \uC2DC\uC791 \uAC12\uC740 \uBC94\uC704\uC758 \uB05D \uAC12\uBCF4\uB2E4 \uC791\uC544\uC57C \uD569\uB2C8\uB2E4.";
var L_NoDateEntered = "\uB0A0\uC9DC\uB97C \uC785\uB825\uD558\uC2ED\uC2DC\uC624.";
var L_Empty         = "\uAC12\uC744 \uC785\uB825\uD558\uC2ED\uC2DC\uC624.";

// Strings for filter dialog
var L_closeDialog="\uCC3D \uB2EB\uAE30";

var L_SetFilter = "\uD544\uD130 \uC124\uC815";
var L_OK        = "\uD655\uC778";
var L_Cancel    = "\uCDE8\uC18C";

 /* Crystal Decisions Confidential Proprietary Information */
