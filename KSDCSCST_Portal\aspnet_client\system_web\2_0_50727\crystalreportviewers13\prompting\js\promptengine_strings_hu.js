/* Copyright (c) Business Objects 2006. All rights reserved. */

// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "Ma";
var L_January   = "Janu\u00E1r";
var L_February  = "Febru\u00E1r";
var L_March     = "M\u00E1rcius";
var L_April     = "\u00C1prilis";
var L_May       = "M\u00E1jus";
var L_June      = "J\u00FAnius";
var L_July      = "J\u00FAlius";
var L_August    = "Augusztus";
var L_September = "Szeptember";
var L_October   = "Okt\u00F3ber";
var L_November  = "November";
var L_December  = "December";
var L_Su        = "V";
var L_Mo        = "H";
var L_Tu        = "K";
var L_We        = "Sze";
var L_Th        = "Cs";
var L_Fr        = "P";
var L_Sa        = "Szo";

// Strings for prompts.js and prompts_param.js
var L_YYYY          = "\u00E9\u00E9\u00E9\u00E9";
var L_MM            = "hh";
var L_DD            = "nn";
var L_BadNumber     = "Ez a param\u00E9ter Sz\u00E1m t\u00EDpus\u00FA, ez\u00E9rt csak m\u00EDnuszjelet, sz\u00E1mjegyeket (0-9), sz\u00E1mjegy-csoportos\u00EDt\u00F3 jeleket vagy tizedesjelet tartalmazhat. Helyesb\u00EDtse a megadott param\u00E9ter\u00E9rt\u00E9ket.";
var L_BadCurrency   = "Ez a param\u00E9ter P\u00E9nznem t\u00EDpus\u00FA, ez\u00E9rt csak m\u00EDnuszjelet, sz\u00E1mjegyeket (0-9), sz\u00E1mjegy-csoportos\u00EDt\u00F3 jeleket vagy tizedesjelet tartalmazhat. Helyesb\u00EDtse a megadott param\u00E9ter\u00E9rt\u00E9ket.";
var L_BadDate       = "Ez a param\u00E9ter D\u00E1tum t\u00EDpus\u00FA, \u00E9s %1 form\u00E1tumban kell megadni, ahol az \u00E9\u00E9\u00E9\u00E9 a n\u00E9gy sz\u00E1mjegy\u0171 \u00E9v, a hh a h\u00F3nap (p\u00E9ld\u00E1ul janu\u00E1r = 01), az nn pedig a h\u00F3nap napja.";
var L_BadDateTime   = "Ez a param\u00E9ter D\u00E1tum/id\u0151 t\u00EDpus\u00FA, \u00E9s %1 \u00F3\u00F3:pp:mm form\u00E1tumban kell megadni, ahol az \u00E9\u00E9\u00E9\u00E9 a n\u00E9gy sz\u00E1mjegy\u0171 \u00E9v, a hh a h\u00F3nap (p\u00E9ld\u00E1ul janu\u00E1r = 01), az nn a h\u00F3nap napja, az \u00F3\u00F3 az \u00F3ra 24 \u00F3r\u00E1s form\u00E1tumban, a pp a perc, az mm pedig a m\u00E1sodperc.";
var L_BadTime       = "Ez a param\u00E9ter Time t\u00EDpus\u00FA, \u00E9s \u00F3\u00F3:pp:mm form\u00E1tumban kell megadni, ahol az \u00F3\u00F3 az \u00F3ra 24 \u00F3r\u00E1s form\u00E1tumban, a pp a perc, az mm pedig a m\u00E1sodperc.";
var L_NoValue       = "Nincs \u00E9rt\u00E9k";
var L_BadValue      = "A Nincs \u00E9rt\u00E9k be\u00E1ll\u00EDt\u00E1s\u00E1hoz a kezd\u0151 \u00E9s a z\u00E1r\u00F3 \u00E9rt\u00E9kn\u00E9l is a Nincs \u00E9rt\u00E9k be\u00E1ll\u00EDt\u00E1st kell megadnia.";
var L_BadBound      = "A Nincs als\u00F3 hat\u00E1r \u00E9s a Nincs fels\u0151 hat\u00E1r \u00E9rt\u00E9k nem \u00E1ll\u00EDthat\u00F3 be egyszerre.";
var L_NoValueAlready = "A param\u00E9ter be\u00E1ll\u00EDt\u00E1sa m\u00E1r Nincs \u00E9rt\u00E9k. T\u00E1vol\u00EDtsa el a Nincs \u00E9rt\u00E9k be\u00E1ll\u00EDt\u00E1st, miel\u0151tt elt\u00E9r\u0151 \u00E9rt\u00E9ket pr\u00F3b\u00E1lna be\u00E1ll\u00EDtani.";
var L_RangeError    = "A tartom\u00E1ny kezdete nem lehet nagyobb a tartom\u00E1ny v\u00E9g\u00E9n\u00E9l.";
var L_NoDateEntered = "Meg kell adnia egy d\u00E1tumot.";
var L_Empty         = "Adjon meg \u00E9rt\u00E9ket.";

// Strings for filter dialog
var L_closeDialog="Ablak bez\u00E1r\u00E1sa";

var L_SetFilter = "Sz\u0171r\u0151 be\u00E1ll\u00EDt\u00E1sa";
var L_OK        = "OK";
var L_Cancel    = "M\u00E9gse";

 /* Crystal Decisions Confidential Proprietary Information */
