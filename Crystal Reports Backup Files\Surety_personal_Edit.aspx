﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Surety_personal_Edit.aspx.cs" Inherits="KSDCSCST_Portal.Surety_personal_Edit" %>
 


<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- daterange picker -->
    <link rel="stylesheet" href="assets/plugins/daterangepicker/daterangepicker.css">
    <!-- iCheck for checkboxes and radio inputs -->
    <link rel="stylesheet" href="assets/plugins/icheck-bootstrap/icheck-bootstrap.min.css">
    <!-- Bootstrap Color Picker -->
    <link rel="stylesheet" href="assets/plugins/bootstrap-colorpicker/css/bootstrap-colorpicker.min.css">
    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet" href="assets/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css">
    <!-- Select2 -->
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">
    <!-- Bootstrap4 Duallistbox -->
    <link rel="stylesheet" href="assets/plugins/bootstrap4-duallistbox/bootstrap-duallistbox.min.css">
    <!-- BS Stepper -->
    <link rel="stylesheet" href="assets/plugins/bs-stepper/css/bs-stepper.min.css">
    <!-- dropzonejs -->
    <link rel="stylesheet" href="assets/plugins/dropzone/min/dropzone.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">

    <style>
        input:disabled {
            border: 0;
        }
    </style>
     <style>
         
        txtPresent_Post_Office {
            width: 300px;
            padding: 10px;
            font-size: 16px;
        }
        txtPermanent_Post_Office {
            width: 300px;
            padding: 10px;
            font-size: 16px;
        }
         
                 #search-results-pre-pin {
            list-style: none;
            padding: 0;
            margin-top: 5px;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
        }

            /* Styles for individual search result items */
            #search-results-pre-pin li {
                padding: 5px;
                cursor: pointer;
            }

                /* Highlight the selected result */
                #search-results-pre-pin li:hover {
                    background-color: #f2f2f2;
                }

                        #search-results-per-pin {
            list-style: none;
            padding: 0;
            margin-top: 5px;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
        }

            /* Styles for individual search result items */
            #search-results-per-pin li {
                padding: 5px;
                cursor: pointer;
            }

                /* Highlight the selected result */
                #search-results-per-pin li:hover {
                    background-color: #f2f2f2;
                }
                 #search-results-dofficer-pin {
            list-style: none;
            padding: 0;
            margin-top: 5px;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
        }

            /* Styles for individual search result items */
            #search-results-dofficer-pin li {
                padding: 5px;
                cursor: pointer;
            }

                /* Highlight the selected result */
                #search-results-dofficer-pin li:hover {
                    background-color: #f2f2f2;
                }
    </style>

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Guarantor Personal Details</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">Guarantor Personal Details</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">
									 <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Application Register No*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtApplicationRegNo" placeholder="Application Register No">
                                        </div>
                                    </div>
                                     
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Applicant Name*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtApplicant_Name" placeholder="Applicant Name">
                                        </div>
                                    </div>
									<div class="Sub_Header">Surety details</div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name Of Surety*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtName_Of_Surety" placeholder="Name Of Surety">
                                        </div>
                                    </div>
									
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Father's Name*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtFather_Name" placeholder="Father's Name">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Spouse's Name*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtSpouse_Name" placeholder="Spouse's Name">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">PAN No.*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtPAN_No" placeholder="PAN No">
                                        </div>
                                    </div>
									
									
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">IT Return Acknowledgement No.*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtIT_Return_Ack_No" placeholder="IT Return Acknowledgement No.">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Aadhar No.*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtAadhar_No" placeholder="Aadhar No.">
                                        </div>
                                    </div>
									   
									  <div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">Date Of Birth*</label>
                                        <div class="col-sm-9">
                                            <input     type="date" class="form-control" id="txtDate_Of_Birth" placeholder="dd/mm/yyyy">
                                        </div>
                                    </div> 
									   
									   
									   
									   
									   
									   
								 
                                    <div class="Sub_Header">Address Details</div>

                     

                                 
									<div class="Sub_Header">Present Address</div>

                                 	
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">House Name/No.*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtPresent_House_Name_Or_No" placeholder="House Name/No.">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">Lane1*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtPresent_Lane1" placeholder="Lane1">
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">Lane2*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtPresent_Lane2" placeholder="Lane2">
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">Pincode*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtPresent_Pincode" placeholder="Pincode">
                                             <ul id="search-results-pre-pin"></ul>

                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">Post Office*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtPresent_Post_Office" placeholder="Post Office">
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">phone*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtPresent_Phone" placeholder="phone">
                                        </div>
                                    </div>
									
									 <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Village*</label>
                                        <div class="col-sm-9">
                                            <select id="dropPresent_Village"   class="form-control">
                                            </select>
                                        </div>
                                    </div>
									
									
									   <div class="Sub_Header">Permanent Address
                                        <div style="float: right;">
                                             <input style="margin-right: 6px;margin-top: 4px;" type="checkbox" name="chk_Same_As_Present_Address" id="chk_Same_As_Present_Address">Same As Present Address 
                                        </div>
                                    </div>


									
									 
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">House Name/No.*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtPermanent_House_Name_Or_No" placeholder="House Name/No.">
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">Lane1*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtPermanent_Lane1" placeholder="Lane1">
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">Lane2*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtPermanent_Lane2" placeholder="Lane2">
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">Pincode*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtPermanent_Pincode" placeholder="Pincode">
                                            <ul id="search-results-per-pin"></ul>
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">Post Office*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtPermanent_Post_Office" placeholder="Post Office">
                                        </div>
                                    </div>
									
									
									<div class="form-group row">
                                        <label  class="col-sm-3 col-form-label">phone*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtPermanent_Phone" placeholder="phone">
                                        </div>
                                    </div>
									 
								 
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Village*</label>
                                        <div class="col-sm-9">
                                            <select id="dropPermanent_Village"   class="form-control">
                                            </select>
                                        </div>
                                    </div>

 
                                  

                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                             
                                            <a onclick="Save();" class="btn btn-success">Save</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <script src="assets/plugins/toastr/toastr.min.js"></script>



    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.js"></script>

    <script>
        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;

        const $searchInputPrePin = $('#txtPresent_Pincode');
        const $searchResultsPrePin = $('#search-results-pre-pin');
        const $searchInputPerPin = $('#txtPermanent_Pincode');
        const $searchResultsPerPin = $('#search-results-per-pin');

        $('#search-results-pre-pin').hide();
        $('#search-results-per-pin').hide();

        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }

        $(function () {
            
            $searchInputPrePin.on('input', function () {
                const query = $searchInputPrePin.val();
                fetchAutocompleteResultsPrePin(query);
            });

            // Event handler for selecting a result
            $searchResultsPrePin.on('click', 'li', function () {
                $("#txtPresent_Pincode").val($(this).attr("data-pincode"));
                $("#txtPresent_Post_Office").val($(this).html());
                //  const selectedResult = $(this).text();
                //  $searchInput.val(selectedResult);
                $searchResultsPrePin.hide();
            });

            $searchInputPerPin.on('input', function () {
                const query = $searchInputPerPin.val();
                fetchAutocompleteResultsPerPin(query);
            });

            // Event handler for selecting a result
            $searchResultsPerPin.on('click', 'li', function () {
                $("#txtPermanent_Pincode").val($(this).attr("data-pincode"));
                $("#txtPermanent_Post_Office").val($(this).html());
                //  const selectedResult = $(this).text();
                //  $searchInput.val(selectedResult);
                $searchResultsPerPin.hide();
            });
             
        })


        $(document).ready(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            // Get the current date
            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtApplication_Date').val(formattedDate);

            $("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
            $("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");
           // Load_All_Schemes();
            //   Load_All_Districts();

            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }

            Load_All_Application_Issue_By_Id(getParameterByName("LoanappId"));

        });

        function fetchAutocompleteResultsPrePin(query) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ pincode: query }), // If you have parameters
                url: "WebService.asmx/Select_All_PostOffices",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $searchResultsPrePin.empty();
                    if (data.d.length > 0) {

                        $searchResultsPrePin.show();
                    } else {
                        $searchResultsPrePin.hide();
                        $("#txtPresent_Post_Office").val('');
                    }
                    $.each(data.d, function (key, value) {
                        $searchResultsPrePin.append('<li data-pincode="' + value.Pincode + '">' + value.Post_office + '</li>');
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });


        }
        function fetchAutocompleteResultsPerPin(query) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ pincode: query }), // If you have parameters
                url: "WebService.asmx/Select_All_PostOffices",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $searchResultsPerPin.empty();
                    if (data.d.length > 0) {

                        $searchResultsPerPin.show();
                    } else {
                        $searchResultsPerPin.hide();
                        $("#txtPermanent_Post_Office").val('');
                    }
                    $.each(data.d, function (key, value) {
                        $searchResultsPerPin.append('<li data-pincode="' + value.Pincode + '">' + value.Post_office + '</li>');
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });


        }

        function Load_All_Application_Issue_By_Id(LoanappId) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Id: LoanappId }), // If you have parameters
                url: "WebService.asmx/Select_tbl_LoanApp_By_LoanAppId",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        $(".LABEL_APP_Status").text(value.chr_status);
                        var int_loanappid = value.int_loanappid;
                        var Name_Applicant = value.vchr_applname;
                        var Scheme_Id = value.int_schemeid;
                        Cast_Id = value.vchr_caste;
                        Sub_Cast_Id = value.SubCast;
                        var vchr_appreceivregno = value.vchr_appreceivregno;
                        $("#txtApplicant_Name").val(Name_Applicant);
                        $("#txtApplicationRegNo").val(vchr_appreceivregno);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Select_EmpSurety_By_Loanappid(getParameterByName("Id"))
            });
        }

        function getFormattedDate(date) {
            date = date.split('/')[2] + "-" + date.split('/')[1] + "-" + date.split('/')[0];
            return date;
        }

        var Present_Village_Id;
        var Permanent_Village_Id;
        var Present_District_Id;
        var Permanent_District_Id;
        var Present_Taluk;
        var Permanent_Taluk;
        var Present_District;
        var Permanent_District;
        var Department;

        function Select_EmpSurety_By_Loanappid(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Id: Id }), // If you have parameters
                url: "WebService.asmx/Select_tbl_empsurety_By_Id",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        $("#txtName_Of_Surety").val(value.vchr_empname);
                        $("#txtFather_Name").val(value.vchr_fathername);
                        $("#txtSpouse_Name").val(value.vchr_spousename);
                        $("#txtPAN_No").val(value.vchr_PanNo);
                        $("#txtIT_Return_Ack_No").val(value.vchr_ITAckNo);
                        $("#txtAadhar_No").val(value.vchr_Aadhar);

                        $("#txtPresent_House_Name_Or_No").val(value.vchr_presenthsename);
                        $("#txtPresent_Lane1").val(value.vchr_presentlane1);
                        $("#txtPresent_Lane2").val(value.vchr_presentlane2);
                        $("#txtPresent_Pincode").val(value.vchr_presentpin);
                        $("#txtPresent_Post_Office").val(value.vchr_presentpost);
                        $("#txtPresent_Phone").val(value.vchr_presentphno);
                        Present_Village_Id = value.vchr_presentVillage;

                        $("#txtPermanent_House_Name_Or_No").val(value.vchr_emppermhsename);
                        $("#txtPermanent_Lane1").val(value.vchr_emppermlane1);
                        $("#txtPermanent_Lane2").val(value.vchr_emppermlane2);
                        $("#txtPermanent_Pincode").val(value.vchr_emppermpin);
                        $("#txtPermanent_Post_Office").val(value.vchr_emppermpost);
                        $('#txtPermanent_Phone').val(value.vchr_emppermphno);
                        Permanent_Village_Id = value.vchr_PermVillage;

                        $("#txtDate_Of_Birth").val(getFormattedDate(value.dte_dob));
                        var Undertaking = 1;

                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Villages(Present_Village_Id, Permanent_Village_Id);
            });
        }

        function Load_All_Villages(Present_Village_Id, Permanent_Village_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Villages",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropPresent_Village').empty();
                    //$('#dropPresent_Village').append('<option value="0">Select</option>');
                    $('#dropPermanent_Village').empty();
                    // $('#dropPermanent_Village').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Village = value.Village;
                        var html = '<option value="' + Id + '">' + Village + '</option>';

                        $('#dropPresent_Village').append(html);
                        $('#dropPermanent_Village').append(html);
                    });


                },
                error: function (error) {


                    alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $("#dropPresent_Village option:contains(" + Present_Village_Id + ")").attr('selected', 'selected');
                $("#dropPermanent_Village option:contains(" + Permanent_Village_Id + ")").attr('selected', 'selected');
                Present_Village_Id = $('#dropPresent_Village').val();
                Permanent_Village_Id = $('#dropPermanent_Village').val();
                Load_All_tbl_Taluk_By_Present_Village_Id(Present_Village_Id);
                Load_All_tbl_Taluk_By_Permanent_Village_Id(Permanent_Village_Id);
                Select_All_Districts_By_Present_Village_Id(Present_Village_Id);
                Select_All_Districts_By_Permanent_Village_Id(Permanent_Village_Id);
 
            });

        }

        $('#dropPresent_Village').change(function () {
            var optionSelected = $(this).find("option:selected");
            var valueSelected = optionSelected.val();
            Load_All_tbl_Taluk_By_Present_Village_Id(valueSelected);
            Select_All_Districts_By_Present_Village_Id(valueSelected);

        });
        $('#dropPermanent_Village').change(function () {
            var optionSelected = $(this).find("option:selected");
            var valueSelected = optionSelected.val();
            Load_All_tbl_Taluk_By_Permanent_Village_Id(valueSelected);
            Select_All_Districts_By_Permanent_Village_Id(valueSelected);
        });

        function Select_All_Districts_By_Present_Village_Id(Present_Village_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts_By_Village_Id",
                data: JSON.stringify({ Village_Id: Present_Village_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    Present_District = $('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var District_Name = value.District_Name;
                        Present_District = District_Name;
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                //alert(Present_District);
            });
        }
        function Select_All_Districts_By_Permanent_Village_Id(Permanent_Village_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts_By_Village_Id",
                data: JSON.stringify({ Village_Id: Permanent_Village_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    Permanent_District = $('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var DistrictName = value.District_Name;
                        Permanent_District = DistrictName;
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }
        function Load_All_tbl_Taluk_By_Present_Village_Id(Present_Village_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_Taluk_By_Id",
                data: JSON.stringify({ Village_Id: Present_Village_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    Present_Taluk = $('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Taluk_Name = value.Taluk;
                        Present_Taluk = Taluk_Name;
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }
        function Load_All_tbl_Taluk_By_Permanent_Village_Id(Permanent_Village_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_Taluk_By_Id",
                data: JSON.stringify({ Village_Id: Permanent_Village_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {

                    Permanent_Taluk = $('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Taluk_Name = value.Taluk;
                        Permanent_Taluk = Taluk_Name;
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }

        $(document).on('click', '#chk_Same_As_Present_Address', function () {
            var ckbox = $('#chk_Same_As_Present_Address');
            if (ckbox.is(':checked')) {
                $('#txtPermanent_House_Name_Or_No').val($('#txtPresent_House_Name_Or_No').val());
                $('#txtPermanent_Lane1').val($('#txtPresent_Lane1').val());
                $('#txtPermanent_Lane2').val($('#txtPresent_Lane2').val());
                $('#txtPermanent_Pincode').val($('#txtPresent_Pincode').val());
                $('#txtPermanent_Post_Office').val($('#txtPresent_Post_Office').val());
                $('#txtPermanent_Phone').val($('#txtPresent_Phone').val());
                $('#dropPermanent_Village').val($('#dropPresent_Village').val());
                Permanent_Taluk = Present_Taluk;
                Permanent_District = Present_District;

                $('#txtPermanent_House_Name_Or_No').attr('readonly', 'readonly');
                $('#txtPermanent_Lane1').attr('readonly', 'readonly');
                $('#txtPermanent_Lane2').attr('readonly', 'readonly');
                $('#txtPermanent_Pincode').attr('readonly', 'readonly');
                $('#txtPermanent_Post_Office').attr('readonly', 'readonly');
                $('#txtPermanent_Phone').attr('readonly', 'readonly');
                $('#dropPermanent_Village').attr('disabled', 'disabled');

            }
            else {
                $('#txtPermanent_House_Name_Or_No').val('');
                $('#txtPermanent_Lane1').val('');
                $('#txtPermanent_Lane2').val('');
                $('#txtPermanent_Pincode').val('');
                $('#txtPermanent_Post_Office').val('');
                $('#txtPermanent_Phone').val('');
                $('#dropPermanent_Village').prop('selectedIndex', 0);
                $('#txtPermanent_House_Name_Or_No').removeAttr('readonly');
                $('#txtPermanent_Lane1').removeAttr('readonly');
                $('#txtPermanent_Lane2').removeAttr('readonly');
                $('#txtPermanent_Pincode').removeAttr('readonly');
                $('#txtPermanent_Post_Office').removeAttr('readonly');
                $('#txtPermanent_Phone').removeAttr('readonly');
                $('#dropPermanent_Village').removeAttr('disabled');
                var valueSelected = $('#dropPermanent_Village').val();
                Load_All_tbl_Taluk_By_Permanent_Village_Id(valueSelected);
                Select_All_Districts_By_Permanent_Village_Id(valueSelected);
            }
        });

        function Save() {

            var loanapp_Id = getParameterByName("LoanappId");
            var emp_Id = getParameterByName("Id");

            var ApplicationRegNo = $("#txtApplicationRegNo");
            var Name_Of_Surety = $("#txtName_Of_Surety");
            var Name_Of_Father = $("#txtFather_Name");
            var Name_Of_Spouse = $("#txtSpouse_Name");
            var PANNo = $("#txtPAN_No");
            var ITRetAckNo = $("#txtIT_Return_Ack_No");
            var AadhaarNo = $("#txtAadhar_No");

            var PresentHNameNo = $("#txtPresent_House_Name_Or_No");
            var PresentLane1 = $("#txtPresent_Lane1");
            var PresentLane2 = $("#txtPresent_Lane2");
            var PresentPin = $("#txtPresent_Pincode");
            var PresentPost = $("#txtPresent_Post_Office");
            var PresentPhone = $("#txtPresent_Phone");
            var PresentVillage = $("#dropPresent_Village option:selected");

            var PermanentHNameNo = $("#txtPermanent_House_Name_Or_No");
            var PermanentLane1 = $("#txtPermanent_Lane1");
            var PermanentLane2 = $("#txtPermanent_Lane2");
            var PermanentPin = $("#txtPermanent_Pincode");
            var PermanentPost = $("#txtPermanent_Post_Office");
            var PermanentPhone = $('#txtPermanent_Phone');
            var PermanentVillage = $("#dropPermanent_Village option:selected");

            var DOB = $("#txtDate_Of_Birth");

            var Undertaking = 1;

            if (PresentPhone.val().trim() == "") {
                Toast.fire({
                    icon: 'error',
                    title: 'Phone No is required !'
                })
            }
            if (Name_Of_Surety.val().trim() == "") {
                //  alert("Name is required");
                Toast.fire({
                    icon: 'error',
                    title: 'Name of Applicant is required !'
                })
            }
            else if (!Is_Valid_Text(Name_Of_Surety.val().trim())) {
                //  alert("Name is required");
                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed !'
                })
            }

            else {
                $("#btnSave").css("background-color", "#000");
                $("#btnSave").attr("disabled", "disabled");
                $("#btnSave").html("Please wait !");
                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/Update_To_tbl_empsurety",
                    data: JSON.stringify({ Id: emp_Id, int_empid: emp_Id, int_loanappid: loanapp_Id, vchr_appreceivregno: ApplicationRegNo.val(), vchr_empname: Name_Of_Surety.val(), vchr_fathername: Name_Of_Father.val(), vchr_spousename: Name_Of_Spouse.val(), vchr_empdesig: "", vchr_empoffname: "", vchr_empofflane1: "", vchr_empofflane2: "", vchr_empoffpost: "", vchr_empoffpin: 0, vchr_empoffphno: 0, vchr_presenthsename: PresentHNameNo.val(), vchr_presentlane1: PresentLane1.val(), vchr_presentlane2: PresentLane2.val(), vchr_presentpost: PresentPost.val(), vchr_presentpin: PresentPin.val(), vchr_presentphno: PresentPhone.val(), vchr_emppermhsename: PermanentHNameNo.val(), vchr_emppermlane1: PermanentLane1.val(), vchr_emppermlane2: PermanentLane2.val(), vchr_emppermpost: PermanentPost.val(), vchr_emppermpin: PermanentPin.val(), vchr_emppermphno: PermanentPhone.val(), dte_dob: DOB.val(), dte_eos: "01-01-0001", dte_empdateofretire: "01-01-0001", int_basicpay: 0, int_netsal: 0, int_grosssal: 0, vchr_senior_empname: "", vchr_senior_empdes: "", vchr_senior_empoffname: "", vchr_senior_emplane1: "", vchr_senior_emplane2: "", vchr_senior_emppost: "", vchr_senior_emppin: "", vchr_senior_empphone: 0, vchr_auditno: 0, vchr_depart: "", int_sr_officer: 0, vchr_scale: "", dte_conf_send_date: "01-01-0001", dte_conf_rec_date: "01-01-0001", int_loanno: 0, vchr_oldno: 0, vchr_presentVillage: PresentVillage.text(), vchr_presentTaluk: Present_Taluk, vchr_presentDistrict: Present_District, vchr_PermVillage: PermanentVillage.text(), vchr_PermTaluk: Permanent_Taluk, vchr_PermDistrict: Permanent_District, vchr_empoffname1: "", vchr_empofflane11: "", vchr_empofflane21: "", vchr_empoffpost1: "", vchr_empoffpin1: 0, vchr_empoffphno1: 0, int_loanee: 0, int_under: Undertaking, vchr_Ashwas: "", vchr_pen: 0, vchr_PanNo: PANNo.val(), vchr_ITAckNo: ITRetAckNo.val(), vchr_Aadhar: AadhaarNo.val(), int_personal: 1, int_eligibleloan: 0, vchr_scaleofpay: "", vchr_PensionScheme: "", vchr_PRAN: "", vchr_PFNo: "", Type: "Personal" }), // If you have parameters
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {

                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        // Handle AJAX error
                        //   alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);

                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                        })

                    }

                }).done(function () {
                    $("#btnSave").css("background-color", "#c8328b");
                    $("#btnSave").removeAttr("disabled");
                    $("#btnSave").html("Submit");
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Guarantor Personal Details Successfully Updated !',
                        allowOutsideClick: false,

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked

                            location.href = 'Surety_Modification_List.aspx';
                            // Your code here
                        }
                    });

                });


            }




        }


        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }


        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }



    </script>
</asp:Content>


