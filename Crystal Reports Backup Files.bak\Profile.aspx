﻿<%@ Page Title="Profile" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Profile.aspx.cs" Inherits="KSDCSCST_Portal.Profile" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Profile</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">User Profile</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">
                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Username</label>
                                        <div class="col-sm-9">
                                            <input disabled="disabled" type="email" class="form-control" id="txtUserName" value="">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName" class="col-sm-3 col-form-label">Name</label>
                                        <div class="col-sm-9">
                                            <input disabled="disabled" type="text" class="form-control" id="txtName" value="">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Email</label>
                                        <div class="col-sm-9">
                                            <input disabled="disabled" type="email" class="form-control" id="txtEmail" value="">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputName2" class="col-sm-3 col-form-label">Mobile Number</label>
                                        <div class="col-sm-9">
                                            <input disabled="disabled" type="text" class="form-control" id="txtMob_No" value="" placeholder="">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">District *</label>
                                        <div class="col-sm-9">
                                            <select id="dropDistrict" disabled="disabled" class="form-control">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="inputEmail" class="col-sm-3 col-form-label">Branch *</label>
                                        <div class="col-sm-9">
                                            <select id="dropSubDistrict" disabled="disabled" class="form-control">
                                            </select>
                                        </div>
                                    </div>
                                    <hr />
                                    <h5 style="font-weight: bold;">Reset Password</h5>
                                    <hr />
                                    <div class="form-group row">
                                        <label for="newpassword" class="col-sm-3 col-form-label">New Password</label>
                                        <div class="col-sm-9">
                                            <input type="password" class="form-control" id="newpassword" placeholder="New Password">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label for="confPassword" class="col-sm-3 col-form-label">Confirm Password</label>
                                        <div class="col-sm-9">
                                            <input type="password" class="form-control" id="confPassword" placeholder="Confirm Password">
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                            <a onclick="Save();" class="btn btn-success">Update</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <script src="assets/plugins/toastr/toastr.min.js"></script>



    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/inputmask/5.0.4/jquery.inputmask.min.js"></script>

    <script>
        var Toast;
        $(document).ready(function () {
            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            $("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
            $("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");
            Get_User_Details(<%= Session["User_Id"] %>);
        });

        function Get_User_Details(User_Id) {
            //alert(User_Id);
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Get_User_Details",
                data: JSON.stringify({ user_Id: User_Id }),
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $.each(data.d, function (key, value) {
                        $("#txtUserName").val(value.Username);
                        $("#txtName").val(value.Name);
                        $("#txtEmail").val(value.EmailId);
                        $("#txtMob_No").val(value.Mobile);
                        //$("#newpassword").val(value.Username);
                        //$("#confPassword").val(value.Username);
                       
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });

        }



        function Save() {
            //alert(Counter);
            if (confirm('Are you sure you want to perform this action?')) {



                var New_password = $("#newpassword");
                var Confirm_password = $("#confPassword");



                if (New_password.val() != Confirm_password.val()) {
                    Focus_Error(New_password);
                    Toast.fire({
                        icon: 'error',
                        title: 'Passwords do not match!'
                    })
                }
                else {

                    $.ajax({
                        type: "POST",
                        dataType: "json",
                        url: "WebService.asmx/Update_Profile",
                        data: JSON.stringify({ user_Id: <%= Session["User_Id"] %> , Password: Confirm_password.val() }), // If you have parameters
                        contentType: "application/json; charset=utf-8",
                        success: function (data) {

                        },
                        error: function (jqXHR, textStatus, errorThrown) {
                            // Handle AJAX error
                            //   alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);

                            Swal.fire({
                                icon: 'error',
                                title: 'Oops...',
                                text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                            })

                        }

                    }).done(function () {
                        Swal.fire({
                            icon: 'success',
                            title: 'Message',
                            text: ' Your Profile Updated Successfully!',

                        }).then((result) => {
                            if (result.isConfirmed) {
                                // OK button clicked

                                location.href = 'Default.aspx';
                                // Your code here
                            }
                        });

                    });




                }





                $(".form-control").removeAttr("style");


 

                 
            }
            else {
                // User clicked "Cancel", do nothing
                alert('Action was cancelled.');
            }



        }

        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }

    </script>
</asp:Content>
