﻿<%@ Page Title="Application Data Entry - Working Capital Or Business Development Loan | View" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="DE_Working_Capital_Or_Business_Development_Loan_View.aspx.cs" Inherits="KSDCSCST_Portal.DE_Working_Capital_Or_Business_Development_Loan" %>
<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
      <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>To be filled for Working Capital / Business Development Loan</h1>
                </div>
               <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="Welcome.aspx">Home</a></li>
						  <li class="breadcrumb-item  "><a href="ApplicationDataEntry_List.aspx">Application Data Entry</a></li>
                        <li class="breadcrumb-item active">To be filled for Working Capital / Business Development Loan</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">
                                    
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Application Register No*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtApplicationRegNo" disabled="disabled"  placeholder="Application Register No">
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Loanee Name*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtLoanee_Name" disabled="disabled" placeholder="Loanee Name">
                                        </div>
                                    </div>
									
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Sector*</label>
                                         <div class="col-sm-9">
                                            <select id="dropSector"   class="form-control">  <option>Select</option>
                                            </select>
                                        </div>
                                    </div>
									
									  <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Project*</label>
                                         <div class="col-sm-9">
                                            <select id="dropProject"   class="form-control" > <option>Select</option>
                                            </select>
                                        </div>
                                    </div>
									
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Estimated Amount*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtEstimated_Amount" placeholder="Estimated Amount">
                                        </div>
                                    </div>
									<div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Amount In Hand*</label>
                                        <div class="col-sm-9">
                                            <input   type="number" class="form-control" id="txtAmount_In_Hand" placeholder="Amount In Hand">
                                        </div>
                                    </div>
										 
								 
									
									 <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Purpose of the loan*</label>
                                        <div class="col-sm-9">
                                             <textarea class="form-control" maxlength="200" id="txtPurpose_Of_Loan" placeholder="Purpose of the loan"></textarea>
                                            <span class="validation_message">Maximum text length is 200</span>
                                        </div>
                                    </div>
									 
									  <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Applicant Experience*</label>
                                        <div class="col-sm-9">
                                            <input   type="text" class="form-control" id="txtApplicant_Experience" placeholder="Applicant Experience">
                                        </div>
                                    </div>
									 
									 <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Existing Business Details*</label>
                                        <div class="col-sm-9">
                                             <textarea class="form-control" maxlength="200" id="txtBusiness_Details" placeholder="Existing Business Details"></textarea>
                                            <span class="validation_message">Maximum text length is 200</span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                            
                                            <a onclick="Save();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->

    <!-- jQuery -->
     <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 -->
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <!-- Bootstrap4 Duallistbox -->
    <script src="assets/plugins/bootstrap4-duallistbox/jquery.bootstrap-duallistbox.min.js"></script>
    <!-- InputMask -->
    <script src="assets/plugins/moment/moment.min.js"></script>
    <script src="assets/plugins/inputmask/jquery.inputmask.min.js"></script>
    <!-- date-range-picker -->
    <script src="assets/plugins/daterangepicker/daterangepicker.js"></script>
    <!-- bootstrap color picker -->
    <script src="assets/plugins/bootstrap-colorpicker/js/bootstrap-colorpicker.min.js"></script>
    <!-- Tempusdominus Bootstrap 4 -->
    <script src="assets/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js"></script>
    <!-- BS-Stepper -->
    <script src="assets/plugins/bs-stepper/js/bs-stepper.min.js"></script>
    <!-- dropzonejs -->
    <script src="assets/plugins/dropzone/min/dropzone.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script src="assets/js/jquery.dateandtime.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>


    <script>
        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;
        $(document).ready(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            // Get the current date
            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtApplication_Date').val(formattedDate);
 
            //   Load_All_Districts();

            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }

            Load_All_Application_Issue_By_Id(getParameterByName("Id"));
        });

        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }

        function Load_All_Application_Issue_By_Id(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Id: Id }), // If you have parameters
                url: "WebService.asmx/Select_tbl_LoanApp_By_LoanAppId",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        $(".LABEL_APP_Status").text(value.chr_status);
                        var int_loanappid = value.int_loanappid;
                        var Name_Applicant = value.vchr_applname;
                        var Scheme_Id = value.int_schemeid;
                        Cast_Id = value.vchr_caste;
                        Sub_Cast_Id = value.SubCast;
                        var vchr_appreceivregno = value.vchr_appreceivregno;
                        $("#txtLoanee_Name").val(Name_Applicant);
                        $("#txtApplicationRegNo").val(vchr_appreceivregno);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Sectors()
            });
        }


        function Load_All_Sectors() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_Sectors",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSector').empty();
                    $('#dropSector').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Sector = value.Sector + " (" + value.Code + ")";
                        var html = '<option value="' + Id + '">' + Sector + '</option>';
                        if (Id != 15) {
                            $('#dropSector').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
             
            });

        }

        $('#dropSector').change(function () {
            Load_All_Projects($(this).val());
        });


        function Load_All_Projects(Sector_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_Projects_By_Sector_Id",
                data: JSON.stringify({ Sector_Id: Sector_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropProject').empty();
                    $('#dropProject').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Project = value.Project;
                        var html = '<option value="' + Id + '">' + Project + '</option>';
                        if (Id != 15) {
                            $('#dropProject').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });

        }

        function Save() {


            var Sector = $("#dropSector");
            var Project = $("#dropProject");
            var Estimated_Amount = $("#txtEstimated_Amount");
            var Amount_In_Hand = $("#txtAmount_In_Hand");
            var Loan_Purpose = $("#txtPurpose_Of_Loan");
            var Applicant_Eperience = $("#txtApplicant_Experience");
            var Business_Details = $("#txtBusiness_Details");

            if (Sector.val() == "0") {
                Focus_Error(Sector);
                Toast.fire({
                    icon: 'error',
                    title: 'Sector is required !'
                })
            }

            else if (Project.val() == "0") {
                Focus_Error(Project);
                Toast.fire({
                    icon: 'error',
                    title: 'Project is required !'
                })
            }

            else if (Estimated_Amount.val() == "") {
                Focus_Error(Estimated_Amount);
                Toast.fire({
                    icon: 'error',
                    title: 'Estimated Amount is required !'
                })
            }
            else if (Amount_In_Hand.val() == "") {
                Focus_Error(Amount_In_Hand);
                Toast.fire({
                    icon: 'error',
                    title: 'Amount in Hand is required !'
                })
            }

            else if (Loan_Purpose.val() == "") {
                Focus_Error(Loan_Purpose);
                Toast.fire({
                    icon: 'error',
                    title: 'Loan Purpose is required !'
                })
            }

            else if (Applicant_Eperience.val() == "") {
                Focus_Error(Applicant_Eperience);
                Toast.fire({
                    icon: 'error',
                    title: 'Applicant Experience is required !'
                })
            }


            else if (Business_Details.val().trim() == "") {
                Focus_Error(Business_Details);
                Toast.fire({
                    icon: 'error',
                    title: 'Existing Business Details is required !'
                })
            }

            else {

                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/Insert_To_tbl_workcapital",
                    data: JSON.stringify({ int_loanappid: getParameterByName("Id"), int_schemeid: getParameterByName("Scheme_Id"), vchr_sector: Sector.val(), vchr_project: Project.val(), int_amt_est: Estimated_Amount.val(), int_amt_hand: Amount_In_Hand.val(), vchr_projdetails: Loan_Purpose.val(), vchr_businessdet: Business_Details.val(), applicant_Experience: Applicant_Eperience.val() }),
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {

                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        // Handle AJAX error
                        //   alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);

                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                        })

                    }

                }).done(function () {
                    // Update_Status();
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Application Data Entry Successfully Submitted!',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked

                            location.href = 'ApplicationDataEntry_List.aspx';
                            // Your code here
                        }
                    });
                });


            }







        }

        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }


        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }


        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }



    </script>
</asp:Content>
