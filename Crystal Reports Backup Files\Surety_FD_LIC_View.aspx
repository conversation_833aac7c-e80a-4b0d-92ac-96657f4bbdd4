﻿<%@ Page Title="Surety FD LIC | View" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Surety_FD_LIC_View.aspx.cs" Inherits="KSDCSCST_Portal.Surety_FD_LIC_View" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- daterange picker -->
    <link rel="stylesheet" href="assets/plugins/daterangepicker/daterangepicker.css">
    <!-- iCheck for checkboxes and radio inputs -->
    <link rel="stylesheet" href="assets/plugins/icheck-bootstrap/icheck-bootstrap.min.css">
    <!-- Bootstrap Color Picker -->
    <link rel="stylesheet" href="assets/plugins/bootstrap-colorpicker/css/bootstrap-colorpicker.min.css">
    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet" href="assets/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css">
    <!-- Select2 -->
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">
    <!-- Bootstrap4 Duallistbox -->
    <link rel="stylesheet" href="assets/plugins/bootstrap4-duallistbox/bootstrap-duallistbox.min.css">
    <!-- BS Stepper -->
    <link rel="stylesheet" href="assets/plugins/bs-stepper/css/bs-stepper.min.css">
    <!-- dropzonejs -->
    <link rel="stylesheet" href="assets/plugins/dropzone/min/dropzone.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">

    <style>
        input:disabled {
            border: 0;
        }
    </style>
    <style>
        /* Styles for the search input */
        txtPost_Office {
            width: 300px;
            padding: 10px;
            font-size: 16px;
        }

        /* Styles for the search results container */
        #search-results {
            list-style: none;
            padding: 0;
            margin-top: 5px;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
        }

            /* Styles for individual search result items */
            #search-results li {
                padding: 5px;
                cursor: pointer;
            }

                /* Highlight the selected result */
                #search-results li:hover {
                    background-color: #f2f2f2;
                }

        #search-results-pre-pin {
            list-style: none;
            padding: 0;
            margin-top: 5px;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
        }
    </style>

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Guarantor FD/LIC Details </h1>

                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="Welcome.aspx">Home</a></li>
                        <li class="breadcrumb-item active">Guarantor FD/LIC Details </li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-6 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">
                                <div class="active tab-pane" id="settings">
                                    <div class="Sub_Header">Guarantor Details</div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Application Register No*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtApplicationRegNo" placeholder="Application Register No">
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Loanee Name*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtLoanee_Name" placeholder="Loanee Name">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name of Policy / Account Holder*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtName_Of_Policy_Account_Holder" placeholder="Name of Policy / Account Holder">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Name of Nominee*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtName_Of_Nominee" placeholder="Name of Nominee ">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">House Name/Number*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtHouse_Name_Number" placeholder="House Name/Number">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Lane1*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtLane1" placeholder="Lane1">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Lane2*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtLane2" placeholder="Lane2">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Pincode*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtPincode" placeholder="Search...">
                                            <ul id="search-results"></ul>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Post Office*</label>
                                        <div class="col-sm-9">
                                            <input type="text" disabled="disabled" class="form-control" id="txtPost_Office" placeholder="Post Office">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Phone*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtPhone" placeholder="Phone">
                                        </div>


                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Village*</label>
                                        <div class="col-sm-9">
                                            <select id="dropVillage" class="form-control">
                                                <option>[Select]</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Taluk*</label>
                                        <div class="col-sm-9">
                                            <input type="text" disabled="disabled" class="form-control" id="txtTaluk" placeholder="Taluk">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">District*</label>
                                        <div class="col-sm-9">
                                            <input type="text" disabled="disabled" class="form-control" id="txtDistrict" placeholder="District">
                                        </div>
                                    </div>


                                    <div class="Sub_Header">FD / Policy Details</div>

                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <div class="form-group row">
                                                <label class="col-sm-6 col-form-label">Type Of Surety*</label>
                                                <div class="col-sm-6">
                                                    <select id="dropFDPolicy_Type" class="form-control">
                                                        <option value="FD">FD</option>
                                                        <option value="LIC">LIC</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6" style="display:none;">
                                            <div class="form-group row">
                                                <label class="col-sm-6 col-form-label">Select for More than one Policy</label>
                                                <div class="col-sm-6">
                                                    <input class="col-sm-0 form-check-input" style="margin: 0; position: relative;" type="checkbox" id="checkSelect_For_More_Than_One_Policy">
                                                </div>
                                            </div>
                                        </div>



                                    </div>



                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Account / Policy Number*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtAccount_Policy_Number" placeholder="Account / Policy Number">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Details of Bank / Policy Office*</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="txtDetails_Of_Bank_PolicyOffice" placeholder="Details of Bank / Policy Office">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Date*</label>
                                        <div class="col-sm-9">
                                            <input type="date" class="dateandtime form-control" id="txtDate">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Amount*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtAmount" placeholder="Amount">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Maturity Date*</label>
                                        <div class="col-sm-9">
                                            <input type="date" class="dateandtime form-control" id="txtMaturity_Date">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label">Maturity Amount*</label>
                                        <div class="col-sm-9">
                                            <input type="number" class="form-control" id="txtMaturity_Amount" placeholder="Maturity Amount">
                                        </div>
                                    </div>



                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">

                                            <a onclick="Save();" class="btn btn-success">Submit</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="assets/plugins/select2/js/select2.full.min.js"></script>
    <script src="assets/plugins/toastr/toastr.min.js"></script>



    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.js"></script>

    <script>
        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;

        const $searchInput = $('#txtPincode');
        const $searchResults = $('#search-results');
        $('#search-results').hide();

        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }

        $(function () {

            $searchInput.on('input', function () {
                const query = $searchInput.val();
                fetchAutocompleteResults(query);
            });

            // Event handler for selecting a result
            $searchResults.on('click', 'li', function () {
                $("#txtPincode").val($(this).attr("data-pincode"));
                $("#txtPost_Office").val($(this).html());
                //  const selectedResult = $(this).text();
                //  $searchInput.val(selectedResult);
                $searchResults.hide();
            });
        });

        $(document).ready(function () {

            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });

            // Get the current date
            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#txtApplication_Date').val(formattedDate);

            $("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
            $("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");
            // Load_All_Schemes();
            //   Load_All_Districts();

            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }

            Load_All_Application_Issue_By_Id(getParameterByName("Id"));

        });


        function fetchAutocompleteResults(query) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ pincode: query }), // If you have parameters
                url: "WebService.asmx/Select_All_PostOffices",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $searchResults.empty();
                    if (data.d.length > 0) {

                        $searchResults.show();
                    } else {
                        $searchResults.hide();
                        $("#txtPost_Office").val('');
                    }
                    $.each(data.d, function (key, value) {
                        $searchResults.append('<li data-pincode="' + value.Pincode + '">' + value.Post_office + '</li>');
                    });
                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });


        }

        function Load_All_Application_Issue_By_Id(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Id: Id }), // If you have parameters
                url: "WebService.asmx/Select_tbl_LoanApp_By_LoanAppId",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        $(".LABEL_APP_Status").text(value.chr_status);
                        var int_loanappid = value.int_loanappid;
                        var Name_Applicant = value.vchr_applname;
                        var vchr_appreceivregno = value.vchr_appreceivregno;
                        $("#txtLoanee_Name").val(Name_Applicant);
                        $("#txtApplicationRegNo").val(vchr_appreceivregno);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                Load_All_Villages();
            });
        }


        function Load_All_Villages() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Villages",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropVillage').empty();
                    $('#dropVillage').append('<option value="0">[Select]</option>');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Village = value.Village;
                        var html = '<option value="' + Id + '">' + Village + '</option>';
                        $('#dropVillage').append(html);
                    });
                },
                error: function (error) {
                    //alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });

        }

        $('#dropVillage').change(function () {
            var optionSelected = $(this).find("option:selected");
            var valueSelected = optionSelected.val();
            if (valueSelected > 0) {
                Load_All_tbl_Taluk_By_Village_Id(valueSelected);
                Load_All_Districts_By_Village_Id(valueSelected);
            }
            else {
                $('#txtTaluk').val('');
                $('#txtDistrict').val('');
            }
        });


        function Load_All_tbl_Taluk_By_Village_Id(Village_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_tbl_Taluk_By_Id",
                data: JSON.stringify({ Village_Id: Village_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtTaluk').val('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Taluk = value.Taluk;
                        $('#txtTaluk').val(Taluk);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

            });
        }

        function Load_All_Districts_By_Village_Id(Village_Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts_By_Village_Id",
                data: JSON.stringify({ Village_Id: Village_Id }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#txtDistrict').val('');
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var District_Name = value.District_Name;
                        $('#txtDistrict').val(District_Name);
                    });
                },
                error: function (error) {
                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                //alert(Present_District);
            });
        }

        function Save() {
            var int_loanappid = getParameterByName("Id");
            var ApplicationRegNo = $("#txtApplicationRegNo");
            var PolicyAccountHolder = $("#txtName_Of_Policy_Account_Holder");
            var NomineeName = $("#txtName_Of_Nominee");
            var HouseName = $("#txtHouse_Name_Number");
            var Lane1 = $("#txtLane1");
            var Lane2 = $("#txtLane2");
            var PinCode = $("#txtPincode");
            var PostOffice = $("#txtPost_Office");
            var PhoneNo = $("#txtPhone");
            var Village = $("#dropVillage option:selected");
            var Taluk = $("#txtTaluk");
            var District = $("#txtDistrict");
            var FDPolicyType = $("#dropFDPolicy_Type option:selected");
            var AccountPolicyNumber = $("#txtAccount_Policy_Number");
            var AccountPolicyOfficeDetails = $("#txtDetails_Of_Bank_PolicyOffice");
            var FDPolicyDate = $("#txtDate");
            var Amount = $("#txtAmount");
            var MaturityDate = $("#txtMaturity_Date");
            var MaturityAmount = $("#txtMaturity_Amount");

            if (PolicyAccountHolder.val().trim() == "") {
                //  alert("Name is required");
                Toast.fire({
                    icon: 'error',
                    title: 'Name of Applicant is required !'
                })
            }
            else if (!Is_Valid_Text(PolicyAccountHolder.val().trim())) {
                //  alert("Name is required");
                Toast.fire({
                    icon: 'error',
                    title: 'Special characters or Numbers not allowed !'
                })
            }


            else {

                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "WebService.asmx/Insert_To_tbl_FDLICsurety",
                    data: JSON.stringify({ int_loanappid: int_loanappid, vchr_apprecregno: ApplicationRegNo.val(), vchr_holdername: PolicyAccountHolder.val(), vchr_nomineename: NomineeName.val(), vchr_hsename: HouseName.val(), vchr_lane1: Lane1.val(), vchr_lane2: Lane2.val(), vchr_post: PostOffice.val(), int_pin: PinCode.val(), vchr_phno: PhoneNo.val(), vchr_village: Village.text(), vchr_taluk: Taluk.val(), vchr_district: District.val(), vchr_fdpolnum: AccountPolicyNumber.val(), vchr_fdpoldetails: AccountPolicyOfficeDetails.val(), dt_fdpoldate: FDPolicyDate.val(), dt_mature_date: MaturityDate.val(), dte_conf_send_date: "01-01-0001", dte_conf_rec_date: "01-01-0001", int_loanno: 0, int_MatureAmt: MaturityAmount.val(), vchr_type: FDPolicyType.text(), int_Amount: Amount.val() }), // If you have parameters
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {

                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        // Handle AJAX error
                        //   alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);

                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                        })

                    }

                }).done(function () {
                    //  Update_Status();
                    Swal.fire({
                        icon: 'success',
                        title: 'Message',
                        text: 'Guarantor FD_LIC Details Successfully Saved !',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // OK button clicked

                            location.href = 'Surety_FD_LIC_List.aspx';
                            // Your code here
                        }
                    });
                });
            }
        }

        function Update_Status() {
            //alert(Counter);
            var loanapp_Id = getParameterByName("Id");
            var Type = 'FD_LIC';
            var Status = 'Surety Entry Done';



            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/New_Update_To_tbl_loanapp_Surety_Status_And_Type",
                data: JSON.stringify({ int_loanappid: loanapp_Id, status: Status, type: Type }), // If you have parameters
                contentType: "application/json; charset=utf-8",
                success: function (data) {

                },
                error: function (jqXHR, textStatus, errorThrown) {
                    // Handle AJAX error
                    alert("AJAX Error: " + errorThrown + "/" + textStatus + "/" + jqXHR);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Contact your administrator for more information, Email Id: <EMAIL>',

                    })

                }

            }).done(function () {
                Swal.fire({
                    icon: 'success',
                    title: 'Message',
                    text: 'Guarantor FD_LIC Details Successfully Saved !',

                }).then((result) => {
                    if (result.isConfirmed) {
                        // OK button clicked

                        location.href = 'Surety_FD_LIC_List.aspx';
                        // Your code here
                    }
                });

            });




        }

        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }


        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }



    </script>
</asp:Content>


