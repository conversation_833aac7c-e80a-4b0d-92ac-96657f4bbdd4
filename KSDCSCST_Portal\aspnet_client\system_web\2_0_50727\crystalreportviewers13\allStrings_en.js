/* Copyright (c) Business Objects 2006. All rights reserved. */

var L_bobj_crv_MainReport = "Main Report";
// Viewer Toolbar tooltips
var L_bobj_crv_FirstPage = "Go to First Page";
var L_bobj_crv_PrevPage = "Go to Previous Page";
var L_bobj_crv_NextPage = "Go to Next Page";
var L_bobj_crv_LastPage = "Go to Last Page";
var L_bobj_crv_ParamPanel = "Parameter Panel";
var L_bobj_crv_Parameters = "Parameters";
var L_bobj_crv_GroupTree = "Group Tree";
var L_bobj_crv_DrillUp = "Drill Up";
var L_bobj_crv_Refresh = "Refresh Report";
var L_bobj_crv_Zoom = "Zoom";
var L_bobj_crv_PageNav = "Page Navigation";
var L_bobj_crv_SelectPage = "Go to Page";
var L_bobj_crv_SearchText = "Search for text";
var L_bobj_crv_Export = "Export this report";
var L_bobj_crv_Print = "Print this report";
var L_bobj_crv_TabList = "Tab List";
var L_bobj_crv_Close = "Close";
var L_bobj_crv_Logo=  "Business Objects Logo";
var L_bobj_crv_FileMenu = "File Menu";

var L_bobj_crv_File = "File";

var L_bobj_crv_Show = "Show";
var L_bobj_crv_Hide = "Hide";

var L_bobj_crv_Find = "Find...";
var L_bobj_crv_of = "%1 of %2"; // Example: Page "1 of 3"

var L_bobj_crv_submitBtnLbl = "Export";
var L_bobj_crv_ActiveXPrintDialogTitle = "Print";
var L_bobj_crv_PDFPrintDialogTitle = "Print to PDF";
var L_bobj_crv_PrintRangeLbl = "Page Range:";
var L_bobj_crv_PrintAllLbl = "All Pages";
var L_bobj_crv_PrintPagesLbl = "Select Pages";
var L_bobj_crv_PrintFromLbl = "From:";
var L_bobj_crv_PrintToLbl = "To:";
var L_bobj_crv_PrintInfoTitle = "Print to PDF:";
var L_bobj_crv_PrintInfo1 = 'The viewer must export to PDF to print. Choose the Print option from the PDF reader application once the document is opened.';
var L_bobj_crv_PrintInfo2 = 'Note: You must have a PDF reader installed to print. (eg. Adobe Reader)';
var L_bobj_crv_PrintPageRangeError = "Enter a valid page range.";

var L_bobj_crv_ExportBtnLbl = "Export";
var L_bobj_crv_ExportDialogTitle = "Export";
var L_bobj_crv_ExportFormatLbl = "File Format:";
var L_bobj_crv_ExportInfoTitle = "To export:";

var L_bobj_crv_ParamsApply = "Apply";
var L_bobj_crv_ParamsAdvDlg = "Edit parameter value";
var L_bobj_crv_ParamsDeleteTooltip = "Delete parameter value";
var L_bobj_crv_ParamsAddValue = "Click to Add...";
var L_bobj_crv_ParamsApplyTip = "Apply button (enabled)";
var L_bobj_crv_ParamsApplyDisabledTip = "Apply button (disabled)";
var L_bobj_crv_ParamsDlgTitle = "Enter Values";
var L_bobj_crv_ParamsCalBtn = "Calendar button";
var L_bobj_crv_Reset= "Reset";
var L_bobj_crv_ResetTip = "Reset button (enabled)";
var L_bobj_crv_ResetDisabledTip = "Reset button (disabled)";
var L_bobj_crv_ParamsDirtyTip = "Parameter value has changed. Click the Apply button to apply changes.";
var L_bobj_crv_ParamsDataTip = "This is a data-fetching parameter";
var L_bobj_crv_ParamsMaxNumDefaultValues = "Click here for more items...";
var L_bobj_crv_paramsOpenAdvance = "Advanced prompt button for \'%1\'";

var L_bobj_crv_ParamsInvalidTitle = "The parameter value is not valid";
var L_bobj_crv_ParamsTooLong = "Parameter value can be no more than %1 characters long";
var L_bobj_crv_ParamsTooShort = "Parameter value must be at least %1 characters long";
var L_bobj_crv_ParamsBadNumber = "This parameter is of type \"Number\" and can only contain a negative sign symbol, digits (\"0-9\"), digit grouping symbols or a decimal symbol.";
var L_bobj_crv_ParamsBadCurrency = "This parameter is of type \"Currency\" and can only contain a negative sign symbol, digits (\"0-9\"), digit grouping symbols or a decimal symbol.";
var L_bobj_crv_ParamsBadDate = "This parameter is of type \"Date\" and the correct format is \"%1\" where \"yyyy\" is the four digit year, \"mm\" is the month (e.g. January = 1), and \"dd\" is the day of the month.";
var L_bobj_crv_ParamsBadTime = "This parameter is of type \"Time\" and the correct format is \"hh:mm:ss\" where \"hh\" is hours in a 24 hour clock, \"mm\" is minutes, and \"ss\" is seconds.";
var L_bobj_crv_ParamsBadDateTime = "This parameter is of type \"DateTime\" and the correct format is \"%1 hh:mm:ss\". \"yyyy\" is the four digit year, \"mm\" is the month (e.g. January = 1), \"dd\" is the day of the month, \"hh\" is hours in a 24 hour clock, \"mm\" is minutes, and \"ss\" is seconds.";
var L_bobj_crv_ParamsMinTooltip = "Please specify a %1 value greater than or equal to %2.";
var L_bobj_crv_ParamsMaxTooltip = "Please specify a %1 value less than or equal to %2.";
var L_bobj_crv_ParamsMinAndMaxTooltip = "Please specify a %1 value between %2 and %3.";
var L_bobj_crv_ParamsStringMinOrMaxTooltip = "The %1 length for this field is %2.";
var L_bobj_crv_ParamsStringMinAndMaxTooltip = "The value must be between %1 and %2 characters long.";
var L_bobj_crv_ParamsYearToken = "yyyy";
var L_bobj_crv_ParamsMonthToken = "mm";
var L_bobj_crv_ParamsDayToken = "dd";
var L_bobj_crv_ParamsReadOnly = "This parameter is of type \"Read Only\".";
var L_bobj_crv_ParamsNoValue = "No Value";
var L_bobj_crv_ParamsDuplicateValue = "Duplicate values are not allowed.";
var L_bobj_crv_ParamsEnterOptional = "Enter %1 (Optional)";
var L_bobj_crv_ParamsNoneSelected= "(None Selected)";
var L_bobj_crv_ParamsClearValues= "Clear Values";
var L_bobj_crv_ParamsMoreValues= "%1 more values...";
var L_bobj_crv_ParamsMoreValue= "%1 more value...";
var L_bobj_crv_Error = "Error";
var L_bobj_crv_OK = "OK";
var L_bobj_crv_Cancel = "Cancel";
var L_bobj_crv_showDetails = "Show Details";
var L_bobj_crv_hideDetails = "Hide Details";
var L_bobj_crv_RequestError = "Unable to process your request";
var L_bobj_crv_ServletMissing = "The viewer is unable to connect with the CrystalReportViewerServlet that handles asynchronous requests.\nPlease ensure that the Servlet and Servlet-Mapping have been properly declared in the application\'s web.xml file.";
var L_bobj_crv_FlashRequired = "This content requires Adobe Flash Player 9 or higher. {0}Please click here to install";
var L_bobj_crv_ReadOnlyInPanel= "This parameter is not editable in the panel. Open advanced prompt dialog to modify its value";

var L_bobj_crv_Tree_Drilldown_Node = "Drilldown node %1";

var L_bobj_crv_ReportProcessingMessage = "Please wait while the document is being processed.";
var L_bobj_crv_PrintControlProcessingMessage = "Please wait while the Crystal Reports Print Control is loaded.";

var L_bobj_crv_SundayShort = "S";
var L_bobj_crv_MondayShort = "M";
var L_bobj_crv_TuesdayShort = "T";
var L_bobj_crv_WednesdayShort = "W";
var L_bobj_crv_ThursdayShort = "T";
var L_bobj_crv_FridayShort = "F";
var L_bobj_crv_SaturdayShort = "S";

var L_bobj_crv_Minimum = "minimum";
var L_bobj_crv_Maximum = "maximum";

var L_bobj_crv_Date = "Date";
var L_bobj_crv_Time = "Time";
var L_bobj_crv_DateTime = "DateTime";
var L_bobj_crv_Boolean = "Boolean";
var L_bobj_crv_Number = "Number";
var L_bobj_crv_Text = "Text";

var L_bobj_crv_InteractiveParam_NoAjax = "The web browser you are using is not configured to show the Parameter Panel.";
var L_bobj_crv_AdvancedDialog_NoAjax= "The viewer is unable to open an advanced prompt dialog.";

var L_bobj_crv_EnableAjax= "Please contact your administrator to enable asynchronous requests.";

var L_bobj_crv_LastRefreshed = "Last Refreshed";

var L_bobj_crv_Collapse = "Collapse";

var L_bobj_crv_CatalystTip = "Online Resources";
// <script>
/*
=============================================================
WebIntelligence(r) Report Panel
Copyright(c) 2001-2003 Business Objects S.A.
All rights reserved

Use and support of this software is governed by the terms
and conditions of the software license agreement and support
policy of Business Objects S.A. and/or its subsidiaries. 
The Business Objects products and technology are protected
by the US patent number 5,555,403 and 6,247,008

File: labels.js


=============================================================
*/

_default="Default"
_black="Black"
_brown="Brown"
_oliveGreen="Olive Green"
_darkGreen="Dark Green"
_darkTeal="Dark Teal"
_navyBlue="Navy Blue"
_indigo="Indigo"
_darkGray="Dark Gray"
_darkRed="Dark Red"
_orange="Orange"
_darkYellow="Dark Yellow"
_green="Green"
_teal="Teal"
_blue="Blue"
_blueGray="Blue Gray"
_mediumGray="Medium Gray"
_red="Red"
_lightOrange="Light Orange"
_lime="Lime"
_seaGreen="Sea Green"
_aqua="Aqua"
_lightBlue="Light Blue"
_violet="Violet"
_gray="Gray"
_magenta="Magenta"
_gold="Gold"
_yellow="Yellow"
_brightGreen="Bright Green"
_cyan="Cyan"
_skyBlue="Sky Blue"
_plum="Plum"
_lightGray="Light Gray"
_pink="Pink"
_tan="Tan"
_lightYellow="Light Yellow"
_lightGreen="Light Green"
_lightTurquoise="Light Turquoise"
_paleBlue="Pale Blue"
_lavender="Lavender"
_white="White"
_lastUsed="Last used:"
_moreColors="More Colors..."

_month=new Array

_month[0]="JANUARY"
_month[1]="FEBRUARY"
_month[2]="MARCH"
_month[3]="APRIL"
_month[4]="MAY"
_month[5]="JUNE"
_month[6]="JULY"
_month[7]="AUGUST"
_month[8]="SEPTEMBER"
_month[9]="OCTOBER"
_month[10]="NOVEMBER"
_month[11]="DECEMBER"

_day=new Array
_day[0]="S"
_day[1]="M"
_day[2]="T"
_day[3]="W"
_day[4]="T"
_day[5]="F"
_day[6]="S"

_today="Today"

_AM="AM"
_PM="PM"

_closeDialog="Close Window"

_lstMoveUpLab="Move Up"
_lstMoveDownLab="Move Down"
_lstMoveLeftLab="Move Left" 
_lstMoveRightLab="Move Right"
_lstNewNodeLab="Add Nested Filter"
_lstAndLabel="AND"
_lstOrLabel="OR"
_lstSelectedLabel="Selected"
_lstQuickFilterLab="Add Quick Filter"

_openMenu="Click here to access {0} options"
_openCalendarLab="Open Calendar"

_scroll_first_tab="Scroll to first tab"
_scroll_previous_tab="Scroll to previous tab"
_scroll_next_tab="Scroll to next tab"
_scroll_last_tab="Scroll to last tab"

_expandedLab="Expanded"
_collapsedLab="Collapsed"
_selectedLab="Selected"

_expandNode="Expand node %1"
_collapseNode="Collapse node %1"

_checkedPromptLab="Set"
_nocheckedPromptLab="Not Set"
_selectionPromptLab="values equal to"
_noselectionPromptLab="no values"

_lovTextFieldLab="Type values here"
_lovCalendarLab="Type date here"
_lovPrevChunkLab="Go to previous chunk"
_lovNextChunkLab="Go to next chunk"
_lovComboChunkLab="Chunk"
_lovRefreshLab="Refresh"
_lovSearchFieldLab="Type text to search here"
_lovSearchLab="Search"
_lovNormalLab="Normal"
_lovMatchCase="Match Case"
_lovRefreshValuesLab="Refresh Values"

_calendarNextMonthLab="Go to next month"
_calendarPrevMonthLab="Go to previous month"
_calendarNextYearLab="Go to next year"
_calendarPrevYearLab="Go to previous year"
_calendarSelectionLab="Selected day "

_menuCheckLab="Checked"
_menuDisableLab="Disabled"
	
_level="Level"
_closeTab="Close Tab"
_of=" of "

_RGBTxtBegin= "RGB("
_RGBTxtEnd= ")"

_helpLab="Help"

_waitTitleLab="Please wait"
_cancelButtonLab="Cancel"

_modifiers= new Array
_modifiers[0]="Ctrl+"
_modifiers[1]="Shift+"
_modifiers[2]="Alt+"

_bordersMoreColorsLabel="More Borders..."
_bordersTooltip=new Array
_bordersTooltip[0]="No Border"
_bordersTooltip[1]="Left Border"
_bordersTooltip[2]="Right Border"
_bordersTooltip[3]="Bottom Border"
_bordersTooltip[4]="Medium Bottom Border"
_bordersTooltip[5]="Thick Bottom Border"
_bordersTooltip[6]="Top and Bottom Border"
_bordersTooltip[7]="Top And Medium Bottom Border"
_bordersTooltip[8]="Top And Thick Bottom Border"
_bordersTooltip[9]="All Border"
_bordersTooltip[10]="All Medium Border"
_bordersTooltip[11]="All Thick Border"/* Copyright (c) Business Objects 2006. All rights reserved. */

// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "Today";
var L_January   = "January";
var L_February  = "February";
var L_March     = "March";
var L_April     = "April";
var L_May       = "May";
var L_June      = "June";
var L_July      = "July";
var L_August    = "August";
var L_September = "September";
var L_October   = "October";
var L_November  = "November";
var L_December  = "December";
var L_Su        = "Su";
var L_Mo        = "Mo";
var L_Tu        = "Tu";
var L_We        = "We";
var L_Th        = "Th";
var L_Fr        = "Fr";
var L_Sa        = "Sa";

// Strings for prompts.js and prompts_param.js
var L_YYYY          = "yyyy";
var L_MM            = "mm";
var L_DD            = "dd";
var L_BadNumber     = "This parameter is of type \"Number\" and can only contain a negative sign symbol, digits (\"0-9\"), digit grouping symbols or a decimal symbol. Please correct the entered parameter value.";
var L_BadCurrency   = "This parameter is of type \"Currency\" and can only contain a negative sign symbol, digits (\"0-9\"), digit grouping symbols or a decimal symbol. Please correct the entered parameter value.";
var L_BadDate       = "This parameter is of type \"Date\" and should be in the format \"%1\" where \"yyyy\" is the four digit year, \"mm\" is the month (e.g. January = 1), and \"dd\" is the day of the month.";
var L_BadDateTime   = "This parameter is of type \"DateTime\" and the correct format is \"%1 hh:mm:ss\". \"yyyy\" is the four digit year, \"mm\" is the month (e.g. January = 1), \"dd\" is the day of the month, \"hh\" is hours in a 24 hour clock, \"mm\" is minutes, and \"ss\" is seconds.";
var L_BadTime       = "This parameter is of type \"Time\" and should be in the format \"hh:mm:ss\" where \"hh\" is hours in a 24 hour clock, \"mm\" is minutes, and \"ss\" is seconds.";
var L_NoValue       = "No Value";
var L_BadValue      = "To set \"No Value\", you must set both From and To values to \"No Value\".";
var L_BadBound      = "You cannot set \"No Lower Bound\" together with \"No Upper Bound\".";
var L_NoValueAlready = "This parameter is already set to \"No Value\". Remove \"No Value\" before adding other values.";
var L_RangeError    = "The start of range cannot be greater than the end of range.";
var L_NoDateEntered = "You must enter a date.";
var L_Empty         = "Please enter a value.";

// Strings for filter dialog
var L_closeDialog="Close window";

var L_SetFilter = "Set Filter";
var L_OK        = "OK";
var L_Cancel    = "Cancel";

 /* Crystal Decisions Confidential Proprietary Information */
