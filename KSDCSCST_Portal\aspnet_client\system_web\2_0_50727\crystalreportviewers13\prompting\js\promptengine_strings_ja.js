/* Copyright (c) Business Objects 2006. All rights reserved. */

// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "\u4ECA\u65E5";
var L_January   = "1 \u6708";
var L_February  = "2 \u6708";
var L_March     = "3 \u6708";
var L_April     = "4 \u6708";
var L_May       = "5 \u6708";
var L_June      = "6 \u6708";
var L_July      = "7 \u6708";
var L_August    = "8 \u6708";
var L_September = "9 \u6708";
var L_October   = "10 \u6708";
var L_November  = "11 \u6708";
var L_December  = "12 \u6708";
var L_Su        = "\u65E5";
var L_Mo        = "\u6708";
var L_Tu        = "\u706B";
var L_We        = "\u6C34";
var L_Th        = "\u6728";
var L_Fr        = "\u91D1";
var L_Sa        = "\u571F";

// Strings for prompts.js and prompts_param.js
var L_YYYY          = "yyyy";
var L_MM            = "mm";
var L_DD            = "dd";
var L_BadNumber     = "\u3053\u306E\u30D1\u30E9\u30E1\u30FC\u30BF\u306E\u578B\u306F \"\u6570\u5024\" \u3067\u3001\u8CA0\u53F7\u3001\u6570\u5B57 (\"0-9\")\u3001\u304A\u3088\u3073\u5C0F\u6570\u70B9\u3092\u793A\u3059\u30D4\u30EA\u30AA\u30C9\u307E\u305F\u306F\u30AB\u30F3\u30DE\u306E\u307F\u3092\u542B\u3080\u3053\u3068\u304C\u3067\u304D\u307E\u3059\u3002\u5165\u529B\u3055\u308C\u305F\u30D1\u30E9\u30E1\u30FC\u30BF\u5024\u3092\u4FEE\u6B63\u3057\u3066\u304F\u3060\u3055\u3044\u3002";
var L_BadCurrency   = "\u3053\u306E\u30D1\u30E9\u30E1\u30FC\u30BF\u306E\u578B\u306F \"\u901A\u8CA8\" \u3067\u3001\u8CA0\u53F7\u3001\u6570\u5B57 (\"0-9\")\u3001\u304A\u3088\u3073\u5C0F\u6570\u70B9\u3092\u793A\u3059\u30D4\u30EA\u30AA\u30C9\u307E\u305F\u306F\u30AB\u30F3\u30DE\u306E\u307F\u3092\u542B\u3080\u3053\u3068\u304C\u3067\u304D\u307E\u3059\u3002\u5165\u529B\u3055\u308C\u305F\u30D1\u30E9\u30E1\u30FC\u30BF\u5024\u3092\u4FEE\u6B63\u3057\u3066\u304F\u3060\u3055\u3044\u3002";
var L_BadDate       = "\u3053\u306E\u30D1\u30E9\u30E1\u30FC\u30BF\u306E\u578B\u306F \"\u65E5\u4ED8\" \u3067\u3001\u305D\u306E\u5F62\u5F0F\u306F \"%1\" \u3067\u3059\u3002\"yyyy\" \u306F\u5E74\u3092\u8868\u3059 4 \u6841\u306E\u6570\u5B57\u3001\"mm\" \u306F\u6708 (\u4F8B: 1 \u6708 = 1)\u3001\"dd\" \u306F\u305D\u306E\u6708\u306E\u65E5\u3092\u8868\u3057\u307E\u3059\u3002";
var L_BadDateTime   = "\u3053\u306E\u30D1\u30E9\u30E1\u30FC\u30BF\u306E\u578B\u306F \"\u65E5\u6642\" \u3067\u3001\u305D\u306E\u5F62\u5F0F\u306F \"%1 hh:mm:ss\" \u3067\u3059\u3002\"yyyy\" \u306F\u5E74\u3092\u8868\u3059 4 \u6841\u306E\u6570\u5B57\u3001 \"mm\" \u306F\u6708 (\u4F8B: 1 \u6708 = 1)\u3001\"dd\" \u306F\u65E5\u3001\"hh\" \u306F 24 \u6642\u9593\u8868\u793A\u306B\u3088\u308B\u6642\u9593\u3001\"mm\" \u306F\u5206\u3001\"ss\" \u306F\u79D2\u3092\u8868\u3057\u307E\u3059\u3002";
var L_BadTime       = "\u3053\u306E\u30D1\u30E9\u30E1\u30FC\u30BF\u306E\u578B\u306F \"\u6642\u523B\" \u3067\u3001\u305D\u306E\u5F62\u5F0F\u306F \"hh:mm:ss\" \u3067\u3059\u3002\"hh\" \u306F 24 \u6642\u9593\u8868\u793A\u306B\u3088\u308B\u6642\u9593\u3001\"mm\" \u306F\u5206\u3001\"ss\" \u306F\u79D2\u3092\u8868\u3057\u307E\u3059\u3002";
var L_NoValue       = "\u5024\u306A\u3057";
var L_BadValue      = "\"\u5024\u306A\u3057\" \u3068\u8A2D\u5B9A\u3059\u308B\u305F\u3081\u306B\u306F\u3001\u958B\u59CB\u5024\u3068\u7D42\u4E86\u5024\u306E\u4E21\u65B9\u3092 \"\u5024\u306A\u3057\" \u306B\u8A2D\u5B9A\u3059\u308B\u5FC5\u8981\u304C\u3042\u308A\u307E\u3059\u3002";
var L_BadBound      = "\"\u7BC4\u56F2\u306E\u958B\u59CB\u3092\u6307\u5B9A\u3057\u306A\u3044\" \u3068 \"\u7BC4\u56F2\u306E\u7D42\u4E86\u3092\u6307\u5B9A\u3057\u306A\u3044\" \u3092\u540C\u6642\u306B\u8A2D\u5B9A\u3059\u308B\u3053\u3068\u306F\u3067\u304D\u307E\u305B\u3093\u3002";
var L_NoValueAlready = "\u3053\u306E\u30D1\u30E9\u30E1\u30FC\u30BF\u306F\u3059\u3067\u306B \"\u5024\u306A\u3057\" \u306B\u8A2D\u5B9A\u3055\u308C\u3066\u3044\u307E\u3059\u3002\u4ED6\u306E\u5024\u3092\u8A2D\u5B9A\u3059\u308B\u524D\u306B \"\u5024\u306A\u3057\" \u3092\u524A\u9664\u3057\u307E\u3059\u3002";
var L_RangeError    = "\u7BC4\u56F2\u306E\u958B\u59CB\u5024\u306F\u7D42\u4E86\u5024\u3088\u308A\u5C0F\u3055\u306A\u5024\u306B\u3059\u308B\u5FC5\u8981\u304C\u3042\u308A\u307E\u3059\u3002";
var L_NoDateEntered = "\u65E5\u4ED8\u3092\u5165\u529B\u3059\u308B\u5FC5\u8981\u304C\u3042\u308A\u307E\u3059\u3002";
var L_Empty         = "\u5024\u3092\u5165\u529B\u3057\u3066\u304F\u3060\u3055\u3044\u3002";

// Strings for filter dialog
var L_closeDialog="\u30A6\u30A3\u30F3\u30C9\u30A6\u3092\u9589\u3058\u308B";

var L_SetFilter = "\u30D5\u30A3\u30EB\u30BF\u306E\u8A2D\u5B9A";
var L_OK        = "OK";
var L_Cancel    = "\u30AD\u30E3\u30F3\u30BB\u30EB";

 /* Crystal Decisions Confidential Proprietary Information */
