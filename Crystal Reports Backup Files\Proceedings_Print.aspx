﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Proceedings_Print.aspx.cs" Inherits="KSDCSCST_Portal.Proceedings_Print" %>




<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

    <header>
        <style>
            .font {
                font-family: 'meeraregular';
                font-size: 13px;
                float: left;
            }

            .Label_Value {
                width: 100%;
                float: left;
                border-bottom: 1px dotted #000;
                padding-bottom: 0px;
                min-height: 15px;
                font-weight: bold;
                font-size: 13px;
                margin-top: 2px;
            }
        </style>
    </header>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/dist/css/adminlte.min.css">

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Proceedings Print</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">Proceedings Print</li>
                    </ol>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- /.col -->
                <div class="col-md-7 mx-auto">
                    <div class="card">

                        <div class="card-body">
                            <div class="tab-content">

                                <div class="active tab-pane" id="settings">

                                    <div id="Print_Section" style="display: block;">

                                        <div style="padding: 0px; float: left; width: 100%;">

                                            <div style="margin: 20px;">
                                                <div style="width: 100%">
                                                    <div style="width: 30%; float: left;">
                                                        <img style="width: 220px;" src="assets/img/Logo_Black_And_White.jpg" />
                                                        <p style="font-size: 12px; font-weight: bold; margin: 0; margin-top: 9px;">A GOVT.OF KERALA UNDER TAKING</p>
                                                    </div>
                                                    <div style="width: 70%; float: left;">
                                                        <p style="font-size: 16px; font-weight: bold; line-height: 1.5; margin-bottom: 0px !important; margin-top: 0px;">കേരള സംസ്ഥാന പട്ടികജാതി  പട്ടിക വർഗ്ഗ വികസന  കോർപ്പറേഷൻ  ലിമിറ്റഡ്</p>
                                                        <p style="margin-bottom: 0; font-size: 14px; margin-top: 5px;">CIN: U91990KL1972SGC002466</p>
                                                        <p style="margin-bottom: 0; font-size: 12px; margin-top: 5px;">
                                                            <b>CORPORATE OFFICE:</b> P.B. No. 523, Town Hall Road, Thrissur - 680020 |
                                                       
                                                        <b>Off.Ph:</b> 0487 2331064 |
                                                            <br />
                                                            <b>Email : </b><EMAIL> | Website : ksdcscst.kerala.gov.in
                                                        </p>
                                                    </div>
                                                </div>

                                                <div style="width: 100%; float: left; margin-top: 5px;">

                                                    <div style="width: 100%; float: left;">
                                                        <div style="text-align: center; font-weight: bold;">
                                                            <p style="font-size: 14px; text-decoration: underline;">DISTRICT OFFICE :  <span id="span_Office_Name"></span></p>
                                                        </div>
                                                    </div>

                                                </div>


                                                <hr style="float: left; width: 100%; margin-top: 0px;" />
                                                <div style="width: 100%; float: left; margin-top: 5px;">
                                                    <div style="width: 100%; float: left;">

                                                        <div style="text-align: center; margin-bottom: 20px; text-decoration: underline;">ഔദ്യോഗിക നടപടി  </div>


                                                        <div style="width: 100%; float: left;">
                                                            <div class="font" style="width: 10%;">വിഷയം</div>
                                                            <div class="font" style="width: 2%;">:-</div>
                                                            <div class="font" style="width: 40%;">
                                                                <label id="lbl_Scheme_Name" class="Label_Value">XXX</label>
                                                            </div>
                                                            <div class="font" style="width: 48%;">വായ്പാ അനുവദിച്ചുകൊണ്ടുള്ള ഉത്തരവ് </div>

                                                        </div>

                                                        <div style="width: 100%; float: left;">
                                                            <div class="font" style="width: 10%;">സൂചന</div>
                                                            <div class="font" style="width: 2%;">:-</div>
                                                            <div class="font" style="width: 12%;">ശ്രീ/ശ്രീമതി</div>
                                                            <div class="font" style="width: 45%;">
                                                                <label id="lbl_Loanee_Name_2" class="Label_Value">XXX</label>
                                                            </div>
                                                            <div class="font" style="width: 8%;">യുടെ </div>
                                                            <div class="font" style="width: 15%;">
                                                                <label id="lbl_Application_Receipt_Date" class="Label_Value">XXX</label>
                                                            </div>

                                                        </div>

                                                        <div class="font" style="width: 100%; line-height: 10px; text-align: justify; margin-bottom: 40px;">
                                                            <div class="font" style="width: 12%;">&nbsp;</div>
                                                            തിയ്യതിയിലെ അപേക്ഷ  
                                                        </div>


                                                        <div   style="text-align: center;   margin-bottom: 20px; text-decoration: underline; font-weight: bold; margin-top: 30px;   ">
                                                            <div class="font" style="width: 5%;">&nbsp;</div>
                                                            <div class="font" style="width: 20%;">ഉത്തരവ് നമ്പർ</div>
                                                            <div class="font" style="width: 2%;">:</div>
                                                            <div class="font" style="width: 30%;">
                                                                <label id="lbl_Loan_No" class="Label_Value">XXX</label>
                                                            </div>
                                                            <div class="font" style="width: 10%;">തിയ്യതി</div>
                                                            <div class="font" style="width: 2%;">:</div>
                                                            <div class="font" style="width: 20%;">
                                                                <label id="lbl_Current_Date" class="Label_Value">XXX</label>
                                                            </div>
                                                        </div>

                                                        <div class="font" style="width: 100%; line-height: 25px; text-align: justify;margin-top: 30px;">
                                                            <div class="font" style="width: 15%;">&nbsp;</div>
                                                            <div class="font" style="width: 30%;">
                                                                <label id="lbl_Scheme_Nane" class="Label_Value">XXX</label>
                                                            </div>
                                                            <div class="font" style="width: 25%;">പദ്ധതി  പ്രകാരം   ശ്രീ/ ശ്രീമതി</div>

                                                            <div class="font" style="width: 30%;">
                                                                <label id="lbl_Loanee_Name_1" class="Label_Value">XXX</label>
                                                            </div>

                                                        </div>
                                                         
                                                        <div class="font" style="width: 100%; line-height: 10px; text-align: justify;margin-top: 15px;  ">
                                                            <div class="font" style="width: 5%;">&nbsp;</div>
                                                            വായ്പ്ക്ക്  അപേക്ഷിച്ചിരുന്നു.വായ്പ് അനുവദിക്കുന്നതിനാവശ്യമായ
                                                        </div>
                                                        <div class="font" style="width: 100%; line-height: 10px; text-align: justify; margin-top: 15px;">
                                                            <div class="font" style="width: 5%;">&nbsp;</div>
                                                            നടപടി ക്രമങ്ങൾ പൂർത്തീകരിച്ചതിന്റെ  അടിസ്ഥാനത്തിൽ താഴെപറയുന്ന വ്യവസ്ഥകൾ
                                                        </div>

                                                        <div class="font" style="width: 100%; line-height: 25px; text-align: justify;margin-top: 15px;">
                                                            <div class="font" style="width: 5%;">&nbsp;</div>
                                                            <div class="font" style="width: 30%;">പാലിക്കാമെന്ന നിബന്ധനയിൽ </div>

                                                            <div class="font" style="width: 50%;">
                                                                <label id="lbl_Sanctioned_Loan_Amount" class="Label_Value">XXX</label>
                                                            </div>
                                                            <div class="font" style="width: 15%;">രൂപ വായ്പ്</div>
                                                        </div>
                                                        <div class="font" style="width: 100%; line-height: 10px; text-align: justify; margin-top: 15px; ">
                                                            <div class="font" style="width: 5%;">&nbsp;</div>
                                                          അനുവദിച്ചിരിക്കുന്നു.
                                                        </div>




                                                        <div class="font" style="width: 100%; line-height: 25px; text-align: justify;margin-top: 15px;">
                                                            <div class="font" style="width: 5%;">(1)</div>
                                                            വായ്പ, വായ്പയുടെ  അവസാന ഗഡു അനുവദിച്ച്  1മാസത്തിനുശേഷം / പഠനം അവസാനിച്ച് 6 മാസ
                                                        </div>
                                                        <div class="font" style="width: 100%; line-height: 25px; text-align: justify;">
                                                            <div class="font" style="width: 5%;">&nbsp;</div>
                                                            <div class="font" style="width: 50%;">ത്തിനുശേഷം/ ജോലി ലഭിച്ചയുടനെ ഏതാണോ ആദ്യം</div>

                                                            <div class="font" style="width: 20%;">
                                                                <label id="lbl_Intr_Rate" class="Label_Value">XXX</label>
                                                            </div>
                                                            <div class="font" style="width: 25%;">പലിശ നിരക്കിൽ</div>
                                                        </div>


                                                        <div class="font" style="width: 100%; line-height: 25px; text-align: justify; margin-top: 20px;">
                                                            <div class="font" style="width: 5%;">(2)</div>

                                                            <div class="font" style="width: 25%;">ആദ്യ ഗഡു അടക്കേണ്ടത്</div>

                                                            <div class="font" style="width: 20%;">
                                                                <label id="lbl_Repayment_Staring_Date" class="Label_Value">XXX</label>
                                                            </div>
                                                            <div class="font" style="width: 15%;">തിയ്യതിയിലാണ്.</div>
                                                            <div class="font" style="width: 15%;">ഗഡുസംഖ്യ</div>
                                                            <div class="font" style="width: 20%;">
                                                                <label id="lbl_EMI" class="Label_Value">XXX</label>
                                                            </div>

                                                        </div>
                                                        <div class="font" style="width: 100%; line-height: 10px; text-align: justify; margin-top: 15px;">
                                                            <div class="font" style="width: 5%;">&nbsp;</div>
                                                            രൂപയായി നിജപ്പെടുത്തിരിക്കുന്നു
                                                        </div>



                                                        <div class="font" style="width: 100%; line-height: 25px; text-align: justify; margin-top: 20px;">
                                                            <div class="font" style="width: 5%;">(3)</div>
                                                            വീഴ്ച്  വരുത്തിയ ഗഡുക്കൾക്ക് 2% പിഴപലിശ നൽകേണ്ടതാണ് 
                                                        </div>
                                                        <div class="font" style="width: 100%; line-height: 25px; text-align: justify; margin-top: 20px;">
                                                            <div class="font" style="width: 5%;">(4)</div>
                                                            വായ്പാതുക അനുവദിച്ച ആവിശ്യത്തിനുമാത്രമേ  ഉപയോഗിക്കാവൂ
                                                        </div>
                                                        <div class="font" style="width: 100%; line-height: 25px; text-align: justify; margin-top: 20px;">
                                                            <div class="font" style="width: 5%;">(5)</div>
                                                            വായ്പാ പ്രകാരം അനുവദിച്ച വാഹനം / ഉപകരണം അതിന്റെ പാർക്കിംഗ്  സ്ഥലത്തുനിന്നോ
                                                        </div>
                                                        <div class="font" style="width: 100%; line-height: 10px; text-align: justify; margin-top: 15px;">
                                                            <div class="font" style="width: 5%;">&nbsp;</div>
                                                            സ്ഥാപിച്ച സ്ഥലത്തുനിന്നോ പ്രവർത്തന സൗകര്യാർത്ഥമോ മറ്റോ മാറ്റുകയാണെങ്കിൽ കോർപ്പറേ
                                                        </div>
                                                        <div class="font" style="width: 100%; line-height: 10px; text-align: justify; margin-top: 15px;">
                                                            <div class="font" style="width: 5%;">&nbsp;</div>
                                                            ഷന്റെ മുൻ‌കൂർ അനുവാദം വാങ്ങേണ്ടതാണ്. 
                                                        </div>
                                                        <div class="font" style="width: 100%; line-height: 25px; text-align: justify; margin-top: 20px;">
                                                            <div class="font" style="width: 5%;">(6)</div>
                                                            വായ്പാതുക ദുരുപയോഗപ്പെടുത്തിയെന്ന് തെളിയുന്നപക്ഷം മുഴുവൻ തുകയും പലിശയും പിഴപ്പ
                                                        </div>
                                                        C

                                                         <div class="font" style="width: 100%; line-height: 25px; text-align: justify; margin-top: 20px;">
                                                             <div class="font" style="width: 5%;">(7)</div>
                                                             കോർപ്പറേഷൻ ആവശ്യപ്പെടുന്ന സമയത്ത് ആവശ്യപ്പെട്ട രേഖകൾ ഹാജരാക്കേണ്ടതാണ്
                                                         </div>

                                                        <div class="font" style="width: 100%; line-height: 25px; text-align: justify; margin-top: 20px;">
                                                            <div class="font" style="width: 5%;">(8)</div>
                                                            ഗഡു സംഖ്യ ഡ്രാഫ്റ്റായോ,പണമായോ ,മണി  ഓർഡറായോ തൃശൂർ ജില്ലാ ഓഫീസിൽ ഓരോ
                                                        </div>
                                                        <div class="font" style="width: 100%; line-height: 10px; text-align: justify; margin-top: 15px;">
                                                            <div class="font" style="width: 5%;">&nbsp;</div>
                                                            മാസവും 5- തിയതിക്കകം അടക്കേണ്ടതാണ്.
                                                        </div>
                                                        <div class="font" style="width: 100%; line-height: 25px; text-align: justify; margin-top: 20px;">
                                                            <div class="font" style="width: 5%;">(9)</div>
                                                            ഡ്രാഫ്റ്റ് /മാണി ഓർഡർ ജില്ലാ മാനേജർ , കെ .എസ് .ഡി സി ഫോർ എസ് .സി /എസ് .ടി തൃശൂർ എന്ന
                                                        </div>
                                                        <div class="font" style="width: 100%; line-height: 10px; text-align: justify; margin-top: 15px;">
                                                            <div class="font" style="width: 5%;">&nbsp;</div>
                                                            പേരിൽ മാറത്തക്കവിധം എടുക്കേണ്ടതാണ് .
                                                        </div>
                                                        <div class="font" style="width: 100%; line-height: 25px; text-align: justify; margin-top: 20px;">
                                                            <div class="font" style="width: 5%;">(10)</div>
                                                            2 ഗഡുക്കൾ വീഴ്ച വരുത്തുന്നപക്ഷം വായ്പാതുക പൂർണമായും വസൂലാക്കുന്നതിന് ഉചിതമായ
                                                        </div>
                                                        <div class="font" style="width: 100%; line-height: 10px; text-align: justify; margin-top: 15px;">
                                                            <div class="font" style="width: 5%;">&nbsp;</div>
                                                            നടപടികൾ കോർപറേഷൻ സ്വീകരിക്കുന്നതാണ് .
                                                        </div>
                                                        <div class="font" style="width: 100%; line-height: 25px; text-align: justify; margin-top: 20px;">
                                                            <div class="font" style="width: 5%;">(11)</div>
                                                            വിവാഹ ശേഷം പഞ്ചായത്തിൽ വിവാഹം രജിസ്റ്റർ ചെയ്യുമ്പോൾ ലഭിക്കുന്ന വിവാഹ സെര്ടിഫിക്കറ്റിന്റെ
                                                        </div>
                                                        <div class="font" style="width: 100%; line-height: 10px; text-align: justify; margin-top: 15px;">
                                                            <div class="font" style="width: 5%;">&nbsp;</div>
                                                            സാക്ഷ്യപെടുത്തിയ പകർപ്പ് ഈ ഓഫീസിൽ ഹാജരാക്കേണ്ടതാണ് 
                                                        </div>
                                                    </div>



                                                    <div class="font" style="width: 100%; line-height: 10px; text-align: justify; margin-top: 15px;">
                                                        <div class="font" style="width: 5%;">&nbsp;</div>
                                                        വായ്പക്കാരനിൽ നിന്നും ഈടാക്കാവുന്നതാണ്.
                                                    </div>


                                                    <%--    ANSI need to add here !--%>
                                                </div>

                                                <div style="width: 100%; float: right;">
                                                    <span style="width: 45%; font-size: 14px; text-align: right; line-height: 1.5; margin-bottom: 0px !important; margin-top: 65px; float: right; font-weight: bold;">ജില്ലാ മാനേജർ</span>

                                                </div>


                                            </div>

                                        </div>

                                    </div>


                                    <div class="form-group row">
                                        <div class="col-sm-12 text-right">
                                            <a href="Loan_Remittane_List.aspx" class="btn btn-dark">Back</a>
                                            <a onclick="Print();" class="btn btn-success">Print</a>
                                        </div>
                                    </div>

                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->

    <!-- jQuery -->
    <script src="assets/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="assets/dist/js/adminlte.min.js"></script>
    <!-- AdminLTE for demo purposes -->
    <script src="assets/dist/js/demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script src="assets/plugins/jquery-validation/jquery.validate.min.js"></script>


    <script>
        var $hiddenForm;
        var Toast;
        var Is_Valid_Phone_Number = false;
        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }
        $(document).ready(function () {

            function numberToEnglish(number) {
                // Define arrays for one-digit and two-digit numbers
                const oneDigits = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
                const twoDigits = ['', 'Ten', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];

                // Define arrays for special two-digit numbers and teens
                const specialTwoDigits = ['', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];

                // Convert a two-digit number to English
                function convertTwoDigit(num) {
                    if (num < 10) {
                        return oneDigits[num];
                    } else if (num >= 11 && num <= 19) {
                        return specialTwoDigits[num - 10];
                    } else {
                        const tens = Math.floor(num / 10);
                        const ones = num % 10;
                        return twoDigits[tens] + (ones !== 0 ? ' ' + oneDigits[ones] : '');
                    }
                }

                // Convert a three-digit number to English
                function convertThreeDigit(num) {
                    const hundreds = Math.floor(num / 100);
                    const remainder = num % 100;
                    const result = [];

                    if (hundreds !== 0) {
                        result.push(oneDigits[hundreds] + ' Hundred');
                    }

                    if (remainder !== 0) {
                        result.push(convertTwoDigit(remainder));
                    }

                    return result.join(' ');
                }

                if (number === 0) {
                    return 'Zero';
                }

                // Split the number into groups of three digits
                const billion = Math.floor(number / 1000000000);
                const million = Math.floor((number % 1000000000) / 1000000);
                const thousand = Math.floor((number % 1000000) / 1000);
                const remainder = number % 1000;

                const result = [];

                if (billion !== 0) {
                    result.push(convertThreeDigit(billion) + ' Billion');
                }

                if (million !== 0) {
                    result.push(convertThreeDigit(million) + ' Million');
                }

                if (thousand !== 0) {
                    result.push(convertThreeDigit(thousand) + ' Thousand');
                }

                if (remainder !== 0) {
                    result.push(convertThreeDigit(remainder));
                }

                return result.join(' ') + " Only";
            }



            var currentDate = new Date();

            // Format the date as desired (e.g., "MM/DD/YYYY")
            var formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();

            // Display the formatted date in the "currentDate" div
            $('#lblDate').text(formattedDate);
            $('#span_Date').text(formattedDate);

            $("#lbl_Receipt_No").text(getParameterByName("Rec_No"));

            $("#SPAN_Amount").text(getParameterByName("Amount"));

            $("#SPAN_Total_Amount").text(getParameterByName("Amount"));
            $("#lblName").text(getParameterByName("Name"));


            const englishRepresentation = numberToEnglish(parseFloat(getParameterByName("Amount")));
            $("#lbl_Amount_Text").text(englishRepresentation);
            $("#SPAN_Loan_No").text("(" + getParameterByName("LoanNumber") + ")");

            // Get the current date
            Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000
            });
            $("#dropDistrict").append("<option value='" + <%= Session["District_Id"] %> +"'><%= Session["District_Name"] %></option>");
            $("#dropSubDistrict").append("<option value='" + <%= Session["SubDistrict_Id"] %> +"'><%= Session["SubDistrict_Name"] %></option>");

            //              Load_All_Districts();


            $("#txtPhoneNo").on("input", Input_Phone_Input_On_Event);

            function Input_Phone_Input_On_Event() {

                var inputValue = $(this).val();
                var sanitizedValue = inputValue.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
                $(this).val(sanitizedValue); // Set the sanitized value back to the input field


                if (inputValue.trim() == ",") {
                    sanitizedValue = inputValue.replace(/[^0-9,]/g, '');
                    $(this).val("");
                }
                $(this).val($(this).val().replace(/,+/g, ','));

                var values = sanitizedValue.split(",");
                if (values[1] == "" && values[0].length < 10) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Enter valid phone number !'
                    })
                }

            }

            $(document).on("focusout", ".form-control", function () {
                $(".form-control").removeAttr("style");

                var This_Id = $(this).attr("id");
                var This_Control = $(this);
                if (This_Id == "txtApplicant_Name") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Name of Applicant is required !'
                        })
                    }
                    else if (!Is_Valid_Text(This_Control.val().trim())) {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Special characters or Numbers not allowed in Name of Applicant !'
                        })
                    }
                }
                else if (This_Id == "dropScheme") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Loan Scheme is required !'
                        })
                    }
                }
                else if (This_Id == "dropCast") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Caste is required !'
                        })
                    }
                }
                else if (This_Id == "dropSubCast") {
                    if (This_Control.val() == "0") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Sub Caste is required !'
                        })
                    }
                }
                else if (This_Id == "txtPhoneNo") {
                    if (This_Control.val().trim() == "") {
                        Focus_Error(This_Control);
                        Toast.fire({
                            icon: 'error',
                            title: 'Phone No is required !'
                        })
                    }
                }
                //else if (This_Id == "txtAadharNumber") {
                //    if (This_Control.val().trim() == "") {
                //        Focus_Error(This_Control);
                //        Toast.fire({
                //            icon: 'error',
                //            title: 'Aadhar No is required !'
                //        })
                //    }
                //}
                // Your common focusout function code goes here
                //   alert("Input value changed: " + value);
            });


            //$(document).on("input", ".form-control", function () {
            //    // Get the current value of the input
            //    var currentValue = $(this).val();

            //    // Convert the value to propcase
            //    var propcaseValue = currentValue.replace(/\w\S*/g, function (txt) {
            //        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
            //    });

            //    // Update the input with the propcase value
            //    $(this).val(propcaseValue);
            //});
        });


        function Load_All_Districts() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Districts",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropDistrict').empty();
                    $('#dropDistrict').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var District_Name = value.District_Name;
                        var html = '<option value="' + Id + '">' + District_Name + '</option>';
                        if (Id != 15) {
                            $('#dropDistrict').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $('#dropDistrict').change(function () {
                    Load_All_Sub_Districts($(this).val());
                });

            });

        }
        function Load_All_Sub_Districts(District_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Districts",
                data: JSON.stringify({ District_Id: District_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubDistrict').empty();
                    $('#dropSubDistrict').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Sub_District_Name = value.Sub_District_Name;
                        var html = '<option value="' + Id + '">' + Sub_District_Name + '</option>';

                        $('#dropSubDistrict').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {



            });

        }

        function Load_All_Schemes() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Schemes",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropScheme').empty();
                    $('#dropScheme').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var IsActive = value.IsActive;
                        var Scheme = value.Scheme;
                        var html = '<option value="' + Id + '">' + Scheme + '</option>';
                        if (IsActive == 1) {
                            $('#dropScheme').append(html);
                        }
                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_All_Cast();

            });

        }

        function Load_All_Cast() {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Cast",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropCast').empty();
                    $('#dropCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var Cast = value.Cast;
                        var html = '<option value="' + Id + '">' + Cast + '</option>';

                        $('#dropCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                $('#dropCast').change(function () {
                    Load_All_SubCast($(this).val());
                });

                Load_All_Application_Issue_By_Id(getParameterByName("Id"));
            });

        }

        var District_Id;
        var SubDistrict_id;
        var Cast_Id;
        var Sub_Cast_Id;

        function Load_All_Application_Issue_By_Id(Id) {
            $.ajax({
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ Id: Id }), // If you have parameters
                url: "WebService.asmx/Select_All_tbl_Loan_App_Issue_By_ID",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#tblBody').empty();
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        District_Id = value.District_Id;
                        SubDistrict_id = value.SubDistrict_id;
                        var Name_Applicant = value.Name_Applicant;
                        var Application_Submitted_Date = value.Application_Submitted_Date;
                        var Scheme_Id = value.Scheme_Id;

                        Cast_Id = value.Cast_Id;
                        Sub_Cast_Id = value.Sub_Cast_Id;
                        var Reciept_No = value.Reciept_No;
                        var Amount = value.Amount;
                        var Remarks = value.Remarks;

                        var PhoneNo = value.PhoneNo;
                        var AadharNumber = value.AadharNumber;
                        $("#lbl_Receipt_No").text(value.Reciept_No);
                        $("#lblDate").text(Application_Submitted_Date);


                        $("#txtApplication_Date").val(Application_Submitted_Date);
                        $("#dropDistrict").val(District_Id);

                        $("#txtApplicant_Name").val(Name_Applicant);
                        $("#lblName").text(Name_Applicant);

                        $("#dropScheme").val(Scheme_Id);
                        $("#dropCast").val(Cast_Id);

                        $("#txtReceiptNo").val(Reciept_No);
                        $("#txtAmount").val(Amount);
                        $("#txtRemarks").val(Remarks);
                        $("#txtPhoneNo").val(PhoneNo);
                        $("#txtAadharNumber").val(AadharNumber);
                        //  $("#txtApplicationNo").val(Id);






                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {

                Load_Sub_District_And_Set(District_Id, SubDistrict_id, Cast_Id, Sub_Cast_Id);

            });
        }
        var Sub_District_Count;
        function Load_Sub_District_And_Set(District_Id, SubDistrict_id, Cast_Id, Sub_Cast_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Districts",
                data: JSON.stringify({ District_Id: District_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubDistrict').empty();
                    $('#dropSubDistrict').append('<option value="0">Select</option>');
                    Sub_District_Count = data.d.length;
                    $.each(data.d, function (key, value) {
                        var Id = value.Id;
                        var Sub_District_Name = value.Sub_District_Name;
                        var html = '<option value="' + Id + '">' + Sub_District_Name + '</option>';

                        $('#dropSubDistrict').append(html);


                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                if (Sub_District_Count == 0) {
                    $("#dropSubDistrict").val('0');
                }
                else {
                    $("#dropSubDistrict").val(SubDistrict_id);
                    $("#span_Office_Name").text($('#dropSubDistrict :selected').text());
                }


                Load_All_SubCast_And_Set(Cast_Id, Sub_Cast_Id);
            });

        }

        function Load_All_SubCast_And_Set(Cast_Id, Sub_Cast_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Cast",
                data: JSON.stringify({ Cast_Id: Cast_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubCast').empty();
                    $('#dropSubCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var SubCast = value.SubCast;
                        var html = '<option value="' + Id + '">' + SubCast + '</option>';

                        $('#dropSubCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {
                $("#dropSubCast").val(Sub_Cast_Id);

            });

        }


        function Load_All_SubCast(Cast_Id) {

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "WebService.asmx/Select_All_Sub_Cast",
                data: JSON.stringify({ Cast_Id: Cast_Id }), // If you have parameters

                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    $('#dropSubCast').empty();
                    $('#dropSubCast').append('<option value="0">Select</option>');

                    $.each(data.d, function (key, value) {
                        var Id = value.Id;

                        var SubCast = value.SubCast;
                        var html = '<option value="' + Id + '">' + SubCast + '</option>';

                        $('#dropSubCast').append(html);

                    });


                },
                error: function (error) {


                    // alert("error" + error.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',

                    })
                }
            }).done(function () {


            });

        }

        function Print() {
            var printWindow = window.open('', '_blank');
            printWindow.document.open();
            printWindow.document.write('<html><head><title>Print</title></head><body style="width: 210mm;height: 297mm;margin: 0;  ">');
            printWindow.document.write($("#Print_Section").html());
            printWindow.document.write('</body></html>');
            printWindow.document.close();
            printWindow.print();
            if (confirm('Print dialog initiated. Please close the window after printing.')) {
                printWindow.close();
            }
        }


        function Save() {
            //alert(Counter);



        }
        function Focus_Error(Control) {
            Control.css("border", "2px solid red");
            Control.focus();
        }
        function Is_Valid_Text(inputText) {


            var regex = /^[a-zA-Z\s]+$/;

            if (regex.test(inputText)) {
                // The input contains only alphabetical characters
                return true;
            } else {
                // The input contains non-alphabetical characters
                return false;
            }

        }

        function Is_Valid_Mobile_Number(mobile_number) {
            // Regex to check valid
            // mobile_number  
            let regex = new RegExp(/^((0|91)?[6-9][0-9]{9})$/);

            // if mobile_number 
            // is empty return false
            if (mobile_number == null) {
                Is_Valid_Phone_Number = false;
                return false;
            }

            // Return true if the mobile_number
            // matched the ReGex
            if (regex.test(mobile_number) == true) {
                Is_Valid_Phone_Number = true;
                return true;
            }
            else {
                Is_Valid_Phone_Number = false;
                return false;
            }
        }


    </script>
</asp:Content>




